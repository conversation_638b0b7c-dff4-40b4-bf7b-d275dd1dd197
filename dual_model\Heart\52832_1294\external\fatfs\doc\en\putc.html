<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html lang="en">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<meta http-equiv="Content-Style-Type" content="text/css">
<link rel="up" title="FatFs" href="../00index_e.html">
<link rel="alternate" hreflang="ja" title="Japanese" href="../ja/putc.html">
<link rel="stylesheet" href="../css_e.css" type="text/css" media="screen" title="ELM Default">
<title>FatFs - f_putc</title>
</head>

<body>

<div class="para func">
<h2>f_putc</h2>
<p>The f_putc funciton puts a character to the file.</p>
<pre>
int f_putc (
  TCHAR <span class="arg">chr</span>,  <span class="c">/* [IN] A character to put */</span>
  FIL* <span class="arg">fp</span>     <span class="c">/* [IN] File object */</span>
);
</pre>
</div>

<div class="para arg">
<h4>Parameters</h4>
<dl class="par">
<dt>chr</dt>
<dd>A character to be put.</dd>
<dt>fp</dt>
<dd>Pointer to the open file object structuer.</dd>
</dl>
</div>


<div class="para ret">
<h4>Return Values</h4>
<p>When the character was written successfuly, it returns number of characters written. When the function failed due to disk full or any error, an <tt>EOF (-1)</tt> will be returned.</p>
<p>When FatFs is configured to Unicode API (<tt>_LFN_UNICODE == 1</tt>), character encoding on the string fuctions, <tt>f_putc</tt>, <tt>f_puts</tt>, <tt>f_printf</tt> and <tt>f_gets</tt> function, is also switched to Unicode. The character encoding on the file to be read/written via those functions is selected by <tt>_STRF_ENCODE</tt> option.</p>
</div>


<div class="para comp">
<h4>QuickInfo</h4>
<p>This is a wrapper function of <a href="write.html"><tt>f_write</tt></a> function. Available when <tt>_FS_READONLY == 0</tt> and <tt>_USE_STRFUNC</tt> is 1 or 2. When it is set to 2, a <tt>'\n'</tt> is converted to <tt>'\r'+'\n'</tt>.</p>
</div>


<div class="para ref">
<h4>See Also</h4>
<p><tt><a href="open.html">f_open</a>, <a href="puts.html">f_puts</a>, <a href="printf.html">f_printf</a>, <a href="gets.html">f_gets</a>, <a href="close.html">f_close</a>, <a href="sfile.html">FIL</a></tt></p>
</div>

<p class="foot"><a href="../00index_e.html">Return</a></p>
</body>
</html>
