<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=iso-8859-1">
<title>The Protothreads Library 1.4: Local continuations</title>
<link href="doxygen.css" rel="stylesheet" type="text/css">
<link href="tabs.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.4.6 -->
<div class="tabs">
  <ul>
    <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
    <li><a href="modules.html"><span>Modules</span></a></li>
    <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    <li><a href="files.html"><span>Files</span></a></li>
  </ul></div>
<h1>Local continuations<br>
<small>
[<a class="el" href="a00014.html">Protothreads</a>]</small>
</h1><hr><a name="_details"></a><h2>Detailed Description</h2>
Local continuations form the basis for implementing protothreads. 
<p>
A local continuation can be <em>set</em> in a specific function to capture the state of the function. After a local continuation has been set can be <em>resumed</em> in order to restore the state of the function at the point where the local continuation was set. 
<p>
<table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Files</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">file &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00011.html">lc.h</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Local continuations. <br></td></tr>

<p>
<tr><td class="memItemLeft" nowrap align="right" valign="top">file &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00010.html">lc-switch.h</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Implementation of local continuations based on switch() statment. <br></td></tr>

<p>
<tr><td class="memItemLeft" nowrap align="right" valign="top">file &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00009.html">lc-addrlabels.h</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Implementation of local continuations based on the "Labels as values" feature of gcc. <br></td></tr>

<p>
<tr><td colspan="2"><br><h2>Defines</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">#define&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00017.html#g9ca9d0fef02b9c5d93bed2834e7aeb76">LC_INIT</a>(lc)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Initialize a local continuation.  <a href="#g9ca9d0fef02b9c5d93bed2834e7aeb76"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">#define&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00017.html#gfb1d5e671e40464a7a7bda589b5d4341">LC_SET</a>(lc)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Set a local continuation.  <a href="#gfb1d5e671e40464a7a7bda589b5d4341"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">#define&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00017.html#g33dad6011c98dfeb4e64fee1d6892cb3">LC_RESUME</a>(lc)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Resume a local continuation.  <a href="#g33dad6011c98dfeb4e64fee1d6892cb3"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">#define&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00017.html#g3d76802e55349cc8bf74f286ced203c3">LC_END</a>(lc)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Mark the end of local continuation usage.  <a href="#g3d76802e55349cc8bf74f286ced203c3"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="anchor" name="g2c1bb4fa6d7a6ff951a41c73fc721109"></a><!-- doxytag: member="lc::LC_INIT" ref="g2c1bb4fa6d7a6ff951a41c73fc721109" args="(s)" -->
#define&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00017.html#g2c1bb4fa6d7a6ff951a41c73fc721109">LC_INIT</a>(s)&nbsp;&nbsp;&nbsp;s = 0;</td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="anchor" name="g1ec8b8f4710dce1fa7fb87d3a31541ae"></a><!-- doxytag: member="lc::LC_RESUME" ref="g1ec8b8f4710dce1fa7fb87d3a31541ae" args="(s)" -->
#define&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00017.html#g1ec8b8f4710dce1fa7fb87d3a31541ae">LC_RESUME</a>(s)&nbsp;&nbsp;&nbsp;switch(s) { case 0:</td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="anchor" name="gd8eec328a4868d767f0c00c8d1c6cfc1"></a><!-- doxytag: member="lc::LC_SET" ref="gd8eec328a4868d767f0c00c8d1c6cfc1" args="(s)" -->
#define&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00017.html#gd8eec328a4868d767f0c00c8d1c6cfc1">LC_SET</a>(s)&nbsp;&nbsp;&nbsp;s = __LINE__; case __LINE__:</td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="anchor" name="gca51ceb2f5d855dfde55bcedf8d3b92d"></a><!-- doxytag: member="lc::LC_END" ref="gca51ceb2f5d855dfde55bcedf8d3b92d" args="(s)" -->
#define&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00017.html#gca51ceb2f5d855dfde55bcedf8d3b92d">LC_END</a>(s)&nbsp;&nbsp;&nbsp;}</td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="anchor" name="g2c1bb4fa6d7a6ff951a41c73fc721109"></a><!-- doxytag: member="lc::LC_INIT" ref="g2c1bb4fa6d7a6ff951a41c73fc721109" args="(s)" -->
#define&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00017.html#g2c1bb4fa6d7a6ff951a41c73fc721109">LC_INIT</a>(s)&nbsp;&nbsp;&nbsp;s = NULL</td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="anchor" name="g1ec8b8f4710dce1fa7fb87d3a31541ae"></a><!-- doxytag: member="lc::LC_RESUME" ref="g1ec8b8f4710dce1fa7fb87d3a31541ae" args="(s)" -->
#define&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00017.html#g1ec8b8f4710dce1fa7fb87d3a31541ae">LC_RESUME</a>(s)</td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="anchor" name="g6e1e879e172e2d8838e5f567dac8918c"></a><!-- doxytag: member="lc::LC_CONCAT2" ref="g6e1e879e172e2d8838e5f567dac8918c" args="(s1, s2)" -->
#define&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00017.html#g6e1e879e172e2d8838e5f567dac8918c">LC_CONCAT2</a>(s1, s2)&nbsp;&nbsp;&nbsp;s1##s2</td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="anchor" name="g2b1f9b9fe8b6895b156f0af10538971c"></a><!-- doxytag: member="lc::LC_CONCAT" ref="g2b1f9b9fe8b6895b156f0af10538971c" args="(s1, s2)" -->
#define&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00017.html#g2b1f9b9fe8b6895b156f0af10538971c">LC_CONCAT</a>(s1, s2)&nbsp;&nbsp;&nbsp;LC_CONCAT2(s1, s2)</td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="anchor" name="gd8eec328a4868d767f0c00c8d1c6cfc1"></a><!-- doxytag: member="lc::LC_SET" ref="gd8eec328a4868d767f0c00c8d1c6cfc1" args="(s)" -->
#define&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00017.html#gd8eec328a4868d767f0c00c8d1c6cfc1">LC_SET</a>(s)</td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="anchor" name="gca51ceb2f5d855dfde55bcedf8d3b92d"></a><!-- doxytag: member="lc::LC_END" ref="gca51ceb2f5d855dfde55bcedf8d3b92d" args="(s)" -->
#define&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00017.html#gca51ceb2f5d855dfde55bcedf8d3b92d">LC_END</a>(s)</td></tr>

<tr><td colspan="2"><br><h2>Typedefs</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="anchor" name="gfad6704adb116cc16edb80f744e7239d"></a><!-- doxytag: member="lc::lc_t" ref="gfad6704adb116cc16edb80f744e7239d" args="" -->
typedef unsigned short&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00017.html#gfad6704adb116cc16edb80f744e7239d">lc_t</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">The local continuation type. <br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="anchor" name="g2bdc4b7b4038454a79f1b2a94a6d2a98"></a><!-- doxytag: member="lc::lc_t" ref="g2bdc4b7b4038454a79f1b2a94a6d2a98" args="" -->
typedef void *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00017.html#g2bdc4b7b4038454a79f1b2a94a6d2a98">lc_t</a></td></tr>

</table>
<hr><h2>Define Documentation</h2>
<a class="anchor" name="g3d76802e55349cc8bf74f286ced203c3"></a><!-- doxytag: member="lc.h::LC_END" ref="g3d76802e55349cc8bf74f286ced203c3" args="(lc)" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top">#define LC_END          </td>
          <td class="md" valign="top">(&nbsp;</td>
          <td class="md" nowrap valign="top">lc&nbsp;</td>
          <td class="mdname1" valign="top" nowrap>          </td>
          <td class="md" valign="top">&nbsp;)&nbsp;</td>
          <td class="md" nowrap></td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
Mark the end of local continuation usage. 
<p>
The end operation signifies that local continuations should not be used any more in the function. This operation is not needed for most implementations of local continuation, but is required by a few implementations. 
<p>
Definition at line <a class="el" href="a00020.html#l00108">108</a> of file <a class="el" href="a00020.html">lc.h</a>.    </td>
  </tr>
</table>
<a class="anchor" name="g9ca9d0fef02b9c5d93bed2834e7aeb76"></a><!-- doxytag: member="lc.h::LC_INIT" ref="g9ca9d0fef02b9c5d93bed2834e7aeb76" args="(lc)" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top">#define LC_INIT          </td>
          <td class="md" valign="top">(&nbsp;</td>
          <td class="md" nowrap valign="top">lc&nbsp;</td>
          <td class="mdname1" valign="top" nowrap>          </td>
          <td class="md" valign="top">&nbsp;)&nbsp;</td>
          <td class="md" nowrap></td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
Initialize a local continuation. 
<p>
This operation initializes the local continuation, thereby unsetting any previously set continuation state. 
<p>
Definition at line <a class="el" href="a00020.html#l00071">71</a> of file <a class="el" href="a00020.html">lc.h</a>.    </td>
  </tr>
</table>
<a class="anchor" name="g33dad6011c98dfeb4e64fee1d6892cb3"></a><!-- doxytag: member="lc.h::LC_RESUME" ref="g33dad6011c98dfeb4e64fee1d6892cb3" args="(lc)" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top">#define LC_RESUME          </td>
          <td class="md" valign="top">(&nbsp;</td>
          <td class="md" nowrap valign="top">lc&nbsp;</td>
          <td class="mdname1" valign="top" nowrap>          </td>
          <td class="md" valign="top">&nbsp;)&nbsp;</td>
          <td class="md" nowrap></td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
Resume a local continuation. 
<p>
The resume operation resumes a previously set local continuation, thus restoring the state in which the function was when the local continuation was set. If the local continuation has not been previously set, the resume operation does nothing. 
<p>
Definition at line <a class="el" href="a00020.html#l00096">96</a> of file <a class="el" href="a00020.html">lc.h</a>.    </td>
  </tr>
</table>
<a class="anchor" name="gfb1d5e671e40464a7a7bda589b5d4341"></a><!-- doxytag: member="lc.h::LC_SET" ref="gfb1d5e671e40464a7a7bda589b5d4341" args="(lc)" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top">#define LC_SET          </td>
          <td class="md" valign="top">(&nbsp;</td>
          <td class="md" nowrap valign="top">lc&nbsp;</td>
          <td class="mdname1" valign="top" nowrap>          </td>
          <td class="md" valign="top">&nbsp;)&nbsp;</td>
          <td class="md" nowrap></td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
Set a local continuation. 
<p>
The set operation saves the state of the function at the point where the operation is executed. As far as the set operation is concerned, the state of the function does <b>not</b> include the call-stack or local (automatic) variables, but only the program counter and such CPU registers that needs to be saved. 
<p>
Definition at line <a class="el" href="a00020.html#l00084">84</a> of file <a class="el" href="a00020.html">lc.h</a>.    </td>
  </tr>
</table>
<hr size="1"><address style="align: right;"><small>Generated on Mon Oct 2 10:06:29 2006 for The Protothreads Library 1.4 by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.4.6 </small></address>
</body>
</html>
