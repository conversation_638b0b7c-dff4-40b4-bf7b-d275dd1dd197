<?xml version="1.0" encoding="iso-8859-1"?>


<project>
  <fileVersion>2</fileVersion>  <configuration>
    <name>nrf52840_xxaa</name>
    <toolchain>
      <name>ARM</name>
    </toolchain>
    <debug>0</debug>
    <settings>
      <name>General</name>
      <archiveVersion>3</archiveVersion>
      <data>
        <version>22</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>0</debug>
        <option>
          <name>ExePath</name>
          <state>_build</state>
        </option>
        <option>
          <name>ObjPath</name>
          <state>_build</state>
        </option>
        <option>
          <name>ListPath</name>
          <state>_build</state>
        </option>
        <option>
          <name>Variant</name>
          <version>20</version>
          <state>34</state>
        </option>
        <option>
          <name>GEndianMode</name>
          <state>0</state>
        </option>
        <option>
          <name>Input variant</name>
          <version>3</version>
          <state>1</state>
        </option>
        <option>
          <name>Input description</name>
          <state>Full formatting.</state>
        </option>
        <option>
          <name>Output variant</name>
          <version>2</version>
          <state>1</state>
        </option>
        <option>
          <name>Output description</name>
          <state>Full formatting.</state>
        </option>
        <option>
          <name>GOutputBinary</name>
          <state>0</state>
        </option>
        <option>
          <name>FPU</name>
          <version>2</version>
          <state>5</state>
        </option>
        <option>
          <name>OGCoreOrChip</name>
          <state>1</state>
        </option>
        <option>
          <name>GRuntimeLibSelect</name>
          <version>0</version>
          <state>2</state>
        </option>
        <option>
          <name>GRuntimeLibSelectSlave</name>
          <version>0</version>
          <state>2</state>
        </option>
        <option>
          <name>RTDescription</name>
          <state>Use the full configuration of the C/C++ runtime library. Full locale interface, C locale, file descriptor support, multibytes in printf and scanf, and hex floats in strtod.</state>
        </option>
        <option>
          <name>OGProductVersion</name>
          <state>6.10.3.52260</state>
        </option>
        <option>
          <name>OGLastSavedByProductVersion</name>
          <state>7.20.2.7418</state>
        </option>
        <option>
          <name>GeneralEnableMisra</name>
          <state>0</state>
        </option>
        <option>
          <name>GeneralMisraVerbose</name>
          <state>0</state>
        </option>
        <option>
          <name>OGChipSelectEditMenu</name>
          <state>nrf52840_xxaa	nRF52840_xxAA</state>
        </option>
        <option>
          <name>GenLowLevelInterface</name>
          <state>0</state>
        </option>
        <option>
          <name>GEndianModeBE</name>
          <state>1</state>
        </option>
        <option>
          <name>OGBufferedTerminalOutput</name>
          <state>0</state>
        </option>
        <option>
          <name>GenStdoutInterface</name>
          <state>0</state>
        </option>
        <option>
          <name>GeneralMisraRules98</name>
          <version>0</version>
          <state>1000111110110101101110011100111111101110011011000101110111101101100111111111111100110011111001110111001111111111111111111111111</state>
        </option>
        <option>
          <name>GeneralMisraVer</name>
          <state>0</state>
        </option>
        <option>
          <name>GeneralMisraRules04</name>
          <version>0</version>
          <state>111101110010111111111000110111111111111111111111111110010111101111010101111111111111111111111111101111111011111001111011111011111111111111111</state>
        </option>
        <option>
          <name>RTConfigPath2</name>
          <state>$TOOLKIT_DIR$\INC\c\DLib_Config_Full.h</state>
        </option>
        <option>
          <name>GFPUCoreSlave</name>
          <version>20</version>
          <state>39</state>
        </option>
        <option>
          <name>GBECoreSlave</name>
          <version>20</version>
          <state>39</state>
        </option>
        <option>
          <name>OGUseCmsis</name>
          <state>0</state>
        </option>
        <option>
          <name>OGUseCmsisDspLib</name>
          <state>0</state>
        </option>
        <option>
          <name>GRuntimeLibThreads</name>
          <state>0</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>ICCARM</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>31</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>0</debug>
        <option>
          <name>CCGuardCalls</name>
          <state>1</state>
        </option>
        <option>
          <name>CCOptimizationNoSizeConstraints</name>
          <state>0</state>
        </option>
        <option>
          <name>CCDefines</name>
          <state>BOARD_PCA10056</state>
          <state>BSP_DEFINES_ONLY</state>
          <state>CONFIG_GPIO_AS_PINRESET</state>
          <state>DEBUG</state>
          <state>DEBUG_NRF</state>
          <state>FLOAT_ABI_HARD</state>
          <state>MBEDTLS_CONFIG_FILE=&lt;nrf_crypto_mbedtls_config.h&gt;</state>
          <state>NRF52840_XXAA</state>
          <state>NRF_CRYPTO_MAX_INSTANCE_COUNT=1</state>
          <state>NRF_SDK_PRESENT</state>
          <state>uECC_ENABLE_VLI_API=0</state>
          <state>uECC_OPTIMIZATION_LEVEL=3</state>
          <state>uECC_SQUARE_FUNC=0</state>
          <state>uECC_SUPPORT_COMPRESSED_POINT=0</state>
          <state>uECC_VLI_NATIVE_LITTLE_ENDIAN=1</state>
        </option>
        <option>
          <name>CCPreprocFile</name>
          <state>0</state>
        </option>
        <option>
          <name>CCPreprocComments</name>
          <state>0</state>
        </option>
        <option>
          <name>CCPreprocLine</name>
          <state>0</state>
        </option>
        <option>
          <name>CCListCFile</name>
          <state>0</state>
        </option>
        <option>
          <name>CCListCMnemonics</name>
          <state>0</state>
        </option>
        <option>
          <name>CCListCMessages</name>
          <state>0</state>
        </option>
        <option>
          <name>CCListAssFile</name>
          <state>0</state>
        </option>
        <option>
          <name>CCListAssSource</name>
          <state>0</state>
        </option>
        <option>
          <name>CCEnableRemarks</name>
          <state>0</state>
        </option>
        <option>
          <name>CCDiagSuppress</name>
          <state></state>
        </option>
        <option>
          <name>CCDiagRemark</name>
          <state></state>
        </option>
        <option>
          <name>CCDiagWarning</name>
          <state></state>
        </option>
        <option>
          <name>CCDiagError</name>
          <state></state>
        </option>
        <option>
          <name>CCObjPrefix</name>
          <state>1</state>
        </option>
        <option>
          <name>CCAllowList</name>
          <version>1</version>
          <state>11111110</state>
        </option>
        <option>
          <name>CCDebugInfo</name>
          <state>1</state>
        </option>
        <option>
          <name>IEndianMode</name>
          <state>1</state>
        </option>
        <option>
          <name>IProcessor</name>
          <state>1</state>
        </option>
        <option>
          <name>IExtraOptionsCheck</name>
          <state>1</state>
        </option>
        <option>
          <name>IExtraOptions</name>
          <state>--diag_suppress Pe111</state>
        </option>
        <option>
          <name>CCLangConformance</name>
          <state>0</state>
        </option>
        <option>
          <name>CCSignedPlainChar</name>
          <state>1</state>
        </option>
        <option>
          <name>CCRequirePrototypes</name>
          <state>0</state>
        </option>
        <option>
          <name>CCMultibyteSupport</name>
          <state>0</state>
        </option>
        <option>
          <name>CCDiagWarnAreErr</name>
          <state>1</state>
        </option>
        <option>
          <name>CCCompilerRuntimeInfo</name>
          <state>0</state>
        </option>
        <option>
          <name>IFpuProcessor</name>
          <state>1</state>
        </option>
        <option>
          <name>OutputFile</name>
          <state>$FILE_BNAME$.o</state>
        </option>
        <option>
          <name>CCLibConfigHeader</name>
          <state>1</state>
        </option>
        <option>
          <name>PreInclude</name>
          <state></state>
        </option>
        <option>
          <name>CompilerMisraOverride</name>
          <state>0</state>
        </option>
        <option>
          <name>CCIncludePath2</name>
          <state>$PROJ_DIR$\..\..\..\config</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\..\components</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\..\components\boards</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\..\components\drivers_nrf\nrf_soc_nosd</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\..\components\libraries\atomic</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\..\components\libraries\balloc</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\..\components\libraries\bsp</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\..\components\libraries\crypto</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\..\components\libraries\crypto\backend\cc310</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\..\components\libraries\crypto\backend\cc310_bl</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\..\components\libraries\crypto\backend\cifra</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\..\components\libraries\crypto\backend\mbedtls</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\..\components\libraries\crypto\backend\micro_ecc</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\..\components\libraries\crypto\backend\nrf_hw</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\..\components\libraries\crypto\backend\nrf_sw</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\..\components\libraries\crypto\backend\oberon</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\..\components\libraries\crypto\backend\optiga</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\..\components\libraries\delay</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\..\components\libraries\experimental_section_vars</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\..\components\libraries\log</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\..\components\libraries\log\src</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\..\components\libraries\mem_manager</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\..\components\libraries\memobj</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\..\components\libraries\mutex</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\..\components\libraries\queue</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\..\components\libraries\ringbuf</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\..\components\libraries\stack_info</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\..\components\libraries\strerror</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\..\components\libraries\util</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\..\components\toolchain\cmsis\include</state>
          <state>$PROJ_DIR$\..\..\..</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\..\external\cifra_AES128-EAX</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\..\external\fprintf</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\..\external\mbedtls\include</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\..\external\micro-ecc\micro-ecc</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\..\external\nrf_cc310\include</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\..\external\nrf_oberon</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\..\external\nrf_oberon\include</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\..\external\nrf_tls\mbedtls\nrf_crypto\config</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\..\external\segger_rtt</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\..\integration\nrfx</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\..\integration\nrfx\legacy</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\..\modules\nrfx</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\..\modules\nrfx\drivers\include</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\..\modules\nrfx\hal</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\..\modules\nrfx\mdk</state>
          <state>$PROJ_DIR$\..\config</state>
        </option>
        <option>
          <name>CCStdIncCheck</name>
          <state>0</state>
        </option>
        <option>
          <name>CCCodeSection</name>
          <state>.text</state>
        </option>
        <option>
          <name>IInterwork2</name>
          <state>0</state>
        </option>
        <option>
          <name>IProcessorMode2</name>
          <state>1</state>
        </option>
        <option>
          <name>CCOptLevel</name>
          <state>3</state>
        </option>
        <option>
          <name>CCOptStrategy</name>
          <version>0</version>
          <state>1</state>
        </option>
        <option>
          <name>CCOptLevelSlave</name>
          <state>3</state>
        </option>
        <option>
          <name>CompilerMisraRules98</name>
          <version>0</version>
          <state>1000111110110101101110011100111111101110011011000101110111101101100111111111111100110011111001110111001111111111111111111111111</state>
        </option>
        <option>
          <name>CompilerMisraRules04</name>
          <version>0</version>
          <state>111101110010111111111000110111111111111111111111111110010111101111010101111111111111111111111111101111111011111001111011111011111111111111111</state>
        </option>
        <option>
          <name>CCPosIndRopi</name>
          <state>0</state>
        </option>
        <option>
          <name>CCPosIndRwpi</name>
          <state>0</state>
        </option>
        <option>
          <name>CCPosIndNoDynInit</name>
          <state>0</state>
        </option>
        <option>
          <name>IccLang</name>
          <state>0</state>
        </option>
        <option>
          <name>IccCDialect</name>
          <state>1</state>
        </option>
        <option>
          <name>IccAllowVLA</name>
          <state>0</state>
        </option>
        <option>
          <name>IccCppDialect</name>
          <state>1</state>
        </option>
        <option>
          <name>IccExceptions</name>
          <state>1</state>
        </option>
        <option>
          <name>IccRTTI</name>
          <state>1</state>
        </option>
        <option>
          <name>IccStaticDestr</name>
          <state>1</state>
        </option>
        <option>
          <name>IccCppInlineSemantics</name>
          <state>0</state>
        </option>
        <option>
          <name>IccCmsis</name>
          <state>1</state>
        </option>
        <option>
          <name>IccFloatSemantics</name>
          <state>0</state>
        </option>
        <option>
          <name>CCNoLiteralPool</name>
          <state>0</state>
        </option>
        <option>
          <name>CCOptStrategySlave</name>
          <version>0</version>
          <state>1</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>AARM</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>9</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>0</debug>
        <option>
          <name>AObjPrefix</name>
          <state>1</state>
        </option>
        <option>
          <name>AEndian</name>
          <state>1</state>
        </option>
        <option>
          <name>ACaseSensitivity</name>
          <state>1</state>
        </option>
        <option>
          <name>MacroChars</name>
          <version>0</version>
          <state>0</state>
        </option>
        <option>
          <name>AWarnEnable</name>
          <state>0</state>
        </option>
        <option>
          <name>AWarnWhat</name>
          <state>0</state>
        </option>
        <option>
          <name>AWarnOne</name>
          <state></state>
        </option>
        <option>
          <name>AWarnRange1</name>
          <state></state>
        </option>
        <option>
          <name>AWarnRange2</name>
          <state></state>
        </option>
        <option>
          <name>ADebug</name>
          <state></state>
        </option>
        <option>
          <name>AltRegisterNames</name>
          <state>0</state>
        </option>
        <option>
      <name>ADefines</name>
          <state>BOARD_PCA10056</state>
          <state>BSP_DEFINES_ONLY</state>
          <state>CONFIG_GPIO_AS_PINRESET</state>
          <state>DEBUG</state>
          <state>DEBUG_NRF</state>
          <state>FLOAT_ABI_HARD</state>
          <state>MBEDTLS_CONFIG_FILE=&lt;nrf_crypto_mbedtls_config.h&gt;</state>
          <state>NRF52840_XXAA</state>
          <state>NRF_CRYPTO_MAX_INSTANCE_COUNT=1</state>
          <state>NRF_SDK_PRESENT</state>
          <state>uECC_ENABLE_VLI_API=0</state>
          <state>uECC_OPTIMIZATION_LEVEL=3</state>
          <state>uECC_SQUARE_FUNC=0</state>
          <state>uECC_SUPPORT_COMPRESSED_POINT=0</state>
          <state>uECC_VLI_NATIVE_LITTLE_ENDIAN=1</state>
        </option>
        <option>
          <name>AList</name>
          <state>0</state>
        </option>
        <option>
          <name>AListHeader</name>
          <state>1</state>
        </option>
        <option>
          <name>AListing</name>
          <state>1</state>
        </option>
        <option>
          <name>Includes</name>
          <state>0</state>
        </option>
        <option>
          <name>MacDefs</name>
          <state>0</state>
        </option>
        <option>
          <name>MacExps</name>
          <state>1</state>
        </option>
        <option>
          <name>MacExec</name>
          <state>0</state>
        </option>
        <option>
          <name>OnlyAssed</name>
          <state>0</state>
        </option>
        <option>
          <name>MultiLine</name>
          <state>0</state>
        </option>
        <option>
          <name>PageLengthCheck</name>
          <state>0</state>
        </option>
        <option>
          <name>PageLength</name>
          <state>80</state>
        </option>
        <option>
          <name>TabSpacing</name>
          <state>8</state>
        </option>
        <option>
          <name>AXRef</name>
          <state>0</state>
        </option>
        <option>
          <name>AXRefDefines</name>
          <state>0</state>
        </option>
        <option>
          <name>AXRefInternal</name>
          <state>0</state>
        </option>
        <option>
          <name>AXRefDual</name>
          <state>0</state>
        </option>
        <option>
          <name>AProcessor</name>
          <state>1</state>
        </option>
        <option>
          <name>AFpuProcessor</name>
          <state>1</state>
        </option>
        <option>
          <name>AOutputFile</name>
          <state>$FILE_BNAME$.o</state>
        </option>
        <option>
          <name>AMultibyteSupport</name>
          <state>0</state>
        </option>
        <option>
          <name>ALimitErrorsCheck</name>
          <state>0</state>
        </option>
        <option>
          <name>ALimitErrorsEdit</name>
          <state>100</state>
        </option>
        <option>
          <name>AIgnoreStdInclude</name>
          <state>0</state>
        </option>
        <option>
          <name>AUserIncludes</name>
          <state>$PROJ_DIR$\..\..\..\config</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\..\components</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\..\components\boards</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\..\components\drivers_nrf\nrf_soc_nosd</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\..\components\libraries\atomic</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\..\components\libraries\balloc</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\..\components\libraries\bsp</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\..\components\libraries\crypto</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\..\components\libraries\crypto\backend\cc310</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\..\components\libraries\crypto\backend\cc310_bl</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\..\components\libraries\crypto\backend\cifra</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\..\components\libraries\crypto\backend\mbedtls</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\..\components\libraries\crypto\backend\micro_ecc</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\..\components\libraries\crypto\backend\nrf_hw</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\..\components\libraries\crypto\backend\nrf_sw</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\..\components\libraries\crypto\backend\oberon</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\..\components\libraries\crypto\backend\optiga</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\..\components\libraries\delay</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\..\components\libraries\experimental_section_vars</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\..\components\libraries\log</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\..\components\libraries\log\src</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\..\components\libraries\mem_manager</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\..\components\libraries\memobj</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\..\components\libraries\mutex</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\..\components\libraries\queue</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\..\components\libraries\ringbuf</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\..\components\libraries\stack_info</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\..\components\libraries\strerror</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\..\components\libraries\util</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\..\components\toolchain\cmsis\include</state>
          <state>$PROJ_DIR$\..\..\..</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\..\external\cifra_AES128-EAX</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\..\external\fprintf</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\..\external\mbedtls\include</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\..\external\micro-ecc\micro-ecc</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\..\external\nrf_cc310\include</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\..\external\nrf_oberon</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\..\external\nrf_oberon\include</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\..\external\nrf_tls\mbedtls\nrf_crypto\config</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\..\external\segger_rtt</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\..\integration\nrfx</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\..\integration\nrfx\legacy</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\..\modules\nrfx</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\..\modules\nrfx\drivers\include</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\..\modules\nrfx\hal</state>
          <state>$PROJ_DIR$\..\..\..\..\..\..\..\modules\nrfx\mdk</state>
          <state>$PROJ_DIR$\..\config</state>
        </option>
        <option>
          <name>AExtraOptionsCheckV2</name>
          <state>0</state>
        </option>
        <option>
          <name>AExtraOptionsV2</name>
          <state></state>
        </option>
        <option>
          <name>AsmNoLiteralPool</name>
          <state>0</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>OBJCOPY</name>
      <archiveVersion>0</archiveVersion>
      <data>
        <version>1</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>0</debug>
        <option>
          <name>OOCOutputFormat</name>
          <version>2</version>
          <state>1</state>
        </option>
        <option>
          <name>OCOutputOverride</name>
          <state>1</state>
        </option>
        <option>
          <name>OOCOutputFile</name>
          <state>nrf_crypto_rng_pca10056.hex</state>
        </option>
        <option>
          <name>OOCCommandLineProducer</name>
          <state>1</state>
        </option>
        <option>
          <name>OOCObjCopyEnable</name>
          <state>1</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>CUSTOM</name>
      <archiveVersion>3</archiveVersion>
      <data>
        <extensions></extensions>
        <cmdline></cmdline>
      </data>
    </settings>
    <settings>
      <name>BICOMP</name>
      <archiveVersion>0</archiveVersion>
      <data/>
    </settings>
    <settings>
      <name>BUILDACTION</name>
      <archiveVersion>1</archiveVersion>
      <data>
        <prebuild></prebuild>
        <postbuild></postbuild>
      </data>
    </settings>
    <settings>
      <name>ILINK</name>
      <archiveVersion>0</archiveVersion>
      <data>
        <version>16</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>0</debug>
        <option>
          <name>IlinkLibIOConfig</name>
          <state>1</state>
        </option>
        <option>
          <name>XLinkMisraHandler</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkInputFileSlave</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkOutputFile</name>
          <state>nrf_crypto_rng_pca10056.out</state>
        </option>
        <option>
          <name>IlinkDebugInfoEnable</name>
          <state>1</state>
        </option>
        <option>
          <name>IlinkKeepSymbols</name>
          <state></state>
        </option>
        <option>
          <name>IlinkRawBinaryFile</name>
          <state></state>
        </option>
        <option>
          <name>IlinkRawBinarySymbol</name>
          <state></state>
        </option>
        <option>
          <name>IlinkRawBinarySegment</name>
          <state></state>
        </option>
        <option>
          <name>IlinkRawBinaryAlign</name>
          <state></state>
        </option>
        <option>
          <name>IlinkDefines</name>
          <state></state>
        </option>
        <option>
          <name>IlinkConfigDefines</name>
          <state></state>
        </option>
        <option>
          <name>IlinkMapFile</name>
          <state>1</state>
        </option>
        <option>
          <name>IlinkLogFile</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkLogInitialization</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkLogModule</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkLogSection</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkLogVeneer</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkIcfOverride</name>
          <state>1</state>
        </option>
        <option>
          <name>IlinkIcfFile</name>
      <state>$PROJ_DIR$\nrf_crypto_rng_iar_nRF5x.icf</state>
        </option>
        <option>
          <name>IlinkIcfFileSlave</name>
          <state></state>
        </option>
        <option>
          <name>IlinkEnableRemarks</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkSuppressDiags</name>
          <state></state>
        </option>
        <option>
          <name>IlinkTreatAsRem</name>
          <state></state>
        </option>
        <option>
          <name>IlinkTreatAsWarn</name>
          <state></state>
        </option>
        <option>
          <name>IlinkTreatAsErr</name>
          <state></state>
        </option>
        <option>
          <name>IlinkWarningsAreErrors</name>
          <state>1</state>
        </option>
        <option>
          <name>IlinkUseExtraOptions</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkExtraOptions</name>
          <state></state>
        </option>
        <option>
          <name>IlinkLowLevelInterfaceSlave</name>
          <state>1</state>
        </option>
        <option>
          <name>IlinkAutoLibEnable</name>
          <state>1</state>
        </option>
        <option>
          <name>IlinkAdditionalLibs</name>
          <state></state>
        </option>
        <option>
          <name>IlinkOverrideProgramEntryLabel</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkProgramEntryLabelSelect</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkProgramEntryLabel</name>
          <state>__iar_program_start</state>
        </option>
        <option>
          <name>DoFill</name>
          <state>0</state>
        </option>
        <option>
          <name>FillerByte</name>
          <state>0xFF</state>
        </option>
        <option>
          <name>FillerStart</name>
          <state>0x0</state>
        </option>
        <option>
          <name>FillerEnd</name>
          <state>0x0</state>
        </option>
        <option>
          <name>CrcSize</name>
          <version>0</version>
          <state>1</state>
        </option>
        <option>
          <name>CrcAlign</name>
          <state>1</state>
        </option>
        <option>
          <name>CrcPoly</name>
          <state>0x11021</state>
        </option>
        <option>
          <name>CrcCompl</name>
          <version>0</version>
          <state>0</state>
        </option>
        <option>
          <name>CrcBitOrder</name>
          <version>0</version>
          <state>0</state>
        </option>
        <option>
          <name>CrcInitialValue</name>
          <state>0x0</state>
        </option>
        <option>
          <name>DoCrc</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkBE8Slave</name>
          <state>1</state>
        </option>
        <option>
          <name>IlinkBufferedTerminalOutput</name>
          <state>1</state>
        </option>
        <option>
          <name>IlinkStdoutInterfaceSlave</name>
          <state>1</state>
        </option>
        <option>
          <name>CrcFullSize</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkIElfToolPostProcess</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkLogAutoLibSelect</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkLogRedirSymbols</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkLogUnusedFragments</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkCrcReverseByteOrder</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkCrcUseAsInput</name>
          <state>1</state>
        </option>
        <option>
          <name>IlinkOptInline</name>
          <state>1</state>
        </option>
        <option>
          <name>IlinkOptExceptionsAllow</name>
          <state>1</state>
        </option>
        <option>
          <name>IlinkOptExceptionsForce</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkCmsis</name>
          <state>1</state>
        </option>
        <option>
          <name>IlinkOptMergeDuplSections</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkOptUseVfe</name>
          <state>1</state>
        </option>
        <option>
          <name>IlinkOptForceVfe</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkStackAnalysisEnable</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkStackControlFile</name>
          <state></state>
        </option>
        <option>
          <name>IlinkStackCallGraphFile</name>
          <state></state>
        </option>
        <option>
          <name>CrcAlgorithm</name>
          <version>0</version>
          <state>1</state>
        </option>
        <option>
          <name>CrcUnitSize</name>
          <version>0</version>
          <state>0</state>
        </option>
        <option>
          <name>IlinkThreadsSlave</name>
          <state>1</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>IARCHIVE</name>
      <archiveVersion>0</archiveVersion>
      <data>
        <version>0</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>0</debug>
        <option>
          <name>IarchiveInputs</name>
          <state></state>
        </option>
        <option>
          <name>IarchiveOverride</name>
          <state>0</state>
        </option>
        <option>
          <name>IarchiveOutput</name>
          <state>###Unitialized###</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>BILINK</name>
      <archiveVersion>0</archiveVersion>
      <data/>
    </settings>
  </configuration>  <group>
  <name>nrf_cc310</name>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\external\nrf_cc310\lib\cortex-m4\hard-float\short-wchar\libnrf_cc310_0.9.13.a</name>    </file>  </group>  <group>
  <name>nRF_Crypto backend nRF HW</name>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\components\libraries\crypto\backend\nrf_hw\nrf_hw_backend_init.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\components\libraries\crypto\backend\nrf_hw\nrf_hw_backend_rng.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\components\libraries\crypto\backend\nrf_hw\nrf_hw_backend_rng_mbedtls.c</name>    </file>  </group>  <group>
  <name>nRF_Libraries</name>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\components\libraries\util\app_error.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\components\libraries\util\app_error_handler_iar.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\components\libraries\util\app_error_weak.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\components\libraries\util\app_util_platform.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\external\cifra_AES128-EAX\blockwise.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\external\cifra_AES128-EAX\cifra_cmac.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\external\cifra_AES128-EAX\cifra_eax_aes.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\external\cifra_AES128-EAX\eax.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\external\cifra_AES128-EAX\gf128.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\components\libraries\mem_manager\mem_manager.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\external\cifra_AES128-EAX\modes.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\components\libraries\util\nrf_assert.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\components\libraries\atomic\nrf_atomic.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\components\libraries\balloc\nrf_balloc.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\external\fprintf\nrf_fprintf.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\external\fprintf\nrf_fprintf_format.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\components\libraries\memobj\nrf_memobj.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\components\libraries\queue\nrf_queue.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\components\libraries\ringbuf\nrf_ringbuf.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\components\libraries\strerror\nrf_strerror.c</name>    </file>  </group>  <group>
  <name>nRF_Crypto backend uECC</name>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\components\libraries\crypto\backend\micro_ecc\micro_ecc_backend_ecc.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\components\libraries\crypto\backend\micro_ecc\micro_ecc_backend_ecdh.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\components\libraries\crypto\backend\micro_ecc\micro_ecc_backend_ecdsa.c</name>    </file>  </group>  <group>
  <name>nRF_Log</name>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\components\libraries\log\src\nrf_log_backend_rtt.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\components\libraries\log\src\nrf_log_backend_serial.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\components\libraries\log\src\nrf_log_backend_uart.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\components\libraries\log\src\nrf_log_default_backends.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\components\libraries\log\src\nrf_log_frontend.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\components\libraries\log\src\nrf_log_str_formatter.c</name>    </file>  </group>  <group>
  <name>Board Definition</name>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\components\boards\boards.c</name>    </file>  </group>  <group>
  <name>nRF_Crypto backend mbed TLS</name>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\components\libraries\crypto\backend\mbedtls\mbedtls_backend_aes.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\components\libraries\crypto\backend\mbedtls\mbedtls_backend_aes_aead.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\components\libraries\crypto\backend\mbedtls\mbedtls_backend_ecc.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\components\libraries\crypto\backend\mbedtls\mbedtls_backend_ecdh.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\components\libraries\crypto\backend\mbedtls\mbedtls_backend_ecdsa.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\components\libraries\crypto\backend\mbedtls\mbedtls_backend_hash.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\components\libraries\crypto\backend\mbedtls\mbedtls_backend_hmac.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\components\libraries\crypto\backend\mbedtls\mbedtls_backend_init.c</name>    </file>  </group>  <group>
  <name>nRF_Drivers</name>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\integration\nrfx\legacy\nrf_drv_rng.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\integration\nrfx\legacy\nrf_drv_uart.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\modules\nrfx\soc\nrfx_atomic.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\modules\nrfx\drivers\src\prs\nrfx_prs.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\modules\nrfx\drivers\src\nrfx_rng.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\modules\nrfx\drivers\src\nrfx_uart.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\modules\nrfx\drivers\src\nrfx_uarte.c</name>    </file>  </group>  <group>
  <name>nRF_Crypto backend cifra</name>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\components\libraries\crypto\backend\cifra\cifra_backend_aes_aead.c</name>    </file>  </group>  <group>
  <name>nRF_Crypto</name>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\components\libraries\crypto\nrf_crypto_aead.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\components\libraries\crypto\nrf_crypto_aes.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\components\libraries\crypto\nrf_crypto_aes_shared.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\components\libraries\crypto\nrf_crypto_ecc.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\components\libraries\crypto\nrf_crypto_ecdh.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\components\libraries\crypto\nrf_crypto_ecdsa.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\components\libraries\crypto\nrf_crypto_eddsa.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\components\libraries\crypto\nrf_crypto_error.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\components\libraries\crypto\nrf_crypto_hash.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\components\libraries\crypto\nrf_crypto_hkdf.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\components\libraries\crypto\nrf_crypto_hmac.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\components\libraries\crypto\nrf_crypto_init.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\components\libraries\crypto\nrf_crypto_rng.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\components\libraries\crypto\nrf_crypto_shared.c</name>    </file>  </group>  <group>
  <name>Application</name>    <file>
    <name>$PROJ_DIR$\..\..\..\main.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\config\sdk_config.h</name>    </file>  </group>  <group>
  <name>nRF_micro-ecc</name>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\external\micro-ecc\nrf52hf_iar\armgcc\micro_ecc_lib_nrf52.a</name>    </file>  </group>  <group>
  <name>nRF_TLS</name>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\external\mbedtls\library\aes.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\external\mbedtls\library\aesni.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\external\mbedtls\library\arc4.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\external\mbedtls\library\aria.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\external\mbedtls\library\asn1parse.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\external\mbedtls\library\asn1write.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\external\mbedtls\library\base64.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\external\mbedtls\library\bignum.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\external\mbedtls\library\blowfish.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\external\mbedtls\library\camellia.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\external\mbedtls\library\ccm.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\external\mbedtls\library\certs.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\external\mbedtls\library\chacha20.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\external\mbedtls\library\chachapoly.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\external\mbedtls\library\cipher.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\external\mbedtls\library\cipher_wrap.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\external\mbedtls\library\cmac.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\external\mbedtls\library\ctr_drbg.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\external\mbedtls\library\debug.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\external\mbedtls\library\des.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\external\mbedtls\library\dhm.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\external\mbedtls\library\ecdh.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\external\mbedtls\library\ecdsa.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\external\mbedtls\library\ecjpake.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\external\mbedtls\library\ecp.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\external\mbedtls\library\ecp_curves.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\external\mbedtls\library\entropy.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\external\mbedtls\library\entropy_poll.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\external\mbedtls\library\error.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\external\mbedtls\library\gcm.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\external\mbedtls\library\havege.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\external\mbedtls\library\hmac_drbg.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\external\mbedtls\library\md.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\external\mbedtls\library\md2.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\external\mbedtls\library\md4.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\external\mbedtls\library\md5.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\external\mbedtls\library\md_wrap.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\external\mbedtls\library\memory_buffer_alloc.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\external\mbedtls\library\net_sockets.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\external\mbedtls\library\nist_kw.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\external\mbedtls\library\oid.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\external\mbedtls\library\padlock.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\external\mbedtls\library\pem.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\external\mbedtls\library\pk.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\external\mbedtls\library\pk_wrap.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\external\mbedtls\library\pkcs11.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\external\mbedtls\library\pkcs12.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\external\mbedtls\library\pkcs5.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\external\mbedtls\library\pkparse.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\external\mbedtls\library\pkwrite.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\external\mbedtls\library\platform.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\external\mbedtls\library\platform_util.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\external\mbedtls\library\poly1305.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\external\mbedtls\library\ripemd160.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\external\mbedtls\library\rsa.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\external\mbedtls\library\rsa_internal.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\external\mbedtls\library\sha1.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\external\mbedtls\library\sha256.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\external\mbedtls\library\sha512.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\external\mbedtls\library\ssl_cache.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\external\mbedtls\library\ssl_ciphersuites.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\external\mbedtls\library\ssl_cli.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\external\mbedtls\library\ssl_cookie.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\external\mbedtls\library\ssl_srv.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\external\mbedtls\library\ssl_ticket.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\external\mbedtls\library\ssl_tls.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\external\mbedtls\library\threading.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\external\mbedtls\library\version.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\external\mbedtls\library\version_features.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\external\mbedtls\library\x509.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\external\mbedtls\library\x509_create.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\external\mbedtls\library\x509_crl.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\external\mbedtls\library\x509_crt.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\external\mbedtls\library\x509_csr.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\external\mbedtls\library\x509write_crt.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\external\mbedtls\library\x509write_csr.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\external\mbedtls\library\xtea.c</name>    </file>  </group>  <group>
  <name>nRF_Oberon_Crypto</name>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\external\nrf_oberon\lib\cortex-m4\hard-float\short-wchar\liboberon_3.0.5.a</name>    </file>  </group>  <group>
  <name>None</name>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\modules\nrfx\mdk\iar_startup_nrf52840.s</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\modules\nrfx\mdk\system_nrf52840.c</name>    </file>  </group>  <group>
  <name>nRF_Crypto backend CC310</name>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\components\libraries\crypto\backend\cc310\cc310_backend_aes.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\components\libraries\crypto\backend\cc310\cc310_backend_aes_aead.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\components\libraries\crypto\backend\cc310\cc310_backend_chacha_poly_aead.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\components\libraries\crypto\backend\cc310\cc310_backend_ecc.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\components\libraries\crypto\backend\cc310\cc310_backend_ecdh.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\components\libraries\crypto\backend\cc310\cc310_backend_ecdsa.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\components\libraries\crypto\backend\cc310\cc310_backend_eddsa.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\components\libraries\crypto\backend\cc310\cc310_backend_hash.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\components\libraries\crypto\backend\cc310\cc310_backend_hmac.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\components\libraries\crypto\backend\cc310\cc310_backend_init.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\components\libraries\crypto\backend\cc310\cc310_backend_mutex.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\components\libraries\crypto\backend\cc310\cc310_backend_rng.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\components\libraries\crypto\backend\cc310\cc310_backend_shared.c</name>    </file>  </group>  <group>
  <name>nRF_Segger_RTT</name>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\external\segger_rtt\SEGGER_RTT.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\external\segger_rtt\SEGGER_RTT_Syscalls_IAR.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\external\segger_rtt\SEGGER_RTT_printf.c</name>    </file>  </group>  <group>
  <name>nRF_Crypto backend Oberon</name>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\components\libraries\crypto\backend\oberon\oberon_backend_chacha_poly_aead.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\components\libraries\crypto\backend\oberon\oberon_backend_ecc.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\components\libraries\crypto\backend\oberon\oberon_backend_ecdh.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\components\libraries\crypto\backend\oberon\oberon_backend_ecdsa.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\components\libraries\crypto\backend\oberon\oberon_backend_eddsa.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\components\libraries\crypto\backend\oberon\oberon_backend_hash.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\..\..\components\libraries\crypto\backend\oberon\oberon_backend_hmac.c</name>    </file>  </group></project>


