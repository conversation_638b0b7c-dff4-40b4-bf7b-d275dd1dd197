<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html lang="en">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<meta http-equiv="Content-Style-Type" content="text/css">
<link rel="up" title="FatFs" href="../00index_e.html">
<link rel="alternate" hreflang="ja" title="Japanese" href="../ja/printf.html">
<link rel="stylesheet" href="../css_e.css" type="text/css" media="screen" title="ELM Default">
<title>FatFs - f_printf</title>
</head>

<body>

<div class="para func">
<h2>f_printf</h2>
<p>The f_printf function writes formatted string to the file.</p>
<pre>
int f_printf (
  FIL* <span class="arg">fp</span>,          <span class="c">/* [IN] File object */</span>
  const TCHAR* <span class="arg">fmt</span>, <span class="c">/* [IN] Format stirng */</span>
  ...
);
</pre>
</div>

<div class="para arg">
<h4>Parameters</h4>
<dl class="par">
<dt>fp</dt>
<dd>Pointer to the open file object structure.</dd>
<dt>fmt</dt>
<dd>Pointer to the null terminated format string. The terminator charactor will not be written.</dd>
<dt>...</dt>
<dd>Optional arguments...</dd>

</dl>
</div>


<div class="para ret">
<h4>Return Values</h4>
<p>When the function succeeded, it returns number of characters written. If the function could not write the generated string to the file due to disk full or an error, <tt>EOF (-1)</tt> will be returned.</p>
</div>


<div class="para desc">
<h4>Description</h4>
<p>The format control directive is a sub-set of standard library shown as follos:</p>
<ul>
<li>Type: <tt>c C s S d D u U x X b B</tt></li>
<li>Size: <tt>l L</tt></li>
<li>Flag: <tt>0 -</tt></li>
</ul>
</div>


<div class="para comp">
<h4>QuickInfo</h4>
<p>This is a wrapper function of <a href="write.html"><tt>f_write</tt></a> function. Available when <tt>_FS_READONLY == 0</tt> and <tt>_USE_STRFUNC</tt> is 1 or 2. When it is set to 2, <tt>'\n'</tt>s contained in the output are converted to <tt>'\r'+'\n'</tt>.</p>
<p>When FatFs is configured to Unicode API (<tt>_LFN_UNICODE == 1</tt>), data types on the srting fuctions, <tt>f_putc</tt>, <tt>f_puts</tt>, <tt>f_printf</tt> and <tt>f_gets</tt> function, is also switched to Unicode. The character encoding on the file to be read/written via those functions is selected by <tt>_STRF_ENCODE</tt> option.</p>
</div>


<div class="para use">
<h4>Example</h4>
<pre>
    f_printf(&amp;fil, "%d", 1234);            <span class="c">/* "1234" */</span>
    f_printf(&amp;fil, "%6d,%3d%%", -200, 5);  <span class="c">/* "  -200,  5%" */</span>
    f_printf(&amp;fil, "%ld", 12345L);         <span class="c">/* "12345" */</span>
    f_printf(&amp;fil, "%06d", 25);            <span class="c">/* "000025" */</span>
    f_printf(&amp;fil, "%06d", -25);           <span class="c">/* "000-25" */</span>
    f_printf(&amp;fil, "%-6d", 25);            <span class="c">/* "25    " */</span>
    f_printf(&amp;fil, "%u", -1);              <span class="c">/* "65535" or "4294967295" */</span>
    f_printf(&amp;fil, "%04x", 0xAB3);         <span class="c">/* "0ab3" */</span>
    f_printf(&amp;fil, "%08LX", 0x123ABCL);    <span class="c">/* "00123ABC" */</span>
    f_printf(&amp;fil, "%016b", 0x550F);       <span class="c">/* "0101010100001111" */</span>
    f_printf(&amp;fil, "%s", "String");        <span class="c">/* "String" */</span>
    f_printf(&amp;fil, "%8s", "abc");          <span class="c">/* "     abc" */</span>
    f_printf(&amp;fil, "%-8s", "abc");         <span class="c">/* "abc     " */</span>
    f_printf(&amp;fil, "%c", 'a');             <span class="c">/* "a" */</span>
    f_printf(&amp;fil, "%f", 10.0);            <span class="c">/* f_printf lacks floating point support */</span>
</pre>
</div>


<div class="para ref">
<h4>See Also</h4>
<p><tt><a href="open.html">f_open</a>, <a href="putc.html">f_putc</a>, <a href="puts.html">f_puts</a>, <a href="gets.html">f_gets</a>, <a href="close.html">f_close</a>, <a href="sfile.html">FIL</a></tt></p>
</div>

<p class="foot"><a href="../00index_e.html">Return</a></p>
</body>
</html>
