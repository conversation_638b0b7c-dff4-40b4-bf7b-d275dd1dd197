Component: ARM Compiler 5.06 update 7 (build 960) Tool: armlink [4d3601]

==============================================================================

Section Cross References

    main.o(i.assert_nrf_callback) refers to app_error_handler_keil.o(.emb_text) for app_error_handler
    main.o(i.ble_evt_handler) refers to ble_nus_c.o(i.ble_nus_c_handles_assign) for ble_nus_c_handles_assign
    main.o(i.ble_evt_handler) refers to app_error.o(i.app_error_handler_bare) for app_error_handler_bare
    main.o(i.ble_evt_handler) refers to bsp.o(i.bsp_indication_set) for bsp_indication_set
    main.o(i.ble_evt_handler) refers to ble_db_discovery.o(i.ble_db_discovery_start) for ble_db_discovery_start
    main.o(i.ble_evt_handler) refers to main.o(.bss) for .bss
    main.o(i.ble_evt_handler) refers to main.o(.data) for .data
    main.o(i.ble_evt_handler) refers to main.o(.constdata) for .constdata
    main.o(i.ble_nus_c_evt_handler) refers to main.o(i.scan_start) for scan_start
    main.o(i.ble_nus_c_evt_handler) refers to ble_nus_c.o(i.ble_nus_c_handles_assign) for ble_nus_c_handles_assign
    main.o(i.ble_nus_c_evt_handler) refers to app_error.o(i.app_error_handler_bare) for app_error_handler_bare
    main.o(i.ble_nus_c_evt_handler) refers to ble_nus_c.o(i.ble_nus_c_tx_notif_enable) for ble_nus_c_tx_notif_enable
    main.o(i.ble_nus_c_evt_handler) refers to memcpya.o(.text) for __aeabi_memcpy
    main.o(i.ble_nus_c_evt_handler) refers to main.o(.data) for .data
    main.o(i.ble_nus_c_evt_handler) refers to main.o(.bss) for .bss
    main.o(i.bsp_event_handler) refers to nrf_pwr_mgmt.o(i.nrf_pwr_mgmt_shutdown) for nrf_pwr_mgmt_shutdown
    main.o(i.bsp_event_handler) refers to app_error.o(i.app_error_handler_bare) for app_error_handler_bare
    main.o(i.bsp_event_handler) refers to main.o(.bss) for .bss
    main.o(i.db_disc_handler) refers to ble_nus_c.o(i.ble_nus_c_on_db_disc_evt) for ble_nus_c_on_db_disc_evt
    main.o(i.db_disc_handler) refers to main.o(.bss) for .bss
    main.o(i.gatt_evt_handler) refers to main.o(.data) for .data
    main.o(i.gatt_init) refers to nrf_ble_gatt.o(i.nrf_ble_gatt_init) for nrf_ble_gatt_init
    main.o(i.gatt_init) refers to app_error.o(i.app_error_handler_bare) for app_error_handler_bare
    main.o(i.gatt_init) refers to nrf_ble_gatt.o(i.nrf_ble_gatt_att_mtu_central_set) for nrf_ble_gatt_att_mtu_central_set
    main.o(i.gatt_init) refers to main.o(i.gatt_evt_handler) for gatt_evt_handler
    main.o(i.gatt_init) refers to main.o(.bss) for .bss
    main.o(i.main) refers to app_timer2.o(i.app_timer_init) for app_timer_init
    main.o(i.main) refers to app_error.o(i.app_error_handler_bare) for app_error_handler_bare
    main.o(i.main) refers to memcpya.o(.text) for __aeabi_memcpy4
    main.o(i.main) refers to app_uart_fifo.o(i.app_uart_init) for app_uart_init
    main.o(i.main) refers to ble_db_discovery.o(i.ble_db_discovery_init) for ble_db_discovery_init
    main.o(i.main) refers to nrf_pwr_mgmt.o(i.nrf_pwr_mgmt_init) for nrf_pwr_mgmt_init
    main.o(i.main) refers to nrf_sdh.o(i.nrf_sdh_enable_request) for nrf_sdh_enable_request
    main.o(i.main) refers to nrf_sdh_ble.o(i.nrf_sdh_ble_default_cfg_set) for nrf_sdh_ble_default_cfg_set
    main.o(i.main) refers to nrf_sdh_ble.o(i.nrf_sdh_ble_enable) for nrf_sdh_ble_enable
    main.o(i.main) refers to main.o(i.gatt_init) for gatt_init
    main.o(i.main) refers to ble_nus_c.o(i.ble_nus_c_init) for ble_nus_c_init
    main.o(i.main) refers to main.o(i.scan_init) for scan_init
    main.o(i.main) refers to main.o(i.scan_start) for scan_start
    main.o(i.main) refers to nrf_pwr_mgmt.o(i.nrf_pwr_mgmt_run) for nrf_pwr_mgmt_run
    main.o(i.main) refers to app_uart_fifo.o(i.app_uart_put) for app_uart_put
    main.o(i.main) refers to main.o(.constdata) for .constdata
    main.o(i.main) refers to main.o(.bss) for .bss
    main.o(i.main) refers to main.o(i.uart_event_handle) for uart_event_handle
    main.o(i.main) refers to main.o(i.db_disc_handler) for db_disc_handler
    main.o(i.main) refers to main.o(.data) for .data
    main.o(i.main) refers to main.o(i.ble_nus_c_evt_handler) for ble_nus_c_evt_handler
    main.o(i.main) refers to main.o(i.nus_error_handler) for nus_error_handler
    main.o(i.nus_error_handler) refers to app_error.o(i.app_error_handler_bare) for app_error_handler_bare
    main.o(i.scan_evt_handler) refers to app_error.o(i.app_error_handler_bare) for app_error_handler_bare
    main.o(i.scan_evt_handler) refers to main.o(i.scan_start) for scan_start
    main.o(i.scan_init) refers to nrf_ble_scan.o(i.nrf_ble_scan_init) for nrf_ble_scan_init
    main.o(i.scan_init) refers to app_error.o(i.app_error_handler_bare) for app_error_handler_bare
    main.o(i.scan_init) refers to nrf_ble_scan.o(i.nrf_ble_scan_filter_set) for nrf_ble_scan_filter_set
    main.o(i.scan_init) refers to nrf_ble_scan.o(i.nrf_ble_scan_filters_enable) for nrf_ble_scan_filters_enable
    main.o(i.scan_init) refers to main.o(i.scan_evt_handler) for scan_evt_handler
    main.o(i.scan_init) refers to main.o(.bss) for .bss
    main.o(i.scan_init) refers to main.o(.constdata) for .constdata
    main.o(i.scan_start) refers to nrf_ble_scan.o(i.nrf_ble_scan_start) for nrf_ble_scan_start
    main.o(i.scan_start) refers to app_error.o(i.app_error_handler_bare) for app_error_handler_bare
    main.o(i.scan_start) refers to bsp.o(i.bsp_indication_set) for bsp_indication_set
    main.o(i.scan_start) refers to main.o(.bss) for .bss
    main.o(i.shutdown_handler) refers to bsp.o(i.bsp_indication_set) for bsp_indication_set
    main.o(i.shutdown_handler) refers to app_error.o(i.app_error_handler_bare) for app_error_handler_bare
    main.o(i.shutdown_handler) refers to bsp_btn_ble.o(i.bsp_btn_ble_sleep_mode_prepare) for bsp_btn_ble_sleep_mode_prepare
    main.o(.constdata) refers to main.o(.bss) for m_ble_gatt_queuereq_queue0_nrf_queue_cb
    main.o(.constdata) refers to main.o(.bss) for m_ble_gatt_queuereq_queue0_nrf_queue_buffer
    main.o(.data) refers to main.o(.data) for m_ble_gatt_queueconn_handles_arr
    main.o(.data) refers to main.o(.constdata) for m_ble_gatt_queuereq_queue
    main.o(.data) refers to main.o(nrf_queue) for m_ble_gatt_queuepurge_queue
    main.o(.data) refers to main.o(nrf_balloc) for m_ble_gatt_queuepool
    main.o(nrf_balloc) refers to main.o(.data) for m_ble_gatt_queuepool_nrf_balloc_cb
    main.o(nrf_balloc) refers to main.o(.data) for m_ble_gatt_queuepool_nrf_balloc_pool_stack
    main.o(nrf_balloc) refers to main.o(.bss) for m_ble_gatt_queuepool_nrf_balloc_pool_mem
    main.o(nrf_queue) refers to main.o(.bss) for m_ble_gatt_queuepurge_queue_nrf_queue_cb
    main.o(nrf_queue) refers to main.o(.data) for m_ble_gatt_queuepurge_queue_nrf_queue_buffer
    main.o(pwr_mgmt_data1) refers to main.o(i.shutdown_handler) for shutdown_handler
    main.o(sdh_ble_observers1) refers to nrf_ble_gatt.o(i.nrf_ble_gatt_on_ble_evt) for nrf_ble_gatt_on_ble_evt
    main.o(sdh_ble_observers1) refers to main.o(.bss) for m_gatt
    main.o(sdh_ble_observers1) refers to ble_db_discovery.o(i.ble_db_discovery_on_ble_evt) for ble_db_discovery_on_ble_evt
    main.o(sdh_ble_observers1) refers to main.o(.data) for m_db_disc
    main.o(sdh_ble_observers1) refers to nrf_ble_scan.o(i.nrf_ble_scan_on_ble_evt) for nrf_ble_scan_on_ble_evt
    main.o(sdh_ble_observers1) refers to nrf_ble_gq.o(i.nrf_ble_gq_on_ble_evt) for nrf_ble_gq_on_ble_evt
    main.o(sdh_ble_observers2) refers to ble_nus_c.o(i.ble_nus_c_on_ble_evt) for ble_nus_c_on_ble_evt
    main.o(sdh_ble_observers2) refers to main.o(.bss) for m_ble_nus_c
    main.o(sdh_ble_observers3) refers to main.o(i.ble_evt_handler) for ble_evt_handler
    boards.o(i.bsp_board_button_idx_to_pin) refers to boards.o(.constdata) for .constdata
    boards.o(i.bsp_board_button_state_get) refers to boards.o(.constdata) for .constdata
    boards.o(i.bsp_board_init) refers to boards.o(i.nrf_gpio_cfg) for nrf_gpio_cfg
    boards.o(i.bsp_board_init) refers to boards.o(i.bsp_board_leds_off) for bsp_board_leds_off
    boards.o(i.bsp_board_init) refers to boards.o(.constdata) for .constdata
    boards.o(i.bsp_board_led_idx_to_pin) refers to boards.o(.constdata) for .constdata
    boards.o(i.bsp_board_led_invert) refers to boards.o(.constdata) for .constdata
    boards.o(i.bsp_board_led_off) refers to boards.o(i.nrf_gpio_pin_write) for nrf_gpio_pin_write
    boards.o(i.bsp_board_led_off) refers to boards.o(.constdata) for .constdata
    boards.o(i.bsp_board_led_on) refers to boards.o(i.nrf_gpio_pin_write) for nrf_gpio_pin_write
    boards.o(i.bsp_board_led_on) refers to boards.o(.constdata) for .constdata
    boards.o(i.bsp_board_led_state_get) refers to boards.o(.constdata) for .constdata
    boards.o(i.bsp_board_leds_off) refers to boards.o(i.bsp_board_led_off) for bsp_board_led_off
    boards.o(i.bsp_board_leds_on) refers to boards.o(i.bsp_board_led_on) for bsp_board_led_on
    boards.o(i.bsp_board_pin_to_button_idx) refers to boards.o(.constdata) for .constdata
    boards.o(i.bsp_board_pin_to_led_idx) refers to boards.o(.constdata) for .constdata
    bsp.o(i.alert_timer_handler) refers to boards.o(i.bsp_board_led_invert) for bsp_board_led_invert
    bsp.o(i.bsp_button_event_handler) refers to boards.o(i.bsp_board_pin_to_button_idx) for bsp_board_pin_to_button_idx
    bsp.o(i.bsp_button_event_handler) refers to app_timer2.o(i.app_timer_start) for app_timer_start
    bsp.o(i.bsp_button_event_handler) refers to app_timer2.o(i.app_timer_stop) for app_timer_stop
    bsp.o(i.bsp_button_event_handler) refers to bsp.o(.constdata) for .constdata
    bsp.o(i.bsp_button_event_handler) refers to bsp.o(.data) for .data
    bsp.o(i.bsp_button_event_handler) refers to bsp.o(.bss) for .bss
    bsp.o(i.bsp_button_is_pressed) refers to boards.o(i.bsp_board_button_state_get) for bsp_board_button_state_get
    bsp.o(i.bsp_buttons_disable) refers to app_button.o(i.app_button_disable) for app_button_disable
    bsp.o(i.bsp_buttons_enable) refers to app_button.o(i.app_button_enable) for app_button_enable
    bsp.o(i.bsp_event_to_button_action_assign) refers to bsp.o(.bss) for .bss
    bsp.o(i.bsp_indication_set) refers to bsp.o(i.bsp_led_indication) for bsp_led_indication
    bsp.o(i.bsp_indication_set) refers to bsp.o(.data) for .data
    bsp.o(i.bsp_init) refers to bsp.o(i.bsp_event_to_button_action_assign) for bsp_event_to_button_action_assign
    bsp.o(i.bsp_init) refers to app_button.o(i.app_button_init) for app_button_init
    bsp.o(i.bsp_init) refers to app_button.o(i.app_button_enable) for app_button_enable
    bsp.o(i.bsp_init) refers to app_timer2.o(i.app_timer_create) for app_timer_create
    bsp.o(i.bsp_init) refers to boards.o(i.bsp_board_init) for bsp_board_init
    bsp.o(i.bsp_init) refers to bsp.o(.data) for .data
    bsp.o(i.bsp_init) refers to bsp.o(.constdata) for .constdata
    bsp.o(i.bsp_init) refers to bsp.o(i.button_timer_handler) for button_timer_handler
    bsp.o(i.bsp_init) refers to bsp.o(i.leds_timer_handler) for leds_timer_handler
    bsp.o(i.bsp_init) refers to bsp.o(i.alert_timer_handler) for alert_timer_handler
    bsp.o(i.bsp_led_indication) refers to bsp.o(i.leds_off) for leds_off
    bsp.o(i.bsp_led_indication) refers to app_timer2.o(i.app_timer_stop) for app_timer_stop
    bsp.o(i.bsp_led_indication) refers to boards.o(i.bsp_board_led_state_get) for bsp_board_led_state_get
    bsp.o(i.bsp_led_indication) refers to boards.o(i.bsp_board_led_off) for bsp_board_led_off
    bsp.o(i.bsp_led_indication) refers to boards.o(i.bsp_board_led_on) for bsp_board_led_on
    bsp.o(i.bsp_led_indication) refers to uldiv.o(.text) for __aeabi_uldivmod
    bsp.o(i.bsp_led_indication) refers to app_timer2.o(i.app_timer_start) for app_timer_start
    bsp.o(i.bsp_led_indication) refers to boards.o(i.bsp_board_led_invert) for bsp_board_led_invert
    bsp.o(i.bsp_led_indication) refers to boards.o(i.bsp_board_leds_on) for bsp_board_leds_on
    bsp.o(i.bsp_led_indication) refers to bsp.o(.data) for .data
    bsp.o(i.bsp_led_indication) refers to bsp.o(.constdata) for .constdata
    bsp.o(i.bsp_wakeup_button_disable) refers to bsp.o(i.wakeup_button_cfg) for wakeup_button_cfg
    bsp.o(i.bsp_wakeup_button_enable) refers to bsp.o(i.wakeup_button_cfg) for wakeup_button_cfg
    bsp.o(i.button_timer_handler) refers to bsp.o(i.bsp_button_event_handler) for bsp_button_event_handler
    bsp.o(i.leds_off) refers to boards.o(i.bsp_board_led_off) for bsp_board_led_off
    bsp.o(i.leds_off) refers to boards.o(i.bsp_board_leds_off) for bsp_board_leds_off
    bsp.o(i.leds_off) refers to bsp.o(.data) for .data
    bsp.o(i.leds_timer_handler) refers to bsp.o(i.bsp_led_indication) for bsp_led_indication
    bsp.o(i.leds_timer_handler) refers to bsp.o(.data) for .data
    bsp.o(i.wakeup_button_cfg) refers to boards.o(i.bsp_board_button_idx_to_pin) for bsp_board_button_idx_to_pin
    bsp.o(.constdata) refers to bsp.o(.bss) for m_bsp_leds_tmr_data
    bsp.o(.constdata) refers to bsp.o(.bss) for m_bsp_alert_tmr_data
    bsp.o(.constdata) refers to bsp.o(.bss) for m_bsp_button_tmr_data
    bsp.o(.constdata) refers to bsp.o(i.bsp_button_event_handler) for bsp_button_event_handler
    bsp_btn_ble.o(i.advertising_buttons_configure) refers to bsp.o(i.bsp_event_to_button_action_assign) for bsp_event_to_button_action_assign
    bsp_btn_ble.o(i.ble_evt_handler) refers to bsp_btn_ble.o(i.advertising_buttons_configure) for advertising_buttons_configure
    bsp_btn_ble.o(i.ble_evt_handler) refers to bsp.o(i.bsp_event_to_button_action_assign) for bsp_event_to_button_action_assign
    bsp_btn_ble.o(i.ble_evt_handler) refers to bsp_btn_ble.o(.data) for .data
    bsp_btn_ble.o(i.bsp_btn_ble_init) refers to bsp.o(i.bsp_button_is_pressed) for bsp_button_is_pressed
    bsp_btn_ble.o(i.bsp_btn_ble_init) refers to bsp_btn_ble.o(i.advertising_buttons_configure) for advertising_buttons_configure
    bsp_btn_ble.o(i.bsp_btn_ble_init) refers to bsp_btn_ble.o(.data) for .data
    bsp_btn_ble.o(i.bsp_btn_ble_sleep_mode_prepare) refers to bsp.o(i.bsp_wakeup_button_enable) for bsp_wakeup_button_enable
    bsp_btn_ble.o(sdh_ble_observers1) refers to bsp_btn_ble.o(i.ble_evt_handler) for ble_evt_handler
    utf.o(i.utf16RuneCount) refers to utf.o(i.utf16DecodeRune) for utf16DecodeRune
    utf.o(i.utf16UTF8Count) refers to utf.o(i.utf16DecodeRune) for utf16DecodeRune
    utf.o(i.utf16UTF8Count) refers to utf.o(i.utf8EncodeRune) for utf8EncodeRune
    utf.o(i.utf8RuneCount) refers to utf.o(i.utf8DecodeRune) for utf8DecodeRune
    utf.o(i.utf8UTF16Count) refers to utf.o(i.utf8DecodeRune) for utf8DecodeRune
    utf.o(i.utf8UTF16Count) refers to utf.o(i.utf16EncodeRune) for utf16EncodeRune
    ble_advdata.o(i.ble_advdata_appearance_find) refers to ble_advdata.o(i.ble_advdata_search) for ble_advdata_search
    ble_advdata.o(i.ble_advdata_encode) refers to ble_advdata.o(i.ble_device_addr_encode) for ble_device_addr_encode
    ble_advdata.o(i.ble_advdata_encode) refers to ble_advdata.o(i.uint16_encode) for uint16_encode
    ble_advdata.o(i.ble_advdata_encode) refers to ble_advdata.o(i.uuid_list_encode) for uuid_list_encode
    ble_advdata.o(i.ble_advdata_encode) refers to ble_advdata.o(i.conn_int_encode) for conn_int_encode
    ble_advdata.o(i.ble_advdata_encode) refers to ble_advdata.o(i.manuf_specific_data_encode) for manuf_specific_data_encode
    ble_advdata.o(i.ble_advdata_encode) refers to ble_advdata.o(i.service_data_encode) for service_data_encode
    ble_advdata.o(i.ble_advdata_encode) refers to ble_advdata.o(i.name_encode) for name_encode
    ble_advdata.o(i.ble_advdata_name_find) refers to ble_advdata.o(i.ble_advdata_search) for ble_advdata_search
    ble_advdata.o(i.ble_advdata_name_find) refers to strlen.o(.text) for strlen
    ble_advdata.o(i.ble_advdata_name_find) refers to memcmp.o(.text) for memcmp
    ble_advdata.o(i.ble_advdata_parse) refers to ble_advdata.o(i.ble_advdata_search) for ble_advdata_search
    ble_advdata.o(i.ble_advdata_short_name_find) refers to ble_advdata.o(i.ble_advdata_search) for ble_advdata_search
    ble_advdata.o(i.ble_advdata_short_name_find) refers to strlen.o(.text) for strlen
    ble_advdata.o(i.ble_advdata_short_name_find) refers to memcmp.o(.text) for memcmp
    ble_advdata.o(i.ble_advdata_uuid_find) refers to ble_advdata.o(i.ble_advdata_search) for ble_advdata_search
    ble_advdata.o(i.ble_advdata_uuid_find) refers to memcmp.o(.text) for memcmp
    ble_advdata.o(i.conn_int_encode) refers to ble_advdata.o(i.uint16_encode) for uint16_encode
    ble_advdata.o(i.manuf_specific_data_encode) refers to ble_advdata.o(i.uint16_encode) for uint16_encode
    ble_advdata.o(i.manuf_specific_data_encode) refers to memcpya.o(.text) for __aeabi_memcpy
    ble_advdata.o(i.service_data_encode) refers to ble_advdata.o(i.uint16_encode) for uint16_encode
    ble_advdata.o(i.service_data_encode) refers to memcpya.o(.text) for __aeabi_memcpy
    ble_advdata.o(i.uuid_list_encode) refers to ble_advdata.o(i.uuid_list_sized_encode) for uuid_list_sized_encode
    ble_db_discovery.o(i.ble_db_discovery_close) refers to ble_db_discovery.o(.data) for .data
    ble_db_discovery.o(i.ble_db_discovery_evt_register) refers to ble_db_discovery.o(i.registered_handler_get) for registered_handler_get
    ble_db_discovery.o(i.ble_db_discovery_evt_register) refers to ble_db_discovery.o(.data) for .data
    ble_db_discovery.o(i.ble_db_discovery_evt_register) refers to ble_db_discovery.o(.bss) for .bss
    ble_db_discovery.o(i.ble_db_discovery_init) refers to ble_db_discovery.o(.data) for .data
    ble_db_discovery.o(i.ble_db_discovery_on_ble_evt) refers to ble_db_discovery.o(i.on_descriptor_discovery_rsp) for on_descriptor_discovery_rsp
    ble_db_discovery.o(i.ble_db_discovery_on_ble_evt) refers to ble_db_discovery.o(i.on_primary_srv_discovery_rsp) for on_primary_srv_discovery_rsp
    ble_db_discovery.o(i.ble_db_discovery_on_ble_evt) refers to ble_db_discovery.o(i.on_characteristic_discovery_rsp) for on_characteristic_discovery_rsp
    ble_db_discovery.o(i.ble_db_discovery_on_ble_evt) refers to ble_db_discovery.o(.data) for .data
    ble_db_discovery.o(i.ble_db_discovery_start) refers to ble_db_discovery.o(i.discovery_start) for discovery_start
    ble_db_discovery.o(i.ble_db_discovery_start) refers to ble_db_discovery.o(.data) for .data
    ble_db_discovery.o(i.characteristics_discover) refers to memseta.o(.text) for __aeabi_memclr4
    ble_db_discovery.o(i.characteristics_discover) refers to nrf_ble_gq.o(i.nrf_ble_gq_item_add) for nrf_ble_gq_item_add
    ble_db_discovery.o(i.characteristics_discover) refers to ble_db_discovery.o(i.discovery_error_handler) for discovery_error_handler
    ble_db_discovery.o(i.characteristics_discover) refers to ble_db_discovery.o(.data) for .data
    ble_db_discovery.o(i.descriptors_discover) refers to memseta.o(.text) for __aeabi_memclr4
    ble_db_discovery.o(i.descriptors_discover) refers to ble_db_discovery.o(i.is_desc_discovery_reqd) for is_desc_discovery_reqd
    ble_db_discovery.o(i.descriptors_discover) refers to nrf_ble_gq.o(i.nrf_ble_gq_item_add) for nrf_ble_gq_item_add
    ble_db_discovery.o(i.descriptors_discover) refers to ble_db_discovery.o(i.discovery_error_handler) for discovery_error_handler
    ble_db_discovery.o(i.descriptors_discover) refers to ble_db_discovery.o(.data) for .data
    ble_db_discovery.o(i.discovery_available_evt_trigger) refers to memseta.o(.text) for __aeabi_memclr4
    ble_db_discovery.o(i.discovery_available_evt_trigger) refers to ble_db_discovery.o(.data) for .data
    ble_db_discovery.o(i.discovery_complete_evt_trigger) refers to ble_db_discovery.o(i.registered_handler_get) for registered_handler_get
    ble_db_discovery.o(i.discovery_complete_evt_trigger) refers to memcpya.o(.text) for __aeabi_memcpy
    ble_db_discovery.o(i.discovery_complete_evt_trigger) refers to ble_db_discovery.o(.data) for .data
    ble_db_discovery.o(i.discovery_error_handler) refers to ble_db_discovery.o(i.registered_handler_get) for registered_handler_get
    ble_db_discovery.o(i.discovery_error_handler) refers to memcpya.o(.text) for __aeabi_memcpy4
    ble_db_discovery.o(i.discovery_error_handler) refers to ble_db_discovery.o(i.discovery_available_evt_trigger) for discovery_available_evt_trigger
    ble_db_discovery.o(i.discovery_error_handler) refers to ble_db_discovery.o(.constdata) for .constdata
    ble_db_discovery.o(i.discovery_start) refers to memseta.o(.text) for __aeabi_memclr4
    ble_db_discovery.o(i.discovery_start) refers to nrf_ble_gq.o(i.nrf_ble_gq_conn_handle_register) for nrf_ble_gq_conn_handle_register
    ble_db_discovery.o(i.discovery_start) refers to nrf_ble_gq.o(i.nrf_ble_gq_item_add) for nrf_ble_gq_item_add
    ble_db_discovery.o(i.discovery_start) refers to ble_db_discovery.o(.data) for .data
    ble_db_discovery.o(i.discovery_start) refers to ble_db_discovery.o(.bss) for .bss
    ble_db_discovery.o(i.discovery_start) refers to ble_db_discovery.o(i.discovery_error_handler) for discovery_error_handler
    ble_db_discovery.o(i.on_characteristic_discovery_rsp) refers to ble_db_discovery.o(i.characteristics_discover) for characteristics_discover
    ble_db_discovery.o(i.on_characteristic_discovery_rsp) refers to ble_db_discovery.o(i.discovery_error_handler) for discovery_error_handler
    ble_db_discovery.o(i.on_characteristic_discovery_rsp) refers to ble_db_discovery.o(i.descriptors_discover) for descriptors_discover
    ble_db_discovery.o(i.on_characteristic_discovery_rsp) refers to ble_db_discovery.o(i.discovery_complete_evt_trigger) for discovery_complete_evt_trigger
    ble_db_discovery.o(i.on_characteristic_discovery_rsp) refers to ble_db_discovery.o(i.on_srv_disc_completion) for on_srv_disc_completion
    ble_db_discovery.o(i.on_descriptor_discovery_rsp) refers to ble_db_discovery.o(i.descriptors_discover) for descriptors_discover
    ble_db_discovery.o(i.on_descriptor_discovery_rsp) refers to ble_db_discovery.o(i.discovery_error_handler) for discovery_error_handler
    ble_db_discovery.o(i.on_descriptor_discovery_rsp) refers to ble_db_discovery.o(i.discovery_complete_evt_trigger) for discovery_complete_evt_trigger
    ble_db_discovery.o(i.on_descriptor_discovery_rsp) refers to ble_db_discovery.o(i.on_srv_disc_completion) for on_srv_disc_completion
    ble_db_discovery.o(i.on_primary_srv_discovery_rsp) refers to ble_db_discovery.o(i.discovery_complete_evt_trigger) for discovery_complete_evt_trigger
    ble_db_discovery.o(i.on_primary_srv_discovery_rsp) refers to ble_db_discovery.o(i.on_srv_disc_completion) for on_srv_disc_completion
    ble_db_discovery.o(i.on_primary_srv_discovery_rsp) refers to ble_db_discovery.o(i.characteristics_discover) for characteristics_discover
    ble_db_discovery.o(i.on_primary_srv_discovery_rsp) refers to ble_db_discovery.o(i.discovery_error_handler) for discovery_error_handler
    ble_db_discovery.o(i.on_srv_disc_completion) refers to memseta.o(.text) for __aeabi_memclr4
    ble_db_discovery.o(i.on_srv_disc_completion) refers to nrf_ble_gq.o(i.nrf_ble_gq_item_add) for nrf_ble_gq_item_add
    ble_db_discovery.o(i.on_srv_disc_completion) refers to ble_db_discovery.o(i.discovery_error_handler) for discovery_error_handler
    ble_db_discovery.o(i.on_srv_disc_completion) refers to ble_db_discovery.o(i.discovery_available_evt_trigger) for discovery_available_evt_trigger
    ble_db_discovery.o(i.on_srv_disc_completion) refers to ble_db_discovery.o(.data) for .data
    ble_db_discovery.o(i.on_srv_disc_completion) refers to ble_db_discovery.o(.bss) for .bss
    ble_db_discovery.o(i.registered_handler_get) refers to ble_db_discovery.o(.bss) for .bss
    ble_db_discovery.o(i.registered_handler_get) refers to ble_db_discovery.o(.data) for .data
    ble_srv_common.o(i.ble_srv_ascii_to_utf8) refers to strlen.o(.text) for strlen
    ble_srv_common.o(i.characteristic_add) refers to ble_srv_common.o(i.set_security_req) for set_security_req
    ble_srv_common.o(i.characteristic_add) refers to memseta.o(.text) for __aeabi_memclr4
    ble_srv_common.o(i.descriptor_add) refers to memseta.o(.text) for __aeabi_memclr4
    ble_srv_common.o(i.descriptor_add) refers to ble_srv_common.o(i.set_security_req) for set_security_req
    nrf_ble_gatt.o(i.nrf_ble_gatt_data_length_set) refers to nrf_ble_gatt.o(i.data_length_update) for data_length_update
    nrf_ble_gatt.o(i.nrf_ble_gatt_init) refers to nrf_ble_gatt.o(i.link_init) for link_init
    nrf_ble_gatt.o(i.nrf_ble_gatt_on_ble_evt) refers to nrf_ble_gatt.o(i.data_length_update) for data_length_update
    nrf_ble_gatt.o(i.nrf_ble_gatt_on_ble_evt) refers to nrf_ble_gatt.o(i.link_init) for link_init
    nrf_ble_gatt.o(i.nrf_ble_gatt_on_ble_evt) refers to memcpya.o(.text) for __aeabi_memcpy4
    nrf_ble_gatt.o(i.nrf_ble_gatt_on_ble_evt) refers to nrf_ble_gatt.o(.constdata) for .constdata
    nrf_ble_gq.o(i.gattc_write_alloc) refers to nrf_memobj.o(i.nrf_memobj_alloc) for nrf_memobj_alloc
    nrf_ble_gq.o(i.gattc_write_alloc) refers to nrf_memobj.o(i.nrf_memobj_write) for nrf_memobj_write
    nrf_ble_gq.o(i.gatts_hvx_alloc) refers to nrf_memobj.o(i.nrf_memobj_alloc) for nrf_memobj_alloc
    nrf_ble_gq.o(i.gatts_hvx_alloc) refers to nrf_memobj.o(i.nrf_memobj_write) for nrf_memobj_write
    nrf_ble_gq.o(i.nrf_ble_gq_conn_handle_register) refers to nrf_ble_gq.o(i.queues_purge) for queues_purge
    nrf_ble_gq.o(i.nrf_ble_gq_conn_handle_register) refers to nrf_ble_gq.o(i.conn_handle_id_find) for conn_handle_id_find
    nrf_ble_gq.o(i.nrf_ble_gq_conn_handle_register) refers to nrf_memobj.o(i.nrf_memobj_pool_init) for nrf_memobj_pool_init
    nrf_ble_gq.o(i.nrf_ble_gq_item_add) refers to nrf_ble_gq.o(i.queues_purge) for queues_purge
    nrf_ble_gq.o(i.nrf_ble_gq_item_add) refers to nrf_ble_gq.o(i.conn_handle_id_find) for conn_handle_id_find
    nrf_ble_gq.o(i.nrf_ble_gq_item_add) refers to nrf_queue.o(i.nrf_queue_is_empty) for nrf_queue_is_empty
    nrf_ble_gq.o(i.nrf_ble_gq_item_add) refers to nrf_ble_gq.o(i.request_process) for request_process
    nrf_ble_gq.o(i.nrf_ble_gq_item_add) refers to nrf_queue.o(i.nrf_queue_push) for nrf_queue_push
    nrf_ble_gq.o(i.nrf_ble_gq_item_add) refers to nrf_memobj.o(i.nrf_memobj_free) for nrf_memobj_free
    nrf_ble_gq.o(i.nrf_ble_gq_item_add) refers to nrf_ble_gq.o(i.queue_process) for queue_process
    nrf_ble_gq.o(i.nrf_ble_gq_item_add) refers to nrf_ble_gq.o(.constdata) for .constdata
    nrf_ble_gq.o(i.nrf_ble_gq_on_ble_evt) refers to nrf_ble_gq.o(i.conn_handle_id_find) for conn_handle_id_find
    nrf_ble_gq.o(i.nrf_ble_gq_on_ble_evt) refers to nrf_ble_gq.o(i.queue_process) for queue_process
    nrf_ble_gq.o(i.nrf_ble_gq_on_ble_evt) refers to nrf_queue.o(i.nrf_queue_push) for nrf_queue_push
    nrf_ble_gq.o(i.queue_process) refers to nrf_queue.o(i.nrf_queue_generic_pop) for nrf_queue_generic_pop
    nrf_ble_gq.o(i.queue_process) refers to nrf_memobj.o(i.nrf_memobj_read) for nrf_memobj_read
    nrf_ble_gq.o(i.queue_process) refers to nrf_memobj.o(i.nrf_memobj_free) for nrf_memobj_free
    nrf_ble_gq.o(i.queue_process) refers to nrf_ble_gq.o(i.request_err_code_handle) for request_err_code_handle
    nrf_ble_gq.o(i.queue_process) refers to nrf_ble_gq.o(.constdata) for .constdata
    nrf_ble_gq.o(i.queues_purge) refers to nrf_queue.o(i.nrf_queue_generic_pop) for nrf_queue_generic_pop
    nrf_ble_gq.o(i.queues_purge) refers to nrf_memobj.o(i.nrf_memobj_free) for nrf_memobj_free
    nrf_ble_gq.o(i.queues_purge) refers to nrf_ble_gq.o(.constdata) for .constdata
    nrf_ble_gq.o(i.request_process) refers to nrf_ble_gq.o(i.request_err_code_handle) for request_err_code_handle
    nrf_ble_gq.o(.constdata) refers to nrf_ble_gq.o(i.gattc_write_alloc) for gattc_write_alloc
    nrf_ble_gq.o(.constdata) refers to nrf_ble_gq.o(i.gatts_hvx_alloc) for gatts_hvx_alloc
    nrf_ble_scan.o(i.nrf_ble_scan_connect_with_target) refers to memseta.o(.text) for __aeabi_memclr4
    nrf_ble_scan.o(i.nrf_ble_scan_filters_enable) refers to nrf_ble_scan.o(i.nrf_ble_scan_filters_disable) for nrf_ble_scan_filters_disable
    nrf_ble_scan.o(i.nrf_ble_scan_init) refers to memcpya.o(.text) for __aeabi_memcpy
    nrf_ble_scan.o(i.nrf_ble_scan_init) refers to nrf_ble_scan.o(i.nrf_ble_scan_default_param_set) for nrf_ble_scan_default_param_set
    nrf_ble_scan.o(i.nrf_ble_scan_init) refers to nrf_ble_scan.o(i.nrf_ble_scan_default_conn_param_set) for nrf_ble_scan_default_conn_param_set
    nrf_ble_scan.o(i.nrf_ble_scan_on_adv_report) refers to memseta.o(.text) for __aeabi_memclr4
    nrf_ble_scan.o(i.nrf_ble_scan_on_adv_report) refers to nrf_ble_scan.o(i.is_whitelist_used) for is_whitelist_used
    nrf_ble_scan.o(i.nrf_ble_scan_on_adv_report) refers to nrf_ble_scan.o(i.nrf_ble_scan_connect_with_target) for nrf_ble_scan_connect_with_target
    nrf_ble_scan.o(i.nrf_ble_scan_on_adv_report) refers to ble_advdata.o(i.ble_advdata_uuid_find) for ble_advdata_uuid_find
    nrf_ble_scan.o(i.nrf_ble_scan_on_ble_evt) refers to nrf_ble_scan.o(i.nrf_ble_scan_on_adv_report) for nrf_ble_scan_on_adv_report
    nrf_ble_scan.o(i.nrf_ble_scan_on_ble_evt) refers to memseta.o(.text) for __aeabi_memclr4
    nrf_ble_scan.o(i.nrf_ble_scan_params_set) refers to memcpya.o(.text) for __aeabi_memcpy
    nrf_ble_scan.o(i.nrf_ble_scan_params_set) refers to nrf_ble_scan.o(i.nrf_ble_scan_default_param_set) for nrf_ble_scan_default_param_set
    nrf_ble_scan.o(i.nrf_ble_scan_start) refers to memseta.o(.text) for __aeabi_memclr4
    nrf_ble_scan.o(i.nrf_ble_scan_start) refers to nrf_ble_scan.o(i.is_whitelist_used) for is_whitelist_used
    ble_nus_c.o(i.ble_nus_c_handles_assign) refers to nrf_ble_gq.o(i.nrf_ble_gq_conn_handle_register) for nrf_ble_gq_conn_handle_register
    ble_nus_c.o(i.ble_nus_c_init) refers to ble_db_discovery.o(i.ble_db_discovery_evt_register) for ble_db_discovery_evt_register
    ble_nus_c.o(i.ble_nus_c_init) refers to ble_nus_c.o(.constdata) for .constdata
    ble_nus_c.o(i.ble_nus_c_on_db_disc_evt) refers to memseta.o(.text) for __aeabi_memclr4
    ble_nus_c.o(i.ble_nus_c_string_send) refers to memseta.o(.text) for __aeabi_memclr4
    ble_nus_c.o(i.ble_nus_c_string_send) refers to nrf_ble_gq.o(i.nrf_ble_gq_item_add) for nrf_ble_gq_item_add
    ble_nus_c.o(i.ble_nus_c_string_send) refers to ble_nus_c.o(i.gatt_error_handler) for gatt_error_handler
    ble_nus_c.o(i.ble_nus_c_tx_notif_enable) refers to memseta.o(.text) for __aeabi_memclr4
    ble_nus_c.o(i.ble_nus_c_tx_notif_enable) refers to nrf_ble_gq.o(i.nrf_ble_gq_item_add) for nrf_ble_gq_item_add
    ble_nus_c.o(i.ble_nus_c_tx_notif_enable) refers to ble_nus_c.o(i.gatt_error_handler) for gatt_error_handler
    nrf_drv_clock.o(i.clock_clk_started_notify) refers to nrf_drv_clock.o(.bss) for .bss
    nrf_drv_clock.o(i.clock_irq_handler) refers to nrf_drv_clock.o(i.clock_clk_started_notify) for clock_clk_started_notify
    nrf_drv_clock.o(i.clock_irq_handler) refers to nrf_drv_clock.o(.bss) for .bss
    nrf_drv_clock.o(i.nrf_drv_clock_hfclk_is_running) refers to nrf_sdh.o(i.nrf_sdh_is_enabled) for nrf_sdh_is_enabled
    nrf_drv_clock.o(i.nrf_drv_clock_hfclk_release) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    nrf_drv_clock.o(i.nrf_drv_clock_hfclk_release) refers to nrf_sdh.o(i.nrf_sdh_is_enabled) for nrf_sdh_is_enabled
    nrf_drv_clock.o(i.nrf_drv_clock_hfclk_release) refers to nrfx_clock.o(i.nrfx_clock_hfclk_stop) for nrfx_clock_hfclk_stop
    nrf_drv_clock.o(i.nrf_drv_clock_hfclk_release) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    nrf_drv_clock.o(i.nrf_drv_clock_hfclk_release) refers to nrf_drv_clock.o(.bss) for .bss
    nrf_drv_clock.o(i.nrf_drv_clock_hfclk_request) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    nrf_drv_clock.o(i.nrf_drv_clock_hfclk_request) refers to nrf_drv_clock.o(i.item_enqueue) for item_enqueue
    nrf_drv_clock.o(i.nrf_drv_clock_hfclk_request) refers to nrf_sdh.o(i.nrf_sdh_is_enabled) for nrf_sdh_is_enabled
    nrf_drv_clock.o(i.nrf_drv_clock_hfclk_request) refers to nrfx_clock.o(i.nrfx_clock_hfclk_start) for nrfx_clock_hfclk_start
    nrf_drv_clock.o(i.nrf_drv_clock_hfclk_request) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    nrf_drv_clock.o(i.nrf_drv_clock_hfclk_request) refers to nrf_drv_clock.o(.bss) for .bss
    nrf_drv_clock.o(i.nrf_drv_clock_init) refers to nrfx_clock.o(i.nrfx_clock_init) for nrfx_clock_init
    nrf_drv_clock.o(i.nrf_drv_clock_init) refers to nrf_sdh.o(i.nrf_sdh_is_enabled) for nrf_sdh_is_enabled
    nrf_drv_clock.o(i.nrf_drv_clock_init) refers to nrfx_clock.o(i.nrfx_clock_enable) for nrfx_clock_enable
    nrf_drv_clock.o(i.nrf_drv_clock_init) refers to nrf_drv_clock.o(i.nrf_wdt_started) for nrf_wdt_started
    nrf_drv_clock.o(i.nrf_drv_clock_init) refers to nrf_drv_clock.o(.bss) for .bss
    nrf_drv_clock.o(i.nrf_drv_clock_init) refers to nrf_drv_clock.o(i.clock_irq_handler) for clock_irq_handler
    nrf_drv_clock.o(i.nrf_drv_clock_init_check) refers to nrf_drv_clock.o(.bss) for .bss
    nrf_drv_clock.o(i.nrf_drv_clock_lfclk_is_running) refers to nrf_sdh.o(i.nrf_sdh_is_enabled) for nrf_sdh_is_enabled
    nrf_drv_clock.o(i.nrf_drv_clock_lfclk_release) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    nrf_drv_clock.o(i.nrf_drv_clock_lfclk_release) refers to nrf_drv_clock.o(i.nrf_wdt_started) for nrf_wdt_started
    nrf_drv_clock.o(i.nrf_drv_clock_lfclk_release) refers to nrfx_clock.o(i.nrfx_clock_lfclk_stop) for nrfx_clock_lfclk_stop
    nrf_drv_clock.o(i.nrf_drv_clock_lfclk_release) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    nrf_drv_clock.o(i.nrf_drv_clock_lfclk_release) refers to nrf_drv_clock.o(.bss) for .bss
    nrf_drv_clock.o(i.nrf_drv_clock_lfclk_request) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    nrf_drv_clock.o(i.nrf_drv_clock_lfclk_request) refers to nrf_drv_clock.o(i.item_enqueue) for item_enqueue
    nrf_drv_clock.o(i.nrf_drv_clock_lfclk_request) refers to nrfx_clock.o(i.nrfx_clock_lfclk_start) for nrfx_clock_lfclk_start
    nrf_drv_clock.o(i.nrf_drv_clock_lfclk_request) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    nrf_drv_clock.o(i.nrf_drv_clock_lfclk_request) refers to nrf_drv_clock.o(.bss) for .bss
    nrf_drv_clock.o(i.nrf_drv_clock_uninit) refers to nrfx_clock.o(i.nrfx_clock_disable) for nrfx_clock_disable
    nrf_drv_clock.o(i.nrf_drv_clock_uninit) refers to nrfx_clock.o(i.nrfx_clock_uninit) for nrfx_clock_uninit
    nrf_drv_clock.o(i.nrf_drv_clock_uninit) refers to nrf_drv_clock.o(.bss) for .bss
    nrf_drv_clock.o(i.sd_state_evt_handler) refers to nrfx_clock.o(i.nrfx_clock_enable) for nrfx_clock_enable
    nrf_drv_clock.o(i.sd_state_evt_handler) refers to nrf_drv_clock.o(i.nrf_drv_clock_lfclk_release) for nrf_drv_clock_lfclk_release
    nrf_drv_clock.o(i.sd_state_evt_handler) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    nrf_drv_clock.o(i.sd_state_evt_handler) refers to nrf_drv_clock.o(i.nrf_drv_clock_init) for nrf_drv_clock_init
    nrf_drv_clock.o(i.sd_state_evt_handler) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    nrf_drv_clock.o(i.sd_state_evt_handler) refers to nrf_drv_clock.o(.bss) for .bss
    nrf_drv_clock.o(i.soc_evt_handler) refers to nrf_drv_clock.o(i.clock_clk_started_notify) for clock_clk_started_notify
    nrf_drv_clock.o(i.soc_evt_handler) refers to nrf_drv_clock.o(.bss) for .bss
    nrf_drv_clock.o(sdh_soc_observers0) refers to nrf_drv_clock.o(i.soc_evt_handler) for soc_evt_handler
    nrf_drv_clock.o(sdh_state_observers0) refers to nrf_drv_clock.o(i.sd_state_evt_handler) for sd_state_evt_handler
    nrf_drv_uart.o(i.nrf_drv_uart_init) refers to memcpya.o(.text) for __aeabi_memcpy4
    nrf_drv_uart.o(i.nrf_drv_uart_init) refers to nrfx_uarte.o(i.nrfx_uarte_init) for nrfx_uarte_init
    nrf_drv_uart.o(i.nrf_drv_uart_init) refers to nrfx_uart.o(i.nrfx_uart_init) for nrfx_uart_init
    nrf_drv_uart.o(i.nrf_drv_uart_init) refers to nrf_drv_uart.o(.data) for .data
    nrf_drv_uart.o(i.nrf_drv_uart_init) refers to nrf_drv_uart.o(i.uarte_evt_handler) for uarte_evt_handler
    nrf_drv_uart.o(i.nrf_drv_uart_init) refers to nrf_drv_uart.o(i.uart_evt_handler) for uart_evt_handler
    nrf_drv_uart.o(i.uart_evt_handler) refers to nrf_drv_uart.o(.data) for .data
    nrf_drv_uart.o(i.uarte_evt_handler) refers to nrf_drv_uart.o(.data) for .data
    nrfx_atomic.o(i.nrfx_atomic_flag_clear) refers to nrfx_atomic.o(i.nrfx_atomic_u32_and) for nrfx_atomic_u32_and
    nrfx_atomic.o(i.nrfx_atomic_flag_clear_fetch) refers to nrfx_atomic.o(i.nrfx_atomic_u32_fetch_and) for nrfx_atomic_u32_fetch_and
    nrfx_atomic.o(i.nrfx_atomic_flag_set) refers to nrfx_atomic.o(i.nrfx_atomic_u32_or) for nrfx_atomic_u32_or
    nrfx_atomic.o(i.nrfx_atomic_flag_set_fetch) refers to nrfx_atomic.o(i.nrfx_atomic_u32_fetch_or) for nrfx_atomic_u32_fetch_or
    nrfx_atomic.o(i.nrfx_atomic_u32_add) refers to nrfx_atomic.o(.emb_text) for __asm___13_nrfx_atomic_c_3bd32246__nrfx_atomic_internal_add
    nrfx_atomic.o(i.nrfx_atomic_u32_and) refers to nrfx_atomic.o(.emb_text) for __asm___13_nrfx_atomic_c_3bd32246__nrfx_atomic_internal_and
    nrfx_atomic.o(i.nrfx_atomic_u32_cmp_exch) refers to nrfx_atomic.o(.emb_text) for __asm___13_nrfx_atomic_c_3bd32246__nrfx_atomic_internal_cmp_exch
    nrfx_atomic.o(i.nrfx_atomic_u32_fetch_add) refers to nrfx_atomic.o(.emb_text) for __asm___13_nrfx_atomic_c_3bd32246__nrfx_atomic_internal_add
    nrfx_atomic.o(i.nrfx_atomic_u32_fetch_and) refers to nrfx_atomic.o(.emb_text) for __asm___13_nrfx_atomic_c_3bd32246__nrfx_atomic_internal_and
    nrfx_atomic.o(i.nrfx_atomic_u32_fetch_or) refers to nrfx_atomic.o(.emb_text) for __asm___13_nrfx_atomic_c_3bd32246__nrfx_atomic_internal_orr
    nrfx_atomic.o(i.nrfx_atomic_u32_fetch_store) refers to nrfx_atomic.o(.emb_text) for __asm___13_nrfx_atomic_c_3bd32246__nrfx_atomic_internal_mov
    nrfx_atomic.o(i.nrfx_atomic_u32_fetch_sub) refers to nrfx_atomic.o(.emb_text) for __asm___13_nrfx_atomic_c_3bd32246__nrfx_atomic_internal_sub
    nrfx_atomic.o(i.nrfx_atomic_u32_fetch_sub_hs) refers to nrfx_atomic.o(.emb_text) for __asm___13_nrfx_atomic_c_3bd32246__nrfx_atomic_internal_sub_hs
    nrfx_atomic.o(i.nrfx_atomic_u32_fetch_xor) refers to nrfx_atomic.o(.emb_text) for __asm___13_nrfx_atomic_c_3bd32246__nrfx_atomic_internal_eor
    nrfx_atomic.o(i.nrfx_atomic_u32_or) refers to nrfx_atomic.o(.emb_text) for __asm___13_nrfx_atomic_c_3bd32246__nrfx_atomic_internal_orr
    nrfx_atomic.o(i.nrfx_atomic_u32_store) refers to nrfx_atomic.o(.emb_text) for __asm___13_nrfx_atomic_c_3bd32246__nrfx_atomic_internal_mov
    nrfx_atomic.o(i.nrfx_atomic_u32_sub) refers to nrfx_atomic.o(.emb_text) for __asm___13_nrfx_atomic_c_3bd32246__nrfx_atomic_internal_sub
    nrfx_atomic.o(i.nrfx_atomic_u32_sub_hs) refers to nrfx_atomic.o(.emb_text) for __asm___13_nrfx_atomic_c_3bd32246__nrfx_atomic_internal_sub_hs
    nrfx_atomic.o(i.nrfx_atomic_u32_xor) refers to nrfx_atomic.o(.emb_text) for __asm___13_nrfx_atomic_c_3bd32246__nrfx_atomic_internal_eor
    nrfx_clock.o(i.POWER_CLOCK_IRQHandler) refers to nrfx_clock.o(i.nrf_clock_event_check) for nrf_clock_event_check
    nrfx_clock.o(i.POWER_CLOCK_IRQHandler) refers to nrfx_clock.o(i.nrf_clock_event_clear) for nrf_clock_event_clear
    nrfx_clock.o(i.POWER_CLOCK_IRQHandler) refers to nrfx_clock.o(.data) for .data
    nrfx_clock.o(i.nrfx_clock_hfclk_start) refers to nrfx_clock.o(i.nrf_clock_event_clear) for nrf_clock_event_clear
    nrfx_clock.o(i.nrfx_clock_hfclk_stop) refers to nrfx_clock.o(i.nrf_clock_event_clear) for nrf_clock_event_clear
    nrfx_clock.o(i.nrfx_clock_hfclk_stop) refers to nrfx_clock.o(.data) for .data
    nrfx_clock.o(i.nrfx_clock_init) refers to nrfx_clock.o(.data) for .data
    nrfx_clock.o(i.nrfx_clock_lfclk_start) refers to nrfx_clock.o(i.nrf_clock_event_clear) for nrf_clock_event_clear
    nrfx_clock.o(i.nrfx_clock_lfclk_stop) refers to nrfx_clock.o(i.nrf_clock_event_clear) for nrf_clock_event_clear
    nrfx_clock.o(i.nrfx_clock_uninit) refers to nrfx_clock.o(i.nrfx_clock_lfclk_stop) for nrfx_clock_lfclk_stop
    nrfx_clock.o(i.nrfx_clock_uninit) refers to nrfx_clock.o(i.nrfx_clock_hfclk_stop) for nrfx_clock_hfclk_stop
    nrfx_clock.o(i.nrfx_clock_uninit) refers to nrfx_clock.o(.data) for .data
    nrfx_gpiote.o(i.GPIOTE_IRQHandler) refers to nrfx_gpiote.o(i.nrf_gpiote_event_is_set) for nrf_gpiote_event_is_set
    nrfx_gpiote.o(i.GPIOTE_IRQHandler) refers to nrfx_gpiote.o(i.nrf_gpiote_event_clear) for nrf_gpiote_event_clear
    nrfx_gpiote.o(i.GPIOTE_IRQHandler) refers to nrfx_gpiote.o(i.nrf_gpio_latches_read_and_clear) for nrf_gpio_latches_read_and_clear
    nrfx_gpiote.o(i.GPIOTE_IRQHandler) refers to nrfx_gpiote.o(i.port_event_handle) for port_event_handle
    nrfx_gpiote.o(i.GPIOTE_IRQHandler) refers to nrfx_gpiote.o(.bss) for .bss
    nrfx_gpiote.o(i.channel_free) refers to nrfx_gpiote.o(.bss) for .bss
    nrfx_gpiote.o(i.channel_port_alloc) refers to nrfx_gpiote.o(.bss) for .bss
    nrfx_gpiote.o(i.channel_port_get) refers to nrfx_gpiote.o(.bss) for .bss
    nrfx_gpiote.o(i.nrf_gpio_cfg_default) refers to nrfx_gpiote.o(i.nrf_gpio_cfg) for nrf_gpio_cfg
    nrfx_gpiote.o(i.nrf_gpio_latches_read_and_clear) refers to nrfx_gpiote.o(.constdata) for .constdata
    nrfx_gpiote.o(i.nrfx_gpiote_clr_task_addr_get) refers to nrfx_gpiote.o(i.nrfx_gpiote_clr_task_get) for nrfx_gpiote_clr_task_get
    nrfx_gpiote.o(i.nrfx_gpiote_clr_task_get) refers to nrfx_gpiote.o(i.channel_port_get) for channel_port_get
    nrfx_gpiote.o(i.nrfx_gpiote_clr_task_trigger) refers to nrfx_gpiote.o(i.channel_port_get) for channel_port_get
    nrfx_gpiote.o(i.nrfx_gpiote_in_event_addr_get) refers to nrfx_gpiote.o(i.nrfx_gpiote_in_event_get) for nrfx_gpiote_in_event_get
    nrfx_gpiote.o(i.nrfx_gpiote_in_event_disable) refers to nrfx_gpiote.o(i.pin_in_use_by_port) for pin_in_use_by_port
    nrfx_gpiote.o(i.nrfx_gpiote_in_event_disable) refers to nrfx_gpiote.o(i.nrf_gpio_cfg_sense_set) for nrf_gpio_cfg_sense_set
    nrfx_gpiote.o(i.nrfx_gpiote_in_event_disable) refers to nrfx_gpiote.o(i.pin_in_use_by_te) for pin_in_use_by_te
    nrfx_gpiote.o(i.nrfx_gpiote_in_event_disable) refers to nrfx_gpiote.o(i.channel_port_get) for channel_port_get
    nrfx_gpiote.o(i.nrfx_gpiote_in_event_enable) refers to nrfx_gpiote.o(i.pin_in_use_by_port) for pin_in_use_by_port
    nrfx_gpiote.o(i.nrfx_gpiote_in_event_enable) refers to nrfx_gpiote.o(i.channel_port_get) for channel_port_get
    nrfx_gpiote.o(i.nrfx_gpiote_in_event_enable) refers to nrfx_gpiote.o(i.port_handler_polarity_get) for port_handler_polarity_get
    nrfx_gpiote.o(i.nrfx_gpiote_in_event_enable) refers to nrfx_gpiote.o(i.nrf_gpio_cfg_sense_set) for nrf_gpio_cfg_sense_set
    nrfx_gpiote.o(i.nrfx_gpiote_in_event_enable) refers to nrfx_gpiote.o(i.pin_in_use_by_te) for pin_in_use_by_te
    nrfx_gpiote.o(i.nrfx_gpiote_in_event_enable) refers to nrfx_gpiote.o(i.nrf_gpiote_event_clear) for nrf_gpiote_event_clear
    nrfx_gpiote.o(i.nrfx_gpiote_in_event_enable) refers to nrfx_gpiote.o(.bss) for .bss
    nrfx_gpiote.o(i.nrfx_gpiote_in_event_get) refers to nrfx_gpiote.o(i.pin_in_use_by_te) for pin_in_use_by_te
    nrfx_gpiote.o(i.nrfx_gpiote_in_event_get) refers to nrfx_gpiote.o(i.channel_port_get) for channel_port_get
    nrfx_gpiote.o(i.nrfx_gpiote_in_init) refers to nrfx_gpiote.o(i.channel_port_alloc) for channel_port_alloc
    nrfx_gpiote.o(i.nrfx_gpiote_in_init) refers to nrfx_gpiote.o(i.nrf_gpio_cfg) for nrf_gpio_cfg
    nrfx_gpiote.o(i.nrfx_gpiote_in_init) refers to nrfx_gpiote.o(i.pin_configured_set) for pin_configured_set
    nrfx_gpiote.o(i.nrfx_gpiote_in_init) refers to nrfx_gpiote.o(.bss) for .bss
    nrfx_gpiote.o(i.nrfx_gpiote_in_uninit) refers to nrfx_gpiote.o(i.nrfx_gpiote_in_event_disable) for nrfx_gpiote_in_event_disable
    nrfx_gpiote.o(i.nrfx_gpiote_in_uninit) refers to nrfx_gpiote.o(i.pin_in_use_by_te) for pin_in_use_by_te
    nrfx_gpiote.o(i.nrfx_gpiote_in_uninit) refers to nrfx_gpiote.o(i.channel_port_get) for channel_port_get
    nrfx_gpiote.o(i.nrfx_gpiote_in_uninit) refers to nrfx_gpiote.o(i.pin_configured_check) for pin_configured_check
    nrfx_gpiote.o(i.nrfx_gpiote_in_uninit) refers to nrfx_gpiote.o(i.nrf_gpio_cfg_default) for nrf_gpio_cfg_default
    nrfx_gpiote.o(i.nrfx_gpiote_in_uninit) refers to nrfx_gpiote.o(i.pin_configured_clear) for pin_configured_clear
    nrfx_gpiote.o(i.nrfx_gpiote_in_uninit) refers to nrfx_gpiote.o(i.channel_free) for channel_free
    nrfx_gpiote.o(i.nrfx_gpiote_in_uninit) refers to nrfx_gpiote.o(.bss) for .bss
    nrfx_gpiote.o(i.nrfx_gpiote_init) refers to nrfx_gpiote.o(i.nrf_gpio_pin_present_check) for nrf_gpio_pin_present_check
    nrfx_gpiote.o(i.nrfx_gpiote_init) refers to nrfx_gpiote.o(i.channel_free) for channel_free
    nrfx_gpiote.o(i.nrfx_gpiote_init) refers to nrfx_gpiote.o(i.nrf_gpiote_event_clear) for nrf_gpiote_event_clear
    nrfx_gpiote.o(i.nrfx_gpiote_init) refers to nrfx_gpiote.o(.bss) for .bss
    nrfx_gpiote.o(i.nrfx_gpiote_is_init) refers to nrfx_gpiote.o(.bss) for .bss
    nrfx_gpiote.o(i.nrfx_gpiote_out_init) refers to nrfx_gpiote.o(i.channel_port_alloc) for channel_port_alloc
    nrfx_gpiote.o(i.nrfx_gpiote_out_init) refers to nrfx_gpiote.o(i.nrf_gpio_cfg) for nrf_gpio_cfg
    nrfx_gpiote.o(i.nrfx_gpiote_out_init) refers to nrfx_gpiote.o(i.pin_configured_set) for pin_configured_set
    nrfx_gpiote.o(i.nrfx_gpiote_out_init) refers to nrfx_gpiote.o(.bss) for .bss
    nrfx_gpiote.o(i.nrfx_gpiote_out_task_addr_get) refers to nrfx_gpiote.o(i.nrfx_gpiote_out_task_get) for nrfx_gpiote_out_task_get
    nrfx_gpiote.o(i.nrfx_gpiote_out_task_disable) refers to nrfx_gpiote.o(.bss) for .bss
    nrfx_gpiote.o(i.nrfx_gpiote_out_task_enable) refers to nrfx_gpiote.o(.bss) for .bss
    nrfx_gpiote.o(i.nrfx_gpiote_out_task_force) refers to nrfx_gpiote.o(.bss) for .bss
    nrfx_gpiote.o(i.nrfx_gpiote_out_task_get) refers to nrfx_gpiote.o(i.channel_port_get) for channel_port_get
    nrfx_gpiote.o(i.nrfx_gpiote_out_task_trigger) refers to nrfx_gpiote.o(i.channel_port_get) for channel_port_get
    nrfx_gpiote.o(i.nrfx_gpiote_out_uninit) refers to nrfx_gpiote.o(i.pin_in_use_by_te) for pin_in_use_by_te
    nrfx_gpiote.o(i.nrfx_gpiote_out_uninit) refers to nrfx_gpiote.o(i.channel_port_get) for channel_port_get
    nrfx_gpiote.o(i.nrfx_gpiote_out_uninit) refers to nrfx_gpiote.o(i.channel_free) for channel_free
    nrfx_gpiote.o(i.nrfx_gpiote_out_uninit) refers to nrfx_gpiote.o(i.pin_configured_check) for pin_configured_check
    nrfx_gpiote.o(i.nrfx_gpiote_out_uninit) refers to nrfx_gpiote.o(i.nrf_gpio_cfg_default) for nrf_gpio_cfg_default
    nrfx_gpiote.o(i.nrfx_gpiote_out_uninit) refers to nrfx_gpiote.o(i.pin_configured_clear) for pin_configured_clear
    nrfx_gpiote.o(i.nrfx_gpiote_out_uninit) refers to nrfx_gpiote.o(.bss) for .bss
    nrfx_gpiote.o(i.nrfx_gpiote_set_task_addr_get) refers to nrfx_gpiote.o(i.nrfx_gpiote_set_task_get) for nrfx_gpiote_set_task_get
    nrfx_gpiote.o(i.nrfx_gpiote_set_task_get) refers to nrfx_gpiote.o(i.channel_port_get) for channel_port_get
    nrfx_gpiote.o(i.nrfx_gpiote_set_task_trigger) refers to nrfx_gpiote.o(i.channel_port_get) for channel_port_get
    nrfx_gpiote.o(i.nrfx_gpiote_uninit) refers to nrfx_gpiote.o(i.nrf_gpio_pin_present_check) for nrf_gpio_pin_present_check
    nrfx_gpiote.o(i.nrfx_gpiote_uninit) refers to nrfx_gpiote.o(i.nrfx_gpiote_in_uninit) for nrfx_gpiote_in_uninit
    nrfx_gpiote.o(i.nrfx_gpiote_uninit) refers to nrfx_gpiote.o(i.nrfx_gpiote_out_uninit) for nrfx_gpiote_out_uninit
    nrfx_gpiote.o(i.nrfx_gpiote_uninit) refers to nrfx_gpiote.o(.bss) for .bss
    nrfx_gpiote.o(i.pin_configured_check) refers to nrfx_gpiote.o(i.nrf_bitmask_bit_is_set) for nrf_bitmask_bit_is_set
    nrfx_gpiote.o(i.pin_configured_check) refers to nrfx_gpiote.o(.bss) for .bss
    nrfx_gpiote.o(i.pin_configured_clear) refers to nrfx_gpiote.o(.bss) for .bss
    nrfx_gpiote.o(i.pin_configured_set) refers to nrfx_gpiote.o(.bss) for .bss
    nrfx_gpiote.o(i.pin_in_use_by_port) refers to nrfx_gpiote.o(.bss) for .bss
    nrfx_gpiote.o(i.pin_in_use_by_te) refers to nrfx_gpiote.o(.bss) for .bss
    nrfx_gpiote.o(i.port_event_handle) refers to nrfx_gpiote.o(i.nrf_bitmask_bit_is_set) for nrf_bitmask_bit_is_set
    nrfx_gpiote.o(i.port_event_handle) refers to nrfx_gpiote.o(i.port_handler_polarity_get) for port_handler_polarity_get
    nrfx_gpiote.o(i.port_event_handle) refers to nrfx_gpiote.o(i.nrf_gpio_cfg_sense_set) for nrf_gpio_cfg_sense_set
    nrfx_gpiote.o(i.port_event_handle) refers to nrfx_gpiote.o(i.channel_port_get) for channel_port_get
    nrfx_gpiote.o(i.port_event_handle) refers to nrfx_gpiote.o(i.nrf_gpio_latches_read_and_clear) for nrf_gpio_latches_read_and_clear
    nrfx_gpiote.o(i.port_event_handle) refers to nrfx_gpiote.o(.bss) for .bss
    nrfx_gpiote.o(i.port_handler_polarity_get) refers to nrfx_gpiote.o(.bss) for .bss
    nrfx_prs.o(i.UARTE0_UART0_IRQHandler) refers to nrfx_prs.o(.data) for .data
    nrfx_prs.o(i.nrfx_prs_acquire) refers to nrfx_prs.o(i.prs_box_get) for prs_box_get
    nrfx_prs.o(i.nrfx_prs_acquire) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    nrfx_prs.o(i.nrfx_prs_acquire) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    nrfx_prs.o(i.nrfx_prs_release) refers to nrfx_prs.o(i.prs_box_get) for prs_box_get
    nrfx_prs.o(i.prs_box_get) refers to nrfx_prs.o(.data) for .data
    nrfx_uart.o(i.apply_config) refers to nrfx_uart.o(i.nrf_gpio_cfg_output) for nrf_gpio_cfg_output
    nrfx_uart.o(i.apply_config) refers to nrfx_uart.o(i.nrf_gpio_cfg_input) for nrf_gpio_cfg_input
    nrfx_uart.o(i.nrf_gpio_cfg_default) refers to nrfx_uart.o(i.nrf_gpio_cfg) for nrf_gpio_cfg
    nrfx_uart.o(i.nrf_gpio_cfg_input) refers to nrfx_uart.o(i.nrf_gpio_cfg) for nrf_gpio_cfg
    nrfx_uart.o(i.nrf_gpio_cfg_output) refers to nrfx_uart.o(i.nrf_gpio_cfg) for nrf_gpio_cfg
    nrfx_uart.o(i.nrfx_uart_0_irq_handler) refers to nrfx_uart.o(i.uart_irq_handler) for uart_irq_handler
    nrfx_uart.o(i.nrfx_uart_0_irq_handler) refers to nrfx_uart.o(.bss) for .bss
    nrfx_uart.o(i.nrfx_uart_errorsrc_get) refers to nrfx_uart.o(i.nrf_uart_event_clear) for nrf_uart_event_clear
    nrfx_uart.o(i.nrfx_uart_init) refers to nrfx_prs.o(i.nrfx_prs_acquire) for nrfx_prs_acquire
    nrfx_uart.o(i.nrfx_uart_init) refers to nrfx_uart.o(i.apply_config) for apply_config
    nrfx_uart.o(i.nrfx_uart_init) refers to nrfx_uart.o(i.nrf_uart_event_clear) for nrf_uart_event_clear
    nrfx_uart.o(i.nrfx_uart_init) refers to nrfx_uart.o(.bss) for .bss
    nrfx_uart.o(i.nrfx_uart_init) refers to nrfx_uart.o(.constdata) for .constdata
    nrfx_uart.o(i.nrfx_uart_rx) refers to nrfx_uart.o(i.rx_enable) for rx_enable
    nrfx_uart.o(i.nrfx_uart_rx) refers to nrfx_uart.o(i.nrf_uart_event_clear) for nrf_uart_event_clear
    nrfx_uart.o(i.nrfx_uart_rx) refers to nrfx_uart.o(i.nrf_uart_event_check) for nrf_uart_event_check
    nrfx_uart.o(i.nrfx_uart_rx) refers to nrfx_uart.o(i.rx_byte) for rx_byte
    nrfx_uart.o(i.nrfx_uart_rx) refers to nrfx_uart.o(.bss) for .bss
    nrfx_uart.o(i.nrfx_uart_rx_disable) refers to nrfx_uart.o(.bss) for .bss
    nrfx_uart.o(i.nrfx_uart_rx_enable) refers to nrfx_uart.o(i.rx_enable) for rx_enable
    nrfx_uart.o(i.nrfx_uart_rx_enable) refers to nrfx_uart.o(.bss) for .bss
    nrfx_uart.o(i.nrfx_uart_tx) refers to nrfx_uart.o(i.nrfx_uart_tx_in_progress) for nrfx_uart_tx_in_progress
    nrfx_uart.o(i.nrfx_uart_tx) refers to nrfx_uart.o(i.nrf_uart_event_clear) for nrf_uart_event_clear
    nrfx_uart.o(i.nrfx_uart_tx) refers to nrfx_uart.o(i.tx_byte) for tx_byte
    nrfx_uart.o(i.nrfx_uart_tx) refers to nrfx_uart.o(i.nrf_uart_event_check) for nrf_uart_event_check
    nrfx_uart.o(i.nrfx_uart_tx) refers to nrfx_uart.o(.bss) for .bss
    nrfx_uart.o(i.nrfx_uart_tx_abort) refers to nrfx_uart.o(i.tx_done_event) for tx_done_event
    nrfx_uart.o(i.nrfx_uart_tx_abort) refers to nrfx_uart.o(.bss) for .bss
    nrfx_uart.o(i.nrfx_uart_tx_in_progress) refers to nrfx_uart.o(.bss) for .bss
    nrfx_uart.o(i.nrfx_uart_uninit) refers to nrfx_uart.o(i.nrf_gpio_cfg_default) for nrf_gpio_cfg_default
    nrfx_uart.o(i.nrfx_uart_uninit) refers to nrfx_prs.o(i.nrfx_prs_release) for nrfx_prs_release
    nrfx_uart.o(i.nrfx_uart_uninit) refers to nrfx_uart.o(.bss) for .bss
    nrfx_uart.o(i.rx_byte) refers to nrfx_uart.o(i.nrf_uart_event_clear) for nrf_uart_event_clear
    nrfx_uart.o(i.rx_enable) refers to nrfx_uart.o(i.nrf_uart_event_clear) for nrf_uart_event_clear
    nrfx_uart.o(i.tx_byte) refers to nrfx_uart.o(i.nrf_uart_event_clear) for nrf_uart_event_clear
    nrfx_uart.o(i.uart_irq_handler) refers to nrfx_uart.o(i.nrf_uart_int_enable_check) for nrf_uart_int_enable_check
    nrfx_uart.o(i.uart_irq_handler) refers to nrfx_uart.o(i.nrf_uart_event_check) for nrf_uart_event_check
    nrfx_uart.o(i.uart_irq_handler) refers to nrfx_uart.o(i.nrf_uart_event_clear) for nrf_uart_event_clear
    nrfx_uart.o(i.uart_irq_handler) refers to nrfx_uart.o(i.rx_byte) for rx_byte
    nrfx_uart.o(i.uart_irq_handler) refers to nrfx_uart.o(i.rx_done_event) for rx_done_event
    nrfx_uart.o(i.uart_irq_handler) refers to nrfx_uart.o(i.tx_done_event) for tx_done_event
    nrfx_uart.o(i.uart_irq_handler) refers to nrfx_uart.o(i.tx_byte) for tx_byte
    nrfx_uart.o(.constdata) refers to nrfx_uart.o(i.nrfx_uart_0_irq_handler) for nrfx_uart_0_irq_handler
    nrfx_uarte.o(i.apply_config) refers to nrfx_uarte.o(i.nrf_gpio_cfg_output) for nrf_gpio_cfg_output
    nrfx_uarte.o(i.apply_config) refers to nrfx_uarte.o(i.nrf_gpio_cfg_input) for nrf_gpio_cfg_input
    nrfx_uarte.o(i.interrupts_enable) refers to nrfx_uarte.o(i.nrf_uarte_event_clear) for nrf_uarte_event_clear
    nrfx_uarte.o(i.nrf_gpio_cfg_default) refers to nrfx_uarte.o(i.nrf_gpio_cfg) for nrf_gpio_cfg
    nrfx_uarte.o(i.nrf_gpio_cfg_input) refers to nrfx_uarte.o(i.nrf_gpio_cfg) for nrf_gpio_cfg
    nrfx_uarte.o(i.nrf_gpio_cfg_output) refers to nrfx_uarte.o(i.nrf_gpio_cfg) for nrf_gpio_cfg
    nrfx_uarte.o(i.nrfx_uarte_0_irq_handler) refers to nrfx_uarte.o(i.uarte_irq_handler) for uarte_irq_handler
    nrfx_uarte.o(i.nrfx_uarte_0_irq_handler) refers to nrfx_uarte.o(.bss) for .bss
    nrfx_uarte.o(i.nrfx_uarte_errorsrc_get) refers to nrfx_uarte.o(i.nrf_uarte_event_clear) for nrf_uarte_event_clear
    nrfx_uarte.o(i.nrfx_uarte_init) refers to nrfx_prs.o(i.nrfx_prs_acquire) for nrfx_prs_acquire
    nrfx_uarte.o(i.nrfx_uarte_init) refers to nrfx_uarte.o(i.apply_config) for apply_config
    nrfx_uarte.o(i.nrfx_uarte_init) refers to nrfx_uarte.o(i.interrupts_enable) for interrupts_enable
    nrfx_uarte.o(i.nrfx_uarte_init) refers to nrfx_uarte.o(.bss) for .bss
    nrfx_uarte.o(i.nrfx_uarte_init) refers to nrfx_uarte.o(.constdata) for .constdata
    nrfx_uarte.o(i.nrfx_uarte_rx) refers to nrfx_uarte.o(i.nrfx_is_in_ram) for nrfx_is_in_ram
    nrfx_uarte.o(i.nrfx_uarte_rx) refers to nrfx_uarte.o(i.nrf_uarte_event_clear) for nrf_uarte_event_clear
    nrfx_uarte.o(i.nrfx_uarte_rx) refers to nrfx_uarte.o(i.nrf_uarte_event_check) for nrf_uarte_event_check
    nrfx_uarte.o(i.nrfx_uarte_rx) refers to nrfx_uarte.o(.bss) for .bss
    nrfx_uarte.o(i.nrfx_uarte_rx_abort) refers to nrfx_uarte.o(.bss) for .bss
    nrfx_uarte.o(i.nrfx_uarte_tx) refers to nrfx_uarte.o(i.nrfx_is_in_ram) for nrfx_is_in_ram
    nrfx_uarte.o(i.nrfx_uarte_tx) refers to nrfx_uarte.o(i.nrfx_uarte_tx_in_progress) for nrfx_uarte_tx_in_progress
    nrfx_uarte.o(i.nrfx_uarte_tx) refers to nrfx_uarte.o(i.nrf_uarte_event_clear) for nrf_uarte_event_clear
    nrfx_uarte.o(i.nrfx_uarte_tx) refers to nrfx_uarte.o(i.nrf_uarte_event_check) for nrf_uarte_event_check
    nrfx_uarte.o(i.nrfx_uarte_tx) refers to nrfx_uarte.o(.bss) for .bss
    nrfx_uarte.o(i.nrfx_uarte_tx_abort) refers to nrfx_uarte.o(i.nrf_uarte_event_clear) for nrf_uarte_event_clear
    nrfx_uarte.o(i.nrfx_uarte_tx_abort) refers to nrfx_uarte.o(i.nrf_uarte_event_check) for nrf_uarte_event_check
    nrfx_uarte.o(i.nrfx_uarte_tx_abort) refers to nrfx_uarte.o(.bss) for .bss
    nrfx_uarte.o(i.nrfx_uarte_tx_in_progress) refers to nrfx_uarte.o(.bss) for .bss
    nrfx_uarte.o(i.nrfx_uarte_uninit) refers to nrfx_uarte.o(i.nrf_uarte_event_clear) for nrf_uarte_event_clear
    nrfx_uarte.o(i.nrfx_uarte_uninit) refers to nrfx_uarte.o(i.nrf_uarte_event_check) for nrf_uarte_event_check
    nrfx_uarte.o(i.nrfx_uarte_uninit) refers to nrfx_uarte.o(i.nrf_gpio_cfg_default) for nrf_gpio_cfg_default
    nrfx_uarte.o(i.nrfx_uarte_uninit) refers to nrfx_prs.o(i.nrfx_prs_release) for nrfx_prs_release
    nrfx_uarte.o(i.nrfx_uarte_uninit) refers to nrfx_uarte.o(.bss) for .bss
    nrfx_uarte.o(i.uarte_irq_handler) refers to nrfx_uarte.o(i.nrf_uarte_event_check) for nrf_uarte_event_check
    nrfx_uarte.o(i.uarte_irq_handler) refers to nrfx_uarte.o(i.nrf_uarte_event_clear) for nrf_uarte_event_clear
    nrfx_uarte.o(i.uarte_irq_handler) refers to nrfx_uarte.o(i.rx_done_event) for rx_done_event
    nrfx_uarte.o(i.uarte_irq_handler) refers to nrfx_uarte.o(i.tx_done_event) for tx_done_event
    nrfx_uarte.o(.constdata) refers to nrfx_uarte.o(i.nrfx_uarte_0_irq_handler) for nrfx_uarte_0_irq_handler
    app_button.o(i.app_button_disable) refers to nrfx_gpiote.o(i.nrfx_gpiote_in_event_disable) for nrfx_gpiote_in_event_disable
    app_button.o(i.app_button_disable) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    app_button.o(i.app_button_disable) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    app_button.o(i.app_button_disable) refers to app_timer2.o(i.app_timer_stop) for app_timer_stop
    app_button.o(i.app_button_disable) refers to app_button.o(.data) for .data
    app_button.o(i.app_button_disable) refers to app_button.o(.constdata) for .constdata
    app_button.o(i.app_button_enable) refers to nrfx_gpiote.o(i.nrfx_gpiote_in_event_enable) for nrfx_gpiote_in_event_enable
    app_button.o(i.app_button_enable) refers to app_button.o(.data) for .data
    app_button.o(i.app_button_init) refers to nrfx_gpiote.o(i.nrfx_gpiote_is_init) for nrfx_gpiote_is_init
    app_button.o(i.app_button_init) refers to nrfx_gpiote.o(i.nrfx_gpiote_init) for nrfx_gpiote_init
    app_button.o(i.app_button_init) refers to memseta.o(.text) for __aeabi_memclr
    app_button.o(i.app_button_init) refers to nrfx_gpiote.o(i.nrfx_gpiote_in_init) for nrfx_gpiote_in_init
    app_button.o(i.app_button_init) refers to app_timer2.o(i.app_timer_create) for app_timer_create
    app_button.o(i.app_button_init) refers to app_button.o(.data) for .data
    app_button.o(i.app_button_init) refers to app_button.o(.bss) for .bss
    app_button.o(i.app_button_init) refers to app_button.o(.constdata) for .constdata
    app_button.o(i.app_button_init) refers to app_button.o(i.gpiote_event_handler) for gpiote_event_handler
    app_button.o(i.app_button_init) refers to app_button.o(i.detection_delay_timeout_handler) for detection_delay_timeout_handler
    app_button.o(i.app_button_is_pushed) refers to nrfx_gpiote.o(i.nrfx_gpiote_in_is_set) for nrfx_gpiote_in_is_set
    app_button.o(i.app_button_is_pushed) refers to app_button.o(.data) for .data
    app_button.o(i.button_get) refers to app_button.o(.data) for .data
    app_button.o(i.detection_delay_timeout_handler) refers to nrfx_gpiote.o(i.nrfx_gpiote_in_is_set) for nrfx_gpiote_in_is_set
    app_button.o(i.detection_delay_timeout_handler) refers to app_button.o(i.evt_handle) for evt_handle
    app_button.o(i.detection_delay_timeout_handler) refers to app_button.o(i.timer_start) for timer_start
    app_button.o(i.detection_delay_timeout_handler) refers to app_button.o(.data) for .data
    app_button.o(i.evt_handle) refers to llshl.o(.text) for __aeabi_llsl
    app_button.o(i.evt_handle) refers to app_button.o(i.state_set) for state_set
    app_button.o(i.evt_handle) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    app_button.o(i.evt_handle) refers to app_button.o(i.usr_event) for usr_event
    app_button.o(i.evt_handle) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    app_button.o(i.evt_handle) refers to app_button.o(.bss) for .bss
    app_button.o(i.evt_handle) refers to app_button.o(.data) for .data
    app_button.o(i.gpiote_event_handler) refers to app_button.o(i.button_get) for button_get
    app_button.o(i.gpiote_event_handler) refers to nrfx_gpiote.o(i.nrfx_gpiote_in_is_set) for nrfx_gpiote_in_is_set
    app_button.o(i.gpiote_event_handler) refers to app_button.o(i.timer_start) for timer_start
    app_button.o(i.gpiote_event_handler) refers to app_button.o(.data) for .data
    app_button.o(i.state_set) refers to app_button.o(.bss) for .bss
    app_button.o(i.timer_start) refers to app_timer2.o(i.app_timer_start) for app_timer_start
    app_button.o(i.timer_start) refers to app_button.o(.data) for .data
    app_button.o(i.timer_start) refers to app_button.o(.constdata) for .constdata
    app_button.o(i.usr_event) refers to app_button.o(i.button_get) for button_get
    app_button.o(.constdata) refers to app_button.o(.bss) for m_detection_delay_timer_id_data
    app_error.o(i.app_error_handler_bare) refers to app_error_weak.o(i.app_error_fault_handler) for app_error_fault_handler
    app_error.o(i.app_error_save_and_stop) refers to app_error.o(.bss) for .bss
    app_error_handler_keil.o(.emb_text) refers to app_error_weak.o(i.app_error_fault_handler) for app_error_fault_handler
    app_fifo.o(i.app_fifo_get) refers to app_fifo.o(i.fifo_get) for fifo_get
    app_fifo.o(i.app_fifo_put) refers to app_fifo.o(i.fifo_put) for fifo_put
    app_fifo.o(i.app_fifo_read) refers to app_fifo.o(i.fifo_get) for fifo_get
    app_fifo.o(i.app_fifo_write) refers to app_fifo.o(i.fifo_put) for fifo_put
    app_scheduler.o(i.app_sched_event_put) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    app_scheduler.o(i.app_sched_event_put) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    app_scheduler.o(i.app_sched_event_put) refers to memcpya.o(.text) for __aeabi_memcpy
    app_scheduler.o(i.app_sched_event_put) refers to app_scheduler.o(.data) for .data
    app_scheduler.o(i.app_sched_execute) refers to app_scheduler.o(.data) for .data
    app_scheduler.o(i.app_sched_init) refers to app_scheduler.o(.data) for .data
    app_scheduler.o(i.app_sched_queue_space_get) refers to app_scheduler.o(.data) for .data
    app_timer2.o(i.app_timer_cnt_get) refers to drv_rtc.o(i.drv_rtc_counter_get) for drv_rtc_counter_get
    app_timer2.o(i.app_timer_cnt_get) refers to app_timer2.o(.data) for .data
    app_timer2.o(i.app_timer_init) refers to nrf_atfifo.o(i.nrf_atfifo_init) for nrf_atfifo_init
    app_timer2.o(i.app_timer_init) refers to drv_rtc.o(i.drv_rtc_init) for drv_rtc_init
    app_timer2.o(i.app_timer_init) refers to drv_rtc.o(i.drv_rtc_overflow_enable) for drv_rtc_overflow_enable
    app_timer2.o(i.app_timer_init) refers to drv_rtc.o(i.drv_rtc_compare_set) for drv_rtc_compare_set
    app_timer2.o(i.app_timer_init) refers to app_timer2.o(.constdata) for .constdata
    app_timer2.o(i.app_timer_init) refers to app_timer2.o(.bss) for .bss
    app_timer2.o(i.app_timer_init) refers to app_timer2.o(i.rtc_irq) for rtc_irq
    app_timer2.o(i.app_timer_init) refers to app_timer2.o(.data) for .data
    app_timer2.o(i.app_timer_pause) refers to drv_rtc.o(i.drv_rtc_stop) for drv_rtc_stop
    app_timer2.o(i.app_timer_pause) refers to app_timer2.o(.data) for .data
    app_timer2.o(i.app_timer_resume) refers to drv_rtc.o(i.drv_rtc_start) for drv_rtc_start
    app_timer2.o(i.app_timer_resume) refers to app_timer2.o(.data) for .data
    app_timer2.o(i.app_timer_start) refers to app_timer2.o(i.get_now) for get_now
    app_timer2.o(i.app_timer_start) refers to app_timer2.o(i.timer_req_schedule) for timer_req_schedule
    app_timer2.o(i.app_timer_stop) refers to app_timer2.o(i.timer_req_schedule) for timer_req_schedule
    app_timer2.o(i.app_timer_stop_all) refers to app_timer2.o(i.timer_req_schedule) for timer_req_schedule
    app_timer2.o(i.app_timer_stop_all) refers to app_timer2.o(.data) for .data
    app_timer2.o(i.get_now) refers to drv_rtc.o(i.drv_rtc_counter_get) for drv_rtc_counter_get
    app_timer2.o(i.get_now) refers to app_timer2.o(.data) for .data
    app_timer2.o(i.rtc_irq) refers to drv_rtc.o(i.drv_rtc_overflow_pending) for drv_rtc_overflow_pending
    app_timer2.o(i.rtc_irq) refers to drv_rtc.o(i.drv_rtc_compare_pending) for drv_rtc_compare_pending
    app_timer2.o(i.rtc_irq) refers to app_timer2.o(i.timer_expire) for timer_expire
    app_timer2.o(i.rtc_irq) refers to app_timer2.o(i.get_now) for get_now
    app_timer2.o(i.rtc_irq) refers to app_timer2.o(i.timer_req_process) for timer_req_process
    app_timer2.o(i.rtc_irq) refers to app_timer2.o(i.rtc_update) for rtc_update
    app_timer2.o(i.rtc_irq) refers to app_timer2.o(.data) for .data
    app_timer2.o(i.rtc_schedule) refers to app_timer2.o(i.get_now) for get_now
    app_timer2.o(i.rtc_schedule) refers to app_timer2.o(i.app_timer_cnt_get) for app_timer_cnt_get
    app_timer2.o(i.rtc_schedule) refers to drv_rtc.o(i.drv_rtc_windowed_compare_set) for drv_rtc_windowed_compare_set
    app_timer2.o(i.rtc_schedule) refers to drv_rtc.o(i.drv_rtc_compare_disable) for drv_rtc_compare_disable
    app_timer2.o(i.rtc_schedule) refers to app_timer2.o(i.timer_expire) for timer_expire
    app_timer2.o(i.rtc_schedule) refers to app_timer2.o(.data) for .data
    app_timer2.o(i.rtc_update) refers to nrf_sortlist.o(i.nrf_sortlist_peek) for nrf_sortlist_peek
    app_timer2.o(i.rtc_update) refers to nrf_sortlist.o(i.nrf_sortlist_add) for nrf_sortlist_add
    app_timer2.o(i.rtc_update) refers to app_timer2.o(i.sortlist_pop) for sortlist_pop
    app_timer2.o(i.rtc_update) refers to app_timer2.o(i.rtc_schedule) for rtc_schedule
    app_timer2.o(i.rtc_update) refers to drv_rtc.o(i.drv_rtc_stop) for drv_rtc_stop
    app_timer2.o(i.rtc_update) refers to drv_rtc.o(i.drv_rtc_start) for drv_rtc_start
    app_timer2.o(i.rtc_update) refers to app_timer2.o(.data) for .data
    app_timer2.o(i.rtc_update) refers to app_timer2.o(.constdata) for .constdata
    app_timer2.o(i.sortlist_pop) refers to nrf_sortlist.o(i.nrf_sortlist_pop) for nrf_sortlist_pop
    app_timer2.o(i.sortlist_pop) refers to app_timer2.o(.constdata) for .constdata
    app_timer2.o(i.timer_expire) refers to app_timer2.o(i.get_now) for get_now
    app_timer2.o(i.timer_expire) refers to nrf_sortlist.o(i.nrf_sortlist_add) for nrf_sortlist_add
    app_timer2.o(i.timer_expire) refers to app_timer2.o(.data) for .data
    app_timer2.o(i.timer_expire) refers to app_timer2.o(.constdata) for .constdata
    app_timer2.o(i.timer_req_process) refers to nrf_atfifo.o(i.nrf_atfifo_item_get) for nrf_atfifo_item_get
    app_timer2.o(i.timer_req_process) refers to nrf_sortlist.o(i.nrf_sortlist_add) for nrf_sortlist_add
    app_timer2.o(i.timer_req_process) refers to nrf_sortlist.o(i.nrf_sortlist_remove) for nrf_sortlist_remove
    app_timer2.o(i.timer_req_process) refers to app_timer2.o(i.sortlist_pop) for sortlist_pop
    app_timer2.o(i.timer_req_process) refers to nrf_atfifo.o(i.nrf_atfifo_item_free) for nrf_atfifo_item_free
    app_timer2.o(i.timer_req_process) refers to app_timer2.o(.constdata) for .constdata
    app_timer2.o(i.timer_req_process) refers to app_timer2.o(.data) for .data
    app_timer2.o(i.timer_req_schedule) refers to nrf_atfifo.o(i.nrf_atfifo_item_alloc) for nrf_atfifo_item_alloc
    app_timer2.o(i.timer_req_schedule) refers to nrf_atfifo.o(i.nrf_atfifo_item_put) for nrf_atfifo_item_put
    app_timer2.o(i.timer_req_schedule) refers to drv_rtc.o(i.drv_rtc_irq_trigger) for drv_rtc_irq_trigger
    app_timer2.o(i.timer_req_schedule) refers to app_timer2.o(.constdata) for .constdata
    app_timer2.o(i.timer_req_schedule) refers to app_timer2.o(.data) for .data
    app_timer2.o(.constdata) refers to app_timer2.o(.bss) for m_req_fifo_inst
    app_timer2.o(.constdata) refers to app_timer2.o(.data) for m_app_timer_sortlist_sortlist_cb
    app_timer2.o(.constdata) refers to app_timer2.o(i.compare_func) for compare_func
    app_uart_fifo.o(i.app_uart_close) refers to nrfx_uarte.o(i.nrfx_uarte_uninit) for nrfx_uarte_uninit
    app_uart_fifo.o(i.app_uart_close) refers to nrfx_uart.o(i.nrfx_uart_uninit) for nrfx_uart_uninit
    app_uart_fifo.o(i.app_uart_close) refers to app_uart_fifo.o(.data) for .data
    app_uart_fifo.o(i.app_uart_close) refers to nrf_drv_uart.o(.data) for nrf_drv_uart_use_easy_dma
    app_uart_fifo.o(i.app_uart_flush) refers to app_fifo.o(i.app_fifo_flush) for app_fifo_flush
    app_uart_fifo.o(i.app_uart_flush) refers to app_uart_fifo.o(.bss) for .bss
    app_uart_fifo.o(i.app_uart_get) refers to app_fifo.o(i.app_fifo_get) for app_fifo_get
    app_uart_fifo.o(i.app_uart_get) refers to app_uart_fifo.o(i.nrf_drv_uart_rx) for nrf_drv_uart_rx
    app_uart_fifo.o(i.app_uart_get) refers to app_error.o(i.app_error_handler_bare) for app_error_handler_bare
    app_uart_fifo.o(i.app_uart_get) refers to app_uart_fifo.o(.data) for .data
    app_uart_fifo.o(i.app_uart_get) refers to app_uart_fifo.o(.bss) for .bss
    app_uart_fifo.o(i.app_uart_init) refers to app_fifo.o(i.app_fifo_init) for app_fifo_init
    app_uart_fifo.o(i.app_uart_init) refers to memcpya.o(.text) for __aeabi_memcpy4
    app_uart_fifo.o(i.app_uart_init) refers to nrf_drv_uart.o(i.nrf_drv_uart_init) for nrf_drv_uart_init
    app_uart_fifo.o(i.app_uart_init) refers to app_uart_fifo.o(i.nrf_drv_uart_rx) for nrf_drv_uart_rx
    app_uart_fifo.o(i.app_uart_init) refers to app_uart_fifo.o(.data) for .data
    app_uart_fifo.o(i.app_uart_init) refers to app_uart_fifo.o(.bss) for .bss
    app_uart_fifo.o(i.app_uart_init) refers to app_uart_fifo.o(.constdata) for .constdata
    app_uart_fifo.o(i.app_uart_init) refers to app_uart_fifo.o(i.uart_event_handler) for uart_event_handler
    app_uart_fifo.o(i.app_uart_put) refers to app_fifo.o(i.app_fifo_put) for app_fifo_put
    app_uart_fifo.o(i.app_uart_put) refers to nrfx_uarte.o(i.nrfx_uarte_tx_in_progress) for nrfx_uarte_tx_in_progress
    app_uart_fifo.o(i.app_uart_put) refers to nrfx_uart.o(i.nrfx_uart_tx_in_progress) for nrfx_uart_tx_in_progress
    app_uart_fifo.o(i.app_uart_put) refers to app_fifo.o(i.app_fifo_get) for app_fifo_get
    app_uart_fifo.o(i.app_uart_put) refers to app_uart_fifo.o(i.nrf_drv_uart_tx) for nrf_drv_uart_tx
    app_uart_fifo.o(i.app_uart_put) refers to app_uart_fifo.o(.bss) for .bss
    app_uart_fifo.o(i.app_uart_put) refers to app_uart_fifo.o(.data) for .data
    app_uart_fifo.o(i.app_uart_put) refers to nrf_drv_uart.o(.data) for nrf_drv_uart_use_easy_dma
    app_uart_fifo.o(i.nrf_drv_uart_rx) refers to nrfx_uarte.o(i.nrfx_uarte_rx) for nrfx_uarte_rx
    app_uart_fifo.o(i.nrf_drv_uart_rx) refers to nrfx_uart.o(i.nrfx_uart_rx) for nrfx_uart_rx
    app_uart_fifo.o(i.nrf_drv_uart_rx) refers to nrf_drv_uart.o(.data) for nrf_drv_uart_use_easy_dma
    app_uart_fifo.o(i.nrf_drv_uart_tx) refers to nrfx_uarte.o(i.nrfx_uarte_tx) for nrfx_uarte_tx
    app_uart_fifo.o(i.nrf_drv_uart_tx) refers to nrfx_uart.o(i.nrfx_uart_tx) for nrfx_uart_tx
    app_uart_fifo.o(i.nrf_drv_uart_tx) refers to nrf_drv_uart.o(.data) for nrf_drv_uart_use_easy_dma
    app_uart_fifo.o(i.uart_event_handler) refers to app_uart_fifo.o(i.nrf_drv_uart_rx) for nrf_drv_uart_rx
    app_uart_fifo.o(i.uart_event_handler) refers to app_fifo.o(i.app_fifo_put) for app_fifo_put
    app_uart_fifo.o(i.uart_event_handler) refers to app_fifo.o(i.app_fifo_get) for app_fifo_get
    app_uart_fifo.o(i.uart_event_handler) refers to app_uart_fifo.o(i.nrf_drv_uart_tx) for nrf_drv_uart_tx
    app_uart_fifo.o(i.uart_event_handler) refers to app_uart_fifo.o(.data) for .data
    app_uart_fifo.o(i.uart_event_handler) refers to app_uart_fifo.o(.bss) for .bss
    app_util_platform.o(i.app_util_critical_region_enter) refers to app_util_platform.o(.bss) for .bss
    app_util_platform.o(i.app_util_critical_region_exit) refers to app_util_platform.o(.bss) for .bss
    app_util_platform.o(i.app_util_disable_irq) refers to app_util_platform.o(.data) for .data
    app_util_platform.o(i.app_util_enable_irq) refers to app_util_platform.o(.data) for .data
    drv_rtc.o(i.RTC1_IRQHandler) refers to drv_rtc.o(.data) for .data
    drv_rtc.o(i.drv_rtc_compare_enable) refers to drv_rtc.o(i.evt_enable) for evt_enable
    drv_rtc.o(i.drv_rtc_compare_pending) refers to drv_rtc.o(i.evt_pending) for evt_pending
    drv_rtc.o(i.drv_rtc_compare_set) refers to drv_rtc.o(i.nrf_rtc_event_clear) for nrf_rtc_event_clear
    drv_rtc.o(i.drv_rtc_init) refers to drv_rtc.o(.data) for .data
    drv_rtc.o(i.drv_rtc_overflow_enable) refers to drv_rtc.o(i.evt_enable) for evt_enable
    drv_rtc.o(i.drv_rtc_overflow_pending) refers to drv_rtc.o(i.evt_pending) for evt_pending
    drv_rtc.o(i.drv_rtc_tick_enable) refers to drv_rtc.o(i.evt_enable) for evt_enable
    drv_rtc.o(i.drv_rtc_tick_pending) refers to drv_rtc.o(i.evt_pending) for evt_pending
    drv_rtc.o(i.drv_rtc_uninit) refers to drv_rtc.o(.data) for .data
    drv_rtc.o(i.drv_rtc_windowed_compare_set) refers to drv_rtc.o(i.nrf_rtc_event_clear) for nrf_rtc_event_clear
    drv_rtc.o(i.drv_rtc_windowed_compare_set) refers to drv_rtc.o(i.nrfx_coredep_delay_us) for nrfx_coredep_delay_us
    drv_rtc.o(i.drv_rtc_windowed_compare_set) refers to drv_rtc.o(i.evt_enable) for evt_enable
    drv_rtc.o(i.evt_pending) refers to drv_rtc.o(i.nrf_rtc_event_clear) for nrf_rtc_event_clear
    drv_rtc.o(i.nrfx_coredep_delay_us) refers to drv_rtc.o(.constdata) for .constdata
    nrf_assert.o(i.assert_nrf_callback) refers to app_error_weak.o(i.app_error_fault_handler) for app_error_fault_handler
    nrf_atfifo.o(i.nrf_atfifo_alloc_put) refers to nrf_atfifo.o(i.nrf_atfifo_item_alloc) for nrf_atfifo_item_alloc
    nrf_atfifo.o(i.nrf_atfifo_alloc_put) refers to memcpya.o(.text) for __aeabi_memcpy
    nrf_atfifo.o(i.nrf_atfifo_alloc_put) refers to nrf_atfifo.o(i.nrf_atfifo_item_put) for nrf_atfifo_item_put
    nrf_atfifo.o(i.nrf_atfifo_clear) refers to nrf_atfifo.o(.emb_text) for __asm___12_nrf_atfifo_c_51f461e1__nrf_atfifo_space_clear
    nrf_atfifo.o(i.nrf_atfifo_get_free) refers to nrf_atfifo.o(i.nrf_atfifo_item_get) for nrf_atfifo_item_get
    nrf_atfifo.o(i.nrf_atfifo_get_free) refers to memcpya.o(.text) for __aeabi_memcpy
    nrf_atfifo.o(i.nrf_atfifo_get_free) refers to nrf_atfifo.o(i.nrf_atfifo_item_free) for nrf_atfifo_item_free
    nrf_atfifo.o(i.nrf_atfifo_item_alloc) refers to nrf_atfifo.o(.emb_text) for __asm___12_nrf_atfifo_c_51f461e1__nrf_atfifo_wspace_req
    nrf_atfifo.o(i.nrf_atfifo_item_free) refers to nrf_atfifo.o(.emb_text) for __asm___12_nrf_atfifo_c_51f461e1__nrf_atfifo_rspace_close
    nrf_atfifo.o(i.nrf_atfifo_item_get) refers to nrf_atfifo.o(.emb_text) for __asm___12_nrf_atfifo_c_51f461e1__nrf_atfifo_rspace_req
    nrf_atfifo.o(i.nrf_atfifo_item_put) refers to nrf_atfifo.o(.emb_text) for __asm___12_nrf_atfifo_c_51f461e1__nrf_atfifo_wspace_close
    nrf_atomic.o(i.nrf_atomic_flag_clear) refers to nrf_atomic.o(i.nrf_atomic_u32_and) for nrf_atomic_u32_and
    nrf_atomic.o(i.nrf_atomic_flag_clear_fetch) refers to nrf_atomic.o(i.nrf_atomic_u32_fetch_and) for nrf_atomic_u32_fetch_and
    nrf_atomic.o(i.nrf_atomic_flag_set) refers to nrf_atomic.o(i.nrf_atomic_u32_or) for nrf_atomic_u32_or
    nrf_atomic.o(i.nrf_atomic_flag_set_fetch) refers to nrf_atomic.o(i.nrf_atomic_u32_fetch_or) for nrf_atomic_u32_fetch_or
    nrf_atomic.o(i.nrf_atomic_u32_add) refers to nrf_atomic.o(.emb_text) for __asm___12_nrf_atomic_c_85ca2469__nrf_atomic_internal_add
    nrf_atomic.o(i.nrf_atomic_u32_and) refers to nrf_atomic.o(.emb_text) for __asm___12_nrf_atomic_c_85ca2469__nrf_atomic_internal_and
    nrf_atomic.o(i.nrf_atomic_u32_cmp_exch) refers to nrf_atomic.o(.emb_text) for __asm___12_nrf_atomic_c_85ca2469__nrf_atomic_internal_cmp_exch
    nrf_atomic.o(i.nrf_atomic_u32_fetch_add) refers to nrf_atomic.o(.emb_text) for __asm___12_nrf_atomic_c_85ca2469__nrf_atomic_internal_add
    nrf_atomic.o(i.nrf_atomic_u32_fetch_and) refers to nrf_atomic.o(.emb_text) for __asm___12_nrf_atomic_c_85ca2469__nrf_atomic_internal_and
    nrf_atomic.o(i.nrf_atomic_u32_fetch_or) refers to nrf_atomic.o(.emb_text) for __asm___12_nrf_atomic_c_85ca2469__nrf_atomic_internal_orr
    nrf_atomic.o(i.nrf_atomic_u32_fetch_store) refers to nrf_atomic.o(.emb_text) for __asm___12_nrf_atomic_c_85ca2469__nrf_atomic_internal_mov
    nrf_atomic.o(i.nrf_atomic_u32_fetch_sub) refers to nrf_atomic.o(.emb_text) for __asm___12_nrf_atomic_c_85ca2469__nrf_atomic_internal_sub
    nrf_atomic.o(i.nrf_atomic_u32_fetch_sub_hs) refers to nrf_atomic.o(.emb_text) for __asm___12_nrf_atomic_c_85ca2469__nrf_atomic_internal_sub_hs
    nrf_atomic.o(i.nrf_atomic_u32_fetch_xor) refers to nrf_atomic.o(.emb_text) for __asm___12_nrf_atomic_c_85ca2469__nrf_atomic_internal_eor
    nrf_atomic.o(i.nrf_atomic_u32_or) refers to nrf_atomic.o(.emb_text) for __asm___12_nrf_atomic_c_85ca2469__nrf_atomic_internal_orr
    nrf_atomic.o(i.nrf_atomic_u32_store) refers to nrf_atomic.o(.emb_text) for __asm___12_nrf_atomic_c_85ca2469__nrf_atomic_internal_mov
    nrf_atomic.o(i.nrf_atomic_u32_sub) refers to nrf_atomic.o(.emb_text) for __asm___12_nrf_atomic_c_85ca2469__nrf_atomic_internal_sub
    nrf_atomic.o(i.nrf_atomic_u32_sub_hs) refers to nrf_atomic.o(.emb_text) for __asm___12_nrf_atomic_c_85ca2469__nrf_atomic_internal_sub_hs
    nrf_atomic.o(i.nrf_atomic_u32_xor) refers to nrf_atomic.o(.emb_text) for __asm___12_nrf_atomic_c_85ca2469__nrf_atomic_internal_eor
    nrf_balloc.o(i.nrf_balloc_alloc) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    nrf_balloc.o(i.nrf_balloc_alloc) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    nrf_balloc.o(i.nrf_balloc_free) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    nrf_balloc.o(i.nrf_balloc_free) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    nrf_fprintf.o(i.nrf_fprintf) refers to nrf_fprintf_format.o(i.nrf_fprintf_fmt) for nrf_fprintf_fmt
    nrf_fprintf_format.o(i.buffer_add) refers to nrf_fprintf.o(i.nrf_fprintf_buffer_flush) for nrf_fprintf_buffer_flush
    nrf_fprintf_format.o(i.int_print) refers to nrf_fprintf_format.o(i.buffer_add) for buffer_add
    nrf_fprintf_format.o(i.int_print) refers to nrf_fprintf_format.o(i.unsigned_print) for unsigned_print
    nrf_fprintf_format.o(i.nrf_fprintf_fmt) refers to nrf_fprintf_format.o(i.buffer_add) for buffer_add
    nrf_fprintf_format.o(i.nrf_fprintf_fmt) refers to nrf_fprintf.o(i.nrf_fprintf_buffer_flush) for nrf_fprintf_buffer_flush
    nrf_fprintf_format.o(i.nrf_fprintf_fmt) refers to nrf_fprintf_format.o(i.int_print) for int_print
    nrf_fprintf_format.o(i.nrf_fprintf_fmt) refers to nrf_fprintf_format.o(i.unsigned_print) for unsigned_print
    nrf_fprintf_format.o(i.nrf_fprintf_fmt) refers to strlen.o(.text) for strlen
    nrf_fprintf_format.o(i.unsigned_print) refers to nrf_fprintf_format.o(i.buffer_add) for buffer_add
    nrf_fprintf_format.o(i.unsigned_print) refers to nrf_fprintf_format.o(.constdata) for .constdata
    nrf_memobj.o(i.memobj_op) refers to memcpya.o(.text) for __aeabi_memcpy
    nrf_memobj.o(i.nrf_memobj_alloc) refers to nrf_balloc.o(i.nrf_balloc_alloc) for nrf_balloc_alloc
    nrf_memobj.o(i.nrf_memobj_alloc) refers to nrf_memobj.o(i.nrf_memobj_free) for nrf_memobj_free
    nrf_memobj.o(i.nrf_memobj_free) refers to nrf_balloc.o(i.nrf_balloc_free) for nrf_balloc_free
    nrf_memobj.o(i.nrf_memobj_get) refers to nrf_atomic.o(i.nrf_atomic_u32_add) for nrf_atomic_u32_add
    nrf_memobj.o(i.nrf_memobj_pool_init) refers to nrf_balloc.o(i.nrf_balloc_init) for nrf_balloc_init
    nrf_memobj.o(i.nrf_memobj_put) refers to nrf_atomic.o(i.nrf_atomic_u32_sub) for nrf_atomic_u32_sub
    nrf_memobj.o(i.nrf_memobj_put) refers to nrf_memobj.o(i.nrf_memobj_free) for nrf_memobj_free
    nrf_memobj.o(i.nrf_memobj_read) refers to nrf_memobj.o(i.memobj_op) for memobj_op
    nrf_memobj.o(i.nrf_memobj_write) refers to nrf_memobj.o(i.memobj_op) for memobj_op
    nrf_pwr_mgmt.o(i.nrf_pwr_mgmt_init) refers to nrf_section_iter.o(i.nrf_section_iter_init) for nrf_section_iter_init
    nrf_pwr_mgmt.o(i.nrf_pwr_mgmt_init) refers to nrf_pwr_mgmt.o(.data) for .data
    nrf_pwr_mgmt.o(i.nrf_pwr_mgmt_init) refers to nrf_pwr_mgmt.o(.constdata) for .constdata
    nrf_pwr_mgmt.o(i.nrf_pwr_mgmt_init) refers to nrf_pwr_mgmt.o(.bss) for .bss
    nrf_pwr_mgmt.o(i.nrf_pwr_mgmt_run) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    nrf_pwr_mgmt.o(i.nrf_pwr_mgmt_run) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    nrf_pwr_mgmt.o(i.nrf_pwr_mgmt_run) refers to nrf_sdh.o(i.nrf_sdh_is_enabled) for nrf_sdh_is_enabled
    nrf_pwr_mgmt.o(i.nrf_pwr_mgmt_shutdown) refers to nrf_atomic.o(i.nrf_atomic_u32_fetch_store) for nrf_atomic_u32_fetch_store
    nrf_pwr_mgmt.o(i.nrf_pwr_mgmt_shutdown) refers to nrf_pwr_mgmt.o(i.shutdown_process) for shutdown_process
    nrf_pwr_mgmt.o(i.nrf_pwr_mgmt_shutdown) refers to nrf_pwr_mgmt.o(.data) for .data
    nrf_pwr_mgmt.o(i.shutdown_process) refers to nrf_section_iter.o(i.nrf_section_iter_next) for nrf_section_iter_next
    nrf_pwr_mgmt.o(i.shutdown_process) refers to nrf_sdh.o(i.nrf_sdh_is_enabled) for nrf_sdh_is_enabled
    nrf_pwr_mgmt.o(i.shutdown_process) refers to nrf_pwr_mgmt.o(.bss) for .bss
    nrf_pwr_mgmt.o(i.shutdown_process) refers to nrf_pwr_mgmt.o(.data) for .data
    nrf_pwr_mgmt.o(.constdata) refers to nrf_pwr_mgmt.o(.constdata) for pwr_mgmt_data_array
    nrf_queue.o(i.nrf_queue_available_get) refers to nrf_queue.o(i.nrf_queue_utilization_get) for nrf_queue_utilization_get
    nrf_queue.o(i.nrf_queue_generic_pop) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    nrf_queue.o(i.nrf_queue_generic_pop) refers to nrf_queue.o(i.nrf_queue_is_empty) for nrf_queue_is_empty
    nrf_queue.o(i.nrf_queue_generic_pop) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    nrf_queue.o(i.nrf_queue_generic_pop) refers to nrf_queue.o(i.nrf_queue_next_idx) for nrf_queue_next_idx
    nrf_queue.o(i.nrf_queue_generic_pop) refers to memcpya.o(.text) for __aeabi_memcpy
    nrf_queue.o(i.nrf_queue_in) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    nrf_queue.o(i.nrf_queue_in) refers to nrf_queue.o(i.nrf_queue_available_get) for nrf_queue_available_get
    nrf_queue.o(i.nrf_queue_in) refers to nrf_queue.o(i.queue_write) for queue_write
    nrf_queue.o(i.nrf_queue_in) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    nrf_queue.o(i.nrf_queue_out) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    nrf_queue.o(i.nrf_queue_out) refers to nrf_queue.o(i.queue_utilization_get) for queue_utilization_get
    nrf_queue.o(i.nrf_queue_out) refers to nrf_queue.o(i.queue_read) for queue_read
    nrf_queue.o(i.nrf_queue_out) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    nrf_queue.o(i.nrf_queue_push) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    nrf_queue.o(i.nrf_queue_push) refers to nrf_queue.o(i.nrf_queue_is_full) for nrf_queue_is_full
    nrf_queue.o(i.nrf_queue_push) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    nrf_queue.o(i.nrf_queue_push) refers to nrf_queue.o(i.nrf_queue_next_idx) for nrf_queue_next_idx
    nrf_queue.o(i.nrf_queue_push) refers to memcpya.o(.text) for __aeabi_memcpy
    nrf_queue.o(i.nrf_queue_push) refers to nrf_queue.o(i.queue_utilization_get) for queue_utilization_get
    nrf_queue.o(i.nrf_queue_read) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    nrf_queue.o(i.nrf_queue_read) refers to nrf_queue.o(i.queue_utilization_get) for queue_utilization_get
    nrf_queue.o(i.nrf_queue_read) refers to nrf_queue.o(i.queue_read) for queue_read
    nrf_queue.o(i.nrf_queue_read) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    nrf_queue.o(i.nrf_queue_reset) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    nrf_queue.o(i.nrf_queue_reset) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    nrf_queue.o(i.nrf_queue_utilization_get) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    nrf_queue.o(i.nrf_queue_utilization_get) refers to nrf_queue.o(i.queue_utilization_get) for queue_utilization_get
    nrf_queue.o(i.nrf_queue_utilization_get) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    nrf_queue.o(i.nrf_queue_write) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    nrf_queue.o(i.nrf_queue_write) refers to nrf_queue.o(i.nrf_queue_available_get) for nrf_queue_available_get
    nrf_queue.o(i.nrf_queue_write) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    nrf_queue.o(i.nrf_queue_write) refers to nrf_queue.o(i.queue_write) for queue_write
    nrf_queue.o(i.queue_read) refers to nrf_queue.o(i.continous_items_get) for continous_items_get
    nrf_queue.o(i.queue_read) refers to memcpya.o(.text) for __aeabi_memcpy
    nrf_queue.o(i.queue_write) refers to nrf_queue.o(i.nrf_queue_available_get) for nrf_queue_available_get
    nrf_queue.o(i.queue_write) refers to nrf_queue.o(i.continous_items_get) for continous_items_get
    nrf_queue.o(i.queue_write) refers to memcpya.o(.text) for __aeabi_memcpy
    nrf_queue.o(i.queue_write) refers to nrf_queue.o(i.nrf_queue_next_idx) for nrf_queue_next_idx
    nrf_queue.o(i.queue_write) refers to nrf_queue.o(i.queue_utilization_get) for queue_utilization_get
    nrf_ringbuf.o(i.nrf_ringbuf_alloc) refers to nrf_atomic.o(i.nrf_atomic_flag_set_fetch) for nrf_atomic_flag_set_fetch
    nrf_ringbuf.o(i.nrf_ringbuf_alloc) refers to nrf_atomic.o(i.nrf_atomic_flag_clear) for nrf_atomic_flag_clear
    nrf_ringbuf.o(i.nrf_ringbuf_cpy_get) refers to nrf_atomic.o(i.nrf_atomic_flag_set_fetch) for nrf_atomic_flag_set_fetch
    nrf_ringbuf.o(i.nrf_ringbuf_cpy_get) refers to memcpya.o(.text) for __aeabi_memcpy
    nrf_ringbuf.o(i.nrf_ringbuf_cpy_get) refers to nrf_atomic.o(i.nrf_atomic_flag_clear) for nrf_atomic_flag_clear
    nrf_ringbuf.o(i.nrf_ringbuf_cpy_put) refers to nrf_atomic.o(i.nrf_atomic_flag_set_fetch) for nrf_atomic_flag_set_fetch
    nrf_ringbuf.o(i.nrf_ringbuf_cpy_put) refers to memcpya.o(.text) for __aeabi_memcpy
    nrf_ringbuf.o(i.nrf_ringbuf_cpy_put) refers to nrf_atomic.o(i.nrf_atomic_flag_clear) for nrf_atomic_flag_clear
    nrf_ringbuf.o(i.nrf_ringbuf_free) refers to nrf_atomic.o(i.nrf_atomic_flag_clear) for nrf_atomic_flag_clear
    nrf_ringbuf.o(i.nrf_ringbuf_get) refers to nrf_atomic.o(i.nrf_atomic_flag_set_fetch) for nrf_atomic_flag_set_fetch
    nrf_ringbuf.o(i.nrf_ringbuf_get) refers to nrf_atomic.o(i.nrf_atomic_flag_clear) for nrf_atomic_flag_clear
    nrf_ringbuf.o(i.nrf_ringbuf_put) refers to nrf_atomic.o(i.nrf_atomic_flag_clear_fetch) for nrf_atomic_flag_clear_fetch
    nrf_section_iter.o(i.nrf_section_iter_init) refers to nrf_section_iter.o(i.nrf_section_iter_item_set) for nrf_section_iter_item_set
    nrf_section_iter.o(i.nrf_section_iter_next) refers to nrf_section_iter.o(i.nrf_section_iter_item_set) for nrf_section_iter_item_set
    nrf_strerror.o(i.nrf_strerror_find) refers to nrf_strerror.o(.constdata) for .constdata
    nrf_strerror.o(i.nrf_strerror_get) refers to nrf_strerror.o(i.nrf_strerror_find) for nrf_strerror_find
    nrf_strerror.o(i.nrf_strerror_get) refers to nrf_strerror.o(.constdata) for .constdata
    nrf_strerror.o(.constdata) refers to nrf_strerror.o(.conststring) for .conststring
    retarget.o(i.fgetc) refers to app_uart_fifo.o(i.app_uart_get) for app_uart_get
    retarget.o(i.fputc) refers to app_uart_fifo.o(i.app_uart_put) for app_uart_put
    segger_rtt.o(i.SEGGER_RTT_AllocDownBuffer) refers to segger_rtt.o(i._DoInit) for _DoInit
    segger_rtt.o(i.SEGGER_RTT_AllocDownBuffer) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    segger_rtt.o(i.SEGGER_RTT_AllocDownBuffer) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    segger_rtt.o(i.SEGGER_RTT_AllocDownBuffer) refers to segger_rtt.o(.bss) for .bss
    segger_rtt.o(i.SEGGER_RTT_AllocUpBuffer) refers to segger_rtt.o(i._DoInit) for _DoInit
    segger_rtt.o(i.SEGGER_RTT_AllocUpBuffer) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    segger_rtt.o(i.SEGGER_RTT_AllocUpBuffer) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    segger_rtt.o(i.SEGGER_RTT_AllocUpBuffer) refers to segger_rtt.o(.bss) for .bss
    segger_rtt.o(i.SEGGER_RTT_ConfigDownBuffer) refers to segger_rtt.o(i._DoInit) for _DoInit
    segger_rtt.o(i.SEGGER_RTT_ConfigDownBuffer) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    segger_rtt.o(i.SEGGER_RTT_ConfigDownBuffer) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    segger_rtt.o(i.SEGGER_RTT_ConfigDownBuffer) refers to segger_rtt.o(.bss) for .bss
    segger_rtt.o(i.SEGGER_RTT_ConfigUpBuffer) refers to segger_rtt.o(i._DoInit) for _DoInit
    segger_rtt.o(i.SEGGER_RTT_ConfigUpBuffer) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    segger_rtt.o(i.SEGGER_RTT_ConfigUpBuffer) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    segger_rtt.o(i.SEGGER_RTT_ConfigUpBuffer) refers to segger_rtt.o(.bss) for .bss
    segger_rtt.o(i.SEGGER_RTT_GetKey) refers to segger_rtt.o(i.SEGGER_RTT_Read) for SEGGER_RTT_Read
    segger_rtt.o(i.SEGGER_RTT_HasData) refers to segger_rtt.o(.bss) for .bss
    segger_rtt.o(i.SEGGER_RTT_HasKey) refers to segger_rtt.o(i._DoInit) for _DoInit
    segger_rtt.o(i.SEGGER_RTT_HasKey) refers to segger_rtt.o(.bss) for .bss
    segger_rtt.o(i.SEGGER_RTT_Init) refers to segger_rtt.o(i._DoInit) for _DoInit
    segger_rtt.o(i.SEGGER_RTT_PutChar) refers to segger_rtt.o(i._DoInit) for _DoInit
    segger_rtt.o(i.SEGGER_RTT_PutChar) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    segger_rtt.o(i.SEGGER_RTT_PutChar) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    segger_rtt.o(i.SEGGER_RTT_PutChar) refers to segger_rtt.o(.bss) for .bss
    segger_rtt.o(i.SEGGER_RTT_PutCharSkip) refers to segger_rtt.o(i._DoInit) for _DoInit
    segger_rtt.o(i.SEGGER_RTT_PutCharSkip) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    segger_rtt.o(i.SEGGER_RTT_PutCharSkip) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    segger_rtt.o(i.SEGGER_RTT_PutCharSkip) refers to segger_rtt.o(.bss) for .bss
    segger_rtt.o(i.SEGGER_RTT_PutCharSkipNoLock) refers to segger_rtt.o(.bss) for .bss
    segger_rtt.o(i.SEGGER_RTT_Read) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    segger_rtt.o(i.SEGGER_RTT_Read) refers to segger_rtt.o(i.SEGGER_RTT_ReadNoLock) for SEGGER_RTT_ReadNoLock
    segger_rtt.o(i.SEGGER_RTT_Read) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    segger_rtt.o(i.SEGGER_RTT_ReadNoLock) refers to segger_rtt.o(i._DoInit) for _DoInit
    segger_rtt.o(i.SEGGER_RTT_ReadNoLock) refers to memcpya.o(.text) for __aeabi_memcpy
    segger_rtt.o(i.SEGGER_RTT_ReadNoLock) refers to segger_rtt.o(.bss) for .bss
    segger_rtt.o(i.SEGGER_RTT_SetFlagsDownBuffer) refers to segger_rtt.o(i._DoInit) for _DoInit
    segger_rtt.o(i.SEGGER_RTT_SetFlagsDownBuffer) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    segger_rtt.o(i.SEGGER_RTT_SetFlagsDownBuffer) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    segger_rtt.o(i.SEGGER_RTT_SetFlagsDownBuffer) refers to segger_rtt.o(.bss) for .bss
    segger_rtt.o(i.SEGGER_RTT_SetFlagsUpBuffer) refers to segger_rtt.o(i._DoInit) for _DoInit
    segger_rtt.o(i.SEGGER_RTT_SetFlagsUpBuffer) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    segger_rtt.o(i.SEGGER_RTT_SetFlagsUpBuffer) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    segger_rtt.o(i.SEGGER_RTT_SetFlagsUpBuffer) refers to segger_rtt.o(.bss) for .bss
    segger_rtt.o(i.SEGGER_RTT_SetNameDownBuffer) refers to segger_rtt.o(i._DoInit) for _DoInit
    segger_rtt.o(i.SEGGER_RTT_SetNameDownBuffer) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    segger_rtt.o(i.SEGGER_RTT_SetNameDownBuffer) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    segger_rtt.o(i.SEGGER_RTT_SetNameDownBuffer) refers to segger_rtt.o(.bss) for .bss
    segger_rtt.o(i.SEGGER_RTT_SetNameUpBuffer) refers to segger_rtt.o(i._DoInit) for _DoInit
    segger_rtt.o(i.SEGGER_RTT_SetNameUpBuffer) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    segger_rtt.o(i.SEGGER_RTT_SetNameUpBuffer) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    segger_rtt.o(i.SEGGER_RTT_SetNameUpBuffer) refers to segger_rtt.o(.bss) for .bss
    segger_rtt.o(i.SEGGER_RTT_SetTerminal) refers to segger_rtt.o(i._DoInit) for _DoInit
    segger_rtt.o(i.SEGGER_RTT_SetTerminal) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    segger_rtt.o(i.SEGGER_RTT_SetTerminal) refers to segger_rtt.o(i._GetAvailWriteSpace) for _GetAvailWriteSpace
    segger_rtt.o(i.SEGGER_RTT_SetTerminal) refers to segger_rtt.o(i._WriteNoCheck) for _WriteNoCheck
    segger_rtt.o(i.SEGGER_RTT_SetTerminal) refers to segger_rtt.o(i._WriteBlocking) for _WriteBlocking
    segger_rtt.o(i.SEGGER_RTT_SetTerminal) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    segger_rtt.o(i.SEGGER_RTT_SetTerminal) refers to segger_rtt.o(.bss) for .bss
    segger_rtt.o(i.SEGGER_RTT_SetTerminal) refers to segger_rtt.o(.data) for .data
    segger_rtt.o(i.SEGGER_RTT_TerminalOut) refers to segger_rtt.o(i._DoInit) for _DoInit
    segger_rtt.o(i.SEGGER_RTT_TerminalOut) refers to strlen.o(.text) for strlen
    segger_rtt.o(i.SEGGER_RTT_TerminalOut) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    segger_rtt.o(i.SEGGER_RTT_TerminalOut) refers to segger_rtt.o(i._GetAvailWriteSpace) for _GetAvailWriteSpace
    segger_rtt.o(i.SEGGER_RTT_TerminalOut) refers to segger_rtt.o(i._PostTerminalSwitch) for _PostTerminalSwitch
    segger_rtt.o(i.SEGGER_RTT_TerminalOut) refers to segger_rtt.o(i._WriteBlocking) for _WriteBlocking
    segger_rtt.o(i.SEGGER_RTT_TerminalOut) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    segger_rtt.o(i.SEGGER_RTT_TerminalOut) refers to segger_rtt.o(.bss) for .bss
    segger_rtt.o(i.SEGGER_RTT_TerminalOut) refers to segger_rtt.o(.data) for .data
    segger_rtt.o(i.SEGGER_RTT_WaitKey) refers to segger_rtt.o(i.SEGGER_RTT_GetKey) for SEGGER_RTT_GetKey
    segger_rtt.o(i.SEGGER_RTT_Write) refers to segger_rtt.o(i._DoInit) for _DoInit
    segger_rtt.o(i.SEGGER_RTT_Write) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    segger_rtt.o(i.SEGGER_RTT_Write) refers to segger_rtt.o(i.SEGGER_RTT_WriteNoLock) for SEGGER_RTT_WriteNoLock
    segger_rtt.o(i.SEGGER_RTT_Write) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    segger_rtt.o(i.SEGGER_RTT_Write) refers to segger_rtt.o(.bss) for .bss
    segger_rtt.o(i.SEGGER_RTT_WriteNoLock) refers to segger_rtt.o(i._GetAvailWriteSpace) for _GetAvailWriteSpace
    segger_rtt.o(i.SEGGER_RTT_WriteNoLock) refers to segger_rtt.o(i._WriteNoCheck) for _WriteNoCheck
    segger_rtt.o(i.SEGGER_RTT_WriteNoLock) refers to segger_rtt.o(i._WriteBlocking) for _WriteBlocking
    segger_rtt.o(i.SEGGER_RTT_WriteNoLock) refers to segger_rtt.o(.bss) for .bss
    segger_rtt.o(i.SEGGER_RTT_WriteSkipNoLock) refers to memcpya.o(.text) for __aeabi_memcpy
    segger_rtt.o(i.SEGGER_RTT_WriteSkipNoLock) refers to segger_rtt.o(.bss) for .bss
    segger_rtt.o(i.SEGGER_RTT_WriteString) refers to strlen.o(.text) for strlen
    segger_rtt.o(i.SEGGER_RTT_WriteString) refers to segger_rtt.o(i.SEGGER_RTT_Write) for SEGGER_RTT_Write
    segger_rtt.o(i.SEGGER_RTT_WriteWithOverwriteNoLock) refers to memcpya.o(.text) for __aeabi_memcpy
    segger_rtt.o(i.SEGGER_RTT_WriteWithOverwriteNoLock) refers to segger_rtt.o(.bss) for .bss
    segger_rtt.o(i._DoInit) refers to strcpy.o(.text) for strcpy
    segger_rtt.o(i._DoInit) refers to segger_rtt.o(.bss) for .bss
    segger_rtt.o(i._PostTerminalSwitch) refers to segger_rtt.o(i._WriteBlocking) for _WriteBlocking
    segger_rtt.o(i._PostTerminalSwitch) refers to segger_rtt.o(.data) for .data
    segger_rtt.o(i._WriteBlocking) refers to memcpya.o(.text) for __aeabi_memcpy
    segger_rtt.o(i._WriteNoCheck) refers to memcpya.o(.text) for __aeabi_memcpy
    segger_rtt_printf.o(i.SEGGER_RTT_printf) refers to segger_rtt_printf.o(i.SEGGER_RTT_vprintf) for SEGGER_RTT_vprintf
    segger_rtt_printf.o(i.SEGGER_RTT_vprintf) refers to segger_rtt_printf.o(i._StoreChar) for _StoreChar
    segger_rtt_printf.o(i.SEGGER_RTT_vprintf) refers to segger_rtt_printf.o(i._PrintInt) for _PrintInt
    segger_rtt_printf.o(i.SEGGER_RTT_vprintf) refers to segger_rtt_printf.o(i._PrintUnsigned) for _PrintUnsigned
    segger_rtt_printf.o(i.SEGGER_RTT_vprintf) refers to segger_rtt.o(i.SEGGER_RTT_Write) for SEGGER_RTT_Write
    segger_rtt_printf.o(i._PrintInt) refers to segger_rtt_printf.o(i._StoreChar) for _StoreChar
    segger_rtt_printf.o(i._PrintInt) refers to segger_rtt_printf.o(i._PrintUnsigned) for _PrintUnsigned
    segger_rtt_printf.o(i._PrintUnsigned) refers to segger_rtt_printf.o(i._StoreChar) for _StoreChar
    segger_rtt_printf.o(i._PrintUnsigned) refers to segger_rtt_printf.o(.constdata) for .constdata
    segger_rtt_printf.o(i._StoreChar) refers to segger_rtt.o(i.SEGGER_RTT_Write) for SEGGER_RTT_Write
    nrf_sdh.o(i.SWI2_EGU2_IRQHandler) refers to nrf_sdh.o(i.nrf_sdh_evts_poll) for nrf_sdh_evts_poll
    nrf_sdh.o(i.nrf_sdh_disable_request) refers to nrf_sdh.o(i.sdh_request_observer_notify) for sdh_request_observer_notify
    nrf_sdh.o(i.nrf_sdh_disable_request) refers to nrf_sdh.o(i.sdh_state_observer_notify) for sdh_state_observer_notify
    nrf_sdh.o(i.nrf_sdh_disable_request) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    nrf_sdh.o(i.nrf_sdh_disable_request) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    nrf_sdh.o(i.nrf_sdh_disable_request) refers to nrf_sdh.o(i.softdevice_evt_irq_disable) for softdevice_evt_irq_disable
    nrf_sdh.o(i.nrf_sdh_disable_request) refers to nrf_sdh.o(.data) for .data
    nrf_sdh.o(i.nrf_sdh_enable_request) refers to nrf_sdh.o(i.sdh_request_observer_notify) for sdh_request_observer_notify
    nrf_sdh.o(i.nrf_sdh_enable_request) refers to nrf_sdh.o(i.sdh_state_observer_notify) for sdh_state_observer_notify
    nrf_sdh.o(i.nrf_sdh_enable_request) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    nrf_sdh.o(i.nrf_sdh_enable_request) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    nrf_sdh.o(i.nrf_sdh_enable_request) refers to nrf_sdh.o(i.softdevices_evt_irq_enable) for softdevices_evt_irq_enable
    nrf_sdh.o(i.nrf_sdh_enable_request) refers to nrf_sdh.o(.data) for .data
    nrf_sdh.o(i.nrf_sdh_enable_request) refers to nrf_sdh.o(.constdata) for .constdata
    nrf_sdh.o(i.nrf_sdh_enable_request) refers to app_error_weak.o(i.app_error_fault_handler) for app_error_fault_handler
    nrf_sdh.o(i.nrf_sdh_evts_poll) refers to nrf_section_iter.o(i.nrf_section_iter_init) for nrf_section_iter_init
    nrf_sdh.o(i.nrf_sdh_evts_poll) refers to nrf_section_iter.o(i.nrf_section_iter_next) for nrf_section_iter_next
    nrf_sdh.o(i.nrf_sdh_evts_poll) refers to nrf_sdh.o(.constdata) for .constdata
    nrf_sdh.o(i.nrf_sdh_is_enabled) refers to nrf_sdh.o(.data) for .data
    nrf_sdh.o(i.nrf_sdh_is_suspended) refers to nrf_sdh.o(.data) for .data
    nrf_sdh.o(i.nrf_sdh_request_continue) refers to nrf_sdh.o(i.nrf_sdh_disable_request) for nrf_sdh_disable_request
    nrf_sdh.o(i.nrf_sdh_request_continue) refers to nrf_sdh.o(i.nrf_sdh_enable_request) for nrf_sdh_enable_request
    nrf_sdh.o(i.nrf_sdh_request_continue) refers to nrf_sdh.o(.data) for .data
    nrf_sdh.o(i.nrf_sdh_resume) refers to nrf_sdh.o(i.__sd_nvic_app_accessible_irq) for __sd_nvic_app_accessible_irq
    nrf_sdh.o(i.nrf_sdh_resume) refers to app_error.o(i.app_error_handler_bare) for app_error_handler_bare
    nrf_sdh.o(i.nrf_sdh_resume) refers to nrf_sdh.o(i.softdevices_evt_irq_enable) for softdevices_evt_irq_enable
    nrf_sdh.o(i.nrf_sdh_resume) refers to nrf_sdh.o(.data) for .data
    nrf_sdh.o(i.nrf_sdh_suspend) refers to nrf_sdh.o(i.softdevice_evt_irq_disable) for softdevice_evt_irq_disable
    nrf_sdh.o(i.nrf_sdh_suspend) refers to nrf_sdh.o(.data) for .data
    nrf_sdh.o(i.sdh_request_observer_notify) refers to nrf_section_iter.o(i.nrf_section_iter_init) for nrf_section_iter_init
    nrf_sdh.o(i.sdh_request_observer_notify) refers to nrf_section_iter.o(i.nrf_section_iter_next) for nrf_section_iter_next
    nrf_sdh.o(i.sdh_request_observer_notify) refers to nrf_sdh.o(.constdata) for .constdata
    nrf_sdh.o(i.sdh_state_observer_notify) refers to nrf_section_iter.o(i.nrf_section_iter_init) for nrf_section_iter_init
    nrf_sdh.o(i.sdh_state_observer_notify) refers to nrf_section_iter.o(i.nrf_section_iter_next) for nrf_section_iter_next
    nrf_sdh.o(i.sdh_state_observer_notify) refers to nrf_sdh.o(.constdata) for .constdata
    nrf_sdh.o(i.softdevice_evt_irq_disable) refers to nrf_sdh.o(i.__sd_nvic_app_accessible_irq) for __sd_nvic_app_accessible_irq
    nrf_sdh.o(i.softdevice_evt_irq_disable) refers to app_error.o(i.app_error_handler_bare) for app_error_handler_bare
    nrf_sdh.o(i.softdevice_evt_irq_disable) refers to app_util_platform.o(.bss) for nrf_nvic_state
    nrf_sdh.o(i.softdevices_evt_irq_enable) refers to nrf_sdh.o(i.__sd_nvic_app_accessible_irq) for __sd_nvic_app_accessible_irq
    nrf_sdh.o(i.softdevices_evt_irq_enable) refers to app_error.o(i.app_error_handler_bare) for app_error_handler_bare
    nrf_sdh.o(i.softdevices_evt_irq_enable) refers to app_util_platform.o(.bss) for nrf_nvic_state
    nrf_sdh.o(.constdata) refers to nrf_sdh.o(.constdata) for sdh_req_observers_array
    nrf_sdh.o(.constdata) refers to nrf_sdh.o(.constdata) for sdh_state_observers_array
    nrf_sdh.o(.constdata) refers to nrf_sdh.o(.constdata) for sdh_stack_observers_array
    nrf_sdh_ble.o(i.nrf_sdh_ble_app_ram_start_get) refers to nrf_sdh_ble.o(.constdata) for .constdata
    nrf_sdh_ble.o(i.nrf_sdh_ble_default_cfg_set) refers to nrf_sdh_ble.o(i.nrf_sdh_ble_app_ram_start_get) for nrf_sdh_ble_app_ram_start_get
    nrf_sdh_ble.o(i.nrf_sdh_ble_enable) refers to nrf_sdh_ble.o(.data) for .data
    nrf_sdh_ble.o(i.nrf_sdh_ble_evts_poll) refers to app_error.o(i.app_error_handler_bare) for app_error_handler_bare
    nrf_sdh_ble.o(i.nrf_sdh_ble_evts_poll) refers to nrf_section_iter.o(i.nrf_section_iter_init) for nrf_section_iter_init
    nrf_sdh_ble.o(i.nrf_sdh_ble_evts_poll) refers to nrf_section_iter.o(i.nrf_section_iter_next) for nrf_section_iter_next
    nrf_sdh_ble.o(i.nrf_sdh_ble_evts_poll) refers to nrf_sdh_ble.o(.data) for .data
    nrf_sdh_ble.o(i.nrf_sdh_ble_evts_poll) refers to nrf_sdh_ble.o(.constdata) for .constdata
    nrf_sdh_ble.o(.constdata) refers to nrf_sdh_ble.o(.constdata) for sdh_ble_observers_array
    nrf_sdh_ble.o(sdh_stack_observers0) refers to nrf_sdh_ble.o(i.nrf_sdh_ble_evts_poll) for nrf_sdh_ble_evts_poll
    nrf_sdh_soc.o(i.nrf_sdh_soc_evts_poll) refers to app_error.o(i.app_error_handler_bare) for app_error_handler_bare
    nrf_sdh_soc.o(i.nrf_sdh_soc_evts_poll) refers to nrf_section_iter.o(i.nrf_section_iter_init) for nrf_section_iter_init
    nrf_sdh_soc.o(i.nrf_sdh_soc_evts_poll) refers to nrf_section_iter.o(i.nrf_section_iter_next) for nrf_section_iter_next
    nrf_sdh_soc.o(i.nrf_sdh_soc_evts_poll) refers to nrf_sdh_soc.o(.constdata) for .constdata
    nrf_sdh_soc.o(.constdata) refers to nrf_sdh_soc.o(.constdata) for sdh_soc_observers_array
    nrf_sdh_soc.o(sdh_stack_observers0) refers to nrf_sdh_soc.o(i.nrf_sdh_soc_evts_poll) for nrf_sdh_soc_evts_poll
    arm_startup_nrf52.o(RESET) refers to arm_startup_nrf52.o(STACK) for __initial_sp
    arm_startup_nrf52.o(RESET) refers to arm_startup_nrf52.o(.text) for Reset_Handler
    arm_startup_nrf52.o(RESET) refers to nrfx_clock.o(i.POWER_CLOCK_IRQHandler) for POWER_CLOCK_IRQHandler
    arm_startup_nrf52.o(RESET) refers to nrfx_prs.o(i.UARTE0_UART0_IRQHandler) for UARTE0_UART0_IRQHandler
    arm_startup_nrf52.o(RESET) refers to nrfx_gpiote.o(i.GPIOTE_IRQHandler) for GPIOTE_IRQHandler
    arm_startup_nrf52.o(RESET) refers to drv_rtc.o(i.RTC1_IRQHandler) for RTC1_IRQHandler
    arm_startup_nrf52.o(RESET) refers to nrf_sdh.o(i.SWI2_EGU2_IRQHandler) for SWI2_EGU2_IRQHandler
    arm_startup_nrf52.o(.text) refers to system_nrf52.o(i.SystemInit) for SystemInit
    arm_startup_nrf52.o(.text) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    system_nrf52.o(i.SystemCoreClockUpdate) refers to system_nrf52.o(.data) for .data
    system_nrf52.o(i.SystemInit) refers to system_nrf52.o(.data) for .data
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry10a.o(.ARM.Collect$$$$0000000F) for __rt_final_cpp
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry11a.o(.ARM.Collect$$$$00000011) for __rt_final_exit
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry12b.o(.ARM.Collect$$$$0000000E) for __rt_lib_shutdown_fini
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry7b.o(.ARM.Collect$$$$00000008) for _main_clock
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry8b.o(.ARM.Collect$$$$0000000A) for _main_cpp_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry9a.o(.ARM.Collect$$$$0000000B) for _main_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry5.o(.ARM.Collect$$$$00000004) for _main_scatterload
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry2.o(.ARM.Collect$$$$00000001) for _main_stk
    uldiv.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    uldiv.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    entry2.o(.ARM.Collect$$$$00000001) refers to entry2.o(.ARM.Collect$$$$00002712) for __lit__00000000
    entry2.o(.ARM.Collect$$$$00002712) refers to arm_startup_nrf52.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to arm_startup_nrf52.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry5.o(.ARM.Collect$$$$00000004) refers to init.o(.text) for __scatterload
    entry9a.o(.ARM.Collect$$$$0000000B) refers to main.o(i.main) for main
    entry9b.o(.ARM.Collect$$$$0000000C) refers to main.o(i.main) for main
    init.o(.text) refers to entry5.o(.ARM.Collect$$$$00000004) for __main_after_scatterload


==============================================================================

Removing Unused input sections from the image.

    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing main.o(.rrx_text), (6 bytes).
    Removing main.o(i.assert_nrf_callback), (16 bytes).
    Removing main.o(i.bsp_event_handler), (40 bytes).
    Removing boards.o(.rev16_text), (4 bytes).
    Removing boards.o(.revsh_text), (4 bytes).
    Removing boards.o(.rrx_text), (6 bytes).
    Removing boards.o(i.bsp_board_button_state_get), (32 bytes).
    Removing boards.o(i.bsp_board_init), (84 bytes).
    Removing boards.o(i.bsp_board_led_idx_to_pin), (12 bytes).
    Removing boards.o(i.bsp_board_pin_to_led_idx), (36 bytes).
    Removing boards.o(i.nrf_gpio_cfg), (36 bytes).
    Removing bsp.o(.rev16_text), (4 bytes).
    Removing bsp.o(.revsh_text), (4 bytes).
    Removing bsp.o(.rrx_text), (6 bytes).
    Removing bsp.o(i.alert_timer_handler), (6 bytes).
    Removing bsp.o(i.bsp_button_is_pressed), (12 bytes).
    Removing bsp.o(i.bsp_buttons_disable), (4 bytes).
    Removing bsp.o(i.bsp_buttons_enable), (4 bytes).
    Removing bsp.o(i.bsp_init), (148 bytes).
    Removing bsp.o(i.bsp_wakeup_button_disable), (6 bytes).
    Removing bsp.o(i.button_timer_handler), (8 bytes).
    Removing bsp.o(i.leds_timer_handler), (20 bytes).
    Removing bsp_btn_ble.o(.rev16_text), (4 bytes).
    Removing bsp_btn_ble.o(.revsh_text), (4 bytes).
    Removing bsp_btn_ble.o(.rrx_text), (6 bytes).
    Removing bsp_btn_ble.o(i.bsp_btn_ble_init), (56 bytes).
    Removing utf.o(i.utf16DecodeRune), (74 bytes).
    Removing utf.o(i.utf16EncodeRune), (64 bytes).
    Removing utf.o(i.utf16RuneCount), (60 bytes).
    Removing utf.o(i.utf16UTF8Count), (78 bytes).
    Removing utf.o(i.utf8DecodeRune), (174 bytes).
    Removing utf.o(i.utf8EncodeRune), (160 bytes).
    Removing utf.o(i.utf8RuneCount), (58 bytes).
    Removing utf.o(i.utf8UTF16Count), (76 bytes).
    Removing ble_advdata.o(.rev16_text), (4 bytes).
    Removing ble_advdata.o(.revsh_text), (4 bytes).
    Removing ble_advdata.o(.rrx_text), (6 bytes).
    Removing ble_advdata.o(i.ble_advdata_appearance_find), (52 bytes).
    Removing ble_advdata.o(i.ble_advdata_encode), (386 bytes).
    Removing ble_advdata.o(i.ble_advdata_name_find), (68 bytes).
    Removing ble_advdata.o(i.ble_advdata_parse), (32 bytes).
    Removing ble_advdata.o(i.ble_advdata_short_name_find), (74 bytes).
    Removing ble_advdata.o(i.ble_device_addr_encode), (96 bytes).
    Removing ble_advdata.o(i.conn_int_encode), (136 bytes).
    Removing ble_advdata.o(i.manuf_specific_data_encode), (100 bytes).
    Removing ble_advdata.o(i.name_encode), (166 bytes).
    Removing ble_advdata.o(i.service_data_encode), (136 bytes).
    Removing ble_advdata.o(i.uint16_encode), (10 bytes).
    Removing ble_advdata.o(i.uuid_list_encode), (48 bytes).
    Removing ble_advdata.o(i.uuid_list_sized_encode), (158 bytes).
    Removing ble_db_discovery.o(.rev16_text), (4 bytes).
    Removing ble_db_discovery.o(.revsh_text), (4 bytes).
    Removing ble_db_discovery.o(.rrx_text), (6 bytes).
    Removing ble_db_discovery.o(i.ble_db_discovery_close), (20 bytes).
    Removing ble_srv_common.o(.rev16_text), (4 bytes).
    Removing ble_srv_common.o(.revsh_text), (4 bytes).
    Removing ble_srv_common.o(.rrx_text), (6 bytes).
    Removing ble_srv_common.o(i.ble_srv_ascii_to_utf8), (18 bytes).
    Removing ble_srv_common.o(i.ble_srv_is_indication_enabled), (8 bytes).
    Removing ble_srv_common.o(i.ble_srv_is_notification_enabled), (8 bytes).
    Removing ble_srv_common.o(i.ble_srv_report_ref_encode), (12 bytes).
    Removing ble_srv_common.o(i.characteristic_add), (400 bytes).
    Removing ble_srv_common.o(i.descriptor_add), (170 bytes).
    Removing ble_srv_common.o(i.set_security_req), (48 bytes).
    Removing nrf_ble_gatt.o(.rev16_text), (4 bytes).
    Removing nrf_ble_gatt.o(.revsh_text), (4 bytes).
    Removing nrf_ble_gatt.o(.rrx_text), (6 bytes).
    Removing nrf_ble_gatt.o(i.nrf_ble_gatt_att_mtu_periph_set), (24 bytes).
    Removing nrf_ble_gatt.o(i.nrf_ble_gatt_data_length_get), (34 bytes).
    Removing nrf_ble_gatt.o(i.nrf_ble_gatt_data_length_set), (46 bytes).
    Removing nrf_ble_gatt.o(i.nrf_ble_gatt_eff_mtu_get), (12 bytes).
    Removing nrf_ble_gq.o(.rev16_text), (4 bytes).
    Removing nrf_ble_gq.o(.revsh_text), (4 bytes).
    Removing nrf_ble_gq.o(.rrx_text), (6 bytes).
    Removing nrf_ble_scan.o(.rev16_text), (4 bytes).
    Removing nrf_ble_scan.o(.revsh_text), (4 bytes).
    Removing nrf_ble_scan.o(.rrx_text), (6 bytes).
    Removing nrf_ble_scan.o(i.nrf_ble_scan_all_filter_remove), (10 bytes).
    Removing nrf_ble_scan.o(i.nrf_ble_scan_copy_addr_to_sd_gap_addr), (70 bytes).
    Removing nrf_ble_scan.o(i.nrf_ble_scan_filter_get), (20 bytes).
    Removing nrf_ble_scan.o(i.nrf_ble_scan_params_set), (40 bytes).
    Removing nrf_ble_scan.o(i.nrf_ble_scan_stop), (4 bytes).
    Removing ble_nus_c.o(.rev16_text), (4 bytes).
    Removing ble_nus_c.o(.revsh_text), (4 bytes).
    Removing ble_nus_c.o(.rrx_text), (6 bytes).
    Removing ble_nus_c.o(i.ble_nus_c_string_send), (104 bytes).
    Removing nrf_drv_clock.o(.rev16_text), (4 bytes).
    Removing nrf_drv_clock.o(.revsh_text), (4 bytes).
    Removing nrf_drv_clock.o(.rrx_text), (6 bytes).
    Removing nrf_drv_clock.o(i.item_enqueue), (22 bytes).
    Removing nrf_drv_clock.o(i.nrf_drv_clock_calibration_abort), (4 bytes).
    Removing nrf_drv_clock.o(i.nrf_drv_clock_calibration_start), (4 bytes).
    Removing nrf_drv_clock.o(i.nrf_drv_clock_hfclk_is_running), (44 bytes).
    Removing nrf_drv_clock.o(i.nrf_drv_clock_hfclk_release), (56 bytes).
    Removing nrf_drv_clock.o(i.nrf_drv_clock_hfclk_request), (92 bytes).
    Removing nrf_drv_clock.o(i.nrf_drv_clock_init_check), (12 bytes).
    Removing nrf_drv_clock.o(i.nrf_drv_clock_is_calibrating), (4 bytes).
    Removing nrf_drv_clock.o(i.nrf_drv_clock_lfclk_is_running), (28 bytes).
    Removing nrf_drv_clock.o(i.nrf_drv_clock_lfclk_request), (84 bytes).
    Removing nrf_drv_clock.o(i.nrf_drv_clock_uninit), (24 bytes).
    Removing nrf_drv_clock.o(.constdata), (19 bytes).
    Removing nrf_drv_clock.o(.constdata), (32 bytes).
    Removing nrf_drv_clock.o(.constdata), (32 bytes).
    Removing nrf_drv_clock.o(.constdata), (29 bytes).
    Removing nrf_drv_uart.o(.rev16_text), (4 bytes).
    Removing nrf_drv_uart.o(.revsh_text), (4 bytes).
    Removing nrf_drv_uart.o(.rrx_text), (6 bytes).
    Removing nrfx_atomic.o(.rev16_text), (4 bytes).
    Removing nrfx_atomic.o(.revsh_text), (4 bytes).
    Removing nrfx_atomic.o(.rrx_text), (6 bytes).
    Removing nrfx_atomic.o(.emb_text), (226 bytes).
    Removing nrfx_atomic.o(i.nrfx_atomic_flag_clear), (6 bytes).
    Removing nrfx_atomic.o(i.nrfx_atomic_flag_clear_fetch), (6 bytes).
    Removing nrfx_atomic.o(i.nrfx_atomic_flag_set), (6 bytes).
    Removing nrfx_atomic.o(i.nrfx_atomic_flag_set_fetch), (6 bytes).
    Removing nrfx_atomic.o(i.nrfx_atomic_u32_add), (12 bytes).
    Removing nrfx_atomic.o(i.nrfx_atomic_u32_and), (12 bytes).
    Removing nrfx_atomic.o(i.nrfx_atomic_u32_cmp_exch), (4 bytes).
    Removing nrfx_atomic.o(i.nrfx_atomic_u32_fetch_add), (10 bytes).
    Removing nrfx_atomic.o(i.nrfx_atomic_u32_fetch_and), (10 bytes).
    Removing nrfx_atomic.o(i.nrfx_atomic_u32_fetch_or), (10 bytes).
    Removing nrfx_atomic.o(i.nrfx_atomic_u32_fetch_store), (10 bytes).
    Removing nrfx_atomic.o(i.nrfx_atomic_u32_fetch_sub), (10 bytes).
    Removing nrfx_atomic.o(i.nrfx_atomic_u32_fetch_sub_hs), (10 bytes).
    Removing nrfx_atomic.o(i.nrfx_atomic_u32_fetch_xor), (10 bytes).
    Removing nrfx_atomic.o(i.nrfx_atomic_u32_or), (12 bytes).
    Removing nrfx_atomic.o(i.nrfx_atomic_u32_store), (12 bytes).
    Removing nrfx_atomic.o(i.nrfx_atomic_u32_sub), (12 bytes).
    Removing nrfx_atomic.o(i.nrfx_atomic_u32_sub_hs), (12 bytes).
    Removing nrfx_atomic.o(i.nrfx_atomic_u32_xor), (12 bytes).
    Removing nrfx_clock.o(.rev16_text), (4 bytes).
    Removing nrfx_clock.o(.revsh_text), (4 bytes).
    Removing nrfx_clock.o(.rrx_text), (6 bytes).
    Removing nrfx_clock.o(i.nrfx_clock_calibration_start), (4 bytes).
    Removing nrfx_clock.o(i.nrfx_clock_calibration_timer_start), (2 bytes).
    Removing nrfx_clock.o(i.nrfx_clock_calibration_timer_stop), (2 bytes).
    Removing nrfx_clock.o(i.nrfx_clock_disable), (30 bytes).
    Removing nrfx_clock.o(i.nrfx_clock_hfclk_start), (22 bytes).
    Removing nrfx_clock.o(i.nrfx_clock_hfclk_stop), (60 bytes).
    Removing nrfx_clock.o(i.nrfx_clock_is_calibrating), (4 bytes).
    Removing nrfx_clock.o(i.nrfx_clock_lfclk_start), (76 bytes).
    Removing nrfx_clock.o(i.nrfx_clock_uninit), (24 bytes).
    Removing nrfx_clock.o(.constdata), (16 bytes).
    Removing nrfx_clock.o(.constdata), (29 bytes).
    Removing nrfx_gpiote.o(.rev16_text), (4 bytes).
    Removing nrfx_gpiote.o(.revsh_text), (4 bytes).
    Removing nrfx_gpiote.o(.rrx_text), (6 bytes).
    Removing nrfx_gpiote.o(i.channel_free), (28 bytes).
    Removing nrfx_gpiote.o(i.channel_port_alloc), (72 bytes).
    Removing nrfx_gpiote.o(i.nrf_gpio_cfg), (36 bytes).
    Removing nrfx_gpiote.o(i.nrf_gpio_cfg_default), (18 bytes).
    Removing nrfx_gpiote.o(i.nrf_gpio_pin_present_check), (30 bytes).
    Removing nrfx_gpiote.o(i.nrfx_gpiote_clr_task_addr_get), (16 bytes).
    Removing nrfx_gpiote.o(i.nrfx_gpiote_clr_task_get), (16 bytes).
    Removing nrfx_gpiote.o(i.nrfx_gpiote_clr_task_trigger), (28 bytes).
    Removing nrfx_gpiote.o(i.nrfx_gpiote_in_event_addr_get), (16 bytes).
    Removing nrfx_gpiote.o(i.nrfx_gpiote_in_event_disable), (72 bytes).
    Removing nrfx_gpiote.o(i.nrfx_gpiote_in_event_enable), (160 bytes).
    Removing nrfx_gpiote.o(i.nrfx_gpiote_in_event_get), (34 bytes).
    Removing nrfx_gpiote.o(i.nrfx_gpiote_in_init), (204 bytes).
    Removing nrfx_gpiote.o(i.nrfx_gpiote_in_is_set), (20 bytes).
    Removing nrfx_gpiote.o(i.nrfx_gpiote_in_uninit), (88 bytes).
    Removing nrfx_gpiote.o(i.nrfx_gpiote_init), (116 bytes).
    Removing nrfx_gpiote.o(i.nrfx_gpiote_is_init), (20 bytes).
    Removing nrfx_gpiote.o(i.nrfx_gpiote_out_clear), (14 bytes).
    Removing nrfx_gpiote.o(i.nrfx_gpiote_out_init), (188 bytes).
    Removing nrfx_gpiote.o(i.nrfx_gpiote_out_set), (14 bytes).
    Removing nrfx_gpiote.o(i.nrfx_gpiote_out_task_addr_get), (16 bytes).
    Removing nrfx_gpiote.o(i.nrfx_gpiote_out_task_disable), (36 bytes).
    Removing nrfx_gpiote.o(i.nrfx_gpiote_out_task_enable), (36 bytes).
    Removing nrfx_gpiote.o(i.nrfx_gpiote_out_task_force), (40 bytes).
    Removing nrfx_gpiote.o(i.nrfx_gpiote_out_task_get), (12 bytes).
    Removing nrfx_gpiote.o(i.nrfx_gpiote_out_task_trigger), (24 bytes).
    Removing nrfx_gpiote.o(i.nrfx_gpiote_out_toggle), (28 bytes).
    Removing nrfx_gpiote.o(i.nrfx_gpiote_out_uninit), (88 bytes).
    Removing nrfx_gpiote.o(i.nrfx_gpiote_set_task_addr_get), (16 bytes).
    Removing nrfx_gpiote.o(i.nrfx_gpiote_set_task_get), (16 bytes).
    Removing nrfx_gpiote.o(i.nrfx_gpiote_set_task_trigger), (28 bytes).
    Removing nrfx_gpiote.o(i.nrfx_gpiote_uninit), (64 bytes).
    Removing nrfx_gpiote.o(i.pin_configured_check), (20 bytes).
    Removing nrfx_gpiote.o(i.pin_configured_clear), (28 bytes).
    Removing nrfx_gpiote.o(i.pin_configured_set), (28 bytes).
    Removing nrfx_gpiote.o(i.pin_in_use_by_port), (24 bytes).
    Removing nrfx_gpiote.o(i.pin_in_use_by_te), (24 bytes).
    Removing nrfx_gpiote.o(.constdata), (17 bytes).
    Removing nrfx_gpiote.o(.constdata), (21 bytes).
    Removing nrfx_gpiote.o(.constdata), (20 bytes).
    Removing nrfx_prs.o(.rev16_text), (4 bytes).
    Removing nrfx_prs.o(.revsh_text), (4 bytes).
    Removing nrfx_prs.o(.rrx_text), (6 bytes).
    Removing nrfx_prs.o(i.nrfx_prs_release), (18 bytes).
    Removing nrfx_prs.o(.constdata), (17 bytes).
    Removing nrfx_uart.o(.rev16_text), (4 bytes).
    Removing nrfx_uart.o(.revsh_text), (4 bytes).
    Removing nrfx_uart.o(.rrx_text), (6 bytes).
    Removing nrfx_uart.o(i.nrf_gpio_cfg_default), (18 bytes).
    Removing nrfx_uart.o(i.nrfx_uart_errorsrc_get), (26 bytes).
    Removing nrfx_uart.o(i.nrfx_uart_rx_abort), (18 bytes).
    Removing nrfx_uart.o(i.nrfx_uart_rx_disable), (36 bytes).
    Removing nrfx_uart.o(i.nrfx_uart_rx_enable), (60 bytes).
    Removing nrfx_uart.o(i.nrfx_uart_rx_ready), (14 bytes).
    Removing nrfx_uart.o(i.nrfx_uart_tx_abort), (48 bytes).
    Removing nrfx_uart.o(i.nrfx_uart_uninit), (192 bytes).
    Removing nrfx_uart.o(.constdata), (15 bytes).
    Removing nrfx_uart.o(.constdata), (13 bytes).
    Removing nrfx_uart.o(.constdata), (13 bytes).
    Removing nrfx_uarte.o(.rev16_text), (4 bytes).
    Removing nrfx_uarte.o(.revsh_text), (4 bytes).
    Removing nrfx_uarte.o(.rrx_text), (6 bytes).
    Removing nrfx_uarte.o(i.nrf_gpio_cfg_default), (18 bytes).
    Removing nrfx_uarte.o(i.nrfx_uarte_errorsrc_get), (26 bytes).
    Removing nrfx_uarte.o(i.nrfx_uarte_rx_abort), (44 bytes).
    Removing nrfx_uarte.o(i.nrfx_uarte_rx_ready), (14 bytes).
    Removing nrfx_uarte.o(i.nrfx_uarte_tx_abort), (56 bytes).
    Removing nrfx_uarte.o(i.nrfx_uarte_uninit), (264 bytes).
    Removing nrfx_uarte.o(.constdata), (16 bytes).
    Removing nrfx_uarte.o(.constdata), (14 bytes).
    Removing nrfx_uarte.o(.constdata), (14 bytes).
    Removing app_button.o(.rev16_text), (4 bytes).
    Removing app_button.o(.revsh_text), (4 bytes).
    Removing app_button.o(.rrx_text), (6 bytes).
    Removing app_button.o(i.app_button_disable), (72 bytes).
    Removing app_button.o(i.app_button_enable), (36 bytes).
    Removing app_button.o(i.app_button_init), (128 bytes).
    Removing app_button.o(i.app_button_is_pushed), (36 bytes).
    Removing app_button.o(i.button_get), (40 bytes).
    Removing app_button.o(i.detection_delay_timeout_handler), (72 bytes).
    Removing app_button.o(i.evt_handle), (224 bytes).
    Removing app_button.o(i.gpiote_event_handler), (60 bytes).
    Removing app_button.o(i.state_set), (48 bytes).
    Removing app_button.o(i.timer_start), (24 bytes).
    Removing app_button.o(i.usr_event), (32 bytes).
    Removing app_button.o(.bss), (32 bytes).
    Removing app_button.o(.bss), (16 bytes).
    Removing app_button.o(.constdata), (8 bytes).
    Removing app_button.o(.data), (24 bytes).
    Removing app_error.o(.rev16_text), (4 bytes).
    Removing app_error.o(.revsh_text), (4 bytes).
    Removing app_error.o(.rrx_text), (6 bytes).
    Removing app_error.o(i.app_error_save_and_stop), (100 bytes).
    Removing app_error.o(.bss), (32 bytes).
    Removing app_error_handler_keil.o(.rev16_text), (4 bytes).
    Removing app_error_handler_keil.o(.revsh_text), (4 bytes).
    Removing app_error_handler_keil.o(.rrx_text), (6 bytes).
    Removing app_error_handler_keil.o(.emb_text), (26 bytes).
    Removing app_error_weak.o(.rev16_text), (4 bytes).
    Removing app_error_weak.o(.revsh_text), (4 bytes).
    Removing app_error_weak.o(.rrx_text), (6 bytes).
    Removing app_fifo.o(.rev16_text), (4 bytes).
    Removing app_fifo.o(.revsh_text), (4 bytes).
    Removing app_fifo.o(.rrx_text), (6 bytes).
    Removing app_fifo.o(i.app_fifo_flush), (8 bytes).
    Removing app_fifo.o(i.app_fifo_peek), (34 bytes).
    Removing app_fifo.o(i.app_fifo_read), (74 bytes).
    Removing app_fifo.o(i.app_fifo_write), (82 bytes).
    Removing app_scheduler.o(.rev16_text), (4 bytes).
    Removing app_scheduler.o(.revsh_text), (4 bytes).
    Removing app_scheduler.o(.rrx_text), (6 bytes).
    Removing app_scheduler.o(i.app_sched_event_put), (160 bytes).
    Removing app_scheduler.o(i.app_sched_execute), (64 bytes).
    Removing app_scheduler.o(i.app_sched_init), (44 bytes).
    Removing app_scheduler.o(i.app_sched_queue_space_get), (36 bytes).
    Removing app_scheduler.o(.data), (16 bytes).
    Removing app_timer2.o(.rev16_text), (4 bytes).
    Removing app_timer2.o(.revsh_text), (4 bytes).
    Removing app_timer2.o(.rrx_text), (6 bytes).
    Removing app_timer2.o(i.app_timer_cnt_diff_compute), (8 bytes).
    Removing app_timer2.o(i.app_timer_create), (22 bytes).
    Removing app_timer2.o(i.app_timer_pause), (12 bytes).
    Removing app_timer2.o(i.app_timer_resume), (12 bytes).
    Removing app_timer2.o(i.app_timer_stop_all), (20 bytes).
    Removing app_uart_fifo.o(.rev16_text), (4 bytes).
    Removing app_uart_fifo.o(.revsh_text), (4 bytes).
    Removing app_uart_fifo.o(.rrx_text), (6 bytes).
    Removing app_uart_fifo.o(i.app_uart_close), (40 bytes).
    Removing app_uart_fifo.o(i.app_uart_flush), (32 bytes).
    Removing app_uart_fifo.o(i.app_uart_get), (52 bytes).
    Removing app_util_platform.o(.rev16_text), (4 bytes).
    Removing app_util_platform.o(.revsh_text), (4 bytes).
    Removing app_util_platform.o(.rrx_text), (6 bytes).
    Removing app_util_platform.o(i.app_util_disable_irq), (16 bytes).
    Removing app_util_platform.o(i.app_util_enable_irq), (20 bytes).
    Removing app_util_platform.o(i.current_int_priority_get), (48 bytes).
    Removing app_util_platform.o(i.privilege_level_get), (26 bytes).
    Removing app_util_platform.o(.data), (4 bytes).
    Removing drv_rtc.o(.rev16_text), (4 bytes).
    Removing drv_rtc.o(.revsh_text), (4 bytes).
    Removing drv_rtc.o(.rrx_text), (6 bytes).
    Removing drv_rtc.o(i.drv_rtc_compare_enable), (12 bytes).
    Removing drv_rtc.o(i.drv_rtc_compare_get), (14 bytes).
    Removing drv_rtc.o(i.drv_rtc_overflow_disable), (16 bytes).
    Removing drv_rtc.o(i.drv_rtc_tick_disable), (16 bytes).
    Removing drv_rtc.o(i.drv_rtc_tick_enable), (8 bytes).
    Removing drv_rtc.o(i.drv_rtc_tick_pending), (8 bytes).
    Removing drv_rtc.o(i.drv_rtc_uninit), (80 bytes).
    Removing hardfault_implementation.o(.rev16_text), (4 bytes).
    Removing hardfault_implementation.o(.revsh_text), (4 bytes).
    Removing hardfault_implementation.o(.rrx_text), (6 bytes).
    Removing nrf_assert.o(.rev16_text), (4 bytes).
    Removing nrf_assert.o(.revsh_text), (4 bytes).
    Removing nrf_assert.o(.rrx_text), (6 bytes).
    Removing nrf_assert.o(i.assert_nrf_callback), (20 bytes).
    Removing nrf_atfifo.o(.rev16_text), (4 bytes).
    Removing nrf_atfifo.o(.revsh_text), (4 bytes).
    Removing nrf_atfifo.o(.rrx_text), (6 bytes).
    Removing nrf_atfifo.o(i.nrf_atfifo_alloc_put), (46 bytes).
    Removing nrf_atfifo.o(i.nrf_atfifo_clear), (16 bytes).
    Removing nrf_atfifo.o(i.nrf_atfifo_get_free), (48 bytes).
    Removing nrf_atomic.o(.rev16_text), (4 bytes).
    Removing nrf_atomic.o(.revsh_text), (4 bytes).
    Removing nrf_atomic.o(.rrx_text), (6 bytes).
    Removing nrf_atomic.o(.emb_text), (226 bytes).
    Removing nrf_atomic.o(i.nrf_atomic_flag_clear), (6 bytes).
    Removing nrf_atomic.o(i.nrf_atomic_flag_clear_fetch), (6 bytes).
    Removing nrf_atomic.o(i.nrf_atomic_flag_set), (6 bytes).
    Removing nrf_atomic.o(i.nrf_atomic_flag_set_fetch), (6 bytes).
    Removing nrf_atomic.o(i.nrf_atomic_u32_add), (12 bytes).
    Removing nrf_atomic.o(i.nrf_atomic_u32_and), (12 bytes).
    Removing nrf_atomic.o(i.nrf_atomic_u32_cmp_exch), (4 bytes).
    Removing nrf_atomic.o(i.nrf_atomic_u32_fetch_add), (10 bytes).
    Removing nrf_atomic.o(i.nrf_atomic_u32_fetch_and), (10 bytes).
    Removing nrf_atomic.o(i.nrf_atomic_u32_fetch_or), (10 bytes).
    Removing nrf_atomic.o(i.nrf_atomic_u32_fetch_store), (10 bytes).
    Removing nrf_atomic.o(i.nrf_atomic_u32_fetch_sub), (10 bytes).
    Removing nrf_atomic.o(i.nrf_atomic_u32_fetch_sub_hs), (10 bytes).
    Removing nrf_atomic.o(i.nrf_atomic_u32_fetch_xor), (10 bytes).
    Removing nrf_atomic.o(i.nrf_atomic_u32_or), (12 bytes).
    Removing nrf_atomic.o(i.nrf_atomic_u32_store), (12 bytes).
    Removing nrf_atomic.o(i.nrf_atomic_u32_sub), (12 bytes).
    Removing nrf_atomic.o(i.nrf_atomic_u32_sub_hs), (12 bytes).
    Removing nrf_atomic.o(i.nrf_atomic_u32_xor), (12 bytes).
    Removing nrf_balloc.o(.rev16_text), (4 bytes).
    Removing nrf_balloc.o(.revsh_text), (4 bytes).
    Removing nrf_balloc.o(.rrx_text), (6 bytes).
    Removing nrf_fprintf.o(.rev16_text), (4 bytes).
    Removing nrf_fprintf.o(.revsh_text), (4 bytes).
    Removing nrf_fprintf.o(.rrx_text), (6 bytes).
    Removing nrf_fprintf.o(i.nrf_fprintf), (26 bytes).
    Removing nrf_fprintf.o(i.nrf_fprintf_buffer_flush), (24 bytes).
    Removing nrf_fprintf_format.o(.rev16_text), (4 bytes).
    Removing nrf_fprintf_format.o(.revsh_text), (4 bytes).
    Removing nrf_fprintf_format.o(.rrx_text), (6 bytes).
    Removing nrf_fprintf_format.o(i.buffer_add), (46 bytes).
    Removing nrf_fprintf_format.o(i.int_print), (166 bytes).
    Removing nrf_fprintf_format.o(i.nrf_fprintf_fmt), (474 bytes).
    Removing nrf_fprintf_format.o(i.unsigned_print), (180 bytes).
    Removing nrf_fprintf_format.o(.constdata), (16 bytes).
    Removing nrf_memobj.o(.rev16_text), (4 bytes).
    Removing nrf_memobj.o(.revsh_text), (4 bytes).
    Removing nrf_memobj.o(.rrx_text), (6 bytes).
    Removing nrf_memobj.o(i.nrf_memobj_get), (8 bytes).
    Removing nrf_memobj.o(i.nrf_memobj_put), (30 bytes).
    Removing nrf_pwr_mgmt.o(.rev16_text), (4 bytes).
    Removing nrf_pwr_mgmt.o(.revsh_text), (4 bytes).
    Removing nrf_pwr_mgmt.o(.rrx_text), (6 bytes).
    Removing nrf_pwr_mgmt.o(i.nrf_pwr_mgmt_feed), (2 bytes).
    Removing nrf_pwr_mgmt.o(i.nrf_pwr_mgmt_shutdown), (60 bytes).
    Removing nrf_pwr_mgmt.o(i.shutdown_process), (112 bytes).
    Removing nrf_queue.o(.rev16_text), (4 bytes).
    Removing nrf_queue.o(.revsh_text), (4 bytes).
    Removing nrf_queue.o(.rrx_text), (6 bytes).
    Removing nrf_queue.o(i.continous_items_get), (38 bytes).
    Removing nrf_queue.o(i.nrf_queue_available_get), (14 bytes).
    Removing nrf_queue.o(i.nrf_queue_in), (70 bytes).
    Removing nrf_queue.o(i.nrf_queue_max_utilization_get), (6 bytes).
    Removing nrf_queue.o(i.nrf_queue_max_utilization_reset), (8 bytes).
    Removing nrf_queue.o(i.nrf_queue_out), (58 bytes).
    Removing nrf_queue.o(i.nrf_queue_read), (62 bytes).
    Removing nrf_queue.o(i.nrf_queue_reset), (34 bytes).
    Removing nrf_queue.o(i.nrf_queue_utilization_get), (36 bytes).
    Removing nrf_queue.o(i.nrf_queue_write), (68 bytes).
    Removing nrf_queue.o(i.queue_read), (100 bytes).
    Removing nrf_queue.o(i.queue_write), (144 bytes).
    Removing nrf_ringbuf.o(.rev16_text), (4 bytes).
    Removing nrf_ringbuf.o(.revsh_text), (4 bytes).
    Removing nrf_ringbuf.o(.rrx_text), (6 bytes).
    Removing nrf_ringbuf.o(i.nrf_ringbuf_alloc), (112 bytes).
    Removing nrf_ringbuf.o(i.nrf_ringbuf_cpy_get), (124 bytes).
    Removing nrf_ringbuf.o(i.nrf_ringbuf_cpy_put), (114 bytes).
    Removing nrf_ringbuf.o(i.nrf_ringbuf_free), (38 bytes).
    Removing nrf_ringbuf.o(i.nrf_ringbuf_get), (112 bytes).
    Removing nrf_ringbuf.o(i.nrf_ringbuf_init), (28 bytes).
    Removing nrf_ringbuf.o(i.nrf_ringbuf_put), (48 bytes).
    Removing nrf_section_iter.o(.rev16_text), (4 bytes).
    Removing nrf_section_iter.o(.revsh_text), (4 bytes).
    Removing nrf_section_iter.o(.rrx_text), (6 bytes).
    Removing nrf_sortlist.o(.rev16_text), (4 bytes).
    Removing nrf_sortlist.o(.revsh_text), (4 bytes).
    Removing nrf_sortlist.o(.rrx_text), (6 bytes).
    Removing nrf_sortlist.o(i.nrf_sortlist_next), (4 bytes).
    Removing nrf_strerror.o(.rev16_text), (4 bytes).
    Removing nrf_strerror.o(.revsh_text), (4 bytes).
    Removing nrf_strerror.o(.rrx_text), (6 bytes).
    Removing nrf_strerror.o(i.nrf_strerror_find), (56 bytes).
    Removing nrf_strerror.o(i.nrf_strerror_get), (20 bytes).
    Removing nrf_strerror.o(.constdata), (316 bytes).
    Removing nrf_strerror.o(.conststring), (1001 bytes).
    Removing retarget.o(.rev16_text), (4 bytes).
    Removing retarget.o(.revsh_text), (4 bytes).
    Removing retarget.o(.rrx_text), (6 bytes).
    Removing retarget.o(i.fgetc), (18 bytes).
    Removing retarget.o(i.fputc), (14 bytes).
    Removing retarget.o(.data), (4 bytes).
    Removing retarget.o(.data), (4 bytes).
    Removing nrf_log_backend_rtt.o(.rev16_text), (4 bytes).
    Removing nrf_log_backend_rtt.o(.revsh_text), (4 bytes).
    Removing nrf_log_backend_rtt.o(.rrx_text), (6 bytes).
    Removing nrf_log_backend_serial.o(.rev16_text), (4 bytes).
    Removing nrf_log_backend_serial.o(.revsh_text), (4 bytes).
    Removing nrf_log_backend_serial.o(.rrx_text), (6 bytes).
    Removing nrf_log_default_backends.o(.rev16_text), (4 bytes).
    Removing nrf_log_default_backends.o(.revsh_text), (4 bytes).
    Removing nrf_log_default_backends.o(.rrx_text), (6 bytes).
    Removing nrf_log_frontend.o(.rev16_text), (4 bytes).
    Removing nrf_log_frontend.o(.revsh_text), (4 bytes).
    Removing nrf_log_frontend.o(.rrx_text), (6 bytes).
    Removing nrf_log_str_formatter.o(.rev16_text), (4 bytes).
    Removing nrf_log_str_formatter.o(.revsh_text), (4 bytes).
    Removing nrf_log_str_formatter.o(.rrx_text), (6 bytes).
    Removing segger_rtt.o(.rev16_text), (4 bytes).
    Removing segger_rtt.o(.revsh_text), (4 bytes).
    Removing segger_rtt.o(.rrx_text), (6 bytes).
    Removing segger_rtt.o(i.SEGGER_RTT_AllocDownBuffer), (112 bytes).
    Removing segger_rtt.o(i.SEGGER_RTT_AllocUpBuffer), (112 bytes).
    Removing segger_rtt.o(i.SEGGER_RTT_ConfigDownBuffer), (96 bytes).
    Removing segger_rtt.o(i.SEGGER_RTT_ConfigUpBuffer), (96 bytes).
    Removing segger_rtt.o(i.SEGGER_RTT_GetKey), (28 bytes).
    Removing segger_rtt.o(i.SEGGER_RTT_HasData), (24 bytes).
    Removing segger_rtt.o(i.SEGGER_RTT_HasKey), (32 bytes).
    Removing segger_rtt.o(i.SEGGER_RTT_Init), (4 bytes).
    Removing segger_rtt.o(i.SEGGER_RTT_PutChar), (96 bytes).
    Removing segger_rtt.o(i.SEGGER_RTT_PutCharSkip), (84 bytes).
    Removing segger_rtt.o(i.SEGGER_RTT_PutCharSkipNoLock), (52 bytes).
    Removing segger_rtt.o(i.SEGGER_RTT_Read), (44 bytes).
    Removing segger_rtt.o(i.SEGGER_RTT_ReadNoLock), (120 bytes).
    Removing segger_rtt.o(i.SEGGER_RTT_SetFlagsDownBuffer), (68 bytes).
    Removing segger_rtt.o(i.SEGGER_RTT_SetFlagsUpBuffer), (68 bytes).
    Removing segger_rtt.o(i.SEGGER_RTT_SetNameDownBuffer), (68 bytes).
    Removing segger_rtt.o(i.SEGGER_RTT_SetNameUpBuffer), (68 bytes).
    Removing segger_rtt.o(i.SEGGER_RTT_SetTerminal), (136 bytes).
    Removing segger_rtt.o(i.SEGGER_RTT_TerminalOut), (176 bytes).
    Removing segger_rtt.o(i.SEGGER_RTT_WaitKey), (14 bytes).
    Removing segger_rtt.o(i.SEGGER_RTT_Write), (60 bytes).
    Removing segger_rtt.o(i.SEGGER_RTT_WriteNoLock), (92 bytes).
    Removing segger_rtt.o(i.SEGGER_RTT_WriteSkipNoLock), (116 bytes).
    Removing segger_rtt.o(i.SEGGER_RTT_WriteString), (26 bytes).
    Removing segger_rtt.o(i.SEGGER_RTT_WriteWithOverwriteNoLock), (148 bytes).
    Removing segger_rtt.o(i._DoInit), (104 bytes).
    Removing segger_rtt.o(i._GetAvailWriteSpace), (22 bytes).
    Removing segger_rtt.o(i._PostTerminalSwitch), (32 bytes).
    Removing segger_rtt.o(i._WriteBlocking), (90 bytes).
    Removing segger_rtt.o(i._WriteNoCheck), (66 bytes).
    Removing segger_rtt.o(.bss), (648 bytes).
    Removing segger_rtt.o(.data), (17 bytes).
    Removing segger_rtt_printf.o(.rev16_text), (4 bytes).
    Removing segger_rtt_printf.o(.revsh_text), (4 bytes).
    Removing segger_rtt_printf.o(.rrx_text), (6 bytes).
    Removing segger_rtt_printf.o(i.SEGGER_RTT_printf), (22 bytes).
    Removing segger_rtt_printf.o(i.SEGGER_RTT_vprintf), (406 bytes).
    Removing segger_rtt_printf.o(i._PrintInt), (198 bytes).
    Removing segger_rtt_printf.o(i._PrintUnsigned), (212 bytes).
    Removing segger_rtt_printf.o(i._StoreChar), (62 bytes).
    Removing segger_rtt_printf.o(.constdata), (16 bytes).
    Removing nrf_sdh.o(.rev16_text), (4 bytes).
    Removing nrf_sdh.o(.revsh_text), (4 bytes).
    Removing nrf_sdh.o(.rrx_text), (6 bytes).
    Removing nrf_sdh.o(i.nrf_sdh_disable_request), (84 bytes).
    Removing nrf_sdh.o(i.nrf_sdh_is_suspended), (20 bytes).
    Removing nrf_sdh.o(i.nrf_sdh_request_continue), (28 bytes).
    Removing nrf_sdh.o(i.nrf_sdh_resume), (56 bytes).
    Removing nrf_sdh.o(i.nrf_sdh_suspend), (24 bytes).
    Removing nrf_sdh.o(i.softdevice_evt_irq_disable), (64 bytes).
    Removing nrf_sdh_ble.o(.rev16_text), (4 bytes).
    Removing nrf_sdh_ble.o(.revsh_text), (4 bytes).
    Removing nrf_sdh_ble.o(.rrx_text), (6 bytes).
    Removing nrf_sdh_soc.o(.rev16_text), (4 bytes).
    Removing nrf_sdh_soc.o(.revsh_text), (4 bytes).
    Removing nrf_sdh_soc.o(.rrx_text), (6 bytes).
    Removing arm_startup_nrf52.o(HEAP), (8192 bytes).
    Removing system_nrf52.o(.rev16_text), (4 bytes).
    Removing system_nrf52.o(.revsh_text), (4 bytes).
    Removing system_nrf52.o(.rrx_text), (6 bytes).
    Removing system_nrf52.o(i.SystemCoreClockUpdate), (16 bytes).

485 unused section(s) (total 27083 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/../cmprslib/zerorunl.c           0x00000000   Number         0  __dczerorl.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uldiv.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry12b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry2.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry5.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry12a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llushr.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llshl.o ABSOLUTE
    ../clib/microlib/string/memcmp.c         0x00000000   Number         0  memcmp.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpyb.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpya.o ABSOLUTE
    ../clib/microlib/string/memset.c         0x00000000   Number         0  memseta.o ABSOLUTE
    ../clib/microlib/string/strcpy.c         0x00000000   Number         0  strcpy.o ABSOLUTE
    ../clib/microlib/string/strlen.c         0x00000000   Number         0  strlen.o ABSOLUTE
    ..\..\..\..\..\..\components\ble\ble_db_discovery\ble_db_discovery.c 0x00000000   Number         0  ble_db_discovery.o ABSOLUTE
    ..\..\..\..\..\..\components\ble\ble_services\ble_nus_c\ble_nus_c.c 0x00000000   Number         0  ble_nus_c.o ABSOLUTE
    ..\..\..\..\..\..\components\ble\common\ble_advdata.c 0x00000000   Number         0  ble_advdata.o ABSOLUTE
    ..\..\..\..\..\..\components\ble\common\ble_srv_common.c 0x00000000   Number         0  ble_srv_common.o ABSOLUTE
    ..\..\..\..\..\..\components\ble\nrf_ble_gatt\nrf_ble_gatt.c 0x00000000   Number         0  nrf_ble_gatt.o ABSOLUTE
    ..\..\..\..\..\..\components\ble\nrf_ble_gq\nrf_ble_gq.c 0x00000000   Number         0  nrf_ble_gq.o ABSOLUTE
    ..\..\..\..\..\..\components\ble\nrf_ble_scan\nrf_ble_scan.c 0x00000000   Number         0  nrf_ble_scan.o ABSOLUTE
    ..\..\..\..\..\..\components\boards\boards.c 0x00000000   Number         0  boards.o ABSOLUTE
    ..\..\..\..\..\..\components\libraries\atomic\nrf_atomic.c 0x00000000   Number         0  nrf_atomic.o ABSOLUTE
    ..\..\..\..\..\..\components\libraries\atomic_fifo\nrf_atfifo.c 0x00000000   Number         0  nrf_atfifo.o ABSOLUTE
    ..\..\..\..\..\..\components\libraries\balloc\nrf_balloc.c 0x00000000   Number         0  nrf_balloc.o ABSOLUTE
    ..\..\..\..\..\..\components\libraries\bsp\bsp.c 0x00000000   Number         0  bsp.o ABSOLUTE
    ..\..\..\..\..\..\components\libraries\bsp\bsp_btn_ble.c 0x00000000   Number         0  bsp_btn_ble.o ABSOLUTE
    ..\..\..\..\..\..\components\libraries\button\app_button.c 0x00000000   Number         0  app_button.o ABSOLUTE
    ..\..\..\..\..\..\components\libraries\experimental_section_vars\nrf_section_iter.c 0x00000000   Number         0  nrf_section_iter.o ABSOLUTE
    ..\..\..\..\..\..\components\libraries\fifo\app_fifo.c 0x00000000   Number         0  app_fifo.o ABSOLUTE
    ..\..\..\..\..\..\components\libraries\hardfault\hardfault_implementation.c 0x00000000   Number         0  hardfault_implementation.o ABSOLUTE
    ..\..\..\..\..\..\components\libraries\log\src\nrf_log_backend_rtt.c 0x00000000   Number         0  nrf_log_backend_rtt.o ABSOLUTE
    ..\..\..\..\..\..\components\libraries\log\src\nrf_log_backend_serial.c 0x00000000   Number         0  nrf_log_backend_serial.o ABSOLUTE
    ..\..\..\..\..\..\components\libraries\log\src\nrf_log_default_backends.c 0x00000000   Number         0  nrf_log_default_backends.o ABSOLUTE
    ..\..\..\..\..\..\components\libraries\log\src\nrf_log_frontend.c 0x00000000   Number         0  nrf_log_frontend.o ABSOLUTE
    ..\..\..\..\..\..\components\libraries\log\src\nrf_log_str_formatter.c 0x00000000   Number         0  nrf_log_str_formatter.o ABSOLUTE
    ..\..\..\..\..\..\components\libraries\memobj\nrf_memobj.c 0x00000000   Number         0  nrf_memobj.o ABSOLUTE
    ..\..\..\..\..\..\components\libraries\pwr_mgmt\nrf_pwr_mgmt.c 0x00000000   Number         0  nrf_pwr_mgmt.o ABSOLUTE
    ..\..\..\..\..\..\components\libraries\queue\nrf_queue.c 0x00000000   Number         0  nrf_queue.o ABSOLUTE
    ..\..\..\..\..\..\components\libraries\ringbuf\nrf_ringbuf.c 0x00000000   Number         0  nrf_ringbuf.o ABSOLUTE
    ..\..\..\..\..\..\components\libraries\scheduler\app_scheduler.c 0x00000000   Number         0  app_scheduler.o ABSOLUTE
    ..\..\..\..\..\..\components\libraries\sortlist\nrf_sortlist.c 0x00000000   Number         0  nrf_sortlist.o ABSOLUTE
    ..\..\..\..\..\..\components\libraries\strerror\nrf_strerror.c 0x00000000   Number         0  nrf_strerror.o ABSOLUTE
    ..\..\..\..\..\..\components\libraries\timer\app_timer2.c 0x00000000   Number         0  app_timer2.o ABSOLUTE
    ..\..\..\..\..\..\components\libraries\timer\drv_rtc.c 0x00000000   Number         0  drv_rtc.o ABSOLUTE
    ..\..\..\..\..\..\components\libraries\uart\app_uart_fifo.c 0x00000000   Number         0  app_uart_fifo.o ABSOLUTE
    ..\..\..\..\..\..\components\libraries\uart\retarget.c 0x00000000   Number         0  retarget.o ABSOLUTE
    ..\..\..\..\..\..\components\libraries\util\app_error.c 0x00000000   Number         0  app_error.o ABSOLUTE
    ..\..\..\..\..\..\components\libraries\util\app_error_handler_keil.c 0x00000000   Number         0  app_error_handler_keil.o ABSOLUTE
    ..\..\..\..\..\..\components\libraries\util\app_error_weak.c 0x00000000   Number         0  app_error_weak.o ABSOLUTE
    ..\..\..\..\..\..\components\libraries\util\app_util_platform.c 0x00000000   Number         0  app_util_platform.o ABSOLUTE
    ..\..\..\..\..\..\components\libraries\util\nrf_assert.c 0x00000000   Number         0  nrf_assert.o ABSOLUTE
    ..\..\..\..\..\..\components\softdevice\common\nrf_sdh.c 0x00000000   Number         0  nrf_sdh.o ABSOLUTE
    ..\..\..\..\..\..\components\softdevice\common\nrf_sdh_ble.c 0x00000000   Number         0  nrf_sdh_ble.o ABSOLUTE
    ..\..\..\..\..\..\components\softdevice\common\nrf_sdh_soc.c 0x00000000   Number         0  nrf_sdh_soc.o ABSOLUTE
    ..\..\..\..\..\..\external\fprintf\nrf_fprintf.c 0x00000000   Number         0  nrf_fprintf.o ABSOLUTE
    ..\..\..\..\..\..\external\fprintf\nrf_fprintf_format.c 0x00000000   Number         0  nrf_fprintf_format.o ABSOLUTE
    ..\..\..\..\..\..\external\segger_rtt\SEGGER_RTT.c 0x00000000   Number         0  segger_rtt.o ABSOLUTE
    ..\..\..\..\..\..\external\segger_rtt\SEGGER_RTT_Syscalls_KEIL.c 0x00000000   Number         0  segger_rtt_syscalls_keil.o ABSOLUTE
    ..\..\..\..\..\..\external\segger_rtt\SEGGER_RTT_printf.c 0x00000000   Number         0  segger_rtt_printf.o ABSOLUTE
    ..\..\..\..\..\..\external\utf_converter\utf.c 0x00000000   Number         0  utf.o ABSOLUTE
    ..\..\..\..\..\..\integration\nrfx\legacy\nrf_drv_clock.c 0x00000000   Number         0  nrf_drv_clock.o ABSOLUTE
    ..\..\..\..\..\..\integration\nrfx\legacy\nrf_drv_uart.c 0x00000000   Number         0  nrf_drv_uart.o ABSOLUTE
    ..\..\..\..\..\..\modules\nrfx\drivers\src\nrfx_clock.c 0x00000000   Number         0  nrfx_clock.o ABSOLUTE
    ..\..\..\..\..\..\modules\nrfx\drivers\src\nrfx_gpiote.c 0x00000000   Number         0  nrfx_gpiote.o ABSOLUTE
    ..\..\..\..\..\..\modules\nrfx\drivers\src\nrfx_uart.c 0x00000000   Number         0  nrfx_uart.o ABSOLUTE
    ..\..\..\..\..\..\modules\nrfx\drivers\src\nrfx_uarte.c 0x00000000   Number         0  nrfx_uarte.o ABSOLUTE
    ..\..\..\..\..\..\modules\nrfx\drivers\src\prs\nrfx_prs.c 0x00000000   Number         0  nrfx_prs.o ABSOLUTE
    ..\..\..\..\..\..\modules\nrfx\soc\nrfx_atomic.c 0x00000000   Number         0  nrfx_atomic.o ABSOLUTE
    ..\..\..\main.c                          0x00000000   Number         0  main.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\ble\\ble_db_discovery\\ble_db_discovery.c 0x00000000   Number         0  ble_db_discovery.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\ble\\ble_services\\ble_nus_c\\ble_nus_c.c 0x00000000   Number         0  ble_nus_c.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\ble\\common\\ble_advdata.c 0x00000000   Number         0  ble_advdata.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\ble\\common\\ble_srv_common.c 0x00000000   Number         0  ble_srv_common.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\ble\\nrf_ble_gatt\\nrf_ble_gatt.c 0x00000000   Number         0  nrf_ble_gatt.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\ble\\nrf_ble_gq\\nrf_ble_gq.c 0x00000000   Number         0  nrf_ble_gq.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\ble\\nrf_ble_scan\\nrf_ble_scan.c 0x00000000   Number         0  nrf_ble_scan.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\boards\\boards.c 0x00000000   Number         0  boards.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\libraries\\atomic\\nrf_atomic.c 0x00000000   Number         0  nrf_atomic.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\libraries\\atomic_fifo\\nrf_atfifo.c 0x00000000   Number         0  nrf_atfifo.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\libraries\\balloc\\nrf_balloc.c 0x00000000   Number         0  nrf_balloc.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\libraries\\bsp\\bsp.c 0x00000000   Number         0  bsp.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\libraries\\bsp\\bsp_btn_ble.c 0x00000000   Number         0  bsp_btn_ble.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\libraries\\button\\app_button.c 0x00000000   Number         0  app_button.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\libraries\\experimental_section_vars\\nrf_section_iter.c 0x00000000   Number         0  nrf_section_iter.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\libraries\\fifo\\app_fifo.c 0x00000000   Number         0  app_fifo.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\libraries\\hardfault\\hardfault_implementation.c 0x00000000   Number         0  hardfault_implementation.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\libraries\\log\\src\\nrf_log_backend_rtt.c 0x00000000   Number         0  nrf_log_backend_rtt.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\libraries\\log\\src\\nrf_log_backend_serial.c 0x00000000   Number         0  nrf_log_backend_serial.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\libraries\\log\\src\\nrf_log_default_backends.c 0x00000000   Number         0  nrf_log_default_backends.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\libraries\\log\\src\\nrf_log_frontend.c 0x00000000   Number         0  nrf_log_frontend.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\libraries\\log\\src\\nrf_log_str_formatter.c 0x00000000   Number         0  nrf_log_str_formatter.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\libraries\\memobj\\nrf_memobj.c 0x00000000   Number         0  nrf_memobj.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\libraries\\pwr_mgmt\\nrf_pwr_mgmt.c 0x00000000   Number         0  nrf_pwr_mgmt.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\libraries\\queue\\nrf_queue.c 0x00000000   Number         0  nrf_queue.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\libraries\\ringbuf\\nrf_ringbuf.c 0x00000000   Number         0  nrf_ringbuf.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\libraries\\scheduler\\app_scheduler.c 0x00000000   Number         0  app_scheduler.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\libraries\\sortlist\\nrf_sortlist.c 0x00000000   Number         0  nrf_sortlist.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\libraries\\strerror\\nrf_strerror.c 0x00000000   Number         0  nrf_strerror.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\libraries\\timer\\app_timer2.c 0x00000000   Number         0  app_timer2.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\libraries\\timer\\drv_rtc.c 0x00000000   Number         0  drv_rtc.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\libraries\\uart\\app_uart_fifo.c 0x00000000   Number         0  app_uart_fifo.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\libraries\\uart\\retarget.c 0x00000000   Number         0  retarget.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\libraries\\util\\app_error.c 0x00000000   Number         0  app_error.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\libraries\\util\\app_error_handler_keil.c 0x00000000   Number         0  app_error_handler_keil.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\libraries\\util\\app_error_weak.c 0x00000000   Number         0  app_error_weak.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\libraries\\util\\app_util_platform.c 0x00000000   Number         0  app_util_platform.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\libraries\\util\\nrf_assert.c 0x00000000   Number         0  nrf_assert.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\softdevice\\common\\nrf_sdh.c 0x00000000   Number         0  nrf_sdh.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\softdevice\\common\\nrf_sdh_ble.c 0x00000000   Number         0  nrf_sdh_ble.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\softdevice\\common\\nrf_sdh_soc.c 0x00000000   Number         0  nrf_sdh_soc.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\external\\fprintf\\nrf_fprintf.c 0x00000000   Number         0  nrf_fprintf.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\external\\fprintf\\nrf_fprintf_format.c 0x00000000   Number         0  nrf_fprintf_format.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\external\\segger_rtt\\SEGGER_RTT.c 0x00000000   Number         0  segger_rtt.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\external\\segger_rtt\\SEGGER_RTT_printf.c 0x00000000   Number         0  segger_rtt_printf.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\integration\\nrfx\\legacy\\nrf_drv_clock.c 0x00000000   Number         0  nrf_drv_clock.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\integration\\nrfx\\legacy\\nrf_drv_uart.c 0x00000000   Number         0  nrf_drv_uart.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\modules\\nrfx\\drivers\\src\\nrfx_clock.c 0x00000000   Number         0  nrfx_clock.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\modules\\nrfx\\drivers\\src\\nrfx_gpiote.c 0x00000000   Number         0  nrfx_gpiote.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\modules\\nrfx\\drivers\\src\\nrfx_uart.c 0x00000000   Number         0  nrfx_uart.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\modules\\nrfx\\drivers\\src\\nrfx_uarte.c 0x00000000   Number         0  nrfx_uarte.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\modules\\nrfx\\drivers\\src\\prs\\nrfx_prs.c 0x00000000   Number         0  nrfx_prs.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\modules\\nrfx\\soc\\nrfx_atomic.c 0x00000000   Number         0  nrfx_atomic.o ABSOLUTE
    ..\\..\\..\\main.c                       0x00000000   Number         0  main.o ABSOLUTE
    RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s 0x00000000   Number         0  arm_startup_nrf52.o ABSOLUTE
    RTE\Device\nRF52832_xxAA\system_nrf52.c  0x00000000   Number         0  system_nrf52.o ABSOLUTE
    RTE\\Device\\nRF52832_xxAA\\system_nrf52.c 0x00000000   Number         0  system_nrf52.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    handlers.s                               0x00000000   Number         0  handlers.o ABSOLUTE
    init.s                                   0x00000000   Number         0  init.o ABSOLUTE
    RESET                                    0x00026000   Section      512  arm_startup_nrf52.o(RESET)
    .ARM.Collect$$$$00000000                 0x00026200   Section        0  entry.o(.ARM.Collect$$$$00000000)
    .ARM.Collect$$$$00000001                 0x00026200   Section        4  entry2.o(.ARM.Collect$$$$00000001)
    .ARM.Collect$$$$00000004                 0x00026204   Section        4  entry5.o(.ARM.Collect$$$$00000004)
    .ARM.Collect$$$$00000008                 0x00026208   Section        0  entry7b.o(.ARM.Collect$$$$00000008)
    .ARM.Collect$$$$0000000A                 0x00026208   Section        0  entry8b.o(.ARM.Collect$$$$0000000A)
    .ARM.Collect$$$$0000000B                 0x00026208   Section        8  entry9a.o(.ARM.Collect$$$$0000000B)
    .ARM.Collect$$$$0000000E                 0x00026210   Section        4  entry12b.o(.ARM.Collect$$$$0000000E)
    .ARM.Collect$$$$0000000F                 0x00026214   Section        0  entry10a.o(.ARM.Collect$$$$0000000F)
    .ARM.Collect$$$$00000011                 0x00026214   Section        0  entry11a.o(.ARM.Collect$$$$00000011)
    .ARM.Collect$$$$00002712                 0x00026214   Section        4  entry2.o(.ARM.Collect$$$$00002712)
    __lit__00000000                          0x00026214   Data           4  entry2.o(.ARM.Collect$$$$00002712)
    .emb_text                                0x00026218   Section      200  nrf_atfifo.o(.emb_text)
    $v0                                      0x00026218   Number         0  nrf_atfifo.o(.emb_text)
    .text                                    0x000262e0   Section       36  arm_startup_nrf52.o(.text)
    $v0                                      0x000262e0   Number         0  arm_startup_nrf52.o(.text)
    .text                                    0x00026304   Section        0  uldiv.o(.text)
    .text                                    0x00026366   Section        0  llshl.o(.text)
    .text                                    0x00026384   Section        0  memcpya.o(.text)
    .text                                    0x000263a8   Section        0  memseta.o(.text)
    .text                                    0x000263cc   Section        0  memcmp.o(.text)
    .text                                    0x000263e6   Section        0  llushr.o(.text)
    .text                                    0x00026408   Section       36  init.o(.text)
    .text                                    0x0002642c   Section        0  __dczerorl.o(.text)
    i.GPIOTE_IRQHandler                      0x00026468   Section        0  nrfx_gpiote.o(i.GPIOTE_IRQHandler)
    i.POWER_CLOCK_IRQHandler                 0x0002650c   Section        0  nrfx_clock.o(i.POWER_CLOCK_IRQHandler)
    i.RTC1_IRQHandler                        0x00026564   Section        0  drv_rtc.o(i.RTC1_IRQHandler)
    i.SWI2_EGU2_IRQHandler                   0x00026574   Section        0  nrf_sdh.o(i.SWI2_EGU2_IRQHandler)
    i.SystemInit                             0x00026578   Section        0  system_nrf52.o(i.SystemInit)
    i.UARTE0_UART0_IRQHandler                0x00026894   Section        0  nrfx_prs.o(i.UARTE0_UART0_IRQHandler)
    i.__scatterload_copy                     0x000268a0   Section       14  handlers.o(i.__scatterload_copy)
    i.__scatterload_null                     0x000268ae   Section        2  handlers.o(i.__scatterload_null)
    i.__scatterload_zeroinit                 0x000268b0   Section       14  handlers.o(i.__scatterload_zeroinit)
    i.__sd_nvic_app_accessible_irq           0x000268c0   Section        0  nrf_sdh.o(i.__sd_nvic_app_accessible_irq)
    __sd_nvic_app_accessible_irq             0x000268c1   Thumb Code    32  nrf_sdh.o(i.__sd_nvic_app_accessible_irq)
    i.advertising_buttons_configure          0x000268e4   Section        0  bsp_btn_ble.o(i.advertising_buttons_configure)
    advertising_buttons_configure            0x000268e5   Thumb Code    54  bsp_btn_ble.o(i.advertising_buttons_configure)
    i.app_error_fault_handler                0x0002691c   Section        0  app_error_weak.o(i.app_error_fault_handler)
    i.app_error_handler_bare                 0x0002694c   Section        0  app_error.o(i.app_error_handler_bare)
    i.app_fifo_get                           0x00026962   Section        0  app_fifo.o(i.app_fifo_get)
    i.app_fifo_init                          0x00026978   Section        0  app_fifo.o(i.app_fifo_init)
    i.app_fifo_put                           0x00026998   Section        0  app_fifo.o(i.app_fifo_put)
    i.app_timer_cnt_get                      0x000269b4   Section        0  app_timer2.o(i.app_timer_cnt_get)
    i.app_timer_init                         0x000269c0   Section        0  app_timer2.o(i.app_timer_init)
    i.app_timer_start                        0x00026a1c   Section        0  app_timer2.o(i.app_timer_start)
    i.app_timer_stop                         0x00026a4c   Section        0  app_timer2.o(i.app_timer_stop)
    i.app_uart_init                          0x00026a58   Section        0  app_uart_fifo.o(i.app_uart_init)
    i.app_uart_put                           0x00026afc   Section        0  app_uart_fifo.o(i.app_uart_put)
    i.app_util_critical_region_enter         0x00026b50   Section        0  app_util_platform.o(i.app_util_critical_region_enter)
    i.app_util_critical_region_exit          0x00026b98   Section        0  app_util_platform.o(i.app_util_critical_region_exit)
    i.apply_config                           0x00026bcc   Section        0  nrfx_uart.o(i.apply_config)
    apply_config                             0x00026bcd   Thumb Code   136  nrfx_uart.o(i.apply_config)
    i.apply_config                           0x00026c54   Section        0  nrfx_uarte.o(i.apply_config)
    apply_config                             0x00026c55   Thumb Code   136  nrfx_uarte.o(i.apply_config)
    i.ble_advdata_search                     0x00026cdc   Section        0  ble_advdata.o(i.ble_advdata_search)
    i.ble_advdata_uuid_find                  0x00026d22   Section        0  ble_advdata.o(i.ble_advdata_uuid_find)
    i.ble_db_discovery_evt_register          0x00026dd8   Section        0  ble_db_discovery.o(i.ble_db_discovery_evt_register)
    i.ble_db_discovery_init                  0x00026e18   Section        0  ble_db_discovery.o(i.ble_db_discovery_init)
    i.ble_db_discovery_on_ble_evt            0x00026e3c   Section        0  ble_db_discovery.o(i.ble_db_discovery_on_ble_evt)
    i.ble_db_discovery_start                 0x00026e98   Section        0  ble_db_discovery.o(i.ble_db_discovery_start)
    i.ble_evt_handler                        0x00026ec0   Section        0  main.o(i.ble_evt_handler)
    ble_evt_handler                          0x00026ec1   Thumb Code   144  main.o(i.ble_evt_handler)
    i.ble_evt_handler                        0x00026f5c   Section        0  bsp_btn_ble.o(i.ble_evt_handler)
    ble_evt_handler                          0x00026f5d   Thumb Code   106  bsp_btn_ble.o(i.ble_evt_handler)
    i.ble_nus_c_evt_handler                  0x00026fcc   Section        0  main.o(i.ble_nus_c_evt_handler)
    ble_nus_c_evt_handler                    0x00026fcd   Thumb Code    86  main.o(i.ble_nus_c_evt_handler)
    i.ble_nus_c_handles_assign               0x0002702c   Section        0  ble_nus_c.o(i.ble_nus_c_handles_assign)
    i.ble_nus_c_init                         0x00027048   Section        0  ble_nus_c.o(i.ble_nus_c_init)
    i.ble_nus_c_on_ble_evt                   0x000270a0   Section        0  ble_nus_c.o(i.ble_nus_c_on_ble_evt)
    i.ble_nus_c_on_db_disc_evt               0x0002710a   Section        0  ble_nus_c.o(i.ble_nus_c_on_db_disc_evt)
    i.ble_nus_c_tx_notif_enable              0x0002718c   Section        0  ble_nus_c.o(i.ble_nus_c_tx_notif_enable)
    i.bsp_board_button_idx_to_pin            0x000271f8   Section        0  boards.o(i.bsp_board_button_idx_to_pin)
    i.bsp_board_led_invert                   0x00027204   Section        0  boards.o(i.bsp_board_led_invert)
    i.bsp_board_led_off                      0x00027228   Section        0  boards.o(i.bsp_board_led_off)
    i.bsp_board_led_on                       0x00027238   Section        0  boards.o(i.bsp_board_led_on)
    i.bsp_board_led_state_get                0x00027248   Section        0  boards.o(i.bsp_board_led_state_get)
    i.bsp_board_leds_off                     0x00027268   Section        0  boards.o(i.bsp_board_leds_off)
    i.bsp_board_leds_on                      0x0002727a   Section        0  boards.o(i.bsp_board_leds_on)
    i.bsp_board_pin_to_button_idx            0x0002728c   Section        0  boards.o(i.bsp_board_pin_to_button_idx)
    i.bsp_btn_ble_sleep_mode_prepare         0x000272b0   Section        0  bsp_btn_ble.o(i.bsp_btn_ble_sleep_mode_prepare)
    i.bsp_button_event_handler               0x000272d0   Section        0  bsp.o(i.bsp_button_event_handler)
    bsp_button_event_handler                 0x000272d1   Thumb Code   132  bsp.o(i.bsp_button_event_handler)
    i.bsp_event_to_button_action_assign      0x00027360   Section        0  bsp.o(i.bsp_event_to_button_action_assign)
    i.bsp_indication_set                     0x000273a4   Section        0  bsp.o(i.bsp_indication_set)
    i.bsp_led_indication                     0x000273bc   Section        0  bsp.o(i.bsp_led_indication)
    bsp_led_indication                       0x000273bd   Thumb Code   446  bsp.o(i.bsp_led_indication)
    i.bsp_wakeup_button_enable               0x00027584   Section        0  bsp.o(i.bsp_wakeup_button_enable)
    i.channel_port_get                       0x0002758c   Section        0  nrfx_gpiote.o(i.channel_port_get)
    channel_port_get                         0x0002758d   Thumb Code    10  nrfx_gpiote.o(i.channel_port_get)
    i.characteristics_discover               0x0002759c   Section        0  ble_db_discovery.o(i.characteristics_discover)
    characteristics_discover                 0x0002759d   Thumb Code   100  ble_db_discovery.o(i.characteristics_discover)
    i.clock_clk_started_notify               0x00027608   Section        0  nrf_drv_clock.o(i.clock_clk_started_notify)
    clock_clk_started_notify                 0x00027609   Thumb Code    34  nrf_drv_clock.o(i.clock_clk_started_notify)
    i.clock_irq_handler                      0x00027630   Section        0  nrf_drv_clock.o(i.clock_irq_handler)
    clock_irq_handler                        0x00027631   Thumb Code    24  nrf_drv_clock.o(i.clock_irq_handler)
    i.compare_func                           0x0002764c   Section        0  app_timer2.o(i.compare_func)
    compare_func                             0x0002764d   Thumb Code    24  app_timer2.o(i.compare_func)
    i.conn_handle_id_find                    0x00027664   Section        0  nrf_ble_gq.o(i.conn_handle_id_find)
    conn_handle_id_find                      0x00027665   Thumb Code    30  nrf_ble_gq.o(i.conn_handle_id_find)
    i.data_length_update                     0x00027682   Section        0  nrf_ble_gatt.o(i.data_length_update)
    data_length_update                       0x00027683   Thumb Code    30  nrf_ble_gatt.o(i.data_length_update)
    i.db_disc_handler                        0x000276a0   Section        0  main.o(i.db_disc_handler)
    db_disc_handler                          0x000276a1   Thumb Code     8  main.o(i.db_disc_handler)
    i.descriptors_discover                   0x000276ac   Section        0  ble_db_discovery.o(i.descriptors_discover)
    descriptors_discover                     0x000276ad   Thumb Code   174  ble_db_discovery.o(i.descriptors_discover)
    i.discovery_available_evt_trigger        0x00027764   Section        0  ble_db_discovery.o(i.discovery_available_evt_trigger)
    discovery_available_evt_trigger          0x00027765   Thumb Code    44  ble_db_discovery.o(i.discovery_available_evt_trigger)
    i.discovery_complete_evt_trigger         0x00027794   Section        0  ble_db_discovery.o(i.discovery_complete_evt_trigger)
    discovery_complete_evt_trigger           0x00027795   Thumb Code   174  ble_db_discovery.o(i.discovery_complete_evt_trigger)
    i.discovery_error_handler                0x00027848   Section        0  ble_db_discovery.o(i.discovery_error_handler)
    discovery_error_handler                  0x00027849   Thumb Code    74  ble_db_discovery.o(i.discovery_error_handler)
    i.discovery_start                        0x00027898   Section        0  ble_db_discovery.o(i.discovery_start)
    discovery_start                          0x00027899   Thumb Code   110  ble_db_discovery.o(i.discovery_start)
    i.drv_rtc_compare_disable                0x00027914   Section        0  drv_rtc.o(i.drv_rtc_compare_disable)
    i.drv_rtc_compare_pending                0x00027928   Section        0  drv_rtc.o(i.drv_rtc_compare_pending)
    i.drv_rtc_compare_set                    0x00027936   Section        0  drv_rtc.o(i.drv_rtc_compare_set)
    i.drv_rtc_counter_get                    0x00027986   Section        0  drv_rtc.o(i.drv_rtc_counter_get)
    i.drv_rtc_init                           0x00027990   Section        0  drv_rtc.o(i.drv_rtc_init)
    i.drv_rtc_irq_trigger                    0x00027a0c   Section        0  drv_rtc.o(i.drv_rtc_irq_trigger)
    i.drv_rtc_overflow_enable                0x00027a2a   Section        0  drv_rtc.o(i.drv_rtc_overflow_enable)
    i.drv_rtc_overflow_pending               0x00027a32   Section        0  drv_rtc.o(i.drv_rtc_overflow_pending)
    i.drv_rtc_start                          0x00027a3a   Section        0  drv_rtc.o(i.drv_rtc_start)
    i.drv_rtc_stop                           0x00027a42   Section        0  drv_rtc.o(i.drv_rtc_stop)
    i.drv_rtc_windowed_compare_set           0x00027a4a   Section        0  drv_rtc.o(i.drv_rtc_windowed_compare_set)
    i.evt_enable                             0x00027b28   Section        0  drv_rtc.o(i.evt_enable)
    evt_enable                               0x00027b29   Thumb Code    18  drv_rtc.o(i.evt_enable)
    i.evt_pending                            0x00027b3a   Section        0  drv_rtc.o(i.evt_pending)
    evt_pending                              0x00027b3b   Thumb Code    20  drv_rtc.o(i.evt_pending)
    i.fifo_get                               0x00027b4e   Section        0  app_fifo.o(i.fifo_get)
    fifo_get                                 0x00027b4f   Thumb Code    20  app_fifo.o(i.fifo_get)
    i.fifo_put                               0x00027b62   Section        0  app_fifo.o(i.fifo_put)
    fifo_put                                 0x00027b63   Thumb Code    18  app_fifo.o(i.fifo_put)
    i.gatt_error_handler                     0x00027b74   Section        0  ble_nus_c.o(i.gatt_error_handler)
    gatt_error_handler                       0x00027b75   Thumb Code    10  ble_nus_c.o(i.gatt_error_handler)
    i.gatt_evt_handler                       0x00027b80   Section        0  main.o(i.gatt_evt_handler)
    i.gatt_init                              0x00027b98   Section        0  main.o(i.gatt_init)
    i.gattc_write_alloc                      0x00027bc8   Section        0  nrf_ble_gq.o(i.gattc_write_alloc)
    gattc_write_alloc                        0x00027bc9   Thumb Code    44  nrf_ble_gq.o(i.gattc_write_alloc)
    i.gatts_hvx_alloc                        0x00027bf4   Section        0  nrf_ble_gq.o(i.gatts_hvx_alloc)
    gatts_hvx_alloc                          0x00027bf5   Thumb Code    62  nrf_ble_gq.o(i.gatts_hvx_alloc)
    i.get_now                                0x00027c34   Section        0  app_timer2.o(i.get_now)
    get_now                                  0x00027c35   Thumb Code    46  app_timer2.o(i.get_now)
    i.interrupts_enable                      0x00027c68   Section        0  nrfx_uarte.o(i.interrupts_enable)
    interrupts_enable                        0x00027c69   Thumb Code   132  nrfx_uarte.o(i.interrupts_enable)
    i.is_desc_discovery_reqd                 0x00027cf0   Section        0  ble_db_discovery.o(i.is_desc_discovery_reqd)
    is_desc_discovery_reqd                   0x00027cf1   Thumb Code    76  ble_db_discovery.o(i.is_desc_discovery_reqd)
    i.is_whitelist_used                      0x00027d3c   Section        0  nrf_ble_scan.o(i.is_whitelist_used)
    i.leds_off                               0x00027d54   Section        0  bsp.o(i.leds_off)
    leds_off                                 0x00027d55   Thumb Code    42  bsp.o(i.leds_off)
    i.link_init                              0x00027d84   Section        0  nrf_ble_gatt.o(i.link_init)
    link_init                                0x00027d85   Thumb Code    24  nrf_ble_gatt.o(i.link_init)
    i.main                                   0x00027d9c   Section        0  main.o(i.main)
    i.memobj_op                              0x00027fc4   Section        0  nrf_memobj.o(i.memobj_op)
    memobj_op                                0x00027fc5   Thumb Code   126  nrf_memobj.o(i.memobj_op)
    i.nrf_atfifo_init                        0x00028042   Section        0  nrf_atfifo.o(i.nrf_atfifo_init)
    i.nrf_atfifo_item_alloc                  0x00028068   Section        0  nrf_atfifo.o(i.nrf_atfifo_item_alloc)
    i.nrf_atfifo_item_free                   0x0002807e   Section        0  nrf_atfifo.o(i.nrf_atfifo_item_free)
    i.nrf_atfifo_item_get                    0x00028094   Section        0  nrf_atfifo.o(i.nrf_atfifo_item_get)
    i.nrf_atfifo_item_put                    0x000280aa   Section        0  nrf_atfifo.o(i.nrf_atfifo_item_put)
    i.nrf_balloc_alloc                       0x000280c0   Section        0  nrf_balloc.o(i.nrf_balloc_alloc)
    i.nrf_balloc_free                        0x00028104   Section        0  nrf_balloc.o(i.nrf_balloc_free)
    i.nrf_balloc_init                        0x00028134   Section        0  nrf_balloc.o(i.nrf_balloc_init)
    i.nrf_bitmask_bit_is_set                 0x00028164   Section        0  nrfx_gpiote.o(i.nrf_bitmask_bit_is_set)
    nrf_bitmask_bit_is_set                   0x00028165   Thumb Code    16  nrfx_gpiote.o(i.nrf_bitmask_bit_is_set)
    i.nrf_ble_gatt_att_mtu_central_set       0x00028174   Section        0  nrf_ble_gatt.o(i.nrf_ble_gatt_att_mtu_central_set)
    i.nrf_ble_gatt_init                      0x0002818c   Section        0  nrf_ble_gatt.o(i.nrf_ble_gatt_init)
    i.nrf_ble_gatt_on_ble_evt                0x000281b0   Section        0  nrf_ble_gatt.o(i.nrf_ble_gatt_on_ble_evt)
    i.nrf_ble_gq_conn_handle_register        0x00028324   Section        0  nrf_ble_gq.o(i.nrf_ble_gq_conn_handle_register)
    i.nrf_ble_gq_item_add                    0x00028394   Section        0  nrf_ble_gq.o(i.nrf_ble_gq_item_add)
    i.nrf_ble_gq_on_ble_evt                  0x00028434   Section        0  nrf_ble_gq.o(i.nrf_ble_gq_on_ble_evt)
    i.nrf_ble_scan_connect_with_target       0x00028494   Section        0  nrf_ble_scan.o(i.nrf_ble_scan_connect_with_target)
    nrf_ble_scan_connect_with_target         0x00028495   Thumb Code    76  nrf_ble_scan.o(i.nrf_ble_scan_connect_with_target)
    i.nrf_ble_scan_default_conn_param_set    0x000284e0   Section        0  nrf_ble_scan.o(i.nrf_ble_scan_default_conn_param_set)
    nrf_ble_scan_default_conn_param_set      0x000284e1   Thumb Code    20  nrf_ble_scan.o(i.nrf_ble_scan_default_conn_param_set)
    i.nrf_ble_scan_default_param_set         0x000284f4   Section        0  nrf_ble_scan.o(i.nrf_ble_scan_default_param_set)
    nrf_ble_scan_default_param_set           0x000284f5   Thumb Code    30  nrf_ble_scan.o(i.nrf_ble_scan_default_param_set)
    i.nrf_ble_scan_filter_set                0x00028512   Section        0  nrf_ble_scan.o(i.nrf_ble_scan_filter_set)
    i.nrf_ble_scan_filters_disable           0x00028554   Section        0  nrf_ble_scan.o(i.nrf_ble_scan_filters_disable)
    i.nrf_ble_scan_filters_enable            0x00028562   Section        0  nrf_ble_scan.o(i.nrf_ble_scan_filters_enable)
    i.nrf_ble_scan_init                      0x00028598   Section        0  nrf_ble_scan.o(i.nrf_ble_scan_init)
    i.nrf_ble_scan_on_adv_report             0x00028602   Section        0  nrf_ble_scan.o(i.nrf_ble_scan_on_adv_report)
    nrf_ble_scan_on_adv_report               0x00028603   Thumb Code   252  nrf_ble_scan.o(i.nrf_ble_scan_on_adv_report)
    i.nrf_ble_scan_on_ble_evt                0x000286fe   Section        0  nrf_ble_scan.o(i.nrf_ble_scan_on_ble_evt)
    i.nrf_ble_scan_start                     0x0002877e   Section        0  nrf_ble_scan.o(i.nrf_ble_scan_start)
    i.nrf_clock_event_check                  0x000287c2   Section        0  nrfx_clock.o(i.nrf_clock_event_check)
    nrf_clock_event_check                    0x000287c3   Thumb Code    14  nrfx_clock.o(i.nrf_clock_event_check)
    i.nrf_clock_event_clear                  0x000287d0   Section        0  nrfx_clock.o(i.nrf_clock_event_clear)
    nrf_clock_event_clear                    0x000287d1   Thumb Code    16  nrfx_clock.o(i.nrf_clock_event_clear)
    i.nrf_drv_clock_init                     0x000287e0   Section        0  nrf_drv_clock.o(i.nrf_drv_clock_init)
    i.nrf_drv_clock_lfclk_release            0x00028820   Section        0  nrf_drv_clock.o(i.nrf_drv_clock_lfclk_release)
    i.nrf_drv_uart_init                      0x00028854   Section        0  nrf_drv_uart.o(i.nrf_drv_uart_init)
    i.nrf_drv_uart_rx                        0x000288bc   Section        0  app_uart_fifo.o(i.nrf_drv_uart_rx)
    nrf_drv_uart_rx                          0x000288bd   Thumb Code    26  app_uart_fifo.o(i.nrf_drv_uart_rx)
    i.nrf_drv_uart_tx                        0x000288dc   Section        0  app_uart_fifo.o(i.nrf_drv_uart_tx)
    nrf_drv_uart_tx                          0x000288dd   Thumb Code    26  app_uart_fifo.o(i.nrf_drv_uart_tx)
    i.nrf_gpio_cfg                           0x000288fc   Section        0  nrfx_uart.o(i.nrf_gpio_cfg)
    nrf_gpio_cfg                             0x000288fd   Thumb Code    36  nrfx_uart.o(i.nrf_gpio_cfg)
    i.nrf_gpio_cfg                           0x00028920   Section        0  nrfx_uarte.o(i.nrf_gpio_cfg)
    nrf_gpio_cfg                             0x00028921   Thumb Code    36  nrfx_uarte.o(i.nrf_gpio_cfg)
    i.nrf_gpio_cfg_input                     0x00028944   Section        0  nrfx_uart.o(i.nrf_gpio_cfg_input)
    nrf_gpio_cfg_input                       0x00028945   Thumb Code    18  nrfx_uart.o(i.nrf_gpio_cfg_input)
    i.nrf_gpio_cfg_input                     0x00028956   Section        0  nrfx_uarte.o(i.nrf_gpio_cfg_input)
    nrf_gpio_cfg_input                       0x00028957   Thumb Code    18  nrfx_uarte.o(i.nrf_gpio_cfg_input)
    i.nrf_gpio_cfg_output                    0x00028968   Section        0  nrfx_uart.o(i.nrf_gpio_cfg_output)
    nrf_gpio_cfg_output                      0x00028969   Thumb Code    20  nrfx_uart.o(i.nrf_gpio_cfg_output)
    i.nrf_gpio_cfg_output                    0x0002897c   Section        0  nrfx_uarte.o(i.nrf_gpio_cfg_output)
    nrf_gpio_cfg_output                      0x0002897d   Thumb Code    20  nrfx_uarte.o(i.nrf_gpio_cfg_output)
    i.nrf_gpio_cfg_sense_set                 0x00028990   Section        0  nrfx_gpiote.o(i.nrf_gpio_cfg_sense_set)
    nrf_gpio_cfg_sense_set                   0x00028991   Thumb Code    34  nrfx_gpiote.o(i.nrf_gpio_cfg_sense_set)
    i.nrf_gpio_latches_read_and_clear        0x000289b4   Section        0  nrfx_gpiote.o(i.nrf_gpio_latches_read_and_clear)
    nrf_gpio_latches_read_and_clear          0x000289b5   Thumb Code    38  nrfx_gpiote.o(i.nrf_gpio_latches_read_and_clear)
    i.nrf_gpio_pin_write                     0x000289e0   Section        0  boards.o(i.nrf_gpio_pin_write)
    nrf_gpio_pin_write                       0x000289e1   Thumb Code    22  boards.o(i.nrf_gpio_pin_write)
    i.nrf_gpiote_event_clear                 0x000289f8   Section        0  nrfx_gpiote.o(i.nrf_gpiote_event_clear)
    nrf_gpiote_event_clear                   0x000289f9   Thumb Code    16  nrfx_gpiote.o(i.nrf_gpiote_event_clear)
    i.nrf_gpiote_event_is_set                0x00028a0c   Section        0  nrfx_gpiote.o(i.nrf_gpiote_event_is_set)
    nrf_gpiote_event_is_set                  0x00028a0d   Thumb Code    12  nrfx_gpiote.o(i.nrf_gpiote_event_is_set)
    i.nrf_memobj_alloc                       0x00028a1c   Section        0  nrf_memobj.o(i.nrf_memobj_alloc)
    i.nrf_memobj_free                        0x00028a7c   Section        0  nrf_memobj.o(i.nrf_memobj_free)
    i.nrf_memobj_pool_init                   0x00028aae   Section        0  nrf_memobj.o(i.nrf_memobj_pool_init)
    i.nrf_memobj_read                        0x00028ab2   Section        0  nrf_memobj.o(i.nrf_memobj_read)
    i.nrf_memobj_write                       0x00028ac2   Section        0  nrf_memobj.o(i.nrf_memobj_write)
    i.nrf_pwr_mgmt_init                      0x00028ad4   Section        0  nrf_pwr_mgmt.o(i.nrf_pwr_mgmt_init)
    i.nrf_pwr_mgmt_run                       0x00028afc   Section        0  nrf_pwr_mgmt.o(i.nrf_pwr_mgmt_run)
    i.nrf_queue_generic_pop                  0x00028b40   Section        0  nrf_queue.o(i.nrf_queue_generic_pop)
    i.nrf_queue_is_empty                     0x00028bce   Section        0  nrf_queue.o(i.nrf_queue_is_empty)
    i.nrf_queue_is_full                      0x00028be0   Section        0  nrf_queue.o(i.nrf_queue_is_full)
    i.nrf_queue_next_idx                     0x00028bfe   Section        0  nrf_queue.o(i.nrf_queue_next_idx)
    nrf_queue_next_idx                       0x00028bff   Thumb Code    14  nrf_queue.o(i.nrf_queue_next_idx)
    i.nrf_queue_push                         0x00028c0c   Section        0  nrf_queue.o(i.nrf_queue_push)
    i.nrf_rtc_event_clear                    0x00028cbe   Section        0  drv_rtc.o(i.nrf_rtc_event_clear)
    nrf_rtc_event_clear                      0x00028cbf   Thumb Code    12  drv_rtc.o(i.nrf_rtc_event_clear)
    i.nrf_sdh_ble_app_ram_start_get          0x00028ccc   Section        0  nrf_sdh_ble.o(i.nrf_sdh_ble_app_ram_start_get)
    i.nrf_sdh_ble_default_cfg_set            0x00028ce0   Section        0  nrf_sdh_ble.o(i.nrf_sdh_ble_default_cfg_set)
    i.nrf_sdh_ble_enable                     0x00028d7c   Section        0  nrf_sdh_ble.o(i.nrf_sdh_ble_enable)
    i.nrf_sdh_ble_evts_poll                  0x00028d90   Section        0  nrf_sdh_ble.o(i.nrf_sdh_ble_evts_poll)
    nrf_sdh_ble_evts_poll                    0x00028d91   Thumb Code    88  nrf_sdh_ble.o(i.nrf_sdh_ble_evts_poll)
    i.nrf_sdh_enable_request                 0x00028df0   Section        0  nrf_sdh.o(i.nrf_sdh_enable_request)
    i.nrf_sdh_evts_poll                      0x00028e64   Section        0  nrf_sdh.o(i.nrf_sdh_evts_poll)
    i.nrf_sdh_is_enabled                     0x00028e88   Section        0  nrf_sdh.o(i.nrf_sdh_is_enabled)
    i.nrf_sdh_soc_evts_poll                  0x00028e94   Section        0  nrf_sdh_soc.o(i.nrf_sdh_soc_evts_poll)
    nrf_sdh_soc_evts_poll                    0x00028e95   Thumb Code    56  nrf_sdh_soc.o(i.nrf_sdh_soc_evts_poll)
    i.nrf_section_iter_init                  0x00028ed0   Section        0  nrf_section_iter.o(i.nrf_section_iter_init)
    i.nrf_section_iter_item_set              0x00028eda   Section        0  nrf_section_iter.o(i.nrf_section_iter_item_set)
    nrf_section_iter_item_set                0x00028edb   Thumb Code    36  nrf_section_iter.o(i.nrf_section_iter_item_set)
    i.nrf_section_iter_next                  0x00028efe   Section        0  nrf_section_iter.o(i.nrf_section_iter_next)
    i.nrf_sortlist_add                       0x00028f1e   Section        0  nrf_sortlist.o(i.nrf_sortlist_add)
    i.nrf_sortlist_peek                      0x00028f40   Section        0  nrf_sortlist.o(i.nrf_sortlist_peek)
    i.nrf_sortlist_pop                       0x00028f46   Section        0  nrf_sortlist.o(i.nrf_sortlist_pop)
    i.nrf_sortlist_remove                    0x00028f54   Section        0  nrf_sortlist.o(i.nrf_sortlist_remove)
    i.nrf_uart_event_check                   0x00028f72   Section        0  nrfx_uart.o(i.nrf_uart_event_check)
    nrf_uart_event_check                     0x00028f73   Thumb Code    10  nrfx_uart.o(i.nrf_uart_event_check)
    i.nrf_uart_event_clear                   0x00028f7c   Section        0  nrfx_uart.o(i.nrf_uart_event_clear)
    nrf_uart_event_clear                     0x00028f7d   Thumb Code    12  nrfx_uart.o(i.nrf_uart_event_clear)
    i.nrf_uart_int_enable_check              0x00028f88   Section        0  nrfx_uart.o(i.nrf_uart_int_enable_check)
    nrf_uart_int_enable_check                0x00028f89   Thumb Code    12  nrfx_uart.o(i.nrf_uart_int_enable_check)
    i.nrf_uarte_event_check                  0x00028f94   Section        0  nrfx_uarte.o(i.nrf_uarte_event_check)
    nrf_uarte_event_check                    0x00028f95   Thumb Code    10  nrfx_uarte.o(i.nrf_uarte_event_check)
    i.nrf_uarte_event_clear                  0x00028f9e   Section        0  nrfx_uarte.o(i.nrf_uarte_event_clear)
    nrf_uarte_event_clear                    0x00028f9f   Thumb Code    12  nrfx_uarte.o(i.nrf_uarte_event_clear)
    i.nrf_wdt_started                        0x00028fac   Section        0  nrf_drv_clock.o(i.nrf_wdt_started)
    nrf_wdt_started                          0x00028fad   Thumb Code    12  nrf_drv_clock.o(i.nrf_wdt_started)
    i.nrfx_clock_enable                      0x00028fbc   Section        0  nrfx_clock.o(i.nrfx_clock_enable)
    i.nrfx_clock_init                        0x00028fe8   Section        0  nrfx_clock.o(i.nrfx_clock_init)
    i.nrfx_clock_lfclk_stop                  0x00029008   Section        0  nrfx_clock.o(i.nrfx_clock_lfclk_stop)
    i.nrfx_coredep_delay_us                  0x00029038   Section        0  drv_rtc.o(i.nrfx_coredep_delay_us)
    nrfx_coredep_delay_us                    0x00029039   Thumb Code    12  drv_rtc.o(i.nrfx_coredep_delay_us)
    i.nrfx_is_in_ram                         0x00029048   Section        0  nrfx_uarte.o(i.nrfx_is_in_ram)
    nrfx_is_in_ram                           0x00029049   Thumb Code    16  nrfx_uarte.o(i.nrfx_is_in_ram)
    i.nrfx_prs_acquire                       0x00029058   Section        0  nrfx_prs.o(i.nrfx_prs_acquire)
    i.nrfx_uart_0_irq_handler                0x00029094   Section        0  nrfx_uart.o(i.nrfx_uart_0_irq_handler)
    i.nrfx_uart_init                         0x000290a4   Section        0  nrfx_uart.o(i.nrfx_uart_init)
    i.nrfx_uart_rx                           0x00029178   Section        0  nrfx_uart.o(i.nrfx_uart_rx)
    i.nrfx_uart_tx                           0x00029264   Section        0  nrfx_uart.o(i.nrfx_uart_tx)
    i.nrfx_uart_tx_in_progress               0x0002930c   Section        0  nrfx_uart.o(i.nrfx_uart_tx_in_progress)
    i.nrfx_uarte_0_irq_handler               0x0002932c   Section        0  nrfx_uarte.o(i.nrfx_uarte_0_irq_handler)
    i.nrfx_uarte_init                        0x0002933c   Section        0  nrfx_uarte.o(i.nrfx_uarte_init)
    i.nrfx_uarte_rx                          0x000293a8   Section        0  nrfx_uarte.o(i.nrfx_uarte_rx)
    i.nrfx_uarte_tx                          0x000294ac   Section        0  nrfx_uarte.o(i.nrfx_uarte_tx)
    i.nrfx_uarte_tx_in_progress              0x00029558   Section        0  nrfx_uarte.o(i.nrfx_uarte_tx_in_progress)
    i.nus_error_handler                      0x00029574   Section        0  main.o(i.nus_error_handler)
    nus_error_handler                        0x00029575   Thumb Code     4  main.o(i.nus_error_handler)
    i.on_characteristic_discovery_rsp        0x00029578   Section        0  ble_db_discovery.o(i.on_characteristic_discovery_rsp)
    on_characteristic_discovery_rsp          0x00029579   Thumb Code   252  ble_db_discovery.o(i.on_characteristic_discovery_rsp)
    i.on_descriptor_discovery_rsp            0x00029674   Section        0  ble_db_discovery.o(i.on_descriptor_discovery_rsp)
    on_descriptor_discovery_rsp              0x00029675   Thumb Code   218  ble_db_discovery.o(i.on_descriptor_discovery_rsp)
    i.on_primary_srv_discovery_rsp           0x0002974e   Section        0  ble_db_discovery.o(i.on_primary_srv_discovery_rsp)
    on_primary_srv_discovery_rsp             0x0002974f   Thumb Code   114  ble_db_discovery.o(i.on_primary_srv_discovery_rsp)
    i.on_srv_disc_completion                 0x000297c0   Section        0  ble_db_discovery.o(i.on_srv_disc_completion)
    on_srv_disc_completion                   0x000297c1   Thumb Code   142  ble_db_discovery.o(i.on_srv_disc_completion)
    i.port_event_handle                      0x0002985c   Section        0  nrfx_gpiote.o(i.port_event_handle)
    port_event_handle                        0x0002985d   Thumb Code   176  nrfx_gpiote.o(i.port_event_handle)
    i.port_handler_polarity_get              0x00029910   Section        0  nrfx_gpiote.o(i.port_handler_polarity_get)
    port_handler_polarity_get                0x00029911   Thumb Code    12  nrfx_gpiote.o(i.port_handler_polarity_get)
    i.prs_box_get                            0x00029920   Section        0  nrfx_prs.o(i.prs_box_get)
    prs_box_get                              0x00029921   Thumb Code    14  nrfx_prs.o(i.prs_box_get)
    i.queue_process                          0x00029938   Section        0  nrf_ble_gq.o(i.queue_process)
    queue_process                            0x00029939   Thumb Code   218  nrf_ble_gq.o(i.queue_process)
    i.queue_utilization_get                  0x00029a18   Section        0  nrf_queue.o(i.queue_utilization_get)
    queue_utilization_get                    0x00029a19   Thumb Code    24  nrf_queue.o(i.queue_utilization_get)
    i.queues_purge                           0x00029a30   Section        0  nrf_ble_gq.o(i.queues_purge)
    queues_purge                             0x00029a31   Thumb Code    84  nrf_ble_gq.o(i.queues_purge)
    i.registered_handler_get                 0x00029a88   Section        0  ble_db_discovery.o(i.registered_handler_get)
    registered_handler_get                   0x00029a89   Thumb Code    48  ble_db_discovery.o(i.registered_handler_get)
    i.request_err_code_handle                0x00029ac0   Section        0  nrf_ble_gq.o(i.request_err_code_handle)
    request_err_code_handle                  0x00029ac1   Thumb Code    30  nrf_ble_gq.o(i.request_err_code_handle)
    i.request_process                        0x00029ade   Section        0  nrf_ble_gq.o(i.request_process)
    request_process                          0x00029adf   Thumb Code   114  nrf_ble_gq.o(i.request_process)
    i.rtc_irq                                0x00029b50   Section        0  app_timer2.o(i.rtc_irq)
    rtc_irq                                  0x00029b51   Thumb Code    88  app_timer2.o(i.rtc_irq)
    i.rtc_schedule                           0x00029bac   Section        0  app_timer2.o(i.rtc_schedule)
    rtc_schedule                             0x00029bad   Thumb Code    92  app_timer2.o(i.rtc_schedule)
    i.rtc_update                             0x00029c10   Section        0  app_timer2.o(i.rtc_update)
    rtc_update                               0x00029c11   Thumb Code   108  app_timer2.o(i.rtc_update)
    i.rx_byte                                0x00029c84   Section        0  nrfx_uart.o(i.rx_byte)
    rx_byte                                  0x00029c85   Thumb Code    50  nrfx_uart.o(i.rx_byte)
    i.rx_done_event                          0x00029cb6   Section        0  nrfx_uart.o(i.rx_done_event)
    rx_done_event                            0x00029cb7   Thumb Code    22  nrfx_uart.o(i.rx_done_event)
    i.rx_done_event                          0x00029ccc   Section        0  nrfx_uarte.o(i.rx_done_event)
    rx_done_event                            0x00029ccd   Thumb Code    22  nrfx_uarte.o(i.rx_done_event)
    i.rx_enable                              0x00029ce2   Section        0  nrfx_uart.o(i.rx_enable)
    rx_enable                                0x00029ce3   Thumb Code    32  nrfx_uart.o(i.rx_enable)
    i.scan_evt_handler                       0x00029d02   Section        0  main.o(i.scan_evt_handler)
    scan_evt_handler                         0x00029d03   Thumb Code    26  main.o(i.scan_evt_handler)
    i.scan_init                              0x00029d1c   Section        0  main.o(i.scan_init)
    scan_init                                0x00029d1d   Thumb Code    74  main.o(i.scan_init)
    i.scan_start                             0x00029d74   Section        0  main.o(i.scan_start)
    scan_start                               0x00029d75   Thumb Code    34  main.o(i.scan_start)
    i.sd_state_evt_handler                   0x00029d9c   Section        0  nrf_drv_clock.o(i.sd_state_evt_handler)
    sd_state_evt_handler                     0x00029d9d   Thumb Code    84  nrf_drv_clock.o(i.sd_state_evt_handler)
    i.sdh_request_observer_notify            0x00029df4   Section        0  nrf_sdh.o(i.sdh_request_observer_notify)
    sdh_request_observer_notify              0x00029df5   Thumb Code    44  nrf_sdh.o(i.sdh_request_observer_notify)
    i.sdh_state_observer_notify              0x00029e24   Section        0  nrf_sdh.o(i.sdh_state_observer_notify)
    sdh_state_observer_notify                0x00029e25   Thumb Code    38  nrf_sdh.o(i.sdh_state_observer_notify)
    i.shutdown_handler                       0x00029e50   Section        0  main.o(i.shutdown_handler)
    shutdown_handler                         0x00029e51   Thumb Code    32  main.o(i.shutdown_handler)
    i.soc_evt_handler                        0x00029e70   Section        0  nrf_drv_clock.o(i.soc_evt_handler)
    soc_evt_handler                          0x00029e71   Thumb Code    18  nrf_drv_clock.o(i.soc_evt_handler)
    i.softdevices_evt_irq_enable             0x00029e88   Section        0  nrf_sdh.o(i.softdevices_evt_irq_enable)
    softdevices_evt_irq_enable               0x00029e89   Thumb Code    80  nrf_sdh.o(i.softdevices_evt_irq_enable)
    i.sortlist_pop                           0x00029ee0   Section        0  app_timer2.o(i.sortlist_pop)
    sortlist_pop                             0x00029ee1   Thumb Code     6  app_timer2.o(i.sortlist_pop)
    i.timer_expire                           0x00029eec   Section        0  app_timer2.o(i.timer_expire)
    timer_expire                             0x00029eed   Thumb Code    80  app_timer2.o(i.timer_expire)
    i.timer_req_process                      0x00029f44   Section        0  app_timer2.o(i.timer_req_process)
    timer_req_process                        0x00029f45   Thumb Code   110  app_timer2.o(i.timer_req_process)
    i.timer_req_schedule                     0x00029fbc   Section        0  app_timer2.o(i.timer_req_schedule)
    timer_req_schedule                       0x00029fbd   Thumb Code    46  app_timer2.o(i.timer_req_schedule)
    i.tx_byte                                0x00029ff4   Section        0  nrfx_uart.o(i.tx_byte)
    tx_byte                                  0x00029ff5   Thumb Code    32  nrfx_uart.o(i.tx_byte)
    i.tx_done_event                          0x0002a014   Section        0  nrfx_uart.o(i.tx_done_event)
    tx_done_event                            0x0002a015   Thumb Code    26  nrfx_uart.o(i.tx_done_event)
    i.tx_done_event                          0x0002a02e   Section        0  nrfx_uarte.o(i.tx_done_event)
    tx_done_event                            0x0002a02f   Thumb Code    26  nrfx_uarte.o(i.tx_done_event)
    i.uart_event_handle                      0x0002a048   Section        0  main.o(i.uart_event_handle)
    i.uart_event_handler                     0x0002a04c   Section        0  app_uart_fifo.o(i.uart_event_handler)
    uart_event_handler                       0x0002a04d   Thumb Code   156  app_uart_fifo.o(i.uart_event_handler)
    i.uart_evt_handler                       0x0002a0f0   Section        0  nrf_drv_uart.o(i.uart_evt_handler)
    uart_evt_handler                         0x0002a0f1   Thumb Code    42  nrf_drv_uart.o(i.uart_evt_handler)
    i.uart_irq_handler                       0x0002a120   Section        0  nrfx_uart.o(i.uart_irq_handler)
    uart_irq_handler                         0x0002a121   Thumb Code   298  nrfx_uart.o(i.uart_irq_handler)
    i.uarte_evt_handler                      0x0002a24c   Section        0  nrf_drv_uart.o(i.uarte_evt_handler)
    uarte_evt_handler                        0x0002a24d   Thumb Code    42  nrf_drv_uart.o(i.uarte_evt_handler)
    i.uarte_irq_handler                      0x0002a27c   Section        0  nrfx_uarte.o(i.uarte_irq_handler)
    uarte_irq_handler                        0x0002a27d   Thumb Code   274  nrfx_uarte.o(i.uarte_irq_handler)
    i.wakeup_button_cfg                      0x0002a38e   Section        0  bsp.o(i.wakeup_button_cfg)
    wakeup_button_cfg                        0x0002a38f   Thumb Code    58  bsp.o(i.wakeup_button_cfg)
    .constdata                               0x0002a3c8   Section       20  main.o(.constdata)
    m_ble_gatt_queuereq_queue                0x0002a3c8   Data          20  main.o(.constdata)
    .constdata                               0x0002a3dc   Section       32  main.o(.constdata)
    m_nus_uuid                               0x0002a3de   Data           4  main.o(.constdata)
    .constdata                               0x0002a3fc   Section        8  boards.o(.constdata)
    m_board_led_list                         0x0002a3fc   Data           4  boards.o(.constdata)
    m_board_btn_list                         0x0002a400   Data           4  boards.o(.constdata)
    .constdata                               0x0002a404   Section       44  bsp.o(.constdata)
    m_bsp_leds_tmr                           0x0002a404   Data           4  bsp.o(.constdata)
    m_bsp_alert_tmr                          0x0002a408   Data           4  bsp.o(.constdata)
    m_bsp_button_tmr                         0x0002a40c   Data           4  bsp.o(.constdata)
    app_buttons                              0x0002a410   Data          32  bsp.o(.constdata)
    .constdata                               0x0002a430   Section      124  ble_db_discovery.o(.constdata)
    .constdata                               0x0002a4ac   Section       22  nrf_ble_gatt.o(.constdata)
    .constdata                               0x0002a4c4   Section       24  nrf_ble_gq.o(.constdata)
    m_req_data_alloc                         0x0002a4c4   Data          24  nrf_ble_gq.o(.constdata)
    .constdata                               0x0002a4dc   Section       16  ble_nus_c.o(.constdata)
    .constdata                               0x0002a4ec   Section        4  nrfx_gpiote.o(.constdata)
    .constdata                               0x0002a4f0   Section        4  nrfx_uart.o(.constdata)
    irq_handlers                             0x0002a4f0   Data           4  nrfx_uart.o(.constdata)
    .constdata                               0x0002a4f4   Section        4  nrfx_uarte.o(.constdata)
    irq_handlers                             0x0002a4f4   Data           4  nrfx_uarte.o(.constdata)
    .constdata                               0x0002a4f8   Section       20  app_timer2.o(.constdata)
    m_req_fifo                               0x0002a4f8   Data           4  app_timer2.o(.constdata)
    m_app_timer_sortlist                     0x0002a500   Data          12  app_timer2.o(.constdata)
    .constdata                               0x0002a50c   Section       32  app_uart_fifo.o(.constdata)
    .constdata                               0x0002a530   Section        6  drv_rtc.o(.constdata)
    delay_machine_code                       0x0002a530   Data           6  drv_rtc.o(.constdata)
    .constdata                               0x0002a538   Section       24  nrf_pwr_mgmt.o(.constdata)
    pwr_mgmt_data_array                      0x0002a538   Data          24  nrf_pwr_mgmt.o(.constdata)
    .constdata                               0x0002a550   Section       12  nrf_pwr_mgmt.o(.constdata)
    pwr_mgmt_data                            0x0002a550   Data          12  nrf_pwr_mgmt.o(.constdata)
    .constdata                               0x0002a55c   Section       16  nrf_sdh.o(.constdata)
    sdh_req_observers_array                  0x0002a55c   Data          16  nrf_sdh.o(.constdata)
    .constdata                               0x0002a56c   Section       40  nrf_sdh.o(.constdata)
    sdh_req_observers                        0x0002a570   Data          12  nrf_sdh.o(.constdata)
    sdh_state_observers                      0x0002a57c   Data          12  nrf_sdh.o(.constdata)
    sdh_stack_observers                      0x0002a588   Data          12  nrf_sdh.o(.constdata)
    .constdata                               0x0002a594   Section       16  nrf_sdh.o(.constdata)
    sdh_state_observers_array                0x0002a594   Data          16  nrf_sdh.o(.constdata)
    .constdata                               0x0002a5a4   Section       16  nrf_sdh.o(.constdata)
    sdh_stack_observers_array                0x0002a5a4   Data          16  nrf_sdh.o(.constdata)
    .constdata                               0x0002a5b4   Section       32  nrf_sdh_ble.o(.constdata)
    sdh_ble_observers_array                  0x0002a5b4   Data          32  nrf_sdh_ble.o(.constdata)
    .constdata                               0x0002a5d4   Section       16  nrf_sdh_ble.o(.constdata)
    sdh_ble_observers                        0x0002a5d8   Data          12  nrf_sdh_ble.o(.constdata)
    .constdata                               0x0002a5e4   Section       16  nrf_sdh_soc.o(.constdata)
    sdh_soc_observers_array                  0x0002a5e4   Data          16  nrf_sdh_soc.o(.constdata)
    .constdata                               0x0002a5f4   Section       12  nrf_sdh_soc.o(.constdata)
    sdh_soc_observers                        0x0002a5f4   Data          12  nrf_sdh_soc.o(.constdata)
    nrf_balloc                               0x0002a620   Section       20  main.o(nrf_balloc)
    __tagsym$$used                           0x0002a620   Number         0  main.o(nrf_balloc)
    nrf_queue                                0x0002a634   Section       20  main.o(nrf_queue)
    __tagsym$$used                           0x0002a634   Number         0  main.o(nrf_queue)
    pwr_mgmt_data1                           0x0002a648   Section        4  main.o(pwr_mgmt_data1)
    __tagsym$$used                           0x0002a648   Number         0  main.o(pwr_mgmt_data1)
    shutdown_handler_handler_function        0x0002a648   Data           4  main.o(pwr_mgmt_data1)
    sdh_ble_observers1                       0x0002a64c   Section       32  main.o(sdh_ble_observers1)
    __tagsym$$used                           0x0002a64c   Number         0  main.o(sdh_ble_observers1)
    m_gatt_obs                               0x0002a64c   Data           8  main.o(sdh_ble_observers1)
    __tagsym$$used                           0x0002a654   Number         0  main.o(sdh_ble_observers1)
    m_db_disc_obs                            0x0002a654   Data           8  main.o(sdh_ble_observers1)
    __tagsym$$used                           0x0002a65c   Number         0  main.o(sdh_ble_observers1)
    m_scan_ble_obs                           0x0002a65c   Data           8  main.o(sdh_ble_observers1)
    __tagsym$$used                           0x0002a664   Number         0  main.o(sdh_ble_observers1)
    m_ble_gatt_queue_obs                     0x0002a664   Data           8  main.o(sdh_ble_observers1)
    sdh_ble_observers1                       0x0002a66c   Section        8  bsp_btn_ble.o(sdh_ble_observers1)
    __tagsym$$used                           0x0002a66c   Number         0  bsp_btn_ble.o(sdh_ble_observers1)
    m_ble_observer                           0x0002a66c   Data           8  bsp_btn_ble.o(sdh_ble_observers1)
    sdh_ble_observers2                       0x0002a674   Section        8  main.o(sdh_ble_observers2)
    __tagsym$$used                           0x0002a674   Number         0  main.o(sdh_ble_observers2)
    m_ble_nus_c_obs                          0x0002a674   Data           8  main.o(sdh_ble_observers2)
    sdh_ble_observers3                       0x0002a67c   Section        8  main.o(sdh_ble_observers3)
    __tagsym$$used                           0x0002a67c   Number         0  main.o(sdh_ble_observers3)
    m_ble_observer                           0x0002a67c   Data           8  main.o(sdh_ble_observers3)
    sdh_soc_observers0                       0x0002a684   Section        8  nrf_drv_clock.o(sdh_soc_observers0)
    __tagsym$$used                           0x0002a684   Number         0  nrf_drv_clock.o(sdh_soc_observers0)
    m_soc_evt_observer                       0x0002a684   Data           8  nrf_drv_clock.o(sdh_soc_observers0)
    sdh_stack_observers0                     0x0002a68c   Section        8  nrf_sdh_ble.o(sdh_stack_observers0)
    __tagsym$$used                           0x0002a68c   Number         0  nrf_sdh_ble.o(sdh_stack_observers0)
    m_nrf_sdh_ble_evts_poll                  0x0002a68c   Data           8  nrf_sdh_ble.o(sdh_stack_observers0)
    sdh_stack_observers0                     0x0002a694   Section        8  nrf_sdh_soc.o(sdh_stack_observers0)
    __tagsym$$used                           0x0002a694   Number         0  nrf_sdh_soc.o(sdh_stack_observers0)
    m_nrf_sdh_soc_evts_poll                  0x0002a694   Data           8  nrf_sdh_soc.o(sdh_stack_observers0)
    sdh_state_observers0                     0x0002a69c   Section        8  nrf_drv_clock.o(sdh_state_observers0)
    __tagsym$$used                           0x0002a69c   Number         0  nrf_drv_clock.o(sdh_state_observers0)
    m_sd_state_observer                      0x0002a69c   Data           8  nrf_drv_clock.o(sdh_state_observers0)
    .data                                    0x20002b28   Section     1516  main.o(.data)
    m_ble_nus_max_data_len                   0x20002b2a   Data           2  main.o(.data)
    m_db_disc                                0x20002b30   Data        1488  main.o(.data)
    m_ble_gatt_queue                         0x20003100   Data          20  main.o(.data)
    .data                                    0x20003114   Section        2  main.o(.data)
    m_ble_gatt_queueconn_handles_arr         0x20003114   Data           2  main.o(.data)
    .data                                    0x20003116   Section        4  main.o(.data)
    m_ble_gatt_queuepurge_queue_nrf_queue_buffer 0x20003116   Data           4  main.o(.data)
    .data                                    0x2000311a   Section        8  main.o(.data)
    m_ble_gatt_queuepool_nrf_balloc_pool_stack 0x2000311a   Data           8  main.o(.data)
    .data                                    0x20003124   Section        8  main.o(.data)
    m_ble_gatt_queuepool_nrf_balloc_cb       0x20003124   Data           8  main.o(.data)
    .data                                    0x2000312c   Section       16  bsp.o(.data)
    m_stable_state                           0x2000312c   Data           1  bsp.o(.data)
    m_leds_clear                             0x2000312d   Data           1  bsp.o(.data)
    m_alert_on                               0x2000312e   Data           1  bsp.o(.data)
    current_long_push_pin_no                 0x2000312f   Data           1  bsp.o(.data)
    m_indication_type                        0x20003130   Data           4  bsp.o(.data)
    m_registered_callback                    0x20003134   Data           4  bsp.o(.data)
    release_event_at_push                    0x20003138   Data           4  bsp.o(.data)
    .data                                    0x2000313c   Section        8  bsp_btn_ble.o(.data)
    m_error_handler                          0x2000313c   Data           4  bsp_btn_ble.o(.data)
    m_num_connections                        0x20003140   Data           4  bsp_btn_ble.o(.data)
    .data                                    0x20003144   Section       16  ble_db_discovery.o(.data)
    m_initialized                            0x20003144   Data           1  ble_db_discovery.o(.data)
    m_evt_handler                            0x20003148   Data           4  ble_db_discovery.o(.data)
    mp_gatt_queue                            0x2000314c   Data           4  ble_db_discovery.o(.data)
    m_num_of_handlers_reg                    0x20003150   Data           4  ble_db_discovery.o(.data)
    .data                                    0x20003154   Section       12  nrf_drv_uart.o(.data)
    m_handlers                               0x20003158   Data           4  nrf_drv_uart.o(.data)
    m_contexts                               0x2000315c   Data           4  nrf_drv_uart.o(.data)
    .data                                    0x20003160   Section        8  nrfx_clock.o(.data)
    m_clock_cb                               0x20003160   Data           8  nrfx_clock.o(.data)
    .data                                    0x20003168   Section        8  nrfx_prs.o(.data)
    m_prs_box_4                              0x20003168   Data           8  nrfx_prs.o(.data)
    .data                                    0x20003170   Section       32  app_timer2.o(.data)
    m_global_active                          0x20003170   Data           1  app_timer2.o(.data)
    mp_active_timer                          0x20003174   Data           4  app_timer2.o(.data)
    m_rtc_inst                               0x20003178   Data           8  app_timer2.o(.data)
    m_base_counter                           0x20003180   Data           8  app_timer2.o(.data)
    m_stamp64                                0x20003188   Data           8  app_timer2.o(.data)
    .data                                    0x20003190   Section        4  app_timer2.o(.data)
    m_app_timer_sortlist_sortlist_cb         0x20003190   Data           4  app_timer2.o(.data)
    .data                                    0x20003194   Section       28  app_uart_fifo.o(.data)
    tx_buffer                                0x20003194   Data           1  app_uart_fifo.o(.data)
    rx_buffer                                0x20003195   Data           1  app_uart_fifo.o(.data)
    m_rx_ovf                                 0x20003196   Data           1  app_uart_fifo.o(.data)
    m_event_handler                          0x20003198   Data           4  app_uart_fifo.o(.data)
    app_uart_inst                            0x2000319c   Data          20  app_uart_fifo.o(.data)
    .data                                    0x200031b0   Section       12  drv_rtc.o(.data)
    m_handlers                               0x200031b0   Data           4  drv_rtc.o(.data)
    m_cb                                     0x200031b4   Data           8  drv_rtc.o(.data)
    .data                                    0x200031bc   Section        8  nrf_pwr_mgmt.o(.data)
    m_pwr_mgmt_evt                           0x200031bc   Data           1  nrf_pwr_mgmt.o(.data)
    m_shutdown_started                       0x200031bd   Data           1  nrf_pwr_mgmt.o(.data)
    m_sysoff_mtx                             0x200031c0   Data           4  nrf_pwr_mgmt.o(.data)
    .data                                    0x200031c4   Section        3  nrf_sdh.o(.data)
    m_nrf_sdh_enabled                        0x200031c4   Data           1  nrf_sdh.o(.data)
    m_nrf_sdh_suspended                      0x200031c5   Data           1  nrf_sdh.o(.data)
    m_nrf_sdh_continue                       0x200031c6   Data           1  nrf_sdh.o(.data)
    .data                                    0x200031c7   Section        1  nrf_sdh_ble.o(.data)
    m_stack_is_enabled                       0x200031c7   Data           1  nrf_sdh_ble.o(.data)
    .data                                    0x200031c8   Section        4  system_nrf52.o(.data)
    __tagsym$$used                           0x200031c8   Number         0  system_nrf52.o(.data)
    .bss                                     0x200031cc   Section     2416  main.o(.bss)
    rx_buf                                   0x200032c0   Data        1024  main.o(.bss)
    tx_buf                                   0x200036c0   Data        1024  main.o(.bss)
    m_ble_nus_c                              0x20003ac0   Data          24  main.o(.bss)
    m_gatt                                   0x20003ad8   Data          20  main.o(.bss)
    m_scan                                   0x20003aec   Data          80  main.o(.bss)
    .bss                                     0x20003b3c   Section      160  main.o(.bss)
    m_ble_gatt_queuereq_queue0_nrf_queue_buffer 0x20003b3c   Data         160  main.o(.bss)
    .bss                                     0x20003bdc   Section       12  main.o(.bss)
    m_ble_gatt_queuereq_queue0_nrf_queue_cb  0x20003bdc   Data          12  main.o(.bss)
    .bss                                     0x20003be8   Section       12  main.o(.bss)
    m_ble_gatt_queuepurge_queue_nrf_queue_cb 0x20003be8   Data          12  main.o(.bss)
    .bss                                     0x20003bf4   Section      192  main.o(.bss)
    m_ble_gatt_queuepool_nrf_balloc_pool_mem 0x20003bf4   Data         192  main.o(.bss)
    .bss                                     0x20003cb8   Section       32  bsp.o(.bss)
    m_bsp_leds_tmr_data                      0x20003cb8   Data          32  bsp.o(.bss)
    .bss                                     0x20003cd8   Section       32  bsp.o(.bss)
    m_bsp_alert_tmr_data                     0x20003cd8   Data          32  bsp.o(.bss)
    .bss                                     0x20003cf8   Section       12  bsp.o(.bss)
    m_events_list                            0x20003cf8   Data          12  bsp.o(.bss)
    .bss                                     0x20003d08   Section       32  bsp.o(.bss)
    m_bsp_button_tmr_data                    0x20003d08   Data          32  bsp.o(.bss)
    .bss                                     0x20003d28   Section       24  ble_db_discovery.o(.bss)
    m_registered_handlers                    0x20003d28   Data          24  ble_db_discovery.o(.bss)
    .bss                                     0x20003d40   Section       20  nrf_drv_clock.o(.bss)
    m_clock_cb                               0x20003d40   Data          20  nrf_drv_clock.o(.bss)
    .bss                                     0x20003d54   Section       92  nrfx_gpiote.o(.bss)
    m_cb                                     0x20003d54   Data          92  nrfx_gpiote.o(.bss)
    .bss                                     0x20003db0   Section       44  nrfx_uart.o(.bss)
    m_cb                                     0x20003db0   Data          44  nrfx_uart.o(.bss)
    .bss                                     0x20003ddc   Section       36  nrfx_uarte.o(.bss)
    m_cb                                     0x20003ddc   Data          36  nrfx_uarte.o(.bss)
    .bss                                     0x20003e00   Section       16  app_timer2.o(.bss)
    m_req_fifo_inst                          0x20003e00   Data          16  app_timer2.o(.bss)
    .bss                                     0x20003e10   Section       88  app_timer2.o(.bss)
    m_req_fifo_data                          0x20003e10   Data          88  app_timer2.o(.bss)
    .bss                                     0x20003e68   Section       32  app_uart_fifo.o(.bss)
    m_rx_fifo                                0x20003e68   Data          16  app_uart_fifo.o(.bss)
    m_tx_fifo                                0x20003e78   Data          16  app_uart_fifo.o(.bss)
    .bss                                     0x20003e88   Section       12  app_util_platform.o(.bss)
    .bss                                     0x20003e94   Section       12  nrf_pwr_mgmt.o(.bss)
    m_handlers_iter                          0x20003e94   Data          12  nrf_pwr_mgmt.o(.bss)
    STACK                                    0x20003ea0   Section     8192  arm_startup_nrf52.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$E$P$D$K$B$S$7EM$VFPi3$EXTD16$VFPS$VFMA$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OSPACE$EBA8$MICROLIB$REQ8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    __arm_fini_                               - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _microlib_exit                            - Undefined Weak Reference
    pwr_mgmt_data0$$Base                      - Undefined Reference
    pwr_mgmt_data0$$Limit                     - Undefined Reference
    pwr_mgmt_data2$$Base                      - Undefined Reference
    pwr_mgmt_data2$$Limit                     - Undefined Reference
    sdh_ble_observers0$$Base                  - Undefined Reference
    sdh_ble_observers0$$Limit                 - Undefined Reference
    sdh_req_observers0$$Base                  - Undefined Reference
    sdh_req_observers0$$Limit                 - Undefined Reference
    sdh_req_observers1$$Base                  - Undefined Reference
    sdh_req_observers1$$Limit                 - Undefined Reference
    sdh_soc_observers1$$Base                  - Undefined Reference
    sdh_soc_observers1$$Limit                 - Undefined Reference
    sdh_stack_observers1$$Base                - Undefined Reference
    sdh_stack_observers1$$Limit               - Undefined Reference
    sdh_state_observers1$$Base                - Undefined Reference
    sdh_state_observers1$$Limit               - Undefined Reference
    __Vectors_Size                           0x00000200   Number         0  arm_startup_nrf52.o ABSOLUTE
    __Vectors                                0x00026000   Data           4  arm_startup_nrf52.o(RESET)
    __Vectors_End                            0x00026200   Data           0  arm_startup_nrf52.o(RESET)
    __main                                   0x00026201   Thumb Code     0  entry.o(.ARM.Collect$$$$00000000)
    _main_stk                                0x00026201   Thumb Code     0  entry2.o(.ARM.Collect$$$$00000001)
    _main_scatterload                        0x00026205   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    __main_after_scatterload                 0x00026209   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    _main_clock                              0x00026209   Thumb Code     0  entry7b.o(.ARM.Collect$$$$00000008)
    _main_cpp_init                           0x00026209   Thumb Code     0  entry8b.o(.ARM.Collect$$$$0000000A)
    _main_init                               0x00026209   Thumb Code     0  entry9a.o(.ARM.Collect$$$$0000000B)
    __rt_lib_shutdown_fini                   0x00026211   Thumb Code     0  entry12b.o(.ARM.Collect$$$$0000000E)
    __rt_final_cpp                           0x00026215   Thumb Code     0  entry10a.o(.ARM.Collect$$$$0000000F)
    __rt_final_exit                          0x00026215   Thumb Code     0  entry11a.o(.ARM.Collect$$$$00000011)
    __asm___12_nrf_atfifo_c_51f461e1__nrf_atfifo_wspace_req 0x00026219   Thumb Code    56  nrf_atfifo.o(.emb_text)
    __asm___12_nrf_atfifo_c_51f461e1__nrf_atfifo_wspace_close 0x00026251   Thumb Code    18  nrf_atfifo.o(.emb_text)
    __asm___12_nrf_atfifo_c_51f461e1__nrf_atfifo_rspace_req 0x00026263   Thumb Code    58  nrf_atfifo.o(.emb_text)
    __asm___12_nrf_atfifo_c_51f461e1__nrf_atfifo_rspace_close 0x0002629d   Thumb Code    18  nrf_atfifo.o(.emb_text)
    __asm___12_nrf_atfifo_c_51f461e1__nrf_atfifo_space_clear 0x000262af   Thumb Code    50  nrf_atfifo.o(.emb_text)
    Reset_Handler                            0x000262e1   Thumb Code     8  arm_startup_nrf52.o(.text)
    NMI_Handler                              0x000262e9   Thumb Code     2  arm_startup_nrf52.o(.text)
    HardFault_Handler                        0x000262eb   Thumb Code     2  arm_startup_nrf52.o(.text)
    MemoryManagement_Handler                 0x000262ed   Thumb Code     2  arm_startup_nrf52.o(.text)
    BusFault_Handler                         0x000262ef   Thumb Code     2  arm_startup_nrf52.o(.text)
    UsageFault_Handler                       0x000262f1   Thumb Code     2  arm_startup_nrf52.o(.text)
    SVC_Handler                              0x000262f3   Thumb Code     2  arm_startup_nrf52.o(.text)
    DebugMon_Handler                         0x000262f5   Thumb Code     2  arm_startup_nrf52.o(.text)
    PendSV_Handler                           0x000262f7   Thumb Code     2  arm_startup_nrf52.o(.text)
    SysTick_Handler                          0x000262f9   Thumb Code     2  arm_startup_nrf52.o(.text)
    CCM_AAR_IRQHandler                       0x000262fb   Thumb Code     0  arm_startup_nrf52.o(.text)
    COMP_LPCOMP_IRQHandler                   0x000262fb   Thumb Code     0  arm_startup_nrf52.o(.text)
    ECB_IRQHandler                           0x000262fb   Thumb Code     0  arm_startup_nrf52.o(.text)
    FPU_IRQHandler                           0x000262fb   Thumb Code     0  arm_startup_nrf52.o(.text)
    I2S_IRQHandler                           0x000262fb   Thumb Code     0  arm_startup_nrf52.o(.text)
    MWU_IRQHandler                           0x000262fb   Thumb Code     0  arm_startup_nrf52.o(.text)
    NFCT_IRQHandler                          0x000262fb   Thumb Code     0  arm_startup_nrf52.o(.text)
    PDM_IRQHandler                           0x000262fb   Thumb Code     0  arm_startup_nrf52.o(.text)
    PWM0_IRQHandler                          0x000262fb   Thumb Code     0  arm_startup_nrf52.o(.text)
    PWM1_IRQHandler                          0x000262fb   Thumb Code     0  arm_startup_nrf52.o(.text)
    PWM2_IRQHandler                          0x000262fb   Thumb Code     0  arm_startup_nrf52.o(.text)
    QDEC_IRQHandler                          0x000262fb   Thumb Code     0  arm_startup_nrf52.o(.text)
    RADIO_IRQHandler                         0x000262fb   Thumb Code     0  arm_startup_nrf52.o(.text)
    RNG_IRQHandler                           0x000262fb   Thumb Code     0  arm_startup_nrf52.o(.text)
    RTC0_IRQHandler                          0x000262fb   Thumb Code     0  arm_startup_nrf52.o(.text)
    RTC2_IRQHandler                          0x000262fb   Thumb Code     0  arm_startup_nrf52.o(.text)
    SAADC_IRQHandler                         0x000262fb   Thumb Code     0  arm_startup_nrf52.o(.text)
    SPIM0_SPIS0_TWIM0_TWIS0_SPI0_TWI0_IRQHandler 0x000262fb   Thumb Code     0  arm_startup_nrf52.o(.text)
    SPIM1_SPIS1_TWIM1_TWIS1_SPI1_TWI1_IRQHandler 0x000262fb   Thumb Code     0  arm_startup_nrf52.o(.text)
    SPIM2_SPIS2_SPI2_IRQHandler              0x000262fb   Thumb Code     0  arm_startup_nrf52.o(.text)
    SWI0_EGU0_IRQHandler                     0x000262fb   Thumb Code     0  arm_startup_nrf52.o(.text)
    SWI1_EGU1_IRQHandler                     0x000262fb   Thumb Code     0  arm_startup_nrf52.o(.text)
    SWI3_EGU3_IRQHandler                     0x000262fb   Thumb Code     0  arm_startup_nrf52.o(.text)
    SWI4_EGU4_IRQHandler                     0x000262fb   Thumb Code     0  arm_startup_nrf52.o(.text)
    SWI5_EGU5_IRQHandler                     0x000262fb   Thumb Code     0  arm_startup_nrf52.o(.text)
    TEMP_IRQHandler                          0x000262fb   Thumb Code     0  arm_startup_nrf52.o(.text)
    TIMER0_IRQHandler                        0x000262fb   Thumb Code     0  arm_startup_nrf52.o(.text)
    TIMER1_IRQHandler                        0x000262fb   Thumb Code     0  arm_startup_nrf52.o(.text)
    TIMER2_IRQHandler                        0x000262fb   Thumb Code     0  arm_startup_nrf52.o(.text)
    TIMER3_IRQHandler                        0x000262fb   Thumb Code     0  arm_startup_nrf52.o(.text)
    TIMER4_IRQHandler                        0x000262fb   Thumb Code     0  arm_startup_nrf52.o(.text)
    WDT_IRQHandler                           0x000262fb   Thumb Code     0  arm_startup_nrf52.o(.text)
    __aeabi_uldivmod                         0x00026305   Thumb Code    98  uldiv.o(.text)
    __aeabi_llsl                             0x00026367   Thumb Code    30  llshl.o(.text)
    _ll_shift_l                              0x00026367   Thumb Code     0  llshl.o(.text)
    __aeabi_memcpy                           0x00026385   Thumb Code    36  memcpya.o(.text)
    __aeabi_memcpy4                          0x00026385   Thumb Code     0  memcpya.o(.text)
    __aeabi_memcpy8                          0x00026385   Thumb Code     0  memcpya.o(.text)
    __aeabi_memset                           0x000263a9   Thumb Code    14  memseta.o(.text)
    __aeabi_memset4                          0x000263a9   Thumb Code     0  memseta.o(.text)
    __aeabi_memset8                          0x000263a9   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr                           0x000263b7   Thumb Code     4  memseta.o(.text)
    __aeabi_memclr4                          0x000263b7   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr8                          0x000263b7   Thumb Code     0  memseta.o(.text)
    _memset$wrapper                          0x000263bb   Thumb Code    18  memseta.o(.text)
    memcmp                                   0x000263cd   Thumb Code    26  memcmp.o(.text)
    __aeabi_llsr                             0x000263e7   Thumb Code    32  llushr.o(.text)
    _ll_ushift_r                             0x000263e7   Thumb Code     0  llushr.o(.text)
    __scatterload                            0x00026409   Thumb Code    28  init.o(.text)
    __scatterload_rt2                        0x00026409   Thumb Code     0  init.o(.text)
    __decompress                             0x0002642d   Thumb Code     0  __dczerorl.o(.text)
    __decompress0                            0x0002642d   Thumb Code    58  __dczerorl.o(.text)
    GPIOTE_IRQHandler                        0x00026469   Thumb Code   154  nrfx_gpiote.o(i.GPIOTE_IRQHandler)
    POWER_CLOCK_IRQHandler                   0x0002650d   Thumb Code    82  nrfx_clock.o(i.POWER_CLOCK_IRQHandler)
    RTC1_IRQHandler                          0x00026565   Thumb Code    12  drv_rtc.o(i.RTC1_IRQHandler)
    SWI2_EGU2_IRQHandler                     0x00026575   Thumb Code     4  nrf_sdh.o(i.SWI2_EGU2_IRQHandler)
    SystemInit                               0x00026579   Thumb Code   726  system_nrf52.o(i.SystemInit)
    UARTE0_UART0_IRQHandler                  0x00026895   Thumb Code     6  nrfx_prs.o(i.UARTE0_UART0_IRQHandler)
    __scatterload_copy                       0x000268a1   Thumb Code    14  handlers.o(i.__scatterload_copy)
    __scatterload_null                       0x000268af   Thumb Code     2  handlers.o(i.__scatterload_null)
    __scatterload_zeroinit                   0x000268b1   Thumb Code    14  handlers.o(i.__scatterload_zeroinit)
    app_error_fault_handler                  0x0002691d   Thumb Code    40  app_error_weak.o(i.app_error_fault_handler)
    app_error_handler_bare                   0x0002694d   Thumb Code    22  app_error.o(i.app_error_handler_bare)
    app_fifo_get                             0x00026963   Thumb Code    22  app_fifo.o(i.app_fifo_get)
    app_fifo_init                            0x00026979   Thumb Code    32  app_fifo.o(i.app_fifo_init)
    app_fifo_put                             0x00026999   Thumb Code    26  app_fifo.o(i.app_fifo_put)
    app_timer_cnt_get                        0x000269b5   Thumb Code     6  app_timer2.o(i.app_timer_cnt_get)
    app_timer_init                           0x000269c1   Thumb Code    70  app_timer2.o(i.app_timer_init)
    app_timer_start                          0x00026a1d   Thumb Code    48  app_timer2.o(i.app_timer_start)
    app_timer_stop                           0x00026a4d   Thumb Code    12  app_timer2.o(i.app_timer_stop)
    app_uart_init                            0x00026a59   Thumb Code   148  app_uart_fifo.o(i.app_uart_init)
    app_uart_put                             0x00026afd   Thumb Code    70  app_uart_fifo.o(i.app_uart_put)
    app_util_critical_region_enter           0x00026b51   Thumb Code    64  app_util_platform.o(i.app_util_critical_region_enter)
    app_util_critical_region_exit            0x00026b99   Thumb Code    46  app_util_platform.o(i.app_util_critical_region_exit)
    ble_advdata_search                       0x00026cdd   Thumb Code    70  ble_advdata.o(i.ble_advdata_search)
    ble_advdata_uuid_find                    0x00026d23   Thumb Code   182  ble_advdata.o(i.ble_advdata_uuid_find)
    ble_db_discovery_evt_register            0x00026dd9   Thumb Code    56  ble_db_discovery.o(i.ble_db_discovery_evt_register)
    ble_db_discovery_init                    0x00026e19   Thumb Code    32  ble_db_discovery.o(i.ble_db_discovery_init)
    ble_db_discovery_on_ble_evt              0x00026e3d   Thumb Code    86  ble_db_discovery.o(i.ble_db_discovery_on_ble_evt)
    ble_db_discovery_start                   0x00026e99   Thumb Code    34  ble_db_discovery.o(i.ble_db_discovery_start)
    ble_nus_c_handles_assign                 0x0002702d   Thumb Code    28  ble_nus_c.o(i.ble_nus_c_handles_assign)
    ble_nus_c_init                           0x00027049   Thumb Code    84  ble_nus_c.o(i.ble_nus_c_init)
    ble_nus_c_on_ble_evt                     0x000270a1   Thumb Code   106  ble_nus_c.o(i.ble_nus_c_on_ble_evt)
    ble_nus_c_on_db_disc_evt                 0x0002710b   Thumb Code   128  ble_nus_c.o(i.ble_nus_c_on_db_disc_evt)
    ble_nus_c_tx_notif_enable                0x0002718d   Thumb Code   102  ble_nus_c.o(i.ble_nus_c_tx_notif_enable)
    bsp_board_button_idx_to_pin              0x000271f9   Thumb Code     6  boards.o(i.bsp_board_button_idx_to_pin)
    bsp_board_led_invert                     0x00027205   Thumb Code    32  boards.o(i.bsp_board_led_invert)
    bsp_board_led_off                        0x00027229   Thumb Code    10  boards.o(i.bsp_board_led_off)
    bsp_board_led_on                         0x00027239   Thumb Code    10  boards.o(i.bsp_board_led_on)
    bsp_board_led_state_get                  0x00027249   Thumb Code    28  boards.o(i.bsp_board_led_state_get)
    bsp_board_leds_off                       0x00027269   Thumb Code    18  boards.o(i.bsp_board_leds_off)
    bsp_board_leds_on                        0x0002727b   Thumb Code    18  boards.o(i.bsp_board_leds_on)
    bsp_board_pin_to_button_idx              0x0002728d   Thumb Code    30  boards.o(i.bsp_board_pin_to_button_idx)
    bsp_btn_ble_sleep_mode_prepare           0x000272b1   Thumb Code    30  bsp_btn_ble.o(i.bsp_btn_ble_sleep_mode_prepare)
    bsp_event_to_button_action_assign        0x00027361   Thumb Code    64  bsp.o(i.bsp_event_to_button_action_assign)
    bsp_indication_set                       0x000273a5   Thumb Code    20  bsp.o(i.bsp_indication_set)
    bsp_wakeup_button_enable                 0x00027585   Thumb Code     6  bsp.o(i.bsp_wakeup_button_enable)
    drv_rtc_compare_disable                  0x00027915   Thumb Code    20  drv_rtc.o(i.drv_rtc_compare_disable)
    drv_rtc_compare_pending                  0x00027929   Thumb Code    14  drv_rtc.o(i.drv_rtc_compare_pending)
    drv_rtc_compare_set                      0x00027937   Thumb Code    80  drv_rtc.o(i.drv_rtc_compare_set)
    drv_rtc_counter_get                      0x00027987   Thumb Code     8  drv_rtc.o(i.drv_rtc_counter_get)
    drv_rtc_init                             0x00027991   Thumb Code   118  drv_rtc.o(i.drv_rtc_init)
    drv_rtc_irq_trigger                      0x00027a0d   Thumb Code    30  drv_rtc.o(i.drv_rtc_irq_trigger)
    drv_rtc_overflow_enable                  0x00027a2b   Thumb Code     8  drv_rtc.o(i.drv_rtc_overflow_enable)
    drv_rtc_overflow_pending                 0x00027a33   Thumb Code     8  drv_rtc.o(i.drv_rtc_overflow_pending)
    drv_rtc_start                            0x00027a3b   Thumb Code     8  drv_rtc.o(i.drv_rtc_start)
    drv_rtc_stop                             0x00027a43   Thumb Code     8  drv_rtc.o(i.drv_rtc_stop)
    drv_rtc_windowed_compare_set             0x00027a4b   Thumb Code   222  drv_rtc.o(i.drv_rtc_windowed_compare_set)
    gatt_evt_handler                         0x00027b81   Thumb Code    20  main.o(i.gatt_evt_handler)
    gatt_init                                0x00027b99   Thumb Code    38  main.o(i.gatt_init)
    is_whitelist_used                        0x00027d3d   Thumb Code    22  nrf_ble_scan.o(i.is_whitelist_used)
    main                                     0x00027d9d   Thumb Code   512  main.o(i.main)
    nrf_atfifo_init                          0x00028043   Thumb Code    38  nrf_atfifo.o(i.nrf_atfifo_init)
    nrf_atfifo_item_alloc                    0x00028069   Thumb Code    22  nrf_atfifo.o(i.nrf_atfifo_item_alloc)
    nrf_atfifo_item_free                     0x0002807f   Thumb Code    22  nrf_atfifo.o(i.nrf_atfifo_item_free)
    nrf_atfifo_item_get                      0x00028095   Thumb Code    22  nrf_atfifo.o(i.nrf_atfifo_item_get)
    nrf_atfifo_item_put                      0x000280ab   Thumb Code    22  nrf_atfifo.o(i.nrf_atfifo_item_put)
    nrf_balloc_alloc                         0x000280c1   Thumb Code    68  nrf_balloc.o(i.nrf_balloc_alloc)
    nrf_balloc_free                          0x00028105   Thumb Code    48  nrf_balloc.o(i.nrf_balloc_free)
    nrf_balloc_init                          0x00028135   Thumb Code    48  nrf_balloc.o(i.nrf_balloc_init)
    nrf_ble_gatt_att_mtu_central_set         0x00028175   Thumb Code    24  nrf_ble_gatt.o(i.nrf_ble_gatt_att_mtu_central_set)
    nrf_ble_gatt_init                        0x0002818d   Thumb Code    34  nrf_ble_gatt.o(i.nrf_ble_gatt_init)
    nrf_ble_gatt_on_ble_evt                  0x000281b1   Thumb Code   366  nrf_ble_gatt.o(i.nrf_ble_gatt_on_ble_evt)
    nrf_ble_gq_conn_handle_register          0x00028325   Thumb Code   110  nrf_ble_gq.o(i.nrf_ble_gq_conn_handle_register)
    nrf_ble_gq_item_add                      0x00028395   Thumb Code   154  nrf_ble_gq.o(i.nrf_ble_gq_item_add)
    nrf_ble_gq_on_ble_evt                    0x00028435   Thumb Code    96  nrf_ble_gq.o(i.nrf_ble_gq_on_ble_evt)
    nrf_ble_scan_filter_set                  0x00028513   Thumb Code    66  nrf_ble_scan.o(i.nrf_ble_scan_filter_set)
    nrf_ble_scan_filters_disable             0x00028555   Thumb Code    14  nrf_ble_scan.o(i.nrf_ble_scan_filters_disable)
    nrf_ble_scan_filters_enable              0x00028563   Thumb Code    54  nrf_ble_scan.o(i.nrf_ble_scan_filters_enable)
    nrf_ble_scan_init                        0x00028599   Thumb Code   106  nrf_ble_scan.o(i.nrf_ble_scan_init)
    nrf_ble_scan_on_ble_evt                  0x000286ff   Thumb Code   128  nrf_ble_scan.o(i.nrf_ble_scan_on_ble_evt)
    nrf_ble_scan_start                       0x0002877f   Thumb Code    68  nrf_ble_scan.o(i.nrf_ble_scan_start)
    nrf_drv_clock_init                       0x000287e1   Thumb Code    56  nrf_drv_clock.o(i.nrf_drv_clock_init)
    nrf_drv_clock_lfclk_release              0x00028821   Thumb Code    48  nrf_drv_clock.o(i.nrf_drv_clock_lfclk_release)
    nrf_drv_uart_init                        0x00028855   Thumb Code    90  nrf_drv_uart.o(i.nrf_drv_uart_init)
    nrf_memobj_alloc                         0x00028a1d   Thumb Code    96  nrf_memobj.o(i.nrf_memobj_alloc)
    nrf_memobj_free                          0x00028a7d   Thumb Code    50  nrf_memobj.o(i.nrf_memobj_free)
    nrf_memobj_pool_init                     0x00028aaf   Thumb Code     4  nrf_memobj.o(i.nrf_memobj_pool_init)
    nrf_memobj_read                          0x00028ab3   Thumb Code    16  nrf_memobj.o(i.nrf_memobj_read)
    nrf_memobj_write                         0x00028ac3   Thumb Code    16  nrf_memobj.o(i.nrf_memobj_write)
    nrf_pwr_mgmt_init                        0x00028ad5   Thumb Code    28  nrf_pwr_mgmt.o(i.nrf_pwr_mgmt_init)
    nrf_pwr_mgmt_run                         0x00028afd   Thumb Code    64  nrf_pwr_mgmt.o(i.nrf_pwr_mgmt_run)
    nrf_queue_generic_pop                    0x00028b41   Thumb Code   142  nrf_queue.o(i.nrf_queue_generic_pop)
    nrf_queue_is_empty                       0x00028bcf   Thumb Code    18  nrf_queue.o(i.nrf_queue_is_empty)
    nrf_queue_is_full                        0x00028be1   Thumb Code    30  nrf_queue.o(i.nrf_queue_is_full)
    nrf_queue_push                           0x00028c0d   Thumb Code   178  nrf_queue.o(i.nrf_queue_push)
    nrf_sdh_ble_app_ram_start_get            0x00028ccd   Thumb Code    16  nrf_sdh_ble.o(i.nrf_sdh_ble_app_ram_start_get)
    nrf_sdh_ble_default_cfg_set              0x00028ce1   Thumb Code   156  nrf_sdh_ble.o(i.nrf_sdh_ble_default_cfg_set)
    nrf_sdh_ble_enable                       0x00028d7d   Thumb Code    14  nrf_sdh_ble.o(i.nrf_sdh_ble_enable)
    nrf_sdh_enable_request                   0x00028df1   Thumb Code   102  nrf_sdh.o(i.nrf_sdh_enable_request)
    nrf_sdh_evts_poll                        0x00028e65   Thumb Code    32  nrf_sdh.o(i.nrf_sdh_evts_poll)
    nrf_sdh_is_enabled                       0x00028e89   Thumb Code     6  nrf_sdh.o(i.nrf_sdh_is_enabled)
    nrf_section_iter_init                    0x00028ed1   Thumb Code    10  nrf_section_iter.o(i.nrf_section_iter_init)
    nrf_section_iter_next                    0x00028eff   Thumb Code    32  nrf_section_iter.o(i.nrf_section_iter_next)
    nrf_sortlist_add                         0x00028f1f   Thumb Code    34  nrf_sortlist.o(i.nrf_sortlist_add)
    nrf_sortlist_peek                        0x00028f41   Thumb Code     6  nrf_sortlist.o(i.nrf_sortlist_peek)
    nrf_sortlist_pop                         0x00028f47   Thumb Code    14  nrf_sortlist.o(i.nrf_sortlist_pop)
    nrf_sortlist_remove                      0x00028f55   Thumb Code    30  nrf_sortlist.o(i.nrf_sortlist_remove)
    nrfx_clock_enable                        0x00028fbd   Thumb Code    34  nrfx_clock.o(i.nrfx_clock_enable)
    nrfx_clock_init                          0x00028fe9   Thumb Code    26  nrfx_clock.o(i.nrfx_clock_init)
    nrfx_clock_lfclk_stop                    0x00029009   Thumb Code    38  nrfx_clock.o(i.nrfx_clock_lfclk_stop)
    nrfx_prs_acquire                         0x00029059   Thumb Code    58  nrfx_prs.o(i.nrfx_prs_acquire)
    nrfx_uart_0_irq_handler                  0x00029095   Thumb Code     8  nrfx_uart.o(i.nrfx_uart_0_irq_handler)
    nrfx_uart_init                           0x000290a5   Thumb Code   200  nrfx_uart.o(i.nrfx_uart_init)
    nrfx_uart_rx                             0x00029179   Thumb Code   230  nrfx_uart.o(i.nrfx_uart_rx)
    nrfx_uart_tx                             0x00029265   Thumb Code   162  nrfx_uart.o(i.nrfx_uart_tx)
    nrfx_uart_tx_in_progress                 0x0002930d   Thumb Code    26  nrfx_uart.o(i.nrfx_uart_tx_in_progress)
    nrfx_uarte_0_irq_handler                 0x0002932d   Thumb Code     8  nrfx_uarte.o(i.nrfx_uarte_0_irq_handler)
    nrfx_uarte_init                          0x0002933d   Thumb Code   100  nrfx_uarte.o(i.nrfx_uarte_init)
    nrfx_uarte_rx                            0x000293a9   Thumb Code   256  nrfx_uarte.o(i.nrfx_uarte_rx)
    nrfx_uarte_tx                            0x000294ad   Thumb Code   166  nrfx_uarte.o(i.nrfx_uarte_tx)
    nrfx_uarte_tx_in_progress                0x00029559   Thumb Code    22  nrfx_uarte.o(i.nrfx_uarte_tx_in_progress)
    uart_event_handle                        0x0002a049   Thumb Code     2  main.o(i.uart_event_handle)
    m_ram_start                              0x0002a5d4   Data           4  nrf_sdh_ble.o(.constdata)
    Region$$Table$$Base                      0x0002a600   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x0002a620   Number         0  anon$$obj.o(Region$$Table)
    m_ble_gatt_queuepool                     0x0002a620   Data          20  main.o(nrf_balloc)
    m_ble_gatt_queuepurge_queue              0x0002a634   Data          20  main.o(nrf_queue)
    pwr_mgmt_data1$$Base                     0x0002a648   Number         0  main.o(pwr_mgmt_data1)
    pwr_mgmt_data1$$Limit                    0x0002a64c   Number         0  main.o(pwr_mgmt_data1)
    sdh_ble_observers1$$Base                 0x0002a64c   Number         0  main.o(sdh_ble_observers1)
    sdh_ble_observers1$$Limit                0x0002a674   Number         0  bsp_btn_ble.o(sdh_ble_observers1)
    sdh_ble_observers2$$Base                 0x0002a674   Number         0  main.o(sdh_ble_observers2)
    sdh_ble_observers2$$Limit                0x0002a67c   Number         0  main.o(sdh_ble_observers2)
    sdh_ble_observers3$$Base                 0x0002a67c   Number         0  main.o(sdh_ble_observers3)
    sdh_ble_observers3$$Limit                0x0002a684   Number         0  main.o(sdh_ble_observers3)
    sdh_soc_observers0$$Base                 0x0002a684   Number         0  nrf_drv_clock.o(sdh_soc_observers0)
    sdh_soc_observers0$$Limit                0x0002a68c   Number         0  nrf_drv_clock.o(sdh_soc_observers0)
    sdh_stack_observers0$$Base               0x0002a68c   Number         0  nrf_sdh_ble.o(sdh_stack_observers0)
    sdh_stack_observers0$$Limit              0x0002a69c   Number         0  nrf_sdh_soc.o(sdh_stack_observers0)
    sdh_state_observers0$$Base               0x0002a69c   Number         0  nrf_drv_clock.o(sdh_state_observers0)
    sdh_state_observers0$$Limit              0x0002a6a4   Number         0  nrf_drv_clock.o(sdh_state_observers0)
    Image$$RW_IRAM1$$Base                    0x20002b28   Number         0  anon$$obj.o ABSOLUTE
    ble_receive_data_flag                    0x20002b28   Data           1  main.o(.data)
    ble_receive_data_length                  0x20002b2c   Data           2  main.o(.data)
    nrf_drv_uart_use_easy_dma                0x20003154   Data           1  nrf_drv_uart.o(.data)
    SystemCoreClock                          0x200031c8   Data           4  system_nrf52.o(.data)
    ble_receive_data                         0x200031cc   Data         244  main.o(.bss)
    nrf_nvic_state                           0x20003e88   Data          12  app_util_platform.o(.bss)
    __initial_sp                             0x20005ea0   Data           0  arm_startup_nrf52.o(STACK)



==============================================================================

Memory Map of the image

  Image Entry point : 0x00026201

  Load Region LR_IROM1 (Base: 0x00026000, Size: 0x00004d48, Max: 0x0005a000, ABSOLUTE, COMPRESSED[0x000046e0])

    Execution Region ER_IROM1 (Exec base: 0x00026000, Load base: 0x00026000, Size: 0x000046a4, Max: 0x0005a000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x00026000   0x00026000   0x00000200   Data   RO         4753    RESET               arm_startup_nrf52.o
    0x00026200   0x00026200   0x00000000   Code   RO         4794  * .ARM.Collect$$$$00000000  mc_w.l(entry.o)
    0x00026200   0x00026200   0x00000004   Code   RO         4813    .ARM.Collect$$$$00000001  mc_w.l(entry2.o)
    0x00026204   0x00026204   0x00000004   Code   RO         4816    .ARM.Collect$$$$00000004  mc_w.l(entry5.o)
    0x00026208   0x00026208   0x00000000   Code   RO         4818    .ARM.Collect$$$$00000008  mc_w.l(entry7b.o)
    0x00026208   0x00026208   0x00000000   Code   RO         4820    .ARM.Collect$$$$0000000A  mc_w.l(entry8b.o)
    0x00026208   0x00026208   0x00000008   Code   RO         4821    .ARM.Collect$$$$0000000B  mc_w.l(entry9a.o)
    0x00026210   0x00026210   0x00000004   Code   RO         4828    .ARM.Collect$$$$0000000E  mc_w.l(entry12b.o)
    0x00026214   0x00026214   0x00000000   Code   RO         4823    .ARM.Collect$$$$0000000F  mc_w.l(entry10a.o)
    0x00026214   0x00026214   0x00000000   Code   RO         4825    .ARM.Collect$$$$00000011  mc_w.l(entry11a.o)
    0x00026214   0x00026214   0x00000004   Code   RO         4814    .ARM.Collect$$$$00002712  mc_w.l(entry2.o)
    0x00026218   0x00026218   0x000000c8   Code   RO         3386    .emb_text           nrf_atfifo.o
    0x000262e0   0x000262e0   0x00000024   Code   RO         4754    .text               arm_startup_nrf52.o
    0x00026304   0x00026304   0x00000062   Code   RO         4797    .text               mc_w.l(uldiv.o)
    0x00026366   0x00026366   0x0000001e   Code   RO         4799    .text               mc_w.l(llshl.o)
    0x00026384   0x00026384   0x00000024   Code   RO         4801    .text               mc_w.l(memcpya.o)
    0x000263a8   0x000263a8   0x00000024   Code   RO         4803    .text               mc_w.l(memseta.o)
    0x000263cc   0x000263cc   0x0000001a   Code   RO         4807    .text               mc_w.l(memcmp.o)
    0x000263e6   0x000263e6   0x00000020   Code   RO         4829    .text               mc_w.l(llushr.o)
    0x00026406   0x00026406   0x00000002   PAD
    0x00026408   0x00026408   0x00000024   Code   RO         4831    .text               mc_w.l(init.o)
    0x0002642c   0x0002642c   0x0000003a   Code   RO         4841    .text               mc_w.l(__dczerorl.o)
    0x00026466   0x00026466   0x00000002   PAD
    0x00026468   0x00026468   0x000000a4   Code   RO         1887    i.GPIOTE_IRQHandler  nrfx_gpiote.o
    0x0002650c   0x0002650c   0x00000058   Code   RO         1751    i.POWER_CLOCK_IRQHandler  nrfx_clock.o
    0x00026564   0x00026564   0x00000010   Code   RO         3176    i.RTC1_IRQHandler   drv_rtc.o
    0x00026574   0x00026574   0x00000004   Code   RO         4553    i.SWI2_EGU2_IRQHandler  nrf_sdh.o
    0x00026578   0x00026578   0x0000031c   Code   RO         4762    i.SystemInit        system_nrf52.o
    0x00026894   0x00026894   0x0000000c   Code   RO         2189    i.UARTE0_UART0_IRQHandler  nrfx_prs.o
    0x000268a0   0x000268a0   0x0000000e   Code   RO         4835    i.__scatterload_copy  mc_w.l(handlers.o)
    0x000268ae   0x000268ae   0x00000002   Code   RO         4836    i.__scatterload_null  mc_w.l(handlers.o)
    0x000268b0   0x000268b0   0x0000000e   Code   RO         4837    i.__scatterload_zeroinit  mc_w.l(handlers.o)
    0x000268be   0x000268be   0x00000002   PAD
    0x000268c0   0x000268c0   0x00000024   Code   RO         4554    i.__sd_nvic_app_accessible_irq  nrf_sdh.o
    0x000268e4   0x000268e4   0x00000036   Code   RO          635    i.advertising_buttons_configure  bsp_btn_ble.o
    0x0002691a   0x0002691a   0x00000002   PAD
    0x0002691c   0x0002691c   0x00000030   Code   RO         2734    i.app_error_fault_handler  app_error_weak.o
    0x0002694c   0x0002694c   0x00000016   Code   RO         2678    i.app_error_handler_bare  app_error.o
    0x00026962   0x00026962   0x00000016   Code   RO         2769    i.app_fifo_get      app_fifo.o
    0x00026978   0x00026978   0x00000020   Code   RO         2770    i.app_fifo_init     app_fifo.o
    0x00026998   0x00026998   0x0000001a   Code   RO         2772    i.app_fifo_put      app_fifo.o
    0x000269b2   0x000269b2   0x00000002   PAD
    0x000269b4   0x000269b4   0x0000000c   Code   RO         2893    i.app_timer_cnt_get  app_timer2.o
    0x000269c0   0x000269c0   0x0000005c   Code   RO         2895    i.app_timer_init    app_timer2.o
    0x00026a1c   0x00026a1c   0x00000030   Code   RO         2898    i.app_timer_start   app_timer2.o
    0x00026a4c   0x00026a4c   0x0000000c   Code   RO         2899    i.app_timer_stop    app_timer2.o
    0x00026a58   0x00026a58   0x000000a4   Code   RO         3034    i.app_uart_init     app_uart_fifo.o
    0x00026afc   0x00026afc   0x00000054   Code   RO         3035    i.app_uart_put      app_uart_fifo.o
    0x00026b50   0x00026b50   0x00000048   Code   RO         3108    i.app_util_critical_region_enter  app_util_platform.o
    0x00026b98   0x00026b98   0x00000034   Code   RO         3109    i.app_util_critical_region_exit  app_util_platform.o
    0x00026bcc   0x00026bcc   0x00000088   Code   RO         2240    i.apply_config      nrfx_uart.o
    0x00026c54   0x00026c54   0x00000088   Code   RO         2425    i.apply_config      nrfx_uarte.o
    0x00026cdc   0x00026cdc   0x00000046   Code   RO          742    i.ble_advdata_search  ble_advdata.o
    0x00026d22   0x00026d22   0x000000b6   Code   RO          744    i.ble_advdata_uuid_find  ble_advdata.o
    0x00026dd8   0x00026dd8   0x00000040   Code   RO          852    i.ble_db_discovery_evt_register  ble_db_discovery.o
    0x00026e18   0x00026e18   0x00000024   Code   RO          853    i.ble_db_discovery_init  ble_db_discovery.o
    0x00026e3c   0x00026e3c   0x0000005c   Code   RO          854    i.ble_db_discovery_on_ble_evt  ble_db_discovery.o
    0x00026e98   0x00026e98   0x00000028   Code   RO          855    i.ble_db_discovery_start  ble_db_discovery.o
    0x00026ec0   0x00026ec0   0x0000009c   Code   RO            5    i.ble_evt_handler   main.o
    0x00026f5c   0x00026f5c   0x00000070   Code   RO          636    i.ble_evt_handler   bsp_btn_ble.o
    0x00026fcc   0x00026fcc   0x00000060   Code   RO            6    i.ble_nus_c_evt_handler  main.o
    0x0002702c   0x0002702c   0x0000001c   Code   RO         1336    i.ble_nus_c_handles_assign  ble_nus_c.o
    0x00027048   0x00027048   0x00000058   Code   RO         1337    i.ble_nus_c_init    ble_nus_c.o
    0x000270a0   0x000270a0   0x0000006a   Code   RO         1338    i.ble_nus_c_on_ble_evt  ble_nus_c.o
    0x0002710a   0x0002710a   0x00000080   Code   RO         1339    i.ble_nus_c_on_db_disc_evt  ble_nus_c.o
    0x0002718a   0x0002718a   0x00000002   PAD
    0x0002718c   0x0002718c   0x0000006c   Code   RO         1341    i.ble_nus_c_tx_notif_enable  ble_nus_c.o
    0x000271f8   0x000271f8   0x0000000c   Code   RO          405    i.bsp_board_button_idx_to_pin  boards.o
    0x00027204   0x00027204   0x00000024   Code   RO          409    i.bsp_board_led_invert  boards.o
    0x00027228   0x00027228   0x00000010   Code   RO          410    i.bsp_board_led_off  boards.o
    0x00027238   0x00027238   0x00000010   Code   RO          411    i.bsp_board_led_on  boards.o
    0x00027248   0x00027248   0x00000020   Code   RO          412    i.bsp_board_led_state_get  boards.o
    0x00027268   0x00027268   0x00000012   Code   RO          413    i.bsp_board_leds_off  boards.o
    0x0002727a   0x0002727a   0x00000012   Code   RO          414    i.bsp_board_leds_on  boards.o
    0x0002728c   0x0002728c   0x00000024   Code   RO          415    i.bsp_board_pin_to_button_idx  boards.o
    0x000272b0   0x000272b0   0x0000001e   Code   RO          638    i.bsp_btn_ble_sleep_mode_prepare  bsp_btn_ble.o
    0x000272ce   0x000272ce   0x00000002   PAD
    0x000272d0   0x000272d0   0x00000090   Code   RO          519    i.bsp_button_event_handler  bsp.o
    0x00027360   0x00027360   0x00000044   Code   RO          523    i.bsp_event_to_button_action_assign  bsp.o
    0x000273a4   0x000273a4   0x00000018   Code   RO          524    i.bsp_indication_set  bsp.o
    0x000273bc   0x000273bc   0x000001c8   Code   RO          526    i.bsp_led_indication  bsp.o
    0x00027584   0x00027584   0x00000006   Code   RO          528    i.bsp_wakeup_button_enable  bsp.o
    0x0002758a   0x0002758a   0x00000002   PAD
    0x0002758c   0x0002758c   0x00000010   Code   RO         1890    i.channel_port_get  nrfx_gpiote.o
    0x0002759c   0x0002759c   0x0000006c   Code   RO          856    i.characteristics_discover  ble_db_discovery.o
    0x00027608   0x00027608   0x00000028   Code   RO         1406    i.clock_clk_started_notify  nrf_drv_clock.o
    0x00027630   0x00027630   0x0000001c   Code   RO         1407    i.clock_irq_handler  nrf_drv_clock.o
    0x0002764c   0x0002764c   0x00000018   Code   RO         2901    i.compare_func      app_timer2.o
    0x00027664   0x00027664   0x0000001e   Code   RO         1144    i.conn_handle_id_find  nrf_ble_gq.o
    0x00027682   0x00027682   0x0000001e   Code   RO         1054    i.data_length_update  nrf_ble_gatt.o
    0x000276a0   0x000276a0   0x0000000c   Code   RO            8    i.db_disc_handler   main.o
    0x000276ac   0x000276ac   0x000000b8   Code   RO          857    i.descriptors_discover  ble_db_discovery.o
    0x00027764   0x00027764   0x00000030   Code   RO          858    i.discovery_available_evt_trigger  ble_db_discovery.o
    0x00027794   0x00027794   0x000000b4   Code   RO          859    i.discovery_complete_evt_trigger  ble_db_discovery.o
    0x00027848   0x00027848   0x00000050   Code   RO          860    i.discovery_error_handler  ble_db_discovery.o
    0x00027898   0x00027898   0x0000007c   Code   RO          861    i.discovery_start   ble_db_discovery.o
    0x00027914   0x00027914   0x00000014   Code   RO         3177    i.drv_rtc_compare_disable  drv_rtc.o
    0x00027928   0x00027928   0x0000000e   Code   RO         3180    i.drv_rtc_compare_pending  drv_rtc.o
    0x00027936   0x00027936   0x00000050   Code   RO         3181    i.drv_rtc_compare_set  drv_rtc.o
    0x00027986   0x00027986   0x00000008   Code   RO         3182    i.drv_rtc_counter_get  drv_rtc.o
    0x0002798e   0x0002798e   0x00000002   PAD
    0x00027990   0x00027990   0x0000007c   Code   RO         3183    i.drv_rtc_init      drv_rtc.o
    0x00027a0c   0x00027a0c   0x0000001e   Code   RO         3184    i.drv_rtc_irq_trigger  drv_rtc.o
    0x00027a2a   0x00027a2a   0x00000008   Code   RO         3186    i.drv_rtc_overflow_enable  drv_rtc.o
    0x00027a32   0x00027a32   0x00000008   Code   RO         3187    i.drv_rtc_overflow_pending  drv_rtc.o
    0x00027a3a   0x00027a3a   0x00000008   Code   RO         3188    i.drv_rtc_start     drv_rtc.o
    0x00027a42   0x00027a42   0x00000008   Code   RO         3189    i.drv_rtc_stop      drv_rtc.o
    0x00027a4a   0x00027a4a   0x000000de   Code   RO         3194    i.drv_rtc_windowed_compare_set  drv_rtc.o
    0x00027b28   0x00027b28   0x00000012   Code   RO         3195    i.evt_enable        drv_rtc.o
    0x00027b3a   0x00027b3a   0x00000014   Code   RO         3196    i.evt_pending       drv_rtc.o
    0x00027b4e   0x00027b4e   0x00000014   Code   RO         2775    i.fifo_get          app_fifo.o
    0x00027b62   0x00027b62   0x00000012   Code   RO         2776    i.fifo_put          app_fifo.o
    0x00027b74   0x00027b74   0x0000000a   Code   RO         1342    i.gatt_error_handler  ble_nus_c.o
    0x00027b7e   0x00027b7e   0x00000002   PAD
    0x00027b80   0x00027b80   0x00000018   Code   RO            9    i.gatt_evt_handler  main.o
    0x00027b98   0x00027b98   0x00000030   Code   RO           10    i.gatt_init         main.o
    0x00027bc8   0x00027bc8   0x0000002c   Code   RO         1145    i.gattc_write_alloc  nrf_ble_gq.o
    0x00027bf4   0x00027bf4   0x0000003e   Code   RO         1146    i.gatts_hvx_alloc   nrf_ble_gq.o
    0x00027c32   0x00027c32   0x00000002   PAD
    0x00027c34   0x00027c34   0x00000034   Code   RO         2902    i.get_now           app_timer2.o
    0x00027c68   0x00027c68   0x00000088   Code   RO         2426    i.interrupts_enable  nrfx_uarte.o
    0x00027cf0   0x00027cf0   0x0000004c   Code   RO          862    i.is_desc_discovery_reqd  ble_db_discovery.o
    0x00027d3c   0x00027d3c   0x00000016   Code   RO         1221    i.is_whitelist_used  nrf_ble_scan.o
    0x00027d52   0x00027d52   0x00000002   PAD
    0x00027d54   0x00027d54   0x00000030   Code   RO          530    i.leds_off          bsp.o
    0x00027d84   0x00027d84   0x00000018   Code   RO         1055    i.link_init         nrf_ble_gatt.o
    0x00027d9c   0x00027d9c   0x00000228   Code   RO           11    i.main              main.o
    0x00027fc4   0x00027fc4   0x0000007e   Code   RO         3711    i.memobj_op         nrf_memobj.o
    0x00028042   0x00028042   0x00000026   Code   RO         3390    i.nrf_atfifo_init   nrf_atfifo.o
    0x00028068   0x00028068   0x00000016   Code   RO         3391    i.nrf_atfifo_item_alloc  nrf_atfifo.o
    0x0002807e   0x0002807e   0x00000016   Code   RO         3392    i.nrf_atfifo_item_free  nrf_atfifo.o
    0x00028094   0x00028094   0x00000016   Code   RO         3393    i.nrf_atfifo_item_get  nrf_atfifo.o
    0x000280aa   0x000280aa   0x00000016   Code   RO         3394    i.nrf_atfifo_item_put  nrf_atfifo.o
    0x000280c0   0x000280c0   0x00000044   Code   RO         3595    i.nrf_balloc_alloc  nrf_balloc.o
    0x00028104   0x00028104   0x00000030   Code   RO         3596    i.nrf_balloc_free   nrf_balloc.o
    0x00028134   0x00028134   0x00000030   Code   RO         3597    i.nrf_balloc_init   nrf_balloc.o
    0x00028164   0x00028164   0x00000010   Code   RO         1891    i.nrf_bitmask_bit_is_set  nrfx_gpiote.o
    0x00028174   0x00028174   0x00000018   Code   RO         1056    i.nrf_ble_gatt_att_mtu_central_set  nrf_ble_gatt.o
    0x0002818c   0x0002818c   0x00000022   Code   RO         1061    i.nrf_ble_gatt_init  nrf_ble_gatt.o
    0x000281ae   0x000281ae   0x00000002   PAD
    0x000281b0   0x000281b0   0x00000174   Code   RO         1062    i.nrf_ble_gatt_on_ble_evt  nrf_ble_gatt.o
    0x00028324   0x00028324   0x0000006e   Code   RO         1147    i.nrf_ble_gq_conn_handle_register  nrf_ble_gq.o
    0x00028392   0x00028392   0x00000002   PAD
    0x00028394   0x00028394   0x000000a0   Code   RO         1148    i.nrf_ble_gq_item_add  nrf_ble_gq.o
    0x00028434   0x00028434   0x00000060   Code   RO         1149    i.nrf_ble_gq_on_ble_evt  nrf_ble_gq.o
    0x00028494   0x00028494   0x0000004c   Code   RO         1223    i.nrf_ble_scan_connect_with_target  nrf_ble_scan.o
    0x000284e0   0x000284e0   0x00000014   Code   RO         1225    i.nrf_ble_scan_default_conn_param_set  nrf_ble_scan.o
    0x000284f4   0x000284f4   0x0000001e   Code   RO         1226    i.nrf_ble_scan_default_param_set  nrf_ble_scan.o
    0x00028512   0x00028512   0x00000042   Code   RO         1228    i.nrf_ble_scan_filter_set  nrf_ble_scan.o
    0x00028554   0x00028554   0x0000000e   Code   RO         1229    i.nrf_ble_scan_filters_disable  nrf_ble_scan.o
    0x00028562   0x00028562   0x00000036   Code   RO         1230    i.nrf_ble_scan_filters_enable  nrf_ble_scan.o
    0x00028598   0x00028598   0x0000006a   Code   RO         1231    i.nrf_ble_scan_init  nrf_ble_scan.o
    0x00028602   0x00028602   0x000000fc   Code   RO         1232    i.nrf_ble_scan_on_adv_report  nrf_ble_scan.o
    0x000286fe   0x000286fe   0x00000080   Code   RO         1233    i.nrf_ble_scan_on_ble_evt  nrf_ble_scan.o
    0x0002877e   0x0002877e   0x00000044   Code   RO         1235    i.nrf_ble_scan_start  nrf_ble_scan.o
    0x000287c2   0x000287c2   0x0000000e   Code   RO         1752    i.nrf_clock_event_check  nrfx_clock.o
    0x000287d0   0x000287d0   0x00000010   Code   RO         1753    i.nrf_clock_event_clear  nrfx_clock.o
    0x000287e0   0x000287e0   0x00000040   Code   RO         1414    i.nrf_drv_clock_init  nrf_drv_clock.o
    0x00028820   0x00028820   0x00000034   Code   RO         1418    i.nrf_drv_clock_lfclk_release  nrf_drv_clock.o
    0x00028854   0x00028854   0x00000068   Code   RO         1560    i.nrf_drv_uart_init  nrf_drv_uart.o
    0x000288bc   0x000288bc   0x00000020   Code   RO         3036    i.nrf_drv_uart_rx   app_uart_fifo.o
    0x000288dc   0x000288dc   0x00000020   Code   RO         3037    i.nrf_drv_uart_tx   app_uart_fifo.o
    0x000288fc   0x000288fc   0x00000024   Code   RO         2241    i.nrf_gpio_cfg      nrfx_uart.o
    0x00028920   0x00028920   0x00000024   Code   RO         2427    i.nrf_gpio_cfg      nrfx_uarte.o
    0x00028944   0x00028944   0x00000012   Code   RO         2243    i.nrf_gpio_cfg_input  nrfx_uart.o
    0x00028956   0x00028956   0x00000012   Code   RO         2429    i.nrf_gpio_cfg_input  nrfx_uarte.o
    0x00028968   0x00028968   0x00000014   Code   RO         2244    i.nrf_gpio_cfg_output  nrfx_uart.o
    0x0002897c   0x0002897c   0x00000014   Code   RO         2430    i.nrf_gpio_cfg_output  nrfx_uarte.o
    0x00028990   0x00028990   0x00000022   Code   RO         1894    i.nrf_gpio_cfg_sense_set  nrfx_gpiote.o
    0x000289b2   0x000289b2   0x00000002   PAD
    0x000289b4   0x000289b4   0x0000002c   Code   RO         1895    i.nrf_gpio_latches_read_and_clear  nrfx_gpiote.o
    0x000289e0   0x000289e0   0x00000016   Code   RO          418    i.nrf_gpio_pin_write  boards.o
    0x000289f6   0x000289f6   0x00000002   PAD
    0x000289f8   0x000289f8   0x00000014   Code   RO         1897    i.nrf_gpiote_event_clear  nrfx_gpiote.o
    0x00028a0c   0x00028a0c   0x00000010   Code   RO         1898    i.nrf_gpiote_event_is_set  nrfx_gpiote.o
    0x00028a1c   0x00028a1c   0x00000060   Code   RO         3712    i.nrf_memobj_alloc  nrf_memobj.o
    0x00028a7c   0x00028a7c   0x00000032   Code   RO         3713    i.nrf_memobj_free   nrf_memobj.o
    0x00028aae   0x00028aae   0x00000004   Code   RO         3715    i.nrf_memobj_pool_init  nrf_memobj.o
    0x00028ab2   0x00028ab2   0x00000010   Code   RO         3717    i.nrf_memobj_read   nrf_memobj.o
    0x00028ac2   0x00028ac2   0x00000010   Code   RO         3718    i.nrf_memobj_write  nrf_memobj.o
    0x00028ad2   0x00028ad2   0x00000002   PAD
    0x00028ad4   0x00028ad4   0x00000028   Code   RO         3790    i.nrf_pwr_mgmt_init  nrf_pwr_mgmt.o
    0x00028afc   0x00028afc   0x00000044   Code   RO         3791    i.nrf_pwr_mgmt_run  nrf_pwr_mgmt.o
    0x00028b40   0x00028b40   0x0000008e   Code   RO         3861    i.nrf_queue_generic_pop  nrf_queue.o
    0x00028bce   0x00028bce   0x00000012   Code   RO         3863    i.nrf_queue_is_empty  nrf_queue.o
    0x00028be0   0x00028be0   0x0000001e   Code   RO         3864    i.nrf_queue_is_full  nrf_queue.o
    0x00028bfe   0x00028bfe   0x0000000e   Code   RO         3867    i.nrf_queue_next_idx  nrf_queue.o
    0x00028c0c   0x00028c0c   0x000000b2   Code   RO         3869    i.nrf_queue_push    nrf_queue.o
    0x00028cbe   0x00028cbe   0x0000000c   Code   RO         3197    i.nrf_rtc_event_clear  drv_rtc.o
    0x00028cca   0x00028cca   0x00000002   PAD
    0x00028ccc   0x00028ccc   0x00000014   Code   RO         4671    i.nrf_sdh_ble_app_ram_start_get  nrf_sdh_ble.o
    0x00028ce0   0x00028ce0   0x0000009c   Code   RO         4672    i.nrf_sdh_ble_default_cfg_set  nrf_sdh_ble.o
    0x00028d7c   0x00028d7c   0x00000014   Code   RO         4673    i.nrf_sdh_ble_enable  nrf_sdh_ble.o
    0x00028d90   0x00028d90   0x00000060   Code   RO         4674    i.nrf_sdh_ble_evts_poll  nrf_sdh_ble.o
    0x00028df0   0x00028df0   0x00000074   Code   RO         4556    i.nrf_sdh_enable_request  nrf_sdh.o
    0x00028e64   0x00028e64   0x00000024   Code   RO         4557    i.nrf_sdh_evts_poll  nrf_sdh.o
    0x00028e88   0x00028e88   0x0000000c   Code   RO         4558    i.nrf_sdh_is_enabled  nrf_sdh.o
    0x00028e94   0x00028e94   0x0000003c   Code   RO         4722    i.nrf_sdh_soc_evts_poll  nrf_sdh_soc.o
    0x00028ed0   0x00028ed0   0x0000000a   Code   RO         4040    i.nrf_section_iter_init  nrf_section_iter.o
    0x00028eda   0x00028eda   0x00000024   Code   RO         4041    i.nrf_section_iter_item_set  nrf_section_iter.o
    0x00028efe   0x00028efe   0x00000020   Code   RO         4042    i.nrf_section_iter_next  nrf_section_iter.o
    0x00028f1e   0x00028f1e   0x00000022   Code   RO         4075    i.nrf_sortlist_add  nrf_sortlist.o
    0x00028f40   0x00028f40   0x00000006   Code   RO         4077    i.nrf_sortlist_peek  nrf_sortlist.o
    0x00028f46   0x00028f46   0x0000000e   Code   RO         4078    i.nrf_sortlist_pop  nrf_sortlist.o
    0x00028f54   0x00028f54   0x0000001e   Code   RO         4079    i.nrf_sortlist_remove  nrf_sortlist.o
    0x00028f72   0x00028f72   0x0000000a   Code   RO         2245    i.nrf_uart_event_check  nrfx_uart.o
    0x00028f7c   0x00028f7c   0x0000000c   Code   RO         2246    i.nrf_uart_event_clear  nrfx_uart.o
    0x00028f88   0x00028f88   0x0000000c   Code   RO         2247    i.nrf_uart_int_enable_check  nrfx_uart.o
    0x00028f94   0x00028f94   0x0000000a   Code   RO         2431    i.nrf_uarte_event_check  nrfx_uarte.o
    0x00028f9e   0x00028f9e   0x0000000c   Code   RO         2432    i.nrf_uarte_event_clear  nrfx_uarte.o
    0x00028faa   0x00028faa   0x00000002   PAD
    0x00028fac   0x00028fac   0x00000010   Code   RO         1421    i.nrf_wdt_started   nrf_drv_clock.o
    0x00028fbc   0x00028fbc   0x0000002c   Code   RO         1758    i.nrfx_clock_enable  nrfx_clock.o
    0x00028fe8   0x00028fe8   0x00000020   Code   RO         1761    i.nrfx_clock_init   nrfx_clock.o
    0x00029008   0x00029008   0x00000030   Code   RO         1764    i.nrfx_clock_lfclk_stop  nrfx_clock.o
    0x00029038   0x00029038   0x00000010   Code   RO         3198    i.nrfx_coredep_delay_us  drv_rtc.o
    0x00029048   0x00029048   0x00000010   Code   RO         2433    i.nrfx_is_in_ram    nrfx_uarte.o
    0x00029058   0x00029058   0x0000003a   Code   RO         2190    i.nrfx_prs_acquire  nrfx_prs.o
    0x00029092   0x00029092   0x00000002   PAD
    0x00029094   0x00029094   0x00000010   Code   RO         2248    i.nrfx_uart_0_irq_handler  nrfx_uart.o
    0x000290a4   0x000290a4   0x000000d4   Code   RO         2250    i.nrfx_uart_init    nrfx_uart.o
    0x00029178   0x00029178   0x000000ec   Code   RO         2251    i.nrfx_uart_rx      nrfx_uart.o
    0x00029264   0x00029264   0x000000a8   Code   RO         2256    i.nrfx_uart_tx      nrfx_uart.o
    0x0002930c   0x0002930c   0x00000020   Code   RO         2258    i.nrfx_uart_tx_in_progress  nrfx_uart.o
    0x0002932c   0x0002932c   0x00000010   Code   RO         2434    i.nrfx_uarte_0_irq_handler  nrfx_uarte.o
    0x0002933c   0x0002933c   0x0000006c   Code   RO         2436    i.nrfx_uarte_init   nrfx_uarte.o
    0x000293a8   0x000293a8   0x00000104   Code   RO         2437    i.nrfx_uarte_rx     nrfx_uarte.o
    0x000294ac   0x000294ac   0x000000ac   Code   RO         2440    i.nrfx_uarte_tx     nrfx_uarte.o
    0x00029558   0x00029558   0x0000001c   Code   RO         2442    i.nrfx_uarte_tx_in_progress  nrfx_uarte.o
    0x00029574   0x00029574   0x00000004   Code   RO           12    i.nus_error_handler  main.o
    0x00029578   0x00029578   0x000000fc   Code   RO          863    i.on_characteristic_discovery_rsp  ble_db_discovery.o
    0x00029674   0x00029674   0x000000da   Code   RO          864    i.on_descriptor_discovery_rsp  ble_db_discovery.o
    0x0002974e   0x0002974e   0x00000072   Code   RO          865    i.on_primary_srv_discovery_rsp  ble_db_discovery.o
    0x000297c0   0x000297c0   0x0000009c   Code   RO          866    i.on_srv_disc_completion  ble_db_discovery.o
    0x0002985c   0x0002985c   0x000000b4   Code   RO         1931    i.port_event_handle  nrfx_gpiote.o
    0x00029910   0x00029910   0x00000010   Code   RO         1932    i.port_handler_polarity_get  nrfx_gpiote.o
    0x00029920   0x00029920   0x00000018   Code   RO         2192    i.prs_box_get       nrfx_prs.o
    0x00029938   0x00029938   0x000000e0   Code   RO         1150    i.queue_process     nrf_ble_gq.o
    0x00029a18   0x00029a18   0x00000018   Code   RO         3875    i.queue_utilization_get  nrf_queue.o
    0x00029a30   0x00029a30   0x00000058   Code   RO         1151    i.queues_purge      nrf_ble_gq.o
    0x00029a88   0x00029a88   0x00000038   Code   RO          867    i.registered_handler_get  ble_db_discovery.o
    0x00029ac0   0x00029ac0   0x0000001e   Code   RO         1152    i.request_err_code_handle  nrf_ble_gq.o
    0x00029ade   0x00029ade   0x00000072   Code   RO         1153    i.request_process   nrf_ble_gq.o
    0x00029b50   0x00029b50   0x0000005c   Code   RO         2903    i.rtc_irq           app_timer2.o
    0x00029bac   0x00029bac   0x00000064   Code   RO         2904    i.rtc_schedule      app_timer2.o
    0x00029c10   0x00029c10   0x00000074   Code   RO         2905    i.rtc_update        app_timer2.o
    0x00029c84   0x00029c84   0x00000032   Code   RO         2260    i.rx_byte           nrfx_uart.o
    0x00029cb6   0x00029cb6   0x00000016   Code   RO         2261    i.rx_done_event     nrfx_uart.o
    0x00029ccc   0x00029ccc   0x00000016   Code   RO         2444    i.rx_done_event     nrfx_uarte.o
    0x00029ce2   0x00029ce2   0x00000020   Code   RO         2262    i.rx_enable         nrfx_uart.o
    0x00029d02   0x00029d02   0x0000001a   Code   RO           13    i.scan_evt_handler  main.o
    0x00029d1c   0x00029d1c   0x00000058   Code   RO           14    i.scan_init         main.o
    0x00029d74   0x00029d74   0x00000028   Code   RO           15    i.scan_start        main.o
    0x00029d9c   0x00029d9c   0x00000058   Code   RO         1422    i.sd_state_evt_handler  nrf_drv_clock.o
    0x00029df4   0x00029df4   0x00000030   Code   RO         4563    i.sdh_request_observer_notify  nrf_sdh.o
    0x00029e24   0x00029e24   0x0000002c   Code   RO         4564    i.sdh_state_observer_notify  nrf_sdh.o
    0x00029e50   0x00029e50   0x00000020   Code   RO           16    i.shutdown_handler  main.o
    0x00029e70   0x00029e70   0x00000018   Code   RO         1423    i.soc_evt_handler   nrf_drv_clock.o
    0x00029e88   0x00029e88   0x00000058   Code   RO         4566    i.softdevices_evt_irq_enable  nrf_sdh.o
    0x00029ee0   0x00029ee0   0x0000000c   Code   RO         2906    i.sortlist_pop      app_timer2.o
    0x00029eec   0x00029eec   0x00000058   Code   RO         2907    i.timer_expire      app_timer2.o
    0x00029f44   0x00029f44   0x00000078   Code   RO         2908    i.timer_req_process  app_timer2.o
    0x00029fbc   0x00029fbc   0x00000038   Code   RO         2909    i.timer_req_schedule  app_timer2.o
    0x00029ff4   0x00029ff4   0x00000020   Code   RO         2263    i.tx_byte           nrfx_uart.o
    0x0002a014   0x0002a014   0x0000001a   Code   RO         2264    i.tx_done_event     nrfx_uart.o
    0x0002a02e   0x0002a02e   0x0000001a   Code   RO         2445    i.tx_done_event     nrfx_uarte.o
    0x0002a048   0x0002a048   0x00000002   Code   RO           17    i.uart_event_handle  main.o
    0x0002a04a   0x0002a04a   0x00000002   PAD
    0x0002a04c   0x0002a04c   0x000000a4   Code   RO         3038    i.uart_event_handler  app_uart_fifo.o
    0x0002a0f0   0x0002a0f0   0x00000030   Code   RO         1561    i.uart_evt_handler  nrf_drv_uart.o
    0x0002a120   0x0002a120   0x0000012a   Code   RO         2265    i.uart_irq_handler  nrfx_uart.o
    0x0002a24a   0x0002a24a   0x00000002   PAD
    0x0002a24c   0x0002a24c   0x00000030   Code   RO         1562    i.uarte_evt_handler  nrf_drv_uart.o
    0x0002a27c   0x0002a27c   0x00000112   Code   RO         2446    i.uarte_irq_handler  nrfx_uarte.o
    0x0002a38e   0x0002a38e   0x0000003a   Code   RO          532    i.wakeup_button_cfg  bsp.o
    0x0002a3c8   0x0002a3c8   0x00000014   Data   RO           23    .constdata          main.o
    0x0002a3dc   0x0002a3dc   0x00000020   Data   RO           24    .constdata          main.o
    0x0002a3fc   0x0002a3fc   0x00000008   Data   RO          419    .constdata          boards.o
    0x0002a404   0x0002a404   0x0000002c   Data   RO          537    .constdata          bsp.o
    0x0002a430   0x0002a430   0x0000007c   Data   RO          869    .constdata          ble_db_discovery.o
    0x0002a4ac   0x0002a4ac   0x00000016   Data   RO         1063    .constdata          nrf_ble_gatt.o
    0x0002a4c2   0x0002a4c2   0x00000002   PAD
    0x0002a4c4   0x0002a4c4   0x00000018   Data   RO         1154    .constdata          nrf_ble_gq.o
    0x0002a4dc   0x0002a4dc   0x00000010   Data   RO         1343    .constdata          ble_nus_c.o
    0x0002a4ec   0x0002a4ec   0x00000004   Data   RO         1934    .constdata          nrfx_gpiote.o
    0x0002a4f0   0x0002a4f0   0x00000004   Data   RO         2268    .constdata          nrfx_uart.o
    0x0002a4f4   0x0002a4f4   0x00000004   Data   RO         2449    .constdata          nrfx_uarte.o
    0x0002a4f8   0x0002a4f8   0x00000014   Data   RO         2912    .constdata          app_timer2.o
    0x0002a50c   0x0002a50c   0x00000020   Data   RO         3040    .constdata          app_uart_fifo.o
    0x0002a52c   0x0002a52c   0x00000004   PAD
    0x0002a530   0x0002a530   0x00000006   Data   RO         3199    .constdata          drv_rtc.o
    0x0002a536   0x0002a536   0x00000002   PAD
    0x0002a538   0x0002a538   0x00000018   Data   RO         3795    .constdata          nrf_pwr_mgmt.o
    0x0002a550   0x0002a550   0x0000000c   Data   RO         3796    .constdata          nrf_pwr_mgmt.o
    0x0002a55c   0x0002a55c   0x00000010   Data   RO         4567    .constdata          nrf_sdh.o
    0x0002a56c   0x0002a56c   0x00000028   Data   RO         4568    .constdata          nrf_sdh.o
    0x0002a594   0x0002a594   0x00000010   Data   RO         4569    .constdata          nrf_sdh.o
    0x0002a5a4   0x0002a5a4   0x00000010   Data   RO         4570    .constdata          nrf_sdh.o
    0x0002a5b4   0x0002a5b4   0x00000020   Data   RO         4675    .constdata          nrf_sdh_ble.o
    0x0002a5d4   0x0002a5d4   0x00000010   Data   RO         4676    .constdata          nrf_sdh_ble.o
    0x0002a5e4   0x0002a5e4   0x00000010   Data   RO         4723    .constdata          nrf_sdh_soc.o
    0x0002a5f4   0x0002a5f4   0x0000000c   Data   RO         4724    .constdata          nrf_sdh_soc.o
    0x0002a600   0x0002a600   0x00000020   Data   RO         4833    Region$$Table       anon$$obj.o
    0x0002a620   0x0002a620   0x00000014   Data   RO           30    nrf_balloc          main.o
    0x0002a634   0x0002a634   0x00000014   Data   RO           31    nrf_queue           main.o
    0x0002a648   0x0002a648   0x00000004   Data   RO           32    pwr_mgmt_data1      main.o
    0x0002a64c   0x0002a64c   0x00000020   Data   RO           33    sdh_ble_observers1  main.o
    0x0002a66c   0x0002a66c   0x00000008   Data   RO          640    sdh_ble_observers1  bsp_btn_ble.o
    0x0002a674   0x0002a674   0x00000008   Data   RO           34    sdh_ble_observers2  main.o
    0x0002a67c   0x0002a67c   0x00000008   Data   RO           35    sdh_ble_observers3  main.o
    0x0002a684   0x0002a684   0x00000008   Data   RO         1429    sdh_soc_observers0  nrf_drv_clock.o
    0x0002a68c   0x0002a68c   0x00000008   Data   RO         4678    sdh_stack_observers0  nrf_sdh_ble.o
    0x0002a694   0x0002a694   0x00000008   Data   RO         4725    sdh_stack_observers0  nrf_sdh_soc.o
    0x0002a69c   0x0002a69c   0x00000008   Data   RO         1430    sdh_state_observers0  nrf_drv_clock.o


    Execution Region RW_IRAM1 (Exec base: 0x20002b28, Load base: 0x0002a6a4, Size: 0x00003378, Max: 0x0000d4d8, ABSOLUTE, COMPRESSED[0x0000003c])

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20002b28   COMPRESSED   0x000005ec   Data   RW           25    .data               main.o
    0x20003114   COMPRESSED   0x00000002   Data   RW           26    .data               main.o
    0x20003116   COMPRESSED   0x00000004   Data   RW           27    .data               main.o
    0x2000311a   COMPRESSED   0x00000008   Data   RW           28    .data               main.o
    0x20003122   COMPRESSED   0x00000002   PAD
    0x20003124   COMPRESSED   0x00000008   Data   RW           29    .data               main.o
    0x2000312c   COMPRESSED   0x00000010   Data   RW          538    .data               bsp.o
    0x2000313c   COMPRESSED   0x00000008   Data   RW          639    .data               bsp_btn_ble.o
    0x20003144   COMPRESSED   0x00000010   Data   RW          870    .data               ble_db_discovery.o
    0x20003154   COMPRESSED   0x0000000c   Data   RW         1563    .data               nrf_drv_uart.o
    0x20003160   COMPRESSED   0x00000008   Data   RW         1768    .data               nrfx_clock.o
    0x20003168   COMPRESSED   0x00000008   Data   RW         2194    .data               nrfx_prs.o
    0x20003170   COMPRESSED   0x00000020   Data   RW         2913    .data               app_timer2.o
    0x20003190   COMPRESSED   0x00000004   Data   RW         2914    .data               app_timer2.o
    0x20003194   COMPRESSED   0x0000001c   Data   RW         3041    .data               app_uart_fifo.o
    0x200031b0   COMPRESSED   0x0000000c   Data   RW         3200    .data               drv_rtc.o
    0x200031bc   COMPRESSED   0x00000008   Data   RW         3797    .data               nrf_pwr_mgmt.o
    0x200031c4   COMPRESSED   0x00000003   Data   RW         4571    .data               nrf_sdh.o
    0x200031c7   COMPRESSED   0x00000001   Data   RW         4677    .data               nrf_sdh_ble.o
    0x200031c8   COMPRESSED   0x00000004   Data   RW         4763    .data               system_nrf52.o
    0x200031cc        -       0x00000970   Zero   RW           18    .bss                main.o
    0x20003b3c        -       0x000000a0   Zero   RW           19    .bss                main.o
    0x20003bdc        -       0x0000000c   Zero   RW           20    .bss                main.o
    0x20003be8        -       0x0000000c   Zero   RW           21    .bss                main.o
    0x20003bf4        -       0x000000c0   Zero   RW           22    .bss                main.o
    0x20003cb4   COMPRESSED   0x00000004   PAD
    0x20003cb8        -       0x00000020   Zero   RW          533    .bss                bsp.o
    0x20003cd8        -       0x00000020   Zero   RW          534    .bss                bsp.o
    0x20003cf8        -       0x0000000c   Zero   RW          535    .bss                bsp.o
    0x20003d04   COMPRESSED   0x00000004   PAD
    0x20003d08        -       0x00000020   Zero   RW          536    .bss                bsp.o
    0x20003d28        -       0x00000018   Zero   RW          868    .bss                ble_db_discovery.o
    0x20003d40        -       0x00000014   Zero   RW         1424    .bss                nrf_drv_clock.o
    0x20003d54        -       0x0000005c   Zero   RW         1933    .bss                nrfx_gpiote.o
    0x20003db0        -       0x0000002c   Zero   RW         2266    .bss                nrfx_uart.o
    0x20003ddc        -       0x00000024   Zero   RW         2447    .bss                nrfx_uarte.o
    0x20003e00        -       0x00000010   Zero   RW         2910    .bss                app_timer2.o
    0x20003e10        -       0x00000058   Zero   RW         2911    .bss                app_timer2.o
    0x20003e68        -       0x00000020   Zero   RW         3039    .bss                app_uart_fifo.o
    0x20003e88        -       0x0000000c   Zero   RW         3114    .bss                app_util_platform.o
    0x20003e94        -       0x0000000c   Zero   RW         3794    .bss                nrf_pwr_mgmt.o
    0x20003ea0        -       0x00002000   Zero   RW         4751    STACK               arm_startup_nrf52.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

        22          0          0          0          0       2681   app_error.o
        48          8          0          0          0      34213   app_error_weak.o
       118          0          0          0          0       5658   app_fifo.o
       824         88         20         36        104      17236   app_timer2.o
       476         50         32         28         32      16175   app_uart_fifo.o
       124         14          0          0         12      11038   app_util_platform.o
        36          8        512          0       8192        992   arm_startup_nrf52.o
       252          0          0          0          0      45163   ble_advdata.o
      1828         94        124         16         24      33935   ble_db_discovery.o
       468         10         16          0          0      11870   ble_nus_c.o
       206         32          8          0          0      12841   boards.o
       804         60         44         16        108      13672   bsp.o
       196          6          8          8          0       4409   bsp_btn_ble.o
       612         14          6         12          0      50405   drv_rtc.o
      1080        100        144       1538       2792     152992   main.o
       326          0          0          0          0       6563   nrf_atfifo.o
         0          0          0          0          0        604   nrf_atomic.o
       164          0          0          0          0       4008   nrf_balloc.o
       484          6         22          0          0      37025   nrf_ble_gatt.o
       958         28         24          0          0      12016   nrf_ble_gq.o
       836          0          0          0          0      13358   nrf_ble_scan.o
       312         36         16          0         20      44706   nrf_drv_clock.o
       200         26          0         12          0      18756   nrf_drv_uart.o
       308          0          0          0          0       8765   nrf_memobj.o
       108         16         36          8         12      41165   nrf_pwr_mgmt.o
       406          0          0          0          0       7071   nrf_queue.o
       384         46         88          3          0      43695   nrf_sdh.o
       292         18         56          1          0       6127   nrf_sdh_ble.o
        60          4         36          0          0       2632   nrf_sdh_soc.o
        78          0          0          0          0       3156   nrf_section_iter.o
        84          0          0          0          0       4200   nrf_sortlist.o
       242         32          0          8          0      46858   nrfx_clock.o
       506         38          4          0         92      22734   nrfx_gpiote.o
        94         16          0          8          0       4141   nrfx_prs.o
      1368         38          4          0         44      32652   nrfx_uart.o
      1290         36          4          0         36      27005   nrfx_uarte.o
       796         70          0          4          0       3267   system_nrf52.o

    ----------------------------------------------------------------------
     16428        <USER>       <GROUP>       1700      11476     803784   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        38          0          8          2          8          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

        58          0          0          0          0          0   __dczerorl.o
         0          0          0          0          0          0   entry.o
         0          0          0          0          0          0   entry10a.o
         0          0          0          0          0          0   entry11a.o
         4          0          0          0          0          0   entry12b.o
         8          4          0          0          0          0   entry2.o
         4          0          0          0          0          0   entry5.o
         0          0          0          0          0          0   entry7b.o
         0          0          0          0          0          0   entry8b.o
         8          4          0          0          0          0   entry9a.o
        30          0          0          0          0          0   handlers.o
        36          8          0          0          0         68   init.o
        30          0          0          0          0         68   llshl.o
        32          0          0          0          0         68   llushr.o
        26          0          0          0          0         80   memcmp.o
        36          0          0          0          0         68   memcpya.o
        36          0          0          0          0        108   memseta.o
        98          0          0          0          0         92   uldiv.o

    ----------------------------------------------------------------------
       412         <USER>          <GROUP>          0          0        552   Library Totals
         6          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

       406         16          0          0          0        552   mc_w.l

    ----------------------------------------------------------------------
       412         <USER>          <GROUP>          0          0        552   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     16840        910       1244       1700      11476     782012   Grand Totals
     16840        910       1244         60      11476     782012   ELF Image Totals (compressed)
     16840        910       1244         60          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                18084 (  17.66kB)
    Total RW  Size (RW Data + ZI Data)             13176 (  12.87kB)
    Total ROM Size (Code + RO Data + RW Data)      18144 (  17.72kB)

==============================================================================

