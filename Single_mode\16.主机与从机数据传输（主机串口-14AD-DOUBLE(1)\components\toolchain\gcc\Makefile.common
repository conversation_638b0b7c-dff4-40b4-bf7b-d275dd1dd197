# Copyright (c) 2016 - 2017, Nordic Semiconductor ASA
# 
# All rights reserved.
# 
# Redistribution and use in source and binary forms, with or without modification,
# are permitted provided that the following conditions are met:
# 
# 1. Redistributions of source code must retain the above copyright notice, this
#    list of conditions and the following disclaimer.
# 
# 2. Redistributions in binary form, except as embedded into a Nordic
#    Semiconductor ASA integrated circuit in a product or a software update for
#    such product, must reproduce the above copyright notice, this list of
#    conditions and the following disclaimer in the documentation and/or other
#    materials provided with the distribution.
# 
# 3. Neither the name of Nordic Semiconductor ASA nor the names of its
#    contributors may be used to endorse or promote products derived from this
#    software without specific prior written permission.
# 
# 4. This software, with or without modification, must only be used with a
#    Nordic Semiconductor ASA integrated circuit.
# 
# 5. Any software provided in binary form under this license must not be reverse
#    engineered, decompiled, modified and/or disassembled.
# 
# THIS SOFTWARE IS PROVIDED BY NORDIC SEMICONDUCTOR ASA "AS IS" AND ANY EXPRESS
# OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
# OF MERCHANTABILITY, NONINFRINGEMENT, AND FITNESS FOR A PARTICULAR PURPOSE ARE
# DISCLAIMED. IN NO EVENT SHALL NORDIC SEMICONDUCTOR ASA OR CONTRIBUTORS BE
# LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
# CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE
# GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
# HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
# LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT
# OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.


# Options:
#   VERBOSE=1 (default is 0) - print each executed command
#   PRETTY=1  (default is 0) - show progress, in percentage
#   ABSOLUTE_PATHS=1 (default is 0) - convert all include folders and source
#     file paths to their absolute forms
#   PASS_INCLUDE_PATHS_VIA_FILE=1 (default is 0) - use <target>.inc file
#     to pass include paths to gcc
#   PASS_LINKER_INPUT_VIA_FILE=0  (default is 1) - don't use <target>.in file
#     to pass the list of linker input files
VERBOSE ?= 0
PRETTY  ?= 0
ABSOLUTE_PATHS ?= 0
PASS_INCLUDE_PATHS_VIA_FILE ?= 0
PASS_LINKER_INPUT_VIA_FILE  ?= 1

.SUFFIXES: # ignore built-in rules
%.d:       # don't try to make .d files
.PRECIOUS: %.d %.o

MK := mkdir
RM := rm -rf

# echo suspend
ifeq ($(VERBOSE),1)
  NO_ECHO :=
else
  NO_ECHO := @
endif

ifneq (,$(filter clean, $(MAKECMDGOALS)))

OTHER_GOALS := $(filter-out clean, $(MAKECMDGOALS))
ifneq (, $(OTHER_GOALS))
$(info Cannot make anything in parallel with "clean".)
$(info Execute "$(MAKE) clean \
  $(foreach goal, $(OTHER_GOALS),&& $(MAKE) $(goal))" instead.)
$(error Cannot continue)
else
.PHONY: clean
clean:
	$(RM) $(OUTPUT_DIRECTORY)
endif # ifneq(, $(OTHER_GOALS))

else # ifneq (,$(filter clean, $(MAKECMDGOALS)))

ifndef PROGRESS

ifeq ($(PRETTY),1)
    X     := @
    EMPTY :=
    SPACE := $(EMPTY) $(EMPTY)
    TOTAL := $(subst $(SPACE),,$(filter $(X), \
               $(shell "$(MAKE)" $(MAKECMDGOALS) --dry-run \
                 --no-print-directory PROGRESS=$(X))))

    5   := $(X)$(X)$(X)$(X)$(X)
    25  := $(5)$(5)$(5)$(5)$(5)
    100 := $(25)$(25)$(25)$(25)

    C       :=
    COUNTER  = $(eval C := $(C)$(100))$(C)
    P       :=
    count    = $(if $(filter $1%,$2),$(eval \
                 P += 1)$(call count,$1,$(2:$1%=%)),$(eval \
                 C := $2))
    print    = [$(if $(word 99,$1),99,$(if $(word 10,$1),, )$(words $1))%]
    PROGRESS = $(call count,$(TOTAL),$(COUNTER))$(call print,$(P)) $1
else
    PROGRESS = $1
endif # ifeq ($(PRETTY),1)

PLATFORM_SUFFIX := $(if $(filter Windows%,$(OS)),windows,posix)
TOOLCHAIN_CONFIG_FILE := $(TEMPLATE_PATH)/Makefile.$(PLATFORM_SUFFIX)
include $(TOOLCHAIN_CONFIG_FILE)

# $1 path
define quote
'$(subst ','\'',$(1))'
endef

# Toolchain commands
CC      := $(call quote,$(GNU_INSTALL_ROOT)$(GNU_PREFIX)-gcc)
CXX     := $(call quote,$(GNU_INSTALL_ROOT)$(GNU_PREFIX)-c++)
AS      := $(call quote,$(GNU_INSTALL_ROOT)$(GNU_PREFIX)-as)
AR      := $(call quote,$(GNU_INSTALL_ROOT)$(GNU_PREFIX)-ar) -r
LD      := $(call quote,$(GNU_INSTALL_ROOT)$(GNU_PREFIX)-ld)
NM      := $(call quote,$(GNU_INSTALL_ROOT)$(GNU_PREFIX)-nm)
OBJDUMP := $(call quote,$(GNU_INSTALL_ROOT)$(GNU_PREFIX)-objdump)
OBJCOPY := $(call quote,$(GNU_INSTALL_ROOT)$(GNU_PREFIX)-objcopy)
SIZE    := $(call quote,$(GNU_INSTALL_ROOT)$(GNU_PREFIX)-size)
$(if $(shell $(CC) --version),,$(info Cannot find: $(CC).) \
  $(info Please set values in: "$(abspath $(TOOLCHAIN_CONFIG_FILE))") \
  $(info according to the actual configuration of your system.) \
  $(error Cannot continue))

# Use ccache on linux if available
CCACHE := $(if $(filter Windows%,$(OS)),, \
               $(if $(wildcard /usr/bin/ccache),ccache))
CC     := $(CCACHE) $(CC)

endif # ifndef PROGRESS

# $1 type of item
# $2 items paths to check
define ensure_exists_each
$(foreach item, $(2), \
  $(if $(wildcard $(item)),, $(warning Cannot find $(1): $(item))))
endef

ifeq ($(PASS_INCLUDE_PATHS_VIA_FILE),1)
INC_PATHS = @$($@_INC)
GENERATE_INC_FILE := 1
else
INC_PATHS = $(call target_specific, INC_PATHS, $($@_TGT))
GENERATE_INC_FILE :=
endif

# $1 object file
# $2 source file
# $3 include paths container file
# $4 target name
define bind_obj_with_src
$(eval $(1)     := $(2)) \
$(eval $(1)_INC := $(3)) \
$(eval $(1)_TGT := $(4)) \
$(eval $(1): Makefile | $(dir $(1)).) \
$(if $(GENERATE_INC_FILE), $(eval $(1): $(3)))
endef

# $1 target name
# $2 source file name
# Note: this additional .o for .s files is a workaround for issues with make 4.1
#       from MinGW (it does nothing to remake .s.o files when a rule for .S.o
#       files is defined as well).
define get_object_file_name
$(OUTPUT_DIRECTORY)/$(strip $(1))/$(notdir $(2:%.s=%.s.o)).o
endef

# $1 target name
# $2 include paths container file
# $3 list of source files
define get_object_files
$(call ensure_exists_each,source file, $(3)) \
$(foreach src_file, $(3), \
  $(eval obj_file := $(call get_object_file_name, $(1), $(src_file))) \
  $(eval DEPENDENCIES += $(obj_file:.o=.d)) \
  $(call bind_obj_with_src, $(obj_file), $(src_file), $(2), $(1)) \
  $(obj_file))
endef

# $1 variable name
# $2 target name
define target_specific
$($(addsuffix _$(strip $(2)), $(1)))
endef

ifeq ($(ABSOLUTE_PATHS),1)
get_path = $(call quote,$(abspath $1))
else
get_path = $1
endif

# $1 list of include folders
define get_inc_paths
$(call ensure_exists_each,include folder,$(1)) \
$(foreach folder,$(1),-I$(call get_path,$(folder)))
endef

# $1 target name
# $2 include paths container file
# $3 build goal name
define prepare_build
$(eval DEPENDENCIES :=) \
$(eval $(3): \
  $(call get_object_files, $(1), $(2), \
    $(SRC_FILES) $(call target_specific, SRC_FILES, $(1)))) \
$(eval -include $(DEPENDENCIES)) \
$(eval INC_PATHS_$(strip $(1)) := \
  $(call get_inc_paths, \
    $(INC_FOLDERS) $(call target_specific, INC_FOLDERS, $(1))))
endef

# $1 target name
define define_target
$(eval OUTPUT_FILE := $(OUTPUT_DIRECTORY)/$(strip $(1))) \
$(eval $(1): $(OUTPUT_FILE).out $(OUTPUT_FILE).hex $(OUTPUT_FILE).bin \
           ; @echo DONE $(strip $(1))) \
$(call prepare_build, $(1), $(OUTPUT_FILE).inc, $(OUTPUT_FILE).out)
endef

# $1 target name
# $2 library file name
define define_library
$(eval OUTPUT_FILE := $(OUTPUT_DIRECTORY)/$(strip $(1))) \
$(eval $(1) := $(2)) \
$(call prepare_build, $(1), $(OUTPUT_FILE).inc, $(1))
endef

# $1 content to be dumped
# Invokes another instance of MAKE to dump the specified content to stdout,
# which may be then redirected in shell to a file and this way stored there.
# MAKE in version prior to 4.0 does not provide the $(file ...) function.
define dump
$(eval CONTENT_TO_DUMP := $(1)) \
"$(MAKE)" -s --no-print-directory \
  -f "$(TEMPLATE_PATH)/dump.mk" VARIABLE=CONTENT_TO_DUMP
endef
export CONTENT_TO_DUMP

.PHONY: $(TARGETS) all

all: $(TARGETS)

# Create build directories
$(OUTPUT_DIRECTORY):
	$(MK) $@
$(OUTPUT_DIRECTORY)/%/.: | $(OUTPUT_DIRECTORY)
	cd $(OUTPUT_DIRECTORY) && $(MK) $*

$(OUTPUT_DIRECTORY)/%.inc: Makefile | $(OUTPUT_DIRECTORY)
	$(info Generating $@)
	$(NO_ECHO)$(call dump, $(call target_specific, INC_PATHS, $*)) > $@

# $1 command
# $2 flags
# $3 message
define run
$(info $(call PROGRESS,$(3) file: $(notdir $($@)))) \
$(NO_ECHO)$(1) -MP -MD -c -o $@ $(call get_path,$($@)) $(2) $(INC_PATHS)
endef

# Create object files from C source files
%.c.o:
	$(call run,$(CC) -std=c99,$(CFLAGS),Compiling)

# Create object files from C++ source files
%.cpp.o:
	$(call run,$(CXX),$(CFLAGS) $(CXXFLAGS),Compiling)

# Create object files from assembly source files
%.S.o %.s.o.o:
	$(call run,$(CC) -x assembler-with-cpp,$(ASMFLAGS),Assembling)

ifeq ($(PASS_LINKER_INPUT_VIA_FILE),1)
GENERATE_LD_INPUT_FILE = $(call dump, $^ $(LIB_FILES)) > $(@:.out=.in)
LD_INPUT               = @$(@:.out=.in)
else
GENERATE_LD_INPUT_FILE =
LD_INPUT               = $^ $(LIB_FILES)
endif

# Link object files
%.out:
	$(info $(call PROGRESS,Linking target: $@))
	$(NO_ECHO)$(GENERATE_LD_INPUT_FILE)
	$(NO_ECHO)$(CC) $(LDFLAGS) $(LD_INPUT) -Wl,-Map=$(@:.out=.map) -o $@
	$(NO_ECHO)$(SIZE) $@

# Create binary .bin file from the .out file
%.bin: %.out
	$(info Preparing: $@)
	$(NO_ECHO)$(OBJCOPY) -O binary $< $@

# Create binary .hex file from the .out file
%.hex: %.out
	$(info Preparing: $@)
	$(NO_ECHO)$(OBJCOPY) -O ihex $< $@

endif # ifneq (,$(filter clean, $(MAKECMDGOALS)))
