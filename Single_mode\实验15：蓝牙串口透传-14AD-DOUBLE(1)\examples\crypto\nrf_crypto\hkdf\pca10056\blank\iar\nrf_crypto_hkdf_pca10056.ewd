<?xml version="1.0" encoding="iso-8859-1"?>

<project>
  <fileVersion>2</fileVersion>  <configuration>
    <name>nrf52840_xxaa</name>
    <toolchain>
      <name>ARM</name>
    </toolchain>
    <debug>0</debug>
    <settings>
      <name>C-SPY</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>26</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>0</debug>
        <option>
          <name>CInput</name>
          <state>1</state>
        </option>
        <option>
          <name>CEndian</name>
          <state>1</state>
        </option>
        <option>
          <name>CProcessor</name>
          <state>1</state>
        </option>
        <option>
          <name>OCVariant</name>
          <state>0</state>
        </option>
        <option>
          <name>MacOverride</name>
          <state>0</state>
        </option>
        <option>
          <name>MacFile</name>
          <state></state>
        </option>
        <option>
          <name>MemOverride</name>
          <state>0</state>
        </option>
        <option>
          <name>MemFile</name>
          <state>$TOOLKIT_DIR$\CONFIG\debugger\NordicSemiconductor\iar_nrf52840_xxaa.ddf</state>
        </option>
        <option>
          <name>RunToEnable</name>
          <state>1</state>
        </option>
        <option>
          <name>RunToName</name>
          <state>main</state>
        </option>
        <option>
          <name>CExtraOptionsCheck</name>
          <state>1</state>
        </option>
        <option>
          <name>CExtraOptions</name>
          <state>--drv_vector_table_base=0x0</state>
        </option>
        <option>
          <name>CFpuProcessor</name>
          <state>1</state>
        </option>
        <option>
          <name>OCDDFArgumentProducer</name>
          <state></state>
        </option>
        <option>
          <name>OCDownloadSuppressDownload</name>
          <state>0</state>
        </option>
        <option>
          <name>OCDownloadVerifyAll</name>
          <state>0</state>
        </option>
        <option>
          <name>OCProductVersion</name>
          <state>7.20.2.7418</state>
        </option>
        <option>
          <name>OCDynDriverList</name>
          <state>JLINK_ID</state>
        </option>
        <option>
          <name>OCLastSavedByProductVersion</name>
          <state>7.20.2.7418</state>
        </option>
        <option>
          <name>OCDownloadAttachToProgram</name>
          <state>0</state>
        </option>
        <option>
          <name>UseFlashLoader</name>
          <state>0</state>
        </option>
        <option>
          <name>CLowLevel</name>
          <state>1</state>
        </option>
        <option>
          <name>OCBE8Slave</name>
          <state>1</state>
        </option>
        <option>
          <name>MacFile2</name>
          <state></state>
        </option>
        <option>
          <name>CDevice</name>
          <state>1</state>
        </option>
        <option>
          <name>FlashLoadersV3</name>
          <state>$TOOLKIT_DIR$\config\flashloader\NordicSemiconductor\nrf52840_xxaa.board</state>
        </option>
        <option>
          <name>OCImagesSuppressCheck1</name>
          <state>0</state>
        </option>
        <option>
          <name>OCImagesPath1</name>
          <state></state>
        </option>
        <option>
          <name>OCImagesSuppressCheck2</name>
          <state>0</state>
        </option>
        <option>
          <name>OCImagesPath2</name>
          <state></state>
        </option>
        <option>
          <name>OCImagesSuppressCheck3</name>
          <state>0</state>
        </option>
        <option>
          <name>OCImagesPath3</name>
          <state></state>
        </option>
        <option>
          <name>OverrideDefFlashBoard</name>
          <state>0</state>
        </option>
        <option>
          <name>OCImagesOffset1</name>
          <state></state>
        </option>
        <option>
          <name>OCImagesOffset2</name>
          <state></state>
        </option>
        <option>
          <name>OCImagesOffset3</name>
          <state></state>
        </option>
        <option>
          <name>OCImagesUse1</name>
          <state>0</state>
        </option>
        <option>
          <name>OCImagesUse2</name>
          <state>0</state>
        </option>
        <option>
          <name>OCImagesUse3</name>
          <state>0</state>
        </option>
        <option>
          <name>OCDeviceConfigMacroFile</name>
          <state>1</state>
        </option>
        <option>
          <name>OCDebuggerExtraOption</name>
          <state>1</state>
        </option>
        <option>
          <name>OCAllMTBOptions</name>
          <state>1</state>
        </option>
        <option>
          <name>OCMulticoreNrOfCores</name>
          <state>1</state>
        </option>
        <option>
          <name>OCMulticoreMaster</name>
          <state>0</state>
        </option>
        <option>
          <name>OCMulticorePort</name>
          <state>53461</state>
        </option>
        <option>
          <name>OCMulticoreWorkspace</name>
          <state></state>
        </option>
        <option>
          <name>OCMulticoreSlaveProject</name>
          <state></state>
        </option>
        <option>
          <name>OCMulticoreSlaveConfiguration</name>
          <state></state>
        </option>
      </data>
    </settings>
    <settings>
      <name>ARMSIM_ID</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>1</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>0</debug>
        <option>
          <name>OCSimDriverInfo</name>
          <state>1</state>
        </option>
        <option>
          <name>OCSimEnablePSP</name>
          <state>0</state>
        </option>
        <option>
          <name>OCSimPspOverrideConfig</name>
          <state>0</state>
        </option>
        <option>
          <name>OCSimPspConfigFile</name>
          <state></state>
        </option>
      </data>
    </settings>
    <settings>
      <name>ANGEL_ID</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>0</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>0</debug>
        <option>
          <name>CCAngelHeartbeat</name>
          <state>1</state>
        </option>
        <option>
          <name>CAngelCommunication</name>
          <state>1</state>
        </option>
        <option>
          <name>CAngelCommBaud</name>
          <version>0</version>
          <state>3</state>
        </option>
        <option>
          <name>CAngelCommPort</name>
          <version>0</version>
          <state>0</state>
        </option>
        <option>
          <name>ANGELTCPIP</name>
          <state>aaa.bbb.ccc.ddd</state>
        </option>
        <option>
          <name>DoAngelLogfile</name>
          <state>0</state>
        </option>
        <option>
          <name>AngelLogFile</name>
          <state>$PROJ_DIR$\cspycomm.log</state>
        </option>
        <option>
          <name>OCDriverInfo</name>
          <state>1</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>CMSISDAP_ID</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>2</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>0</debug>
        <option>
          <name>OCDriverInfo</name>
          <state>1</state>
        </option>
        <option>
          <name>CMSISDAPAttachSlave</name>
          <state>1</state>
        </option>
        <option>
          <name>OCIarProbeScriptFile</name>
          <state>1</state>
        </option>
        <option>
          <name>CMSISDAPResetList</name>
          <version>1</version>
          <state>10</state>
        </option>
        <option>
          <name>CMSISDAPHWResetDuration</name>
          <state>300</state>
        </option>
        <option>
          <name>CMSISDAPHWResetDelay</name>
          <state>200</state>
        </option>
        <option>
          <name>CMSISDAPDoLogfile</name>
          <state>0</state>
        </option>
        <option>
          <name>CMSISDAPLogFile</name>
          <state>$PROJ_DIR$\cspycomm.log</state>
        </option>
        <option>
          <name>CMSISDAPInterfaceRadio</name>
          <state>0</state>
        </option>
        <option>
          <name>CMSISDAPInterfaceCmdLine</name>
          <state>0</state>
        </option>
        <option>
          <name>CMSISDAPMultiTargetEnable</name>
          <state>0</state>
        </option>
        <option>
          <name>CMSISDAPMultiTarget</name>
          <state>0</state>
        </option>
        <option>
          <name>CMSISDAPJtagSpeedList</name>
          <version>0</version>
          <state>0</state>
        </option>
        <option>
          <name>CMSISDAPBreakpointRadio</name>
          <state>0</state>
        </option>
        <option>
          <name>CMSISDAPRestoreBreakpointsCheck</name>
          <state>0</state>
        </option>
        <option>
          <name>CMSISDAPUpdateBreakpointsEdit</name>
          <state>_call_main</state>
        </option>
        <option>
          <name>RDICatchReset</name>
          <state>0</state>
        </option>
        <option>
          <name>RDICatchUndef</name>
          <state>1</state>
        </option>
        <option>
          <name>RDICatchSWI</name>
          <state>0</state>
        </option>
        <option>
          <name>RDICatchData</name>
          <state>1</state>
        </option>
        <option>
          <name>RDICatchPrefetch</name>
          <state>1</state>
        </option>
        <option>
          <name>RDICatchIRQ</name>
          <state>0</state>
        </option>
        <option>
          <name>RDICatchFIQ</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchCORERESET</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchMMERR</name>
          <state>1</state>
        </option>
        <option>
          <name>CatchNOCPERR</name>
          <state>1</state>
        </option>
        <option>
          <name>CatchCHKERR</name>
          <state>1</state>
        </option>
        <option>
          <name>CatchSTATERR</name>
          <state>1</state>
        </option>
        <option>
          <name>CatchBUSERR</name>
          <state>1</state>
        </option>
        <option>
          <name>CatchINTERR</name>
          <state>1</state>
        </option>
        <option>
          <name>CatchHARDERR</name>
          <state>1</state>
        </option>
        <option>
          <name>CatchDummy</name>
          <state>0</state>
        </option>
        <option>
          <name>CMSISDAPMultiCPUEnable</name>
          <state>0</state>
        </option>
        <option>
          <name>CMSISDAPMultiCPUNumber</name>
          <state>0</state>
        </option>
        <option>
          <name>OCProbeCfgOverride</name>
          <state>0</state>
        </option>
        <option>
          <name>OCProbeConfig</name>
          <state></state>
        </option>
        <option>
          <name>CMSISDAPProbeConfigRadio</name>
          <state>0</state>
        </option>
        <option>
          <name>CMSISDAPSelectedCPUBehaviour</name>
          <state>0</state>
        </option>
        <option>
          <name>ICpuName</name>
          <state></state>
        </option>
        <option>
          <name>OCJetEmuParams</name>
          <state>1</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>GDBSERVER_ID</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>0</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>0</debug>
        <option>
          <name>OCDriverInfo</name>
          <state>1</state>
        </option>
        <option>
          <name>TCPIP</name>
          <state>aaa.bbb.ccc.ddd</state>
        </option>
        <option>
          <name>DoLogfile</name>
          <state>0</state>
        </option>
        <option>
          <name>LogFile</name>
          <state>$PROJ_DIR$\cspycomm.log</state>
        </option>
        <option>
          <name>CCJTagBreakpointRadio</name>
          <state>0</state>
        </option>
        <option>
          <name>CCJTagDoUpdateBreakpoints</name>
          <state>0</state>
        </option>
        <option>
          <name>CCJTagUpdateBreakpoints</name>
          <state>_call_main</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>IARROM_ID</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>1</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>0</debug>
        <option>
          <name>CRomLogFileCheck</name>
          <state>0</state>
        </option>
        <option>
          <name>CRomLogFileEditB</name>
          <state>$PROJ_DIR$\cspycomm.log</state>
        </option>
        <option>
          <name>CRomCommPort</name>
          <version>0</version>
          <state>0</state>
        </option>
        <option>
          <name>CRomCommBaud</name>
          <version>0</version>
          <state>7</state>
        </option>
        <option>
          <name>OCDriverInfo</name>
          <state>1</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>IJET_ID</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>3</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>0</debug>
        <option>
          <name>OCDriverInfo</name>
          <state>1</state>
        </option>
        <option>
          <name>IjetAttachSlave</name>
          <state>1</state>
        </option>
        <option>
          <name>OCIarProbeScriptFile</name>
          <state>1</state>
        </option>
        <option>
          <name>IjetResetList</name>
          <version>1</version>
          <state>10</state>
        </option>
        <option>
          <name>IjetHWResetDuration</name>
          <state>300</state>
        </option>
        <option>
          <name>IjetHWResetDelay</name>
          <state>200</state>
        </option>
        <option>
          <name>IjetPowerFromProbe</name>
          <state>1</state>
        </option>
        <option>
          <name>IjetPowerRadio</name>
          <state>0</state>
        </option>
        <option>
          <name>IjetDoLogfile</name>
          <state>0</state>
        </option>
        <option>
          <name>IjetLogFile</name>
          <state>$PROJ_DIR$\cspycomm.log</state>
        </option>
        <option>
          <name>IjetInterfaceRadio</name>
          <state>0</state>
        </option>
        <option>
          <name>IjetInterfaceCmdLine</name>
          <state>0</state>
        </option>
        <option>
          <name>IjetMultiTargetEnable</name>
          <state>0</state>
        </option>
        <option>
          <name>IjetMultiTarget</name>
          <state>0</state>
        </option>
        <option>
          <name>IjetScanChainNonARMDevices</name>
          <state>0</state>
        </option>
        <option>
          <name>IjetIRLength</name>
          <state>0</state>
        </option>
        <option>
          <name>IjetJtagSpeedList</name>
          <version>0</version>
          <state>0</state>
        </option>
        <option>
          <name>IjetProtocolRadio</name>
          <state>0</state>
        </option>
        <option>
          <name>IjetSwoPin</name>
          <state>0</state>
        </option>
        <option>
          <name>IjetCpuClockEdit</name>
          <state>72.0</state>
        </option>
        <option>
          <name>IjetSwoPrescalerList</name>
          <version>1</version>
          <state>0</state>
        </option>
        <option>
          <name>IjetBreakpointRadio</name>
          <state>0</state>
        </option>
        <option>
          <name>IjetRestoreBreakpointsCheck</name>
          <state>0</state>
        </option>
        <option>
          <name>IjetUpdateBreakpointsEdit</name>
          <state>_call_main</state>
        </option>
        <option>
          <name>RDICatchReset</name>
          <state>0</state>
        </option>
        <option>
          <name>RDICatchUndef</name>
          <state>1</state>
        </option>
        <option>
          <name>RDICatchSWI</name>
          <state>0</state>
        </option>
        <option>
          <name>RDICatchData</name>
          <state>1</state>
        </option>
        <option>
          <name>RDICatchPrefetch</name>
          <state>1</state>
        </option>
        <option>
          <name>RDICatchIRQ</name>
          <state>0</state>
        </option>
        <option>
          <name>RDICatchFIQ</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchCORERESET</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchMMERR</name>
          <state>1</state>
        </option>
        <option>
          <name>CatchNOCPERR</name>
          <state>1</state>
        </option>
        <option>
          <name>CatchCHKERR</name>
          <state>1</state>
        </option>
        <option>
          <name>CatchSTATERR</name>
          <state>1</state>
        </option>
        <option>
          <name>CatchBUSERR</name>
          <state>1</state>
        </option>
        <option>
          <name>CatchINTERR</name>
          <state>1</state>
        </option>
        <option>
          <name>CatchHARDERR</name>
          <state>1</state>
        </option>
        <option>
          <name>CatchDummy</name>
          <state>0</state>
        </option>
        <option>
          <name>OCProbeCfgOverride</name>
          <state>0</state>
        </option>
        <option>
          <name>OCProbeConfig</name>
          <state></state>
        </option>
        <option>
          <name>IjetProbeConfigRadio</name>
          <state>0</state>
        </option>
        <option>
          <name>IjetMultiCPUEnable</name>
          <state>0</state>
        </option>
        <option>
          <name>IjetMultiCPUNumber</name>
          <state>0</state>
        </option>
        <option>
          <name>IjetSelectedCPUBehaviour</name>
          <state>0</state>
        </option>
        <option>
          <name>ICpuName</name>
          <state></state>
        </option>
        <option>
          <name>OCJetEmuParams</name>
          <state>1</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>JLINK_ID</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>15</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>0</debug>
        <option>
          <name>JLinkSpeed</name>
          <state>1000</state>
        </option>
        <option>
          <name>CCJLinkDoLogfile</name>
          <state>0</state>
        </option>
        <option>
          <name>CCJLinkLogFile</name>
          <state>$PROJ_DIR$\cspycomm.log</state>
        </option>
        <option>
          <name>CCJLinkHWResetDelay</name>
          <state>0</state>
        </option>
        <option>
          <name>OCDriverInfo</name>
          <state>1</state>
        </option>
        <option>
          <name>JLinkInitialSpeed</name>
          <state>1000</state>
        </option>
        <option>
          <name>CCDoJlinkMultiTarget</name>
          <state>0</state>
        </option>
        <option>
          <name>CCScanChainNonARMDevices</name>
          <state>0</state>
        </option>
        <option>
          <name>CCJLinkMultiTarget</name>
          <state>0</state>
        </option>
        <option>
          <name>CCJLinkIRLength</name>
          <state>0</state>
        </option>
        <option>
          <name>CCJLinkCommRadio</name>
          <state>0</state>
        </option>
        <option>
          <name>CCJLinkTCPIP</name>
          <state>aaa.bbb.ccc.ddd</state>
        </option>
        <option>
          <name>CCJLinkSpeedRadioV2</name>
          <state>0</state>
        </option>
        <option>
          <name>CCUSBDevice</name>
          <version>1</version>
          <state>1</state>
        </option>
        <option>
          <name>CCRDICatchReset</name>
          <state>0</state>
        </option>
        <option>
          <name>CCRDICatchUndef</name>
          <state>0</state>
        </option>
        <option>
          <name>CCRDICatchSWI</name>
          <state>0</state>
        </option>
        <option>
          <name>CCRDICatchData</name>
          <state>0</state>
        </option>
        <option>
          <name>CCRDICatchPrefetch</name>
          <state>0</state>
        </option>
        <option>
          <name>CCRDICatchIRQ</name>
          <state>0</state>
        </option>
        <option>
          <name>CCRDICatchFIQ</name>
          <state>0</state>
        </option>
        <option>
          <name>CCJLinkBreakpointRadio</name>
          <state>0</state>
        </option>
        <option>
          <name>CCJLinkDoUpdateBreakpoints</name>
          <state>0</state>
        </option>
        <option>
          <name>CCJLinkUpdateBreakpoints</name>
          <state>_call_main</state>
        </option>
        <option>
          <name>CCJLinkInterfaceRadio</name>
          <state>1</state>
        </option>
        <option>
          <name>OCJLinkAttachSlave</name>
          <state>1</state>
        </option>
        <option>
          <name>CCJLinkResetList</name>
          <version>6</version>
          <state>7</state>
        </option>
        <option>
          <name>CCJLinkInterfaceCmdLine</name>
          <state>0</state>
        </option>
        <option>
          <name>CCCatchCORERESET</name>
          <state>0</state>
        </option>
        <option>
          <name>CCCatchMMERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCCatchNOCPERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCCatchCHRERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCCatchSTATERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCCatchBUSERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCCatchINTERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCCatchHARDERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCCatchDummy</name>
          <state>0</state>
        </option>
        <option>
          <name>OCJLinkScriptFile</name>
          <state>1</state>
        </option>
        <option>
          <name>CCJLinkUsbSerialNo</name>
          <state></state>
        </option>
        <option>
          <name>CCTcpIpAlt</name>
          <version>0</version>
          <state>0</state>
        </option>
        <option>
          <name>CCJLinkTcpIpSerialNo</name>
          <state></state>
        </option>
        <option>
          <name>CCCpuClockEdit</name>
          <state>72.0</state>
        </option>
        <option>
          <name>CCSwoClockAuto</name>
          <state>0</state>
        </option>
        <option>
          <name>CCSwoClockEdit</name>
          <state>2000</state>
        </option>
        <option>
          <name>OCJLinkTraceSource</name>
          <state>0</state>
        </option>
        <option>
          <name>OCJLinkTraceSourceDummy</name>
          <state>0</state>
        </option>
        <option>
          <name>OCJLinkDeviceName</name>
          <state>1</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>LMIFTDI_ID</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>2</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>0</debug>
        <option>
          <name>OCDriverInfo</name>
          <state>1</state>
        </option>
        <option>
          <name>LmiftdiSpeed</name>
          <state>500</state>
        </option>
        <option>
          <name>CCLmiftdiDoLogfile</name>
          <state>0</state>
        </option>
        <option>
          <name>CCLmiftdiLogFile</name>
          <state>$PROJ_DIR$\cspycomm.log</state>
        </option>
        <option>
          <name>CCLmiFtdiInterfaceRadio</name>
          <state>0</state>
        </option>
        <option>
          <name>CCLmiFtdiInterfaceCmdLine</name>
          <state>0</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>MACRAIGOR_ID</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>3</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>0</debug>
        <option>
          <name>jtag</name>
          <version>0</version>
          <state>0</state>
        </option>
        <option>
          <name>EmuSpeed</name>
          <state>1</state>
        </option>
        <option>
          <name>TCPIP</name>
          <state>aaa.bbb.ccc.ddd</state>
        </option>
        <option>
          <name>DoLogfile</name>
          <state>0</state>
        </option>
        <option>
          <name>LogFile</name>
          <state>$PROJ_DIR$\cspycomm.log</state>
        </option>
        <option>
          <name>DoEmuMultiTarget</name>
          <state>0</state>
        </option>
        <option>
          <name>EmuMultiTarget</name>
          <state>0@ARM7TDMI</state>
        </option>
        <option>
          <name>EmuHWReset</name>
          <state>0</state>
        </option>
        <option>
          <name>CEmuCommBaud</name>
          <version>0</version>
          <state>4</state>
        </option>
        <option>
          <name>CEmuCommPort</name>
          <version>0</version>
          <state>0</state>
        </option>
        <option>
          <name>jtago</name>
          <version>0</version>
          <state>0</state>
        </option>
        <option>
          <name>OCDriverInfo</name>
          <state>1</state>
        </option>
        <option>
          <name>UnusedAddr</name>
          <state>0x00800000</state>
        </option>
        <option>
          <name>CCMacraigorHWResetDelay</name>
          <state></state>
        </option>
        <option>
          <name>CCJTagBreakpointRadio</name>
          <state>0</state>
        </option>
        <option>
          <name>CCJTagDoUpdateBreakpoints</name>
          <state>0</state>
        </option>
        <option>
          <name>CCJTagUpdateBreakpoints</name>
          <state>_call_main</state>
        </option>
        <option>
          <name>CCMacraigorInterfaceRadio</name>
          <state>0</state>
        </option>
        <option>
          <name>CCMacraigorInterfaceCmdLine</name>
          <state>0</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>PEMICRO_ID</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>1</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>0</debug>
        <option>
          <name>OCDriverInfo</name>
          <state>1</state>
        </option>
        <option>
          <name>OCPEMicroAttachSlave</name>
          <state>1</state>
        </option>
        <option>
          <name>CCPEMicroInterfaceList</name>
          <version>0</version>
          <state>0</state>
        </option>
        <option>
          <name>CCPEMicroResetDelay</name>
          <state></state>
        </option>
        <option>
          <name>CCPEMicroJtagSpeed</name>
          <state>#UNINITIALIZED#</state>
        </option>
        <option>
          <name>CCJPEMicroShowSettings</name>
          <state>0</state>
        </option>
        <option>
          <name>DoLogfile</name>
          <state>0</state>
        </option>
        <option>
          <name>LogFile</name>
          <state>$PROJ_DIR$\cspycomm.log</state>
        </option>
        <option>
          <name>CCPEMicroUSBDevice</name>
          <version>0</version>
          <state>0</state>
        </option>
        <option>
          <name>CCPEMicroSerialPort</name>
          <version>0</version>
          <state>0</state>
        </option>
        <option>
          <name>CCJPEMicroTCPIPAutoScanNetwork</name>
          <state>1</state>
        </option>
        <option>
          <name>CCPEMicroTCPIP</name>
          <state>********</state>
        </option>
        <option>
          <name>CCPEMicroCommCmdLineProducer</name>
          <state>0</state>
        </option>
        <option>
          <name>CCSTLinkInterfaceRadio</name>
          <state>0</state>
        </option>
        <option>
          <name>CCSTLinkInterfaceCmdLine</name>
          <state>0</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>RDI_ID</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>2</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>0</debug>
        <option>
          <name>CRDIDriverDll</name>
          <state>###Uninitialized###</state>
        </option>
        <option>
          <name>CRDILogFileCheck</name>
          <state>0</state>
        </option>
        <option>
          <name>CRDILogFileEdit</name>
          <state>$PROJ_DIR$\cspycomm.log</state>
        </option>
        <option>
          <name>CCRDIHWReset</name>
          <state>0</state>
        </option>
        <option>
          <name>CCRDICatchReset</name>
          <state>0</state>
        </option>
        <option>
          <name>CCRDICatchUndef</name>
          <state>0</state>
        </option>
        <option>
          <name>CCRDICatchSWI</name>
          <state>0</state>
        </option>
        <option>
          <name>CCRDICatchData</name>
          <state>0</state>
        </option>
        <option>
          <name>CCRDICatchPrefetch</name>
          <state>0</state>
        </option>
        <option>
          <name>CCRDICatchIRQ</name>
          <state>0</state>
        </option>
        <option>
          <name>CCRDICatchFIQ</name>
          <state>0</state>
        </option>
        <option>
          <name>OCDriverInfo</name>
          <state>1</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>STLINK_ID</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>2</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>0</debug>
        <option>
          <name>OCDriverInfo</name>
          <state>1</state>
        </option>
        <option>
          <name>CCSTLinkInterfaceRadio</name>
          <state>0</state>
        </option>
        <option>
          <name>CCSTLinkInterfaceCmdLine</name>
          <state>0</state>
        </option>
        <option>
          <name>CCSTLinkResetList</name>
          <version>1</version>
          <state>0</state>
        </option>
        <option>
          <name>CCCpuClockEdit</name>
          <state>72.0</state>
        </option>
        <option>
          <name>CCSwoClockAuto</name>
          <state>0</state>
        </option>
        <option>
          <name>CCSwoClockEdit</name>
          <state>2000</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>THIRDPARTY_ID</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>0</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>0</debug>
        <option>
          <name>CThirdPartyDriverDll</name>
          <state>###Uninitialized###</state>
        </option>
        <option>
          <name>CThirdPartyLogFileCheck</name>
          <state>0</state>
        </option>
        <option>
          <name>CThirdPartyLogFileEditB</name>
          <state>$PROJ_DIR$\cspycomm.log</state>
        </option>
        <option>
          <name>OCDriverInfo</name>
          <state>1</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>XDS100_ID</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>2</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>0</debug>
        <option>
          <name>OCDriverInfo</name>
          <state>1</state>
        </option>
        <option>
          <name>OCXDS100AttachSlave</name>
          <state>1</state>
        </option>
        <option>
          <name>TIPackageOverride</name>
          <state>0</state>
        </option>
        <option>
          <name>TIPackage</name>
          <state></state>
        </option>
        <option>
          <name>CCXds100InterfaceList</name>
          <version>2</version>
          <state>0</state>
        </option>
        <option>
          <name>BoardFile</name>
          <state></state>
        </option>
        <option>
          <name>DoLogfile</name>
          <state>0</state>
        </option>
        <option>
          <name>LogFile</name>
          <state>$PROJ_DIR$\cspycomm.log</state>
        </option>
      </data>
    </settings>
    <debuggerPlugins>
      <plugin>
        <file>$TOOLKIT_DIR$\plugins\middleware\HCCWare\HCCWare.ewplugin</file>
        <loadFlag>0</loadFlag>
      </plugin>
      <plugin>
        <file>$TOOLKIT_DIR$\plugins\rtos\AVIX\AVIX.ENU.ewplugin</file>
        <loadFlag>0</loadFlag>
      </plugin>
      <plugin>
        <file>$TOOLKIT_DIR$\plugins\rtos\CMX\CmxArmPlugin.ENU.ewplugin</file>
        <loadFlag>0</loadFlag>
      </plugin>
      <plugin>
        <file>$TOOLKIT_DIR$\plugins\rtos\CMX\CmxTinyArmPlugin.ENU.ewplugin</file>
        <loadFlag>0</loadFlag>
      </plugin>
      <plugin>
        <file>$TOOLKIT_DIR$\plugins\rtos\embOS\embOSPlugin.ewplugin</file>
        <loadFlag>0</loadFlag>
      </plugin>
      <plugin>
        <file>$TOOLKIT_DIR$\plugins\rtos\MQX\MQXRtosPlugin.ewplugin</file>
        <loadFlag>0</loadFlag>
      </plugin>
      <plugin>
        <file>$TOOLKIT_DIR$\plugins\rtos\OpenRTOS\OpenRTOSPlugin.ewplugin</file>
        <loadFlag>0</loadFlag>
      </plugin>
      <plugin>
        <file>$TOOLKIT_DIR$\plugins\rtos\SafeRTOS\SafeRTOSPlugin.ewplugin</file>
        <loadFlag>0</loadFlag>
      </plugin>
      <plugin>
        <file>$TOOLKIT_DIR$\plugins\rtos\ThreadX\ThreadXArmPlugin.ENU.ewplugin</file>
        <loadFlag>0</loadFlag>
      </plugin>
      <plugin>
        <file>$TOOLKIT_DIR$\plugins\rtos\TI-RTOS\tirtosplugin.ewplugin</file>
        <loadFlag>0</loadFlag>
      </plugin>
      <plugin>
        <file>$TOOLKIT_DIR$\plugins\rtos\uCOS-II\uCOS-II-286-KA-CSpy.ewplugin</file>
        <loadFlag>0</loadFlag>
      </plugin>
      <plugin>
        <file>$TOOLKIT_DIR$\plugins\rtos\uCOS-II\uCOS-II-KA-CSpy.ewplugin</file>
        <loadFlag>0</loadFlag>
      </plugin>
      <plugin>
        <file>$TOOLKIT_DIR$\plugins\rtos\uCOS-III\uCOS-III-KA-CSpy.ewplugin</file>
        <loadFlag>0</loadFlag>
      </plugin>
      <plugin>
        <file>$EW_DIR$\common\plugins\CodeCoverage\CodeCoverage.ENU.ewplugin</file>
        <loadFlag>1</loadFlag>
      </plugin>
      <plugin>
        <file>$EW_DIR$\common\plugins\Orti\Orti.ENU.ewplugin</file>
        <loadFlag>0</loadFlag>
      </plugin>
      <plugin>
        <file>$EW_DIR$\common\plugins\SymList\SymList.ENU.ewplugin</file>
        <loadFlag>1</loadFlag>
      </plugin>
      <plugin>
        <file>$EW_DIR$\common\plugins\uCProbe\uCProbePlugin.ENU.ewplugin</file>
        <loadFlag>0</loadFlag>
      </plugin>
    </debuggerPlugins>
  </configuration></project>


