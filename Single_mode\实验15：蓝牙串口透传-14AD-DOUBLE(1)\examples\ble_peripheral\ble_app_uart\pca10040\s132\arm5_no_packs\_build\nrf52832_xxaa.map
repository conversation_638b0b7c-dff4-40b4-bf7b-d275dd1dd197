Component: ARM Compiler 5.06 update 1 (build 61) Tool: armlink [4d35a8]

==============================================================================

Section Cross References

    main.o(i.assert_nrf_callback) refers to app_error_handler_keil.o(.emb_text) for app_error_handler
    main.o(i.ble_evt_handler) refers to bsp.o(i.bsp_indication_set) for bsp_indication_set
    main.o(i.ble_evt_handler) refers to app_error.o(i.app_error_handler_bare) for app_error_handler_bare
    main.o(i.ble_evt_handler) refers to nrf_ble_qwr.o(i.nrf_ble_qwr_conn_handle_assign) for nrf_ble_qwr_conn_handle_assign
    main.o(i.ble_evt_handler) refers to main.o(.data) for .data
    main.o(i.ble_evt_handler) refers to main.o(.bss) for .bss
    main.o(i.ble_evt_handler) refers to main.o(.constdata) for .constdata
    main.o(i.bsp_event_handler) refers to main.o(i.sleep_mode_enter) for sleep_mode_enter
    main.o(i.bsp_event_handler) refers to app_error.o(i.app_error_handler_bare) for app_error_handler_bare
    main.o(i.bsp_event_handler) refers to ble_advertising.o(i.ble_advertising_restart_without_whitelist) for ble_advertising_restart_without_whitelist
    main.o(i.bsp_event_handler) refers to main.o(.data) for .data
    main.o(i.bsp_event_handler) refers to main.o(.bss) for .bss
    main.o(i.conn_params_error_handler) refers to app_error.o(i.app_error_handler_bare) for app_error_handler_bare
    main.o(i.data_len_set) refers to nrf_ble_gatt.o(i.nrf_ble_gatt_data_length_set) for nrf_ble_gatt_data_length_set
    main.o(i.data_len_set) refers to app_error.o(i.app_error_handler_bare) for app_error_handler_bare
    main.o(i.data_len_set) refers to main.o(.bss) for .bss
    main.o(i.gatt_evt_handler) refers to main.o(.data) for .data
    main.o(i.gatt_init) refers to nrf_ble_gatt.o(i.nrf_ble_gatt_init) for nrf_ble_gatt_init
    main.o(i.gatt_init) refers to app_error.o(i.app_error_handler_bare) for app_error_handler_bare
    main.o(i.gatt_init) refers to nrf_ble_gatt.o(i.nrf_ble_gatt_att_mtu_periph_set) for nrf_ble_gatt_att_mtu_periph_set
    main.o(i.gatt_init) refers to main.o(i.gatt_evt_handler) for gatt_evt_handler
    main.o(i.gatt_init) refers to main.o(.bss) for .bss
    main.o(i.main) refers to app_timer2.o(i.app_timer_init) for app_timer_init
    main.o(i.main) refers to app_error.o(i.app_error_handler_bare) for app_error_handler_bare
    main.o(i.main) refers to nrf_pwr_mgmt.o(i.nrf_pwr_mgmt_init) for nrf_pwr_mgmt_init
    main.o(i.main) refers to nrf_sdh.o(i.nrf_sdh_enable_request) for nrf_sdh_enable_request
    main.o(i.main) refers to nrf_sdh_ble.o(i.nrf_sdh_ble_default_cfg_set) for nrf_sdh_ble_default_cfg_set
    main.o(i.main) refers to nrf_sdh_ble.o(i.nrf_sdh_ble_enable) for nrf_sdh_ble_enable
    main.o(i.main) refers to main.o(i.gatt_init) for gatt_init
    main.o(i.main) refers to nrf_ble_qwr.o(i.nrf_ble_qwr_init) for nrf_ble_qwr_init
    main.o(i.main) refers to ble_nus.o(i.ble_nus_init) for ble_nus_init
    main.o(i.main) refers to memseta.o(.text) for __aeabi_memclr4
    main.o(i.main) refers to ble_advertising.o(i.ble_advertising_init) for ble_advertising_init
    main.o(i.main) refers to ble_advertising.o(i.ble_advertising_conn_cfg_tag_set) for ble_advertising_conn_cfg_tag_set
    main.o(i.main) refers to ble_conn_params.o(i.ble_conn_params_init) for ble_conn_params_init
    main.o(i.main) refers to main.o(i.saadc_init) for saadc_init
    main.o(i.main) refers to main.o(i.saadc_sampling_event_init) for saadc_sampling_event_init
    main.o(i.main) refers to main.o(i.saadc_sampling_event_enable) for saadc_sampling_event_enable
    main.o(i.main) refers to ble_advertising.o(i.ble_advertising_start) for ble_advertising_start
    main.o(i.main) refers to nrf_pwr_mgmt.o(i.nrf_pwr_mgmt_run) for nrf_pwr_mgmt_run
    main.o(i.main) refers to ble_nus.o(i.ble_nus_data_send) for ble_nus_data_send
    main.o(i.main) refers to main.o(i.nrf_qwr_error_handler) for nrf_qwr_error_handler
    main.o(i.main) refers to main.o(.bss) for .bss
    main.o(i.main) refers to main.o(i.nus_data_handler) for nus_data_handler
    main.o(i.main) refers to main.o(.data) for .data
    main.o(i.main) refers to main.o(i.on_adv_evt) for on_adv_evt
    main.o(i.main) refers to main.o(i.on_conn_params_evt) for on_conn_params_evt
    main.o(i.main) refers to main.o(i.conn_params_error_handler) for conn_params_error_handler
    main.o(i.nrf_qwr_error_handler) refers to app_error.o(i.app_error_handler_bare) for app_error_handler_bare
    main.o(i.on_adv_evt) refers to bsp.o(i.bsp_indication_set) for bsp_indication_set
    main.o(i.on_adv_evt) refers to app_error.o(i.app_error_handler_bare) for app_error_handler_bare
    main.o(i.on_adv_evt) refers to main.o(i.sleep_mode_enter) for sleep_mode_enter
    main.o(i.on_conn_params_evt) refers to app_error.o(i.app_error_handler_bare) for app_error_handler_bare
    main.o(i.on_conn_params_evt) refers to main.o(.data) for .data
    main.o(i.saadc_callback) refers to nrfx_saadc.o(i.nrfx_saadc_buffer_convert) for nrfx_saadc_buffer_convert
    main.o(i.saadc_callback) refers to app_error.o(i.app_error_handler_bare) for app_error_handler_bare
    main.o(i.saadc_callback) refers to memcpya.o(.text) for __aeabi_memcpy4
    main.o(i.saadc_callback) refers to main.o(.data) for .data
    main.o(i.saadc_callback) refers to main.o(.bss) for .bss
    main.o(i.saadc_init) refers to nrfx_saadc.o(i.nrfx_saadc_init) for nrfx_saadc_init
    main.o(i.saadc_init) refers to app_error.o(i.app_error_handler_bare) for app_error_handler_bare
    main.o(i.saadc_init) refers to nrfx_saadc.o(i.nrfx_saadc_channel_init) for nrfx_saadc_channel_init
    main.o(i.saadc_init) refers to nrfx_saadc.o(i.nrfx_saadc_buffer_convert) for nrfx_saadc_buffer_convert
    main.o(i.saadc_init) refers to main.o(.constdata) for .constdata
    main.o(i.saadc_init) refers to main.o(i.saadc_callback) for saadc_callback
    main.o(i.saadc_init) refers to main.o(.data) for .data
    main.o(i.saadc_sampling_event_enable) refers to nrfx_ppi.o(i.nrfx_ppi_channel_enable) for nrfx_ppi_channel_enable
    main.o(i.saadc_sampling_event_enable) refers to app_error.o(i.app_error_handler_bare) for app_error_handler_bare
    main.o(i.saadc_sampling_event_enable) refers to main.o(.data) for .data
    main.o(i.saadc_sampling_event_init) refers to nrf_drv_ppi.o(i.nrf_drv_ppi_init) for nrf_drv_ppi_init
    main.o(i.saadc_sampling_event_init) refers to app_error.o(i.app_error_handler_bare) for app_error_handler_bare
    main.o(i.saadc_sampling_event_init) refers to nrfx_timer.o(i.nrfx_timer_init) for nrfx_timer_init
    main.o(i.saadc_sampling_event_init) refers to llushr.o(.text) for __aeabi_llsr
    main.o(i.saadc_sampling_event_init) refers to nrfx_timer.o(i.nrfx_timer_extended_compare) for nrfx_timer_extended_compare
    main.o(i.saadc_sampling_event_init) refers to nrfx_timer.o(i.nrfx_timer_enable) for nrfx_timer_enable
    main.o(i.saadc_sampling_event_init) refers to nrfx_saadc.o(i.nrfx_saadc_sample_task_get) for nrfx_saadc_sample_task_get
    main.o(i.saadc_sampling_event_init) refers to nrfx_ppi.o(i.nrfx_ppi_channel_alloc) for nrfx_ppi_channel_alloc
    main.o(i.saadc_sampling_event_init) refers to nrfx_ppi.o(i.nrfx_ppi_channel_assign) for nrfx_ppi_channel_assign
    main.o(i.saadc_sampling_event_init) refers to main.o(.constdata) for .constdata
    main.o(i.saadc_sampling_event_init) refers to main.o(i.timer_handler) for timer_handler
    main.o(i.saadc_sampling_event_init) refers to main.o(.data) for .data
    main.o(i.sleep_mode_enter) refers to bsp.o(i.bsp_indication_set) for bsp_indication_set
    main.o(i.sleep_mode_enter) refers to app_error.o(i.app_error_handler_bare) for app_error_handler_bare
    main.o(i.sleep_mode_enter) refers to bsp_btn_ble.o(i.bsp_btn_ble_sleep_mode_prepare) for bsp_btn_ble_sleep_mode_prepare
    main.o(.data) refers to main.o(.data) for m_nus_link_ctx_storage_ctx_data_pool
    main.o(.data) refers to main.o(.data) for m_nus_link_ctx_storage
    main.o(sdh_ble_observers1) refers to nrf_ble_gatt.o(i.nrf_ble_gatt_on_ble_evt) for nrf_ble_gatt_on_ble_evt
    main.o(sdh_ble_observers1) refers to main.o(.bss) for m_gatt
    main.o(sdh_ble_observers1) refers to ble_advertising.o(i.ble_advertising_on_ble_evt) for ble_advertising_on_ble_evt
    main.o(sdh_ble_observers2) refers to ble_nus.o(i.ble_nus_on_ble_evt) for ble_nus_on_ble_evt
    main.o(sdh_ble_observers2) refers to main.o(.data) for m_nus
    main.o(sdh_ble_observers2) refers to nrf_ble_qwr.o(i.nrf_ble_qwr_on_ble_evt) for nrf_ble_qwr_on_ble_evt
    main.o(sdh_ble_observers2) refers to main.o(.bss) for m_qwr
    main.o(sdh_ble_observers3) refers to main.o(i.ble_evt_handler) for ble_evt_handler
    boards.o(i.bsp_board_button_idx_to_pin) refers to boards.o(.constdata) for .constdata
    boards.o(i.bsp_board_button_state_get) refers to boards.o(.constdata) for .constdata
    boards.o(i.bsp_board_init) refers to boards.o(i.nrf_gpio_cfg) for nrf_gpio_cfg
    boards.o(i.bsp_board_init) refers to boards.o(i.bsp_board_leds_off) for bsp_board_leds_off
    boards.o(i.bsp_board_init) refers to boards.o(.constdata) for .constdata
    boards.o(i.bsp_board_led_idx_to_pin) refers to boards.o(.constdata) for .constdata
    boards.o(i.bsp_board_led_invert) refers to boards.o(.constdata) for .constdata
    boards.o(i.bsp_board_led_off) refers to boards.o(i.nrf_gpio_pin_write) for nrf_gpio_pin_write
    boards.o(i.bsp_board_led_off) refers to boards.o(.constdata) for .constdata
    boards.o(i.bsp_board_led_on) refers to boards.o(i.nrf_gpio_pin_write) for nrf_gpio_pin_write
    boards.o(i.bsp_board_led_on) refers to boards.o(.constdata) for .constdata
    boards.o(i.bsp_board_led_state_get) refers to boards.o(.constdata) for .constdata
    boards.o(i.bsp_board_leds_off) refers to boards.o(i.bsp_board_led_off) for bsp_board_led_off
    boards.o(i.bsp_board_leds_on) refers to boards.o(i.bsp_board_led_on) for bsp_board_led_on
    boards.o(i.bsp_board_pin_to_button_idx) refers to boards.o(.constdata) for .constdata
    boards.o(i.bsp_board_pin_to_led_idx) refers to boards.o(.constdata) for .constdata
    bsp.o(i.alert_timer_handler) refers to boards.o(i.bsp_board_led_invert) for bsp_board_led_invert
    bsp.o(i.bsp_button_event_handler) refers to boards.o(i.bsp_board_pin_to_button_idx) for bsp_board_pin_to_button_idx
    bsp.o(i.bsp_button_event_handler) refers to app_timer2.o(i.app_timer_start) for app_timer_start
    bsp.o(i.bsp_button_event_handler) refers to app_timer2.o(i.app_timer_stop) for app_timer_stop
    bsp.o(i.bsp_button_event_handler) refers to bsp.o(.constdata) for .constdata
    bsp.o(i.bsp_button_event_handler) refers to bsp.o(.data) for .data
    bsp.o(i.bsp_button_event_handler) refers to bsp.o(.bss) for .bss
    bsp.o(i.bsp_button_is_pressed) refers to boards.o(i.bsp_board_button_state_get) for bsp_board_button_state_get
    bsp.o(i.bsp_buttons_disable) refers to app_button.o(i.app_button_disable) for app_button_disable
    bsp.o(i.bsp_buttons_enable) refers to app_button.o(i.app_button_enable) for app_button_enable
    bsp.o(i.bsp_event_to_button_action_assign) refers to bsp.o(.bss) for .bss
    bsp.o(i.bsp_indication_set) refers to bsp.o(i.bsp_led_indication) for bsp_led_indication
    bsp.o(i.bsp_indication_set) refers to bsp.o(.data) for .data
    bsp.o(i.bsp_init) refers to bsp.o(i.bsp_event_to_button_action_assign) for bsp_event_to_button_action_assign
    bsp.o(i.bsp_init) refers to app_button.o(i.app_button_init) for app_button_init
    bsp.o(i.bsp_init) refers to app_button.o(i.app_button_enable) for app_button_enable
    bsp.o(i.bsp_init) refers to app_timer2.o(i.app_timer_create) for app_timer_create
    bsp.o(i.bsp_init) refers to boards.o(i.bsp_board_init) for bsp_board_init
    bsp.o(i.bsp_init) refers to bsp.o(.data) for .data
    bsp.o(i.bsp_init) refers to bsp.o(.constdata) for .constdata
    bsp.o(i.bsp_init) refers to bsp.o(i.button_timer_handler) for button_timer_handler
    bsp.o(i.bsp_init) refers to bsp.o(i.leds_timer_handler) for leds_timer_handler
    bsp.o(i.bsp_init) refers to bsp.o(i.alert_timer_handler) for alert_timer_handler
    bsp.o(i.bsp_led_indication) refers to bsp.o(i.leds_off) for leds_off
    bsp.o(i.bsp_led_indication) refers to app_timer2.o(i.app_timer_stop) for app_timer_stop
    bsp.o(i.bsp_led_indication) refers to boards.o(i.bsp_board_led_state_get) for bsp_board_led_state_get
    bsp.o(i.bsp_led_indication) refers to boards.o(i.bsp_board_led_off) for bsp_board_led_off
    bsp.o(i.bsp_led_indication) refers to boards.o(i.bsp_board_led_on) for bsp_board_led_on
    bsp.o(i.bsp_led_indication) refers to uldiv.o(.text) for __aeabi_uldivmod
    bsp.o(i.bsp_led_indication) refers to app_timer2.o(i.app_timer_start) for app_timer_start
    bsp.o(i.bsp_led_indication) refers to boards.o(i.bsp_board_led_invert) for bsp_board_led_invert
    bsp.o(i.bsp_led_indication) refers to boards.o(i.bsp_board_leds_on) for bsp_board_leds_on
    bsp.o(i.bsp_led_indication) refers to bsp.o(.data) for .data
    bsp.o(i.bsp_led_indication) refers to bsp.o(.constdata) for .constdata
    bsp.o(i.bsp_wakeup_button_disable) refers to bsp.o(i.wakeup_button_cfg) for wakeup_button_cfg
    bsp.o(i.bsp_wakeup_button_enable) refers to bsp.o(i.wakeup_button_cfg) for wakeup_button_cfg
    bsp.o(i.button_timer_handler) refers to bsp.o(i.bsp_button_event_handler) for bsp_button_event_handler
    bsp.o(i.leds_off) refers to boards.o(i.bsp_board_led_off) for bsp_board_led_off
    bsp.o(i.leds_off) refers to boards.o(i.bsp_board_leds_off) for bsp_board_leds_off
    bsp.o(i.leds_off) refers to bsp.o(.data) for .data
    bsp.o(i.leds_timer_handler) refers to bsp.o(i.bsp_led_indication) for bsp_led_indication
    bsp.o(i.leds_timer_handler) refers to bsp.o(.data) for .data
    bsp.o(i.wakeup_button_cfg) refers to boards.o(i.bsp_board_button_idx_to_pin) for bsp_board_button_idx_to_pin
    bsp.o(.constdata) refers to bsp.o(.bss) for m_bsp_leds_tmr_data
    bsp.o(.constdata) refers to bsp.o(.bss) for m_bsp_alert_tmr_data
    bsp.o(.constdata) refers to bsp.o(.bss) for m_bsp_button_tmr_data
    bsp.o(.constdata) refers to bsp.o(i.bsp_button_event_handler) for bsp_button_event_handler
    bsp_btn_ble.o(i.advertising_buttons_configure) refers to bsp.o(i.bsp_event_to_button_action_assign) for bsp_event_to_button_action_assign
    bsp_btn_ble.o(i.ble_evt_handler) refers to bsp_btn_ble.o(i.advertising_buttons_configure) for advertising_buttons_configure
    bsp_btn_ble.o(i.ble_evt_handler) refers to bsp.o(i.bsp_event_to_button_action_assign) for bsp_event_to_button_action_assign
    bsp_btn_ble.o(i.ble_evt_handler) refers to bsp_btn_ble.o(.data) for .data
    bsp_btn_ble.o(i.bsp_btn_ble_init) refers to bsp.o(i.bsp_button_is_pressed) for bsp_button_is_pressed
    bsp_btn_ble.o(i.bsp_btn_ble_init) refers to bsp_btn_ble.o(i.advertising_buttons_configure) for advertising_buttons_configure
    bsp_btn_ble.o(i.bsp_btn_ble_init) refers to bsp_btn_ble.o(.data) for .data
    bsp_btn_ble.o(i.bsp_btn_ble_sleep_mode_prepare) refers to bsp.o(i.bsp_wakeup_button_enable) for bsp_wakeup_button_enable
    bsp_btn_ble.o(sdh_ble_observers1) refers to bsp_btn_ble.o(i.ble_evt_handler) for ble_evt_handler
    utf.o(i.utf16RuneCount) refers to utf.o(i.utf16DecodeRune) for utf16DecodeRune
    utf.o(i.utf16UTF8Count) refers to utf.o(i.utf16DecodeRune) for utf16DecodeRune
    utf.o(i.utf16UTF8Count) refers to utf.o(i.utf8EncodeRune) for utf8EncodeRune
    utf.o(i.utf8RuneCount) refers to utf.o(i.utf8DecodeRune) for utf8DecodeRune
    utf.o(i.utf8UTF16Count) refers to utf.o(i.utf8DecodeRune) for utf8DecodeRune
    utf.o(i.utf8UTF16Count) refers to utf.o(i.utf16EncodeRune) for utf16EncodeRune
    ble_advdata.o(i.ble_advdata_appearance_find) refers to ble_advdata.o(i.ble_advdata_search) for ble_advdata_search
    ble_advdata.o(i.ble_advdata_encode) refers to ble_advdata.o(i.ble_device_addr_encode) for ble_device_addr_encode
    ble_advdata.o(i.ble_advdata_encode) refers to ble_advdata.o(i.uint16_encode) for uint16_encode
    ble_advdata.o(i.ble_advdata_encode) refers to ble_advdata.o(i.uuid_list_encode) for uuid_list_encode
    ble_advdata.o(i.ble_advdata_encode) refers to ble_advdata.o(i.conn_int_encode) for conn_int_encode
    ble_advdata.o(i.ble_advdata_encode) refers to ble_advdata.o(i.manuf_specific_data_encode) for manuf_specific_data_encode
    ble_advdata.o(i.ble_advdata_encode) refers to ble_advdata.o(i.service_data_encode) for service_data_encode
    ble_advdata.o(i.ble_advdata_encode) refers to ble_advdata.o(i.name_encode) for name_encode
    ble_advdata.o(i.ble_advdata_name_find) refers to ble_advdata.o(i.ble_advdata_search) for ble_advdata_search
    ble_advdata.o(i.ble_advdata_name_find) refers to strlen.o(.text) for strlen
    ble_advdata.o(i.ble_advdata_name_find) refers to memcmp.o(.text) for memcmp
    ble_advdata.o(i.ble_advdata_parse) refers to ble_advdata.o(i.ble_advdata_search) for ble_advdata_search
    ble_advdata.o(i.ble_advdata_short_name_find) refers to ble_advdata.o(i.ble_advdata_search) for ble_advdata_search
    ble_advdata.o(i.ble_advdata_short_name_find) refers to strlen.o(.text) for strlen
    ble_advdata.o(i.ble_advdata_short_name_find) refers to memcmp.o(.text) for memcmp
    ble_advdata.o(i.ble_advdata_uuid_find) refers to ble_advdata.o(i.ble_advdata_search) for ble_advdata_search
    ble_advdata.o(i.ble_advdata_uuid_find) refers to memcmp.o(.text) for memcmp
    ble_advdata.o(i.conn_int_encode) refers to ble_advdata.o(i.uint16_encode) for uint16_encode
    ble_advdata.o(i.manuf_specific_data_encode) refers to ble_advdata.o(i.uint16_encode) for uint16_encode
    ble_advdata.o(i.manuf_specific_data_encode) refers to memcpya.o(.text) for __aeabi_memcpy
    ble_advdata.o(i.service_data_encode) refers to ble_advdata.o(i.uint16_encode) for uint16_encode
    ble_advdata.o(i.service_data_encode) refers to memcpya.o(.text) for __aeabi_memcpy
    ble_advdata.o(i.uuid_list_encode) refers to ble_advdata.o(i.uuid_list_sized_encode) for uuid_list_sized_encode
    ble_advertising.o(i.ble_advertising_advdata_update) refers to ble_advertising.o(i.adv_set_data_size_max_get) for adv_set_data_size_max_get
    ble_advertising.o(i.ble_advertising_advdata_update) refers to ble_advdata.o(i.ble_advdata_encode) for ble_advdata_encode
    ble_advertising.o(i.ble_advertising_init) refers to memcpya.o(.text) for __aeabi_memcpy4
    ble_advertising.o(i.ble_advertising_init) refers to ble_advertising.o(i.adv_set_data_size_max_get) for adv_set_data_size_max_get
    ble_advertising.o(i.ble_advertising_init) refers to ble_advdata.o(i.ble_advdata_encode) for ble_advdata_encode
    ble_advertising.o(i.ble_advertising_modes_config_set) refers to memcpya.o(.text) for __aeabi_memcpy4
    ble_advertising.o(i.ble_advertising_on_ble_evt) refers to ble_advertising.o(i.ble_advertising_start) for ble_advertising_start
    ble_advertising.o(i.ble_advertising_restart_without_whitelist) refers to ble_advertising.o(i.flags_set) for flags_set
    ble_advertising.o(i.ble_advertising_restart_without_whitelist) refers to ble_advertising.o(i.ble_advertising_start) for ble_advertising_start
    ble_advertising.o(i.ble_advertising_start) refers to memseta.o(.text) for __aeabi_memclr4
    ble_advertising.o(i.ble_advertising_start) refers to ble_advertising.o(i.phy_is_valid) for phy_is_valid
    ble_advertising.o(i.ble_advertising_start) refers to ble_advertising.o(i.use_whitelist) for use_whitelist
    ble_advertising.o(i.ble_advertising_start) refers to ble_advertising.o(i.flags_set) for flags_set
    ble_advertising.o(i.flags_set) refers to ble_advdata.o(i.ble_advdata_parse) for ble_advdata_parse
    ble_conn_params.o(i.ble_conn_params_change_conn_params) refers to ble_conn_params.o(i.instance_get) for instance_get
    ble_conn_params.o(i.ble_conn_params_change_conn_params) refers to ble_conn_params.o(.data) for .data
    ble_conn_params.o(i.ble_conn_params_init) refers to memcpya.o(.text) for __aeabi_memcpy4
    ble_conn_params.o(i.ble_conn_params_init) refers to app_timer2.o(i.app_timer_create) for app_timer_create
    ble_conn_params.o(i.ble_conn_params_init) refers to ble_conn_params.o(.bss) for .bss
    ble_conn_params.o(i.ble_conn_params_init) refers to ble_conn_params.o(.data) for .data
    ble_conn_params.o(i.ble_conn_params_init) refers to ble_conn_params.o(i.update_timeout_handler) for update_timeout_handler
    ble_conn_params.o(i.ble_conn_params_stop) refers to app_timer2.o(i.app_timer_stop) for app_timer_stop
    ble_conn_params.o(i.ble_conn_params_stop) refers to ble_conn_params.o(.bss) for .bss
    ble_conn_params.o(i.ble_evt_handler) refers to ble_conn_params.o(i.instance_get) for instance_get
    ble_conn_params.o(i.ble_evt_handler) refers to ble_srv_common.o(i.ble_srv_is_notification_enabled) for ble_srv_is_notification_enabled
    ble_conn_params.o(i.ble_evt_handler) refers to ble_conn_params.o(i.is_conn_params_ok) for is_conn_params_ok
    ble_conn_params.o(i.ble_evt_handler) refers to app_timer2.o(i.app_timer_stop) for app_timer_stop
    ble_conn_params.o(i.ble_evt_handler) refers to ble_conn_params.o(i.send_error_evt) for send_error_evt
    ble_conn_params.o(i.ble_evt_handler) refers to ble_conn_params.o(i.conn_params_negotiation) for conn_params_negotiation
    ble_conn_params.o(i.ble_evt_handler) refers to ble_conn_params.o(.bss) for .bss
    ble_conn_params.o(i.ble_evt_handler) refers to ble_conn_params.o(.data) for .data
    ble_conn_params.o(i.conn_params_negotiation) refers to app_timer2.o(i.app_timer_start) for app_timer_start
    ble_conn_params.o(i.conn_params_negotiation) refers to ble_conn_params.o(i.send_error_evt) for send_error_evt
    ble_conn_params.o(i.conn_params_negotiation) refers to ble_conn_params.o(.bss) for .bss
    ble_conn_params.o(i.instance_get) refers to ble_conn_params.o(.bss) for .bss
    ble_conn_params.o(i.send_error_evt) refers to ble_conn_params.o(.bss) for .bss
    ble_conn_params.o(i.update_timeout_handler) refers to ble_conn_params.o(i.instance_get) for instance_get
    ble_conn_params.o(i.update_timeout_handler) refers to ble_conn_params.o(i.send_error_evt) for send_error_evt
    ble_conn_params.o(i.update_timeout_handler) refers to ble_conn_params.o(.bss) for .bss
    ble_conn_params.o(sdh_ble_observers1) refers to ble_conn_params.o(i.ble_evt_handler) for ble_evt_handler
    ble_conn_state.o(i.active_flag_count) refers to nrf_atflags.o(i.nrf_atflags_get) for nrf_atflags_get
    ble_conn_state.o(i.bcs_internal_state_reset) refers to memseta.o(.text) for __aeabi_memclr4
    ble_conn_state.o(i.bcs_internal_state_reset) refers to ble_conn_state.o(.bss) for .bss
    ble_conn_state.o(i.ble_conn_state_central_conn_count) refers to nrf_atomic.o(i.nrf_atomic_u32_and) for nrf_atomic_u32_and
    ble_conn_state.o(i.ble_conn_state_central_conn_count) refers to ble_conn_state.o(i.active_flag_count) for active_flag_count
    ble_conn_state.o(i.ble_conn_state_central_conn_count) refers to ble_conn_state.o(.bss) for .bss
    ble_conn_state.o(i.ble_conn_state_central_handles) refers to nrf_atomic.o(i.nrf_atomic_u32_and) for nrf_atomic_u32_and
    ble_conn_state.o(i.ble_conn_state_central_handles) refers to ble_conn_state.o(i.conn_handle_list_get) for conn_handle_list_get
    ble_conn_state.o(i.ble_conn_state_central_handles) refers to ble_conn_state.o(.bss) for .bss
    ble_conn_state.o(i.ble_conn_state_conn_count) refers to ble_conn_state.o(i.active_flag_count) for active_flag_count
    ble_conn_state.o(i.ble_conn_state_conn_count) refers to ble_conn_state.o(.bss) for .bss
    ble_conn_state.o(i.ble_conn_state_conn_handles) refers to ble_conn_state.o(i.conn_handle_list_get) for conn_handle_list_get
    ble_conn_state.o(i.ble_conn_state_conn_handles) refers to ble_conn_state.o(.bss) for .bss
    ble_conn_state.o(i.ble_conn_state_conn_idx) refers to ble_conn_state.o(i.ble_conn_state_valid) for ble_conn_state_valid
    ble_conn_state.o(i.ble_conn_state_encrypted) refers to ble_conn_state.o(i.ble_conn_state_valid) for ble_conn_state_valid
    ble_conn_state.o(i.ble_conn_state_encrypted) refers to nrf_atflags.o(i.nrf_atflags_get) for nrf_atflags_get
    ble_conn_state.o(i.ble_conn_state_encrypted) refers to ble_conn_state.o(.bss) for .bss
    ble_conn_state.o(i.ble_conn_state_for_each_connected) refers to ble_conn_state.o(i.for_each_set_flag) for for_each_set_flag
    ble_conn_state.o(i.ble_conn_state_for_each_connected) refers to ble_conn_state.o(.bss) for .bss
    ble_conn_state.o(i.ble_conn_state_for_each_set_user_flag) refers to ble_conn_state.o(i.user_flag_is_acquired) for user_flag_is_acquired
    ble_conn_state.o(i.ble_conn_state_for_each_set_user_flag) refers to ble_conn_state.o(i.for_each_set_flag) for for_each_set_flag
    ble_conn_state.o(i.ble_conn_state_for_each_set_user_flag) refers to ble_conn_state.o(.bss) for .bss
    ble_conn_state.o(i.ble_conn_state_init) refers to ble_conn_state.o(i.bcs_internal_state_reset) for bcs_internal_state_reset
    ble_conn_state.o(i.ble_conn_state_lesc) refers to ble_conn_state.o(i.ble_conn_state_valid) for ble_conn_state_valid
    ble_conn_state.o(i.ble_conn_state_lesc) refers to nrf_atflags.o(i.nrf_atflags_get) for nrf_atflags_get
    ble_conn_state.o(i.ble_conn_state_lesc) refers to ble_conn_state.o(.bss) for .bss
    ble_conn_state.o(i.ble_conn_state_mitm_protected) refers to ble_conn_state.o(i.ble_conn_state_valid) for ble_conn_state_valid
    ble_conn_state.o(i.ble_conn_state_mitm_protected) refers to nrf_atflags.o(i.nrf_atflags_get) for nrf_atflags_get
    ble_conn_state.o(i.ble_conn_state_mitm_protected) refers to ble_conn_state.o(.bss) for .bss
    ble_conn_state.o(i.ble_conn_state_periph_handles) refers to nrf_atomic.o(i.nrf_atomic_u32_and) for nrf_atomic_u32_and
    ble_conn_state.o(i.ble_conn_state_periph_handles) refers to ble_conn_state.o(i.conn_handle_list_get) for conn_handle_list_get
    ble_conn_state.o(i.ble_conn_state_periph_handles) refers to ble_conn_state.o(.bss) for .bss
    ble_conn_state.o(i.ble_conn_state_peripheral_conn_count) refers to nrf_atomic.o(i.nrf_atomic_u32_and) for nrf_atomic_u32_and
    ble_conn_state.o(i.ble_conn_state_peripheral_conn_count) refers to ble_conn_state.o(i.active_flag_count) for active_flag_count
    ble_conn_state.o(i.ble_conn_state_peripheral_conn_count) refers to ble_conn_state.o(.bss) for .bss
    ble_conn_state.o(i.ble_conn_state_role) refers to ble_conn_state.o(i.ble_conn_state_valid) for ble_conn_state_valid
    ble_conn_state.o(i.ble_conn_state_role) refers to nrf_atflags.o(i.nrf_atflags_get) for nrf_atflags_get
    ble_conn_state.o(i.ble_conn_state_role) refers to ble_conn_state.o(.bss) for .bss
    ble_conn_state.o(i.ble_conn_state_status) refers to ble_conn_state.o(i.ble_conn_state_valid) for ble_conn_state_valid
    ble_conn_state.o(i.ble_conn_state_status) refers to nrf_atflags.o(i.nrf_atflags_get) for nrf_atflags_get
    ble_conn_state.o(i.ble_conn_state_status) refers to ble_conn_state.o(.bss) for .bss
    ble_conn_state.o(i.ble_conn_state_user_flag_acquire) refers to nrf_atflags.o(i.nrf_atflags_find_and_set_flag) for nrf_atflags_find_and_set_flag
    ble_conn_state.o(i.ble_conn_state_user_flag_acquire) refers to ble_conn_state.o(.bss) for .bss
    ble_conn_state.o(i.ble_conn_state_user_flag_get) refers to ble_conn_state.o(i.user_flag_is_acquired) for user_flag_is_acquired
    ble_conn_state.o(i.ble_conn_state_user_flag_get) refers to ble_conn_state.o(i.ble_conn_state_valid) for ble_conn_state_valid
    ble_conn_state.o(i.ble_conn_state_user_flag_get) refers to nrf_atflags.o(i.nrf_atflags_get) for nrf_atflags_get
    ble_conn_state.o(i.ble_conn_state_user_flag_get) refers to ble_conn_state.o(.bss) for .bss
    ble_conn_state.o(i.ble_conn_state_user_flag_set) refers to ble_conn_state.o(i.user_flag_is_acquired) for user_flag_is_acquired
    ble_conn_state.o(i.ble_conn_state_user_flag_set) refers to ble_conn_state.o(i.ble_conn_state_valid) for ble_conn_state_valid
    ble_conn_state.o(i.ble_conn_state_user_flag_set) refers to ble_conn_state.o(i.flag_toggle) for flag_toggle
    ble_conn_state.o(i.ble_conn_state_user_flag_set) refers to ble_conn_state.o(.bss) for .bss
    ble_conn_state.o(i.ble_conn_state_valid) refers to nrf_atflags.o(i.nrf_atflags_get) for nrf_atflags_get
    ble_conn_state.o(i.ble_conn_state_valid) refers to ble_conn_state.o(.bss) for .bss
    ble_conn_state.o(i.ble_evt_handler) refers to nrf_atomic.o(i.nrf_atomic_u32_and) for nrf_atomic_u32_and
    ble_conn_state.o(i.ble_evt_handler) refers to ble_conn_state.o(i.conn_handle_list_get) for conn_handle_list_get
    ble_conn_state.o(i.ble_evt_handler) refers to memcpya.o(.text) for __aeabi_memcpy4
    ble_conn_state.o(i.ble_evt_handler) refers to nrf_atflags.o(i.nrf_atflags_clear) for nrf_atflags_clear
    ble_conn_state.o(i.ble_evt_handler) refers to app_error.o(i.app_error_handler_bare) for app_error_handler_bare
    ble_conn_state.o(i.ble_evt_handler) refers to nrf_atflags.o(i.nrf_atflags_set) for nrf_atflags_set
    ble_conn_state.o(i.ble_evt_handler) refers to ble_conn_state.o(i.flag_toggle) for flag_toggle
    ble_conn_state.o(i.ble_evt_handler) refers to ble_conn_state.o(.bss) for .bss
    ble_conn_state.o(i.conn_handle_list_get) refers to nrf_atflags.o(i.nrf_atflags_get) for nrf_atflags_get
    ble_conn_state.o(i.conn_handle_list_get) refers to memcpya.o(.text) for __aeabi_memcpy4
    ble_conn_state.o(i.flag_toggle) refers to nrf_atflags.o(i.nrf_atflags_set) for nrf_atflags_set
    ble_conn_state.o(i.flag_toggle) refers to nrf_atflags.o(i.nrf_atflags_clear) for nrf_atflags_clear
    ble_conn_state.o(i.for_each_set_flag) refers to nrf_atflags.o(i.nrf_atflags_get) for nrf_atflags_get
    ble_conn_state.o(i.user_flag_is_acquired) refers to nrf_atflags.o(i.nrf_atflags_get) for nrf_atflags_get
    ble_conn_state.o(i.user_flag_is_acquired) refers to ble_conn_state.o(.bss) for .bss
    ble_conn_state.o(sdh_ble_observers0) refers to ble_conn_state.o(i.ble_evt_handler) for ble_evt_handler
    ble_link_ctx_manager.o(i.blcm_link_ctx_get) refers to ble_conn_state.o(i.ble_conn_state_conn_idx) for ble_conn_state_conn_idx
    ble_srv_common.o(i.ble_srv_ascii_to_utf8) refers to strlen.o(.text) for strlen
    ble_srv_common.o(i.characteristic_add) refers to ble_srv_common.o(i.set_security_req) for set_security_req
    ble_srv_common.o(i.characteristic_add) refers to memseta.o(.text) for __aeabi_memclr4
    ble_srv_common.o(i.descriptor_add) refers to memseta.o(.text) for __aeabi_memclr4
    ble_srv_common.o(i.descriptor_add) refers to ble_srv_common.o(i.set_security_req) for set_security_req
    nrf_ble_gatt.o(i.nrf_ble_gatt_data_length_set) refers to nrf_ble_gatt.o(i.data_length_update) for data_length_update
    nrf_ble_gatt.o(i.nrf_ble_gatt_init) refers to nrf_ble_gatt.o(i.link_init) for link_init
    nrf_ble_gatt.o(i.nrf_ble_gatt_on_ble_evt) refers to nrf_ble_gatt.o(i.data_length_update) for data_length_update
    nrf_ble_gatt.o(i.nrf_ble_gatt_on_ble_evt) refers to nrf_ble_gatt.o(i.link_init) for link_init
    nrf_ble_gatt.o(i.nrf_ble_gatt_on_ble_evt) refers to memcpya.o(.text) for __aeabi_memcpy4
    nrf_ble_gatt.o(i.nrf_ble_gatt_on_ble_evt) refers to nrf_ble_gatt.o(.constdata) for .constdata
    nrf_ble_qwr.o(i.nrf_ble_qwr_on_ble_evt) refers to nrf_ble_qwr.o(i.user_mem_reply) for user_mem_reply
    ble_nus.o(i.ble_nus_data_send) refers to ble_link_ctx_manager.o(i.blcm_link_ctx_get) for blcm_link_ctx_get
    ble_nus.o(i.ble_nus_init) refers to memseta.o(.text) for __aeabi_memclr4
    ble_nus.o(i.ble_nus_init) refers to ble_srv_common.o(i.characteristic_add) for characteristic_add
    ble_nus.o(i.ble_nus_init) refers to ble_nus.o(.constdata) for .constdata
    ble_nus.o(i.ble_nus_on_ble_evt) refers to ble_link_ctx_manager.o(i.blcm_link_ctx_get) for blcm_link_ctx_get
    ble_nus.o(i.ble_nus_on_ble_evt) refers to memseta.o(.text) for __aeabi_memclr4
    ble_nus.o(i.ble_nus_on_ble_evt) refers to ble_nus.o(i.on_connect) for on_connect
    ble_nus.o(i.ble_nus_on_ble_evt) refers to ble_nus.o(i.on_write) for on_write
    ble_nus.o(i.on_connect) refers to ble_link_ctx_manager.o(i.blcm_link_ctx_get) for blcm_link_ctx_get
    ble_nus.o(i.on_connect) refers to ble_srv_common.o(i.ble_srv_is_notification_enabled) for ble_srv_is_notification_enabled
    ble_nus.o(i.on_connect) refers to memseta.o(.text) for __aeabi_memclr4
    ble_nus.o(i.on_write) refers to ble_link_ctx_manager.o(i.blcm_link_ctx_get) for blcm_link_ctx_get
    ble_nus.o(i.on_write) refers to memseta.o(.text) for __aeabi_memclr4
    ble_nus.o(i.on_write) refers to ble_srv_common.o(i.ble_srv_is_notification_enabled) for ble_srv_is_notification_enabled
    nrf_drv_clock.o(i.clock_clk_started_notify) refers to nrf_drv_clock.o(.bss) for .bss
    nrf_drv_clock.o(i.clock_irq_handler) refers to nrf_drv_clock.o(i.clock_clk_started_notify) for clock_clk_started_notify
    nrf_drv_clock.o(i.clock_irq_handler) refers to nrf_drv_clock.o(.bss) for .bss
    nrf_drv_clock.o(i.nrf_drv_clock_hfclk_is_running) refers to nrf_sdh.o(i.nrf_sdh_is_enabled) for nrf_sdh_is_enabled
    nrf_drv_clock.o(i.nrf_drv_clock_hfclk_release) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    nrf_drv_clock.o(i.nrf_drv_clock_hfclk_release) refers to nrf_sdh.o(i.nrf_sdh_is_enabled) for nrf_sdh_is_enabled
    nrf_drv_clock.o(i.nrf_drv_clock_hfclk_release) refers to nrfx_clock.o(i.nrfx_clock_hfclk_stop) for nrfx_clock_hfclk_stop
    nrf_drv_clock.o(i.nrf_drv_clock_hfclk_release) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    nrf_drv_clock.o(i.nrf_drv_clock_hfclk_release) refers to nrf_drv_clock.o(.bss) for .bss
    nrf_drv_clock.o(i.nrf_drv_clock_hfclk_request) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    nrf_drv_clock.o(i.nrf_drv_clock_hfclk_request) refers to nrf_drv_clock.o(i.item_enqueue) for item_enqueue
    nrf_drv_clock.o(i.nrf_drv_clock_hfclk_request) refers to nrf_sdh.o(i.nrf_sdh_is_enabled) for nrf_sdh_is_enabled
    nrf_drv_clock.o(i.nrf_drv_clock_hfclk_request) refers to nrfx_clock.o(i.nrfx_clock_hfclk_start) for nrfx_clock_hfclk_start
    nrf_drv_clock.o(i.nrf_drv_clock_hfclk_request) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    nrf_drv_clock.o(i.nrf_drv_clock_hfclk_request) refers to nrf_drv_clock.o(.bss) for .bss
    nrf_drv_clock.o(i.nrf_drv_clock_init) refers to nrfx_clock.o(i.nrfx_clock_init) for nrfx_clock_init
    nrf_drv_clock.o(i.nrf_drv_clock_init) refers to nrf_sdh.o(i.nrf_sdh_is_enabled) for nrf_sdh_is_enabled
    nrf_drv_clock.o(i.nrf_drv_clock_init) refers to nrfx_clock.o(i.nrfx_clock_enable) for nrfx_clock_enable
    nrf_drv_clock.o(i.nrf_drv_clock_init) refers to nrf_drv_clock.o(i.nrf_wdt_started) for nrf_wdt_started
    nrf_drv_clock.o(i.nrf_drv_clock_init) refers to nrf_drv_clock.o(.bss) for .bss
    nrf_drv_clock.o(i.nrf_drv_clock_init) refers to nrf_drv_clock.o(i.clock_irq_handler) for clock_irq_handler
    nrf_drv_clock.o(i.nrf_drv_clock_init_check) refers to nrf_drv_clock.o(.bss) for .bss
    nrf_drv_clock.o(i.nrf_drv_clock_lfclk_is_running) refers to nrf_sdh.o(i.nrf_sdh_is_enabled) for nrf_sdh_is_enabled
    nrf_drv_clock.o(i.nrf_drv_clock_lfclk_release) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    nrf_drv_clock.o(i.nrf_drv_clock_lfclk_release) refers to nrf_drv_clock.o(i.nrf_wdt_started) for nrf_wdt_started
    nrf_drv_clock.o(i.nrf_drv_clock_lfclk_release) refers to nrfx_clock.o(i.nrfx_clock_lfclk_stop) for nrfx_clock_lfclk_stop
    nrf_drv_clock.o(i.nrf_drv_clock_lfclk_release) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    nrf_drv_clock.o(i.nrf_drv_clock_lfclk_release) refers to nrf_drv_clock.o(.bss) for .bss
    nrf_drv_clock.o(i.nrf_drv_clock_lfclk_request) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    nrf_drv_clock.o(i.nrf_drv_clock_lfclk_request) refers to nrf_drv_clock.o(i.item_enqueue) for item_enqueue
    nrf_drv_clock.o(i.nrf_drv_clock_lfclk_request) refers to nrfx_clock.o(i.nrfx_clock_lfclk_start) for nrfx_clock_lfclk_start
    nrf_drv_clock.o(i.nrf_drv_clock_lfclk_request) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    nrf_drv_clock.o(i.nrf_drv_clock_lfclk_request) refers to nrf_drv_clock.o(.bss) for .bss
    nrf_drv_clock.o(i.nrf_drv_clock_uninit) refers to nrfx_clock.o(i.nrfx_clock_disable) for nrfx_clock_disable
    nrf_drv_clock.o(i.nrf_drv_clock_uninit) refers to nrfx_clock.o(i.nrfx_clock_uninit) for nrfx_clock_uninit
    nrf_drv_clock.o(i.nrf_drv_clock_uninit) refers to nrf_drv_clock.o(.bss) for .bss
    nrf_drv_clock.o(i.sd_state_evt_handler) refers to nrfx_clock.o(i.nrfx_clock_enable) for nrfx_clock_enable
    nrf_drv_clock.o(i.sd_state_evt_handler) refers to nrf_drv_clock.o(i.nrf_drv_clock_lfclk_release) for nrf_drv_clock_lfclk_release
    nrf_drv_clock.o(i.sd_state_evt_handler) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    nrf_drv_clock.o(i.sd_state_evt_handler) refers to nrf_drv_clock.o(i.nrf_drv_clock_init) for nrf_drv_clock_init
    nrf_drv_clock.o(i.sd_state_evt_handler) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    nrf_drv_clock.o(i.sd_state_evt_handler) refers to nrf_drv_clock.o(.bss) for .bss
    nrf_drv_clock.o(i.soc_evt_handler) refers to nrf_drv_clock.o(i.clock_clk_started_notify) for clock_clk_started_notify
    nrf_drv_clock.o(i.soc_evt_handler) refers to nrf_drv_clock.o(.bss) for .bss
    nrf_drv_clock.o(sdh_soc_observers0) refers to nrf_drv_clock.o(i.soc_evt_handler) for soc_evt_handler
    nrf_drv_clock.o(sdh_state_observers0) refers to nrf_drv_clock.o(i.sd_state_evt_handler) for sd_state_evt_handler
    nrf_drv_uart.o(i.nrf_drv_uart_init) refers to memcpya.o(.text) for __aeabi_memcpy4
    nrf_drv_uart.o(i.nrf_drv_uart_init) refers to nrfx_uarte.o(i.nrfx_uarte_init) for nrfx_uarte_init
    nrf_drv_uart.o(i.nrf_drv_uart_init) refers to nrfx_uart.o(i.nrfx_uart_init) for nrfx_uart_init
    nrf_drv_uart.o(i.nrf_drv_uart_init) refers to nrf_drv_uart.o(.data) for .data
    nrf_drv_uart.o(i.nrf_drv_uart_init) refers to nrf_drv_uart.o(i.uarte_evt_handler) for uarte_evt_handler
    nrf_drv_uart.o(i.nrf_drv_uart_init) refers to nrf_drv_uart.o(i.uart_evt_handler) for uart_evt_handler
    nrf_drv_uart.o(i.uart_evt_handler) refers to nrf_drv_uart.o(.data) for .data
    nrf_drv_uart.o(i.uarte_evt_handler) refers to nrf_drv_uart.o(.data) for .data
    nrfx_atomic.o(i.nrfx_atomic_flag_clear) refers to nrfx_atomic.o(i.nrfx_atomic_u32_and) for nrfx_atomic_u32_and
    nrfx_atomic.o(i.nrfx_atomic_flag_clear_fetch) refers to nrfx_atomic.o(i.nrfx_atomic_u32_fetch_and) for nrfx_atomic_u32_fetch_and
    nrfx_atomic.o(i.nrfx_atomic_flag_set) refers to nrfx_atomic.o(i.nrfx_atomic_u32_or) for nrfx_atomic_u32_or
    nrfx_atomic.o(i.nrfx_atomic_flag_set_fetch) refers to nrfx_atomic.o(i.nrfx_atomic_u32_fetch_or) for nrfx_atomic_u32_fetch_or
    nrfx_atomic.o(i.nrfx_atomic_u32_add) refers to nrfx_atomic.o(.emb_text) for __asm___13_nrfx_atomic_c_3bd32246__nrfx_atomic_internal_add
    nrfx_atomic.o(i.nrfx_atomic_u32_and) refers to nrfx_atomic.o(.emb_text) for __asm___13_nrfx_atomic_c_3bd32246__nrfx_atomic_internal_and
    nrfx_atomic.o(i.nrfx_atomic_u32_cmp_exch) refers to nrfx_atomic.o(.emb_text) for __asm___13_nrfx_atomic_c_3bd32246__nrfx_atomic_internal_cmp_exch
    nrfx_atomic.o(i.nrfx_atomic_u32_fetch_add) refers to nrfx_atomic.o(.emb_text) for __asm___13_nrfx_atomic_c_3bd32246__nrfx_atomic_internal_add
    nrfx_atomic.o(i.nrfx_atomic_u32_fetch_and) refers to nrfx_atomic.o(.emb_text) for __asm___13_nrfx_atomic_c_3bd32246__nrfx_atomic_internal_and
    nrfx_atomic.o(i.nrfx_atomic_u32_fetch_or) refers to nrfx_atomic.o(.emb_text) for __asm___13_nrfx_atomic_c_3bd32246__nrfx_atomic_internal_orr
    nrfx_atomic.o(i.nrfx_atomic_u32_fetch_store) refers to nrfx_atomic.o(.emb_text) for __asm___13_nrfx_atomic_c_3bd32246__nrfx_atomic_internal_mov
    nrfx_atomic.o(i.nrfx_atomic_u32_fetch_sub) refers to nrfx_atomic.o(.emb_text) for __asm___13_nrfx_atomic_c_3bd32246__nrfx_atomic_internal_sub
    nrfx_atomic.o(i.nrfx_atomic_u32_fetch_sub_hs) refers to nrfx_atomic.o(.emb_text) for __asm___13_nrfx_atomic_c_3bd32246__nrfx_atomic_internal_sub_hs
    nrfx_atomic.o(i.nrfx_atomic_u32_fetch_xor) refers to nrfx_atomic.o(.emb_text) for __asm___13_nrfx_atomic_c_3bd32246__nrfx_atomic_internal_eor
    nrfx_atomic.o(i.nrfx_atomic_u32_or) refers to nrfx_atomic.o(.emb_text) for __asm___13_nrfx_atomic_c_3bd32246__nrfx_atomic_internal_orr
    nrfx_atomic.o(i.nrfx_atomic_u32_store) refers to nrfx_atomic.o(.emb_text) for __asm___13_nrfx_atomic_c_3bd32246__nrfx_atomic_internal_mov
    nrfx_atomic.o(i.nrfx_atomic_u32_sub) refers to nrfx_atomic.o(.emb_text) for __asm___13_nrfx_atomic_c_3bd32246__nrfx_atomic_internal_sub
    nrfx_atomic.o(i.nrfx_atomic_u32_sub_hs) refers to nrfx_atomic.o(.emb_text) for __asm___13_nrfx_atomic_c_3bd32246__nrfx_atomic_internal_sub_hs
    nrfx_atomic.o(i.nrfx_atomic_u32_xor) refers to nrfx_atomic.o(.emb_text) for __asm___13_nrfx_atomic_c_3bd32246__nrfx_atomic_internal_eor
    nrfx_clock.o(i.POWER_CLOCK_IRQHandler) refers to nrfx_clock.o(i.nrf_clock_event_check) for nrf_clock_event_check
    nrfx_clock.o(i.POWER_CLOCK_IRQHandler) refers to nrfx_clock.o(i.nrf_clock_event_clear) for nrf_clock_event_clear
    nrfx_clock.o(i.POWER_CLOCK_IRQHandler) refers to nrfx_clock.o(.data) for .data
    nrfx_clock.o(i.nrfx_clock_hfclk_start) refers to nrfx_clock.o(i.nrf_clock_event_clear) for nrf_clock_event_clear
    nrfx_clock.o(i.nrfx_clock_hfclk_stop) refers to nrfx_clock.o(i.nrf_clock_event_clear) for nrf_clock_event_clear
    nrfx_clock.o(i.nrfx_clock_hfclk_stop) refers to nrfx_clock.o(.data) for .data
    nrfx_clock.o(i.nrfx_clock_init) refers to nrfx_clock.o(.data) for .data
    nrfx_clock.o(i.nrfx_clock_lfclk_start) refers to nrfx_clock.o(i.nrf_clock_event_clear) for nrf_clock_event_clear
    nrfx_clock.o(i.nrfx_clock_lfclk_stop) refers to nrfx_clock.o(i.nrf_clock_event_clear) for nrf_clock_event_clear
    nrfx_clock.o(i.nrfx_clock_uninit) refers to nrfx_clock.o(i.nrfx_clock_lfclk_stop) for nrfx_clock_lfclk_stop
    nrfx_clock.o(i.nrfx_clock_uninit) refers to nrfx_clock.o(i.nrfx_clock_hfclk_stop) for nrfx_clock_hfclk_stop
    nrfx_clock.o(i.nrfx_clock_uninit) refers to nrfx_clock.o(.data) for .data
    nrfx_gpiote.o(i.GPIOTE_IRQHandler) refers to nrfx_gpiote.o(i.nrf_gpiote_event_is_set) for nrf_gpiote_event_is_set
    nrfx_gpiote.o(i.GPIOTE_IRQHandler) refers to nrfx_gpiote.o(i.nrf_gpiote_event_clear) for nrf_gpiote_event_clear
    nrfx_gpiote.o(i.GPIOTE_IRQHandler) refers to nrfx_gpiote.o(i.nrf_gpio_latches_read_and_clear) for nrf_gpio_latches_read_and_clear
    nrfx_gpiote.o(i.GPIOTE_IRQHandler) refers to nrfx_gpiote.o(i.port_event_handle) for port_event_handle
    nrfx_gpiote.o(i.GPIOTE_IRQHandler) refers to nrfx_gpiote.o(.bss) for .bss
    nrfx_gpiote.o(i.channel_free) refers to nrfx_gpiote.o(.bss) for .bss
    nrfx_gpiote.o(i.channel_port_alloc) refers to nrfx_gpiote.o(.bss) for .bss
    nrfx_gpiote.o(i.channel_port_get) refers to nrfx_gpiote.o(.bss) for .bss
    nrfx_gpiote.o(i.nrf_gpio_cfg_default) refers to nrfx_gpiote.o(i.nrf_gpio_cfg) for nrf_gpio_cfg
    nrfx_gpiote.o(i.nrf_gpio_latches_read_and_clear) refers to nrfx_gpiote.o(.constdata) for .constdata
    nrfx_gpiote.o(i.nrfx_gpiote_clr_task_addr_get) refers to nrfx_gpiote.o(i.nrfx_gpiote_clr_task_get) for nrfx_gpiote_clr_task_get
    nrfx_gpiote.o(i.nrfx_gpiote_clr_task_get) refers to nrfx_gpiote.o(i.channel_port_get) for channel_port_get
    nrfx_gpiote.o(i.nrfx_gpiote_clr_task_trigger) refers to nrfx_gpiote.o(i.channel_port_get) for channel_port_get
    nrfx_gpiote.o(i.nrfx_gpiote_in_event_addr_get) refers to nrfx_gpiote.o(i.nrfx_gpiote_in_event_get) for nrfx_gpiote_in_event_get
    nrfx_gpiote.o(i.nrfx_gpiote_in_event_disable) refers to nrfx_gpiote.o(i.pin_in_use_by_port) for pin_in_use_by_port
    nrfx_gpiote.o(i.nrfx_gpiote_in_event_disable) refers to nrfx_gpiote.o(i.nrf_gpio_cfg_sense_set) for nrf_gpio_cfg_sense_set
    nrfx_gpiote.o(i.nrfx_gpiote_in_event_disable) refers to nrfx_gpiote.o(i.pin_in_use_by_te) for pin_in_use_by_te
    nrfx_gpiote.o(i.nrfx_gpiote_in_event_disable) refers to nrfx_gpiote.o(i.channel_port_get) for channel_port_get
    nrfx_gpiote.o(i.nrfx_gpiote_in_event_enable) refers to nrfx_gpiote.o(i.pin_in_use_by_port) for pin_in_use_by_port
    nrfx_gpiote.o(i.nrfx_gpiote_in_event_enable) refers to nrfx_gpiote.o(i.channel_port_get) for channel_port_get
    nrfx_gpiote.o(i.nrfx_gpiote_in_event_enable) refers to nrfx_gpiote.o(i.port_handler_polarity_get) for port_handler_polarity_get
    nrfx_gpiote.o(i.nrfx_gpiote_in_event_enable) refers to nrfx_gpiote.o(i.nrf_gpio_cfg_sense_set) for nrf_gpio_cfg_sense_set
    nrfx_gpiote.o(i.nrfx_gpiote_in_event_enable) refers to nrfx_gpiote.o(i.pin_in_use_by_te) for pin_in_use_by_te
    nrfx_gpiote.o(i.nrfx_gpiote_in_event_enable) refers to nrfx_gpiote.o(i.nrf_gpiote_event_clear) for nrf_gpiote_event_clear
    nrfx_gpiote.o(i.nrfx_gpiote_in_event_enable) refers to nrfx_gpiote.o(.bss) for .bss
    nrfx_gpiote.o(i.nrfx_gpiote_in_event_get) refers to nrfx_gpiote.o(i.pin_in_use_by_te) for pin_in_use_by_te
    nrfx_gpiote.o(i.nrfx_gpiote_in_event_get) refers to nrfx_gpiote.o(i.channel_port_get) for channel_port_get
    nrfx_gpiote.o(i.nrfx_gpiote_in_init) refers to nrfx_gpiote.o(i.channel_port_alloc) for channel_port_alloc
    nrfx_gpiote.o(i.nrfx_gpiote_in_init) refers to nrfx_gpiote.o(i.nrf_gpio_cfg) for nrf_gpio_cfg
    nrfx_gpiote.o(i.nrfx_gpiote_in_init) refers to nrfx_gpiote.o(i.pin_configured_set) for pin_configured_set
    nrfx_gpiote.o(i.nrfx_gpiote_in_init) refers to nrfx_gpiote.o(.bss) for .bss
    nrfx_gpiote.o(i.nrfx_gpiote_in_uninit) refers to nrfx_gpiote.o(i.nrfx_gpiote_in_event_disable) for nrfx_gpiote_in_event_disable
    nrfx_gpiote.o(i.nrfx_gpiote_in_uninit) refers to nrfx_gpiote.o(i.pin_in_use_by_te) for pin_in_use_by_te
    nrfx_gpiote.o(i.nrfx_gpiote_in_uninit) refers to nrfx_gpiote.o(i.channel_port_get) for channel_port_get
    nrfx_gpiote.o(i.nrfx_gpiote_in_uninit) refers to nrfx_gpiote.o(i.pin_configured_check) for pin_configured_check
    nrfx_gpiote.o(i.nrfx_gpiote_in_uninit) refers to nrfx_gpiote.o(i.nrf_gpio_cfg_default) for nrf_gpio_cfg_default
    nrfx_gpiote.o(i.nrfx_gpiote_in_uninit) refers to nrfx_gpiote.o(i.pin_configured_clear) for pin_configured_clear
    nrfx_gpiote.o(i.nrfx_gpiote_in_uninit) refers to nrfx_gpiote.o(i.channel_free) for channel_free
    nrfx_gpiote.o(i.nrfx_gpiote_in_uninit) refers to nrfx_gpiote.o(.bss) for .bss
    nrfx_gpiote.o(i.nrfx_gpiote_init) refers to nrfx_gpiote.o(i.nrf_gpio_pin_present_check) for nrf_gpio_pin_present_check
    nrfx_gpiote.o(i.nrfx_gpiote_init) refers to nrfx_gpiote.o(i.channel_free) for channel_free
    nrfx_gpiote.o(i.nrfx_gpiote_init) refers to nrfx_gpiote.o(i.nrf_gpiote_event_clear) for nrf_gpiote_event_clear
    nrfx_gpiote.o(i.nrfx_gpiote_init) refers to nrfx_gpiote.o(.bss) for .bss
    nrfx_gpiote.o(i.nrfx_gpiote_is_init) refers to nrfx_gpiote.o(.bss) for .bss
    nrfx_gpiote.o(i.nrfx_gpiote_out_init) refers to nrfx_gpiote.o(i.channel_port_alloc) for channel_port_alloc
    nrfx_gpiote.o(i.nrfx_gpiote_out_init) refers to nrfx_gpiote.o(i.nrf_gpio_cfg) for nrf_gpio_cfg
    nrfx_gpiote.o(i.nrfx_gpiote_out_init) refers to nrfx_gpiote.o(i.pin_configured_set) for pin_configured_set
    nrfx_gpiote.o(i.nrfx_gpiote_out_init) refers to nrfx_gpiote.o(.bss) for .bss
    nrfx_gpiote.o(i.nrfx_gpiote_out_task_addr_get) refers to nrfx_gpiote.o(i.nrfx_gpiote_out_task_get) for nrfx_gpiote_out_task_get
    nrfx_gpiote.o(i.nrfx_gpiote_out_task_disable) refers to nrfx_gpiote.o(.bss) for .bss
    nrfx_gpiote.o(i.nrfx_gpiote_out_task_enable) refers to nrfx_gpiote.o(.bss) for .bss
    nrfx_gpiote.o(i.nrfx_gpiote_out_task_force) refers to nrfx_gpiote.o(.bss) for .bss
    nrfx_gpiote.o(i.nrfx_gpiote_out_task_get) refers to nrfx_gpiote.o(i.channel_port_get) for channel_port_get
    nrfx_gpiote.o(i.nrfx_gpiote_out_task_trigger) refers to nrfx_gpiote.o(i.channel_port_get) for channel_port_get
    nrfx_gpiote.o(i.nrfx_gpiote_out_uninit) refers to nrfx_gpiote.o(i.pin_in_use_by_te) for pin_in_use_by_te
    nrfx_gpiote.o(i.nrfx_gpiote_out_uninit) refers to nrfx_gpiote.o(i.channel_port_get) for channel_port_get
    nrfx_gpiote.o(i.nrfx_gpiote_out_uninit) refers to nrfx_gpiote.o(i.channel_free) for channel_free
    nrfx_gpiote.o(i.nrfx_gpiote_out_uninit) refers to nrfx_gpiote.o(i.pin_configured_check) for pin_configured_check
    nrfx_gpiote.o(i.nrfx_gpiote_out_uninit) refers to nrfx_gpiote.o(i.nrf_gpio_cfg_default) for nrf_gpio_cfg_default
    nrfx_gpiote.o(i.nrfx_gpiote_out_uninit) refers to nrfx_gpiote.o(i.pin_configured_clear) for pin_configured_clear
    nrfx_gpiote.o(i.nrfx_gpiote_out_uninit) refers to nrfx_gpiote.o(.bss) for .bss
    nrfx_gpiote.o(i.nrfx_gpiote_set_task_addr_get) refers to nrfx_gpiote.o(i.nrfx_gpiote_set_task_get) for nrfx_gpiote_set_task_get
    nrfx_gpiote.o(i.nrfx_gpiote_set_task_get) refers to nrfx_gpiote.o(i.channel_port_get) for channel_port_get
    nrfx_gpiote.o(i.nrfx_gpiote_set_task_trigger) refers to nrfx_gpiote.o(i.channel_port_get) for channel_port_get
    nrfx_gpiote.o(i.nrfx_gpiote_uninit) refers to nrfx_gpiote.o(i.nrf_gpio_pin_present_check) for nrf_gpio_pin_present_check
    nrfx_gpiote.o(i.nrfx_gpiote_uninit) refers to nrfx_gpiote.o(i.nrfx_gpiote_in_uninit) for nrfx_gpiote_in_uninit
    nrfx_gpiote.o(i.nrfx_gpiote_uninit) refers to nrfx_gpiote.o(i.nrfx_gpiote_out_uninit) for nrfx_gpiote_out_uninit
    nrfx_gpiote.o(i.nrfx_gpiote_uninit) refers to nrfx_gpiote.o(.bss) for .bss
    nrfx_gpiote.o(i.pin_configured_check) refers to nrfx_gpiote.o(i.nrf_bitmask_bit_is_set) for nrf_bitmask_bit_is_set
    nrfx_gpiote.o(i.pin_configured_check) refers to nrfx_gpiote.o(.bss) for .bss
    nrfx_gpiote.o(i.pin_configured_clear) refers to nrfx_gpiote.o(.bss) for .bss
    nrfx_gpiote.o(i.pin_configured_set) refers to nrfx_gpiote.o(.bss) for .bss
    nrfx_gpiote.o(i.pin_in_use_by_port) refers to nrfx_gpiote.o(.bss) for .bss
    nrfx_gpiote.o(i.pin_in_use_by_te) refers to nrfx_gpiote.o(.bss) for .bss
    nrfx_gpiote.o(i.port_event_handle) refers to nrfx_gpiote.o(i.nrf_bitmask_bit_is_set) for nrf_bitmask_bit_is_set
    nrfx_gpiote.o(i.port_event_handle) refers to nrfx_gpiote.o(i.port_handler_polarity_get) for port_handler_polarity_get
    nrfx_gpiote.o(i.port_event_handle) refers to nrfx_gpiote.o(i.nrf_gpio_cfg_sense_set) for nrf_gpio_cfg_sense_set
    nrfx_gpiote.o(i.port_event_handle) refers to nrfx_gpiote.o(i.channel_port_get) for channel_port_get
    nrfx_gpiote.o(i.port_event_handle) refers to nrfx_gpiote.o(i.nrf_gpio_latches_read_and_clear) for nrf_gpio_latches_read_and_clear
    nrfx_gpiote.o(i.port_event_handle) refers to nrfx_gpiote.o(.bss) for .bss
    nrfx_gpiote.o(i.port_handler_polarity_get) refers to nrfx_gpiote.o(.bss) for .bss
    nrfx_prs.o(i.UARTE0_UART0_IRQHandler) refers to nrfx_prs.o(.data) for .data
    nrfx_prs.o(i.nrfx_prs_acquire) refers to nrfx_prs.o(i.prs_box_get) for prs_box_get
    nrfx_prs.o(i.nrfx_prs_acquire) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    nrfx_prs.o(i.nrfx_prs_acquire) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    nrfx_prs.o(i.nrfx_prs_release) refers to nrfx_prs.o(i.prs_box_get) for prs_box_get
    nrfx_prs.o(i.prs_box_get) refers to nrfx_prs.o(.data) for .data
    nrfx_uart.o(i.apply_config) refers to nrfx_uart.o(i.nrf_gpio_cfg_output) for nrf_gpio_cfg_output
    nrfx_uart.o(i.apply_config) refers to nrfx_uart.o(i.nrf_gpio_cfg_input) for nrf_gpio_cfg_input
    nrfx_uart.o(i.nrf_gpio_cfg_default) refers to nrfx_uart.o(i.nrf_gpio_cfg) for nrf_gpio_cfg
    nrfx_uart.o(i.nrf_gpio_cfg_input) refers to nrfx_uart.o(i.nrf_gpio_cfg) for nrf_gpio_cfg
    nrfx_uart.o(i.nrf_gpio_cfg_output) refers to nrfx_uart.o(i.nrf_gpio_cfg) for nrf_gpio_cfg
    nrfx_uart.o(i.nrfx_uart_0_irq_handler) refers to nrfx_uart.o(i.uart_irq_handler) for uart_irq_handler
    nrfx_uart.o(i.nrfx_uart_0_irq_handler) refers to nrfx_uart.o(.bss) for .bss
    nrfx_uart.o(i.nrfx_uart_errorsrc_get) refers to nrfx_uart.o(i.nrf_uart_event_clear) for nrf_uart_event_clear
    nrfx_uart.o(i.nrfx_uart_init) refers to nrfx_prs.o(i.nrfx_prs_acquire) for nrfx_prs_acquire
    nrfx_uart.o(i.nrfx_uart_init) refers to nrfx_uart.o(i.apply_config) for apply_config
    nrfx_uart.o(i.nrfx_uart_init) refers to nrfx_uart.o(i.nrf_uart_event_clear) for nrf_uart_event_clear
    nrfx_uart.o(i.nrfx_uart_init) refers to nrfx_uart.o(.bss) for .bss
    nrfx_uart.o(i.nrfx_uart_init) refers to nrfx_uart.o(.constdata) for .constdata
    nrfx_uart.o(i.nrfx_uart_rx) refers to nrfx_uart.o(i.rx_enable) for rx_enable
    nrfx_uart.o(i.nrfx_uart_rx) refers to nrfx_uart.o(i.nrf_uart_event_clear) for nrf_uart_event_clear
    nrfx_uart.o(i.nrfx_uart_rx) refers to nrfx_uart.o(i.nrf_uart_event_check) for nrf_uart_event_check
    nrfx_uart.o(i.nrfx_uart_rx) refers to nrfx_uart.o(i.rx_byte) for rx_byte
    nrfx_uart.o(i.nrfx_uart_rx) refers to nrfx_uart.o(.bss) for .bss
    nrfx_uart.o(i.nrfx_uart_rx_disable) refers to nrfx_uart.o(.bss) for .bss
    nrfx_uart.o(i.nrfx_uart_rx_enable) refers to nrfx_uart.o(i.rx_enable) for rx_enable
    nrfx_uart.o(i.nrfx_uart_rx_enable) refers to nrfx_uart.o(.bss) for .bss
    nrfx_uart.o(i.nrfx_uart_tx) refers to nrfx_uart.o(i.nrfx_uart_tx_in_progress) for nrfx_uart_tx_in_progress
    nrfx_uart.o(i.nrfx_uart_tx) refers to nrfx_uart.o(i.nrf_uart_event_clear) for nrf_uart_event_clear
    nrfx_uart.o(i.nrfx_uart_tx) refers to nrfx_uart.o(i.tx_byte) for tx_byte
    nrfx_uart.o(i.nrfx_uart_tx) refers to nrfx_uart.o(i.nrf_uart_event_check) for nrf_uart_event_check
    nrfx_uart.o(i.nrfx_uart_tx) refers to nrfx_uart.o(.bss) for .bss
    nrfx_uart.o(i.nrfx_uart_tx_abort) refers to nrfx_uart.o(i.tx_done_event) for tx_done_event
    nrfx_uart.o(i.nrfx_uart_tx_abort) refers to nrfx_uart.o(.bss) for .bss
    nrfx_uart.o(i.nrfx_uart_tx_in_progress) refers to nrfx_uart.o(.bss) for .bss
    nrfx_uart.o(i.nrfx_uart_uninit) refers to nrfx_uart.o(i.nrf_gpio_cfg_default) for nrf_gpio_cfg_default
    nrfx_uart.o(i.nrfx_uart_uninit) refers to nrfx_prs.o(i.nrfx_prs_release) for nrfx_prs_release
    nrfx_uart.o(i.nrfx_uart_uninit) refers to nrfx_uart.o(.bss) for .bss
    nrfx_uart.o(i.rx_byte) refers to nrfx_uart.o(i.nrf_uart_event_clear) for nrf_uart_event_clear
    nrfx_uart.o(i.rx_enable) refers to nrfx_uart.o(i.nrf_uart_event_clear) for nrf_uart_event_clear
    nrfx_uart.o(i.tx_byte) refers to nrfx_uart.o(i.nrf_uart_event_clear) for nrf_uart_event_clear
    nrfx_uart.o(i.uart_irq_handler) refers to nrfx_uart.o(i.nrf_uart_int_enable_check) for nrf_uart_int_enable_check
    nrfx_uart.o(i.uart_irq_handler) refers to nrfx_uart.o(i.nrf_uart_event_check) for nrf_uart_event_check
    nrfx_uart.o(i.uart_irq_handler) refers to nrfx_uart.o(i.nrf_uart_event_clear) for nrf_uart_event_clear
    nrfx_uart.o(i.uart_irq_handler) refers to nrfx_uart.o(i.rx_byte) for rx_byte
    nrfx_uart.o(i.uart_irq_handler) refers to nrfx_uart.o(i.rx_done_event) for rx_done_event
    nrfx_uart.o(i.uart_irq_handler) refers to nrfx_uart.o(i.tx_done_event) for tx_done_event
    nrfx_uart.o(i.uart_irq_handler) refers to nrfx_uart.o(i.tx_byte) for tx_byte
    nrfx_uart.o(.constdata) refers to nrfx_uart.o(i.nrfx_uart_0_irq_handler) for nrfx_uart_0_irq_handler
    nrfx_uarte.o(i.apply_config) refers to nrfx_uarte.o(i.nrf_gpio_cfg_output) for nrf_gpio_cfg_output
    nrfx_uarte.o(i.apply_config) refers to nrfx_uarte.o(i.nrf_gpio_cfg_input) for nrf_gpio_cfg_input
    nrfx_uarte.o(i.interrupts_enable) refers to nrfx_uarte.o(i.nrf_uarte_event_clear) for nrf_uarte_event_clear
    nrfx_uarte.o(i.nrf_gpio_cfg_default) refers to nrfx_uarte.o(i.nrf_gpio_cfg) for nrf_gpio_cfg
    nrfx_uarte.o(i.nrf_gpio_cfg_input) refers to nrfx_uarte.o(i.nrf_gpio_cfg) for nrf_gpio_cfg
    nrfx_uarte.o(i.nrf_gpio_cfg_output) refers to nrfx_uarte.o(i.nrf_gpio_cfg) for nrf_gpio_cfg
    nrfx_uarte.o(i.nrfx_uarte_0_irq_handler) refers to nrfx_uarte.o(i.uarte_irq_handler) for uarte_irq_handler
    nrfx_uarte.o(i.nrfx_uarte_0_irq_handler) refers to nrfx_uarte.o(.bss) for .bss
    nrfx_uarte.o(i.nrfx_uarte_errorsrc_get) refers to nrfx_uarte.o(i.nrf_uarte_event_clear) for nrf_uarte_event_clear
    nrfx_uarte.o(i.nrfx_uarte_init) refers to nrfx_prs.o(i.nrfx_prs_acquire) for nrfx_prs_acquire
    nrfx_uarte.o(i.nrfx_uarte_init) refers to nrfx_uarte.o(i.apply_config) for apply_config
    nrfx_uarte.o(i.nrfx_uarte_init) refers to nrfx_uarte.o(i.interrupts_enable) for interrupts_enable
    nrfx_uarte.o(i.nrfx_uarte_init) refers to nrfx_uarte.o(.bss) for .bss
    nrfx_uarte.o(i.nrfx_uarte_init) refers to nrfx_uarte.o(.constdata) for .constdata
    nrfx_uarte.o(i.nrfx_uarte_rx) refers to nrfx_uarte.o(i.nrfx_is_in_ram) for nrfx_is_in_ram
    nrfx_uarte.o(i.nrfx_uarte_rx) refers to nrfx_uarte.o(i.nrf_uarte_event_clear) for nrf_uarte_event_clear
    nrfx_uarte.o(i.nrfx_uarte_rx) refers to nrfx_uarte.o(i.nrf_uarte_event_check) for nrf_uarte_event_check
    nrfx_uarte.o(i.nrfx_uarte_rx) refers to nrfx_uarte.o(.bss) for .bss
    nrfx_uarte.o(i.nrfx_uarte_rx_abort) refers to nrfx_uarte.o(.bss) for .bss
    nrfx_uarte.o(i.nrfx_uarte_tx) refers to nrfx_uarte.o(i.nrfx_is_in_ram) for nrfx_is_in_ram
    nrfx_uarte.o(i.nrfx_uarte_tx) refers to nrfx_uarte.o(i.nrfx_uarte_tx_in_progress) for nrfx_uarte_tx_in_progress
    nrfx_uarte.o(i.nrfx_uarte_tx) refers to nrfx_uarte.o(i.nrf_uarte_event_clear) for nrf_uarte_event_clear
    nrfx_uarte.o(i.nrfx_uarte_tx) refers to nrfx_uarte.o(i.nrf_uarte_event_check) for nrf_uarte_event_check
    nrfx_uarte.o(i.nrfx_uarte_tx) refers to nrfx_uarte.o(.bss) for .bss
    nrfx_uarte.o(i.nrfx_uarte_tx_abort) refers to nrfx_uarte.o(i.nrf_uarte_event_clear) for nrf_uarte_event_clear
    nrfx_uarte.o(i.nrfx_uarte_tx_abort) refers to nrfx_uarte.o(i.nrf_uarte_event_check) for nrf_uarte_event_check
    nrfx_uarte.o(i.nrfx_uarte_tx_abort) refers to nrfx_uarte.o(.bss) for .bss
    nrfx_uarte.o(i.nrfx_uarte_tx_in_progress) refers to nrfx_uarte.o(.bss) for .bss
    nrfx_uarte.o(i.nrfx_uarte_uninit) refers to nrfx_uarte.o(i.nrf_uarte_event_clear) for nrf_uarte_event_clear
    nrfx_uarte.o(i.nrfx_uarte_uninit) refers to nrfx_uarte.o(i.nrf_uarte_event_check) for nrf_uarte_event_check
    nrfx_uarte.o(i.nrfx_uarte_uninit) refers to nrfx_uarte.o(i.nrf_gpio_cfg_default) for nrf_gpio_cfg_default
    nrfx_uarte.o(i.nrfx_uarte_uninit) refers to nrfx_prs.o(i.nrfx_prs_release) for nrfx_prs_release
    nrfx_uarte.o(i.nrfx_uarte_uninit) refers to nrfx_uarte.o(.bss) for .bss
    nrfx_uarte.o(i.uarte_irq_handler) refers to nrfx_uarte.o(i.nrf_uarte_event_check) for nrf_uarte_event_check
    nrfx_uarte.o(i.uarte_irq_handler) refers to nrfx_uarte.o(i.nrf_uarte_event_clear) for nrf_uarte_event_clear
    nrfx_uarte.o(i.uarte_irq_handler) refers to nrfx_uarte.o(i.rx_done_event) for rx_done_event
    nrfx_uarte.o(i.uarte_irq_handler) refers to nrfx_uarte.o(i.tx_done_event) for tx_done_event
    nrfx_uarte.o(.constdata) refers to nrfx_uarte.o(i.nrfx_uarte_0_irq_handler) for nrfx_uarte_0_irq_handler
    nrf_drv_ppi.o(i.nrf_drv_ppi_init) refers to nrf_drv_ppi.o(.data) for .data
    nrf_drv_ppi.o(i.nrf_drv_ppi_uninit) refers to nrfx_ppi.o(i.nrfx_ppi_free_all) for nrfx_ppi_free_all
    nrf_drv_ppi.o(i.nrf_drv_ppi_uninit) refers to nrf_drv_ppi.o(.data) for .data
    nrfx_ppi.o(i.is_allocated_channel) refers to nrfx_ppi.o(.data) for .data
    nrfx_ppi.o(i.is_allocated_group) refers to nrfx_ppi.o(.data) for .data
    nrfx_ppi.o(i.is_app_channel) refers to nrfx_ppi.o(i.are_app_channels) for are_app_channels
    nrfx_ppi.o(i.nrfx_ppi_channel_alloc) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    nrfx_ppi.o(i.nrfx_ppi_channel_alloc) refers to nrfx_ppi.o(i.is_allocated_channel) for is_allocated_channel
    nrfx_ppi.o(i.nrfx_ppi_channel_alloc) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    nrfx_ppi.o(i.nrfx_ppi_channel_alloc) refers to nrfx_ppi.o(.data) for .data
    nrfx_ppi.o(i.nrfx_ppi_channel_assign) refers to nrfx_ppi.o(i.is_programmable_app_channel) for is_programmable_app_channel
    nrfx_ppi.o(i.nrfx_ppi_channel_assign) refers to nrfx_ppi.o(i.is_allocated_channel) for is_allocated_channel
    nrfx_ppi.o(i.nrfx_ppi_channel_disable) refers to nrfx_ppi.o(i.is_app_channel) for is_app_channel
    nrfx_ppi.o(i.nrfx_ppi_channel_disable) refers to nrfx_ppi.o(i.is_programmable_app_channel) for is_programmable_app_channel
    nrfx_ppi.o(i.nrfx_ppi_channel_disable) refers to nrfx_ppi.o(i.is_allocated_channel) for is_allocated_channel
    nrfx_ppi.o(i.nrfx_ppi_channel_enable) refers to nrfx_ppi.o(i.is_app_channel) for is_app_channel
    nrfx_ppi.o(i.nrfx_ppi_channel_enable) refers to nrfx_ppi.o(i.is_programmable_app_channel) for is_programmable_app_channel
    nrfx_ppi.o(i.nrfx_ppi_channel_enable) refers to nrfx_ppi.o(i.is_allocated_channel) for is_allocated_channel
    nrfx_ppi.o(i.nrfx_ppi_channel_fork_assign) refers to nrfx_ppi.o(i.is_allocated_channel) for is_allocated_channel
    nrfx_ppi.o(i.nrfx_ppi_channel_free) refers to nrfx_ppi.o(i.is_programmable_app_channel) for is_programmable_app_channel
    nrfx_ppi.o(i.nrfx_ppi_channel_free) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    nrfx_ppi.o(i.nrfx_ppi_channel_free) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    nrfx_ppi.o(i.nrfx_ppi_channel_free) refers to nrfx_ppi.o(.data) for .data
    nrfx_ppi.o(i.nrfx_ppi_channels_include_in_group) refers to nrfx_ppi.o(i.is_app_group) for is_app_group
    nrfx_ppi.o(i.nrfx_ppi_channels_include_in_group) refers to nrfx_ppi.o(i.is_allocated_group) for is_allocated_group
    nrfx_ppi.o(i.nrfx_ppi_channels_include_in_group) refers to nrfx_ppi.o(i.are_app_channels) for are_app_channels
    nrfx_ppi.o(i.nrfx_ppi_channels_include_in_group) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    nrfx_ppi.o(i.nrfx_ppi_channels_include_in_group) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    nrfx_ppi.o(i.nrfx_ppi_channels_remove_from_group) refers to nrfx_ppi.o(i.is_app_group) for is_app_group
    nrfx_ppi.o(i.nrfx_ppi_channels_remove_from_group) refers to nrfx_ppi.o(i.is_allocated_group) for is_allocated_group
    nrfx_ppi.o(i.nrfx_ppi_channels_remove_from_group) refers to nrfx_ppi.o(i.are_app_channels) for are_app_channels
    nrfx_ppi.o(i.nrfx_ppi_channels_remove_from_group) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    nrfx_ppi.o(i.nrfx_ppi_channels_remove_from_group) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    nrfx_ppi.o(i.nrfx_ppi_free_all) refers to nrfx_ppi.o(.data) for .data
    nrfx_ppi.o(i.nrfx_ppi_group_alloc) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    nrfx_ppi.o(i.nrfx_ppi_group_alloc) refers to nrfx_ppi.o(i.is_allocated_group) for is_allocated_group
    nrfx_ppi.o(i.nrfx_ppi_group_alloc) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    nrfx_ppi.o(i.nrfx_ppi_group_alloc) refers to nrfx_ppi.o(.data) for .data
    nrfx_ppi.o(i.nrfx_ppi_group_disable) refers to nrfx_ppi.o(i.is_app_group) for is_app_group
    nrfx_ppi.o(i.nrfx_ppi_group_enable) refers to nrfx_ppi.o(i.is_app_group) for is_app_group
    nrfx_ppi.o(i.nrfx_ppi_group_enable) refers to nrfx_ppi.o(i.is_allocated_group) for is_allocated_group
    nrfx_ppi.o(i.nrfx_ppi_group_free) refers to nrfx_ppi.o(i.is_app_group) for is_app_group
    nrfx_ppi.o(i.nrfx_ppi_group_free) refers to nrfx_ppi.o(i.is_allocated_group) for is_allocated_group
    nrfx_ppi.o(i.nrfx_ppi_group_free) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    nrfx_ppi.o(i.nrfx_ppi_group_free) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    nrfx_ppi.o(i.nrfx_ppi_group_free) refers to nrfx_ppi.o(.data) for .data
    nrfx_saadc.o(i.SAADC_IRQHandler) refers to nrfx_saadc.o(i.nrf_saadc_event_check) for nrf_saadc_event_check
    nrfx_saadc.o(i.SAADC_IRQHandler) refers to nrfx_saadc.o(i.nrf_saadc_event_clear) for nrf_saadc_event_clear
    nrfx_saadc.o(i.SAADC_IRQHandler) refers to nrfx_saadc.o(i.nrf_saadc_buffer_init) for nrf_saadc_buffer_init
    nrfx_saadc.o(i.SAADC_IRQHandler) refers to nrfx_saadc.o(.bss) for .bss
    nrfx_saadc.o(i.nrfx_coredep_delay_us) refers to nrfx_saadc.o(.constdata) for .constdata
    nrfx_saadc.o(i.nrfx_saadc_abort) refers to nrfx_saadc.o(i.nrfx_saadc_is_busy) for nrfx_saadc_is_busy
    nrfx_saadc.o(i.nrfx_saadc_abort) refers to nrfx_saadc.o(i.nrf_saadc_event_clear) for nrf_saadc_event_clear
    nrfx_saadc.o(i.nrfx_saadc_abort) refers to nrfx_saadc.o(i.nrfx_coredep_delay_us) for nrfx_coredep_delay_us
    nrfx_saadc.o(i.nrfx_saadc_abort) refers to nrfx_saadc.o(.bss) for .bss
    nrfx_saadc.o(i.nrfx_saadc_buffer_convert) refers to nrfx_saadc.o(i.nrf_saadc_buffer_init) for nrf_saadc_buffer_init
    nrfx_saadc.o(i.nrfx_saadc_buffer_convert) refers to nrfx_saadc.o(i.nrf_saadc_event_check) for nrf_saadc_event_check
    nrfx_saadc.o(i.nrfx_saadc_buffer_convert) refers to nrfx_saadc.o(i.nrf_saadc_event_clear) for nrf_saadc_event_clear
    nrfx_saadc.o(i.nrfx_saadc_buffer_convert) refers to nrfx_saadc.o(.bss) for .bss
    nrfx_saadc.o(i.nrfx_saadc_calibrate_offset) refers to nrfx_saadc.o(i.nrf_saadc_event_clear) for nrf_saadc_event_clear
    nrfx_saadc.o(i.nrfx_saadc_calibrate_offset) refers to nrfx_saadc.o(.bss) for .bss
    nrfx_saadc.o(i.nrfx_saadc_channel_init) refers to nrfx_saadc.o(i.nrf_saadc_channel_input_set) for nrf_saadc_channel_input_set
    nrfx_saadc.o(i.nrfx_saadc_channel_init) refers to nrfx_saadc.o(.bss) for .bss
    nrfx_saadc.o(i.nrfx_saadc_channel_uninit) refers to nrfx_saadc.o(i.nrf_saadc_channel_input_set) for nrf_saadc_channel_input_set
    nrfx_saadc.o(i.nrfx_saadc_channel_uninit) refers to nrfx_saadc.o(i.nrfx_saadc_limits_set) for nrfx_saadc_limits_set
    nrfx_saadc.o(i.nrfx_saadc_channel_uninit) refers to nrfx_saadc.o(.bss) for .bss
    nrfx_saadc.o(i.nrfx_saadc_init) refers to nrfx_saadc.o(i.nrf_saadc_event_clear) for nrf_saadc_event_clear
    nrfx_saadc.o(i.nrfx_saadc_init) refers to nrfx_saadc.o(.bss) for .bss
    nrfx_saadc.o(i.nrfx_saadc_is_busy) refers to nrfx_saadc.o(.bss) for .bss
    nrfx_saadc.o(i.nrfx_saadc_limits_set) refers to nrfx_saadc.o(i.nrf_saadc_limit_int_get) for nrf_saadc_limit_int_get
    nrfx_saadc.o(i.nrfx_saadc_limits_set) refers to nrfx_saadc.o(.bss) for .bss
    nrfx_saadc.o(i.nrfx_saadc_sample) refers to nrfx_saadc.o(.bss) for .bss
    nrfx_saadc.o(i.nrfx_saadc_sample_convert) refers to nrfx_saadc.o(i.nrf_saadc_buffer_init) for nrf_saadc_buffer_init
    nrfx_saadc.o(i.nrfx_saadc_sample_convert) refers to nrfx_saadc.o(i.nrf_saadc_channel_input_set) for nrf_saadc_channel_input_set
    nrfx_saadc.o(i.nrfx_saadc_sample_convert) refers to nrfx_saadc.o(i.nrf_saadc_event_check) for nrf_saadc_event_check
    nrfx_saadc.o(i.nrfx_saadc_sample_convert) refers to nrfx_saadc.o(i.nrfx_coredep_delay_us) for nrfx_coredep_delay_us
    nrfx_saadc.o(i.nrfx_saadc_sample_convert) refers to nrfx_saadc.o(i.nrf_saadc_event_clear) for nrf_saadc_event_clear
    nrfx_saadc.o(i.nrfx_saadc_sample_convert) refers to nrfx_saadc.o(.bss) for .bss
    nrfx_saadc.o(i.nrfx_saadc_sample_task_get) refers to nrfx_saadc.o(.bss) for .bss
    nrfx_saadc.o(i.nrfx_saadc_uninit) refers to nrfx_saadc.o(i.nrf_saadc_event_check) for nrf_saadc_event_check
    nrfx_saadc.o(i.nrfx_saadc_uninit) refers to nrfx_saadc.o(i.nrfx_coredep_delay_us) for nrfx_coredep_delay_us
    nrfx_saadc.o(i.nrfx_saadc_uninit) refers to nrfx_saadc.o(i.nrfx_saadc_channel_uninit) for nrfx_saadc_channel_uninit
    nrfx_saadc.o(i.nrfx_saadc_uninit) refers to nrfx_saadc.o(.bss) for .bss
    nrfx_timer.o(i.TIMER1_IRQHandler) refers to nrfx_timer.o(i.nrf_timer_event_clear) for nrf_timer_event_clear
    nrfx_timer.o(i.TIMER1_IRQHandler) refers to nrfx_timer.o(.bss) for .bss
    nrfx_timer.o(i.nrfx_timer_compare) refers to nrfx_timer.o(i.nrf_timer_event_clear) for nrf_timer_event_clear
    nrfx_timer.o(i.nrfx_timer_compare_int_enable) refers to nrfx_timer.o(i.nrf_timer_event_clear) for nrf_timer_event_clear
    nrfx_timer.o(i.nrfx_timer_disable) refers to nrfx_timer.o(.bss) for .bss
    nrfx_timer.o(i.nrfx_timer_enable) refers to nrfx_timer.o(.bss) for .bss
    nrfx_timer.o(i.nrfx_timer_extended_compare) refers to nrfx_timer.o(i.nrfx_timer_compare) for nrfx_timer_compare
    nrfx_timer.o(i.nrfx_timer_init) refers to nrfx_timer.o(i.nrf_timer_event_clear) for nrf_timer_event_clear
    nrfx_timer.o(i.nrfx_timer_init) refers to nrfx_timer.o(.bss) for .bss
    nrfx_timer.o(i.nrfx_timer_is_enabled) refers to nrfx_timer.o(.bss) for .bss
    nrfx_timer.o(i.nrfx_timer_uninit) refers to nrfx_timer.o(i.nrfx_timer_disable) for nrfx_timer_disable
    nrfx_timer.o(i.nrfx_timer_uninit) refers to nrfx_timer.o(.bss) for .bss
    app_button.o(i.app_button_disable) refers to nrfx_gpiote.o(i.nrfx_gpiote_in_event_disable) for nrfx_gpiote_in_event_disable
    app_button.o(i.app_button_disable) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    app_button.o(i.app_button_disable) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    app_button.o(i.app_button_disable) refers to app_timer2.o(i.app_timer_stop) for app_timer_stop
    app_button.o(i.app_button_disable) refers to app_button.o(.data) for .data
    app_button.o(i.app_button_disable) refers to app_button.o(.constdata) for .constdata
    app_button.o(i.app_button_enable) refers to nrfx_gpiote.o(i.nrfx_gpiote_in_event_enable) for nrfx_gpiote_in_event_enable
    app_button.o(i.app_button_enable) refers to app_button.o(.data) for .data
    app_button.o(i.app_button_init) refers to nrfx_gpiote.o(i.nrfx_gpiote_is_init) for nrfx_gpiote_is_init
    app_button.o(i.app_button_init) refers to nrfx_gpiote.o(i.nrfx_gpiote_init) for nrfx_gpiote_init
    app_button.o(i.app_button_init) refers to memseta.o(.text) for __aeabi_memclr
    app_button.o(i.app_button_init) refers to nrfx_gpiote.o(i.nrfx_gpiote_in_init) for nrfx_gpiote_in_init
    app_button.o(i.app_button_init) refers to app_timer2.o(i.app_timer_create) for app_timer_create
    app_button.o(i.app_button_init) refers to app_button.o(.data) for .data
    app_button.o(i.app_button_init) refers to app_button.o(.bss) for .bss
    app_button.o(i.app_button_init) refers to app_button.o(.constdata) for .constdata
    app_button.o(i.app_button_init) refers to app_button.o(i.gpiote_event_handler) for gpiote_event_handler
    app_button.o(i.app_button_init) refers to app_button.o(i.detection_delay_timeout_handler) for detection_delay_timeout_handler
    app_button.o(i.app_button_is_pushed) refers to nrfx_gpiote.o(i.nrfx_gpiote_in_is_set) for nrfx_gpiote_in_is_set
    app_button.o(i.app_button_is_pushed) refers to app_button.o(.data) for .data
    app_button.o(i.button_get) refers to app_button.o(.data) for .data
    app_button.o(i.detection_delay_timeout_handler) refers to nrfx_gpiote.o(i.nrfx_gpiote_in_is_set) for nrfx_gpiote_in_is_set
    app_button.o(i.detection_delay_timeout_handler) refers to app_button.o(i.evt_handle) for evt_handle
    app_button.o(i.detection_delay_timeout_handler) refers to app_button.o(i.timer_start) for timer_start
    app_button.o(i.detection_delay_timeout_handler) refers to app_button.o(.data) for .data
    app_button.o(i.evt_handle) refers to llshl.o(.text) for __aeabi_llsl
    app_button.o(i.evt_handle) refers to app_button.o(i.state_set) for state_set
    app_button.o(i.evt_handle) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    app_button.o(i.evt_handle) refers to app_button.o(i.usr_event) for usr_event
    app_button.o(i.evt_handle) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    app_button.o(i.evt_handle) refers to app_button.o(.bss) for .bss
    app_button.o(i.evt_handle) refers to app_button.o(.data) for .data
    app_button.o(i.gpiote_event_handler) refers to app_button.o(i.button_get) for button_get
    app_button.o(i.gpiote_event_handler) refers to nrfx_gpiote.o(i.nrfx_gpiote_in_is_set) for nrfx_gpiote_in_is_set
    app_button.o(i.gpiote_event_handler) refers to app_button.o(i.timer_start) for timer_start
    app_button.o(i.gpiote_event_handler) refers to app_button.o(.data) for .data
    app_button.o(i.state_set) refers to app_button.o(.bss) for .bss
    app_button.o(i.timer_start) refers to app_timer2.o(i.app_timer_start) for app_timer_start
    app_button.o(i.timer_start) refers to app_button.o(.data) for .data
    app_button.o(i.timer_start) refers to app_button.o(.constdata) for .constdata
    app_button.o(i.usr_event) refers to app_button.o(i.button_get) for button_get
    app_button.o(.constdata) refers to app_button.o(.bss) for m_detection_delay_timer_id_data
    app_error.o(i.app_error_handler_bare) refers to app_error_weak.o(i.app_error_fault_handler) for app_error_fault_handler
    app_error.o(i.app_error_save_and_stop) refers to app_error.o(.bss) for .bss
    app_error_handler_keil.o(.emb_text) refers to app_error_weak.o(i.app_error_fault_handler) for app_error_fault_handler
    app_fifo.o(i.app_fifo_get) refers to app_fifo.o(i.fifo_get) for fifo_get
    app_fifo.o(i.app_fifo_put) refers to app_fifo.o(i.fifo_put) for fifo_put
    app_fifo.o(i.app_fifo_read) refers to app_fifo.o(i.fifo_get) for fifo_get
    app_fifo.o(i.app_fifo_write) refers to app_fifo.o(i.fifo_put) for fifo_put
    app_scheduler.o(i.app_sched_event_put) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    app_scheduler.o(i.app_sched_event_put) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    app_scheduler.o(i.app_sched_event_put) refers to memcpya.o(.text) for __aeabi_memcpy
    app_scheduler.o(i.app_sched_event_put) refers to app_scheduler.o(.data) for .data
    app_scheduler.o(i.app_sched_execute) refers to app_scheduler.o(.data) for .data
    app_scheduler.o(i.app_sched_init) refers to app_scheduler.o(.data) for .data
    app_scheduler.o(i.app_sched_queue_space_get) refers to app_scheduler.o(.data) for .data
    app_timer2.o(i.app_timer_cnt_get) refers to drv_rtc.o(i.drv_rtc_counter_get) for drv_rtc_counter_get
    app_timer2.o(i.app_timer_cnt_get) refers to app_timer2.o(.data) for .data
    app_timer2.o(i.app_timer_init) refers to nrf_atfifo.o(i.nrf_atfifo_init) for nrf_atfifo_init
    app_timer2.o(i.app_timer_init) refers to drv_rtc.o(i.drv_rtc_init) for drv_rtc_init
    app_timer2.o(i.app_timer_init) refers to drv_rtc.o(i.drv_rtc_overflow_enable) for drv_rtc_overflow_enable
    app_timer2.o(i.app_timer_init) refers to drv_rtc.o(i.drv_rtc_compare_set) for drv_rtc_compare_set
    app_timer2.o(i.app_timer_init) refers to app_timer2.o(.constdata) for .constdata
    app_timer2.o(i.app_timer_init) refers to app_timer2.o(.bss) for .bss
    app_timer2.o(i.app_timer_init) refers to app_timer2.o(i.rtc_irq) for rtc_irq
    app_timer2.o(i.app_timer_init) refers to app_timer2.o(.data) for .data
    app_timer2.o(i.app_timer_pause) refers to drv_rtc.o(i.drv_rtc_stop) for drv_rtc_stop
    app_timer2.o(i.app_timer_pause) refers to app_timer2.o(.data) for .data
    app_timer2.o(i.app_timer_resume) refers to drv_rtc.o(i.drv_rtc_start) for drv_rtc_start
    app_timer2.o(i.app_timer_resume) refers to app_timer2.o(.data) for .data
    app_timer2.o(i.app_timer_start) refers to app_timer2.o(i.get_now) for get_now
    app_timer2.o(i.app_timer_start) refers to app_timer2.o(i.timer_req_schedule) for timer_req_schedule
    app_timer2.o(i.app_timer_stop) refers to app_timer2.o(i.timer_req_schedule) for timer_req_schedule
    app_timer2.o(i.app_timer_stop_all) refers to app_timer2.o(i.timer_req_schedule) for timer_req_schedule
    app_timer2.o(i.app_timer_stop_all) refers to app_timer2.o(.data) for .data
    app_timer2.o(i.get_now) refers to drv_rtc.o(i.drv_rtc_counter_get) for drv_rtc_counter_get
    app_timer2.o(i.get_now) refers to app_timer2.o(.data) for .data
    app_timer2.o(i.rtc_irq) refers to drv_rtc.o(i.drv_rtc_overflow_pending) for drv_rtc_overflow_pending
    app_timer2.o(i.rtc_irq) refers to drv_rtc.o(i.drv_rtc_compare_pending) for drv_rtc_compare_pending
    app_timer2.o(i.rtc_irq) refers to app_timer2.o(i.timer_expire) for timer_expire
    app_timer2.o(i.rtc_irq) refers to app_timer2.o(i.get_now) for get_now
    app_timer2.o(i.rtc_irq) refers to app_timer2.o(i.timer_req_process) for timer_req_process
    app_timer2.o(i.rtc_irq) refers to app_timer2.o(i.rtc_update) for rtc_update
    app_timer2.o(i.rtc_irq) refers to app_timer2.o(.data) for .data
    app_timer2.o(i.rtc_schedule) refers to app_timer2.o(i.get_now) for get_now
    app_timer2.o(i.rtc_schedule) refers to app_timer2.o(i.app_timer_cnt_get) for app_timer_cnt_get
    app_timer2.o(i.rtc_schedule) refers to drv_rtc.o(i.drv_rtc_windowed_compare_set) for drv_rtc_windowed_compare_set
    app_timer2.o(i.rtc_schedule) refers to drv_rtc.o(i.drv_rtc_compare_disable) for drv_rtc_compare_disable
    app_timer2.o(i.rtc_schedule) refers to app_timer2.o(i.timer_expire) for timer_expire
    app_timer2.o(i.rtc_schedule) refers to app_timer2.o(.data) for .data
    app_timer2.o(i.rtc_update) refers to nrf_sortlist.o(i.nrf_sortlist_peek) for nrf_sortlist_peek
    app_timer2.o(i.rtc_update) refers to nrf_sortlist.o(i.nrf_sortlist_add) for nrf_sortlist_add
    app_timer2.o(i.rtc_update) refers to app_timer2.o(i.sortlist_pop) for sortlist_pop
    app_timer2.o(i.rtc_update) refers to app_timer2.o(i.rtc_schedule) for rtc_schedule
    app_timer2.o(i.rtc_update) refers to drv_rtc.o(i.drv_rtc_stop) for drv_rtc_stop
    app_timer2.o(i.rtc_update) refers to drv_rtc.o(i.drv_rtc_start) for drv_rtc_start
    app_timer2.o(i.rtc_update) refers to app_timer2.o(.data) for .data
    app_timer2.o(i.rtc_update) refers to app_timer2.o(.constdata) for .constdata
    app_timer2.o(i.sortlist_pop) refers to nrf_sortlist.o(i.nrf_sortlist_pop) for nrf_sortlist_pop
    app_timer2.o(i.sortlist_pop) refers to app_timer2.o(.constdata) for .constdata
    app_timer2.o(i.timer_expire) refers to app_timer2.o(i.get_now) for get_now
    app_timer2.o(i.timer_expire) refers to nrf_sortlist.o(i.nrf_sortlist_add) for nrf_sortlist_add
    app_timer2.o(i.timer_expire) refers to app_timer2.o(.data) for .data
    app_timer2.o(i.timer_expire) refers to app_timer2.o(.constdata) for .constdata
    app_timer2.o(i.timer_req_process) refers to nrf_atfifo.o(i.nrf_atfifo_item_get) for nrf_atfifo_item_get
    app_timer2.o(i.timer_req_process) refers to nrf_sortlist.o(i.nrf_sortlist_add) for nrf_sortlist_add
    app_timer2.o(i.timer_req_process) refers to nrf_sortlist.o(i.nrf_sortlist_remove) for nrf_sortlist_remove
    app_timer2.o(i.timer_req_process) refers to app_timer2.o(i.sortlist_pop) for sortlist_pop
    app_timer2.o(i.timer_req_process) refers to nrf_atfifo.o(i.nrf_atfifo_item_free) for nrf_atfifo_item_free
    app_timer2.o(i.timer_req_process) refers to app_timer2.o(.constdata) for .constdata
    app_timer2.o(i.timer_req_process) refers to app_timer2.o(.data) for .data
    app_timer2.o(i.timer_req_schedule) refers to nrf_atfifo.o(i.nrf_atfifo_item_alloc) for nrf_atfifo_item_alloc
    app_timer2.o(i.timer_req_schedule) refers to nrf_atfifo.o(i.nrf_atfifo_item_put) for nrf_atfifo_item_put
    app_timer2.o(i.timer_req_schedule) refers to drv_rtc.o(i.drv_rtc_irq_trigger) for drv_rtc_irq_trigger
    app_timer2.o(i.timer_req_schedule) refers to app_timer2.o(.constdata) for .constdata
    app_timer2.o(i.timer_req_schedule) refers to app_timer2.o(.data) for .data
    app_timer2.o(.constdata) refers to app_timer2.o(.bss) for m_req_fifo_inst
    app_timer2.o(.constdata) refers to app_timer2.o(.data) for m_app_timer_sortlist_sortlist_cb
    app_timer2.o(.constdata) refers to app_timer2.o(i.compare_func) for compare_func
    app_uart_fifo.o(i.app_uart_close) refers to nrfx_uarte.o(i.nrfx_uarte_uninit) for nrfx_uarte_uninit
    app_uart_fifo.o(i.app_uart_close) refers to nrfx_uart.o(i.nrfx_uart_uninit) for nrfx_uart_uninit
    app_uart_fifo.o(i.app_uart_close) refers to app_uart_fifo.o(.data) for .data
    app_uart_fifo.o(i.app_uart_close) refers to nrf_drv_uart.o(.data) for nrf_drv_uart_use_easy_dma
    app_uart_fifo.o(i.app_uart_flush) refers to app_fifo.o(i.app_fifo_flush) for app_fifo_flush
    app_uart_fifo.o(i.app_uart_flush) refers to app_uart_fifo.o(.bss) for .bss
    app_uart_fifo.o(i.app_uart_get) refers to app_fifo.o(i.app_fifo_get) for app_fifo_get
    app_uart_fifo.o(i.app_uart_get) refers to app_uart_fifo.o(i.nrf_drv_uart_rx) for nrf_drv_uart_rx
    app_uart_fifo.o(i.app_uart_get) refers to app_error.o(i.app_error_handler_bare) for app_error_handler_bare
    app_uart_fifo.o(i.app_uart_get) refers to app_uart_fifo.o(.data) for .data
    app_uart_fifo.o(i.app_uart_get) refers to app_uart_fifo.o(.bss) for .bss
    app_uart_fifo.o(i.app_uart_init) refers to app_fifo.o(i.app_fifo_init) for app_fifo_init
    app_uart_fifo.o(i.app_uart_init) refers to memcpya.o(.text) for __aeabi_memcpy4
    app_uart_fifo.o(i.app_uart_init) refers to nrf_drv_uart.o(i.nrf_drv_uart_init) for nrf_drv_uart_init
    app_uart_fifo.o(i.app_uart_init) refers to app_uart_fifo.o(i.nrf_drv_uart_rx) for nrf_drv_uart_rx
    app_uart_fifo.o(i.app_uart_init) refers to app_uart_fifo.o(.data) for .data
    app_uart_fifo.o(i.app_uart_init) refers to app_uart_fifo.o(.bss) for .bss
    app_uart_fifo.o(i.app_uart_init) refers to app_uart_fifo.o(.constdata) for .constdata
    app_uart_fifo.o(i.app_uart_init) refers to app_uart_fifo.o(i.uart_event_handler) for uart_event_handler
    app_uart_fifo.o(i.app_uart_put) refers to app_fifo.o(i.app_fifo_put) for app_fifo_put
    app_uart_fifo.o(i.app_uart_put) refers to nrfx_uarte.o(i.nrfx_uarte_tx_in_progress) for nrfx_uarte_tx_in_progress
    app_uart_fifo.o(i.app_uart_put) refers to nrfx_uart.o(i.nrfx_uart_tx_in_progress) for nrfx_uart_tx_in_progress
    app_uart_fifo.o(i.app_uart_put) refers to app_fifo.o(i.app_fifo_get) for app_fifo_get
    app_uart_fifo.o(i.app_uart_put) refers to app_uart_fifo.o(i.nrf_drv_uart_tx) for nrf_drv_uart_tx
    app_uart_fifo.o(i.app_uart_put) refers to app_uart_fifo.o(.bss) for .bss
    app_uart_fifo.o(i.app_uart_put) refers to app_uart_fifo.o(.data) for .data
    app_uart_fifo.o(i.app_uart_put) refers to nrf_drv_uart.o(.data) for nrf_drv_uart_use_easy_dma
    app_uart_fifo.o(i.nrf_drv_uart_rx) refers to nrfx_uarte.o(i.nrfx_uarte_rx) for nrfx_uarte_rx
    app_uart_fifo.o(i.nrf_drv_uart_rx) refers to nrfx_uart.o(i.nrfx_uart_rx) for nrfx_uart_rx
    app_uart_fifo.o(i.nrf_drv_uart_rx) refers to nrf_drv_uart.o(.data) for nrf_drv_uart_use_easy_dma
    app_uart_fifo.o(i.nrf_drv_uart_tx) refers to nrfx_uarte.o(i.nrfx_uarte_tx) for nrfx_uarte_tx
    app_uart_fifo.o(i.nrf_drv_uart_tx) refers to nrfx_uart.o(i.nrfx_uart_tx) for nrfx_uart_tx
    app_uart_fifo.o(i.nrf_drv_uart_tx) refers to nrf_drv_uart.o(.data) for nrf_drv_uart_use_easy_dma
    app_uart_fifo.o(i.uart_event_handler) refers to app_uart_fifo.o(i.nrf_drv_uart_rx) for nrf_drv_uart_rx
    app_uart_fifo.o(i.uart_event_handler) refers to app_fifo.o(i.app_fifo_put) for app_fifo_put
    app_uart_fifo.o(i.uart_event_handler) refers to app_fifo.o(i.app_fifo_get) for app_fifo_get
    app_uart_fifo.o(i.uart_event_handler) refers to app_uart_fifo.o(i.nrf_drv_uart_tx) for nrf_drv_uart_tx
    app_uart_fifo.o(i.uart_event_handler) refers to app_uart_fifo.o(.data) for .data
    app_uart_fifo.o(i.uart_event_handler) refers to app_uart_fifo.o(.bss) for .bss
    app_util_platform.o(i.app_util_critical_region_enter) refers to app_util_platform.o(.bss) for .bss
    app_util_platform.o(i.app_util_critical_region_exit) refers to app_util_platform.o(.bss) for .bss
    app_util_platform.o(i.app_util_disable_irq) refers to app_util_platform.o(.data) for .data
    app_util_platform.o(i.app_util_enable_irq) refers to app_util_platform.o(.data) for .data
    drv_rtc.o(i.RTC1_IRQHandler) refers to drv_rtc.o(.data) for .data
    drv_rtc.o(i.drv_rtc_compare_enable) refers to drv_rtc.o(i.evt_enable) for evt_enable
    drv_rtc.o(i.drv_rtc_compare_pending) refers to drv_rtc.o(i.evt_pending) for evt_pending
    drv_rtc.o(i.drv_rtc_compare_set) refers to drv_rtc.o(i.nrf_rtc_event_clear) for nrf_rtc_event_clear
    drv_rtc.o(i.drv_rtc_init) refers to drv_rtc.o(.data) for .data
    drv_rtc.o(i.drv_rtc_overflow_enable) refers to drv_rtc.o(i.evt_enable) for evt_enable
    drv_rtc.o(i.drv_rtc_overflow_pending) refers to drv_rtc.o(i.evt_pending) for evt_pending
    drv_rtc.o(i.drv_rtc_tick_enable) refers to drv_rtc.o(i.evt_enable) for evt_enable
    drv_rtc.o(i.drv_rtc_tick_pending) refers to drv_rtc.o(i.evt_pending) for evt_pending
    drv_rtc.o(i.drv_rtc_uninit) refers to drv_rtc.o(.data) for .data
    drv_rtc.o(i.drv_rtc_windowed_compare_set) refers to drv_rtc.o(i.nrf_rtc_event_clear) for nrf_rtc_event_clear
    drv_rtc.o(i.drv_rtc_windowed_compare_set) refers to drv_rtc.o(i.nrfx_coredep_delay_us) for nrfx_coredep_delay_us
    drv_rtc.o(i.drv_rtc_windowed_compare_set) refers to drv_rtc.o(i.evt_enable) for evt_enable
    drv_rtc.o(i.evt_pending) refers to drv_rtc.o(i.nrf_rtc_event_clear) for nrf_rtc_event_clear
    drv_rtc.o(i.nrfx_coredep_delay_us) refers to drv_rtc.o(.constdata) for .constdata
    nrf_assert.o(i.assert_nrf_callback) refers to app_error_weak.o(i.app_error_fault_handler) for app_error_fault_handler
    nrf_atfifo.o(i.nrf_atfifo_alloc_put) refers to nrf_atfifo.o(i.nrf_atfifo_item_alloc) for nrf_atfifo_item_alloc
    nrf_atfifo.o(i.nrf_atfifo_alloc_put) refers to memcpya.o(.text) for __aeabi_memcpy
    nrf_atfifo.o(i.nrf_atfifo_alloc_put) refers to nrf_atfifo.o(i.nrf_atfifo_item_put) for nrf_atfifo_item_put
    nrf_atfifo.o(i.nrf_atfifo_clear) refers to nrf_atfifo.o(.emb_text) for __asm___12_nrf_atfifo_c_51f461e1__nrf_atfifo_space_clear
    nrf_atfifo.o(i.nrf_atfifo_get_free) refers to nrf_atfifo.o(i.nrf_atfifo_item_get) for nrf_atfifo_item_get
    nrf_atfifo.o(i.nrf_atfifo_get_free) refers to memcpya.o(.text) for __aeabi_memcpy
    nrf_atfifo.o(i.nrf_atfifo_get_free) refers to nrf_atfifo.o(i.nrf_atfifo_item_free) for nrf_atfifo_item_free
    nrf_atfifo.o(i.nrf_atfifo_item_alloc) refers to nrf_atfifo.o(.emb_text) for __asm___12_nrf_atfifo_c_51f461e1__nrf_atfifo_wspace_req
    nrf_atfifo.o(i.nrf_atfifo_item_free) refers to nrf_atfifo.o(.emb_text) for __asm___12_nrf_atfifo_c_51f461e1__nrf_atfifo_rspace_close
    nrf_atfifo.o(i.nrf_atfifo_item_get) refers to nrf_atfifo.o(.emb_text) for __asm___12_nrf_atfifo_c_51f461e1__nrf_atfifo_rspace_req
    nrf_atfifo.o(i.nrf_atfifo_item_put) refers to nrf_atfifo.o(.emb_text) for __asm___12_nrf_atfifo_c_51f461e1__nrf_atfifo_wspace_close
    nrf_atflags.o(i.nrf_atflags_clear) refers to nrf_atomic.o(i.nrf_atomic_u32_and) for nrf_atomic_u32_and
    nrf_atflags.o(i.nrf_atflags_fetch_clear) refers to nrf_atomic.o(i.nrf_atomic_u32_fetch_and) for nrf_atomic_u32_fetch_and
    nrf_atflags.o(i.nrf_atflags_fetch_set) refers to nrf_atomic.o(i.nrf_atomic_u32_fetch_or) for nrf_atomic_u32_fetch_or
    nrf_atflags.o(i.nrf_atflags_find_and_clear_flag) refers to nrf_atflags.o(i.nrf_atflags_fetch_clear) for nrf_atflags_fetch_clear
    nrf_atflags.o(i.nrf_atflags_find_and_set_flag) refers to nrf_atflags.o(i.nrf_atflags_fetch_set) for nrf_atflags_fetch_set
    nrf_atflags.o(i.nrf_atflags_set) refers to nrf_atomic.o(i.nrf_atomic_u32_or) for nrf_atomic_u32_or
    nrf_atomic.o(i.nrf_atomic_flag_clear) refers to nrf_atomic.o(i.nrf_atomic_u32_and) for nrf_atomic_u32_and
    nrf_atomic.o(i.nrf_atomic_flag_clear_fetch) refers to nrf_atomic.o(i.nrf_atomic_u32_fetch_and) for nrf_atomic_u32_fetch_and
    nrf_atomic.o(i.nrf_atomic_flag_set) refers to nrf_atomic.o(i.nrf_atomic_u32_or) for nrf_atomic_u32_or
    nrf_atomic.o(i.nrf_atomic_flag_set_fetch) refers to nrf_atomic.o(i.nrf_atomic_u32_fetch_or) for nrf_atomic_u32_fetch_or
    nrf_atomic.o(i.nrf_atomic_u32_add) refers to nrf_atomic.o(.emb_text) for __asm___12_nrf_atomic_c_85ca2469__nrf_atomic_internal_add
    nrf_atomic.o(i.nrf_atomic_u32_and) refers to nrf_atomic.o(.emb_text) for __asm___12_nrf_atomic_c_85ca2469__nrf_atomic_internal_and
    nrf_atomic.o(i.nrf_atomic_u32_cmp_exch) refers to nrf_atomic.o(.emb_text) for __asm___12_nrf_atomic_c_85ca2469__nrf_atomic_internal_cmp_exch
    nrf_atomic.o(i.nrf_atomic_u32_fetch_add) refers to nrf_atomic.o(.emb_text) for __asm___12_nrf_atomic_c_85ca2469__nrf_atomic_internal_add
    nrf_atomic.o(i.nrf_atomic_u32_fetch_and) refers to nrf_atomic.o(.emb_text) for __asm___12_nrf_atomic_c_85ca2469__nrf_atomic_internal_and
    nrf_atomic.o(i.nrf_atomic_u32_fetch_or) refers to nrf_atomic.o(.emb_text) for __asm___12_nrf_atomic_c_85ca2469__nrf_atomic_internal_orr
    nrf_atomic.o(i.nrf_atomic_u32_fetch_store) refers to nrf_atomic.o(.emb_text) for __asm___12_nrf_atomic_c_85ca2469__nrf_atomic_internal_mov
    nrf_atomic.o(i.nrf_atomic_u32_fetch_sub) refers to nrf_atomic.o(.emb_text) for __asm___12_nrf_atomic_c_85ca2469__nrf_atomic_internal_sub
    nrf_atomic.o(i.nrf_atomic_u32_fetch_sub_hs) refers to nrf_atomic.o(.emb_text) for __asm___12_nrf_atomic_c_85ca2469__nrf_atomic_internal_sub_hs
    nrf_atomic.o(i.nrf_atomic_u32_fetch_xor) refers to nrf_atomic.o(.emb_text) for __asm___12_nrf_atomic_c_85ca2469__nrf_atomic_internal_eor
    nrf_atomic.o(i.nrf_atomic_u32_or) refers to nrf_atomic.o(.emb_text) for __asm___12_nrf_atomic_c_85ca2469__nrf_atomic_internal_orr
    nrf_atomic.o(i.nrf_atomic_u32_store) refers to nrf_atomic.o(.emb_text) for __asm___12_nrf_atomic_c_85ca2469__nrf_atomic_internal_mov
    nrf_atomic.o(i.nrf_atomic_u32_sub) refers to nrf_atomic.o(.emb_text) for __asm___12_nrf_atomic_c_85ca2469__nrf_atomic_internal_sub
    nrf_atomic.o(i.nrf_atomic_u32_sub_hs) refers to nrf_atomic.o(.emb_text) for __asm___12_nrf_atomic_c_85ca2469__nrf_atomic_internal_sub_hs
    nrf_atomic.o(i.nrf_atomic_u32_xor) refers to nrf_atomic.o(.emb_text) for __asm___12_nrf_atomic_c_85ca2469__nrf_atomic_internal_eor
    nrf_balloc.o(i.nrf_balloc_alloc) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    nrf_balloc.o(i.nrf_balloc_alloc) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    nrf_balloc.o(i.nrf_balloc_free) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    nrf_balloc.o(i.nrf_balloc_free) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    nrf_fprintf.o(i.nrf_fprintf) refers to nrf_fprintf_format.o(i.nrf_fprintf_fmt) for nrf_fprintf_fmt
    nrf_fprintf_format.o(i.buffer_add) refers to nrf_fprintf.o(i.nrf_fprintf_buffer_flush) for nrf_fprintf_buffer_flush
    nrf_fprintf_format.o(i.int_print) refers to nrf_fprintf_format.o(i.buffer_add) for buffer_add
    nrf_fprintf_format.o(i.int_print) refers to nrf_fprintf_format.o(i.unsigned_print) for unsigned_print
    nrf_fprintf_format.o(i.nrf_fprintf_fmt) refers to nrf_fprintf_format.o(i.buffer_add) for buffer_add
    nrf_fprintf_format.o(i.nrf_fprintf_fmt) refers to nrf_fprintf.o(i.nrf_fprintf_buffer_flush) for nrf_fprintf_buffer_flush
    nrf_fprintf_format.o(i.nrf_fprintf_fmt) refers to nrf_fprintf_format.o(i.int_print) for int_print
    nrf_fprintf_format.o(i.nrf_fprintf_fmt) refers to nrf_fprintf_format.o(i.unsigned_print) for unsigned_print
    nrf_fprintf_format.o(i.nrf_fprintf_fmt) refers to strlen.o(.text) for strlen
    nrf_fprintf_format.o(i.unsigned_print) refers to nrf_fprintf_format.o(i.buffer_add) for buffer_add
    nrf_fprintf_format.o(i.unsigned_print) refers to nrf_fprintf_format.o(.constdata) for .constdata
    nrf_memobj.o(i.memobj_op) refers to memcpya.o(.text) for __aeabi_memcpy
    nrf_memobj.o(i.nrf_memobj_alloc) refers to nrf_balloc.o(i.nrf_balloc_alloc) for nrf_balloc_alloc
    nrf_memobj.o(i.nrf_memobj_alloc) refers to nrf_memobj.o(i.nrf_memobj_free) for nrf_memobj_free
    nrf_memobj.o(i.nrf_memobj_free) refers to nrf_balloc.o(i.nrf_balloc_free) for nrf_balloc_free
    nrf_memobj.o(i.nrf_memobj_get) refers to nrf_atomic.o(i.nrf_atomic_u32_add) for nrf_atomic_u32_add
    nrf_memobj.o(i.nrf_memobj_pool_init) refers to nrf_balloc.o(i.nrf_balloc_init) for nrf_balloc_init
    nrf_memobj.o(i.nrf_memobj_put) refers to nrf_atomic.o(i.nrf_atomic_u32_sub) for nrf_atomic_u32_sub
    nrf_memobj.o(i.nrf_memobj_put) refers to nrf_memobj.o(i.nrf_memobj_free) for nrf_memobj_free
    nrf_memobj.o(i.nrf_memobj_read) refers to nrf_memobj.o(i.memobj_op) for memobj_op
    nrf_memobj.o(i.nrf_memobj_write) refers to nrf_memobj.o(i.memobj_op) for memobj_op
    nrf_pwr_mgmt.o(i.nrf_pwr_mgmt_init) refers to nrf_section_iter.o(i.nrf_section_iter_init) for nrf_section_iter_init
    nrf_pwr_mgmt.o(i.nrf_pwr_mgmt_init) refers to nrf_pwr_mgmt.o(.data) for .data
    nrf_pwr_mgmt.o(i.nrf_pwr_mgmt_init) refers to nrf_pwr_mgmt.o(.constdata) for .constdata
    nrf_pwr_mgmt.o(i.nrf_pwr_mgmt_init) refers to nrf_pwr_mgmt.o(.bss) for .bss
    nrf_pwr_mgmt.o(i.nrf_pwr_mgmt_run) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    nrf_pwr_mgmt.o(i.nrf_pwr_mgmt_run) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    nrf_pwr_mgmt.o(i.nrf_pwr_mgmt_run) refers to nrf_sdh.o(i.nrf_sdh_is_enabled) for nrf_sdh_is_enabled
    nrf_pwr_mgmt.o(i.nrf_pwr_mgmt_shutdown) refers to nrf_atomic.o(i.nrf_atomic_u32_fetch_store) for nrf_atomic_u32_fetch_store
    nrf_pwr_mgmt.o(i.nrf_pwr_mgmt_shutdown) refers to nrf_pwr_mgmt.o(i.shutdown_process) for shutdown_process
    nrf_pwr_mgmt.o(i.nrf_pwr_mgmt_shutdown) refers to nrf_pwr_mgmt.o(.data) for .data
    nrf_pwr_mgmt.o(i.shutdown_process) refers to nrf_section_iter.o(i.nrf_section_iter_next) for nrf_section_iter_next
    nrf_pwr_mgmt.o(i.shutdown_process) refers to nrf_sdh.o(i.nrf_sdh_is_enabled) for nrf_sdh_is_enabled
    nrf_pwr_mgmt.o(i.shutdown_process) refers to nrf_pwr_mgmt.o(.bss) for .bss
    nrf_pwr_mgmt.o(i.shutdown_process) refers to nrf_pwr_mgmt.o(.data) for .data
    nrf_pwr_mgmt.o(.constdata) refers to nrf_pwr_mgmt.o(.constdata) for pwr_mgmt_data_array
    nrf_ringbuf.o(i.nrf_ringbuf_alloc) refers to nrf_atomic.o(i.nrf_atomic_flag_set_fetch) for nrf_atomic_flag_set_fetch
    nrf_ringbuf.o(i.nrf_ringbuf_alloc) refers to nrf_atomic.o(i.nrf_atomic_flag_clear) for nrf_atomic_flag_clear
    nrf_ringbuf.o(i.nrf_ringbuf_cpy_get) refers to nrf_atomic.o(i.nrf_atomic_flag_set_fetch) for nrf_atomic_flag_set_fetch
    nrf_ringbuf.o(i.nrf_ringbuf_cpy_get) refers to memcpya.o(.text) for __aeabi_memcpy
    nrf_ringbuf.o(i.nrf_ringbuf_cpy_get) refers to nrf_atomic.o(i.nrf_atomic_flag_clear) for nrf_atomic_flag_clear
    nrf_ringbuf.o(i.nrf_ringbuf_cpy_put) refers to nrf_atomic.o(i.nrf_atomic_flag_set_fetch) for nrf_atomic_flag_set_fetch
    nrf_ringbuf.o(i.nrf_ringbuf_cpy_put) refers to memcpya.o(.text) for __aeabi_memcpy
    nrf_ringbuf.o(i.nrf_ringbuf_cpy_put) refers to nrf_atomic.o(i.nrf_atomic_flag_clear) for nrf_atomic_flag_clear
    nrf_ringbuf.o(i.nrf_ringbuf_free) refers to nrf_atomic.o(i.nrf_atomic_flag_clear) for nrf_atomic_flag_clear
    nrf_ringbuf.o(i.nrf_ringbuf_get) refers to nrf_atomic.o(i.nrf_atomic_flag_set_fetch) for nrf_atomic_flag_set_fetch
    nrf_ringbuf.o(i.nrf_ringbuf_get) refers to nrf_atomic.o(i.nrf_atomic_flag_clear) for nrf_atomic_flag_clear
    nrf_ringbuf.o(i.nrf_ringbuf_put) refers to nrf_atomic.o(i.nrf_atomic_flag_clear_fetch) for nrf_atomic_flag_clear_fetch
    nrf_section_iter.o(i.nrf_section_iter_init) refers to nrf_section_iter.o(i.nrf_section_iter_item_set) for nrf_section_iter_item_set
    nrf_section_iter.o(i.nrf_section_iter_next) refers to nrf_section_iter.o(i.nrf_section_iter_item_set) for nrf_section_iter_item_set
    nrf_strerror.o(i.nrf_strerror_find) refers to nrf_strerror.o(.constdata) for .constdata
    nrf_strerror.o(i.nrf_strerror_get) refers to nrf_strerror.o(i.nrf_strerror_find) for nrf_strerror_find
    nrf_strerror.o(i.nrf_strerror_get) refers to nrf_strerror.o(.constdata) for .constdata
    nrf_strerror.o(.constdata) refers to nrf_strerror.o(.conststring) for .conststring
    retarget.o(i.fgetc) refers to app_uart_fifo.o(i.app_uart_get) for app_uart_get
    retarget.o(i.fputc) refers to app_uart_fifo.o(i.app_uart_put) for app_uart_put
    segger_rtt.o(i.SEGGER_RTT_AllocDownBuffer) refers to segger_rtt.o(i._DoInit) for _DoInit
    segger_rtt.o(i.SEGGER_RTT_AllocDownBuffer) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    segger_rtt.o(i.SEGGER_RTT_AllocDownBuffer) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    segger_rtt.o(i.SEGGER_RTT_AllocDownBuffer) refers to segger_rtt.o(.bss) for .bss
    segger_rtt.o(i.SEGGER_RTT_AllocUpBuffer) refers to segger_rtt.o(i._DoInit) for _DoInit
    segger_rtt.o(i.SEGGER_RTT_AllocUpBuffer) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    segger_rtt.o(i.SEGGER_RTT_AllocUpBuffer) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    segger_rtt.o(i.SEGGER_RTT_AllocUpBuffer) refers to segger_rtt.o(.bss) for .bss
    segger_rtt.o(i.SEGGER_RTT_ConfigDownBuffer) refers to segger_rtt.o(i._DoInit) for _DoInit
    segger_rtt.o(i.SEGGER_RTT_ConfigDownBuffer) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    segger_rtt.o(i.SEGGER_RTT_ConfigDownBuffer) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    segger_rtt.o(i.SEGGER_RTT_ConfigDownBuffer) refers to segger_rtt.o(.bss) for .bss
    segger_rtt.o(i.SEGGER_RTT_ConfigUpBuffer) refers to segger_rtt.o(i._DoInit) for _DoInit
    segger_rtt.o(i.SEGGER_RTT_ConfigUpBuffer) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    segger_rtt.o(i.SEGGER_RTT_ConfigUpBuffer) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    segger_rtt.o(i.SEGGER_RTT_ConfigUpBuffer) refers to segger_rtt.o(.bss) for .bss
    segger_rtt.o(i.SEGGER_RTT_GetKey) refers to segger_rtt.o(i.SEGGER_RTT_Read) for SEGGER_RTT_Read
    segger_rtt.o(i.SEGGER_RTT_HasData) refers to segger_rtt.o(.bss) for .bss
    segger_rtt.o(i.SEGGER_RTT_HasKey) refers to segger_rtt.o(i._DoInit) for _DoInit
    segger_rtt.o(i.SEGGER_RTT_HasKey) refers to segger_rtt.o(.bss) for .bss
    segger_rtt.o(i.SEGGER_RTT_Init) refers to segger_rtt.o(i._DoInit) for _DoInit
    segger_rtt.o(i.SEGGER_RTT_PutChar) refers to segger_rtt.o(i._DoInit) for _DoInit
    segger_rtt.o(i.SEGGER_RTT_PutChar) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    segger_rtt.o(i.SEGGER_RTT_PutChar) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    segger_rtt.o(i.SEGGER_RTT_PutChar) refers to segger_rtt.o(.bss) for .bss
    segger_rtt.o(i.SEGGER_RTT_PutCharSkip) refers to segger_rtt.o(i._DoInit) for _DoInit
    segger_rtt.o(i.SEGGER_RTT_PutCharSkip) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    segger_rtt.o(i.SEGGER_RTT_PutCharSkip) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    segger_rtt.o(i.SEGGER_RTT_PutCharSkip) refers to segger_rtt.o(.bss) for .bss
    segger_rtt.o(i.SEGGER_RTT_PutCharSkipNoLock) refers to segger_rtt.o(.bss) for .bss
    segger_rtt.o(i.SEGGER_RTT_Read) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    segger_rtt.o(i.SEGGER_RTT_Read) refers to segger_rtt.o(i.SEGGER_RTT_ReadNoLock) for SEGGER_RTT_ReadNoLock
    segger_rtt.o(i.SEGGER_RTT_Read) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    segger_rtt.o(i.SEGGER_RTT_ReadNoLock) refers to segger_rtt.o(i._DoInit) for _DoInit
    segger_rtt.o(i.SEGGER_RTT_ReadNoLock) refers to memcpya.o(.text) for __aeabi_memcpy
    segger_rtt.o(i.SEGGER_RTT_ReadNoLock) refers to segger_rtt.o(.bss) for .bss
    segger_rtt.o(i.SEGGER_RTT_SetFlagsDownBuffer) refers to segger_rtt.o(i._DoInit) for _DoInit
    segger_rtt.o(i.SEGGER_RTT_SetFlagsDownBuffer) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    segger_rtt.o(i.SEGGER_RTT_SetFlagsDownBuffer) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    segger_rtt.o(i.SEGGER_RTT_SetFlagsDownBuffer) refers to segger_rtt.o(.bss) for .bss
    segger_rtt.o(i.SEGGER_RTT_SetFlagsUpBuffer) refers to segger_rtt.o(i._DoInit) for _DoInit
    segger_rtt.o(i.SEGGER_RTT_SetFlagsUpBuffer) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    segger_rtt.o(i.SEGGER_RTT_SetFlagsUpBuffer) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    segger_rtt.o(i.SEGGER_RTT_SetFlagsUpBuffer) refers to segger_rtt.o(.bss) for .bss
    segger_rtt.o(i.SEGGER_RTT_SetNameDownBuffer) refers to segger_rtt.o(i._DoInit) for _DoInit
    segger_rtt.o(i.SEGGER_RTT_SetNameDownBuffer) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    segger_rtt.o(i.SEGGER_RTT_SetNameDownBuffer) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    segger_rtt.o(i.SEGGER_RTT_SetNameDownBuffer) refers to segger_rtt.o(.bss) for .bss
    segger_rtt.o(i.SEGGER_RTT_SetNameUpBuffer) refers to segger_rtt.o(i._DoInit) for _DoInit
    segger_rtt.o(i.SEGGER_RTT_SetNameUpBuffer) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    segger_rtt.o(i.SEGGER_RTT_SetNameUpBuffer) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    segger_rtt.o(i.SEGGER_RTT_SetNameUpBuffer) refers to segger_rtt.o(.bss) for .bss
    segger_rtt.o(i.SEGGER_RTT_SetTerminal) refers to segger_rtt.o(i._DoInit) for _DoInit
    segger_rtt.o(i.SEGGER_RTT_SetTerminal) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    segger_rtt.o(i.SEGGER_RTT_SetTerminal) refers to segger_rtt.o(i._GetAvailWriteSpace) for _GetAvailWriteSpace
    segger_rtt.o(i.SEGGER_RTT_SetTerminal) refers to segger_rtt.o(i._WriteNoCheck) for _WriteNoCheck
    segger_rtt.o(i.SEGGER_RTT_SetTerminal) refers to segger_rtt.o(i._WriteBlocking) for _WriteBlocking
    segger_rtt.o(i.SEGGER_RTT_SetTerminal) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    segger_rtt.o(i.SEGGER_RTT_SetTerminal) refers to segger_rtt.o(.bss) for .bss
    segger_rtt.o(i.SEGGER_RTT_SetTerminal) refers to segger_rtt.o(.data) for .data
    segger_rtt.o(i.SEGGER_RTT_TerminalOut) refers to segger_rtt.o(i._DoInit) for _DoInit
    segger_rtt.o(i.SEGGER_RTT_TerminalOut) refers to strlen.o(.text) for strlen
    segger_rtt.o(i.SEGGER_RTT_TerminalOut) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    segger_rtt.o(i.SEGGER_RTT_TerminalOut) refers to segger_rtt.o(i._GetAvailWriteSpace) for _GetAvailWriteSpace
    segger_rtt.o(i.SEGGER_RTT_TerminalOut) refers to segger_rtt.o(i._PostTerminalSwitch) for _PostTerminalSwitch
    segger_rtt.o(i.SEGGER_RTT_TerminalOut) refers to segger_rtt.o(i._WriteBlocking) for _WriteBlocking
    segger_rtt.o(i.SEGGER_RTT_TerminalOut) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    segger_rtt.o(i.SEGGER_RTT_TerminalOut) refers to segger_rtt.o(.bss) for .bss
    segger_rtt.o(i.SEGGER_RTT_TerminalOut) refers to segger_rtt.o(.data) for .data
    segger_rtt.o(i.SEGGER_RTT_WaitKey) refers to segger_rtt.o(i.SEGGER_RTT_GetKey) for SEGGER_RTT_GetKey
    segger_rtt.o(i.SEGGER_RTT_Write) refers to segger_rtt.o(i._DoInit) for _DoInit
    segger_rtt.o(i.SEGGER_RTT_Write) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    segger_rtt.o(i.SEGGER_RTT_Write) refers to segger_rtt.o(i.SEGGER_RTT_WriteNoLock) for SEGGER_RTT_WriteNoLock
    segger_rtt.o(i.SEGGER_RTT_Write) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    segger_rtt.o(i.SEGGER_RTT_Write) refers to segger_rtt.o(.bss) for .bss
    segger_rtt.o(i.SEGGER_RTT_WriteNoLock) refers to segger_rtt.o(i._GetAvailWriteSpace) for _GetAvailWriteSpace
    segger_rtt.o(i.SEGGER_RTT_WriteNoLock) refers to segger_rtt.o(i._WriteNoCheck) for _WriteNoCheck
    segger_rtt.o(i.SEGGER_RTT_WriteNoLock) refers to segger_rtt.o(i._WriteBlocking) for _WriteBlocking
    segger_rtt.o(i.SEGGER_RTT_WriteNoLock) refers to segger_rtt.o(.bss) for .bss
    segger_rtt.o(i.SEGGER_RTT_WriteSkipNoLock) refers to memcpya.o(.text) for __aeabi_memcpy
    segger_rtt.o(i.SEGGER_RTT_WriteSkipNoLock) refers to segger_rtt.o(.bss) for .bss
    segger_rtt.o(i.SEGGER_RTT_WriteString) refers to strlen.o(.text) for strlen
    segger_rtt.o(i.SEGGER_RTT_WriteString) refers to segger_rtt.o(i.SEGGER_RTT_Write) for SEGGER_RTT_Write
    segger_rtt.o(i.SEGGER_RTT_WriteWithOverwriteNoLock) refers to memcpya.o(.text) for __aeabi_memcpy
    segger_rtt.o(i.SEGGER_RTT_WriteWithOverwriteNoLock) refers to segger_rtt.o(.bss) for .bss
    segger_rtt.o(i._DoInit) refers to strcpy.o(.text) for strcpy
    segger_rtt.o(i._DoInit) refers to segger_rtt.o(.bss) for .bss
    segger_rtt.o(i._PostTerminalSwitch) refers to segger_rtt.o(i._WriteBlocking) for _WriteBlocking
    segger_rtt.o(i._PostTerminalSwitch) refers to segger_rtt.o(.data) for .data
    segger_rtt.o(i._WriteBlocking) refers to memcpya.o(.text) for __aeabi_memcpy
    segger_rtt.o(i._WriteNoCheck) refers to memcpya.o(.text) for __aeabi_memcpy
    segger_rtt_printf.o(i.SEGGER_RTT_printf) refers to segger_rtt_printf.o(i.SEGGER_RTT_vprintf) for SEGGER_RTT_vprintf
    segger_rtt_printf.o(i.SEGGER_RTT_vprintf) refers to segger_rtt_printf.o(i._StoreChar) for _StoreChar
    segger_rtt_printf.o(i.SEGGER_RTT_vprintf) refers to segger_rtt_printf.o(i._PrintInt) for _PrintInt
    segger_rtt_printf.o(i.SEGGER_RTT_vprintf) refers to segger_rtt_printf.o(i._PrintUnsigned) for _PrintUnsigned
    segger_rtt_printf.o(i.SEGGER_RTT_vprintf) refers to segger_rtt.o(i.SEGGER_RTT_Write) for SEGGER_RTT_Write
    segger_rtt_printf.o(i._PrintInt) refers to segger_rtt_printf.o(i._StoreChar) for _StoreChar
    segger_rtt_printf.o(i._PrintInt) refers to segger_rtt_printf.o(i._PrintUnsigned) for _PrintUnsigned
    segger_rtt_printf.o(i._PrintUnsigned) refers to segger_rtt_printf.o(i._StoreChar) for _StoreChar
    segger_rtt_printf.o(i._PrintUnsigned) refers to segger_rtt_printf.o(.constdata) for .constdata
    segger_rtt_printf.o(i._StoreChar) refers to segger_rtt.o(i.SEGGER_RTT_Write) for SEGGER_RTT_Write
    nrf_sdh.o(i.SWI2_EGU2_IRQHandler) refers to nrf_sdh.o(i.nrf_sdh_evts_poll) for nrf_sdh_evts_poll
    nrf_sdh.o(i.nrf_sdh_disable_request) refers to nrf_sdh.o(i.sdh_request_observer_notify) for sdh_request_observer_notify
    nrf_sdh.o(i.nrf_sdh_disable_request) refers to nrf_sdh.o(i.sdh_state_observer_notify) for sdh_state_observer_notify
    nrf_sdh.o(i.nrf_sdh_disable_request) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    nrf_sdh.o(i.nrf_sdh_disable_request) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    nrf_sdh.o(i.nrf_sdh_disable_request) refers to nrf_sdh.o(i.softdevice_evt_irq_disable) for softdevice_evt_irq_disable
    nrf_sdh.o(i.nrf_sdh_disable_request) refers to nrf_sdh.o(.data) for .data
    nrf_sdh.o(i.nrf_sdh_enable_request) refers to nrf_sdh.o(i.sdh_request_observer_notify) for sdh_request_observer_notify
    nrf_sdh.o(i.nrf_sdh_enable_request) refers to nrf_sdh.o(i.sdh_state_observer_notify) for sdh_state_observer_notify
    nrf_sdh.o(i.nrf_sdh_enable_request) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    nrf_sdh.o(i.nrf_sdh_enable_request) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    nrf_sdh.o(i.nrf_sdh_enable_request) refers to nrf_sdh.o(i.softdevices_evt_irq_enable) for softdevices_evt_irq_enable
    nrf_sdh.o(i.nrf_sdh_enable_request) refers to nrf_sdh.o(.data) for .data
    nrf_sdh.o(i.nrf_sdh_enable_request) refers to nrf_sdh.o(.constdata) for .constdata
    nrf_sdh.o(i.nrf_sdh_enable_request) refers to app_error_weak.o(i.app_error_fault_handler) for app_error_fault_handler
    nrf_sdh.o(i.nrf_sdh_evts_poll) refers to nrf_section_iter.o(i.nrf_section_iter_init) for nrf_section_iter_init
    nrf_sdh.o(i.nrf_sdh_evts_poll) refers to nrf_section_iter.o(i.nrf_section_iter_next) for nrf_section_iter_next
    nrf_sdh.o(i.nrf_sdh_evts_poll) refers to nrf_sdh.o(.constdata) for .constdata
    nrf_sdh.o(i.nrf_sdh_is_enabled) refers to nrf_sdh.o(.data) for .data
    nrf_sdh.o(i.nrf_sdh_is_suspended) refers to nrf_sdh.o(.data) for .data
    nrf_sdh.o(i.nrf_sdh_request_continue) refers to nrf_sdh.o(i.nrf_sdh_disable_request) for nrf_sdh_disable_request
    nrf_sdh.o(i.nrf_sdh_request_continue) refers to nrf_sdh.o(i.nrf_sdh_enable_request) for nrf_sdh_enable_request
    nrf_sdh.o(i.nrf_sdh_request_continue) refers to nrf_sdh.o(.data) for .data
    nrf_sdh.o(i.nrf_sdh_resume) refers to nrf_sdh.o(i.__sd_nvic_app_accessible_irq) for __sd_nvic_app_accessible_irq
    nrf_sdh.o(i.nrf_sdh_resume) refers to app_error.o(i.app_error_handler_bare) for app_error_handler_bare
    nrf_sdh.o(i.nrf_sdh_resume) refers to nrf_sdh.o(i.softdevices_evt_irq_enable) for softdevices_evt_irq_enable
    nrf_sdh.o(i.nrf_sdh_resume) refers to nrf_sdh.o(.data) for .data
    nrf_sdh.o(i.nrf_sdh_suspend) refers to nrf_sdh.o(i.softdevice_evt_irq_disable) for softdevice_evt_irq_disable
    nrf_sdh.o(i.nrf_sdh_suspend) refers to nrf_sdh.o(.data) for .data
    nrf_sdh.o(i.sdh_request_observer_notify) refers to nrf_section_iter.o(i.nrf_section_iter_init) for nrf_section_iter_init
    nrf_sdh.o(i.sdh_request_observer_notify) refers to nrf_section_iter.o(i.nrf_section_iter_next) for nrf_section_iter_next
    nrf_sdh.o(i.sdh_request_observer_notify) refers to nrf_sdh.o(.constdata) for .constdata
    nrf_sdh.o(i.sdh_state_observer_notify) refers to nrf_section_iter.o(i.nrf_section_iter_init) for nrf_section_iter_init
    nrf_sdh.o(i.sdh_state_observer_notify) refers to nrf_section_iter.o(i.nrf_section_iter_next) for nrf_section_iter_next
    nrf_sdh.o(i.sdh_state_observer_notify) refers to nrf_sdh.o(.constdata) for .constdata
    nrf_sdh.o(i.softdevice_evt_irq_disable) refers to nrf_sdh.o(i.__sd_nvic_app_accessible_irq) for __sd_nvic_app_accessible_irq
    nrf_sdh.o(i.softdevice_evt_irq_disable) refers to app_error.o(i.app_error_handler_bare) for app_error_handler_bare
    nrf_sdh.o(i.softdevice_evt_irq_disable) refers to app_util_platform.o(.bss) for nrf_nvic_state
    nrf_sdh.o(i.softdevices_evt_irq_enable) refers to nrf_sdh.o(i.__sd_nvic_app_accessible_irq) for __sd_nvic_app_accessible_irq
    nrf_sdh.o(i.softdevices_evt_irq_enable) refers to app_error.o(i.app_error_handler_bare) for app_error_handler_bare
    nrf_sdh.o(i.softdevices_evt_irq_enable) refers to app_util_platform.o(.bss) for nrf_nvic_state
    nrf_sdh.o(.constdata) refers to nrf_sdh.o(.constdata) for sdh_req_observers_array
    nrf_sdh.o(.constdata) refers to nrf_sdh.o(.constdata) for sdh_state_observers_array
    nrf_sdh.o(.constdata) refers to nrf_sdh.o(.constdata) for sdh_stack_observers_array
    nrf_sdh_ble.o(i.nrf_sdh_ble_app_ram_start_get) refers to nrf_sdh_ble.o(.constdata) for .constdata
    nrf_sdh_ble.o(i.nrf_sdh_ble_default_cfg_set) refers to nrf_sdh_ble.o(i.nrf_sdh_ble_app_ram_start_get) for nrf_sdh_ble_app_ram_start_get
    nrf_sdh_ble.o(i.nrf_sdh_ble_enable) refers to nrf_sdh_ble.o(.data) for .data
    nrf_sdh_ble.o(i.nrf_sdh_ble_evts_poll) refers to app_error.o(i.app_error_handler_bare) for app_error_handler_bare
    nrf_sdh_ble.o(i.nrf_sdh_ble_evts_poll) refers to nrf_section_iter.o(i.nrf_section_iter_init) for nrf_section_iter_init
    nrf_sdh_ble.o(i.nrf_sdh_ble_evts_poll) refers to nrf_section_iter.o(i.nrf_section_iter_next) for nrf_section_iter_next
    nrf_sdh_ble.o(i.nrf_sdh_ble_evts_poll) refers to nrf_sdh_ble.o(.data) for .data
    nrf_sdh_ble.o(i.nrf_sdh_ble_evts_poll) refers to nrf_sdh_ble.o(.constdata) for .constdata
    nrf_sdh_ble.o(.constdata) refers to nrf_sdh_ble.o(.constdata) for sdh_ble_observers_array
    nrf_sdh_ble.o(sdh_stack_observers0) refers to nrf_sdh_ble.o(i.nrf_sdh_ble_evts_poll) for nrf_sdh_ble_evts_poll
    nrf_sdh_soc.o(i.nrf_sdh_soc_evts_poll) refers to app_error.o(i.app_error_handler_bare) for app_error_handler_bare
    nrf_sdh_soc.o(i.nrf_sdh_soc_evts_poll) refers to nrf_section_iter.o(i.nrf_section_iter_init) for nrf_section_iter_init
    nrf_sdh_soc.o(i.nrf_sdh_soc_evts_poll) refers to nrf_section_iter.o(i.nrf_section_iter_next) for nrf_section_iter_next
    nrf_sdh_soc.o(i.nrf_sdh_soc_evts_poll) refers to nrf_sdh_soc.o(.constdata) for .constdata
    nrf_sdh_soc.o(.constdata) refers to nrf_sdh_soc.o(.constdata) for sdh_soc_observers_array
    nrf_sdh_soc.o(sdh_stack_observers0) refers to nrf_sdh_soc.o(i.nrf_sdh_soc_evts_poll) for nrf_sdh_soc_evts_poll
    arm_startup_nrf52.o(RESET) refers to arm_startup_nrf52.o(STACK) for __initial_sp
    arm_startup_nrf52.o(RESET) refers to arm_startup_nrf52.o(.text) for Reset_Handler
    arm_startup_nrf52.o(RESET) refers to nrfx_clock.o(i.POWER_CLOCK_IRQHandler) for POWER_CLOCK_IRQHandler
    arm_startup_nrf52.o(RESET) refers to nrfx_prs.o(i.UARTE0_UART0_IRQHandler) for UARTE0_UART0_IRQHandler
    arm_startup_nrf52.o(RESET) refers to nrfx_gpiote.o(i.GPIOTE_IRQHandler) for GPIOTE_IRQHandler
    arm_startup_nrf52.o(RESET) refers to nrfx_saadc.o(i.SAADC_IRQHandler) for SAADC_IRQHandler
    arm_startup_nrf52.o(RESET) refers to nrfx_timer.o(i.TIMER1_IRQHandler) for TIMER1_IRQHandler
    arm_startup_nrf52.o(RESET) refers to drv_rtc.o(i.RTC1_IRQHandler) for RTC1_IRQHandler
    arm_startup_nrf52.o(RESET) refers to nrf_sdh.o(i.SWI2_EGU2_IRQHandler) for SWI2_EGU2_IRQHandler
    arm_startup_nrf52.o(.text) refers to system_nrf52.o(i.SystemInit) for SystemInit
    arm_startup_nrf52.o(.text) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    system_nrf52.o(i.SystemCoreClockUpdate) refers to system_nrf52.o(.data) for .data
    system_nrf52.o(i.SystemInit) refers to system_nrf52.o(.data) for .data
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry10a.o(.ARM.Collect$$$$0000000D) for __rt_final_cpp
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry11a.o(.ARM.Collect$$$$0000000F) for __rt_final_exit
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry7b.o(.ARM.Collect$$$$00000008) for _main_clock
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry8b.o(.ARM.Collect$$$$0000000A) for _main_cpp_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry9a.o(.ARM.Collect$$$$0000000B) for _main_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry5.o(.ARM.Collect$$$$00000004) for _main_scatterload
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry2.o(.ARM.Collect$$$$00000001) for _main_stk
    uldiv.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    uldiv.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    entry2.o(.ARM.Collect$$$$00000001) refers to entry2.o(.ARM.Collect$$$$00002712) for __lit__00000000
    entry2.o(.ARM.Collect$$$$00002712) refers to arm_startup_nrf52.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to arm_startup_nrf52.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry5.o(.ARM.Collect$$$$00000004) refers to init.o(.text) for __scatterload
    entry9a.o(.ARM.Collect$$$$0000000B) refers to main.o(i.main) for main
    entry9b.o(.ARM.Collect$$$$0000000C) refers to main.o(i.main) for main
    init.o(.text) refers to entry5.o(.ARM.Collect$$$$00000004) for __main_after_scatterload


==============================================================================

Removing Unused input sections from the image.

    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing main.o(.rrx_text), (6 bytes).
    Removing main.o(i.assert_nrf_callback), (16 bytes).
    Removing main.o(i.bsp_event_handler), (76 bytes).
    Removing main.o(i.data_len_set), (32 bytes).
    Removing boards.o(.rev16_text), (4 bytes).
    Removing boards.o(.revsh_text), (4 bytes).
    Removing boards.o(.rrx_text), (6 bytes).
    Removing boards.o(i.bsp_board_button_state_get), (32 bytes).
    Removing boards.o(i.bsp_board_init), (84 bytes).
    Removing boards.o(i.bsp_board_led_idx_to_pin), (12 bytes).
    Removing boards.o(i.bsp_board_pin_to_led_idx), (36 bytes).
    Removing boards.o(i.nrf_gpio_cfg), (36 bytes).
    Removing bsp.o(.rev16_text), (4 bytes).
    Removing bsp.o(.revsh_text), (4 bytes).
    Removing bsp.o(.rrx_text), (6 bytes).
    Removing bsp.o(i.alert_timer_handler), (6 bytes).
    Removing bsp.o(i.bsp_button_is_pressed), (12 bytes).
    Removing bsp.o(i.bsp_buttons_disable), (4 bytes).
    Removing bsp.o(i.bsp_buttons_enable), (4 bytes).
    Removing bsp.o(i.bsp_init), (148 bytes).
    Removing bsp.o(i.bsp_wakeup_button_disable), (6 bytes).
    Removing bsp.o(i.button_timer_handler), (8 bytes).
    Removing bsp.o(i.leds_timer_handler), (20 bytes).
    Removing bsp_btn_ble.o(.rev16_text), (4 bytes).
    Removing bsp_btn_ble.o(.revsh_text), (4 bytes).
    Removing bsp_btn_ble.o(.rrx_text), (6 bytes).
    Removing bsp_btn_ble.o(i.bsp_btn_ble_init), (56 bytes).
    Removing utf.o(i.utf16DecodeRune), (74 bytes).
    Removing utf.o(i.utf16EncodeRune), (64 bytes).
    Removing utf.o(i.utf16RuneCount), (60 bytes).
    Removing utf.o(i.utf16UTF8Count), (78 bytes).
    Removing utf.o(i.utf8DecodeRune), (174 bytes).
    Removing utf.o(i.utf8EncodeRune), (160 bytes).
    Removing utf.o(i.utf8RuneCount), (58 bytes).
    Removing utf.o(i.utf8UTF16Count), (76 bytes).
    Removing ble_advdata.o(.rev16_text), (4 bytes).
    Removing ble_advdata.o(.revsh_text), (4 bytes).
    Removing ble_advdata.o(.rrx_text), (6 bytes).
    Removing ble_advdata.o(i.ble_advdata_appearance_find), (52 bytes).
    Removing ble_advdata.o(i.ble_advdata_name_find), (68 bytes).
    Removing ble_advdata.o(i.ble_advdata_short_name_find), (74 bytes).
    Removing ble_advdata.o(i.ble_advdata_uuid_find), (182 bytes).
    Removing ble_advertising.o(.rev16_text), (4 bytes).
    Removing ble_advertising.o(.revsh_text), (4 bytes).
    Removing ble_advertising.o(.rrx_text), (6 bytes).
    Removing ble_advertising.o(i.ble_advertising_advdata_update), (160 bytes).
    Removing ble_advertising.o(i.ble_advertising_modes_config_set), (8 bytes).
    Removing ble_advertising.o(i.ble_advertising_peer_addr_reply), (38 bytes).
    Removing ble_advertising.o(i.ble_advertising_restart_without_whitelist), (58 bytes).
    Removing ble_advertising.o(i.ble_advertising_whitelist_reply), (36 bytes).
    Removing ble_conn_params.o(.rev16_text), (4 bytes).
    Removing ble_conn_params.o(.revsh_text), (4 bytes).
    Removing ble_conn_params.o(.rrx_text), (6 bytes).
    Removing ble_conn_params.o(i.ble_conn_params_change_conn_params), (64 bytes).
    Removing ble_conn_params.o(i.ble_conn_params_stop), (36 bytes).
    Removing ble_conn_state.o(.rev16_text), (4 bytes).
    Removing ble_conn_state.o(.revsh_text), (4 bytes).
    Removing ble_conn_state.o(.rrx_text), (6 bytes).
    Removing ble_conn_state.o(i.active_flag_count), (28 bytes).
    Removing ble_conn_state.o(i.bcs_internal_state_reset), (12 bytes).
    Removing ble_conn_state.o(i.ble_conn_state_central_conn_count), (28 bytes).
    Removing ble_conn_state.o(i.ble_conn_state_central_handles), (32 bytes).
    Removing ble_conn_state.o(i.ble_conn_state_conn_count), (12 bytes).
    Removing ble_conn_state.o(i.ble_conn_state_conn_handles), (12 bytes).
    Removing ble_conn_state.o(i.ble_conn_state_encrypted), (32 bytes).
    Removing ble_conn_state.o(i.ble_conn_state_for_each_connected), (20 bytes).
    Removing ble_conn_state.o(i.ble_conn_state_for_each_set_user_flag), (44 bytes).
    Removing ble_conn_state.o(i.ble_conn_state_init), (4 bytes).
    Removing ble_conn_state.o(i.ble_conn_state_lesc), (32 bytes).
    Removing ble_conn_state.o(i.ble_conn_state_mitm_protected), (32 bytes).
    Removing ble_conn_state.o(i.ble_conn_state_periph_handles), (36 bytes).
    Removing ble_conn_state.o(i.ble_conn_state_peripheral_conn_count), (32 bytes).
    Removing ble_conn_state.o(i.ble_conn_state_role), (36 bytes).
    Removing ble_conn_state.o(i.ble_conn_state_status), (36 bytes).
    Removing ble_conn_state.o(i.ble_conn_state_user_flag_acquire), (24 bytes).
    Removing ble_conn_state.o(i.ble_conn_state_user_flag_get), (48 bytes).
    Removing ble_conn_state.o(i.ble_conn_state_user_flag_set), (56 bytes).
    Removing ble_conn_state.o(i.for_each_set_flag), (48 bytes).
    Removing ble_conn_state.o(i.user_flag_is_acquired), (12 bytes).
    Removing ble_link_ctx_manager.o(.rev16_text), (4 bytes).
    Removing ble_link_ctx_manager.o(.revsh_text), (4 bytes).
    Removing ble_link_ctx_manager.o(.rrx_text), (6 bytes).
    Removing ble_srv_common.o(.rev16_text), (4 bytes).
    Removing ble_srv_common.o(.revsh_text), (4 bytes).
    Removing ble_srv_common.o(.rrx_text), (6 bytes).
    Removing ble_srv_common.o(i.ble_srv_ascii_to_utf8), (18 bytes).
    Removing ble_srv_common.o(i.ble_srv_is_indication_enabled), (8 bytes).
    Removing ble_srv_common.o(i.ble_srv_report_ref_encode), (12 bytes).
    Removing ble_srv_common.o(i.descriptor_add), (170 bytes).
    Removing nrf_ble_gatt.o(.rev16_text), (4 bytes).
    Removing nrf_ble_gatt.o(.revsh_text), (4 bytes).
    Removing nrf_ble_gatt.o(.rrx_text), (6 bytes).
    Removing nrf_ble_gatt.o(i.nrf_ble_gatt_att_mtu_central_set), (24 bytes).
    Removing nrf_ble_gatt.o(i.nrf_ble_gatt_data_length_get), (34 bytes).
    Removing nrf_ble_gatt.o(i.nrf_ble_gatt_data_length_set), (46 bytes).
    Removing nrf_ble_gatt.o(i.nrf_ble_gatt_eff_mtu_get), (12 bytes).
    Removing nrf_ble_qwr.o(.rev16_text), (4 bytes).
    Removing nrf_ble_qwr.o(.revsh_text), (4 bytes).
    Removing nrf_ble_qwr.o(.rrx_text), (6 bytes).
    Removing ble_nus.o(.rev16_text), (4 bytes).
    Removing ble_nus.o(.revsh_text), (4 bytes).
    Removing ble_nus.o(.rrx_text), (6 bytes).
    Removing nrf_drv_clock.o(.rev16_text), (4 bytes).
    Removing nrf_drv_clock.o(.revsh_text), (4 bytes).
    Removing nrf_drv_clock.o(.rrx_text), (6 bytes).
    Removing nrf_drv_clock.o(i.item_enqueue), (22 bytes).
    Removing nrf_drv_clock.o(i.nrf_drv_clock_calibration_abort), (4 bytes).
    Removing nrf_drv_clock.o(i.nrf_drv_clock_calibration_start), (4 bytes).
    Removing nrf_drv_clock.o(i.nrf_drv_clock_hfclk_is_running), (44 bytes).
    Removing nrf_drv_clock.o(i.nrf_drv_clock_hfclk_release), (56 bytes).
    Removing nrf_drv_clock.o(i.nrf_drv_clock_hfclk_request), (92 bytes).
    Removing nrf_drv_clock.o(i.nrf_drv_clock_init_check), (12 bytes).
    Removing nrf_drv_clock.o(i.nrf_drv_clock_is_calibrating), (4 bytes).
    Removing nrf_drv_clock.o(i.nrf_drv_clock_lfclk_is_running), (28 bytes).
    Removing nrf_drv_clock.o(i.nrf_drv_clock_lfclk_request), (84 bytes).
    Removing nrf_drv_clock.o(i.nrf_drv_clock_uninit), (24 bytes).
    Removing nrf_drv_clock.o(.constdata), (19 bytes).
    Removing nrf_drv_clock.o(.constdata), (32 bytes).
    Removing nrf_drv_clock.o(.constdata), (32 bytes).
    Removing nrf_drv_clock.o(.constdata), (29 bytes).
    Removing nrf_drv_uart.o(.rev16_text), (4 bytes).
    Removing nrf_drv_uart.o(.revsh_text), (4 bytes).
    Removing nrf_drv_uart.o(.rrx_text), (6 bytes).
    Removing nrf_drv_uart.o(i.nrf_drv_uart_init), (104 bytes).
    Removing nrf_drv_uart.o(i.uart_evt_handler), (48 bytes).
    Removing nrf_drv_uart.o(i.uarte_evt_handler), (48 bytes).
    Removing nrf_drv_uart.o(.data), (12 bytes).
    Removing nrfx_atomic.o(.rev16_text), (4 bytes).
    Removing nrfx_atomic.o(.revsh_text), (4 bytes).
    Removing nrfx_atomic.o(.rrx_text), (6 bytes).
    Removing nrfx_atomic.o(.emb_text), (226 bytes).
    Removing nrfx_atomic.o(i.nrfx_atomic_flag_clear), (6 bytes).
    Removing nrfx_atomic.o(i.nrfx_atomic_flag_clear_fetch), (6 bytes).
    Removing nrfx_atomic.o(i.nrfx_atomic_flag_set), (6 bytes).
    Removing nrfx_atomic.o(i.nrfx_atomic_flag_set_fetch), (6 bytes).
    Removing nrfx_atomic.o(i.nrfx_atomic_u32_add), (12 bytes).
    Removing nrfx_atomic.o(i.nrfx_atomic_u32_and), (12 bytes).
    Removing nrfx_atomic.o(i.nrfx_atomic_u32_cmp_exch), (4 bytes).
    Removing nrfx_atomic.o(i.nrfx_atomic_u32_fetch_add), (10 bytes).
    Removing nrfx_atomic.o(i.nrfx_atomic_u32_fetch_and), (10 bytes).
    Removing nrfx_atomic.o(i.nrfx_atomic_u32_fetch_or), (10 bytes).
    Removing nrfx_atomic.o(i.nrfx_atomic_u32_fetch_store), (10 bytes).
    Removing nrfx_atomic.o(i.nrfx_atomic_u32_fetch_sub), (10 bytes).
    Removing nrfx_atomic.o(i.nrfx_atomic_u32_fetch_sub_hs), (10 bytes).
    Removing nrfx_atomic.o(i.nrfx_atomic_u32_fetch_xor), (10 bytes).
    Removing nrfx_atomic.o(i.nrfx_atomic_u32_or), (12 bytes).
    Removing nrfx_atomic.o(i.nrfx_atomic_u32_store), (12 bytes).
    Removing nrfx_atomic.o(i.nrfx_atomic_u32_sub), (12 bytes).
    Removing nrfx_atomic.o(i.nrfx_atomic_u32_sub_hs), (12 bytes).
    Removing nrfx_atomic.o(i.nrfx_atomic_u32_xor), (12 bytes).
    Removing nrfx_clock.o(.rev16_text), (4 bytes).
    Removing nrfx_clock.o(.revsh_text), (4 bytes).
    Removing nrfx_clock.o(.rrx_text), (6 bytes).
    Removing nrfx_clock.o(i.nrfx_clock_calibration_start), (4 bytes).
    Removing nrfx_clock.o(i.nrfx_clock_calibration_timer_start), (2 bytes).
    Removing nrfx_clock.o(i.nrfx_clock_calibration_timer_stop), (2 bytes).
    Removing nrfx_clock.o(i.nrfx_clock_disable), (30 bytes).
    Removing nrfx_clock.o(i.nrfx_clock_hfclk_start), (22 bytes).
    Removing nrfx_clock.o(i.nrfx_clock_hfclk_stop), (60 bytes).
    Removing nrfx_clock.o(i.nrfx_clock_is_calibrating), (4 bytes).
    Removing nrfx_clock.o(i.nrfx_clock_lfclk_start), (76 bytes).
    Removing nrfx_clock.o(i.nrfx_clock_uninit), (24 bytes).
    Removing nrfx_clock.o(.constdata), (16 bytes).
    Removing nrfx_clock.o(.constdata), (29 bytes).
    Removing nrfx_gpiote.o(.rev16_text), (4 bytes).
    Removing nrfx_gpiote.o(.revsh_text), (4 bytes).
    Removing nrfx_gpiote.o(.rrx_text), (6 bytes).
    Removing nrfx_gpiote.o(i.channel_free), (28 bytes).
    Removing nrfx_gpiote.o(i.channel_port_alloc), (72 bytes).
    Removing nrfx_gpiote.o(i.nrf_gpio_cfg), (36 bytes).
    Removing nrfx_gpiote.o(i.nrf_gpio_cfg_default), (18 bytes).
    Removing nrfx_gpiote.o(i.nrf_gpio_pin_present_check), (30 bytes).
    Removing nrfx_gpiote.o(i.nrfx_gpiote_clr_task_addr_get), (16 bytes).
    Removing nrfx_gpiote.o(i.nrfx_gpiote_clr_task_get), (16 bytes).
    Removing nrfx_gpiote.o(i.nrfx_gpiote_clr_task_trigger), (28 bytes).
    Removing nrfx_gpiote.o(i.nrfx_gpiote_in_event_addr_get), (16 bytes).
    Removing nrfx_gpiote.o(i.nrfx_gpiote_in_event_disable), (72 bytes).
    Removing nrfx_gpiote.o(i.nrfx_gpiote_in_event_enable), (160 bytes).
    Removing nrfx_gpiote.o(i.nrfx_gpiote_in_event_get), (34 bytes).
    Removing nrfx_gpiote.o(i.nrfx_gpiote_in_init), (204 bytes).
    Removing nrfx_gpiote.o(i.nrfx_gpiote_in_is_set), (20 bytes).
    Removing nrfx_gpiote.o(i.nrfx_gpiote_in_uninit), (88 bytes).
    Removing nrfx_gpiote.o(i.nrfx_gpiote_init), (116 bytes).
    Removing nrfx_gpiote.o(i.nrfx_gpiote_is_init), (20 bytes).
    Removing nrfx_gpiote.o(i.nrfx_gpiote_out_clear), (14 bytes).
    Removing nrfx_gpiote.o(i.nrfx_gpiote_out_init), (188 bytes).
    Removing nrfx_gpiote.o(i.nrfx_gpiote_out_set), (14 bytes).
    Removing nrfx_gpiote.o(i.nrfx_gpiote_out_task_addr_get), (16 bytes).
    Removing nrfx_gpiote.o(i.nrfx_gpiote_out_task_disable), (36 bytes).
    Removing nrfx_gpiote.o(i.nrfx_gpiote_out_task_enable), (36 bytes).
    Removing nrfx_gpiote.o(i.nrfx_gpiote_out_task_force), (40 bytes).
    Removing nrfx_gpiote.o(i.nrfx_gpiote_out_task_get), (12 bytes).
    Removing nrfx_gpiote.o(i.nrfx_gpiote_out_task_trigger), (24 bytes).
    Removing nrfx_gpiote.o(i.nrfx_gpiote_out_toggle), (28 bytes).
    Removing nrfx_gpiote.o(i.nrfx_gpiote_out_uninit), (88 bytes).
    Removing nrfx_gpiote.o(i.nrfx_gpiote_set_task_addr_get), (16 bytes).
    Removing nrfx_gpiote.o(i.nrfx_gpiote_set_task_get), (16 bytes).
    Removing nrfx_gpiote.o(i.nrfx_gpiote_set_task_trigger), (28 bytes).
    Removing nrfx_gpiote.o(i.nrfx_gpiote_uninit), (64 bytes).
    Removing nrfx_gpiote.o(i.pin_configured_check), (20 bytes).
    Removing nrfx_gpiote.o(i.pin_configured_clear), (28 bytes).
    Removing nrfx_gpiote.o(i.pin_configured_set), (28 bytes).
    Removing nrfx_gpiote.o(i.pin_in_use_by_port), (24 bytes).
    Removing nrfx_gpiote.o(i.pin_in_use_by_te), (24 bytes).
    Removing nrfx_gpiote.o(.constdata), (17 bytes).
    Removing nrfx_gpiote.o(.constdata), (21 bytes).
    Removing nrfx_gpiote.o(.constdata), (20 bytes).
    Removing nrfx_prs.o(.rev16_text), (4 bytes).
    Removing nrfx_prs.o(.revsh_text), (4 bytes).
    Removing nrfx_prs.o(.rrx_text), (6 bytes).
    Removing nrfx_prs.o(i.nrfx_prs_acquire), (58 bytes).
    Removing nrfx_prs.o(i.nrfx_prs_release), (18 bytes).
    Removing nrfx_prs.o(i.prs_box_get), (24 bytes).
    Removing nrfx_prs.o(.constdata), (17 bytes).
    Removing nrfx_uart.o(.rev16_text), (4 bytes).
    Removing nrfx_uart.o(.revsh_text), (4 bytes).
    Removing nrfx_uart.o(.rrx_text), (6 bytes).
    Removing nrfx_uart.o(i.apply_config), (136 bytes).
    Removing nrfx_uart.o(i.nrf_gpio_cfg), (36 bytes).
    Removing nrfx_uart.o(i.nrf_gpio_cfg_default), (18 bytes).
    Removing nrfx_uart.o(i.nrf_gpio_cfg_input), (18 bytes).
    Removing nrfx_uart.o(i.nrf_gpio_cfg_output), (20 bytes).
    Removing nrfx_uart.o(i.nrf_uart_event_check), (10 bytes).
    Removing nrfx_uart.o(i.nrf_uart_event_clear), (12 bytes).
    Removing nrfx_uart.o(i.nrf_uart_int_enable_check), (12 bytes).
    Removing nrfx_uart.o(i.nrfx_uart_0_irq_handler), (16 bytes).
    Removing nrfx_uart.o(i.nrfx_uart_errorsrc_get), (26 bytes).
    Removing nrfx_uart.o(i.nrfx_uart_init), (212 bytes).
    Removing nrfx_uart.o(i.nrfx_uart_rx), (236 bytes).
    Removing nrfx_uart.o(i.nrfx_uart_rx_abort), (18 bytes).
    Removing nrfx_uart.o(i.nrfx_uart_rx_disable), (36 bytes).
    Removing nrfx_uart.o(i.nrfx_uart_rx_enable), (60 bytes).
    Removing nrfx_uart.o(i.nrfx_uart_rx_ready), (14 bytes).
    Removing nrfx_uart.o(i.nrfx_uart_tx), (168 bytes).
    Removing nrfx_uart.o(i.nrfx_uart_tx_abort), (48 bytes).
    Removing nrfx_uart.o(i.nrfx_uart_tx_in_progress), (32 bytes).
    Removing nrfx_uart.o(i.nrfx_uart_uninit), (192 bytes).
    Removing nrfx_uart.o(i.rx_byte), (50 bytes).
    Removing nrfx_uart.o(i.rx_done_event), (22 bytes).
    Removing nrfx_uart.o(i.rx_enable), (32 bytes).
    Removing nrfx_uart.o(i.tx_byte), (32 bytes).
    Removing nrfx_uart.o(i.tx_done_event), (26 bytes).
    Removing nrfx_uart.o(i.uart_irq_handler), (298 bytes).
    Removing nrfx_uart.o(.bss), (44 bytes).
    Removing nrfx_uart.o(.constdata), (15 bytes).
    Removing nrfx_uart.o(.constdata), (4 bytes).
    Removing nrfx_uart.o(.constdata), (13 bytes).
    Removing nrfx_uart.o(.constdata), (13 bytes).
    Removing nrfx_uarte.o(.rev16_text), (4 bytes).
    Removing nrfx_uarte.o(.revsh_text), (4 bytes).
    Removing nrfx_uarte.o(.rrx_text), (6 bytes).
    Removing nrfx_uarte.o(i.apply_config), (136 bytes).
    Removing nrfx_uarte.o(i.interrupts_enable), (136 bytes).
    Removing nrfx_uarte.o(i.nrf_gpio_cfg), (36 bytes).
    Removing nrfx_uarte.o(i.nrf_gpio_cfg_default), (18 bytes).
    Removing nrfx_uarte.o(i.nrf_gpio_cfg_input), (18 bytes).
    Removing nrfx_uarte.o(i.nrf_gpio_cfg_output), (20 bytes).
    Removing nrfx_uarte.o(i.nrf_uarte_event_check), (10 bytes).
    Removing nrfx_uarte.o(i.nrf_uarte_event_clear), (12 bytes).
    Removing nrfx_uarte.o(i.nrfx_is_in_ram), (16 bytes).
    Removing nrfx_uarte.o(i.nrfx_uarte_0_irq_handler), (16 bytes).
    Removing nrfx_uarte.o(i.nrfx_uarte_errorsrc_get), (26 bytes).
    Removing nrfx_uarte.o(i.nrfx_uarte_init), (108 bytes).
    Removing nrfx_uarte.o(i.nrfx_uarte_rx), (260 bytes).
    Removing nrfx_uarte.o(i.nrfx_uarte_rx_abort), (44 bytes).
    Removing nrfx_uarte.o(i.nrfx_uarte_rx_ready), (14 bytes).
    Removing nrfx_uarte.o(i.nrfx_uarte_tx), (172 bytes).
    Removing nrfx_uarte.o(i.nrfx_uarte_tx_abort), (56 bytes).
    Removing nrfx_uarte.o(i.nrfx_uarte_tx_in_progress), (28 bytes).
    Removing nrfx_uarte.o(i.nrfx_uarte_uninit), (264 bytes).
    Removing nrfx_uarte.o(i.rx_done_event), (22 bytes).
    Removing nrfx_uarte.o(i.tx_done_event), (26 bytes).
    Removing nrfx_uarte.o(i.uarte_irq_handler), (274 bytes).
    Removing nrfx_uarte.o(.bss), (36 bytes).
    Removing nrfx_uarte.o(.constdata), (16 bytes).
    Removing nrfx_uarte.o(.constdata), (4 bytes).
    Removing nrfx_uarte.o(.constdata), (14 bytes).
    Removing nrfx_uarte.o(.constdata), (14 bytes).
    Removing nrf_drv_ppi.o(.rev16_text), (4 bytes).
    Removing nrf_drv_ppi.o(.revsh_text), (4 bytes).
    Removing nrf_drv_ppi.o(.rrx_text), (6 bytes).
    Removing nrf_drv_ppi.o(i.nrf_drv_ppi_uninit), (28 bytes).
    Removing nrfx_ppi.o(.rev16_text), (4 bytes).
    Removing nrfx_ppi.o(.revsh_text), (4 bytes).
    Removing nrfx_ppi.o(.rrx_text), (6 bytes).
    Removing nrfx_ppi.o(i.is_allocated_group), (24 bytes).
    Removing nrfx_ppi.o(i.is_app_group), (12 bytes).
    Removing nrfx_ppi.o(i.nrfx_ppi_channel_disable), (52 bytes).
    Removing nrfx_ppi.o(i.nrfx_ppi_channel_fork_assign), (36 bytes).
    Removing nrfx_ppi.o(i.nrfx_ppi_channel_free), (64 bytes).
    Removing nrfx_ppi.o(i.nrfx_ppi_channels_include_in_group), (84 bytes).
    Removing nrfx_ppi.o(i.nrfx_ppi_channels_remove_from_group), (84 bytes).
    Removing nrfx_ppi.o(i.nrfx_ppi_free_all), (84 bytes).
    Removing nrfx_ppi.o(i.nrfx_ppi_group_alloc), (104 bytes).
    Removing nrfx_ppi.o(i.nrfx_ppi_group_disable), (36 bytes).
    Removing nrfx_ppi.o(i.nrfx_ppi_group_enable), (44 bytes).
    Removing nrfx_ppi.o(i.nrfx_ppi_group_free), (80 bytes).
    Removing nrfx_ppi.o(.constdata), (23 bytes).
    Removing nrfx_ppi.o(.constdata), (22 bytes).
    Removing nrfx_ppi.o(.constdata), (24 bytes).
    Removing nrfx_ppi.o(.constdata), (29 bytes).
    Removing nrfx_ppi.o(.constdata), (24 bytes).
    Removing nrfx_ppi.o(.constdata), (25 bytes).
    Removing nrfx_ppi.o(.constdata), (21 bytes).
    Removing nrfx_ppi.o(.constdata), (20 bytes).
    Removing nrfx_ppi.o(.constdata), (22 bytes).
    Removing nrfx_ppi.o(.constdata), (23 bytes).
    Removing nrfx_ppi.o(.constdata), (36 bytes).
    Removing nrfx_ppi.o(.constdata), (35 bytes).
    Removing nrfx_saadc.o(.rev16_text), (4 bytes).
    Removing nrfx_saadc.o(.revsh_text), (4 bytes).
    Removing nrfx_saadc.o(.rrx_text), (6 bytes).
    Removing nrfx_saadc.o(i.nrf_saadc_limit_int_get), (16 bytes).
    Removing nrfx_saadc.o(i.nrfx_coredep_delay_us), (16 bytes).
    Removing nrfx_saadc.o(i.nrfx_saadc_abort), (92 bytes).
    Removing nrfx_saadc.o(i.nrfx_saadc_calibrate_offset), (52 bytes).
    Removing nrfx_saadc.o(i.nrfx_saadc_channel_uninit), (68 bytes).
    Removing nrfx_saadc.o(i.nrfx_saadc_is_busy), (16 bytes).
    Removing nrfx_saadc.o(i.nrfx_saadc_limits_set), (128 bytes).
    Removing nrfx_saadc.o(i.nrfx_saadc_sample), (44 bytes).
    Removing nrfx_saadc.o(i.nrfx_saadc_sample_convert), (192 bytes).
    Removing nrfx_saadc.o(i.nrfx_saadc_uninit), (116 bytes).
    Removing nrfx_saadc.o(.constdata), (6 bytes).
    Removing nrfx_saadc.o(.constdata), (16 bytes).
    Removing nrfx_saadc.o(.constdata), (24 bytes).
    Removing nrfx_saadc.o(.constdata), (26 bytes).
    Removing nrfx_saadc.o(.constdata), (26 bytes).
    Removing nrfx_saadc.o(.constdata), (26 bytes).
    Removing nrfx_saadc.o(.constdata), (18 bytes).
    Removing nrfx_saadc.o(.constdata), (28 bytes).
    Removing nrfx_timer.o(.rev16_text), (4 bytes).
    Removing nrfx_timer.o(.revsh_text), (4 bytes).
    Removing nrfx_timer.o(.rrx_text), (6 bytes).
    Removing nrfx_timer.o(i.nrfx_timer_capture), (30 bytes).
    Removing nrfx_timer.o(i.nrfx_timer_clear), (8 bytes).
    Removing nrfx_timer.o(i.nrfx_timer_compare_int_disable), (14 bytes).
    Removing nrfx_timer.o(i.nrfx_timer_compare_int_enable), (36 bytes).
    Removing nrfx_timer.o(i.nrfx_timer_disable), (28 bytes).
    Removing nrfx_timer.o(i.nrfx_timer_increment), (8 bytes).
    Removing nrfx_timer.o(i.nrfx_timer_is_enabled), (32 bytes).
    Removing nrfx_timer.o(i.nrfx_timer_pause), (8 bytes).
    Removing nrfx_timer.o(i.nrfx_timer_resume), (8 bytes).
    Removing nrfx_timer.o(i.nrfx_timer_uninit), (92 bytes).
    Removing nrfx_timer.o(.constdata), (16 bytes).
    Removing app_button.o(.rev16_text), (4 bytes).
    Removing app_button.o(.revsh_text), (4 bytes).
    Removing app_button.o(.rrx_text), (6 bytes).
    Removing app_button.o(i.app_button_disable), (72 bytes).
    Removing app_button.o(i.app_button_enable), (36 bytes).
    Removing app_button.o(i.app_button_init), (128 bytes).
    Removing app_button.o(i.app_button_is_pushed), (36 bytes).
    Removing app_button.o(i.button_get), (40 bytes).
    Removing app_button.o(i.detection_delay_timeout_handler), (72 bytes).
    Removing app_button.o(i.evt_handle), (224 bytes).
    Removing app_button.o(i.gpiote_event_handler), (60 bytes).
    Removing app_button.o(i.state_set), (48 bytes).
    Removing app_button.o(i.timer_start), (24 bytes).
    Removing app_button.o(i.usr_event), (32 bytes).
    Removing app_button.o(.bss), (32 bytes).
    Removing app_button.o(.bss), (16 bytes).
    Removing app_button.o(.constdata), (8 bytes).
    Removing app_button.o(.data), (24 bytes).
    Removing app_error.o(.rev16_text), (4 bytes).
    Removing app_error.o(.revsh_text), (4 bytes).
    Removing app_error.o(.rrx_text), (6 bytes).
    Removing app_error.o(i.app_error_save_and_stop), (100 bytes).
    Removing app_error.o(.bss), (32 bytes).
    Removing app_error_handler_keil.o(.rev16_text), (4 bytes).
    Removing app_error_handler_keil.o(.revsh_text), (4 bytes).
    Removing app_error_handler_keil.o(.rrx_text), (6 bytes).
    Removing app_error_handler_keil.o(.emb_text), (26 bytes).
    Removing app_error_weak.o(.rev16_text), (4 bytes).
    Removing app_error_weak.o(.revsh_text), (4 bytes).
    Removing app_error_weak.o(.rrx_text), (6 bytes).
    Removing app_fifo.o(.rev16_text), (4 bytes).
    Removing app_fifo.o(.revsh_text), (4 bytes).
    Removing app_fifo.o(.rrx_text), (6 bytes).
    Removing app_fifo.o(i.app_fifo_flush), (8 bytes).
    Removing app_fifo.o(i.app_fifo_get), (22 bytes).
    Removing app_fifo.o(i.app_fifo_init), (32 bytes).
    Removing app_fifo.o(i.app_fifo_peek), (34 bytes).
    Removing app_fifo.o(i.app_fifo_put), (26 bytes).
    Removing app_fifo.o(i.app_fifo_read), (74 bytes).
    Removing app_fifo.o(i.app_fifo_write), (82 bytes).
    Removing app_fifo.o(i.fifo_get), (20 bytes).
    Removing app_fifo.o(i.fifo_put), (18 bytes).
    Removing app_scheduler.o(.rev16_text), (4 bytes).
    Removing app_scheduler.o(.revsh_text), (4 bytes).
    Removing app_scheduler.o(.rrx_text), (6 bytes).
    Removing app_scheduler.o(i.app_sched_event_put), (160 bytes).
    Removing app_scheduler.o(i.app_sched_execute), (64 bytes).
    Removing app_scheduler.o(i.app_sched_init), (44 bytes).
    Removing app_scheduler.o(i.app_sched_queue_space_get), (36 bytes).
    Removing app_scheduler.o(.data), (16 bytes).
    Removing app_timer2.o(.rev16_text), (4 bytes).
    Removing app_timer2.o(.revsh_text), (4 bytes).
    Removing app_timer2.o(.rrx_text), (6 bytes).
    Removing app_timer2.o(i.app_timer_cnt_diff_compute), (8 bytes).
    Removing app_timer2.o(i.app_timer_pause), (12 bytes).
    Removing app_timer2.o(i.app_timer_resume), (12 bytes).
    Removing app_timer2.o(i.app_timer_stop_all), (20 bytes).
    Removing app_uart_fifo.o(.rev16_text), (4 bytes).
    Removing app_uart_fifo.o(.revsh_text), (4 bytes).
    Removing app_uart_fifo.o(.rrx_text), (6 bytes).
    Removing app_uart_fifo.o(i.app_uart_close), (40 bytes).
    Removing app_uart_fifo.o(i.app_uart_flush), (32 bytes).
    Removing app_uart_fifo.o(i.app_uart_get), (52 bytes).
    Removing app_uart_fifo.o(i.app_uart_init), (164 bytes).
    Removing app_uart_fifo.o(i.app_uart_put), (84 bytes).
    Removing app_uart_fifo.o(i.nrf_drv_uart_rx), (32 bytes).
    Removing app_uart_fifo.o(i.nrf_drv_uart_tx), (32 bytes).
    Removing app_uart_fifo.o(i.uart_event_handler), (164 bytes).
    Removing app_uart_fifo.o(.bss), (32 bytes).
    Removing app_uart_fifo.o(.constdata), (32 bytes).
    Removing app_uart_fifo.o(.data), (28 bytes).
    Removing app_util_platform.o(.rev16_text), (4 bytes).
    Removing app_util_platform.o(.revsh_text), (4 bytes).
    Removing app_util_platform.o(.rrx_text), (6 bytes).
    Removing app_util_platform.o(i.app_util_disable_irq), (16 bytes).
    Removing app_util_platform.o(i.app_util_enable_irq), (20 bytes).
    Removing app_util_platform.o(i.current_int_priority_get), (48 bytes).
    Removing app_util_platform.o(i.privilege_level_get), (26 bytes).
    Removing app_util_platform.o(.data), (4 bytes).
    Removing drv_rtc.o(.rev16_text), (4 bytes).
    Removing drv_rtc.o(.revsh_text), (4 bytes).
    Removing drv_rtc.o(.rrx_text), (6 bytes).
    Removing drv_rtc.o(i.drv_rtc_compare_enable), (12 bytes).
    Removing drv_rtc.o(i.drv_rtc_compare_get), (14 bytes).
    Removing drv_rtc.o(i.drv_rtc_overflow_disable), (16 bytes).
    Removing drv_rtc.o(i.drv_rtc_tick_disable), (16 bytes).
    Removing drv_rtc.o(i.drv_rtc_tick_enable), (8 bytes).
    Removing drv_rtc.o(i.drv_rtc_tick_pending), (8 bytes).
    Removing drv_rtc.o(i.drv_rtc_uninit), (80 bytes).
    Removing hardfault_implementation.o(.rev16_text), (4 bytes).
    Removing hardfault_implementation.o(.revsh_text), (4 bytes).
    Removing hardfault_implementation.o(.rrx_text), (6 bytes).
    Removing nrf_assert.o(.rev16_text), (4 bytes).
    Removing nrf_assert.o(.revsh_text), (4 bytes).
    Removing nrf_assert.o(.rrx_text), (6 bytes).
    Removing nrf_assert.o(i.assert_nrf_callback), (20 bytes).
    Removing nrf_atfifo.o(.rev16_text), (4 bytes).
    Removing nrf_atfifo.o(.revsh_text), (4 bytes).
    Removing nrf_atfifo.o(.rrx_text), (6 bytes).
    Removing nrf_atfifo.o(i.nrf_atfifo_alloc_put), (46 bytes).
    Removing nrf_atfifo.o(i.nrf_atfifo_clear), (16 bytes).
    Removing nrf_atfifo.o(i.nrf_atfifo_get_free), (48 bytes).
    Removing nrf_atflags.o(.rev16_text), (4 bytes).
    Removing nrf_atflags.o(.revsh_text), (4 bytes).
    Removing nrf_atflags.o(.rrx_text), (6 bytes).
    Removing nrf_atflags.o(i.nrf_atflags_fetch_clear), (32 bytes).
    Removing nrf_atflags.o(i.nrf_atflags_fetch_set), (32 bytes).
    Removing nrf_atflags.o(i.nrf_atflags_find_and_clear_flag), (72 bytes).
    Removing nrf_atflags.o(i.nrf_atflags_find_and_set_flag), (74 bytes).
    Removing nrf_atflags.o(i.nrf_atflags_init), (36 bytes).
    Removing nrf_atomic.o(.rev16_text), (4 bytes).
    Removing nrf_atomic.o(.revsh_text), (4 bytes).
    Removing nrf_atomic.o(.rrx_text), (6 bytes).
    Removing nrf_atomic.o(i.nrf_atomic_flag_clear), (6 bytes).
    Removing nrf_atomic.o(i.nrf_atomic_flag_clear_fetch), (6 bytes).
    Removing nrf_atomic.o(i.nrf_atomic_flag_set), (6 bytes).
    Removing nrf_atomic.o(i.nrf_atomic_flag_set_fetch), (6 bytes).
    Removing nrf_atomic.o(i.nrf_atomic_u32_add), (12 bytes).
    Removing nrf_atomic.o(i.nrf_atomic_u32_cmp_exch), (4 bytes).
    Removing nrf_atomic.o(i.nrf_atomic_u32_fetch_add), (10 bytes).
    Removing nrf_atomic.o(i.nrf_atomic_u32_fetch_and), (10 bytes).
    Removing nrf_atomic.o(i.nrf_atomic_u32_fetch_or), (10 bytes).
    Removing nrf_atomic.o(i.nrf_atomic_u32_fetch_store), (10 bytes).
    Removing nrf_atomic.o(i.nrf_atomic_u32_fetch_sub), (10 bytes).
    Removing nrf_atomic.o(i.nrf_atomic_u32_fetch_sub_hs), (10 bytes).
    Removing nrf_atomic.o(i.nrf_atomic_u32_fetch_xor), (10 bytes).
    Removing nrf_atomic.o(i.nrf_atomic_u32_store), (12 bytes).
    Removing nrf_atomic.o(i.nrf_atomic_u32_sub), (12 bytes).
    Removing nrf_atomic.o(i.nrf_atomic_u32_sub_hs), (12 bytes).
    Removing nrf_atomic.o(i.nrf_atomic_u32_xor), (12 bytes).
    Removing nrf_balloc.o(.rev16_text), (4 bytes).
    Removing nrf_balloc.o(.revsh_text), (4 bytes).
    Removing nrf_balloc.o(.rrx_text), (6 bytes).
    Removing nrf_balloc.o(i.nrf_balloc_alloc), (68 bytes).
    Removing nrf_balloc.o(i.nrf_balloc_free), (48 bytes).
    Removing nrf_balloc.o(i.nrf_balloc_init), (48 bytes).
    Removing nrf_fprintf.o(.rev16_text), (4 bytes).
    Removing nrf_fprintf.o(.revsh_text), (4 bytes).
    Removing nrf_fprintf.o(.rrx_text), (6 bytes).
    Removing nrf_fprintf.o(i.nrf_fprintf), (26 bytes).
    Removing nrf_fprintf.o(i.nrf_fprintf_buffer_flush), (24 bytes).
    Removing nrf_fprintf_format.o(.rev16_text), (4 bytes).
    Removing nrf_fprintf_format.o(.revsh_text), (4 bytes).
    Removing nrf_fprintf_format.o(.rrx_text), (6 bytes).
    Removing nrf_fprintf_format.o(i.buffer_add), (46 bytes).
    Removing nrf_fprintf_format.o(i.int_print), (166 bytes).
    Removing nrf_fprintf_format.o(i.nrf_fprintf_fmt), (474 bytes).
    Removing nrf_fprintf_format.o(i.unsigned_print), (180 bytes).
    Removing nrf_fprintf_format.o(.constdata), (16 bytes).
    Removing nrf_memobj.o(.rev16_text), (4 bytes).
    Removing nrf_memobj.o(.revsh_text), (4 bytes).
    Removing nrf_memobj.o(.rrx_text), (6 bytes).
    Removing nrf_memobj.o(i.memobj_op), (126 bytes).
    Removing nrf_memobj.o(i.nrf_memobj_alloc), (96 bytes).
    Removing nrf_memobj.o(i.nrf_memobj_free), (50 bytes).
    Removing nrf_memobj.o(i.nrf_memobj_get), (8 bytes).
    Removing nrf_memobj.o(i.nrf_memobj_pool_init), (4 bytes).
    Removing nrf_memobj.o(i.nrf_memobj_put), (30 bytes).
    Removing nrf_memobj.o(i.nrf_memobj_read), (16 bytes).
    Removing nrf_memobj.o(i.nrf_memobj_write), (16 bytes).
    Removing nrf_pwr_mgmt.o(.rev16_text), (4 bytes).
    Removing nrf_pwr_mgmt.o(.revsh_text), (4 bytes).
    Removing nrf_pwr_mgmt.o(.rrx_text), (6 bytes).
    Removing nrf_pwr_mgmt.o(i.nrf_pwr_mgmt_feed), (2 bytes).
    Removing nrf_pwr_mgmt.o(i.nrf_pwr_mgmt_shutdown), (60 bytes).
    Removing nrf_pwr_mgmt.o(i.shutdown_process), (112 bytes).
    Removing nrf_ringbuf.o(.rev16_text), (4 bytes).
    Removing nrf_ringbuf.o(.revsh_text), (4 bytes).
    Removing nrf_ringbuf.o(.rrx_text), (6 bytes).
    Removing nrf_ringbuf.o(i.nrf_ringbuf_alloc), (112 bytes).
    Removing nrf_ringbuf.o(i.nrf_ringbuf_cpy_get), (124 bytes).
    Removing nrf_ringbuf.o(i.nrf_ringbuf_cpy_put), (114 bytes).
    Removing nrf_ringbuf.o(i.nrf_ringbuf_free), (38 bytes).
    Removing nrf_ringbuf.o(i.nrf_ringbuf_get), (112 bytes).
    Removing nrf_ringbuf.o(i.nrf_ringbuf_init), (28 bytes).
    Removing nrf_ringbuf.o(i.nrf_ringbuf_put), (48 bytes).
    Removing nrf_section_iter.o(.rev16_text), (4 bytes).
    Removing nrf_section_iter.o(.revsh_text), (4 bytes).
    Removing nrf_section_iter.o(.rrx_text), (6 bytes).
    Removing nrf_sortlist.o(.rev16_text), (4 bytes).
    Removing nrf_sortlist.o(.revsh_text), (4 bytes).
    Removing nrf_sortlist.o(.rrx_text), (6 bytes).
    Removing nrf_sortlist.o(i.nrf_sortlist_next), (4 bytes).
    Removing nrf_strerror.o(.rev16_text), (4 bytes).
    Removing nrf_strerror.o(.revsh_text), (4 bytes).
    Removing nrf_strerror.o(.rrx_text), (6 bytes).
    Removing nrf_strerror.o(i.nrf_strerror_find), (56 bytes).
    Removing nrf_strerror.o(i.nrf_strerror_get), (20 bytes).
    Removing nrf_strerror.o(.constdata), (316 bytes).
    Removing nrf_strerror.o(.conststring), (1001 bytes).
    Removing retarget.o(.rev16_text), (4 bytes).
    Removing retarget.o(.revsh_text), (4 bytes).
    Removing retarget.o(.rrx_text), (6 bytes).
    Removing retarget.o(i.fgetc), (18 bytes).
    Removing retarget.o(i.fputc), (14 bytes).
    Removing retarget.o(.data), (4 bytes).
    Removing retarget.o(.data), (4 bytes).
    Removing nrf_log_backend_rtt.o(.rev16_text), (4 bytes).
    Removing nrf_log_backend_rtt.o(.revsh_text), (4 bytes).
    Removing nrf_log_backend_rtt.o(.rrx_text), (6 bytes).
    Removing nrf_log_backend_serial.o(.rev16_text), (4 bytes).
    Removing nrf_log_backend_serial.o(.revsh_text), (4 bytes).
    Removing nrf_log_backend_serial.o(.rrx_text), (6 bytes).
    Removing nrf_log_default_backends.o(.rev16_text), (4 bytes).
    Removing nrf_log_default_backends.o(.revsh_text), (4 bytes).
    Removing nrf_log_default_backends.o(.rrx_text), (6 bytes).
    Removing nrf_log_frontend.o(.rev16_text), (4 bytes).
    Removing nrf_log_frontend.o(.revsh_text), (4 bytes).
    Removing nrf_log_frontend.o(.rrx_text), (6 bytes).
    Removing nrf_log_str_formatter.o(.rev16_text), (4 bytes).
    Removing nrf_log_str_formatter.o(.revsh_text), (4 bytes).
    Removing nrf_log_str_formatter.o(.rrx_text), (6 bytes).
    Removing segger_rtt.o(.rev16_text), (4 bytes).
    Removing segger_rtt.o(.revsh_text), (4 bytes).
    Removing segger_rtt.o(.rrx_text), (6 bytes).
    Removing segger_rtt.o(i.SEGGER_RTT_AllocDownBuffer), (112 bytes).
    Removing segger_rtt.o(i.SEGGER_RTT_AllocUpBuffer), (112 bytes).
    Removing segger_rtt.o(i.SEGGER_RTT_ConfigDownBuffer), (96 bytes).
    Removing segger_rtt.o(i.SEGGER_RTT_ConfigUpBuffer), (96 bytes).
    Removing segger_rtt.o(i.SEGGER_RTT_GetKey), (28 bytes).
    Removing segger_rtt.o(i.SEGGER_RTT_HasData), (24 bytes).
    Removing segger_rtt.o(i.SEGGER_RTT_HasKey), (32 bytes).
    Removing segger_rtt.o(i.SEGGER_RTT_Init), (4 bytes).
    Removing segger_rtt.o(i.SEGGER_RTT_PutChar), (96 bytes).
    Removing segger_rtt.o(i.SEGGER_RTT_PutCharSkip), (84 bytes).
    Removing segger_rtt.o(i.SEGGER_RTT_PutCharSkipNoLock), (52 bytes).
    Removing segger_rtt.o(i.SEGGER_RTT_Read), (44 bytes).
    Removing segger_rtt.o(i.SEGGER_RTT_ReadNoLock), (120 bytes).
    Removing segger_rtt.o(i.SEGGER_RTT_SetFlagsDownBuffer), (68 bytes).
    Removing segger_rtt.o(i.SEGGER_RTT_SetFlagsUpBuffer), (68 bytes).
    Removing segger_rtt.o(i.SEGGER_RTT_SetNameDownBuffer), (68 bytes).
    Removing segger_rtt.o(i.SEGGER_RTT_SetNameUpBuffer), (68 bytes).
    Removing segger_rtt.o(i.SEGGER_RTT_SetTerminal), (136 bytes).
    Removing segger_rtt.o(i.SEGGER_RTT_TerminalOut), (176 bytes).
    Removing segger_rtt.o(i.SEGGER_RTT_WaitKey), (14 bytes).
    Removing segger_rtt.o(i.SEGGER_RTT_Write), (60 bytes).
    Removing segger_rtt.o(i.SEGGER_RTT_WriteNoLock), (92 bytes).
    Removing segger_rtt.o(i.SEGGER_RTT_WriteSkipNoLock), (116 bytes).
    Removing segger_rtt.o(i.SEGGER_RTT_WriteString), (26 bytes).
    Removing segger_rtt.o(i.SEGGER_RTT_WriteWithOverwriteNoLock), (148 bytes).
    Removing segger_rtt.o(i._DoInit), (104 bytes).
    Removing segger_rtt.o(i._GetAvailWriteSpace), (22 bytes).
    Removing segger_rtt.o(i._PostTerminalSwitch), (32 bytes).
    Removing segger_rtt.o(i._WriteBlocking), (90 bytes).
    Removing segger_rtt.o(i._WriteNoCheck), (66 bytes).
    Removing segger_rtt.o(.bss), (648 bytes).
    Removing segger_rtt.o(.data), (17 bytes).
    Removing segger_rtt_printf.o(.rev16_text), (4 bytes).
    Removing segger_rtt_printf.o(.revsh_text), (4 bytes).
    Removing segger_rtt_printf.o(.rrx_text), (6 bytes).
    Removing segger_rtt_printf.o(i.SEGGER_RTT_printf), (22 bytes).
    Removing segger_rtt_printf.o(i.SEGGER_RTT_vprintf), (406 bytes).
    Removing segger_rtt_printf.o(i._PrintInt), (198 bytes).
    Removing segger_rtt_printf.o(i._PrintUnsigned), (212 bytes).
    Removing segger_rtt_printf.o(i._StoreChar), (62 bytes).
    Removing segger_rtt_printf.o(.constdata), (16 bytes).
    Removing nrf_sdh.o(.rev16_text), (4 bytes).
    Removing nrf_sdh.o(.revsh_text), (4 bytes).
    Removing nrf_sdh.o(.rrx_text), (6 bytes).
    Removing nrf_sdh.o(i.nrf_sdh_disable_request), (84 bytes).
    Removing nrf_sdh.o(i.nrf_sdh_is_suspended), (20 bytes).
    Removing nrf_sdh.o(i.nrf_sdh_request_continue), (28 bytes).
    Removing nrf_sdh.o(i.nrf_sdh_resume), (56 bytes).
    Removing nrf_sdh.o(i.nrf_sdh_suspend), (24 bytes).
    Removing nrf_sdh.o(i.softdevice_evt_irq_disable), (64 bytes).
    Removing nrf_sdh_ble.o(.rev16_text), (4 bytes).
    Removing nrf_sdh_ble.o(.revsh_text), (4 bytes).
    Removing nrf_sdh_ble.o(.rrx_text), (6 bytes).
    Removing nrf_sdh_soc.o(.rev16_text), (4 bytes).
    Removing nrf_sdh_soc.o(.revsh_text), (4 bytes).
    Removing nrf_sdh_soc.o(.rrx_text), (6 bytes).
    Removing arm_startup_nrf52.o(HEAP), (8192 bytes).
    Removing system_nrf52.o(.rev16_text), (4 bytes).
    Removing system_nrf52.o(.revsh_text), (4 bytes).
    Removing system_nrf52.o(.rrx_text), (6 bytes).
    Removing system_nrf52.o(i.SystemCoreClockUpdate), (16 bytes).

622 unused section(s) (total 32201 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/microlib/division.c              0x00000000   Number         0  uldiv.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry5.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry2.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10b.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llshl.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llushr.o ABSOLUTE
    ../clib/microlib/string/memcmp.c         0x00000000   Number         0  memcmp.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpya.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpyb.o ABSOLUTE
    ../clib/microlib/string/memset.c         0x00000000   Number         0  memseta.o ABSOLUTE
    ../clib/microlib/string/strcpy.c         0x00000000   Number         0  strcpy.o ABSOLUTE
    ../clib/microlib/string/strlen.c         0x00000000   Number         0  strlen.o ABSOLUTE
    ..\..\..\..\..\..\components\ble\ble_advertising\ble_advertising.c 0x00000000   Number         0  ble_advertising.o ABSOLUTE
    ..\..\..\..\..\..\components\ble\ble_link_ctx_manager\ble_link_ctx_manager.c 0x00000000   Number         0  ble_link_ctx_manager.o ABSOLUTE
    ..\..\..\..\..\..\components\ble\ble_services\ble_nus\ble_nus.c 0x00000000   Number         0  ble_nus.o ABSOLUTE
    ..\..\..\..\..\..\components\ble\common\ble_advdata.c 0x00000000   Number         0  ble_advdata.o ABSOLUTE
    ..\..\..\..\..\..\components\ble\common\ble_conn_params.c 0x00000000   Number         0  ble_conn_params.o ABSOLUTE
    ..\..\..\..\..\..\components\ble\common\ble_conn_state.c 0x00000000   Number         0  ble_conn_state.o ABSOLUTE
    ..\..\..\..\..\..\components\ble\common\ble_srv_common.c 0x00000000   Number         0  ble_srv_common.o ABSOLUTE
    ..\..\..\..\..\..\components\ble\nrf_ble_gatt\nrf_ble_gatt.c 0x00000000   Number         0  nrf_ble_gatt.o ABSOLUTE
    ..\..\..\..\..\..\components\ble\nrf_ble_qwr\nrf_ble_qwr.c 0x00000000   Number         0  nrf_ble_qwr.o ABSOLUTE
    ..\..\..\..\..\..\components\boards\boards.c 0x00000000   Number         0  boards.o ABSOLUTE
    ..\..\..\..\..\..\components\libraries\atomic\nrf_atomic.c 0x00000000   Number         0  nrf_atomic.o ABSOLUTE
    ..\..\..\..\..\..\components\libraries\atomic_fifo\nrf_atfifo.c 0x00000000   Number         0  nrf_atfifo.o ABSOLUTE
    ..\..\..\..\..\..\components\libraries\atomic_flags\nrf_atflags.c 0x00000000   Number         0  nrf_atflags.o ABSOLUTE
    ..\..\..\..\..\..\components\libraries\balloc\nrf_balloc.c 0x00000000   Number         0  nrf_balloc.o ABSOLUTE
    ..\..\..\..\..\..\components\libraries\bsp\bsp.c 0x00000000   Number         0  bsp.o ABSOLUTE
    ..\..\..\..\..\..\components\libraries\bsp\bsp_btn_ble.c 0x00000000   Number         0  bsp_btn_ble.o ABSOLUTE
    ..\..\..\..\..\..\components\libraries\button\app_button.c 0x00000000   Number         0  app_button.o ABSOLUTE
    ..\..\..\..\..\..\components\libraries\experimental_section_vars\nrf_section_iter.c 0x00000000   Number         0  nrf_section_iter.o ABSOLUTE
    ..\..\..\..\..\..\components\libraries\fifo\app_fifo.c 0x00000000   Number         0  app_fifo.o ABSOLUTE
    ..\..\..\..\..\..\components\libraries\hardfault\hardfault_implementation.c 0x00000000   Number         0  hardfault_implementation.o ABSOLUTE
    ..\..\..\..\..\..\components\libraries\log\src\nrf_log_backend_rtt.c 0x00000000   Number         0  nrf_log_backend_rtt.o ABSOLUTE
    ..\..\..\..\..\..\components\libraries\log\src\nrf_log_backend_serial.c 0x00000000   Number         0  nrf_log_backend_serial.o ABSOLUTE
    ..\..\..\..\..\..\components\libraries\log\src\nrf_log_default_backends.c 0x00000000   Number         0  nrf_log_default_backends.o ABSOLUTE
    ..\..\..\..\..\..\components\libraries\log\src\nrf_log_frontend.c 0x00000000   Number         0  nrf_log_frontend.o ABSOLUTE
    ..\..\..\..\..\..\components\libraries\log\src\nrf_log_str_formatter.c 0x00000000   Number         0  nrf_log_str_formatter.o ABSOLUTE
    ..\..\..\..\..\..\components\libraries\memobj\nrf_memobj.c 0x00000000   Number         0  nrf_memobj.o ABSOLUTE
    ..\..\..\..\..\..\components\libraries\pwr_mgmt\nrf_pwr_mgmt.c 0x00000000   Number         0  nrf_pwr_mgmt.o ABSOLUTE
    ..\..\..\..\..\..\components\libraries\ringbuf\nrf_ringbuf.c 0x00000000   Number         0  nrf_ringbuf.o ABSOLUTE
    ..\..\..\..\..\..\components\libraries\scheduler\app_scheduler.c 0x00000000   Number         0  app_scheduler.o ABSOLUTE
    ..\..\..\..\..\..\components\libraries\sortlist\nrf_sortlist.c 0x00000000   Number         0  nrf_sortlist.o ABSOLUTE
    ..\..\..\..\..\..\components\libraries\strerror\nrf_strerror.c 0x00000000   Number         0  nrf_strerror.o ABSOLUTE
    ..\..\..\..\..\..\components\libraries\timer\app_timer2.c 0x00000000   Number         0  app_timer2.o ABSOLUTE
    ..\..\..\..\..\..\components\libraries\timer\drv_rtc.c 0x00000000   Number         0  drv_rtc.o ABSOLUTE
    ..\..\..\..\..\..\components\libraries\uart\app_uart_fifo.c 0x00000000   Number         0  app_uart_fifo.o ABSOLUTE
    ..\..\..\..\..\..\components\libraries\uart\retarget.c 0x00000000   Number         0  retarget.o ABSOLUTE
    ..\..\..\..\..\..\components\libraries\util\app_error.c 0x00000000   Number         0  app_error.o ABSOLUTE
    ..\..\..\..\..\..\components\libraries\util\app_error_handler_keil.c 0x00000000   Number         0  app_error_handler_keil.o ABSOLUTE
    ..\..\..\..\..\..\components\libraries\util\app_error_weak.c 0x00000000   Number         0  app_error_weak.o ABSOLUTE
    ..\..\..\..\..\..\components\libraries\util\app_util_platform.c 0x00000000   Number         0  app_util_platform.o ABSOLUTE
    ..\..\..\..\..\..\components\libraries\util\nrf_assert.c 0x00000000   Number         0  nrf_assert.o ABSOLUTE
    ..\..\..\..\..\..\components\softdevice\common\nrf_sdh.c 0x00000000   Number         0  nrf_sdh.o ABSOLUTE
    ..\..\..\..\..\..\components\softdevice\common\nrf_sdh_ble.c 0x00000000   Number         0  nrf_sdh_ble.o ABSOLUTE
    ..\..\..\..\..\..\components\softdevice\common\nrf_sdh_soc.c 0x00000000   Number         0  nrf_sdh_soc.o ABSOLUTE
    ..\..\..\..\..\..\external\fprintf\nrf_fprintf.c 0x00000000   Number         0  nrf_fprintf.o ABSOLUTE
    ..\..\..\..\..\..\external\fprintf\nrf_fprintf_format.c 0x00000000   Number         0  nrf_fprintf_format.o ABSOLUTE
    ..\..\..\..\..\..\external\segger_rtt\SEGGER_RTT.c 0x00000000   Number         0  segger_rtt.o ABSOLUTE
    ..\..\..\..\..\..\external\segger_rtt\SEGGER_RTT_Syscalls_KEIL.c 0x00000000   Number         0  segger_rtt_syscalls_keil.o ABSOLUTE
    ..\..\..\..\..\..\external\segger_rtt\SEGGER_RTT_printf.c 0x00000000   Number         0  segger_rtt_printf.o ABSOLUTE
    ..\..\..\..\..\..\external\utf_converter\utf.c 0x00000000   Number         0  utf.o ABSOLUTE
    ..\..\..\..\..\..\integration\nrfx\legacy\nrf_drv_clock.c 0x00000000   Number         0  nrf_drv_clock.o ABSOLUTE
    ..\..\..\..\..\..\integration\nrfx\legacy\nrf_drv_ppi.c 0x00000000   Number         0  nrf_drv_ppi.o ABSOLUTE
    ..\..\..\..\..\..\integration\nrfx\legacy\nrf_drv_uart.c 0x00000000   Number         0  nrf_drv_uart.o ABSOLUTE
    ..\..\..\..\..\..\modules\nrfx\drivers\src\nrfx_clock.c 0x00000000   Number         0  nrfx_clock.o ABSOLUTE
    ..\..\..\..\..\..\modules\nrfx\drivers\src\nrfx_gpiote.c 0x00000000   Number         0  nrfx_gpiote.o ABSOLUTE
    ..\..\..\..\..\..\modules\nrfx\drivers\src\nrfx_ppi.c 0x00000000   Number         0  nrfx_ppi.o ABSOLUTE
    ..\..\..\..\..\..\modules\nrfx\drivers\src\nrfx_saadc.c 0x00000000   Number         0  nrfx_saadc.o ABSOLUTE
    ..\..\..\..\..\..\modules\nrfx\drivers\src\nrfx_timer.c 0x00000000   Number         0  nrfx_timer.o ABSOLUTE
    ..\..\..\..\..\..\modules\nrfx\drivers\src\nrfx_uart.c 0x00000000   Number         0  nrfx_uart.o ABSOLUTE
    ..\..\..\..\..\..\modules\nrfx\drivers\src\nrfx_uarte.c 0x00000000   Number         0  nrfx_uarte.o ABSOLUTE
    ..\..\..\..\..\..\modules\nrfx\drivers\src\prs\nrfx_prs.c 0x00000000   Number         0  nrfx_prs.o ABSOLUTE
    ..\..\..\..\..\..\modules\nrfx\soc\nrfx_atomic.c 0x00000000   Number         0  nrfx_atomic.o ABSOLUTE
    ..\..\..\main.c                          0x00000000   Number         0  main.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\ble\\ble_advertising\\ble_advertising.c 0x00000000   Number         0  ble_advertising.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\ble\\ble_link_ctx_manager\\ble_link_ctx_manager.c 0x00000000   Number         0  ble_link_ctx_manager.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\ble\\ble_services\\ble_nus\\ble_nus.c 0x00000000   Number         0  ble_nus.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\ble\\common\\ble_advdata.c 0x00000000   Number         0  ble_advdata.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\ble\\common\\ble_conn_params.c 0x00000000   Number         0  ble_conn_params.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\ble\\common\\ble_conn_state.c 0x00000000   Number         0  ble_conn_state.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\ble\\common\\ble_srv_common.c 0x00000000   Number         0  ble_srv_common.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\ble\\nrf_ble_gatt\\nrf_ble_gatt.c 0x00000000   Number         0  nrf_ble_gatt.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\ble\\nrf_ble_qwr\\nrf_ble_qwr.c 0x00000000   Number         0  nrf_ble_qwr.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\boards\\boards.c 0x00000000   Number         0  boards.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\libraries\\atomic\\nrf_atomic.c 0x00000000   Number         0  nrf_atomic.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\libraries\\atomic_fifo\\nrf_atfifo.c 0x00000000   Number         0  nrf_atfifo.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\libraries\\atomic_flags\\nrf_atflags.c 0x00000000   Number         0  nrf_atflags.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\libraries\\balloc\\nrf_balloc.c 0x00000000   Number         0  nrf_balloc.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\libraries\\bsp\\bsp.c 0x00000000   Number         0  bsp.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\libraries\\bsp\\bsp_btn_ble.c 0x00000000   Number         0  bsp_btn_ble.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\libraries\\button\\app_button.c 0x00000000   Number         0  app_button.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\libraries\\experimental_section_vars\\nrf_section_iter.c 0x00000000   Number         0  nrf_section_iter.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\libraries\\fifo\\app_fifo.c 0x00000000   Number         0  app_fifo.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\libraries\\hardfault\\hardfault_implementation.c 0x00000000   Number         0  hardfault_implementation.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\libraries\\log\\src\\nrf_log_backend_rtt.c 0x00000000   Number         0  nrf_log_backend_rtt.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\libraries\\log\\src\\nrf_log_backend_serial.c 0x00000000   Number         0  nrf_log_backend_serial.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\libraries\\log\\src\\nrf_log_default_backends.c 0x00000000   Number         0  nrf_log_default_backends.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\libraries\\log\\src\\nrf_log_frontend.c 0x00000000   Number         0  nrf_log_frontend.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\libraries\\log\\src\\nrf_log_str_formatter.c 0x00000000   Number         0  nrf_log_str_formatter.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\libraries\\memobj\\nrf_memobj.c 0x00000000   Number         0  nrf_memobj.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\libraries\\pwr_mgmt\\nrf_pwr_mgmt.c 0x00000000   Number         0  nrf_pwr_mgmt.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\libraries\\ringbuf\\nrf_ringbuf.c 0x00000000   Number         0  nrf_ringbuf.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\libraries\\scheduler\\app_scheduler.c 0x00000000   Number         0  app_scheduler.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\libraries\\sortlist\\nrf_sortlist.c 0x00000000   Number         0  nrf_sortlist.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\libraries\\strerror\\nrf_strerror.c 0x00000000   Number         0  nrf_strerror.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\libraries\\timer\\app_timer2.c 0x00000000   Number         0  app_timer2.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\libraries\\timer\\drv_rtc.c 0x00000000   Number         0  drv_rtc.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\libraries\\uart\\app_uart_fifo.c 0x00000000   Number         0  app_uart_fifo.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\libraries\\uart\\retarget.c 0x00000000   Number         0  retarget.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\libraries\\util\\app_error.c 0x00000000   Number         0  app_error.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\libraries\\util\\app_error_handler_keil.c 0x00000000   Number         0  app_error_handler_keil.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\libraries\\util\\app_error_weak.c 0x00000000   Number         0  app_error_weak.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\libraries\\util\\app_util_platform.c 0x00000000   Number         0  app_util_platform.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\libraries\\util\\nrf_assert.c 0x00000000   Number         0  nrf_assert.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\softdevice\\common\\nrf_sdh.c 0x00000000   Number         0  nrf_sdh.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\softdevice\\common\\nrf_sdh_ble.c 0x00000000   Number         0  nrf_sdh_ble.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\softdevice\\common\\nrf_sdh_soc.c 0x00000000   Number         0  nrf_sdh_soc.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\external\\fprintf\\nrf_fprintf.c 0x00000000   Number         0  nrf_fprintf.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\external\\fprintf\\nrf_fprintf_format.c 0x00000000   Number         0  nrf_fprintf_format.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\external\\segger_rtt\\SEGGER_RTT.c 0x00000000   Number         0  segger_rtt.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\external\\segger_rtt\\SEGGER_RTT_printf.c 0x00000000   Number         0  segger_rtt_printf.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\integration\\nrfx\\legacy\\nrf_drv_clock.c 0x00000000   Number         0  nrf_drv_clock.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\integration\\nrfx\\legacy\\nrf_drv_ppi.c 0x00000000   Number         0  nrf_drv_ppi.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\integration\\nrfx\\legacy\\nrf_drv_uart.c 0x00000000   Number         0  nrf_drv_uart.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\modules\\nrfx\\drivers\\src\\nrfx_clock.c 0x00000000   Number         0  nrfx_clock.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\modules\\nrfx\\drivers\\src\\nrfx_gpiote.c 0x00000000   Number         0  nrfx_gpiote.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\modules\\nrfx\\drivers\\src\\nrfx_ppi.c 0x00000000   Number         0  nrfx_ppi.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\modules\\nrfx\\drivers\\src\\nrfx_saadc.c 0x00000000   Number         0  nrfx_saadc.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\modules\\nrfx\\drivers\\src\\nrfx_timer.c 0x00000000   Number         0  nrfx_timer.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\modules\\nrfx\\drivers\\src\\nrfx_uart.c 0x00000000   Number         0  nrfx_uart.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\modules\\nrfx\\drivers\\src\\nrfx_uarte.c 0x00000000   Number         0  nrfx_uarte.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\modules\\nrfx\\drivers\\src\\prs\\nrfx_prs.c 0x00000000   Number         0  nrfx_prs.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\modules\\nrfx\\soc\\nrfx_atomic.c 0x00000000   Number         0  nrfx_atomic.o ABSOLUTE
    ..\\..\\..\\main.c                       0x00000000   Number         0  main.o ABSOLUTE
    RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s 0x00000000   Number         0  arm_startup_nrf52.o ABSOLUTE
    RTE\Device\nRF52832_xxAA\system_nrf52.c  0x00000000   Number         0  system_nrf52.o ABSOLUTE
    RTE\\Device\\nRF52832_xxAA\\system_nrf52.c 0x00000000   Number         0  system_nrf52.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    handlers.s                               0x00000000   Number         0  handlers.o ABSOLUTE
    init.s                                   0x00000000   Number         0  init.o ABSOLUTE
    RESET                                    0x00026000   Section      512  arm_startup_nrf52.o(RESET)
    .ARM.Collect$$$$00000000                 0x00026200   Section        0  entry.o(.ARM.Collect$$$$00000000)
    .ARM.Collect$$$$00000001                 0x00026200   Section        4  entry2.o(.ARM.Collect$$$$00000001)
    .ARM.Collect$$$$00000004                 0x00026204   Section        4  entry5.o(.ARM.Collect$$$$00000004)
    .ARM.Collect$$$$00000008                 0x00026208   Section        0  entry7b.o(.ARM.Collect$$$$00000008)
    .ARM.Collect$$$$0000000A                 0x00026208   Section        0  entry8b.o(.ARM.Collect$$$$0000000A)
    .ARM.Collect$$$$0000000B                 0x00026208   Section        8  entry9a.o(.ARM.Collect$$$$0000000B)
    .ARM.Collect$$$$0000000D                 0x00026210   Section        0  entry10a.o(.ARM.Collect$$$$0000000D)
    .ARM.Collect$$$$0000000F                 0x00026210   Section        0  entry11a.o(.ARM.Collect$$$$0000000F)
    .ARM.Collect$$$$00002712                 0x00026210   Section        4  entry2.o(.ARM.Collect$$$$00002712)
    __lit__00000000                          0x00026210   Data           4  entry2.o(.ARM.Collect$$$$00002712)
    .emb_text                                0x00026214   Section      200  nrf_atfifo.o(.emb_text)
    $v0                                      0x00026214   Number         0  nrf_atfifo.o(.emb_text)
    .emb_text                                0x000262dc   Section      226  nrf_atomic.o(.emb_text)
    $v0                                      0x000262dc   Number         0  nrf_atomic.o(.emb_text)
    .text                                    0x000263c0   Section       36  arm_startup_nrf52.o(.text)
    $v0                                      0x000263c0   Number         0  arm_startup_nrf52.o(.text)
    .text                                    0x000263e4   Section        0  uldiv.o(.text)
    .text                                    0x00026446   Section        0  llshl.o(.text)
    .text                                    0x00026464   Section        0  llushr.o(.text)
    .text                                    0x00026484   Section        0  memcpya.o(.text)
    .text                                    0x000264a8   Section        0  memseta.o(.text)
    .text                                    0x000264cc   Section       36  init.o(.text)
    i.GPIOTE_IRQHandler                      0x000264f0   Section        0  nrfx_gpiote.o(i.GPIOTE_IRQHandler)
    i.POWER_CLOCK_IRQHandler                 0x00026594   Section        0  nrfx_clock.o(i.POWER_CLOCK_IRQHandler)
    i.RTC1_IRQHandler                        0x000265ec   Section        0  drv_rtc.o(i.RTC1_IRQHandler)
    i.SAADC_IRQHandler                       0x000265fc   Section        0  nrfx_saadc.o(i.SAADC_IRQHandler)
    i.SWI2_EGU2_IRQHandler                   0x00026744   Section        0  nrf_sdh.o(i.SWI2_EGU2_IRQHandler)
    i.SystemInit                             0x00026748   Section        0  system_nrf52.o(i.SystemInit)
    i.TIMER1_IRQHandler                      0x00026ae0   Section        0  nrfx_timer.o(i.TIMER1_IRQHandler)
    i.UARTE0_UART0_IRQHandler                0x00026b30   Section        0  nrfx_prs.o(i.UARTE0_UART0_IRQHandler)
    i.__scatterload_copy                     0x00026b3c   Section       14  handlers.o(i.__scatterload_copy)
    i.__scatterload_null                     0x00026b4a   Section        2  handlers.o(i.__scatterload_null)
    i.__scatterload_zeroinit                 0x00026b4c   Section       14  handlers.o(i.__scatterload_zeroinit)
    i.__sd_nvic_app_accessible_irq           0x00026b5c   Section        0  nrf_sdh.o(i.__sd_nvic_app_accessible_irq)
    __sd_nvic_app_accessible_irq             0x00026b5d   Thumb Code    32  nrf_sdh.o(i.__sd_nvic_app_accessible_irq)
    i.adv_set_data_size_max_get              0x00026b80   Section        0  ble_advertising.o(i.adv_set_data_size_max_get)
    adv_set_data_size_max_get                0x00026b81   Thumb Code    16  ble_advertising.o(i.adv_set_data_size_max_get)
    i.advertising_buttons_configure          0x00026b90   Section        0  bsp_btn_ble.o(i.advertising_buttons_configure)
    advertising_buttons_configure            0x00026b91   Thumb Code    54  bsp_btn_ble.o(i.advertising_buttons_configure)
    i.app_error_fault_handler                0x00026bc8   Section        0  app_error_weak.o(i.app_error_fault_handler)
    i.app_error_handler_bare                 0x00026bf8   Section        0  app_error.o(i.app_error_handler_bare)
    i.app_timer_cnt_get                      0x00026c10   Section        0  app_timer2.o(i.app_timer_cnt_get)
    i.app_timer_create                       0x00026c1c   Section        0  app_timer2.o(i.app_timer_create)
    i.app_timer_init                         0x00026c34   Section        0  app_timer2.o(i.app_timer_init)
    i.app_timer_start                        0x00026c90   Section        0  app_timer2.o(i.app_timer_start)
    i.app_timer_stop                         0x00026cc0   Section        0  app_timer2.o(i.app_timer_stop)
    i.app_util_critical_region_enter         0x00026ccc   Section        0  app_util_platform.o(i.app_util_critical_region_enter)
    i.app_util_critical_region_exit          0x00026d14   Section        0  app_util_platform.o(i.app_util_critical_region_exit)
    i.are_app_channels                       0x00026d48   Section        0  nrfx_ppi.o(i.are_app_channels)
    are_app_channels                         0x00026d49   Thumb Code    12  nrfx_ppi.o(i.are_app_channels)
    i.blcm_link_ctx_get                      0x00026d54   Section        0  ble_link_ctx_manager.o(i.blcm_link_ctx_get)
    i.ble_advdata_encode                     0x00026d9c   Section        0  ble_advdata.o(i.ble_advdata_encode)
    i.ble_advdata_parse                      0x00026f1e   Section        0  ble_advdata.o(i.ble_advdata_parse)
    i.ble_advdata_search                     0x00026f3e   Section        0  ble_advdata.o(i.ble_advdata_search)
    i.ble_advertising_conn_cfg_tag_set       0x00026f84   Section        0  ble_advertising.o(i.ble_advertising_conn_cfg_tag_set)
    i.ble_advertising_init                   0x00026f8a   Section        0  ble_advertising.o(i.ble_advertising_init)
    i.ble_advertising_on_ble_evt             0x00027082   Section        0  ble_advertising.o(i.ble_advertising_on_ble_evt)
    i.ble_advertising_start                  0x000270ec   Section        0  ble_advertising.o(i.ble_advertising_start)
    i.ble_conn_params_init                   0x0002731c   Section        0  ble_conn_params.o(i.ble_conn_params_init)
    i.ble_conn_state_conn_idx                0x00027384   Section        0  ble_conn_state.o(i.ble_conn_state_conn_idx)
    i.ble_conn_state_valid                   0x00027398   Section        0  ble_conn_state.o(i.ble_conn_state_valid)
    i.ble_device_addr_encode                 0x000273ac   Section        0  ble_advdata.o(i.ble_device_addr_encode)
    ble_device_addr_encode                   0x000273ad   Thumb Code    96  ble_advdata.o(i.ble_device_addr_encode)
    i.ble_evt_handler                        0x0002740c   Section        0  main.o(i.ble_evt_handler)
    ble_evt_handler                          0x0002740d   Thumb Code   140  main.o(i.ble_evt_handler)
    i.ble_evt_handler                        0x000274a4   Section        0  bsp_btn_ble.o(i.ble_evt_handler)
    ble_evt_handler                          0x000274a5   Thumb Code   106  bsp_btn_ble.o(i.ble_evt_handler)
    i.ble_evt_handler                        0x00027514   Section        0  ble_conn_params.o(i.ble_evt_handler)
    ble_evt_handler                          0x00027515   Thumb Code   242  ble_conn_params.o(i.ble_evt_handler)
    i.ble_evt_handler                        0x00027610   Section        0  ble_conn_state.o(i.ble_evt_handler)
    ble_evt_handler                          0x00027611   Thumb Code   274  ble_conn_state.o(i.ble_evt_handler)
    i.ble_nus_data_send                      0x00027728   Section        0  ble_nus.o(i.ble_nus_data_send)
    i.ble_nus_init                           0x00027790   Section        0  ble_nus.o(i.ble_nus_init)
    i.ble_nus_on_ble_evt                     0x00027864   Section        0  ble_nus.o(i.ble_nus_on_ble_evt)
    i.ble_srv_is_notification_enabled        0x000278da   Section        0  ble_srv_common.o(i.ble_srv_is_notification_enabled)
    i.bsp_board_button_idx_to_pin            0x000278e4   Section        0  boards.o(i.bsp_board_button_idx_to_pin)
    i.bsp_board_led_invert                   0x000278f0   Section        0  boards.o(i.bsp_board_led_invert)
    i.bsp_board_led_off                      0x00027914   Section        0  boards.o(i.bsp_board_led_off)
    i.bsp_board_led_on                       0x00027924   Section        0  boards.o(i.bsp_board_led_on)
    i.bsp_board_led_state_get                0x00027934   Section        0  boards.o(i.bsp_board_led_state_get)
    i.bsp_board_leds_off                     0x00027954   Section        0  boards.o(i.bsp_board_leds_off)
    i.bsp_board_leds_on                      0x00027966   Section        0  boards.o(i.bsp_board_leds_on)
    i.bsp_board_pin_to_button_idx            0x00027978   Section        0  boards.o(i.bsp_board_pin_to_button_idx)
    i.bsp_btn_ble_sleep_mode_prepare         0x0002799c   Section        0  bsp_btn_ble.o(i.bsp_btn_ble_sleep_mode_prepare)
    i.bsp_button_event_handler               0x000279bc   Section        0  bsp.o(i.bsp_button_event_handler)
    bsp_button_event_handler                 0x000279bd   Thumb Code   132  bsp.o(i.bsp_button_event_handler)
    i.bsp_event_to_button_action_assign      0x00027a4c   Section        0  bsp.o(i.bsp_event_to_button_action_assign)
    i.bsp_indication_set                     0x00027a90   Section        0  bsp.o(i.bsp_indication_set)
    i.bsp_led_indication                     0x00027aa8   Section        0  bsp.o(i.bsp_led_indication)
    bsp_led_indication                       0x00027aa9   Thumb Code   446  bsp.o(i.bsp_led_indication)
    i.bsp_wakeup_button_enable               0x00027c70   Section        0  bsp.o(i.bsp_wakeup_button_enable)
    i.channel_port_get                       0x00027c78   Section        0  nrfx_gpiote.o(i.channel_port_get)
    channel_port_get                         0x00027c79   Thumb Code    10  nrfx_gpiote.o(i.channel_port_get)
    i.characteristic_add                     0x00027c88   Section        0  ble_srv_common.o(i.characteristic_add)
    i.clock_clk_started_notify               0x00027e18   Section        0  nrf_drv_clock.o(i.clock_clk_started_notify)
    clock_clk_started_notify                 0x00027e19   Thumb Code    34  nrf_drv_clock.o(i.clock_clk_started_notify)
    i.clock_irq_handler                      0x00027e40   Section        0  nrf_drv_clock.o(i.clock_irq_handler)
    clock_irq_handler                        0x00027e41   Thumb Code    24  nrf_drv_clock.o(i.clock_irq_handler)
    i.compare_func                           0x00027e5c   Section        0  app_timer2.o(i.compare_func)
    compare_func                             0x00027e5d   Thumb Code    24  app_timer2.o(i.compare_func)
    i.conn_handle_list_get                   0x00027e74   Section        0  ble_conn_state.o(i.conn_handle_list_get)
    i.conn_int_encode                        0x00027eb2   Section        0  ble_advdata.o(i.conn_int_encode)
    conn_int_encode                          0x00027eb3   Thumb Code   136  ble_advdata.o(i.conn_int_encode)
    i.conn_params_error_handler              0x00027f3a   Section        0  main.o(i.conn_params_error_handler)
    conn_params_error_handler                0x00027f3b   Thumb Code     4  main.o(i.conn_params_error_handler)
    i.conn_params_negotiation                0x00027f40   Section        0  ble_conn_params.o(i.conn_params_negotiation)
    conn_params_negotiation                  0x00027f41   Thumb Code    68  ble_conn_params.o(i.conn_params_negotiation)
    i.data_length_update                     0x00027f88   Section        0  nrf_ble_gatt.o(i.data_length_update)
    data_length_update                       0x00027f89   Thumb Code    30  nrf_ble_gatt.o(i.data_length_update)
    i.drv_rtc_compare_disable                0x00027fa6   Section        0  drv_rtc.o(i.drv_rtc_compare_disable)
    i.drv_rtc_compare_pending                0x00027fba   Section        0  drv_rtc.o(i.drv_rtc_compare_pending)
    i.drv_rtc_compare_set                    0x00027fc8   Section        0  drv_rtc.o(i.drv_rtc_compare_set)
    i.drv_rtc_counter_get                    0x00028018   Section        0  drv_rtc.o(i.drv_rtc_counter_get)
    i.drv_rtc_init                           0x00028020   Section        0  drv_rtc.o(i.drv_rtc_init)
    i.drv_rtc_irq_trigger                    0x0002809c   Section        0  drv_rtc.o(i.drv_rtc_irq_trigger)
    i.drv_rtc_overflow_enable                0x000280ba   Section        0  drv_rtc.o(i.drv_rtc_overflow_enable)
    i.drv_rtc_overflow_pending               0x000280c2   Section        0  drv_rtc.o(i.drv_rtc_overflow_pending)
    i.drv_rtc_start                          0x000280ca   Section        0  drv_rtc.o(i.drv_rtc_start)
    i.drv_rtc_stop                           0x000280d2   Section        0  drv_rtc.o(i.drv_rtc_stop)
    i.drv_rtc_windowed_compare_set           0x000280da   Section        0  drv_rtc.o(i.drv_rtc_windowed_compare_set)
    i.evt_enable                             0x000281b8   Section        0  drv_rtc.o(i.evt_enable)
    evt_enable                               0x000281b9   Thumb Code    18  drv_rtc.o(i.evt_enable)
    i.evt_pending                            0x000281ca   Section        0  drv_rtc.o(i.evt_pending)
    evt_pending                              0x000281cb   Thumb Code    20  drv_rtc.o(i.evt_pending)
    i.flag_toggle                            0x000281de   Section        0  ble_conn_state.o(i.flag_toggle)
    flag_toggle                              0x000281df   Thumb Code    10  ble_conn_state.o(i.flag_toggle)
    i.flags_set                              0x000281e8   Section        0  ble_advertising.o(i.flags_set)
    flags_set                                0x000281e9   Thumb Code    40  ble_advertising.o(i.flags_set)
    i.gatt_evt_handler                       0x00028210   Section        0  main.o(i.gatt_evt_handler)
    i.gatt_init                              0x00028230   Section        0  main.o(i.gatt_init)
    i.get_now                                0x00028260   Section        0  app_timer2.o(i.get_now)
    get_now                                  0x00028261   Thumb Code    46  app_timer2.o(i.get_now)
    i.instance_get                           0x00028294   Section        0  ble_conn_params.o(i.instance_get)
    instance_get                             0x00028295   Thumb Code    14  ble_conn_params.o(i.instance_get)
    i.is_allocated_channel                   0x000282a8   Section        0  nrfx_ppi.o(i.is_allocated_channel)
    is_allocated_channel                     0x000282a9   Thumb Code    20  nrfx_ppi.o(i.is_allocated_channel)
    i.is_app_channel                         0x000282c0   Section        0  nrfx_ppi.o(i.is_app_channel)
    is_app_channel                           0x000282c1   Thumb Code    10  nrfx_ppi.o(i.is_app_channel)
    i.is_conn_params_ok                      0x000282ca   Section        0  ble_conn_params.o(i.is_conn_params_ok)
    is_conn_params_ok                        0x000282cb   Thumb Code    68  ble_conn_params.o(i.is_conn_params_ok)
    i.is_programmable_app_channel            0x0002830e   Section        0  nrfx_ppi.o(i.is_programmable_app_channel)
    is_programmable_app_channel              0x0002830f   Thumb Code    12  nrfx_ppi.o(i.is_programmable_app_channel)
    i.leds_off                               0x0002831c   Section        0  bsp.o(i.leds_off)
    leds_off                                 0x0002831d   Thumb Code    42  bsp.o(i.leds_off)
    i.link_init                              0x0002834c   Section        0  nrf_ble_gatt.o(i.link_init)
    link_init                                0x0002834d   Thumb Code    24  nrf_ble_gatt.o(i.link_init)
    i.main                                   0x00028364   Section        0  main.o(i.main)
    i.manuf_specific_data_encode             0x00028518   Section        0  ble_advdata.o(i.manuf_specific_data_encode)
    manuf_specific_data_encode               0x00028519   Thumb Code   100  ble_advdata.o(i.manuf_specific_data_encode)
    i.name_encode                            0x0002857c   Section        0  ble_advdata.o(i.name_encode)
    name_encode                              0x0002857d   Thumb Code   166  ble_advdata.o(i.name_encode)
    i.nrf_atfifo_init                        0x00028622   Section        0  nrf_atfifo.o(i.nrf_atfifo_init)
    i.nrf_atfifo_item_alloc                  0x00028648   Section        0  nrf_atfifo.o(i.nrf_atfifo_item_alloc)
    i.nrf_atfifo_item_free                   0x0002865e   Section        0  nrf_atfifo.o(i.nrf_atfifo_item_free)
    i.nrf_atfifo_item_get                    0x00028674   Section        0  nrf_atfifo.o(i.nrf_atfifo_item_get)
    i.nrf_atfifo_item_put                    0x0002868a   Section        0  nrf_atfifo.o(i.nrf_atfifo_item_put)
    i.nrf_atflags_clear                      0x000286a0   Section        0  nrf_atflags.o(i.nrf_atflags_clear)
    i.nrf_atflags_get                        0x000286b6   Section        0  nrf_atflags.o(i.nrf_atflags_get)
    i.nrf_atflags_set                        0x000286cc   Section        0  nrf_atflags.o(i.nrf_atflags_set)
    i.nrf_atomic_u32_and                     0x000286e0   Section        0  nrf_atomic.o(i.nrf_atomic_u32_and)
    i.nrf_atomic_u32_or                      0x000286ec   Section        0  nrf_atomic.o(i.nrf_atomic_u32_or)
    i.nrf_bitmask_bit_is_set                 0x000286f8   Section        0  nrfx_gpiote.o(i.nrf_bitmask_bit_is_set)
    nrf_bitmask_bit_is_set                   0x000286f9   Thumb Code    16  nrfx_gpiote.o(i.nrf_bitmask_bit_is_set)
    i.nrf_ble_gatt_att_mtu_periph_set        0x00028708   Section        0  nrf_ble_gatt.o(i.nrf_ble_gatt_att_mtu_periph_set)
    i.nrf_ble_gatt_init                      0x00028720   Section        0  nrf_ble_gatt.o(i.nrf_ble_gatt_init)
    i.nrf_ble_gatt_on_ble_evt                0x00028744   Section        0  nrf_ble_gatt.o(i.nrf_ble_gatt_on_ble_evt)
    i.nrf_ble_qwr_conn_handle_assign         0x000288b8   Section        0  nrf_ble_qwr.o(i.nrf_ble_qwr_conn_handle_assign)
    i.nrf_ble_qwr_init                       0x000288ce   Section        0  nrf_ble_qwr.o(i.nrf_ble_qwr_init)
    i.nrf_ble_qwr_on_ble_evt                 0x000288f2   Section        0  nrf_ble_qwr.o(i.nrf_ble_qwr_on_ble_evt)
    i.nrf_clock_event_check                  0x000289a8   Section        0  nrfx_clock.o(i.nrf_clock_event_check)
    nrf_clock_event_check                    0x000289a9   Thumb Code    14  nrfx_clock.o(i.nrf_clock_event_check)
    i.nrf_clock_event_clear                  0x000289b6   Section        0  nrfx_clock.o(i.nrf_clock_event_clear)
    nrf_clock_event_clear                    0x000289b7   Thumb Code    16  nrfx_clock.o(i.nrf_clock_event_clear)
    i.nrf_drv_clock_init                     0x000289c8   Section        0  nrf_drv_clock.o(i.nrf_drv_clock_init)
    i.nrf_drv_clock_lfclk_release            0x00028a08   Section        0  nrf_drv_clock.o(i.nrf_drv_clock_lfclk_release)
    i.nrf_drv_ppi_init                       0x00028a3c   Section        0  nrf_drv_ppi.o(i.nrf_drv_ppi_init)
    i.nrf_gpio_cfg_sense_set                 0x00028a54   Section        0  nrfx_gpiote.o(i.nrf_gpio_cfg_sense_set)
    nrf_gpio_cfg_sense_set                   0x00028a55   Thumb Code    34  nrfx_gpiote.o(i.nrf_gpio_cfg_sense_set)
    i.nrf_gpio_latches_read_and_clear        0x00028a78   Section        0  nrfx_gpiote.o(i.nrf_gpio_latches_read_and_clear)
    nrf_gpio_latches_read_and_clear          0x00028a79   Thumb Code    38  nrfx_gpiote.o(i.nrf_gpio_latches_read_and_clear)
    i.nrf_gpio_pin_write                     0x00028aa4   Section        0  boards.o(i.nrf_gpio_pin_write)
    nrf_gpio_pin_write                       0x00028aa5   Thumb Code    22  boards.o(i.nrf_gpio_pin_write)
    i.nrf_gpiote_event_clear                 0x00028abc   Section        0  nrfx_gpiote.o(i.nrf_gpiote_event_clear)
    nrf_gpiote_event_clear                   0x00028abd   Thumb Code    16  nrfx_gpiote.o(i.nrf_gpiote_event_clear)
    i.nrf_gpiote_event_is_set                0x00028ad0   Section        0  nrfx_gpiote.o(i.nrf_gpiote_event_is_set)
    nrf_gpiote_event_is_set                  0x00028ad1   Thumb Code    12  nrfx_gpiote.o(i.nrf_gpiote_event_is_set)
    i.nrf_pwr_mgmt_init                      0x00028ae0   Section        0  nrf_pwr_mgmt.o(i.nrf_pwr_mgmt_init)
    i.nrf_pwr_mgmt_run                       0x00028b08   Section        0  nrf_pwr_mgmt.o(i.nrf_pwr_mgmt_run)
    i.nrf_qwr_error_handler                  0x00028b4c   Section        0  main.o(i.nrf_qwr_error_handler)
    nrf_qwr_error_handler                    0x00028b4d   Thumb Code     4  main.o(i.nrf_qwr_error_handler)
    i.nrf_rtc_event_clear                    0x00028b50   Section        0  drv_rtc.o(i.nrf_rtc_event_clear)
    nrf_rtc_event_clear                      0x00028b51   Thumb Code    12  drv_rtc.o(i.nrf_rtc_event_clear)
    i.nrf_saadc_buffer_init                  0x00028b5c   Section        0  nrfx_saadc.o(i.nrf_saadc_buffer_init)
    nrf_saadc_buffer_init                    0x00028b5d   Thumb Code    10  nrfx_saadc.o(i.nrf_saadc_buffer_init)
    i.nrf_saadc_channel_input_set            0x00028b6c   Section        0  nrfx_saadc.o(i.nrf_saadc_channel_input_set)
    nrf_saadc_channel_input_set              0x00028b6d   Thumb Code    16  nrfx_saadc.o(i.nrf_saadc_channel_input_set)
    i.nrf_saadc_event_check                  0x00028b80   Section        0  nrfx_saadc.o(i.nrf_saadc_event_check)
    nrf_saadc_event_check                    0x00028b81   Thumb Code    12  nrfx_saadc.o(i.nrf_saadc_event_check)
    i.nrf_saadc_event_clear                  0x00028b90   Section        0  nrfx_saadc.o(i.nrf_saadc_event_clear)
    nrf_saadc_event_clear                    0x00028b91   Thumb Code    16  nrfx_saadc.o(i.nrf_saadc_event_clear)
    i.nrf_sdh_ble_app_ram_start_get          0x00028ba4   Section        0  nrf_sdh_ble.o(i.nrf_sdh_ble_app_ram_start_get)
    i.nrf_sdh_ble_default_cfg_set            0x00028bb8   Section        0  nrf_sdh_ble.o(i.nrf_sdh_ble_default_cfg_set)
    i.nrf_sdh_ble_enable                     0x00028c54   Section        0  nrf_sdh_ble.o(i.nrf_sdh_ble_enable)
    i.nrf_sdh_ble_evts_poll                  0x00028c68   Section        0  nrf_sdh_ble.o(i.nrf_sdh_ble_evts_poll)
    nrf_sdh_ble_evts_poll                    0x00028c69   Thumb Code    88  nrf_sdh_ble.o(i.nrf_sdh_ble_evts_poll)
    i.nrf_sdh_enable_request                 0x00028cc8   Section        0  nrf_sdh.o(i.nrf_sdh_enable_request)
    i.nrf_sdh_evts_poll                      0x00028d3c   Section        0  nrf_sdh.o(i.nrf_sdh_evts_poll)
    i.nrf_sdh_is_enabled                     0x00028d60   Section        0  nrf_sdh.o(i.nrf_sdh_is_enabled)
    i.nrf_sdh_soc_evts_poll                  0x00028d6c   Section        0  nrf_sdh_soc.o(i.nrf_sdh_soc_evts_poll)
    nrf_sdh_soc_evts_poll                    0x00028d6d   Thumb Code    56  nrf_sdh_soc.o(i.nrf_sdh_soc_evts_poll)
    i.nrf_section_iter_init                  0x00028da8   Section        0  nrf_section_iter.o(i.nrf_section_iter_init)
    i.nrf_section_iter_item_set              0x00028db2   Section        0  nrf_section_iter.o(i.nrf_section_iter_item_set)
    nrf_section_iter_item_set                0x00028db3   Thumb Code    36  nrf_section_iter.o(i.nrf_section_iter_item_set)
    i.nrf_section_iter_next                  0x00028dd6   Section        0  nrf_section_iter.o(i.nrf_section_iter_next)
    i.nrf_sortlist_add                       0x00028df6   Section        0  nrf_sortlist.o(i.nrf_sortlist_add)
    i.nrf_sortlist_peek                      0x00028e18   Section        0  nrf_sortlist.o(i.nrf_sortlist_peek)
    i.nrf_sortlist_pop                       0x00028e1e   Section        0  nrf_sortlist.o(i.nrf_sortlist_pop)
    i.nrf_sortlist_remove                    0x00028e2c   Section        0  nrf_sortlist.o(i.nrf_sortlist_remove)
    i.nrf_timer_event_clear                  0x00028e4a   Section        0  nrfx_timer.o(i.nrf_timer_event_clear)
    nrf_timer_event_clear                    0x00028e4b   Thumb Code    12  nrfx_timer.o(i.nrf_timer_event_clear)
    i.nrf_wdt_started                        0x00028e58   Section        0  nrf_drv_clock.o(i.nrf_wdt_started)
    nrf_wdt_started                          0x00028e59   Thumb Code    12  nrf_drv_clock.o(i.nrf_wdt_started)
    i.nrfx_clock_enable                      0x00028e68   Section        0  nrfx_clock.o(i.nrfx_clock_enable)
    i.nrfx_clock_init                        0x00028e94   Section        0  nrfx_clock.o(i.nrfx_clock_init)
    i.nrfx_clock_lfclk_stop                  0x00028eb4   Section        0  nrfx_clock.o(i.nrfx_clock_lfclk_stop)
    i.nrfx_coredep_delay_us                  0x00028ee4   Section        0  drv_rtc.o(i.nrfx_coredep_delay_us)
    nrfx_coredep_delay_us                    0x00028ee5   Thumb Code    12  drv_rtc.o(i.nrfx_coredep_delay_us)
    i.nrfx_ppi_channel_alloc                 0x00028ef4   Section        0  nrfx_ppi.o(i.nrfx_ppi_channel_alloc)
    i.nrfx_ppi_channel_assign                0x00028f60   Section        0  nrfx_ppi.o(i.nrfx_ppi_channel_assign)
    i.nrfx_ppi_channel_enable                0x00028fa4   Section        0  nrfx_ppi.o(i.nrfx_ppi_channel_enable)
    i.nrfx_saadc_buffer_convert              0x00028fd8   Section        0  nrfx_saadc.o(i.nrfx_saadc_buffer_convert)
    i.nrfx_saadc_channel_init                0x0002907c   Section        0  nrfx_saadc.o(i.nrfx_saadc_channel_init)
    i.nrfx_saadc_init                        0x00029128   Section        0  nrfx_saadc.o(i.nrfx_saadc_init)
    i.nrfx_saadc_sample_task_get             0x000291bc   Section        0  nrfx_saadc.o(i.nrfx_saadc_sample_task_get)
    i.nrfx_timer_compare                     0x000291d8   Section        0  nrfx_timer.o(i.nrfx_timer_compare)
    i.nrfx_timer_enable                      0x00029214   Section        0  nrfx_timer.o(i.nrfx_timer_enable)
    i.nrfx_timer_extended_compare            0x00029230   Section        0  nrfx_timer.o(i.nrfx_timer_extended_compare)
    i.nrfx_timer_init                        0x0002925c   Section        0  nrfx_timer.o(i.nrfx_timer_init)
    i.nus_data_handler                       0x0002931c   Section        0  main.o(i.nus_data_handler)
    nus_data_handler                         0x0002931d   Thumb Code     2  main.o(i.nus_data_handler)
    i.on_adv_evt                             0x0002931e   Section        0  main.o(i.on_adv_evt)
    on_adv_evt                               0x0002931f   Thumb Code    36  main.o(i.on_adv_evt)
    i.on_conn_params_evt                     0x00029344   Section        0  main.o(i.on_conn_params_evt)
    on_conn_params_evt                       0x00029345   Thumb Code    24  main.o(i.on_conn_params_evt)
    i.on_connect                             0x00029360   Section        0  ble_nus.o(i.on_connect)
    on_connect                               0x00029361   Thumb Code   106  ble_nus.o(i.on_connect)
    i.on_write                               0x000293ca   Section        0  ble_nus.o(i.on_write)
    on_write                                 0x000293cb   Thumb Code   132  ble_nus.o(i.on_write)
    i.phy_is_valid                           0x0002944e   Section        0  ble_advertising.o(i.phy_is_valid)
    phy_is_valid                             0x0002944f   Thumb Code    18  ble_advertising.o(i.phy_is_valid)
    i.port_event_handle                      0x00029460   Section        0  nrfx_gpiote.o(i.port_event_handle)
    port_event_handle                        0x00029461   Thumb Code   176  nrfx_gpiote.o(i.port_event_handle)
    i.port_handler_polarity_get              0x00029514   Section        0  nrfx_gpiote.o(i.port_handler_polarity_get)
    port_handler_polarity_get                0x00029515   Thumb Code    12  nrfx_gpiote.o(i.port_handler_polarity_get)
    i.rtc_irq                                0x00029524   Section        0  app_timer2.o(i.rtc_irq)
    rtc_irq                                  0x00029525   Thumb Code    88  app_timer2.o(i.rtc_irq)
    i.rtc_schedule                           0x00029580   Section        0  app_timer2.o(i.rtc_schedule)
    rtc_schedule                             0x00029581   Thumb Code    92  app_timer2.o(i.rtc_schedule)
    i.rtc_update                             0x000295e4   Section        0  app_timer2.o(i.rtc_update)
    rtc_update                               0x000295e5   Thumb Code   108  app_timer2.o(i.rtc_update)
    i.saadc_callback                         0x00029658   Section        0  main.o(i.saadc_callback)
    i.saadc_init                             0x00029700   Section        0  main.o(i.saadc_init)
    i.saadc_sampling_event_enable            0x00029770   Section        0  main.o(i.saadc_sampling_event_enable)
    i.saadc_sampling_event_init              0x0002978c   Section        0  main.o(i.saadc_sampling_event_init)
    i.sd_state_evt_handler                   0x00029824   Section        0  nrf_drv_clock.o(i.sd_state_evt_handler)
    sd_state_evt_handler                     0x00029825   Thumb Code    84  nrf_drv_clock.o(i.sd_state_evt_handler)
    i.sdh_request_observer_notify            0x0002987c   Section        0  nrf_sdh.o(i.sdh_request_observer_notify)
    sdh_request_observer_notify              0x0002987d   Thumb Code    44  nrf_sdh.o(i.sdh_request_observer_notify)
    i.sdh_state_observer_notify              0x000298ac   Section        0  nrf_sdh.o(i.sdh_state_observer_notify)
    sdh_state_observer_notify                0x000298ad   Thumb Code    38  nrf_sdh.o(i.sdh_state_observer_notify)
    i.send_error_evt                         0x000298d8   Section        0  ble_conn_params.o(i.send_error_evt)
    send_error_evt                           0x000298d9   Thumb Code    12  ble_conn_params.o(i.send_error_evt)
    i.service_data_encode                    0x000298e8   Section        0  ble_advdata.o(i.service_data_encode)
    service_data_encode                      0x000298e9   Thumb Code   136  ble_advdata.o(i.service_data_encode)
    i.set_security_req                       0x00029970   Section        0  ble_srv_common.o(i.set_security_req)
    set_security_req                         0x00029971   Thumb Code    48  ble_srv_common.o(i.set_security_req)
    i.sleep_mode_enter                       0x000299a0   Section        0  main.o(i.sleep_mode_enter)
    sleep_mode_enter                         0x000299a1   Thumb Code    40  main.o(i.sleep_mode_enter)
    i.soc_evt_handler                        0x000299c8   Section        0  nrf_drv_clock.o(i.soc_evt_handler)
    soc_evt_handler                          0x000299c9   Thumb Code    18  nrf_drv_clock.o(i.soc_evt_handler)
    i.softdevices_evt_irq_enable             0x000299e0   Section        0  nrf_sdh.o(i.softdevices_evt_irq_enable)
    softdevices_evt_irq_enable               0x000299e1   Thumb Code    80  nrf_sdh.o(i.softdevices_evt_irq_enable)
    i.sortlist_pop                           0x00029a38   Section        0  app_timer2.o(i.sortlist_pop)
    sortlist_pop                             0x00029a39   Thumb Code     6  app_timer2.o(i.sortlist_pop)
    i.timer_expire                           0x00029a44   Section        0  app_timer2.o(i.timer_expire)
    timer_expire                             0x00029a45   Thumb Code    80  app_timer2.o(i.timer_expire)
    i.timer_handler                          0x00029a9c   Section        0  main.o(i.timer_handler)
    i.timer_req_process                      0x00029aa0   Section        0  app_timer2.o(i.timer_req_process)
    timer_req_process                        0x00029aa1   Thumb Code   110  app_timer2.o(i.timer_req_process)
    i.timer_req_schedule                     0x00029b18   Section        0  app_timer2.o(i.timer_req_schedule)
    timer_req_schedule                       0x00029b19   Thumb Code    46  app_timer2.o(i.timer_req_schedule)
    i.uint16_encode                          0x00029b50   Section        0  ble_advdata.o(i.uint16_encode)
    uint16_encode                            0x00029b51   Thumb Code    10  ble_advdata.o(i.uint16_encode)
    i.update_timeout_handler                 0x00029b5c   Section        0  ble_conn_params.o(i.update_timeout_handler)
    update_timeout_handler                   0x00029b5d   Thumb Code    98  ble_conn_params.o(i.update_timeout_handler)
    i.use_whitelist                          0x00029bc4   Section        0  ble_advertising.o(i.use_whitelist)
    use_whitelist                            0x00029bc5   Thumb Code    24  ble_advertising.o(i.use_whitelist)
    i.user_mem_reply                         0x00029bdc   Section        0  nrf_ble_qwr.o(i.user_mem_reply)
    user_mem_reply                           0x00029bdd   Thumb Code    36  nrf_ble_qwr.o(i.user_mem_reply)
    i.uuid_list_encode                       0x00029c00   Section        0  ble_advdata.o(i.uuid_list_encode)
    uuid_list_encode                         0x00029c01   Thumb Code    48  ble_advdata.o(i.uuid_list_encode)
    i.uuid_list_sized_encode                 0x00029c30   Section        0  ble_advdata.o(i.uuid_list_sized_encode)
    uuid_list_sized_encode                   0x00029c31   Thumb Code   158  ble_advdata.o(i.uuid_list_sized_encode)
    i.wakeup_button_cfg                      0x00029cce   Section        0  bsp.o(i.wakeup_button_cfg)
    wakeup_button_cfg                        0x00029ccf   Thumb Code    58  bsp.o(i.wakeup_button_cfg)
    .constdata                               0x00029d08   Section       45  main.o(.constdata)
    default_config                           0x00029d0a   Data           4  main.o(.constdata)
    m_timer                                  0x00029d10   Data           8  main.o(.constdata)
    .constdata                               0x00029d35   Section        8  boards.o(.constdata)
    m_board_led_list                         0x00029d35   Data           4  boards.o(.constdata)
    m_board_btn_list                         0x00029d39   Data           4  boards.o(.constdata)
    .constdata                               0x00029d40   Section       44  bsp.o(.constdata)
    m_bsp_leds_tmr                           0x00029d40   Data           4  bsp.o(.constdata)
    m_bsp_alert_tmr                          0x00029d44   Data           4  bsp.o(.constdata)
    m_bsp_button_tmr                         0x00029d48   Data           4  bsp.o(.constdata)
    app_buttons                              0x00029d4c   Data          32  bsp.o(.constdata)
    .constdata                               0x00029d6c   Section       22  nrf_ble_gatt.o(.constdata)
    .constdata                               0x00029d84   Section       16  ble_nus.o(.constdata)
    .constdata                               0x00029d94   Section        4  nrfx_gpiote.o(.constdata)
    .constdata                               0x00029d98   Section       20  app_timer2.o(.constdata)
    m_req_fifo                               0x00029d98   Data           4  app_timer2.o(.constdata)
    m_app_timer_sortlist                     0x00029da0   Data          12  app_timer2.o(.constdata)
    .constdata                               0x00029db0   Section        6  drv_rtc.o(.constdata)
    delay_machine_code                       0x00029db0   Data           6  drv_rtc.o(.constdata)
    .constdata                               0x00029db8   Section       24  nrf_pwr_mgmt.o(.constdata)
    pwr_mgmt_data_array                      0x00029db8   Data          24  nrf_pwr_mgmt.o(.constdata)
    .constdata                               0x00029dd0   Section       12  nrf_pwr_mgmt.o(.constdata)
    pwr_mgmt_data                            0x00029dd0   Data          12  nrf_pwr_mgmt.o(.constdata)
    .constdata                               0x00029ddc   Section       16  nrf_sdh.o(.constdata)
    sdh_req_observers_array                  0x00029ddc   Data          16  nrf_sdh.o(.constdata)
    .constdata                               0x00029dec   Section       40  nrf_sdh.o(.constdata)
    sdh_req_observers                        0x00029df0   Data          12  nrf_sdh.o(.constdata)
    sdh_state_observers                      0x00029dfc   Data          12  nrf_sdh.o(.constdata)
    sdh_stack_observers                      0x00029e08   Data          12  nrf_sdh.o(.constdata)
    .constdata                               0x00029e14   Section       16  nrf_sdh.o(.constdata)
    sdh_state_observers_array                0x00029e14   Data          16  nrf_sdh.o(.constdata)
    .constdata                               0x00029e24   Section       16  nrf_sdh.o(.constdata)
    sdh_stack_observers_array                0x00029e24   Data          16  nrf_sdh.o(.constdata)
    .constdata                               0x00029e34   Section       32  nrf_sdh_ble.o(.constdata)
    sdh_ble_observers_array                  0x00029e34   Data          32  nrf_sdh_ble.o(.constdata)
    .constdata                               0x00029e54   Section       16  nrf_sdh_ble.o(.constdata)
    sdh_ble_observers                        0x00029e58   Data          12  nrf_sdh_ble.o(.constdata)
    .constdata                               0x00029e64   Section       16  nrf_sdh_soc.o(.constdata)
    sdh_soc_observers_array                  0x00029e64   Data          16  nrf_sdh_soc.o(.constdata)
    .constdata                               0x00029e74   Section       12  nrf_sdh_soc.o(.constdata)
    sdh_soc_observers                        0x00029e74   Data          12  nrf_sdh_soc.o(.constdata)
    sdh_ble_observers0                       0x00029ea0   Section        8  ble_conn_state.o(sdh_ble_observers0)
    __tagsym$$used                           0x00029ea0   Number         0  ble_conn_state.o(sdh_ble_observers0)
    m_ble_evt_observer                       0x00029ea0   Data           8  ble_conn_state.o(sdh_ble_observers0)
    sdh_ble_observers1                       0x00029ea8   Section       16  main.o(sdh_ble_observers1)
    __tagsym$$used                           0x00029ea8   Number         0  main.o(sdh_ble_observers1)
    m_gatt_obs                               0x00029ea8   Data           8  main.o(sdh_ble_observers1)
    __tagsym$$used                           0x00029eb0   Number         0  main.o(sdh_ble_observers1)
    m_advertising_ble_obs                    0x00029eb0   Data           8  main.o(sdh_ble_observers1)
    sdh_ble_observers1                       0x00029eb8   Section        8  bsp_btn_ble.o(sdh_ble_observers1)
    __tagsym$$used                           0x00029eb8   Number         0  bsp_btn_ble.o(sdh_ble_observers1)
    m_ble_observer                           0x00029eb8   Data           8  bsp_btn_ble.o(sdh_ble_observers1)
    sdh_ble_observers1                       0x00029ec0   Section        8  ble_conn_params.o(sdh_ble_observers1)
    __tagsym$$used                           0x00029ec0   Number         0  ble_conn_params.o(sdh_ble_observers1)
    m_ble_observer                           0x00029ec0   Data           8  ble_conn_params.o(sdh_ble_observers1)
    sdh_ble_observers2                       0x00029ec8   Section       16  main.o(sdh_ble_observers2)
    __tagsym$$used                           0x00029ec8   Number         0  main.o(sdh_ble_observers2)
    m_nus_obs                                0x00029ec8   Data           8  main.o(sdh_ble_observers2)
    __tagsym$$used                           0x00029ed0   Number         0  main.o(sdh_ble_observers2)
    m_qwr_obs                                0x00029ed0   Data           8  main.o(sdh_ble_observers2)
    sdh_ble_observers3                       0x00029ed8   Section        8  main.o(sdh_ble_observers3)
    __tagsym$$used                           0x00029ed8   Number         0  main.o(sdh_ble_observers3)
    m_ble_observer                           0x00029ed8   Data           8  main.o(sdh_ble_observers3)
    sdh_soc_observers0                       0x00029ee0   Section        8  nrf_drv_clock.o(sdh_soc_observers0)
    __tagsym$$used                           0x00029ee0   Number         0  nrf_drv_clock.o(sdh_soc_observers0)
    m_soc_evt_observer                       0x00029ee0   Data           8  nrf_drv_clock.o(sdh_soc_observers0)
    sdh_stack_observers0                     0x00029ee8   Section        8  nrf_sdh_ble.o(sdh_stack_observers0)
    __tagsym$$used                           0x00029ee8   Number         0  nrf_sdh_ble.o(sdh_stack_observers0)
    m_nrf_sdh_ble_evts_poll                  0x00029ee8   Data           8  nrf_sdh_ble.o(sdh_stack_observers0)
    sdh_stack_observers0                     0x00029ef0   Section        8  nrf_sdh_soc.o(sdh_stack_observers0)
    __tagsym$$used                           0x00029ef0   Number         0  nrf_sdh_soc.o(sdh_stack_observers0)
    m_nrf_sdh_soc_evts_poll                  0x00029ef0   Data           8  nrf_sdh_soc.o(sdh_stack_observers0)
    sdh_state_observers0                     0x00029ef8   Section        8  nrf_drv_clock.o(sdh_state_observers0)
    __tagsym$$used                           0x00029ef8   Number         0  nrf_drv_clock.o(sdh_state_observers0)
    m_sd_state_observer                      0x00029ef8   Data           8  nrf_drv_clock.o(sdh_state_observers0)
    .data                                    0x20002be0   Section        8  main.o(.data)
    m_nus_link_ctx_storage                   0x20002be0   Data           8  main.o(.data)
    .data                                    0x20002be8   Section       48  main.o(.data)
    m_ppi_channel                            0x20002be9   Data           1  main.o(.data)
    m_conn_handle                            0x20002bea   Data           2  main.o(.data)
    m_ble_nus_max_data_len                   0x20002bec   Data           2  main.o(.data)
    m_adv_uuids                              0x20002bf2   Data           4  main.o(.data)
    m_buffer_pool                            0x20002bf6   Data           4  main.o(.data)
    m_nus                                    0x20002bfc   Data          28  main.o(.data)
    .data                                    0x20002c18   Section        4  main.o(.data)
    m_nus_link_ctx_storage_ctx_data_pool     0x20002c18   Data           4  main.o(.data)
    .data                                    0x20002c1c   Section       16  bsp.o(.data)
    m_stable_state                           0x20002c1c   Data           1  bsp.o(.data)
    m_leds_clear                             0x20002c1d   Data           1  bsp.o(.data)
    m_alert_on                               0x20002c1e   Data           1  bsp.o(.data)
    current_long_push_pin_no                 0x20002c1f   Data           1  bsp.o(.data)
    m_indication_type                        0x20002c20   Data           4  bsp.o(.data)
    m_registered_callback                    0x20002c24   Data           4  bsp.o(.data)
    release_event_at_push                    0x20002c28   Data           4  bsp.o(.data)
    .data                                    0x20002c2c   Section        8  bsp_btn_ble.o(.data)
    m_error_handler                          0x20002c2c   Data           4  bsp_btn_ble.o(.data)
    m_num_connections                        0x20002c30   Data           4  bsp_btn_ble.o(.data)
    .data                                    0x20002c34   Section        8  ble_conn_params.o(.data)
    m_preferred_conn_params                  0x20002c34   Data           8  ble_conn_params.o(.data)
    .data                                    0x20002c3c   Section        8  nrfx_clock.o(.data)
    m_clock_cb                               0x20002c3c   Data           8  nrfx_clock.o(.data)
    .data                                    0x20002c44   Section        8  nrfx_prs.o(.data)
    m_prs_box_4                              0x20002c44   Data           8  nrfx_prs.o(.data)
    .data                                    0x20002c4c   Section        1  nrf_drv_ppi.o(.data)
    m_drv_state                              0x20002c4c   Data           1  nrf_drv_ppi.o(.data)
    .data                                    0x20002c50   Section        8  nrfx_ppi.o(.data)
    m_groups_allocated                       0x20002c50   Data           1  nrfx_ppi.o(.data)
    m_channels_allocated                     0x20002c54   Data           4  nrfx_ppi.o(.data)
    .data                                    0x20002c58   Section       32  app_timer2.o(.data)
    m_global_active                          0x20002c58   Data           1  app_timer2.o(.data)
    mp_active_timer                          0x20002c5c   Data           4  app_timer2.o(.data)
    m_rtc_inst                               0x20002c60   Data           8  app_timer2.o(.data)
    m_base_counter                           0x20002c68   Data           8  app_timer2.o(.data)
    m_stamp64                                0x20002c70   Data           8  app_timer2.o(.data)
    .data                                    0x20002c78   Section        4  app_timer2.o(.data)
    m_app_timer_sortlist_sortlist_cb         0x20002c78   Data           4  app_timer2.o(.data)
    .data                                    0x20002c7c   Section       12  drv_rtc.o(.data)
    m_handlers                               0x20002c7c   Data           4  drv_rtc.o(.data)
    m_cb                                     0x20002c80   Data           8  drv_rtc.o(.data)
    .data                                    0x20002c88   Section        8  nrf_pwr_mgmt.o(.data)
    m_pwr_mgmt_evt                           0x20002c88   Data           1  nrf_pwr_mgmt.o(.data)
    m_shutdown_started                       0x20002c89   Data           1  nrf_pwr_mgmt.o(.data)
    m_sysoff_mtx                             0x20002c8c   Data           4  nrf_pwr_mgmt.o(.data)
    .data                                    0x20002c90   Section        3  nrf_sdh.o(.data)
    m_nrf_sdh_enabled                        0x20002c90   Data           1  nrf_sdh.o(.data)
    m_nrf_sdh_suspended                      0x20002c91   Data           1  nrf_sdh.o(.data)
    m_nrf_sdh_continue                       0x20002c92   Data           1  nrf_sdh.o(.data)
    .data                                    0x20002c93   Section        1  nrf_sdh_ble.o(.data)
    m_stack_is_enabled                       0x20002c93   Data           1  nrf_sdh_ble.o(.data)
    .data                                    0x20002c94   Section        4  system_nrf52.o(.data)
    __tagsym$$used                           0x20002c94   Number         0  system_nrf52.o(.data)
    .bss                                     0x20002c98   Section     1596  main.o(.bss)
    m_gatt                                   0x20002e80   Data          20  main.o(.bss)
    m_qwr                                    0x20002e94   Data          12  main.o(.bss)
    m_advertising                            0x20002ea0   Data        1076  main.o(.bss)
    .bss                                     0x200032d8   Section       32  bsp.o(.bss)
    m_bsp_leds_tmr_data                      0x200032d8   Data          32  bsp.o(.bss)
    .bss                                     0x200032f8   Section       32  bsp.o(.bss)
    m_bsp_alert_tmr_data                     0x200032f8   Data          32  bsp.o(.bss)
    .bss                                     0x20003318   Section       12  bsp.o(.bss)
    m_events_list                            0x20003318   Data          12  bsp.o(.bss)
    .bss                                     0x20003328   Section       32  bsp.o(.bss)
    m_bsp_button_tmr_data                    0x20003328   Data          32  bsp.o(.bss)
    .bss                                     0x20003348   Section       80  ble_conn_params.o(.bss)
    m_timer_data                             0x20003348   Data          32  ble_conn_params.o(.bss)
    m_conn_params_instances                  0x20003368   Data          20  ble_conn_params.o(.bss)
    m_conn_params_config                     0x2000337c   Data          28  ble_conn_params.o(.bss)
    .bss                                     0x20003398   Section      124  ble_conn_state.o(.bss)
    m_bcs                                    0x20003398   Data         124  ble_conn_state.o(.bss)
    .bss                                     0x20003414   Section       20  nrf_drv_clock.o(.bss)
    m_clock_cb                               0x20003414   Data          20  nrf_drv_clock.o(.bss)
    .bss                                     0x20003428   Section       92  nrfx_gpiote.o(.bss)
    m_cb                                     0x20003428   Data          92  nrfx_gpiote.o(.bss)
    .bss                                     0x20003484   Section       48  nrfx_saadc.o(.bss)
    m_cb                                     0x20003484   Data          48  nrfx_saadc.o(.bss)
    .bss                                     0x200034b4   Section       12  nrfx_timer.o(.bss)
    m_cb                                     0x200034b4   Data          12  nrfx_timer.o(.bss)
    .bss                                     0x200034c0   Section       16  app_timer2.o(.bss)
    m_req_fifo_inst                          0x200034c0   Data          16  app_timer2.o(.bss)
    .bss                                     0x200034d0   Section       88  app_timer2.o(.bss)
    m_req_fifo_data                          0x200034d0   Data          88  app_timer2.o(.bss)
    .bss                                     0x20003528   Section       12  app_util_platform.o(.bss)
    .bss                                     0x20003534   Section       12  nrf_pwr_mgmt.o(.bss)
    m_handlers_iter                          0x20003534   Data          12  nrf_pwr_mgmt.o(.bss)
    STACK                                    0x20003540   Section     8192  arm_startup_nrf52.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$E$P$D$K$B$S$7EM$VFPi3$EXTD16$VFPS$VFMA$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OSPACE$EBA8$MICROLIB$REQ8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __decompress                              - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _microlib_exit                            - Undefined Weak Reference
    pwr_mgmt_data0$$Base                      - Undefined Reference
    pwr_mgmt_data0$$Limit                     - Undefined Reference
    pwr_mgmt_data1$$Base                      - Undefined Reference
    pwr_mgmt_data1$$Limit                     - Undefined Reference
    pwr_mgmt_data2$$Base                      - Undefined Reference
    pwr_mgmt_data2$$Limit                     - Undefined Reference
    sdh_req_observers0$$Base                  - Undefined Reference
    sdh_req_observers0$$Limit                 - Undefined Reference
    sdh_req_observers1$$Base                  - Undefined Reference
    sdh_req_observers1$$Limit                 - Undefined Reference
    sdh_soc_observers1$$Base                  - Undefined Reference
    sdh_soc_observers1$$Limit                 - Undefined Reference
    sdh_stack_observers1$$Base                - Undefined Reference
    sdh_stack_observers1$$Limit               - Undefined Reference
    sdh_state_observers1$$Base                - Undefined Reference
    sdh_state_observers1$$Limit               - Undefined Reference
    __Vectors_Size                           0x00000200   Number         0  arm_startup_nrf52.o ABSOLUTE
    __Vectors                                0x00026000   Data           4  arm_startup_nrf52.o(RESET)
    __Vectors_End                            0x00026200   Data           0  arm_startup_nrf52.o(RESET)
    __main                                   0x00026201   Thumb Code     0  entry.o(.ARM.Collect$$$$00000000)
    _main_stk                                0x00026201   Thumb Code     0  entry2.o(.ARM.Collect$$$$00000001)
    _main_scatterload                        0x00026205   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    __main_after_scatterload                 0x00026209   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    _main_clock                              0x00026209   Thumb Code     0  entry7b.o(.ARM.Collect$$$$00000008)
    _main_cpp_init                           0x00026209   Thumb Code     0  entry8b.o(.ARM.Collect$$$$0000000A)
    _main_init                               0x00026209   Thumb Code     0  entry9a.o(.ARM.Collect$$$$0000000B)
    __rt_final_cpp                           0x00026211   Thumb Code     0  entry10a.o(.ARM.Collect$$$$0000000D)
    __rt_final_exit                          0x00026211   Thumb Code     0  entry11a.o(.ARM.Collect$$$$0000000F)
    __asm___12_nrf_atfifo_c_51f461e1__nrf_atfifo_wspace_req 0x00026215   Thumb Code    56  nrf_atfifo.o(.emb_text)
    __asm___12_nrf_atfifo_c_51f461e1__nrf_atfifo_wspace_close 0x0002624d   Thumb Code    18  nrf_atfifo.o(.emb_text)
    __asm___12_nrf_atfifo_c_51f461e1__nrf_atfifo_rspace_req 0x0002625f   Thumb Code    58  nrf_atfifo.o(.emb_text)
    __asm___12_nrf_atfifo_c_51f461e1__nrf_atfifo_rspace_close 0x00026299   Thumb Code    18  nrf_atfifo.o(.emb_text)
    __asm___12_nrf_atfifo_c_51f461e1__nrf_atfifo_space_clear 0x000262ab   Thumb Code    50  nrf_atfifo.o(.emb_text)
    __asm___12_nrf_atomic_c_85ca2469__nrf_atomic_internal_mov 0x000262dd   Thumb Code    24  nrf_atomic.o(.emb_text)
    __asm___12_nrf_atomic_c_85ca2469__nrf_atomic_internal_orr 0x000262f5   Thumb Code    26  nrf_atomic.o(.emb_text)
    __asm___12_nrf_atomic_c_85ca2469__nrf_atomic_internal_and 0x0002630f   Thumb Code    26  nrf_atomic.o(.emb_text)
    __asm___12_nrf_atomic_c_85ca2469__nrf_atomic_internal_eor 0x00026329   Thumb Code    26  nrf_atomic.o(.emb_text)
    __asm___12_nrf_atomic_c_85ca2469__nrf_atomic_internal_add 0x00026343   Thumb Code    26  nrf_atomic.o(.emb_text)
    __asm___12_nrf_atomic_c_85ca2469__nrf_atomic_internal_sub 0x0002635d   Thumb Code    26  nrf_atomic.o(.emb_text)
    __asm___12_nrf_atomic_c_85ca2469__nrf_atomic_internal_cmp_exch 0x00026377   Thumb Code    42  nrf_atomic.o(.emb_text)
    __asm___12_nrf_atomic_c_85ca2469__nrf_atomic_internal_sub_hs 0x000263a1   Thumb Code    30  nrf_atomic.o(.emb_text)
    Reset_Handler                            0x000263c1   Thumb Code     8  arm_startup_nrf52.o(.text)
    NMI_Handler                              0x000263c9   Thumb Code     2  arm_startup_nrf52.o(.text)
    HardFault_Handler                        0x000263cb   Thumb Code     2  arm_startup_nrf52.o(.text)
    MemoryManagement_Handler                 0x000263cd   Thumb Code     2  arm_startup_nrf52.o(.text)
    BusFault_Handler                         0x000263cf   Thumb Code     2  arm_startup_nrf52.o(.text)
    UsageFault_Handler                       0x000263d1   Thumb Code     2  arm_startup_nrf52.o(.text)
    SVC_Handler                              0x000263d3   Thumb Code     2  arm_startup_nrf52.o(.text)
    DebugMon_Handler                         0x000263d5   Thumb Code     2  arm_startup_nrf52.o(.text)
    PendSV_Handler                           0x000263d7   Thumb Code     2  arm_startup_nrf52.o(.text)
    SysTick_Handler                          0x000263d9   Thumb Code     2  arm_startup_nrf52.o(.text)
    CCM_AAR_IRQHandler                       0x000263db   Thumb Code     0  arm_startup_nrf52.o(.text)
    COMP_LPCOMP_IRQHandler                   0x000263db   Thumb Code     0  arm_startup_nrf52.o(.text)
    ECB_IRQHandler                           0x000263db   Thumb Code     0  arm_startup_nrf52.o(.text)
    FPU_IRQHandler                           0x000263db   Thumb Code     0  arm_startup_nrf52.o(.text)
    I2S_IRQHandler                           0x000263db   Thumb Code     0  arm_startup_nrf52.o(.text)
    MWU_IRQHandler                           0x000263db   Thumb Code     0  arm_startup_nrf52.o(.text)
    NFCT_IRQHandler                          0x000263db   Thumb Code     0  arm_startup_nrf52.o(.text)
    PDM_IRQHandler                           0x000263db   Thumb Code     0  arm_startup_nrf52.o(.text)
    PWM0_IRQHandler                          0x000263db   Thumb Code     0  arm_startup_nrf52.o(.text)
    PWM1_IRQHandler                          0x000263db   Thumb Code     0  arm_startup_nrf52.o(.text)
    PWM2_IRQHandler                          0x000263db   Thumb Code     0  arm_startup_nrf52.o(.text)
    QDEC_IRQHandler                          0x000263db   Thumb Code     0  arm_startup_nrf52.o(.text)
    RADIO_IRQHandler                         0x000263db   Thumb Code     0  arm_startup_nrf52.o(.text)
    RNG_IRQHandler                           0x000263db   Thumb Code     0  arm_startup_nrf52.o(.text)
    RTC0_IRQHandler                          0x000263db   Thumb Code     0  arm_startup_nrf52.o(.text)
    RTC2_IRQHandler                          0x000263db   Thumb Code     0  arm_startup_nrf52.o(.text)
    SPIM0_SPIS0_TWIM0_TWIS0_SPI0_TWI0_IRQHandler 0x000263db   Thumb Code     0  arm_startup_nrf52.o(.text)
    SPIM1_SPIS1_TWIM1_TWIS1_SPI1_TWI1_IRQHandler 0x000263db   Thumb Code     0  arm_startup_nrf52.o(.text)
    SPIM2_SPIS2_SPI2_IRQHandler              0x000263db   Thumb Code     0  arm_startup_nrf52.o(.text)
    SWI0_EGU0_IRQHandler                     0x000263db   Thumb Code     0  arm_startup_nrf52.o(.text)
    SWI1_EGU1_IRQHandler                     0x000263db   Thumb Code     0  arm_startup_nrf52.o(.text)
    SWI3_EGU3_IRQHandler                     0x000263db   Thumb Code     0  arm_startup_nrf52.o(.text)
    SWI4_EGU4_IRQHandler                     0x000263db   Thumb Code     0  arm_startup_nrf52.o(.text)
    SWI5_EGU5_IRQHandler                     0x000263db   Thumb Code     0  arm_startup_nrf52.o(.text)
    TEMP_IRQHandler                          0x000263db   Thumb Code     0  arm_startup_nrf52.o(.text)
    TIMER0_IRQHandler                        0x000263db   Thumb Code     0  arm_startup_nrf52.o(.text)
    TIMER2_IRQHandler                        0x000263db   Thumb Code     0  arm_startup_nrf52.o(.text)
    TIMER3_IRQHandler                        0x000263db   Thumb Code     0  arm_startup_nrf52.o(.text)
    TIMER4_IRQHandler                        0x000263db   Thumb Code     0  arm_startup_nrf52.o(.text)
    WDT_IRQHandler                           0x000263db   Thumb Code     0  arm_startup_nrf52.o(.text)
    __aeabi_uldivmod                         0x000263e5   Thumb Code    98  uldiv.o(.text)
    __aeabi_llsl                             0x00026447   Thumb Code    30  llshl.o(.text)
    _ll_shift_l                              0x00026447   Thumb Code     0  llshl.o(.text)
    __aeabi_llsr                             0x00026465   Thumb Code    32  llushr.o(.text)
    _ll_ushift_r                             0x00026465   Thumb Code     0  llushr.o(.text)
    __aeabi_memcpy                           0x00026485   Thumb Code    36  memcpya.o(.text)
    __aeabi_memcpy4                          0x00026485   Thumb Code     0  memcpya.o(.text)
    __aeabi_memcpy8                          0x00026485   Thumb Code     0  memcpya.o(.text)
    __aeabi_memset                           0x000264a9   Thumb Code    14  memseta.o(.text)
    __aeabi_memset4                          0x000264a9   Thumb Code     0  memseta.o(.text)
    __aeabi_memset8                          0x000264a9   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr                           0x000264b7   Thumb Code     4  memseta.o(.text)
    __aeabi_memclr4                          0x000264b7   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr8                          0x000264b7   Thumb Code     0  memseta.o(.text)
    _memset$wrapper                          0x000264bb   Thumb Code    18  memseta.o(.text)
    __scatterload                            0x000264cd   Thumb Code    28  init.o(.text)
    __scatterload_rt2                        0x000264cd   Thumb Code     0  init.o(.text)
    GPIOTE_IRQHandler                        0x000264f1   Thumb Code   154  nrfx_gpiote.o(i.GPIOTE_IRQHandler)
    POWER_CLOCK_IRQHandler                   0x00026595   Thumb Code    82  nrfx_clock.o(i.POWER_CLOCK_IRQHandler)
    RTC1_IRQHandler                          0x000265ed   Thumb Code    12  drv_rtc.o(i.RTC1_IRQHandler)
    SAADC_IRQHandler                         0x000265fd   Thumb Code   320  nrfx_saadc.o(i.SAADC_IRQHandler)
    SWI2_EGU2_IRQHandler                     0x00026745   Thumb Code     4  nrf_sdh.o(i.SWI2_EGU2_IRQHandler)
    SystemInit                               0x00026749   Thumb Code   848  system_nrf52.o(i.SystemInit)
    TIMER1_IRQHandler                        0x00026ae1   Thumb Code    70  nrfx_timer.o(i.TIMER1_IRQHandler)
    UARTE0_UART0_IRQHandler                  0x00026b31   Thumb Code     6  nrfx_prs.o(i.UARTE0_UART0_IRQHandler)
    __scatterload_copy                       0x00026b3d   Thumb Code    14  handlers.o(i.__scatterload_copy)
    __scatterload_null                       0x00026b4b   Thumb Code     2  handlers.o(i.__scatterload_null)
    __scatterload_zeroinit                   0x00026b4d   Thumb Code    14  handlers.o(i.__scatterload_zeroinit)
    app_error_fault_handler                  0x00026bc9   Thumb Code    40  app_error_weak.o(i.app_error_fault_handler)
    app_error_handler_bare                   0x00026bf9   Thumb Code    22  app_error.o(i.app_error_handler_bare)
    app_timer_cnt_get                        0x00026c11   Thumb Code     6  app_timer2.o(i.app_timer_cnt_get)
    app_timer_create                         0x00026c1d   Thumb Code    22  app_timer2.o(i.app_timer_create)
    app_timer_init                           0x00026c35   Thumb Code    70  app_timer2.o(i.app_timer_init)
    app_timer_start                          0x00026c91   Thumb Code    48  app_timer2.o(i.app_timer_start)
    app_timer_stop                           0x00026cc1   Thumb Code    12  app_timer2.o(i.app_timer_stop)
    app_util_critical_region_enter           0x00026ccd   Thumb Code    64  app_util_platform.o(i.app_util_critical_region_enter)
    app_util_critical_region_exit            0x00026d15   Thumb Code    46  app_util_platform.o(i.app_util_critical_region_exit)
    blcm_link_ctx_get                        0x00026d55   Thumb Code    72  ble_link_ctx_manager.o(i.blcm_link_ctx_get)
    ble_advdata_encode                       0x00026d9d   Thumb Code   386  ble_advdata.o(i.ble_advdata_encode)
    ble_advdata_parse                        0x00026f1f   Thumb Code    32  ble_advdata.o(i.ble_advdata_parse)
    ble_advdata_search                       0x00026f3f   Thumb Code    70  ble_advdata.o(i.ble_advdata_search)
    ble_advertising_conn_cfg_tag_set         0x00026f85   Thumb Code     6  ble_advertising.o(i.ble_advertising_conn_cfg_tag_set)
    ble_advertising_init                     0x00026f8b   Thumb Code   248  ble_advertising.o(i.ble_advertising_init)
    ble_advertising_on_ble_evt               0x00027083   Thumb Code   106  ble_advertising.o(i.ble_advertising_on_ble_evt)
    ble_advertising_start                    0x000270ed   Thumb Code   560  ble_advertising.o(i.ble_advertising_start)
    ble_conn_params_init                     0x0002731d   Thumb Code    90  ble_conn_params.o(i.ble_conn_params_init)
    ble_conn_state_conn_idx                  0x00027385   Thumb Code    18  ble_conn_state.o(i.ble_conn_state_conn_idx)
    ble_conn_state_valid                     0x00027399   Thumb Code    16  ble_conn_state.o(i.ble_conn_state_valid)
    ble_nus_data_send                        0x00027729   Thumb Code   102  ble_nus.o(i.ble_nus_data_send)
    ble_nus_init                             0x00027791   Thumb Code   206  ble_nus.o(i.ble_nus_init)
    ble_nus_on_ble_evt                       0x00027865   Thumb Code   118  ble_nus.o(i.ble_nus_on_ble_evt)
    ble_srv_is_notification_enabled          0x000278db   Thumb Code     8  ble_srv_common.o(i.ble_srv_is_notification_enabled)
    bsp_board_button_idx_to_pin              0x000278e5   Thumb Code     6  boards.o(i.bsp_board_button_idx_to_pin)
    bsp_board_led_invert                     0x000278f1   Thumb Code    32  boards.o(i.bsp_board_led_invert)
    bsp_board_led_off                        0x00027915   Thumb Code    10  boards.o(i.bsp_board_led_off)
    bsp_board_led_on                         0x00027925   Thumb Code    10  boards.o(i.bsp_board_led_on)
    bsp_board_led_state_get                  0x00027935   Thumb Code    28  boards.o(i.bsp_board_led_state_get)
    bsp_board_leds_off                       0x00027955   Thumb Code    18  boards.o(i.bsp_board_leds_off)
    bsp_board_leds_on                        0x00027967   Thumb Code    18  boards.o(i.bsp_board_leds_on)
    bsp_board_pin_to_button_idx              0x00027979   Thumb Code    30  boards.o(i.bsp_board_pin_to_button_idx)
    bsp_btn_ble_sleep_mode_prepare           0x0002799d   Thumb Code    30  bsp_btn_ble.o(i.bsp_btn_ble_sleep_mode_prepare)
    bsp_event_to_button_action_assign        0x00027a4d   Thumb Code    64  bsp.o(i.bsp_event_to_button_action_assign)
    bsp_indication_set                       0x00027a91   Thumb Code    20  bsp.o(i.bsp_indication_set)
    bsp_wakeup_button_enable                 0x00027c71   Thumb Code     6  bsp.o(i.bsp_wakeup_button_enable)
    characteristic_add                       0x00027c89   Thumb Code   400  ble_srv_common.o(i.characteristic_add)
    conn_handle_list_get                     0x00027e75   Thumb Code    62  ble_conn_state.o(i.conn_handle_list_get)
    drv_rtc_compare_disable                  0x00027fa7   Thumb Code    20  drv_rtc.o(i.drv_rtc_compare_disable)
    drv_rtc_compare_pending                  0x00027fbb   Thumb Code    14  drv_rtc.o(i.drv_rtc_compare_pending)
    drv_rtc_compare_set                      0x00027fc9   Thumb Code    80  drv_rtc.o(i.drv_rtc_compare_set)
    drv_rtc_counter_get                      0x00028019   Thumb Code     8  drv_rtc.o(i.drv_rtc_counter_get)
    drv_rtc_init                             0x00028021   Thumb Code   118  drv_rtc.o(i.drv_rtc_init)
    drv_rtc_irq_trigger                      0x0002809d   Thumb Code    30  drv_rtc.o(i.drv_rtc_irq_trigger)
    drv_rtc_overflow_enable                  0x000280bb   Thumb Code     8  drv_rtc.o(i.drv_rtc_overflow_enable)
    drv_rtc_overflow_pending                 0x000280c3   Thumb Code     8  drv_rtc.o(i.drv_rtc_overflow_pending)
    drv_rtc_start                            0x000280cb   Thumb Code     8  drv_rtc.o(i.drv_rtc_start)
    drv_rtc_stop                             0x000280d3   Thumb Code     8  drv_rtc.o(i.drv_rtc_stop)
    drv_rtc_windowed_compare_set             0x000280db   Thumb Code   222  drv_rtc.o(i.drv_rtc_windowed_compare_set)
    gatt_evt_handler                         0x00028211   Thumb Code    28  main.o(i.gatt_evt_handler)
    gatt_init                                0x00028231   Thumb Code    38  main.o(i.gatt_init)
    main                                     0x00028365   Thumb Code   392  main.o(i.main)
    nrf_atfifo_init                          0x00028623   Thumb Code    38  nrf_atfifo.o(i.nrf_atfifo_init)
    nrf_atfifo_item_alloc                    0x00028649   Thumb Code    22  nrf_atfifo.o(i.nrf_atfifo_item_alloc)
    nrf_atfifo_item_free                     0x0002865f   Thumb Code    22  nrf_atfifo.o(i.nrf_atfifo_item_free)
    nrf_atfifo_item_get                      0x00028675   Thumb Code    22  nrf_atfifo.o(i.nrf_atfifo_item_get)
    nrf_atfifo_item_put                      0x0002868b   Thumb Code    22  nrf_atfifo.o(i.nrf_atfifo_item_put)
    nrf_atflags_clear                        0x000286a1   Thumb Code    22  nrf_atflags.o(i.nrf_atflags_clear)
    nrf_atflags_get                          0x000286b7   Thumb Code    22  nrf_atflags.o(i.nrf_atflags_get)
    nrf_atflags_set                          0x000286cd   Thumb Code    20  nrf_atflags.o(i.nrf_atflags_set)
    nrf_atomic_u32_and                       0x000286e1   Thumb Code    12  nrf_atomic.o(i.nrf_atomic_u32_and)
    nrf_atomic_u32_or                        0x000286ed   Thumb Code    12  nrf_atomic.o(i.nrf_atomic_u32_or)
    nrf_ble_gatt_att_mtu_periph_set          0x00028709   Thumb Code    24  nrf_ble_gatt.o(i.nrf_ble_gatt_att_mtu_periph_set)
    nrf_ble_gatt_init                        0x00028721   Thumb Code    34  nrf_ble_gatt.o(i.nrf_ble_gatt_init)
    nrf_ble_gatt_on_ble_evt                  0x00028745   Thumb Code   366  nrf_ble_gatt.o(i.nrf_ble_gatt_on_ble_evt)
    nrf_ble_qwr_conn_handle_assign           0x000288b9   Thumb Code    22  nrf_ble_qwr.o(i.nrf_ble_qwr_conn_handle_assign)
    nrf_ble_qwr_init                         0x000288cf   Thumb Code    36  nrf_ble_qwr.o(i.nrf_ble_qwr_init)
    nrf_ble_qwr_on_ble_evt                   0x000288f3   Thumb Code   182  nrf_ble_qwr.o(i.nrf_ble_qwr_on_ble_evt)
    nrf_drv_clock_init                       0x000289c9   Thumb Code    56  nrf_drv_clock.o(i.nrf_drv_clock_init)
    nrf_drv_clock_lfclk_release              0x00028a09   Thumb Code    48  nrf_drv_clock.o(i.nrf_drv_clock_lfclk_release)
    nrf_drv_ppi_init                         0x00028a3d   Thumb Code    18  nrf_drv_ppi.o(i.nrf_drv_ppi_init)
    nrf_pwr_mgmt_init                        0x00028ae1   Thumb Code    28  nrf_pwr_mgmt.o(i.nrf_pwr_mgmt_init)
    nrf_pwr_mgmt_run                         0x00028b09   Thumb Code    64  nrf_pwr_mgmt.o(i.nrf_pwr_mgmt_run)
    nrf_sdh_ble_app_ram_start_get            0x00028ba5   Thumb Code    16  nrf_sdh_ble.o(i.nrf_sdh_ble_app_ram_start_get)
    nrf_sdh_ble_default_cfg_set              0x00028bb9   Thumb Code   156  nrf_sdh_ble.o(i.nrf_sdh_ble_default_cfg_set)
    nrf_sdh_ble_enable                       0x00028c55   Thumb Code    14  nrf_sdh_ble.o(i.nrf_sdh_ble_enable)
    nrf_sdh_enable_request                   0x00028cc9   Thumb Code   102  nrf_sdh.o(i.nrf_sdh_enable_request)
    nrf_sdh_evts_poll                        0x00028d3d   Thumb Code    32  nrf_sdh.o(i.nrf_sdh_evts_poll)
    nrf_sdh_is_enabled                       0x00028d61   Thumb Code     6  nrf_sdh.o(i.nrf_sdh_is_enabled)
    nrf_section_iter_init                    0x00028da9   Thumb Code    10  nrf_section_iter.o(i.nrf_section_iter_init)
    nrf_section_iter_next                    0x00028dd7   Thumb Code    32  nrf_section_iter.o(i.nrf_section_iter_next)
    nrf_sortlist_add                         0x00028df7   Thumb Code    34  nrf_sortlist.o(i.nrf_sortlist_add)
    nrf_sortlist_peek                        0x00028e19   Thumb Code     6  nrf_sortlist.o(i.nrf_sortlist_peek)
    nrf_sortlist_pop                         0x00028e1f   Thumb Code    14  nrf_sortlist.o(i.nrf_sortlist_pop)
    nrf_sortlist_remove                      0x00028e2d   Thumb Code    30  nrf_sortlist.o(i.nrf_sortlist_remove)
    nrfx_clock_enable                        0x00028e69   Thumb Code    34  nrfx_clock.o(i.nrfx_clock_enable)
    nrfx_clock_init                          0x00028e95   Thumb Code    26  nrfx_clock.o(i.nrfx_clock_init)
    nrfx_clock_lfclk_stop                    0x00028eb5   Thumb Code    38  nrfx_clock.o(i.nrfx_clock_lfclk_stop)
    nrfx_ppi_channel_alloc                   0x00028ef5   Thumb Code   100  nrfx_ppi.o(i.nrfx_ppi_channel_alloc)
    nrfx_ppi_channel_assign                  0x00028f61   Thumb Code    62  nrfx_ppi.o(i.nrfx_ppi_channel_assign)
    nrfx_ppi_channel_enable                  0x00028fa5   Thumb Code    48  nrfx_ppi.o(i.nrfx_ppi_channel_enable)
    nrfx_saadc_buffer_convert                0x00028fd9   Thumb Code   154  nrfx_saadc.o(i.nrfx_saadc_buffer_convert)
    nrfx_saadc_channel_init                  0x0002907d   Thumb Code   160  nrfx_saadc.o(i.nrfx_saadc_channel_init)
    nrfx_saadc_init                          0x00029129   Thumb Code   130  nrfx_saadc.o(i.nrfx_saadc_init)
    nrfx_saadc_sample_task_get               0x000291bd   Thumb Code    20  nrfx_saadc.o(i.nrfx_saadc_sample_task_get)
    nrfx_timer_compare                       0x000291d9   Thumb Code    58  nrfx_timer.o(i.nrfx_timer_compare)
    nrfx_timer_enable                        0x00029215   Thumb Code    24  nrfx_timer.o(i.nrfx_timer_enable)
    nrfx_timer_extended_compare              0x00029231   Thumb Code    42  nrfx_timer.o(i.nrfx_timer_extended_compare)
    nrfx_timer_init                          0x0002925d   Thumb Code   188  nrfx_timer.o(i.nrfx_timer_init)
    saadc_callback                           0x00029659   Thumb Code   158  main.o(i.saadc_callback)
    saadc_init                               0x00029701   Thumb Code   100  main.o(i.saadc_init)
    saadc_sampling_event_enable              0x00029771   Thumb Code    24  main.o(i.saadc_sampling_event_enable)
    saadc_sampling_event_init                0x0002978d   Thumb Code   138  main.o(i.saadc_sampling_event_init)
    timer_handler                            0x00029a9d   Thumb Code     2  main.o(i.timer_handler)
    m_ram_start                              0x00029e54   Data           4  nrf_sdh_ble.o(.constdata)
    Region$$Table$$Base                      0x00029e80   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x00029ea0   Number         0  anon$$obj.o(Region$$Table)
    sdh_ble_observers0$$Base                 0x00029ea0   Number         0  ble_conn_state.o(sdh_ble_observers0)
    sdh_ble_observers0$$Limit                0x00029ea8   Number         0  ble_conn_state.o(sdh_ble_observers0)
    sdh_ble_observers1$$Base                 0x00029ea8   Number         0  main.o(sdh_ble_observers1)
    sdh_ble_observers1$$Limit                0x00029ec8   Number         0  ble_conn_params.o(sdh_ble_observers1)
    sdh_ble_observers2$$Base                 0x00029ec8   Number         0  main.o(sdh_ble_observers2)
    sdh_ble_observers2$$Limit                0x00029ed8   Number         0  main.o(sdh_ble_observers2)
    sdh_ble_observers3$$Base                 0x00029ed8   Number         0  main.o(sdh_ble_observers3)
    sdh_ble_observers3$$Limit                0x00029ee0   Number         0  main.o(sdh_ble_observers3)
    sdh_soc_observers0$$Base                 0x00029ee0   Number         0  nrf_drv_clock.o(sdh_soc_observers0)
    sdh_soc_observers0$$Limit                0x00029ee8   Number         0  nrf_drv_clock.o(sdh_soc_observers0)
    sdh_stack_observers0$$Base               0x00029ee8   Number         0  nrf_sdh_ble.o(sdh_stack_observers0)
    sdh_stack_observers0$$Limit              0x00029ef8   Number         0  nrf_sdh_soc.o(sdh_stack_observers0)
    sdh_state_observers0$$Base               0x00029ef8   Number         0  nrf_drv_clock.o(sdh_state_observers0)
    sdh_state_observers0$$Limit              0x00029f00   Number         0  nrf_drv_clock.o(sdh_state_observers0)
    Image$$RW_IRAM1$$Base                    0x20002be0   Number         0  anon$$obj.o ABSOLUTE
    ble_send_data_flag                       0x20002be8   Data           1  main.o(.data)
    uart_receive_temporary_data_length       0x20002bee   Data           2  main.o(.data)
    ble_send_data_length                     0x20002bf0   Data           2  main.o(.data)
    SystemCoreClock                          0x20002c94   Data           4  system_nrf52.o(.data)
    uart_receive_temporary_data              0x20002c98   Data         244  main.o(.bss)
    ble_send_data                            0x20002d8c   Data         244  main.o(.bss)
    nrf_nvic_state                           0x20003528   Data          12  app_util_platform.o(.bss)
    __initial_sp                             0x20005540   Data           0  arm_startup_nrf52.o(STACK)



==============================================================================

Memory Map of the image

  Image Entry point : 0x00026201

  Load Region LR_IROM1 (Base: 0x00026000, Size: 0x00003fb8, Max: 0x0005a000, ABSOLUTE)

    Execution Region ER_IROM1 (Base: 0x00026000, Size: 0x00003f00, Max: 0x0005a000, ABSOLUTE)

    Base Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x00026000   0x00000200   Data   RO         5268    RESET               arm_startup_nrf52.o
    0x00026200   0x00000000   Code   RO         5324  * .ARM.Collect$$$$00000000  mc_w.l(entry.o)
    0x00026200   0x00000004   Code   RO         5345    .ARM.Collect$$$$00000001  mc_w.l(entry2.o)
    0x00026204   0x00000004   Code   RO         5348    .ARM.Collect$$$$00000004  mc_w.l(entry5.o)
    0x00026208   0x00000000   Code   RO         5350    .ARM.Collect$$$$00000008  mc_w.l(entry7b.o)
    0x00026208   0x00000000   Code   RO         5352    .ARM.Collect$$$$0000000A  mc_w.l(entry8b.o)
    0x00026208   0x00000008   Code   RO         5353    .ARM.Collect$$$$0000000B  mc_w.l(entry9a.o)
    0x00026210   0x00000000   Code   RO         5355    .ARM.Collect$$$$0000000D  mc_w.l(entry10a.o)
    0x00026210   0x00000000   Code   RO         5357    .ARM.Collect$$$$0000000F  mc_w.l(entry11a.o)
    0x00026210   0x00000004   Code   RO         5346    .ARM.Collect$$$$00002712  mc_w.l(entry2.o)
    0x00026214   0x000000c8   Code   RO         3950    .emb_text           nrf_atfifo.o
    0x000262dc   0x000000e2   Code   RO         4086    .emb_text           nrf_atomic.o
    0x000263be   0x00000002   PAD
    0x000263c0   0x00000024   Code   RO         5269    .text               arm_startup_nrf52.o
    0x000263e4   0x00000062   Code   RO         5327    .text               mc_w.l(uldiv.o)
    0x00026446   0x0000001e   Code   RO         5329    .text               mc_w.l(llshl.o)
    0x00026464   0x00000020   Code   RO         5331    .text               mc_w.l(llushr.o)
    0x00026484   0x00000024   Code   RO         5333    .text               mc_w.l(memcpya.o)
    0x000264a8   0x00000024   Code   RO         5335    .text               mc_w.l(memseta.o)
    0x000264cc   0x00000024   Code   RO         5359    .text               mc_w.l(init.o)
    0x000264f0   0x000000a4   Code   RO         1984    i.GPIOTE_IRQHandler  nrfx_gpiote.o
    0x00026594   0x00000058   Code   RO         1848    i.POWER_CLOCK_IRQHandler  nrfx_clock.o
    0x000265ec   0x00000010   Code   RO         3743    i.RTC1_IRQHandler   drv_rtc.o
    0x000265fc   0x00000148   Code   RO         2864    i.SAADC_IRQHandler  nrfx_saadc.o
    0x00026744   0x00000004   Code   RO         5068    i.SWI2_EGU2_IRQHandler  nrf_sdh.o
    0x00026748   0x00000398   Code   RO         5277    i.SystemInit        system_nrf52.o
    0x00026ae0   0x00000050   Code   RO         3013    i.TIMER1_IRQHandler  nrfx_timer.o
    0x00026b30   0x0000000c   Code   RO         2286    i.UARTE0_UART0_IRQHandler  nrfx_prs.o
    0x00026b3c   0x0000000e   Code   RO         5363    i.__scatterload_copy  mc_w.l(handlers.o)
    0x00026b4a   0x00000002   Code   RO         5364    i.__scatterload_null  mc_w.l(handlers.o)
    0x00026b4c   0x0000000e   Code   RO         5365    i.__scatterload_zeroinit  mc_w.l(handlers.o)
    0x00026b5a   0x00000002   PAD
    0x00026b5c   0x00000024   Code   RO         5069    i.__sd_nvic_app_accessible_irq  nrf_sdh.o
    0x00026b80   0x00000010   Code   RO          890    i.adv_set_data_size_max_get  ble_advertising.o
    0x00026b90   0x00000036   Code   RO          680    i.advertising_buttons_configure  bsp_btn_ble.o
    0x00026bc6   0x00000002   PAD
    0x00026bc8   0x00000030   Code   RO         3295    i.app_error_fault_handler  app_error_weak.o
    0x00026bf8   0x00000016   Code   RO         3235    i.app_error_handler_bare  app_error.o
    0x00026c0e   0x00000002   PAD
    0x00026c10   0x0000000c   Code   RO         3460    i.app_timer_cnt_get  app_timer2.o
    0x00026c1c   0x00000016   Code   RO         3461    i.app_timer_create  app_timer2.o
    0x00026c32   0x00000002   PAD
    0x00026c34   0x0000005c   Code   RO         3462    i.app_timer_init    app_timer2.o
    0x00026c90   0x00000030   Code   RO         3465    i.app_timer_start   app_timer2.o
    0x00026cc0   0x0000000c   Code   RO         3466    i.app_timer_stop    app_timer2.o
    0x00026ccc   0x00000048   Code   RO         3675    i.app_util_critical_region_enter  app_util_platform.o
    0x00026d14   0x00000034   Code   RO         3676    i.app_util_critical_region_exit  app_util_platform.o
    0x00026d48   0x0000000c   Code   RO         2714    i.are_app_channels  nrfx_ppi.o
    0x00026d54   0x00000048   Code   RO         1235    i.blcm_link_ctx_get  ble_link_ctx_manager.o
    0x00026d9c   0x00000182   Code   RO          784    i.ble_advdata_encode  ble_advdata.o
    0x00026f1e   0x00000020   Code   RO          786    i.ble_advdata_parse  ble_advdata.o
    0x00026f3e   0x00000046   Code   RO          787    i.ble_advdata_search  ble_advdata.o
    0x00026f84   0x00000006   Code   RO          892    i.ble_advertising_conn_cfg_tag_set  ble_advertising.o
    0x00026f8a   0x000000f8   Code   RO          893    i.ble_advertising_init  ble_advertising.o
    0x00027082   0x0000006a   Code   RO          895    i.ble_advertising_on_ble_evt  ble_advertising.o
    0x000270ec   0x00000230   Code   RO          898    i.ble_advertising_start  ble_advertising.o
    0x0002731c   0x00000068   Code   RO          989    i.ble_conn_params_init  ble_conn_params.o
    0x00027384   0x00000012   Code   RO         1067    i.ble_conn_state_conn_idx  ble_conn_state.o
    0x00027396   0x00000002   PAD
    0x00027398   0x00000014   Code   RO         1081    i.ble_conn_state_valid  ble_conn_state.o
    0x000273ac   0x00000060   Code   RO          790    i.ble_device_addr_encode  ble_advdata.o
    0x0002740c   0x00000098   Code   RO            5    i.ble_evt_handler   main.o
    0x000274a4   0x00000070   Code   RO          681    i.ble_evt_handler   bsp_btn_ble.o
    0x00027514   0x000000fc   Code   RO          991    i.ble_evt_handler   ble_conn_params.o
    0x00027610   0x00000118   Code   RO         1082    i.ble_evt_handler   ble_conn_state.o
    0x00027728   0x00000066   Code   RO         1452    i.ble_nus_data_send  ble_nus.o
    0x0002778e   0x00000002   PAD
    0x00027790   0x000000d4   Code   RO         1453    i.ble_nus_init      ble_nus.o
    0x00027864   0x00000076   Code   RO         1454    i.ble_nus_on_ble_evt  ble_nus.o
    0x000278da   0x00000008   Code   RO         1261    i.ble_srv_is_notification_enabled  ble_srv_common.o
    0x000278e2   0x00000002   PAD
    0x000278e4   0x0000000c   Code   RO          450    i.bsp_board_button_idx_to_pin  boards.o
    0x000278f0   0x00000024   Code   RO          454    i.bsp_board_led_invert  boards.o
    0x00027914   0x00000010   Code   RO          455    i.bsp_board_led_off  boards.o
    0x00027924   0x00000010   Code   RO          456    i.bsp_board_led_on  boards.o
    0x00027934   0x00000020   Code   RO          457    i.bsp_board_led_state_get  boards.o
    0x00027954   0x00000012   Code   RO          458    i.bsp_board_leds_off  boards.o
    0x00027966   0x00000012   Code   RO          459    i.bsp_board_leds_on  boards.o
    0x00027978   0x00000024   Code   RO          460    i.bsp_board_pin_to_button_idx  boards.o
    0x0002799c   0x0000001e   Code   RO          683    i.bsp_btn_ble_sleep_mode_prepare  bsp_btn_ble.o
    0x000279ba   0x00000002   PAD
    0x000279bc   0x00000090   Code   RO          564    i.bsp_button_event_handler  bsp.o
    0x00027a4c   0x00000044   Code   RO          568    i.bsp_event_to_button_action_assign  bsp.o
    0x00027a90   0x00000018   Code   RO          569    i.bsp_indication_set  bsp.o
    0x00027aa8   0x000001c8   Code   RO          571    i.bsp_led_indication  bsp.o
    0x00027c70   0x00000006   Code   RO          573    i.bsp_wakeup_button_enable  bsp.o
    0x00027c76   0x00000002   PAD
    0x00027c78   0x00000010   Code   RO         1987    i.channel_port_get  nrfx_gpiote.o
    0x00027c88   0x00000190   Code   RO         1263    i.characteristic_add  ble_srv_common.o
    0x00027e18   0x00000028   Code   RO         1506    i.clock_clk_started_notify  nrf_drv_clock.o
    0x00027e40   0x0000001c   Code   RO         1507    i.clock_irq_handler  nrf_drv_clock.o
    0x00027e5c   0x00000018   Code   RO         3468    i.compare_func      app_timer2.o
    0x00027e74   0x0000003e   Code   RO         1083    i.conn_handle_list_get  ble_conn_state.o
    0x00027eb2   0x00000088   Code   RO          791    i.conn_int_encode   ble_advdata.o
    0x00027f3a   0x00000004   Code   RO            7    i.conn_params_error_handler  main.o
    0x00027f3e   0x00000002   PAD
    0x00027f40   0x00000048   Code   RO          992    i.conn_params_negotiation  ble_conn_params.o
    0x00027f88   0x0000001e   Code   RO         1321    i.data_length_update  nrf_ble_gatt.o
    0x00027fa6   0x00000014   Code   RO         3744    i.drv_rtc_compare_disable  drv_rtc.o
    0x00027fba   0x0000000e   Code   RO         3747    i.drv_rtc_compare_pending  drv_rtc.o
    0x00027fc8   0x00000050   Code   RO         3748    i.drv_rtc_compare_set  drv_rtc.o
    0x00028018   0x00000008   Code   RO         3749    i.drv_rtc_counter_get  drv_rtc.o
    0x00028020   0x0000007c   Code   RO         3750    i.drv_rtc_init      drv_rtc.o
    0x0002809c   0x0000001e   Code   RO         3751    i.drv_rtc_irq_trigger  drv_rtc.o
    0x000280ba   0x00000008   Code   RO         3753    i.drv_rtc_overflow_enable  drv_rtc.o
    0x000280c2   0x00000008   Code   RO         3754    i.drv_rtc_overflow_pending  drv_rtc.o
    0x000280ca   0x00000008   Code   RO         3755    i.drv_rtc_start     drv_rtc.o
    0x000280d2   0x00000008   Code   RO         3756    i.drv_rtc_stop      drv_rtc.o
    0x000280da   0x000000de   Code   RO         3761    i.drv_rtc_windowed_compare_set  drv_rtc.o
    0x000281b8   0x00000012   Code   RO         3762    i.evt_enable        drv_rtc.o
    0x000281ca   0x00000014   Code   RO         3763    i.evt_pending       drv_rtc.o
    0x000281de   0x0000000a   Code   RO         1084    i.flag_toggle       ble_conn_state.o
    0x000281e8   0x00000028   Code   RO          900    i.flags_set         ble_advertising.o
    0x00028210   0x00000020   Code   RO            9    i.gatt_evt_handler  main.o
    0x00028230   0x00000030   Code   RO           10    i.gatt_init         main.o
    0x00028260   0x00000034   Code   RO         3469    i.get_now           app_timer2.o
    0x00028294   0x00000014   Code   RO          993    i.instance_get      ble_conn_params.o
    0x000282a8   0x00000018   Code   RO         2715    i.is_allocated_channel  nrfx_ppi.o
    0x000282c0   0x0000000a   Code   RO         2717    i.is_app_channel    nrfx_ppi.o
    0x000282ca   0x00000044   Code   RO          994    i.is_conn_params_ok  ble_conn_params.o
    0x0002830e   0x0000000c   Code   RO         2719    i.is_programmable_app_channel  nrfx_ppi.o
    0x0002831a   0x00000002   PAD
    0x0002831c   0x00000030   Code   RO          575    i.leds_off          bsp.o
    0x0002834c   0x00000018   Code   RO         1322    i.link_init         nrf_ble_gatt.o
    0x00028364   0x000001b4   Code   RO           11    i.main              main.o
    0x00028518   0x00000064   Code   RO          792    i.manuf_specific_data_encode  ble_advdata.o
    0x0002857c   0x000000a6   Code   RO          793    i.name_encode       ble_advdata.o
    0x00028622   0x00000026   Code   RO         3954    i.nrf_atfifo_init   nrf_atfifo.o
    0x00028648   0x00000016   Code   RO         3955    i.nrf_atfifo_item_alloc  nrf_atfifo.o
    0x0002865e   0x00000016   Code   RO         3956    i.nrf_atfifo_item_free  nrf_atfifo.o
    0x00028674   0x00000016   Code   RO         3957    i.nrf_atfifo_item_get  nrf_atfifo.o
    0x0002868a   0x00000016   Code   RO         3958    i.nrf_atfifo_item_put  nrf_atfifo.o
    0x000286a0   0x00000016   Code   RO         4020    i.nrf_atflags_clear  nrf_atflags.o
    0x000286b6   0x00000016   Code   RO         4025    i.nrf_atflags_get   nrf_atflags.o
    0x000286cc   0x00000014   Code   RO         4027    i.nrf_atflags_set   nrf_atflags.o
    0x000286e0   0x0000000c   Code   RO         4092    i.nrf_atomic_u32_and  nrf_atomic.o
    0x000286ec   0x0000000c   Code   RO         4101    i.nrf_atomic_u32_or  nrf_atomic.o
    0x000286f8   0x00000010   Code   RO         1988    i.nrf_bitmask_bit_is_set  nrfx_gpiote.o
    0x00028708   0x00000018   Code   RO         1324    i.nrf_ble_gatt_att_mtu_periph_set  nrf_ble_gatt.o
    0x00028720   0x00000022   Code   RO         1328    i.nrf_ble_gatt_init  nrf_ble_gatt.o
    0x00028742   0x00000002   PAD
    0x00028744   0x00000174   Code   RO         1329    i.nrf_ble_gatt_on_ble_evt  nrf_ble_gatt.o
    0x000288b8   0x00000016   Code   RO         1411    i.nrf_ble_qwr_conn_handle_assign  nrf_ble_qwr.o
    0x000288ce   0x00000024   Code   RO         1412    i.nrf_ble_qwr_init  nrf_ble_qwr.o
    0x000288f2   0x000000b6   Code   RO         1413    i.nrf_ble_qwr_on_ble_evt  nrf_ble_qwr.o
    0x000289a8   0x0000000e   Code   RO         1849    i.nrf_clock_event_check  nrfx_clock.o
    0x000289b6   0x00000010   Code   RO         1850    i.nrf_clock_event_clear  nrfx_clock.o
    0x000289c6   0x00000002   PAD
    0x000289c8   0x00000040   Code   RO         1514    i.nrf_drv_clock_init  nrf_drv_clock.o
    0x00028a08   0x00000034   Code   RO         1518    i.nrf_drv_clock_lfclk_release  nrf_drv_clock.o
    0x00028a3c   0x00000018   Code   RO         2679    i.nrf_drv_ppi_init  nrf_drv_ppi.o
    0x00028a54   0x00000022   Code   RO         1991    i.nrf_gpio_cfg_sense_set  nrfx_gpiote.o
    0x00028a76   0x00000002   PAD
    0x00028a78   0x0000002c   Code   RO         1992    i.nrf_gpio_latches_read_and_clear  nrfx_gpiote.o
    0x00028aa4   0x00000016   Code   RO          463    i.nrf_gpio_pin_write  boards.o
    0x00028aba   0x00000002   PAD
    0x00028abc   0x00000014   Code   RO         1994    i.nrf_gpiote_event_clear  nrfx_gpiote.o
    0x00028ad0   0x00000010   Code   RO         1995    i.nrf_gpiote_event_is_set  nrfx_gpiote.o
    0x00028ae0   0x00000028   Code   RO         4423    i.nrf_pwr_mgmt_init  nrf_pwr_mgmt.o
    0x00028b08   0x00000044   Code   RO         4424    i.nrf_pwr_mgmt_run  nrf_pwr_mgmt.o
    0x00028b4c   0x00000004   Code   RO           12    i.nrf_qwr_error_handler  main.o
    0x00028b50   0x0000000c   Code   RO         3764    i.nrf_rtc_event_clear  drv_rtc.o
    0x00028b5c   0x00000010   Code   RO         2865    i.nrf_saadc_buffer_init  nrfx_saadc.o
    0x00028b6c   0x00000014   Code   RO         2866    i.nrf_saadc_channel_input_set  nrfx_saadc.o
    0x00028b80   0x00000010   Code   RO         2867    i.nrf_saadc_event_check  nrfx_saadc.o
    0x00028b90   0x00000014   Code   RO         2868    i.nrf_saadc_event_clear  nrfx_saadc.o
    0x00028ba4   0x00000014   Code   RO         5186    i.nrf_sdh_ble_app_ram_start_get  nrf_sdh_ble.o
    0x00028bb8   0x0000009c   Code   RO         5187    i.nrf_sdh_ble_default_cfg_set  nrf_sdh_ble.o
    0x00028c54   0x00000014   Code   RO         5188    i.nrf_sdh_ble_enable  nrf_sdh_ble.o
    0x00028c68   0x00000060   Code   RO         5189    i.nrf_sdh_ble_evts_poll  nrf_sdh_ble.o
    0x00028cc8   0x00000074   Code   RO         5071    i.nrf_sdh_enable_request  nrf_sdh.o
    0x00028d3c   0x00000024   Code   RO         5072    i.nrf_sdh_evts_poll  nrf_sdh.o
    0x00028d60   0x0000000c   Code   RO         5073    i.nrf_sdh_is_enabled  nrf_sdh.o
    0x00028d6c   0x0000003c   Code   RO         5237    i.nrf_sdh_soc_evts_poll  nrf_sdh_soc.o
    0x00028da8   0x0000000a   Code   RO         4555    i.nrf_section_iter_init  nrf_section_iter.o
    0x00028db2   0x00000024   Code   RO         4556    i.nrf_section_iter_item_set  nrf_section_iter.o
    0x00028dd6   0x00000020   Code   RO         4557    i.nrf_section_iter_next  nrf_section_iter.o
    0x00028df6   0x00000022   Code   RO         4590    i.nrf_sortlist_add  nrf_sortlist.o
    0x00028e18   0x00000006   Code   RO         4592    i.nrf_sortlist_peek  nrf_sortlist.o
    0x00028e1e   0x0000000e   Code   RO         4593    i.nrf_sortlist_pop  nrf_sortlist.o
    0x00028e2c   0x0000001e   Code   RO         4594    i.nrf_sortlist_remove  nrf_sortlist.o
    0x00028e4a   0x0000000c   Code   RO         3014    i.nrf_timer_event_clear  nrfx_timer.o
    0x00028e56   0x00000002   PAD
    0x00028e58   0x00000010   Code   RO         1521    i.nrf_wdt_started   nrf_drv_clock.o
    0x00028e68   0x0000002c   Code   RO         1855    i.nrfx_clock_enable  nrfx_clock.o
    0x00028e94   0x00000020   Code   RO         1858    i.nrfx_clock_init   nrfx_clock.o
    0x00028eb4   0x00000030   Code   RO         1861    i.nrfx_clock_lfclk_stop  nrfx_clock.o
    0x00028ee4   0x00000010   Code   RO         3765    i.nrfx_coredep_delay_us  drv_rtc.o
    0x00028ef4   0x0000006c   Code   RO         2720    i.nrfx_ppi_channel_alloc  nrfx_ppi.o
    0x00028f60   0x00000044   Code   RO         2721    i.nrfx_ppi_channel_assign  nrfx_ppi.o
    0x00028fa4   0x00000034   Code   RO         2723    i.nrfx_ppi_channel_enable  nrfx_ppi.o
    0x00028fd8   0x000000a4   Code   RO         2872    i.nrfx_saadc_buffer_convert  nrfx_saadc.o
    0x0002907c   0x000000ac   Code   RO         2874    i.nrfx_saadc_channel_init  nrfx_saadc.o
    0x00029128   0x00000094   Code   RO         2876    i.nrfx_saadc_init   nrfx_saadc.o
    0x000291bc   0x0000001c   Code   RO         2881    i.nrfx_saadc_sample_task_get  nrfx_saadc.o
    0x000291d8   0x0000003a   Code   RO         3017    i.nrfx_timer_compare  nrfx_timer.o
    0x00029212   0x00000002   PAD
    0x00029214   0x0000001c   Code   RO         3021    i.nrfx_timer_enable  nrfx_timer.o
    0x00029230   0x0000002a   Code   RO         3022    i.nrfx_timer_extended_compare  nrfx_timer.o
    0x0002925a   0x00000002   PAD
    0x0002925c   0x000000c0   Code   RO         3024    i.nrfx_timer_init   nrfx_timer.o
    0x0002931c   0x00000002   Code   RO           13    i.nus_data_handler  main.o
    0x0002931e   0x00000024   Code   RO           14    i.on_adv_evt        main.o
    0x00029342   0x00000002   PAD
    0x00029344   0x0000001c   Code   RO           15    i.on_conn_params_evt  main.o
    0x00029360   0x0000006a   Code   RO         1455    i.on_connect        ble_nus.o
    0x000293ca   0x00000084   Code   RO         1456    i.on_write          ble_nus.o
    0x0002944e   0x00000012   Code   RO          901    i.phy_is_valid      ble_advertising.o
    0x00029460   0x000000b4   Code   RO         2028    i.port_event_handle  nrfx_gpiote.o
    0x00029514   0x00000010   Code   RO         2029    i.port_handler_polarity_get  nrfx_gpiote.o
    0x00029524   0x0000005c   Code   RO         3470    i.rtc_irq           app_timer2.o
    0x00029580   0x00000064   Code   RO         3471    i.rtc_schedule      app_timer2.o
    0x000295e4   0x00000074   Code   RO         3472    i.rtc_update        app_timer2.o
    0x00029658   0x000000a8   Code   RO           16    i.saadc_callback    main.o
    0x00029700   0x00000070   Code   RO           17    i.saadc_init        main.o
    0x00029770   0x0000001c   Code   RO           18    i.saadc_sampling_event_enable  main.o
    0x0002978c   0x00000098   Code   RO           19    i.saadc_sampling_event_init  main.o
    0x00029824   0x00000058   Code   RO         1522    i.sd_state_evt_handler  nrf_drv_clock.o
    0x0002987c   0x00000030   Code   RO         5078    i.sdh_request_observer_notify  nrf_sdh.o
    0x000298ac   0x0000002c   Code   RO         5079    i.sdh_state_observer_notify  nrf_sdh.o
    0x000298d8   0x00000010   Code   RO          995    i.send_error_evt    ble_conn_params.o
    0x000298e8   0x00000088   Code   RO          794    i.service_data_encode  ble_advdata.o
    0x00029970   0x00000030   Code   RO         1265    i.set_security_req  ble_srv_common.o
    0x000299a0   0x00000028   Code   RO           20    i.sleep_mode_enter  main.o
    0x000299c8   0x00000018   Code   RO         1523    i.soc_evt_handler   nrf_drv_clock.o
    0x000299e0   0x00000058   Code   RO         5081    i.softdevices_evt_irq_enable  nrf_sdh.o
    0x00029a38   0x0000000c   Code   RO         3473    i.sortlist_pop      app_timer2.o
    0x00029a44   0x00000058   Code   RO         3474    i.timer_expire      app_timer2.o
    0x00029a9c   0x00000002   Code   RO           21    i.timer_handler     main.o
    0x00029a9e   0x00000002   PAD
    0x00029aa0   0x00000078   Code   RO         3475    i.timer_req_process  app_timer2.o
    0x00029b18   0x00000038   Code   RO         3476    i.timer_req_schedule  app_timer2.o
    0x00029b50   0x0000000a   Code   RO          795    i.uint16_encode     ble_advdata.o
    0x00029b5a   0x00000002   PAD
    0x00029b5c   0x00000068   Code   RO          996    i.update_timeout_handler  ble_conn_params.o
    0x00029bc4   0x00000018   Code   RO          902    i.use_whitelist     ble_advertising.o
    0x00029bdc   0x00000024   Code   RO         1414    i.user_mem_reply    nrf_ble_qwr.o
    0x00029c00   0x00000030   Code   RO          796    i.uuid_list_encode  ble_advdata.o
    0x00029c30   0x0000009e   Code   RO          797    i.uuid_list_sized_encode  ble_advdata.o
    0x00029cce   0x0000003a   Code   RO          577    i.wakeup_button_cfg  bsp.o
    0x00029d08   0x0000002d   Data   RO           23    .constdata          main.o
    0x00029d35   0x00000008   Data   RO          464    .constdata          boards.o
    0x00029d3d   0x00000003   PAD
    0x00029d40   0x0000002c   Data   RO          582    .constdata          bsp.o
    0x00029d6c   0x00000016   Data   RO         1330    .constdata          nrf_ble_gatt.o
    0x00029d82   0x00000002   PAD
    0x00029d84   0x00000010   Data   RO         1457    .constdata          ble_nus.o
    0x00029d94   0x00000004   Data   RO         2031    .constdata          nrfx_gpiote.o
    0x00029d98   0x00000014   Data   RO         3479    .constdata          app_timer2.o
    0x00029dac   0x00000004   PAD
    0x00029db0   0x00000006   Data   RO         3766    .constdata          drv_rtc.o
    0x00029db6   0x00000002   PAD
    0x00029db8   0x00000018   Data   RO         4428    .constdata          nrf_pwr_mgmt.o
    0x00029dd0   0x0000000c   Data   RO         4429    .constdata          nrf_pwr_mgmt.o
    0x00029ddc   0x00000010   Data   RO         5082    .constdata          nrf_sdh.o
    0x00029dec   0x00000028   Data   RO         5083    .constdata          nrf_sdh.o
    0x00029e14   0x00000010   Data   RO         5084    .constdata          nrf_sdh.o
    0x00029e24   0x00000010   Data   RO         5085    .constdata          nrf_sdh.o
    0x00029e34   0x00000020   Data   RO         5190    .constdata          nrf_sdh_ble.o
    0x00029e54   0x00000010   Data   RO         5191    .constdata          nrf_sdh_ble.o
    0x00029e64   0x00000010   Data   RO         5238    .constdata          nrf_sdh_soc.o
    0x00029e74   0x0000000c   Data   RO         5239    .constdata          nrf_sdh_soc.o
    0x00029e80   0x00000020   Data   RO         5361    Region$$Table       anon$$obj.o
    0x00029ea0   0x00000008   Data   RO         1088    sdh_ble_observers0  ble_conn_state.o
    0x00029ea8   0x00000010   Data   RO           27    sdh_ble_observers1  main.o
    0x00029eb8   0x00000008   Data   RO          685    sdh_ble_observers1  bsp_btn_ble.o
    0x00029ec0   0x00000008   Data   RO          999    sdh_ble_observers1  ble_conn_params.o
    0x00029ec8   0x00000010   Data   RO           28    sdh_ble_observers2  main.o
    0x00029ed8   0x00000008   Data   RO           29    sdh_ble_observers3  main.o
    0x00029ee0   0x00000008   Data   RO         1529    sdh_soc_observers0  nrf_drv_clock.o
    0x00029ee8   0x00000008   Data   RO         5193    sdh_stack_observers0  nrf_sdh_ble.o
    0x00029ef0   0x00000008   Data   RO         5240    sdh_stack_observers0  nrf_sdh_soc.o
    0x00029ef8   0x00000008   Data   RO         1530    sdh_state_observers0  nrf_drv_clock.o


    Execution Region RW_IRAM1 (Base: 0x20002be0, Size: 0x00002960, Max: 0x0000d420, ABSOLUTE)

    Base Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20002be0   0x00000008   Data   RW           24    .data               main.o
    0x20002be8   0x00000030   Data   RW           25    .data               main.o
    0x20002c18   0x00000004   Data   RW           26    .data               main.o
    0x20002c1c   0x00000010   Data   RW          583    .data               bsp.o
    0x20002c2c   0x00000008   Data   RW          684    .data               bsp_btn_ble.o
    0x20002c34   0x00000008   Data   RW          998    .data               ble_conn_params.o
    0x20002c3c   0x00000008   Data   RW         1865    .data               nrfx_clock.o
    0x20002c44   0x00000008   Data   RW         2291    .data               nrfx_prs.o
    0x20002c4c   0x00000001   Data   RW         2681    .data               nrf_drv_ppi.o
    0x20002c4d   0x00000003   PAD
    0x20002c50   0x00000008   Data   RW         2745    .data               nrfx_ppi.o
    0x20002c58   0x00000020   Data   RW         3480    .data               app_timer2.o
    0x20002c78   0x00000004   Data   RW         3481    .data               app_timer2.o
    0x20002c7c   0x0000000c   Data   RW         3767    .data               drv_rtc.o
    0x20002c88   0x00000008   Data   RW         4430    .data               nrf_pwr_mgmt.o
    0x20002c90   0x00000003   Data   RW         5086    .data               nrf_sdh.o
    0x20002c93   0x00000001   Data   RW         5192    .data               nrf_sdh_ble.o
    0x20002c94   0x00000004   Data   RW         5278    .data               system_nrf52.o
    0x20002c98   0x0000063c   Zero   RW           22    .bss                main.o
    0x200032d4   0x00000004   PAD
    0x200032d8   0x00000020   Zero   RW          578    .bss                bsp.o
    0x200032f8   0x00000020   Zero   RW          579    .bss                bsp.o
    0x20003318   0x0000000c   Zero   RW          580    .bss                bsp.o
    0x20003324   0x00000004   PAD
    0x20003328   0x00000020   Zero   RW          581    .bss                bsp.o
    0x20003348   0x00000050   Zero   RW          997    .bss                ble_conn_params.o
    0x20003398   0x0000007c   Zero   RW         1087    .bss                ble_conn_state.o
    0x20003414   0x00000014   Zero   RW         1524    .bss                nrf_drv_clock.o
    0x20003428   0x0000005c   Zero   RW         2030    .bss                nrfx_gpiote.o
    0x20003484   0x00000030   Zero   RW         2883    .bss                nrfx_saadc.o
    0x200034b4   0x0000000c   Zero   RW         3029    .bss                nrfx_timer.o
    0x200034c0   0x00000010   Zero   RW         3477    .bss                app_timer2.o
    0x200034d0   0x00000058   Zero   RW         3478    .bss                app_timer2.o
    0x20003528   0x0000000c   Zero   RW         3681    .bss                app_util_platform.o
    0x20003534   0x0000000c   Zero   RW         4427    .bss                nrf_pwr_mgmt.o
    0x20003540   0x00002000   Zero   RW         5266    STACK               arm_startup_nrf52.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

        22          0          0          0          0       2673   app_error.o
        48          8          0          0          0      34245   app_error_weak.o
       846         88         20         36        104      17951   app_timer2.o
       124         14          0          0         12      11026   app_util_platform.o
        36          8        512          0       8192        984   arm_startup_nrf52.o
      1338          0          0          0          0      54528   ble_advdata.o
      1018          6          0          0          0      10871   ble_advertising.o
       636         44          8          8         80      10015   ble_conn_params.o
       390         10          8          0        124       8343   ble_conn_state.o
        72          0          0          0          0       1804   ble_link_ctx_manager.o
       670          6         16          0          0       7052   ble_nus.o
       456          6          0          0          0       3691   ble_srv_common.o
       206         32          8          0          0      12157   boards.o
       804         60         44         16        108      13552   bsp.o
       196          6          8          8          0       4341   bsp_btn_ble.o
       612         14          6         12          0      49465   drv_rtc.o
      1244        114         85         60       1596     202279   main.o
       326          0          0          0          0       6479   nrf_atfifo.o
        64          0          0          0          0       3332   nrf_atflags.o
       250          0          0          0          0       3992   nrf_atomic.o
       484          6         22          0          0      36916   nrf_ble_gatt.o
       276          0          0          0          0       5101   nrf_ble_qwr.o
       312         36         16          0         20      44562   nrf_drv_clock.o
        24          6          0          1          0       1123   nrf_drv_ppi.o
       108         16         36          8         12      41173   nrf_pwr_mgmt.o
       384         46         88          3          0      43591   nrf_sdh.o
       292         18         56          1          0       6035   nrf_sdh_ble.o
        60          4         36          0          0       2588   nrf_sdh_soc.o
        78          0          0          0          0       3092   nrf_section_iter.o
        84          0          0          0          0       4128   nrf_sortlist.o
       242         32          0          8          0      46750   nrfx_clock.o
       506         38          4          0         92      22546   nrfx_gpiote.o
       286         22          0          8          0      12645   nrfx_ppi.o
        12          6          0          8          0       2274   nrfx_prs.o
       912         74          0          0         48      20193   nrfx_saadc.o
       412         18          0          0         12      15134   nrfx_timer.o
         0          0          0          0          0       3540   nrfx_uart.o
       920         72          0          4          0      13635   system_nrf52.o

    ----------------------------------------------------------------------
     14792        <USER>       <GROUP>        184      10408     783806   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        42          0         11          3          8          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

         0          0          0          0          0          0   entry.o
         0          0          0          0          0          0   entry10a.o
         0          0          0          0          0          0   entry11a.o
         8          4          0          0          0          0   entry2.o
         4          0          0          0          0          0   entry5.o
         0          0          0          0          0          0   entry7b.o
         0          0          0          0          0          0   entry8b.o
         8          4          0          0          0          0   entry9a.o
        30          0          0          0          0          0   handlers.o
        36          8          0          0          0         68   init.o
        30          0          0          0          0         68   llshl.o
        32          0          0          0          0         68   llushr.o
        36          0          0          0          0         68   memcpya.o
        36          0          0          0          0        108   memseta.o
        98          0          0          0          0         92   uldiv.o

    ----------------------------------------------------------------------
       320         <USER>          <GROUP>          0          0        472   Library Totals
         2          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

       318         16          0          0          0        472   mc_w.l

    ----------------------------------------------------------------------
       320         <USER>          <GROUP>          0          0        472   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     15112        826       1016        184      10408     765126   Grand Totals
     15112        826       1016        184      10408     765126   ELF Image Totals
     15112        826       1016        184          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                16128 (  15.75kB)
    Total RW  Size (RW Data + ZI Data)             10592 (  10.34kB)
    Total ROM Size (Code + RO Data + RW Data)      16312 (  15.93kB)

==============================================================================

