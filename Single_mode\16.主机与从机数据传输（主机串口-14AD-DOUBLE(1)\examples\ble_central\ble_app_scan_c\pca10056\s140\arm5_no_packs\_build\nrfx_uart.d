.\_build\nrfx_uart.o: ..\..\..\..\..\..\modules\nrfx\drivers\src\nrfx_uart.c
.\_build\nrfx_uart.o: ..\..\..\..\..\..\modules\nrfx\nrfx.h
.\_build\nrfx_uart.o: ..\..\..\..\..\..\integration\nrfx\nrfx_config.h
.\_build\nrfx_uart.o: ..\config\sdk_config.h
.\_build\nrfx_uart.o: ..\..\..\..\..\..\modules\nrfx\drivers/nrfx_common.h
.\_build\nrfx_uart.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\stdint.h
.\_build\nrfx_uart.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\stddef.h
.\_build\nrfx_uart.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\stdbool.h
.\_build\nrfx_uart.o: C:\Keil_v5\ARM\PACK\NordicSemiconductor\nRF_DeviceFamilyPack\8.32.1\Device\Include\nrf.h
.\_build\nrfx_uart.o: C:\Keil_v5\ARM\PACK\NordicSemiconductor\nRF_DeviceFamilyPack\8.32.1\Device\Include\nrf52840.h
.\_build\nrfx_uart.o: C:\Keil_v5\ARM\PACK\ARM\CMSIS\5.6.0\CMSIS\Core\Include\core_cm4.h
.\_build\nrfx_uart.o: C:\Keil_v5\ARM\PACK\ARM\CMSIS\5.6.0\CMSIS\Core\Include\cmsis_version.h
.\_build\nrfx_uart.o: C:\Keil_v5\ARM\PACK\ARM\CMSIS\5.6.0\CMSIS\Core\Include\cmsis_compiler.h
.\_build\nrfx_uart.o: C:\Keil_v5\ARM\PACK\ARM\CMSIS\5.6.0\CMSIS\Core\Include\cmsis_armcc.h
.\_build\nrfx_uart.o: C:\Keil_v5\ARM\PACK\ARM\CMSIS\5.6.0\CMSIS\Core\Include\mpu_armv7.h
.\_build\nrfx_uart.o: C:\Keil_v5\ARM\PACK\NordicSemiconductor\nRF_DeviceFamilyPack\8.32.1\Device\Include\system_nrf52840.h
.\_build\nrfx_uart.o: C:\Keil_v5\ARM\PACK\NordicSemiconductor\nRF_DeviceFamilyPack\8.32.1\Device\Include\nrf52840_bitfields.h
.\_build\nrfx_uart.o: C:\Keil_v5\ARM\PACK\NordicSemiconductor\nRF_DeviceFamilyPack\8.32.1\Device\Include\nrf51_to_nrf52840.h
.\_build\nrfx_uart.o: C:\Keil_v5\ARM\PACK\NordicSemiconductor\nRF_DeviceFamilyPack\8.32.1\Device\Include\nrf52_to_nrf52840.h
.\_build\nrfx_uart.o: C:\Keil_v5\ARM\PACK\NordicSemiconductor\nRF_DeviceFamilyPack\8.32.1\Device\Include\compiler_abstraction.h
.\_build\nrfx_uart.o: C:\Keil_v5\ARM\PACK\NordicSemiconductor\nRF_DeviceFamilyPack\8.32.1\Device\Include\nrf_peripherals.h
.\_build\nrfx_uart.o: C:\Keil_v5\ARM\PACK\NordicSemiconductor\nRF_DeviceFamilyPack\8.32.1\Device\Include\nrf52840_peripherals.h
.\_build\nrfx_uart.o: ..\..\..\..\..\..\integration\nrfx\nrfx_glue.h
.\_build\nrfx_uart.o: ..\..\..\..\..\..\integration\nrfx\legacy/apply_old_config.h
.\_build\nrfx_uart.o: ..\..\..\..\..\..\modules\nrfx\soc/nrfx_irqs.h
.\_build\nrfx_uart.o: ..\..\..\..\..\..\modules\nrfx\soc/nrfx_irqs_nrf52840.h
.\_build\nrfx_uart.o: ..\..\..\..\..\..\components\libraries\util\nrf_assert.h
.\_build\nrfx_uart.o: ..\..\..\..\..\..\components\libraries\util\app_util.h
.\_build\nrfx_uart.o: ..\..\..\..\..\..\components\libraries\util\nordic_common.h
.\_build\nrfx_uart.o: ..\..\..\..\..\..\components\softdevice\s140\headers\nrf52\nrf_mbr.h
.\_build\nrfx_uart.o: ..\..\..\..\..\..\components\softdevice\s140\headers\nrf_svc.h
.\_build\nrfx_uart.o: ..\..\..\..\..\..\components\libraries\util\app_util_platform.h
.\_build\nrfx_uart.o: ..\..\..\..\..\..\components\softdevice\s140\headers\nrf_soc.h
.\_build\nrfx_uart.o: ..\..\..\..\..\..\components\softdevice\s140\headers\nrf_error.h
.\_build\nrfx_uart.o: ..\..\..\..\..\..\components\softdevice\s140\headers\nrf_error_soc.h
.\_build\nrfx_uart.o: ..\..\..\..\..\..\components\softdevice\s140\headers\nrf_nvic.h
.\_build\nrfx_uart.o: ..\..\..\..\..\..\components\libraries\util\app_error.h
.\_build\nrfx_uart.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\stdio.h
.\_build\nrfx_uart.o: ..\..\..\..\..\..\components\libraries\util\sdk_errors.h
.\_build\nrfx_uart.o: ..\..\..\..\..\..\components\libraries\util\app_error_weak.h
.\_build\nrfx_uart.o: ..\..\..\..\..\..\modules\nrfx\soc/nrfx_coredep.h
.\_build\nrfx_uart.o: ..\..\..\..\..\..\modules\nrfx\soc/nrfx_atomic.h
.\_build\nrfx_uart.o: ..\..\..\..\..\..\modules\nrfx\nrfx.h
.\_build\nrfx_uart.o: ..\..\..\..\..\..\components\libraries\util\sdk_resources.h
.\_build\nrfx_uart.o: ..\..\..\..\..\..\components\softdevice\s140\headers\nrf_sd_def.h
.\_build\nrfx_uart.o: ..\..\..\..\..\..\modules\nrfx\drivers/nrfx_errors.h
.\_build\nrfx_uart.o: ..\..\..\..\..\..\modules\nrfx\drivers\include\nrfx_uart.h
.\_build\nrfx_uart.o: ..\..\..\..\..\..\modules\nrfx\hal/nrf_uart.h
.\_build\nrfx_uart.o: ..\..\..\..\..\..\modules\nrfx\drivers\src\prs/nrfx_prs.h
.\_build\nrfx_uart.o: ..\..\..\..\..\..\modules\nrfx\hal/nrf_gpio.h
.\_build\nrfx_uart.o: ..\..\..\..\..\..\integration\nrfx\nrfx_log.h
.\_build\nrfx_uart.o: ..\..\..\..\..\..\components\libraries\log\nrf_log.h
.\_build\nrfx_uart.o: ..\..\..\..\..\..\components\libraries\util\sdk_common.h
.\_build\nrfx_uart.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\string.h
.\_build\nrfx_uart.o: ..\..\..\..\..\..\components\libraries\util\sdk_os.h
.\_build\nrfx_uart.o: ..\..\..\..\..\..\components\libraries\util\sdk_macros.h
.\_build\nrfx_uart.o: ..\..\..\..\..\..\components\libraries\experimental_section_vars\nrf_section.h
.\_build\nrfx_uart.o: ..\..\..\..\..\..\components\libraries\strerror\nrf_strerror.h
.\_build\nrfx_uart.o: ..\..\..\..\..\..\components\libraries\log\src\nrf_log_internal.h
.\_build\nrfx_uart.o: ..\..\..\..\..\..\components\libraries\log\nrf_log_instance.h
.\_build\nrfx_uart.o: ..\..\..\..\..\..\components\libraries\log\nrf_log_types.h
