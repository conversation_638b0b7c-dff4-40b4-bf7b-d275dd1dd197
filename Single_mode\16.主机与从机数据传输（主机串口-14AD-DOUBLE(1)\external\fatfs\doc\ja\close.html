<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html lang="ja">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta http-equiv="Content-Style-Type" content="text/css">
<link rel="up" title="FatFs" href="../00index_j.html">
<link rel="alternate" hreflang="en" title="English" href="../en/close.html">
<link rel="stylesheet" href="../css_j.css" type="text/css" media="screen" title="ELM Default">
<title>FatFs - f_close</title>
</head>

<body>

<div class="para func">
<h2>f_close</h2>
<p>ファイルを閉じます。</p>
<pre>
FRESULT f_close (
  FIL* <span class="arg">fp</span>     <span class="c">/* [IN] ファイル オブジェクトへのポインタ */</span>
);
</pre>
</div>

<div class="para arg">
<h4>引数</h4>
<dl class="par">
<dt>fp</dt>
<dd>閉じようとするファイルのファイル オブジェクト構造体へのポインタを指定します。</dd>
</dl>
</div>


<div class="para ret">
<h4>戻り値</h4>
<p>
<a href="rc.html#ok">FR_OK</a>,
<a href="rc.html#de">FR_DISK_ERR</a>,
<a href="rc.html#ie">FR_INT_ERR</a>,
<a href="rc.html#io">FR_INVALID_OBJECT</a>,
<a href="rc.html#tm">FR_TIMEOUT</a>
</p>
</div>


<div class="para desc">
<h4>解説</h4>
<p>ファイルを閉じます。何らかの書き込みの行われたファイルの場合、キャッシュされた状態(リード/ライト バッファ上のデータ、変更されたFATやディレクトリ項目)はディスクに書き戻されます。関数が正常終了すると、そのファイル オブジェクトは無効になり、そのメモリも解放できます。</p>
<p>ファイル オブジェクトが読み出し専用モードで、<tt>_FS_LOCK</tt>オプションが選択されていない場合は、ファイルを閉じずにファイル オブジェクトを破棄することもできます。しかし、これは将来の互換性の点で推奨はされません。</p>
</div>


<div class="para comp">
<h4>対応情報</h4>
<p>全ての構成で使用可能です。</p>
</div>


<div class="para ref">
<h4>参照</h4>
<tt><a href="open.html">f_open</a>, <a href="read.html">f_read</a>, <a href="write.html">f_write</a>,  <a href="sync.html">f_sync</a>, <a href="sfile.html">FIL</a>, <a href="sfatfs.html">FATFS</a></tt>
</div>

<p class="foot"><a href="../00index_j.html">戻る</a></p>
</body>
</html>
