<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html lang="ja">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta http-equiv="Content-Style-Type" content="text/css">
<link rel="up" title="FatFs" href="../00index_j.html">
<link rel="alternate" hreflang="en" title="English" href="../en/puts.html">
<link rel="stylesheet" href="../css_j.css" type="text/css" media="screen" title="ELM Default">
<title>FatFs - f_puts</title>
</head>

<body>

<div class="para func">
<h2>f_puts</h2>
<p>ファイルに文字列を書き込みます。</p>
<pre>
int f_puts (
  const TCHAR* <span class="arg">str</span>,  <span class="c">/* [IN] 書き込む文字列 */</span>
  FIL* <span class="arg">fp</span>            <span class="c">/* [IN] ファイル オブジェクト */</span>
);
</pre>
</div>

<div class="para arg">
<h4>引数</h4>
<dl class="par">
<dt>str</dt>
<dd>書き込むヌル文字<tt>'\0'</tt>終端の文字列を指すポインタを指定します。ヌル文字は書き込まれません。</dd>
<dt>fp</dt>
<dd>ファイル オブジェクト構造体へのポインタを指定します。</dd>
</dl>
</div>


<div class="para ret">
<h4>戻り値</h4>
<p>文字列が正常に書き込まれると、書き込まれた文字数が返されます。ディスクが満杯またはエラーにより書き込みが中断されたときは<tt>EOF (-1)</tt>が返されます。</p>
<p>APIにUnicodeが選択(<tt>_LFN_UNICODE</tt>が1)されているときは、<tt class="arg">str</tt>はUTF-16文字列になりますが、ファイル上のエンコードは、<tt>_STRF_ENCODE</tt>オプションで選択できます。それ以外の時は無変換(1バイト/1文字)で書き込みます。</p>
</div>


<div class="para desc">
<h4>解説</h4>
<p>文字列をファイルに書き込みます。</p>
</div>


<div class="para comp">
<h4>対応情報</h4>
<p>この関数は<a href="write.html"><tt>f_write</tt></a>関数のラッパー関数です。<tt>_FS_READONLY == 0</tt>で、且つ<tt>_USE_STRFUNC</tt>が1または2のとき使用可能です。2を指定すると、文字列に含まれる<tt>'\n'</tt>は<tt>'\r'+'\n'<tt>に展開されてファイルに書き込まれます。</p>
</div>


<div class="para ref">
<h4>参照</h4>
<p><tt><a href="open.html">f_open</a>, <a href="putc.html">f_putc</a>, <a href="printf.html">f_printf</a>, <a href="gets.html">f_gets</a>, <a href="close.html">f_close</a>, <a href="sfile.html">FIL</a></tt></p>
</div>

<p class="foot"><a href="../00index_j.html">戻る</a></p>
</body>
</html>
