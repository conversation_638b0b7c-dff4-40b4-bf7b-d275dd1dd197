<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html lang="ja">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta http-equiv="Content-Style-Type" content="text/css">
<link rel="up" title="FatFs" href="../00index_j.html">
<link rel="alternate" hreflang="en" title="English" href="../en/dinit.html">
<link rel="stylesheet" href="../css_j.css" type="text/css" media="screen" title="ELM Default">
<title>FatFs - disk_initialize</title>
</head>

<body>

<div class="para func">
<h2>disk_initialize</h2>
<p>ストレージ デバイスを初期化します。</p>
<pre>
DSTATUS disk_initialize (
  BYTE <span class="arg">pdrv</span>      <span class="c">/* [IN] 物理ドライブ番号 */</span>
);
</pre>
</div>

<div class="para arg">
<h4>引数</h4>
<dl class="par">
<dt>pdrv</dt>
<dd>対象のデバイスを識別する物理ドライブ番号(0-9)が指定されます。</dd>
</dl>
</div>


<div class="para ret">
<h4>戻り値</h4>
<p>この関数は戻り値としてディスク ステータスを返します。ディスク ステータスの詳細に関しては<a href="dstat.html"><tt>disk_status</tt></a>関数を参照してください。</p>
</div>

<div class="para desc">
<h4>解説</h4>
<p>ストレージ デバイスを初期化し、データの読み書きなど全ての動作が可能な状態にします。関数が成功すると、戻り値の<tt>STA_NOINIT</tt>フラグがクリアされます。</p>
<p>この関数はFatFsの管理下にあり、自動マウント動作により必要に応じて呼び出されます。<em>アプリケーションからはこの関数を呼び出してはなりません。さもないと、FATボリュームが破壊される可能性があります。再初期化が必要なときは、<tt>f_mount</tt>関数を使用してください。</em></p>
</div>

<p class="foot"><a href="../00index_j.html">戻る</a></p>
</body>
</html>
