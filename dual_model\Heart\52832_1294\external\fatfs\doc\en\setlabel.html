<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html lang="en">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=ISO-8859-1">
<meta http-equiv="Content-Style-Type" content="text/css">
<link rel="up" title="FatFs" href="../00index_e.html">
<link rel="alternate" hreflang="ja" title="Japanese" href="../ja/setlabel.html">
<link rel="stylesheet" href="../css_e.css" type="text/css" media="screen" title="ELM Default">
<title>FatFs - f_setlabel</title>
</head>

<body>

<div class="para func">
<h2>f_setlabel</h2>
<p>The f_setlabel function sets/removes the label of a volume.</p>
<pre>
FRESULT f_setlabel (
  const TCHAR* <span class="arg">label</span>  <span class="c">/* [IN] Volume label to be set */</span>
);
</pre>
</div>

<div class="para arg">
<h4>Parameters</h4>
<dl class="par">
<dt>label</dt>
<dd>Pointer to the null-terminated string that specifies the volume label to be set.</dd>
</dl>
</div>


<div class="para ret">
<h4>Return Values</h4>
<p>
<a href="rc.html#ok">FR_OK</a>,
<a href="rc.html#de">FR_DISK_ERR</a>,
<a href="rc.html#ie">FR_INT_ERR</a>,
<a href="rc.html#nr">FR_NOT_READY</a>,
<a href="rc.html#in">FR_INVALID_NAME</a>,
<a href="rc.html#wp">FR_WRITE_PROTECTED</a>,
<a href="rc.html#id">FR_INVALID_DRIVE</a>,
<a href="rc.html#ne">FR_NOT_ENABLED</a>,
<a href="rc.html#ns">FR_NO_FILESYSTEM</a>,
<a href="rc.html#tm">FR_TIMEOUT</a>
</p>
</div>


<div class="para desc">
<h4>Description</h4>
<p>When the string has a drive number, the volume label will be set to the volume specified by the drive number. If not, the volume label will be set to the default drive. If length of the given volume label is zero, the volume label on the volume will be removed. The format of the volume label on the FAT volume is similar to the file name but there are some differences shown below:</p>
<ul>
<li>Spaces can be contained anywhere in the volume label. Trailing spaces are truncated off at FAT volume.</li>
<li>Period is not allowed.</li>
<li>Up to 11 bytes long as conversion of OEM code page at FAT volume.</li>
<li>Up to 11 characters long at exFAT volume. Case information is preserved.</li>
</ul>
<p>Remark: The standard system (Windows) has a problem at handling of the volume label with a heading <tt>\xE5</tt> on the FAT volume. To avoid this problem, this function rejects such volume label as invalid name.</p>
</div>

<div class="para comp">
<h4>QuickInfo</h4>
<p>Available when <tt>_FS_READONLY == 0</tt> and <tt>_USE_LABEL == 1</tt>.</p>
</div>


<div class="para use">
<h4>Example</h4>
<pre>
    <span class="c">/* Set volume label to the default drive */</span>
    f_setlabel("DATA DISK");

    <span class="c">/* Set volume label to the drive 2 */</span>
    f_setlabel("2:DISK 3 OF 4");

    <span class="c">/* Remove volume label of the drive 2 */</span>
    f_setlabel("2:");
</pre>
</div>


<div class="para ref">
<h4>See Also</h4>
<tt><a href="getlabel.html">f_getlabel</a></tt>
</div>


<p class="foot"><a href="../00index_e.html">Return</a></p>
</body>
</html>
