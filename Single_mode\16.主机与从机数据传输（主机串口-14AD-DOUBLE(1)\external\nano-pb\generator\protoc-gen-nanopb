#!/bin/sh

# This file is used to invoke nanopb_generator.py as a plugin
# to protoc on Linux and other *nix-style systems.
# Use it like this:
# protoc --plugin=nanopb=..../protoc-gen-nanopb --nanopb_out=dir foo.proto
#
# Note that if you use the binary package of nanopb, the protoc
# path is already set up properly and there is no need to give
# --plugin= on the command line.

MYPATH=$(dirname "$0")
exec "$MYPATH/nanopb_generator.py" --protoc-plugin
