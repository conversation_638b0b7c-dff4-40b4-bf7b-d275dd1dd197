Component: ARM Compiler 5.06 update 1 (build 61) Tool: armlink [4d35a8]

==============================================================================

Section Cross References

    main.o(i.assert_nrf_callback) refers to app_error_handler_keil.o(.emb_text) for app_error_handler
    main.o(i.ble_evt_handler) refers to nrf_log_frontend.o(i.nrf_log_frontend_std_0) for nrf_log_frontend_std_0
    main.o(i.ble_evt_handler) refers to bsp.o(i.bsp_indication_set) for bsp_indication_set
    main.o(i.ble_evt_handler) refers to app_error.o(i.app_error_handler_bare) for app_error_handler_bare
    main.o(i.ble_evt_handler) refers to nrf_log_frontend.o(i.nrf_log_frontend_std_2) for nrf_log_frontend_std_2
    main.o(i.ble_evt_handler) refers to nrf_log_frontend.o(log_const_data) for m_nrf_log_app_logs_data_const
    main.o(i.bsp_event_handler) refers to nrf_pwr_mgmt.o(i.nrf_pwr_mgmt_shutdown) for nrf_pwr_mgmt_shutdown
    main.o(i.bsp_event_handler) refers to app_error.o(i.app_error_handler_bare) for app_error_handler_bare
    main.o(i.gatt_evt_handler) refers to nrf_log_frontend.o(i.nrf_log_frontend_std_0) for nrf_log_frontend_std_0
    main.o(i.gatt_evt_handler) refers to nrf_log_frontend.o(i.nrf_log_frontend_std_2) for nrf_log_frontend_std_2
    main.o(i.gatt_evt_handler) refers to nrf_log_frontend.o(log_const_data) for m_nrf_log_app_logs_data_const
    main.o(i.gatt_evt_handler) refers to main.o(.data) for .data
    main.o(i.gatt_init) refers to nrf_ble_gatt.o(i.nrf_ble_gatt_init) for nrf_ble_gatt_init
    main.o(i.gatt_init) refers to app_error.o(i.app_error_handler_bare) for app_error_handler_bare
    main.o(i.gatt_init) refers to main.o(i.gatt_evt_handler) for gatt_evt_handler
    main.o(i.gatt_init) refers to main.o(.bss) for .bss
    main.o(i.main) refers to nrf_log_frontend.o(i.nrf_log_init) for nrf_log_init
    main.o(i.main) refers to app_error.o(i.app_error_handler_bare) for app_error_handler_bare
    main.o(i.main) refers to nrf_log_default_backends.o(i.nrf_log_default_backends_init) for nrf_log_default_backends_init
    main.o(i.main) refers to app_timer2.o(i.app_timer_init) for app_timer_init
    main.o(i.main) refers to bsp.o(i.bsp_init) for bsp_init
    main.o(i.main) refers to bsp_btn_ble.o(i.bsp_btn_ble_init) for bsp_btn_ble_init
    main.o(i.main) refers to nrf_pwr_mgmt.o(i.nrf_pwr_mgmt_init) for nrf_pwr_mgmt_init
    main.o(i.main) refers to nrf_sdh.o(i.nrf_sdh_enable_request) for nrf_sdh_enable_request
    main.o(i.main) refers to nrf_sdh_ble.o(i.nrf_sdh_ble_default_cfg_set) for nrf_sdh_ble_default_cfg_set
    main.o(i.main) refers to nrf_sdh_ble.o(i.nrf_sdh_ble_enable) for nrf_sdh_ble_enable
    main.o(i.main) refers to main.o(i.gatt_init) for gatt_init
    main.o(i.main) refers to main.o(i.scan_init) for scan_init
    main.o(i.main) refers to nrf_log_frontend.o(i.nrf_log_frontend_std_0) for nrf_log_frontend_std_0
    main.o(i.main) refers to main.o(i.scan_start) for scan_start
    main.o(i.main) refers to nrf_log_frontend.o(i.nrf_log_frontend_dequeue) for nrf_log_frontend_dequeue
    main.o(i.main) refers to nrf_pwr_mgmt.o(i.nrf_pwr_mgmt_run) for nrf_pwr_mgmt_run
    main.o(i.main) refers to main.o(i.bsp_event_handler) for bsp_event_handler
    main.o(i.main) refers to nrf_log_frontend.o(log_const_data) for m_nrf_log_app_logs_data_const
    main.o(i.scan_evt_handler) refers to nrf_log_frontend.o(i.nrf_log_frontend_std_6) for nrf_log_frontend_std_6
    main.o(i.scan_evt_handler) refers to app_error.o(i.app_error_handler_bare) for app_error_handler_bare
    main.o(i.scan_evt_handler) refers to nrf_log_frontend.o(i.nrf_log_frontend_std_0) for nrf_log_frontend_std_0
    main.o(i.scan_evt_handler) refers to main.o(i.scan_start) for scan_start
    main.o(i.scan_evt_handler) refers to nrf_log_frontend.o(log_const_data) for m_nrf_log_app_logs_data_const
    main.o(i.scan_init) refers to nrf_ble_scan.o(i.nrf_ble_scan_init) for nrf_ble_scan_init
    main.o(i.scan_init) refers to app_error.o(i.app_error_handler_bare) for app_error_handler_bare
    main.o(i.scan_init) refers to nrf_ble_scan.o(i.nrf_ble_scan_filter_set) for nrf_ble_scan_filter_set
    main.o(i.scan_init) refers to nrf_ble_scan.o(i.nrf_ble_scan_filters_enable) for nrf_ble_scan_filters_enable
    main.o(i.scan_init) refers to main.o(i.scan_evt_handler) for scan_evt_handler
    main.o(i.scan_init) refers to main.o(.bss) for .bss
    main.o(i.scan_init) refers to main.o(.constdata) for .constdata
    main.o(i.scan_start) refers to nrf_ble_scan.o(i.nrf_ble_scan_start) for nrf_ble_scan_start
    main.o(i.scan_start) refers to app_error.o(i.app_error_handler_bare) for app_error_handler_bare
    main.o(i.scan_start) refers to bsp.o(i.bsp_indication_set) for bsp_indication_set
    main.o(i.scan_start) refers to main.o(.bss) for .bss
    main.o(i.shutdown_handler) refers to bsp.o(i.bsp_indication_set) for bsp_indication_set
    main.o(i.shutdown_handler) refers to app_error.o(i.app_error_handler_bare) for app_error_handler_bare
    main.o(i.shutdown_handler) refers to bsp_btn_ble.o(i.bsp_btn_ble_sleep_mode_prepare) for bsp_btn_ble_sleep_mode_prepare
    main.o(pwr_mgmt_data1) refers to main.o(i.shutdown_handler) for shutdown_handler
    main.o(sdh_ble_observers1) refers to nrf_ble_gatt.o(i.nrf_ble_gatt_on_ble_evt) for nrf_ble_gatt_on_ble_evt
    main.o(sdh_ble_observers1) refers to main.o(.bss) for m_gatt
    main.o(sdh_ble_observers1) refers to nrf_ble_scan.o(i.nrf_ble_scan_on_ble_evt) for nrf_ble_scan_on_ble_evt
    main.o(sdh_ble_observers3) refers to main.o(i.ble_evt_handler) for ble_evt_handler
    boards.o(i.bsp_board_button_idx_to_pin) refers to boards.o(.constdata) for .constdata
    boards.o(i.bsp_board_button_state_get) refers to boards.o(i.nrf_gpio_pin_port_decode) for nrf_gpio_pin_port_decode
    boards.o(i.bsp_board_button_state_get) refers to boards.o(.constdata) for .constdata
    boards.o(i.bsp_board_init) refers to boards.o(i.nrf_gpio_cfg) for nrf_gpio_cfg
    boards.o(i.bsp_board_init) refers to boards.o(i.bsp_board_leds_off) for bsp_board_leds_off
    boards.o(i.bsp_board_init) refers to boards.o(.constdata) for .constdata
    boards.o(i.bsp_board_led_idx_to_pin) refers to boards.o(.constdata) for .constdata
    boards.o(i.bsp_board_led_invert) refers to boards.o(i.nrf_gpio_pin_port_decode) for nrf_gpio_pin_port_decode
    boards.o(i.bsp_board_led_invert) refers to boards.o(.constdata) for .constdata
    boards.o(i.bsp_board_led_off) refers to boards.o(i.nrf_gpio_pin_write) for nrf_gpio_pin_write
    boards.o(i.bsp_board_led_off) refers to boards.o(.constdata) for .constdata
    boards.o(i.bsp_board_led_on) refers to boards.o(i.nrf_gpio_pin_write) for nrf_gpio_pin_write
    boards.o(i.bsp_board_led_on) refers to boards.o(.constdata) for .constdata
    boards.o(i.bsp_board_led_state_get) refers to boards.o(i.nrf_gpio_pin_port_decode) for nrf_gpio_pin_port_decode
    boards.o(i.bsp_board_led_state_get) refers to boards.o(.constdata) for .constdata
    boards.o(i.bsp_board_leds_off) refers to boards.o(i.bsp_board_led_off) for bsp_board_led_off
    boards.o(i.bsp_board_leds_on) refers to boards.o(i.bsp_board_led_on) for bsp_board_led_on
    boards.o(i.bsp_board_pin_to_button_idx) refers to boards.o(.constdata) for .constdata
    boards.o(i.bsp_board_pin_to_led_idx) refers to boards.o(.constdata) for .constdata
    boards.o(i.nrf_gpio_cfg) refers to boards.o(i.nrf_gpio_pin_port_decode) for nrf_gpio_pin_port_decode
    boards.o(i.nrf_gpio_pin_write) refers to boards.o(i.nrf_gpio_pin_port_decode) for nrf_gpio_pin_port_decode
    bsp.o(i.alert_timer_handler) refers to boards.o(i.bsp_board_led_invert) for bsp_board_led_invert
    bsp.o(i.bsp_button_event_handler) refers to boards.o(i.bsp_board_pin_to_button_idx) for bsp_board_pin_to_button_idx
    bsp.o(i.bsp_button_event_handler) refers to app_timer2.o(i.app_timer_start) for app_timer_start
    bsp.o(i.bsp_button_event_handler) refers to app_timer2.o(i.app_timer_stop) for app_timer_stop
    bsp.o(i.bsp_button_event_handler) refers to bsp.o(.constdata) for .constdata
    bsp.o(i.bsp_button_event_handler) refers to bsp.o(.data) for .data
    bsp.o(i.bsp_button_event_handler) refers to bsp.o(.bss) for .bss
    bsp.o(i.bsp_button_is_pressed) refers to boards.o(i.bsp_board_button_state_get) for bsp_board_button_state_get
    bsp.o(i.bsp_buttons_disable) refers to app_button.o(i.app_button_disable) for app_button_disable
    bsp.o(i.bsp_buttons_enable) refers to app_button.o(i.app_button_enable) for app_button_enable
    bsp.o(i.bsp_event_to_button_action_assign) refers to bsp.o(.bss) for .bss
    bsp.o(i.bsp_indication_set) refers to bsp.o(i.bsp_led_indication) for bsp_led_indication
    bsp.o(i.bsp_indication_set) refers to bsp.o(.data) for .data
    bsp.o(i.bsp_init) refers to bsp.o(i.bsp_event_to_button_action_assign) for bsp_event_to_button_action_assign
    bsp.o(i.bsp_init) refers to app_button.o(i.app_button_init) for app_button_init
    bsp.o(i.bsp_init) refers to app_button.o(i.app_button_enable) for app_button_enable
    bsp.o(i.bsp_init) refers to app_timer2.o(i.app_timer_create) for app_timer_create
    bsp.o(i.bsp_init) refers to boards.o(i.bsp_board_init) for bsp_board_init
    bsp.o(i.bsp_init) refers to bsp.o(.data) for .data
    bsp.o(i.bsp_init) refers to bsp.o(.constdata) for .constdata
    bsp.o(i.bsp_init) refers to bsp.o(i.button_timer_handler) for button_timer_handler
    bsp.o(i.bsp_init) refers to bsp.o(i.leds_timer_handler) for leds_timer_handler
    bsp.o(i.bsp_init) refers to bsp.o(i.alert_timer_handler) for alert_timer_handler
    bsp.o(i.bsp_led_indication) refers to bsp.o(i.leds_off) for leds_off
    bsp.o(i.bsp_led_indication) refers to app_timer2.o(i.app_timer_stop) for app_timer_stop
    bsp.o(i.bsp_led_indication) refers to boards.o(i.bsp_board_led_state_get) for bsp_board_led_state_get
    bsp.o(i.bsp_led_indication) refers to boards.o(i.bsp_board_led_off) for bsp_board_led_off
    bsp.o(i.bsp_led_indication) refers to boards.o(i.bsp_board_led_on) for bsp_board_led_on
    bsp.o(i.bsp_led_indication) refers to uldiv.o(.text) for __aeabi_uldivmod
    bsp.o(i.bsp_led_indication) refers to app_timer2.o(i.app_timer_start) for app_timer_start
    bsp.o(i.bsp_led_indication) refers to boards.o(i.bsp_board_led_invert) for bsp_board_led_invert
    bsp.o(i.bsp_led_indication) refers to boards.o(i.bsp_board_leds_on) for bsp_board_leds_on
    bsp.o(i.bsp_led_indication) refers to bsp.o(.data) for .data
    bsp.o(i.bsp_led_indication) refers to bsp.o(.constdata) for .constdata
    bsp.o(i.bsp_wakeup_button_disable) refers to bsp.o(i.wakeup_button_cfg) for wakeup_button_cfg
    bsp.o(i.bsp_wakeup_button_enable) refers to bsp.o(i.wakeup_button_cfg) for wakeup_button_cfg
    bsp.o(i.button_timer_handler) refers to bsp.o(i.bsp_button_event_handler) for bsp_button_event_handler
    bsp.o(i.leds_off) refers to boards.o(i.bsp_board_led_off) for bsp_board_led_off
    bsp.o(i.leds_off) refers to boards.o(i.bsp_board_leds_off) for bsp_board_leds_off
    bsp.o(i.leds_off) refers to bsp.o(.data) for .data
    bsp.o(i.leds_timer_handler) refers to bsp.o(i.bsp_led_indication) for bsp_led_indication
    bsp.o(i.leds_timer_handler) refers to bsp.o(.data) for .data
    bsp.o(i.wakeup_button_cfg) refers to boards.o(i.bsp_board_button_idx_to_pin) for bsp_board_button_idx_to_pin
    bsp.o(.constdata) refers to bsp.o(.bss) for m_bsp_leds_tmr_data
    bsp.o(.constdata) refers to bsp.o(.bss) for m_bsp_alert_tmr_data
    bsp.o(.constdata) refers to bsp.o(.bss) for m_bsp_button_tmr_data
    bsp.o(.constdata) refers to bsp.o(i.bsp_button_event_handler) for bsp_button_event_handler
    bsp_btn_ble.o(i.advertising_buttons_configure) refers to bsp.o(i.bsp_event_to_button_action_assign) for bsp_event_to_button_action_assign
    bsp_btn_ble.o(i.ble_evt_handler) refers to bsp_btn_ble.o(i.advertising_buttons_configure) for advertising_buttons_configure
    bsp_btn_ble.o(i.ble_evt_handler) refers to bsp.o(i.bsp_event_to_button_action_assign) for bsp_event_to_button_action_assign
    bsp_btn_ble.o(i.ble_evt_handler) refers to bsp_btn_ble.o(.data) for .data
    bsp_btn_ble.o(i.bsp_btn_ble_init) refers to bsp.o(i.bsp_button_is_pressed) for bsp_button_is_pressed
    bsp_btn_ble.o(i.bsp_btn_ble_init) refers to bsp_btn_ble.o(i.advertising_buttons_configure) for advertising_buttons_configure
    bsp_btn_ble.o(i.bsp_btn_ble_init) refers to bsp_btn_ble.o(.data) for .data
    bsp_btn_ble.o(i.bsp_btn_ble_sleep_mode_prepare) refers to bsp.o(i.bsp_wakeup_button_enable) for bsp_wakeup_button_enable
    bsp_btn_ble.o(sdh_ble_observers1) refers to bsp_btn_ble.o(i.ble_evt_handler) for ble_evt_handler
    utf.o(i.utf16RuneCount) refers to utf.o(i.utf16DecodeRune) for utf16DecodeRune
    utf.o(i.utf16UTF8Count) refers to utf.o(i.utf16DecodeRune) for utf16DecodeRune
    utf.o(i.utf16UTF8Count) refers to utf.o(i.utf8EncodeRune) for utf8EncodeRune
    utf.o(i.utf8RuneCount) refers to utf.o(i.utf8DecodeRune) for utf8DecodeRune
    utf.o(i.utf8UTF16Count) refers to utf.o(i.utf8DecodeRune) for utf8DecodeRune
    utf.o(i.utf8UTF16Count) refers to utf.o(i.utf16EncodeRune) for utf16EncodeRune
    ble_advdata.o(i.ble_advdata_appearance_find) refers to ble_advdata.o(i.ble_advdata_search) for ble_advdata_search
    ble_advdata.o(i.ble_advdata_encode) refers to ble_advdata.o(i.ble_device_addr_encode) for ble_device_addr_encode
    ble_advdata.o(i.ble_advdata_encode) refers to ble_advdata.o(i.uint16_encode) for uint16_encode
    ble_advdata.o(i.ble_advdata_encode) refers to ble_advdata.o(i.uuid_list_encode) for uuid_list_encode
    ble_advdata.o(i.ble_advdata_encode) refers to ble_advdata.o(i.conn_int_encode) for conn_int_encode
    ble_advdata.o(i.ble_advdata_encode) refers to ble_advdata.o(i.manuf_specific_data_encode) for manuf_specific_data_encode
    ble_advdata.o(i.ble_advdata_encode) refers to ble_advdata.o(i.service_data_encode) for service_data_encode
    ble_advdata.o(i.ble_advdata_encode) refers to ble_advdata.o(i.name_encode) for name_encode
    ble_advdata.o(i.ble_advdata_name_find) refers to ble_advdata.o(i.ble_advdata_search) for ble_advdata_search
    ble_advdata.o(i.ble_advdata_name_find) refers to strlen.o(.text) for strlen
    ble_advdata.o(i.ble_advdata_name_find) refers to memcmp.o(.text) for memcmp
    ble_advdata.o(i.ble_advdata_parse) refers to ble_advdata.o(i.ble_advdata_search) for ble_advdata_search
    ble_advdata.o(i.ble_advdata_short_name_find) refers to ble_advdata.o(i.ble_advdata_search) for ble_advdata_search
    ble_advdata.o(i.ble_advdata_short_name_find) refers to strlen.o(.text) for strlen
    ble_advdata.o(i.ble_advdata_short_name_find) refers to memcmp.o(.text) for memcmp
    ble_advdata.o(i.ble_advdata_uuid_find) refers to ble_advdata.o(i.ble_advdata_search) for ble_advdata_search
    ble_advdata.o(i.ble_advdata_uuid_find) refers to memcmp.o(.text) for memcmp
    ble_advdata.o(i.conn_int_encode) refers to ble_advdata.o(i.uint16_encode) for uint16_encode
    ble_advdata.o(i.manuf_specific_data_encode) refers to ble_advdata.o(i.uint16_encode) for uint16_encode
    ble_advdata.o(i.manuf_specific_data_encode) refers to memcpya.o(.text) for __aeabi_memcpy
    ble_advdata.o(i.service_data_encode) refers to ble_advdata.o(i.uint16_encode) for uint16_encode
    ble_advdata.o(i.service_data_encode) refers to memcpya.o(.text) for __aeabi_memcpy
    ble_advdata.o(i.uuid_list_encode) refers to ble_advdata.o(i.uuid_list_sized_encode) for uuid_list_sized_encode
    ble_db_discovery.o(i.ble_db_discovery_close) refers to ble_db_discovery.o(.data) for .data
    ble_db_discovery.o(i.ble_db_discovery_evt_register) refers to ble_db_discovery.o(i.registered_handler_get) for registered_handler_get
    ble_db_discovery.o(i.ble_db_discovery_evt_register) refers to ble_db_discovery.o(.data) for .data
    ble_db_discovery.o(i.ble_db_discovery_evt_register) refers to ble_db_discovery.o(.bss) for .bss
    ble_db_discovery.o(i.ble_db_discovery_init) refers to ble_db_discovery.o(.data) for .data
    ble_db_discovery.o(i.ble_db_discovery_on_ble_evt) refers to ble_db_discovery.o(i.on_descriptor_discovery_rsp) for on_descriptor_discovery_rsp
    ble_db_discovery.o(i.ble_db_discovery_on_ble_evt) refers to ble_db_discovery.o(i.on_primary_srv_discovery_rsp) for on_primary_srv_discovery_rsp
    ble_db_discovery.o(i.ble_db_discovery_on_ble_evt) refers to ble_db_discovery.o(i.on_characteristic_discovery_rsp) for on_characteristic_discovery_rsp
    ble_db_discovery.o(i.ble_db_discovery_on_ble_evt) refers to ble_db_discovery.o(.data) for .data
    ble_db_discovery.o(i.ble_db_discovery_start) refers to ble_db_discovery.o(i.discovery_start) for discovery_start
    ble_db_discovery.o(i.ble_db_discovery_start) refers to ble_db_discovery.o(.data) for .data
    ble_db_discovery.o(i.characteristics_discover) refers to memseta.o(.text) for __aeabi_memclr4
    ble_db_discovery.o(i.characteristics_discover) refers to nrf_ble_gq.o(i.nrf_ble_gq_item_add) for nrf_ble_gq_item_add
    ble_db_discovery.o(i.characteristics_discover) refers to ble_db_discovery.o(i.discovery_error_handler) for discovery_error_handler
    ble_db_discovery.o(i.characteristics_discover) refers to ble_db_discovery.o(.data) for .data
    ble_db_discovery.o(i.descriptors_discover) refers to memseta.o(.text) for __aeabi_memclr4
    ble_db_discovery.o(i.descriptors_discover) refers to ble_db_discovery.o(i.is_desc_discovery_reqd) for is_desc_discovery_reqd
    ble_db_discovery.o(i.descriptors_discover) refers to nrf_ble_gq.o(i.nrf_ble_gq_item_add) for nrf_ble_gq_item_add
    ble_db_discovery.o(i.descriptors_discover) refers to ble_db_discovery.o(i.discovery_error_handler) for discovery_error_handler
    ble_db_discovery.o(i.descriptors_discover) refers to ble_db_discovery.o(.data) for .data
    ble_db_discovery.o(i.discovery_available_evt_trigger) refers to memseta.o(.text) for __aeabi_memclr4
    ble_db_discovery.o(i.discovery_available_evt_trigger) refers to ble_db_discovery.o(.data) for .data
    ble_db_discovery.o(i.discovery_complete_evt_trigger) refers to ble_db_discovery.o(i.registered_handler_get) for registered_handler_get
    ble_db_discovery.o(i.discovery_complete_evt_trigger) refers to memcpya.o(.text) for __aeabi_memcpy
    ble_db_discovery.o(i.discovery_complete_evt_trigger) refers to ble_db_discovery.o(.data) for .data
    ble_db_discovery.o(i.discovery_error_handler) refers to ble_db_discovery.o(i.registered_handler_get) for registered_handler_get
    ble_db_discovery.o(i.discovery_error_handler) refers to memcpya.o(.text) for __aeabi_memcpy4
    ble_db_discovery.o(i.discovery_error_handler) refers to ble_db_discovery.o(i.discovery_available_evt_trigger) for discovery_available_evt_trigger
    ble_db_discovery.o(i.discovery_error_handler) refers to ble_db_discovery.o(.constdata) for .constdata
    ble_db_discovery.o(i.discovery_start) refers to memseta.o(.text) for __aeabi_memclr4
    ble_db_discovery.o(i.discovery_start) refers to nrf_ble_gq.o(i.nrf_ble_gq_conn_handle_register) for nrf_ble_gq_conn_handle_register
    ble_db_discovery.o(i.discovery_start) refers to nrf_ble_gq.o(i.nrf_ble_gq_item_add) for nrf_ble_gq_item_add
    ble_db_discovery.o(i.discovery_start) refers to ble_db_discovery.o(.data) for .data
    ble_db_discovery.o(i.discovery_start) refers to ble_db_discovery.o(.bss) for .bss
    ble_db_discovery.o(i.discovery_start) refers to ble_db_discovery.o(i.discovery_error_handler) for discovery_error_handler
    ble_db_discovery.o(i.on_characteristic_discovery_rsp) refers to nrf_log_frontend.o(i.nrf_log_frontend_std_1) for nrf_log_frontend_std_1
    ble_db_discovery.o(i.on_characteristic_discovery_rsp) refers to nrf_log_frontend.o(i.nrf_log_frontend_std_0) for nrf_log_frontend_std_0
    ble_db_discovery.o(i.on_characteristic_discovery_rsp) refers to ble_db_discovery.o(i.characteristics_discover) for characteristics_discover
    ble_db_discovery.o(i.on_characteristic_discovery_rsp) refers to ble_db_discovery.o(i.discovery_error_handler) for discovery_error_handler
    ble_db_discovery.o(i.on_characteristic_discovery_rsp) refers to ble_db_discovery.o(i.descriptors_discover) for descriptors_discover
    ble_db_discovery.o(i.on_characteristic_discovery_rsp) refers to ble_db_discovery.o(i.discovery_complete_evt_trigger) for discovery_complete_evt_trigger
    ble_db_discovery.o(i.on_characteristic_discovery_rsp) refers to ble_db_discovery.o(i.on_srv_disc_completion) for on_srv_disc_completion
    ble_db_discovery.o(i.on_characteristic_discovery_rsp) refers to ble_db_discovery.o(log_const_data) for log_const_data
    ble_db_discovery.o(i.on_characteristic_discovery_rsp) refers to ble_db_discovery.o(.conststring) for .conststring
    ble_db_discovery.o(i.on_descriptor_discovery_rsp) refers to ble_db_discovery.o(i.descriptors_discover) for descriptors_discover
    ble_db_discovery.o(i.on_descriptor_discovery_rsp) refers to ble_db_discovery.o(i.discovery_error_handler) for discovery_error_handler
    ble_db_discovery.o(i.on_descriptor_discovery_rsp) refers to ble_db_discovery.o(i.discovery_complete_evt_trigger) for discovery_complete_evt_trigger
    ble_db_discovery.o(i.on_descriptor_discovery_rsp) refers to ble_db_discovery.o(i.on_srv_disc_completion) for on_srv_disc_completion
    ble_db_discovery.o(i.on_primary_srv_discovery_rsp) refers to ble_db_discovery.o(i.discovery_complete_evt_trigger) for discovery_complete_evt_trigger
    ble_db_discovery.o(i.on_primary_srv_discovery_rsp) refers to ble_db_discovery.o(i.on_srv_disc_completion) for on_srv_disc_completion
    ble_db_discovery.o(i.on_primary_srv_discovery_rsp) refers to nrf_log_frontend.o(i.nrf_log_frontend_std_0) for nrf_log_frontend_std_0
    ble_db_discovery.o(i.on_primary_srv_discovery_rsp) refers to ble_db_discovery.o(i.characteristics_discover) for characteristics_discover
    ble_db_discovery.o(i.on_primary_srv_discovery_rsp) refers to ble_db_discovery.o(i.discovery_error_handler) for discovery_error_handler
    ble_db_discovery.o(i.on_primary_srv_discovery_rsp) refers to ble_db_discovery.o(log_const_data) for log_const_data
    ble_db_discovery.o(i.on_primary_srv_discovery_rsp) refers to ble_db_discovery.o(.conststring) for .conststring
    ble_db_discovery.o(i.on_srv_disc_completion) refers to memseta.o(.text) for __aeabi_memclr4
    ble_db_discovery.o(i.on_srv_disc_completion) refers to nrf_ble_gq.o(i.nrf_ble_gq_item_add) for nrf_ble_gq_item_add
    ble_db_discovery.o(i.on_srv_disc_completion) refers to ble_db_discovery.o(i.discovery_error_handler) for discovery_error_handler
    ble_db_discovery.o(i.on_srv_disc_completion) refers to ble_db_discovery.o(i.discovery_available_evt_trigger) for discovery_available_evt_trigger
    ble_db_discovery.o(i.on_srv_disc_completion) refers to ble_db_discovery.o(.data) for .data
    ble_db_discovery.o(i.on_srv_disc_completion) refers to ble_db_discovery.o(.bss) for .bss
    ble_db_discovery.o(i.registered_handler_get) refers to ble_db_discovery.o(.bss) for .bss
    ble_db_discovery.o(i.registered_handler_get) refers to ble_db_discovery.o(.data) for .data
    ble_db_discovery.o(log_const_data) refers to ble_db_discovery.o(.conststrlit) for .conststrlit
    ble_srv_common.o(i.ble_srv_ascii_to_utf8) refers to strlen.o(.text) for strlen
    ble_srv_common.o(i.characteristic_add) refers to ble_srv_common.o(i.set_security_req) for set_security_req
    ble_srv_common.o(i.characteristic_add) refers to memseta.o(.text) for __aeabi_memclr4
    ble_srv_common.o(i.descriptor_add) refers to memseta.o(.text) for __aeabi_memclr4
    ble_srv_common.o(i.descriptor_add) refers to ble_srv_common.o(i.set_security_req) for set_security_req
    nrf_ble_gatt.o(i.data_length_update) refers to nrf_strerror.o(i.nrf_strerror_get) for nrf_strerror_get
    nrf_ble_gatt.o(i.data_length_update) refers to nrf_log_frontend.o(i.nrf_log_frontend_std_2) for nrf_log_frontend_std_2
    nrf_ble_gatt.o(i.data_length_update) refers to nrf_log_frontend.o(i.nrf_log_frontend_std_1) for nrf_log_frontend_std_1
    nrf_ble_gatt.o(i.data_length_update) refers to nrf_ble_gatt.o(log_const_data) for log_const_data
    nrf_ble_gatt.o(i.data_length_update) refers to nrf_ble_gatt.o(.conststring) for .conststring
    nrf_ble_gatt.o(i.nrf_ble_gatt_data_length_set) refers to nrf_ble_gatt.o(i.data_length_update) for data_length_update
    nrf_ble_gatt.o(i.nrf_ble_gatt_init) refers to nrf_ble_gatt.o(i.link_init) for link_init
    nrf_ble_gatt.o(i.nrf_ble_gatt_on_ble_evt) refers to nrf_ble_gatt.o(i.on_connected_evt) for on_connected_evt
    nrf_ble_gatt.o(i.nrf_ble_gatt_on_ble_evt) refers to nrf_ble_gatt.o(i.link_init) for link_init
    nrf_ble_gatt.o(i.nrf_ble_gatt_on_ble_evt) refers to nrf_ble_gatt.o(i.on_exchange_mtu_request_evt) for on_exchange_mtu_request_evt
    nrf_ble_gatt.o(i.nrf_ble_gatt_on_ble_evt) refers to memcpya.o(.text) for __aeabi_memcpy4
    nrf_ble_gatt.o(i.nrf_ble_gatt_on_ble_evt) refers to nrf_ble_gatt.o(i.data_length_update) for data_length_update
    nrf_ble_gatt.o(i.nrf_ble_gatt_on_ble_evt) refers to nrf_strerror.o(i.nrf_strerror_get) for nrf_strerror_get
    nrf_ble_gatt.o(i.nrf_ble_gatt_on_ble_evt) refers to nrf_log_frontend.o(i.nrf_log_frontend_std_1) for nrf_log_frontend_std_1
    nrf_ble_gatt.o(i.nrf_ble_gatt_on_ble_evt) refers to nrf_ble_gatt.o(.constdata) for .constdata
    nrf_ble_gatt.o(i.nrf_ble_gatt_on_ble_evt) refers to nrf_ble_gatt.o(log_const_data) for log_const_data
    nrf_ble_gatt.o(i.on_connected_evt) refers to nrf_strerror.o(i.nrf_strerror_get) for nrf_strerror_get
    nrf_ble_gatt.o(i.on_connected_evt) refers to nrf_log_frontend.o(i.nrf_log_frontend_std_1) for nrf_log_frontend_std_1
    nrf_ble_gatt.o(i.on_connected_evt) refers to nrf_ble_gatt.o(i.data_length_update) for data_length_update
    nrf_ble_gatt.o(i.on_connected_evt) refers to nrf_ble_gatt.o(log_const_data) for log_const_data
    nrf_ble_gatt.o(i.on_exchange_mtu_request_evt) refers to nrf_strerror.o(i.nrf_strerror_get) for nrf_strerror_get
    nrf_ble_gatt.o(i.on_exchange_mtu_request_evt) refers to nrf_log_frontend.o(i.nrf_log_frontend_std_1) for nrf_log_frontend_std_1
    nrf_ble_gatt.o(i.on_exchange_mtu_request_evt) refers to nrf_ble_gatt.o(log_const_data) for log_const_data
    nrf_ble_gatt.o(i.on_exchange_mtu_request_evt) refers to nrf_ble_gatt.o(.constdata) for .constdata
    nrf_ble_gatt.o(log_const_data) refers to nrf_ble_gatt.o(.conststrlit) for .conststrlit
    nrf_ble_gq.o(i.gattc_write_alloc) refers to nrf_memobj.o(i.nrf_memobj_alloc) for nrf_memobj_alloc
    nrf_ble_gq.o(i.gattc_write_alloc) refers to nrf_memobj.o(i.nrf_memobj_write) for nrf_memobj_write
    nrf_ble_gq.o(i.gatts_hvx_alloc) refers to nrf_memobj.o(i.nrf_memobj_alloc) for nrf_memobj_alloc
    nrf_ble_gq.o(i.gatts_hvx_alloc) refers to nrf_memobj.o(i.nrf_memobj_write) for nrf_memobj_write
    nrf_ble_gq.o(i.nrf_ble_gq_conn_handle_register) refers to nrf_ble_gq.o(i.queues_purge) for queues_purge
    nrf_ble_gq.o(i.nrf_ble_gq_conn_handle_register) refers to nrf_ble_gq.o(i.conn_handle_id_find) for conn_handle_id_find
    nrf_ble_gq.o(i.nrf_ble_gq_conn_handle_register) refers to nrf_memobj.o(i.nrf_memobj_pool_init) for nrf_memobj_pool_init
    nrf_ble_gq.o(i.nrf_ble_gq_item_add) refers to nrf_ble_gq.o(i.queues_purge) for queues_purge
    nrf_ble_gq.o(i.nrf_ble_gq_item_add) refers to nrf_ble_gq.o(i.conn_handle_id_find) for conn_handle_id_find
    nrf_ble_gq.o(i.nrf_ble_gq_item_add) refers to nrf_queue.o(i.nrf_queue_is_empty) for nrf_queue_is_empty
    nrf_ble_gq.o(i.nrf_ble_gq_item_add) refers to nrf_ble_gq.o(i.request_process) for request_process
    nrf_ble_gq.o(i.nrf_ble_gq_item_add) refers to nrf_queue.o(i.nrf_queue_push) for nrf_queue_push
    nrf_ble_gq.o(i.nrf_ble_gq_item_add) refers to nrf_memobj.o(i.nrf_memobj_free) for nrf_memobj_free
    nrf_ble_gq.o(i.nrf_ble_gq_item_add) refers to nrf_ble_gq.o(i.queue_process) for queue_process
    nrf_ble_gq.o(i.nrf_ble_gq_item_add) refers to nrf_ble_gq.o(.constdata) for .constdata
    nrf_ble_gq.o(i.nrf_ble_gq_on_ble_evt) refers to nrf_ble_gq.o(i.conn_handle_id_find) for conn_handle_id_find
    nrf_ble_gq.o(i.nrf_ble_gq_on_ble_evt) refers to nrf_ble_gq.o(i.queue_process) for queue_process
    nrf_ble_gq.o(i.nrf_ble_gq_on_ble_evt) refers to nrf_queue.o(i.nrf_queue_push) for nrf_queue_push
    nrf_ble_gq.o(i.queue_process) refers to nrf_queue.o(i.nrf_queue_generic_pop) for nrf_queue_generic_pop
    nrf_ble_gq.o(i.queue_process) refers to nrf_memobj.o(i.nrf_memobj_read) for nrf_memobj_read
    nrf_ble_gq.o(i.queue_process) refers to nrf_memobj.o(i.nrf_memobj_free) for nrf_memobj_free
    nrf_ble_gq.o(i.queue_process) refers to nrf_ble_gq.o(i.request_err_code_handle) for request_err_code_handle
    nrf_ble_gq.o(i.queue_process) refers to nrf_log_frontend.o(i.nrf_log_frontend_std_0) for nrf_log_frontend_std_0
    nrf_ble_gq.o(i.queue_process) refers to nrf_ble_gq.o(.constdata) for .constdata
    nrf_ble_gq.o(i.queue_process) refers to nrf_ble_gq.o(log_const_data) for log_const_data
    nrf_ble_gq.o(i.queues_purge) refers to nrf_queue.o(i.nrf_queue_generic_pop) for nrf_queue_generic_pop
    nrf_ble_gq.o(i.queues_purge) refers to nrf_memobj.o(i.nrf_memobj_free) for nrf_memobj_free
    nrf_ble_gq.o(i.queues_purge) refers to nrf_ble_gq.o(.constdata) for .constdata
    nrf_ble_gq.o(i.request_err_code_handle) refers to nrf_log_frontend.o(i.nrf_log_frontend_std_3) for nrf_log_frontend_std_3
    nrf_ble_gq.o(i.request_err_code_handle) refers to nrf_ble_gq.o(log_const_data) for log_const_data
    nrf_ble_gq.o(i.request_err_code_handle) refers to nrf_ble_gq.o(.conststring) for .conststring
    nrf_ble_gq.o(i.request_process) refers to nrf_log_frontend.o(i.nrf_log_frontend_std_0) for nrf_log_frontend_std_0
    nrf_ble_gq.o(i.request_process) refers to nrf_ble_gq.o(i.request_err_code_handle) for request_err_code_handle
    nrf_ble_gq.o(i.request_process) refers to nrf_ble_gq.o(log_const_data) for log_const_data
    nrf_ble_gq.o(.constdata) refers to nrf_ble_gq.o(i.gattc_write_alloc) for gattc_write_alloc
    nrf_ble_gq.o(.constdata) refers to nrf_ble_gq.o(i.gatts_hvx_alloc) for gatts_hvx_alloc
    nrf_ble_gq.o(log_const_data) refers to nrf_ble_gq.o(.conststrlit) for .conststrlit
    nrf_ble_scan.o(i.nrf_ble_scan_connect_with_target) refers to memseta.o(.text) for __aeabi_memclr4
    nrf_ble_scan.o(i.nrf_ble_scan_filters_enable) refers to nrf_ble_scan.o(i.nrf_ble_scan_filters_disable) for nrf_ble_scan_filters_disable
    nrf_ble_scan.o(i.nrf_ble_scan_init) refers to memcpya.o(.text) for __aeabi_memcpy
    nrf_ble_scan.o(i.nrf_ble_scan_init) refers to nrf_ble_scan.o(i.nrf_ble_scan_default_param_set) for nrf_ble_scan_default_param_set
    nrf_ble_scan.o(i.nrf_ble_scan_init) refers to nrf_ble_scan.o(i.nrf_ble_scan_default_conn_param_set) for nrf_ble_scan_default_conn_param_set
    nrf_ble_scan.o(i.nrf_ble_scan_on_adv_report) refers to memseta.o(.text) for __aeabi_memclr4
    nrf_ble_scan.o(i.nrf_ble_scan_on_adv_report) refers to nrf_ble_scan.o(i.is_whitelist_used) for is_whitelist_used
    nrf_ble_scan.o(i.nrf_ble_scan_on_adv_report) refers to nrf_ble_scan.o(i.nrf_ble_scan_connect_with_target) for nrf_ble_scan_connect_with_target
    nrf_ble_scan.o(i.nrf_ble_scan_on_adv_report) refers to ble_advdata.o(i.ble_advdata_uuid_find) for ble_advdata_uuid_find
    nrf_ble_scan.o(i.nrf_ble_scan_on_ble_evt) refers to nrf_ble_scan.o(i.nrf_ble_scan_on_adv_report) for nrf_ble_scan_on_adv_report
    nrf_ble_scan.o(i.nrf_ble_scan_on_ble_evt) refers to memseta.o(.text) for __aeabi_memclr4
    nrf_ble_scan.o(i.nrf_ble_scan_params_set) refers to memcpya.o(.text) for __aeabi_memcpy
    nrf_ble_scan.o(i.nrf_ble_scan_params_set) refers to nrf_ble_scan.o(i.nrf_ble_scan_default_param_set) for nrf_ble_scan_default_param_set
    nrf_ble_scan.o(i.nrf_ble_scan_start) refers to memseta.o(.text) for __aeabi_memclr4
    nrf_ble_scan.o(i.nrf_ble_scan_start) refers to nrf_ble_scan.o(i.is_whitelist_used) for is_whitelist_used
    nrf_ble_scan.o(i.nrf_ble_scan_start) refers to nrf_log_frontend.o(i.nrf_log_frontend_std_1) for nrf_log_frontend_std_1
    nrf_ble_scan.o(i.nrf_ble_scan_start) refers to nrf_ble_scan.o(log_const_data) for log_const_data
    nrf_ble_scan.o(log_const_data) refers to nrf_ble_scan.o(.conststrlit) for .conststrlit
    nrf_drv_clock.o(i.clock_clk_started_notify) refers to nrf_drv_clock.o(.bss) for .bss
    nrf_drv_clock.o(i.clock_irq_handler) refers to nrf_drv_clock.o(i.clock_clk_started_notify) for clock_clk_started_notify
    nrf_drv_clock.o(i.clock_irq_handler) refers to nrf_drv_clock.o(.bss) for .bss
    nrf_drv_clock.o(i.nrf_drv_clock_hfclk_is_running) refers to nrf_sdh.o(i.nrf_sdh_is_enabled) for nrf_sdh_is_enabled
    nrf_drv_clock.o(i.nrf_drv_clock_hfclk_release) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    nrf_drv_clock.o(i.nrf_drv_clock_hfclk_release) refers to nrf_sdh.o(i.nrf_sdh_is_enabled) for nrf_sdh_is_enabled
    nrf_drv_clock.o(i.nrf_drv_clock_hfclk_release) refers to nrfx_clock.o(i.nrfx_clock_hfclk_stop) for nrfx_clock_hfclk_stop
    nrf_drv_clock.o(i.nrf_drv_clock_hfclk_release) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    nrf_drv_clock.o(i.nrf_drv_clock_hfclk_release) refers to nrf_drv_clock.o(.bss) for .bss
    nrf_drv_clock.o(i.nrf_drv_clock_hfclk_request) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    nrf_drv_clock.o(i.nrf_drv_clock_hfclk_request) refers to nrf_drv_clock.o(i.item_enqueue) for item_enqueue
    nrf_drv_clock.o(i.nrf_drv_clock_hfclk_request) refers to nrf_sdh.o(i.nrf_sdh_is_enabled) for nrf_sdh_is_enabled
    nrf_drv_clock.o(i.nrf_drv_clock_hfclk_request) refers to nrfx_clock.o(i.nrfx_clock_hfclk_start) for nrfx_clock_hfclk_start
    nrf_drv_clock.o(i.nrf_drv_clock_hfclk_request) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    nrf_drv_clock.o(i.nrf_drv_clock_hfclk_request) refers to nrf_drv_clock.o(.bss) for .bss
    nrf_drv_clock.o(i.nrf_drv_clock_init) refers to nrfx_clock.o(i.nrfx_clock_init) for nrfx_clock_init
    nrf_drv_clock.o(i.nrf_drv_clock_init) refers to nrf_sdh.o(i.nrf_sdh_is_enabled) for nrf_sdh_is_enabled
    nrf_drv_clock.o(i.nrf_drv_clock_init) refers to nrfx_clock.o(i.nrfx_clock_enable) for nrfx_clock_enable
    nrf_drv_clock.o(i.nrf_drv_clock_init) refers to nrf_drv_clock.o(i.nrf_wdt_started) for nrf_wdt_started
    nrf_drv_clock.o(i.nrf_drv_clock_init) refers to nrf_drv_clock.o(.bss) for .bss
    nrf_drv_clock.o(i.nrf_drv_clock_init) refers to nrf_drv_clock.o(i.clock_irq_handler) for clock_irq_handler
    nrf_drv_clock.o(i.nrf_drv_clock_init_check) refers to nrf_drv_clock.o(.bss) for .bss
    nrf_drv_clock.o(i.nrf_drv_clock_lfclk_is_running) refers to nrf_sdh.o(i.nrf_sdh_is_enabled) for nrf_sdh_is_enabled
    nrf_drv_clock.o(i.nrf_drv_clock_lfclk_release) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    nrf_drv_clock.o(i.nrf_drv_clock_lfclk_release) refers to nrf_drv_clock.o(i.nrf_wdt_started) for nrf_wdt_started
    nrf_drv_clock.o(i.nrf_drv_clock_lfclk_release) refers to nrfx_clock.o(i.nrfx_clock_lfclk_stop) for nrfx_clock_lfclk_stop
    nrf_drv_clock.o(i.nrf_drv_clock_lfclk_release) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    nrf_drv_clock.o(i.nrf_drv_clock_lfclk_release) refers to nrf_drv_clock.o(.bss) for .bss
    nrf_drv_clock.o(i.nrf_drv_clock_lfclk_request) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    nrf_drv_clock.o(i.nrf_drv_clock_lfclk_request) refers to nrf_drv_clock.o(i.item_enqueue) for item_enqueue
    nrf_drv_clock.o(i.nrf_drv_clock_lfclk_request) refers to nrfx_clock.o(i.nrfx_clock_lfclk_start) for nrfx_clock_lfclk_start
    nrf_drv_clock.o(i.nrf_drv_clock_lfclk_request) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    nrf_drv_clock.o(i.nrf_drv_clock_lfclk_request) refers to nrf_drv_clock.o(.bss) for .bss
    nrf_drv_clock.o(i.nrf_drv_clock_uninit) refers to nrfx_clock.o(i.nrfx_clock_disable) for nrfx_clock_disable
    nrf_drv_clock.o(i.nrf_drv_clock_uninit) refers to nrfx_clock.o(i.nrfx_clock_uninit) for nrfx_clock_uninit
    nrf_drv_clock.o(i.nrf_drv_clock_uninit) refers to nrf_drv_clock.o(.bss) for .bss
    nrf_drv_clock.o(i.sd_state_evt_handler) refers to nrfx_clock.o(i.nrfx_clock_enable) for nrfx_clock_enable
    nrf_drv_clock.o(i.sd_state_evt_handler) refers to nrf_drv_clock.o(i.nrf_drv_clock_lfclk_release) for nrf_drv_clock_lfclk_release
    nrf_drv_clock.o(i.sd_state_evt_handler) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    nrf_drv_clock.o(i.sd_state_evt_handler) refers to nrf_drv_clock.o(i.nrf_drv_clock_init) for nrf_drv_clock_init
    nrf_drv_clock.o(i.sd_state_evt_handler) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    nrf_drv_clock.o(i.sd_state_evt_handler) refers to nrf_drv_clock.o(.bss) for .bss
    nrf_drv_clock.o(i.soc_evt_handler) refers to nrf_drv_clock.o(i.clock_clk_started_notify) for clock_clk_started_notify
    nrf_drv_clock.o(i.soc_evt_handler) refers to nrf_drv_clock.o(.bss) for .bss
    nrf_drv_clock.o(log_const_data) refers to nrf_drv_clock.o(.conststrlit) for .conststrlit
    nrf_drv_clock.o(sdh_soc_observers0) refers to nrf_drv_clock.o(i.soc_evt_handler) for soc_evt_handler
    nrf_drv_clock.o(sdh_state_observers0) refers to nrf_drv_clock.o(i.sd_state_evt_handler) for sd_state_evt_handler
    nrf_drv_uart.o(i.nrf_drv_uart_init) refers to memcpya.o(.text) for __aeabi_memcpy4
    nrf_drv_uart.o(i.nrf_drv_uart_init) refers to nrfx_uarte.o(i.nrfx_uarte_init) for nrfx_uarte_init
    nrf_drv_uart.o(i.nrf_drv_uart_init) refers to nrfx_uart.o(i.nrfx_uart_init) for nrfx_uart_init
    nrf_drv_uart.o(i.nrf_drv_uart_init) refers to nrf_drv_uart.o(.data) for .data
    nrf_drv_uart.o(i.nrf_drv_uart_init) refers to nrf_drv_uart.o(i.uarte_evt_handler) for uarte_evt_handler
    nrf_drv_uart.o(i.nrf_drv_uart_init) refers to nrf_drv_uart.o(i.uart_evt_handler) for uart_evt_handler
    nrf_drv_uart.o(i.uart_evt_handler) refers to nrf_drv_uart.o(.data) for .data
    nrf_drv_uart.o(i.uarte_evt_handler) refers to nrf_drv_uart.o(.data) for .data
    nrfx_atomic.o(i.nrfx_atomic_flag_clear) refers to nrfx_atomic.o(i.nrfx_atomic_u32_and) for nrfx_atomic_u32_and
    nrfx_atomic.o(i.nrfx_atomic_flag_clear_fetch) refers to nrfx_atomic.o(i.nrfx_atomic_u32_fetch_and) for nrfx_atomic_u32_fetch_and
    nrfx_atomic.o(i.nrfx_atomic_flag_set) refers to nrfx_atomic.o(i.nrfx_atomic_u32_or) for nrfx_atomic_u32_or
    nrfx_atomic.o(i.nrfx_atomic_flag_set_fetch) refers to nrfx_atomic.o(i.nrfx_atomic_u32_fetch_or) for nrfx_atomic_u32_fetch_or
    nrfx_atomic.o(i.nrfx_atomic_u32_add) refers to nrfx_atomic.o(.emb_text) for __asm___13_nrfx_atomic_c_3bd32246__nrfx_atomic_internal_add
    nrfx_atomic.o(i.nrfx_atomic_u32_and) refers to nrfx_atomic.o(.emb_text) for __asm___13_nrfx_atomic_c_3bd32246__nrfx_atomic_internal_and
    nrfx_atomic.o(i.nrfx_atomic_u32_cmp_exch) refers to nrfx_atomic.o(.emb_text) for __asm___13_nrfx_atomic_c_3bd32246__nrfx_atomic_internal_cmp_exch
    nrfx_atomic.o(i.nrfx_atomic_u32_fetch_add) refers to nrfx_atomic.o(.emb_text) for __asm___13_nrfx_atomic_c_3bd32246__nrfx_atomic_internal_add
    nrfx_atomic.o(i.nrfx_atomic_u32_fetch_and) refers to nrfx_atomic.o(.emb_text) for __asm___13_nrfx_atomic_c_3bd32246__nrfx_atomic_internal_and
    nrfx_atomic.o(i.nrfx_atomic_u32_fetch_or) refers to nrfx_atomic.o(.emb_text) for __asm___13_nrfx_atomic_c_3bd32246__nrfx_atomic_internal_orr
    nrfx_atomic.o(i.nrfx_atomic_u32_fetch_store) refers to nrfx_atomic.o(.emb_text) for __asm___13_nrfx_atomic_c_3bd32246__nrfx_atomic_internal_mov
    nrfx_atomic.o(i.nrfx_atomic_u32_fetch_sub) refers to nrfx_atomic.o(.emb_text) for __asm___13_nrfx_atomic_c_3bd32246__nrfx_atomic_internal_sub
    nrfx_atomic.o(i.nrfx_atomic_u32_fetch_sub_hs) refers to nrfx_atomic.o(.emb_text) for __asm___13_nrfx_atomic_c_3bd32246__nrfx_atomic_internal_sub_hs
    nrfx_atomic.o(i.nrfx_atomic_u32_fetch_xor) refers to nrfx_atomic.o(.emb_text) for __asm___13_nrfx_atomic_c_3bd32246__nrfx_atomic_internal_eor
    nrfx_atomic.o(i.nrfx_atomic_u32_or) refers to nrfx_atomic.o(.emb_text) for __asm___13_nrfx_atomic_c_3bd32246__nrfx_atomic_internal_orr
    nrfx_atomic.o(i.nrfx_atomic_u32_store) refers to nrfx_atomic.o(.emb_text) for __asm___13_nrfx_atomic_c_3bd32246__nrfx_atomic_internal_mov
    nrfx_atomic.o(i.nrfx_atomic_u32_sub) refers to nrfx_atomic.o(.emb_text) for __asm___13_nrfx_atomic_c_3bd32246__nrfx_atomic_internal_sub
    nrfx_atomic.o(i.nrfx_atomic_u32_sub_hs) refers to nrfx_atomic.o(.emb_text) for __asm___13_nrfx_atomic_c_3bd32246__nrfx_atomic_internal_sub_hs
    nrfx_atomic.o(i.nrfx_atomic_u32_xor) refers to nrfx_atomic.o(.emb_text) for __asm___13_nrfx_atomic_c_3bd32246__nrfx_atomic_internal_eor
    nrfx_clock.o(i.POWER_CLOCK_IRQHandler) refers to nrfx_clock.o(i.nrf_clock_event_check) for nrf_clock_event_check
    nrfx_clock.o(i.POWER_CLOCK_IRQHandler) refers to nrfx_clock.o(i.nrf_clock_event_clear) for nrf_clock_event_clear
    nrfx_clock.o(i.POWER_CLOCK_IRQHandler) refers to nrfx_clock.o(.data) for .data
    nrfx_clock.o(i.nrfx_clock_hfclk_start) refers to nrfx_clock.o(i.nrf_clock_event_clear) for nrf_clock_event_clear
    nrfx_clock.o(i.nrfx_clock_hfclk_stop) refers to nrfx_clock.o(i.nrf_clock_event_clear) for nrf_clock_event_clear
    nrfx_clock.o(i.nrfx_clock_hfclk_stop) refers to nrfx_clock.o(.data) for .data
    nrfx_clock.o(i.nrfx_clock_init) refers to nrfx_clock.o(.data) for .data
    nrfx_clock.o(i.nrfx_clock_lfclk_start) refers to nrfx_clock.o(i.nrf_clock_event_clear) for nrf_clock_event_clear
    nrfx_clock.o(i.nrfx_clock_lfclk_stop) refers to nrfx_clock.o(i.nrf_clock_event_clear) for nrf_clock_event_clear
    nrfx_clock.o(i.nrfx_clock_uninit) refers to nrfx_clock.o(i.nrfx_clock_lfclk_stop) for nrfx_clock_lfclk_stop
    nrfx_clock.o(i.nrfx_clock_uninit) refers to nrfx_clock.o(i.nrfx_clock_hfclk_stop) for nrfx_clock_hfclk_stop
    nrfx_clock.o(i.nrfx_clock_uninit) refers to nrfx_clock.o(.data) for .data
    nrfx_clock.o(log_const_data) refers to nrfx_clock.o(.conststrlit) for .conststrlit
    nrfx_gpiote.o(i.GPIOTE_IRQHandler) refers to nrfx_gpiote.o(i.nrf_gpiote_event_is_set) for nrf_gpiote_event_is_set
    nrfx_gpiote.o(i.GPIOTE_IRQHandler) refers to nrfx_gpiote.o(i.nrf_gpiote_event_clear) for nrf_gpiote_event_clear
    nrfx_gpiote.o(i.GPIOTE_IRQHandler) refers to nrfx_gpiote.o(i.nrf_gpio_latches_read_and_clear) for nrf_gpio_latches_read_and_clear
    nrfx_gpiote.o(i.GPIOTE_IRQHandler) refers to nrfx_gpiote.o(i.port_event_handle) for port_event_handle
    nrfx_gpiote.o(i.GPIOTE_IRQHandler) refers to nrfx_gpiote.o(.bss) for .bss
    nrfx_gpiote.o(i.channel_free) refers to nrfx_gpiote.o(.bss) for .bss
    nrfx_gpiote.o(i.channel_port_alloc) refers to nrfx_gpiote.o(.bss) for .bss
    nrfx_gpiote.o(i.channel_port_get) refers to nrfx_gpiote.o(.bss) for .bss
    nrfx_gpiote.o(i.nrf_gpio_cfg) refers to nrfx_gpiote.o(i.nrf_gpio_pin_port_decode) for nrf_gpio_pin_port_decode
    nrfx_gpiote.o(i.nrf_gpio_cfg_default) refers to nrfx_gpiote.o(i.nrf_gpio_cfg) for nrf_gpio_cfg
    nrfx_gpiote.o(i.nrf_gpio_cfg_sense_set) refers to nrfx_gpiote.o(i.nrf_gpio_pin_port_decode) for nrf_gpio_pin_port_decode
    nrfx_gpiote.o(i.nrf_gpio_latches_read_and_clear) refers to nrfx_gpiote.o(.constdata) for .constdata
    nrfx_gpiote.o(i.nrf_gpio_pin_clear) refers to nrfx_gpiote.o(i.nrf_gpio_pin_port_decode) for nrf_gpio_pin_port_decode
    nrfx_gpiote.o(i.nrf_gpio_pin_read) refers to nrfx_gpiote.o(i.nrf_gpio_pin_port_decode) for nrf_gpio_pin_port_decode
    nrfx_gpiote.o(i.nrf_gpio_pin_set) refers to nrfx_gpiote.o(i.nrf_gpio_pin_port_decode) for nrf_gpio_pin_port_decode
    nrfx_gpiote.o(i.nrfx_gpiote_clr_task_addr_get) refers to nrfx_gpiote.o(i.nrfx_gpiote_clr_task_get) for nrfx_gpiote_clr_task_get
    nrfx_gpiote.o(i.nrfx_gpiote_clr_task_get) refers to nrfx_gpiote.o(i.channel_port_get) for channel_port_get
    nrfx_gpiote.o(i.nrfx_gpiote_clr_task_trigger) refers to nrfx_gpiote.o(i.channel_port_get) for channel_port_get
    nrfx_gpiote.o(i.nrfx_gpiote_in_event_addr_get) refers to nrfx_gpiote.o(i.nrfx_gpiote_in_event_get) for nrfx_gpiote_in_event_get
    nrfx_gpiote.o(i.nrfx_gpiote_in_event_disable) refers to nrfx_gpiote.o(i.pin_in_use_by_port) for pin_in_use_by_port
    nrfx_gpiote.o(i.nrfx_gpiote_in_event_disable) refers to nrfx_gpiote.o(i.nrf_gpio_cfg_sense_set) for nrf_gpio_cfg_sense_set
    nrfx_gpiote.o(i.nrfx_gpiote_in_event_disable) refers to nrfx_gpiote.o(i.pin_in_use_by_te) for pin_in_use_by_te
    nrfx_gpiote.o(i.nrfx_gpiote_in_event_disable) refers to nrfx_gpiote.o(i.channel_port_get) for channel_port_get
    nrfx_gpiote.o(i.nrfx_gpiote_in_event_enable) refers to nrfx_gpiote.o(i.pin_in_use_by_port) for pin_in_use_by_port
    nrfx_gpiote.o(i.nrfx_gpiote_in_event_enable) refers to nrfx_gpiote.o(i.channel_port_get) for channel_port_get
    nrfx_gpiote.o(i.nrfx_gpiote_in_event_enable) refers to nrfx_gpiote.o(i.port_handler_polarity_get) for port_handler_polarity_get
    nrfx_gpiote.o(i.nrfx_gpiote_in_event_enable) refers to nrfx_gpiote.o(i.nrf_gpio_pin_read) for nrf_gpio_pin_read
    nrfx_gpiote.o(i.nrfx_gpiote_in_event_enable) refers to nrfx_gpiote.o(i.nrf_gpio_cfg_sense_set) for nrf_gpio_cfg_sense_set
    nrfx_gpiote.o(i.nrfx_gpiote_in_event_enable) refers to nrfx_gpiote.o(i.pin_in_use_by_te) for pin_in_use_by_te
    nrfx_gpiote.o(i.nrfx_gpiote_in_event_enable) refers to nrfx_gpiote.o(i.nrf_gpiote_event_clear) for nrf_gpiote_event_clear
    nrfx_gpiote.o(i.nrfx_gpiote_in_event_enable) refers to nrfx_gpiote.o(.bss) for .bss
    nrfx_gpiote.o(i.nrfx_gpiote_in_event_get) refers to nrfx_gpiote.o(i.pin_in_use_by_te) for pin_in_use_by_te
    nrfx_gpiote.o(i.nrfx_gpiote_in_event_get) refers to nrfx_gpiote.o(i.channel_port_get) for channel_port_get
    nrfx_gpiote.o(i.nrfx_gpiote_in_init) refers to nrfx_gpiote.o(i.channel_port_alloc) for channel_port_alloc
    nrfx_gpiote.o(i.nrfx_gpiote_in_init) refers to nrfx_gpiote.o(i.nrf_gpio_pin_port_decode) for nrf_gpio_pin_port_decode
    nrfx_gpiote.o(i.nrfx_gpiote_in_init) refers to nrfx_gpiote.o(i.nrf_gpio_cfg) for nrf_gpio_cfg
    nrfx_gpiote.o(i.nrfx_gpiote_in_init) refers to nrfx_gpiote.o(i.pin_configured_set) for pin_configured_set
    nrfx_gpiote.o(i.nrfx_gpiote_in_init) refers to nrfx_gpiote.o(.bss) for .bss
    nrfx_gpiote.o(i.nrfx_gpiote_in_is_set) refers to nrfx_gpiote.o(i.nrf_gpio_pin_read) for nrf_gpio_pin_read
    nrfx_gpiote.o(i.nrfx_gpiote_in_uninit) refers to nrfx_gpiote.o(i.nrfx_gpiote_in_event_disable) for nrfx_gpiote_in_event_disable
    nrfx_gpiote.o(i.nrfx_gpiote_in_uninit) refers to nrfx_gpiote.o(i.pin_in_use_by_te) for pin_in_use_by_te
    nrfx_gpiote.o(i.nrfx_gpiote_in_uninit) refers to nrfx_gpiote.o(i.channel_port_get) for channel_port_get
    nrfx_gpiote.o(i.nrfx_gpiote_in_uninit) refers to nrfx_gpiote.o(i.pin_configured_check) for pin_configured_check
    nrfx_gpiote.o(i.nrfx_gpiote_in_uninit) refers to nrfx_gpiote.o(i.nrf_gpio_cfg_default) for nrf_gpio_cfg_default
    nrfx_gpiote.o(i.nrfx_gpiote_in_uninit) refers to nrfx_gpiote.o(i.pin_configured_clear) for pin_configured_clear
    nrfx_gpiote.o(i.nrfx_gpiote_in_uninit) refers to nrfx_gpiote.o(i.channel_free) for channel_free
    nrfx_gpiote.o(i.nrfx_gpiote_in_uninit) refers to nrfx_gpiote.o(.bss) for .bss
    nrfx_gpiote.o(i.nrfx_gpiote_init) refers to nrfx_gpiote.o(i.nrf_gpio_pin_present_check) for nrf_gpio_pin_present_check
    nrfx_gpiote.o(i.nrfx_gpiote_init) refers to nrfx_gpiote.o(i.channel_free) for channel_free
    nrfx_gpiote.o(i.nrfx_gpiote_init) refers to nrfx_gpiote.o(i.nrf_gpiote_event_clear) for nrf_gpiote_event_clear
    nrfx_gpiote.o(i.nrfx_gpiote_init) refers to nrfx_gpiote.o(.bss) for .bss
    nrfx_gpiote.o(i.nrfx_gpiote_is_init) refers to nrfx_gpiote.o(.bss) for .bss
    nrfx_gpiote.o(i.nrfx_gpiote_out_clear) refers to nrfx_gpiote.o(i.nrf_gpio_pin_clear) for nrf_gpio_pin_clear
    nrfx_gpiote.o(i.nrfx_gpiote_out_init) refers to nrfx_gpiote.o(i.channel_port_alloc) for channel_port_alloc
    nrfx_gpiote.o(i.nrfx_gpiote_out_init) refers to nrfx_gpiote.o(i.nrf_gpio_pin_clear) for nrf_gpio_pin_clear
    nrfx_gpiote.o(i.nrfx_gpiote_out_init) refers to nrfx_gpiote.o(i.nrf_gpio_cfg) for nrf_gpio_cfg
    nrfx_gpiote.o(i.nrfx_gpiote_out_init) refers to nrfx_gpiote.o(i.pin_configured_set) for pin_configured_set
    nrfx_gpiote.o(i.nrfx_gpiote_out_init) refers to nrfx_gpiote.o(i.nrf_gpio_pin_set) for nrf_gpio_pin_set
    nrfx_gpiote.o(i.nrfx_gpiote_out_init) refers to nrfx_gpiote.o(.bss) for .bss
    nrfx_gpiote.o(i.nrfx_gpiote_out_set) refers to nrfx_gpiote.o(i.nrf_gpio_pin_set) for nrf_gpio_pin_set
    nrfx_gpiote.o(i.nrfx_gpiote_out_task_addr_get) refers to nrfx_gpiote.o(i.nrfx_gpiote_out_task_get) for nrfx_gpiote_out_task_get
    nrfx_gpiote.o(i.nrfx_gpiote_out_task_disable) refers to nrfx_gpiote.o(.bss) for .bss
    nrfx_gpiote.o(i.nrfx_gpiote_out_task_enable) refers to nrfx_gpiote.o(.bss) for .bss
    nrfx_gpiote.o(i.nrfx_gpiote_out_task_force) refers to nrfx_gpiote.o(.bss) for .bss
    nrfx_gpiote.o(i.nrfx_gpiote_out_task_get) refers to nrfx_gpiote.o(i.channel_port_get) for channel_port_get
    nrfx_gpiote.o(i.nrfx_gpiote_out_task_trigger) refers to nrfx_gpiote.o(i.channel_port_get) for channel_port_get
    nrfx_gpiote.o(i.nrfx_gpiote_out_toggle) refers to nrfx_gpiote.o(i.nrf_gpio_pin_port_decode) for nrf_gpio_pin_port_decode
    nrfx_gpiote.o(i.nrfx_gpiote_out_uninit) refers to nrfx_gpiote.o(i.pin_in_use_by_te) for pin_in_use_by_te
    nrfx_gpiote.o(i.nrfx_gpiote_out_uninit) refers to nrfx_gpiote.o(i.channel_port_get) for channel_port_get
    nrfx_gpiote.o(i.nrfx_gpiote_out_uninit) refers to nrfx_gpiote.o(i.channel_free) for channel_free
    nrfx_gpiote.o(i.nrfx_gpiote_out_uninit) refers to nrfx_gpiote.o(i.pin_configured_check) for pin_configured_check
    nrfx_gpiote.o(i.nrfx_gpiote_out_uninit) refers to nrfx_gpiote.o(i.nrf_gpio_cfg_default) for nrf_gpio_cfg_default
    nrfx_gpiote.o(i.nrfx_gpiote_out_uninit) refers to nrfx_gpiote.o(i.pin_configured_clear) for pin_configured_clear
    nrfx_gpiote.o(i.nrfx_gpiote_out_uninit) refers to nrfx_gpiote.o(.bss) for .bss
    nrfx_gpiote.o(i.nrfx_gpiote_set_task_addr_get) refers to nrfx_gpiote.o(i.nrfx_gpiote_set_task_get) for nrfx_gpiote_set_task_get
    nrfx_gpiote.o(i.nrfx_gpiote_set_task_get) refers to nrfx_gpiote.o(i.channel_port_get) for channel_port_get
    nrfx_gpiote.o(i.nrfx_gpiote_set_task_trigger) refers to nrfx_gpiote.o(i.channel_port_get) for channel_port_get
    nrfx_gpiote.o(i.nrfx_gpiote_uninit) refers to nrfx_gpiote.o(i.nrf_gpio_pin_present_check) for nrf_gpio_pin_present_check
    nrfx_gpiote.o(i.nrfx_gpiote_uninit) refers to nrfx_gpiote.o(i.nrfx_gpiote_in_uninit) for nrfx_gpiote_in_uninit
    nrfx_gpiote.o(i.nrfx_gpiote_uninit) refers to nrfx_gpiote.o(i.nrfx_gpiote_out_uninit) for nrfx_gpiote_out_uninit
    nrfx_gpiote.o(i.nrfx_gpiote_uninit) refers to nrfx_gpiote.o(.bss) for .bss
    nrfx_gpiote.o(i.pin_configured_check) refers to nrfx_gpiote.o(i.nrf_bitmask_bit_is_set) for nrf_bitmask_bit_is_set
    nrfx_gpiote.o(i.pin_configured_check) refers to nrfx_gpiote.o(.bss) for .bss
    nrfx_gpiote.o(i.pin_configured_clear) refers to nrfx_gpiote.o(.bss) for .bss
    nrfx_gpiote.o(i.pin_configured_set) refers to nrfx_gpiote.o(.bss) for .bss
    nrfx_gpiote.o(i.pin_in_use_by_port) refers to nrfx_gpiote.o(.bss) for .bss
    nrfx_gpiote.o(i.pin_in_use_by_te) refers to nrfx_gpiote.o(.bss) for .bss
    nrfx_gpiote.o(i.port_event_handle) refers to nrfx_gpiote.o(i.nrf_bitmask_bit_is_set) for nrf_bitmask_bit_is_set
    nrfx_gpiote.o(i.port_event_handle) refers to nrfx_gpiote.o(i.port_handler_polarity_get) for port_handler_polarity_get
    nrfx_gpiote.o(i.port_event_handle) refers to nrfx_gpiote.o(i.nrf_gpio_pin_port_decode) for nrf_gpio_pin_port_decode
    nrfx_gpiote.o(i.port_event_handle) refers to nrfx_gpiote.o(i.nrf_gpio_cfg_sense_set) for nrf_gpio_cfg_sense_set
    nrfx_gpiote.o(i.port_event_handle) refers to nrfx_gpiote.o(i.channel_port_get) for channel_port_get
    nrfx_gpiote.o(i.port_event_handle) refers to nrfx_gpiote.o(i.nrf_gpio_latches_read_and_clear) for nrf_gpio_latches_read_and_clear
    nrfx_gpiote.o(i.port_event_handle) refers to nrfx_gpiote.o(.bss) for .bss
    nrfx_gpiote.o(i.port_handler_polarity_get) refers to nrfx_gpiote.o(.bss) for .bss
    nrfx_gpiote.o(log_const_data) refers to nrfx_gpiote.o(.conststrlit) for .conststrlit
    nrfx_prs.o(i.UARTE0_UART0_IRQHandler) refers to nrfx_prs.o(.data) for .data
    nrfx_prs.o(i.nrfx_prs_acquire) refers to nrfx_prs.o(i.prs_box_get) for prs_box_get
    nrfx_prs.o(i.nrfx_prs_acquire) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    nrfx_prs.o(i.nrfx_prs_acquire) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    nrfx_prs.o(i.nrfx_prs_release) refers to nrfx_prs.o(i.prs_box_get) for prs_box_get
    nrfx_prs.o(i.prs_box_get) refers to nrfx_prs.o(.data) for .data
    nrfx_prs.o(log_const_data) refers to nrfx_prs.o(.conststrlit) for .conststrlit
    nrfx_uart.o(i.apply_config) refers to nrfx_uart.o(i.nrf_gpio_pin_set) for nrf_gpio_pin_set
    nrfx_uart.o(i.apply_config) refers to nrfx_uart.o(i.nrf_gpio_cfg_output) for nrf_gpio_cfg_output
    nrfx_uart.o(i.apply_config) refers to nrfx_uart.o(i.nrf_gpio_cfg_input) for nrf_gpio_cfg_input
    nrfx_uart.o(i.nrf_gpio_cfg) refers to nrfx_uart.o(i.nrf_gpio_pin_port_decode) for nrf_gpio_pin_port_decode
    nrfx_uart.o(i.nrf_gpio_cfg_default) refers to nrfx_uart.o(i.nrf_gpio_cfg) for nrf_gpio_cfg
    nrfx_uart.o(i.nrf_gpio_cfg_input) refers to nrfx_uart.o(i.nrf_gpio_cfg) for nrf_gpio_cfg
    nrfx_uart.o(i.nrf_gpio_cfg_output) refers to nrfx_uart.o(i.nrf_gpio_cfg) for nrf_gpio_cfg
    nrfx_uart.o(i.nrf_gpio_pin_set) refers to nrfx_uart.o(i.nrf_gpio_pin_port_decode) for nrf_gpio_pin_port_decode
    nrfx_uart.o(i.nrfx_uart_0_irq_handler) refers to nrfx_uart.o(i.uart_irq_handler) for uart_irq_handler
    nrfx_uart.o(i.nrfx_uart_0_irq_handler) refers to nrfx_uart.o(.bss) for .bss
    nrfx_uart.o(i.nrfx_uart_errorsrc_get) refers to nrfx_uart.o(i.nrf_uart_event_clear) for nrf_uart_event_clear
    nrfx_uart.o(i.nrfx_uart_init) refers to nrfx_prs.o(i.nrfx_prs_acquire) for nrfx_prs_acquire
    nrfx_uart.o(i.nrfx_uart_init) refers to nrfx_uart.o(i.apply_config) for apply_config
    nrfx_uart.o(i.nrfx_uart_init) refers to nrfx_uart.o(i.nrf_uart_event_clear) for nrf_uart_event_clear
    nrfx_uart.o(i.nrfx_uart_init) refers to nrfx_uart.o(.bss) for .bss
    nrfx_uart.o(i.nrfx_uart_init) refers to nrfx_uart.o(.constdata) for .constdata
    nrfx_uart.o(i.nrfx_uart_rx) refers to nrfx_uart.o(i.rx_enable) for rx_enable
    nrfx_uart.o(i.nrfx_uart_rx) refers to nrfx_uart.o(i.nrf_uart_event_clear) for nrf_uart_event_clear
    nrfx_uart.o(i.nrfx_uart_rx) refers to nrfx_uart.o(i.nrf_uart_event_check) for nrf_uart_event_check
    nrfx_uart.o(i.nrfx_uart_rx) refers to nrfx_uart.o(i.rx_byte) for rx_byte
    nrfx_uart.o(i.nrfx_uart_rx) refers to nrfx_uart.o(.bss) for .bss
    nrfx_uart.o(i.nrfx_uart_rx_disable) refers to nrfx_uart.o(.bss) for .bss
    nrfx_uart.o(i.nrfx_uart_rx_enable) refers to nrfx_uart.o(i.rx_enable) for rx_enable
    nrfx_uart.o(i.nrfx_uart_rx_enable) refers to nrfx_uart.o(.bss) for .bss
    nrfx_uart.o(i.nrfx_uart_tx) refers to nrfx_uart.o(i.nrfx_uart_tx_in_progress) for nrfx_uart_tx_in_progress
    nrfx_uart.o(i.nrfx_uart_tx) refers to nrfx_uart.o(i.nrf_uart_event_clear) for nrf_uart_event_clear
    nrfx_uart.o(i.nrfx_uart_tx) refers to nrfx_uart.o(i.tx_byte) for tx_byte
    nrfx_uart.o(i.nrfx_uart_tx) refers to nrfx_uart.o(i.nrf_uart_event_check) for nrf_uart_event_check
    nrfx_uart.o(i.nrfx_uart_tx) refers to nrfx_uart.o(.bss) for .bss
    nrfx_uart.o(i.nrfx_uart_tx_abort) refers to nrfx_uart.o(i.tx_done_event) for tx_done_event
    nrfx_uart.o(i.nrfx_uart_tx_abort) refers to nrfx_uart.o(.bss) for .bss
    nrfx_uart.o(i.nrfx_uart_tx_in_progress) refers to nrfx_uart.o(.bss) for .bss
    nrfx_uart.o(i.nrfx_uart_uninit) refers to nrfx_uart.o(i.nrf_gpio_cfg_default) for nrf_gpio_cfg_default
    nrfx_uart.o(i.nrfx_uart_uninit) refers to nrfx_prs.o(i.nrfx_prs_release) for nrfx_prs_release
    nrfx_uart.o(i.nrfx_uart_uninit) refers to nrfx_uart.o(.bss) for .bss
    nrfx_uart.o(i.rx_byte) refers to nrfx_uart.o(i.nrf_uart_event_clear) for nrf_uart_event_clear
    nrfx_uart.o(i.rx_enable) refers to nrfx_uart.o(i.nrf_uart_event_clear) for nrf_uart_event_clear
    nrfx_uart.o(i.tx_byte) refers to nrfx_uart.o(i.nrf_uart_event_clear) for nrf_uart_event_clear
    nrfx_uart.o(i.uart_irq_handler) refers to nrfx_uart.o(i.nrf_uart_int_enable_check) for nrf_uart_int_enable_check
    nrfx_uart.o(i.uart_irq_handler) refers to nrfx_uart.o(i.nrf_uart_event_check) for nrf_uart_event_check
    nrfx_uart.o(i.uart_irq_handler) refers to nrfx_uart.o(i.nrf_uart_event_clear) for nrf_uart_event_clear
    nrfx_uart.o(i.uart_irq_handler) refers to nrfx_uart.o(i.rx_byte) for rx_byte
    nrfx_uart.o(i.uart_irq_handler) refers to nrfx_uart.o(i.rx_done_event) for rx_done_event
    nrfx_uart.o(i.uart_irq_handler) refers to nrfx_uart.o(i.tx_done_event) for tx_done_event
    nrfx_uart.o(i.uart_irq_handler) refers to nrfx_uart.o(i.tx_byte) for tx_byte
    nrfx_uart.o(.constdata) refers to nrfx_uart.o(i.nrfx_uart_0_irq_handler) for nrfx_uart_0_irq_handler
    nrfx_uart.o(log_const_data) refers to nrfx_uart.o(.conststrlit) for .conststrlit
    nrfx_uarte.o(i.apply_config) refers to nrfx_uarte.o(i.nrf_gpio_pin_set) for nrf_gpio_pin_set
    nrfx_uarte.o(i.apply_config) refers to nrfx_uarte.o(i.nrf_gpio_cfg_output) for nrf_gpio_cfg_output
    nrfx_uarte.o(i.apply_config) refers to nrfx_uarte.o(i.nrf_gpio_cfg_input) for nrf_gpio_cfg_input
    nrfx_uarte.o(i.interrupts_enable) refers to nrfx_uarte.o(i.nrf_uarte_event_clear) for nrf_uarte_event_clear
    nrfx_uarte.o(i.nrf_gpio_cfg) refers to nrfx_uarte.o(i.nrf_gpio_pin_port_decode) for nrf_gpio_pin_port_decode
    nrfx_uarte.o(i.nrf_gpio_cfg_default) refers to nrfx_uarte.o(i.nrf_gpio_cfg) for nrf_gpio_cfg
    nrfx_uarte.o(i.nrf_gpio_cfg_input) refers to nrfx_uarte.o(i.nrf_gpio_cfg) for nrf_gpio_cfg
    nrfx_uarte.o(i.nrf_gpio_cfg_output) refers to nrfx_uarte.o(i.nrf_gpio_cfg) for nrf_gpio_cfg
    nrfx_uarte.o(i.nrf_gpio_pin_set) refers to nrfx_uarte.o(i.nrf_gpio_pin_port_decode) for nrf_gpio_pin_port_decode
    nrfx_uarte.o(i.nrfx_uarte_0_irq_handler) refers to nrfx_uarte.o(i.uarte_irq_handler) for uarte_irq_handler
    nrfx_uarte.o(i.nrfx_uarte_0_irq_handler) refers to nrfx_uarte.o(.bss) for .bss
    nrfx_uarte.o(i.nrfx_uarte_errorsrc_get) refers to nrfx_uarte.o(i.nrf_uarte_event_clear) for nrf_uarte_event_clear
    nrfx_uarte.o(i.nrfx_uarte_init) refers to nrfx_prs.o(i.nrfx_prs_acquire) for nrfx_prs_acquire
    nrfx_uarte.o(i.nrfx_uarte_init) refers to nrfx_uarte.o(i.apply_config) for apply_config
    nrfx_uarte.o(i.nrfx_uarte_init) refers to nrfx_uarte.o(i.interrupts_enable) for interrupts_enable
    nrfx_uarte.o(i.nrfx_uarte_init) refers to nrfx_uarte.o(.bss) for .bss
    nrfx_uarte.o(i.nrfx_uarte_init) refers to nrfx_uarte.o(.constdata) for .constdata
    nrfx_uarte.o(i.nrfx_uarte_rx) refers to nrfx_uarte.o(i.nrfx_is_in_ram) for nrfx_is_in_ram
    nrfx_uarte.o(i.nrfx_uarte_rx) refers to nrfx_uarte.o(i.nrf_uarte_event_clear) for nrf_uarte_event_clear
    nrfx_uarte.o(i.nrfx_uarte_rx) refers to nrfx_uarte.o(i.nrf_uarte_event_check) for nrf_uarte_event_check
    nrfx_uarte.o(i.nrfx_uarte_rx) refers to nrfx_uarte.o(.bss) for .bss
    nrfx_uarte.o(i.nrfx_uarte_rx_abort) refers to nrfx_uarte.o(.bss) for .bss
    nrfx_uarte.o(i.nrfx_uarte_tx) refers to nrfx_uarte.o(i.nrfx_is_in_ram) for nrfx_is_in_ram
    nrfx_uarte.o(i.nrfx_uarte_tx) refers to nrfx_uarte.o(i.nrfx_uarte_tx_in_progress) for nrfx_uarte_tx_in_progress
    nrfx_uarte.o(i.nrfx_uarte_tx) refers to nrfx_uarte.o(i.nrf_uarte_event_clear) for nrf_uarte_event_clear
    nrfx_uarte.o(i.nrfx_uarte_tx) refers to nrfx_uarte.o(i.nrf_uarte_event_check) for nrf_uarte_event_check
    nrfx_uarte.o(i.nrfx_uarte_tx) refers to nrfx_uarte.o(.bss) for .bss
    nrfx_uarte.o(i.nrfx_uarte_tx_abort) refers to nrfx_uarte.o(i.nrf_uarte_event_clear) for nrf_uarte_event_clear
    nrfx_uarte.o(i.nrfx_uarte_tx_abort) refers to nrfx_uarte.o(i.nrf_uarte_event_check) for nrf_uarte_event_check
    nrfx_uarte.o(i.nrfx_uarte_tx_abort) refers to nrfx_uarte.o(.bss) for .bss
    nrfx_uarte.o(i.nrfx_uarte_tx_in_progress) refers to nrfx_uarte.o(.bss) for .bss
    nrfx_uarte.o(i.nrfx_uarte_uninit) refers to nrfx_uarte.o(i.nrf_uarte_event_clear) for nrf_uarte_event_clear
    nrfx_uarte.o(i.nrfx_uarte_uninit) refers to nrfx_uarte.o(i.nrf_uarte_event_check) for nrf_uarte_event_check
    nrfx_uarte.o(i.nrfx_uarte_uninit) refers to nrfx_uarte.o(i.nrf_gpio_cfg_default) for nrf_gpio_cfg_default
    nrfx_uarte.o(i.nrfx_uarte_uninit) refers to nrfx_prs.o(i.nrfx_prs_release) for nrfx_prs_release
    nrfx_uarte.o(i.nrfx_uarte_uninit) refers to nrfx_uarte.o(.bss) for .bss
    nrfx_uarte.o(i.uarte_irq_handler) refers to nrfx_uarte.o(i.nrf_uarte_event_check) for nrf_uarte_event_check
    nrfx_uarte.o(i.uarte_irq_handler) refers to nrfx_uarte.o(i.nrf_uarte_event_clear) for nrf_uarte_event_clear
    nrfx_uarte.o(i.uarte_irq_handler) refers to nrfx_uarte.o(i.rx_done_event) for rx_done_event
    nrfx_uarte.o(i.uarte_irq_handler) refers to nrfx_uarte.o(i.tx_done_event) for tx_done_event
    nrfx_uarte.o(.constdata) refers to nrfx_uarte.o(i.nrfx_uarte_0_irq_handler) for nrfx_uarte_0_irq_handler
    nrfx_uarte.o(log_const_data) refers to nrfx_uarte.o(.conststrlit) for .conststrlit
    app_button.o(i.app_button_disable) refers to nrfx_gpiote.o(i.nrfx_gpiote_in_event_disable) for nrfx_gpiote_in_event_disable
    app_button.o(i.app_button_disable) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    app_button.o(i.app_button_disable) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    app_button.o(i.app_button_disable) refers to app_timer2.o(i.app_timer_stop) for app_timer_stop
    app_button.o(i.app_button_disable) refers to app_button.o(.data) for .data
    app_button.o(i.app_button_disable) refers to app_button.o(.constdata) for .constdata
    app_button.o(i.app_button_enable) refers to nrfx_gpiote.o(i.nrfx_gpiote_in_event_enable) for nrfx_gpiote_in_event_enable
    app_button.o(i.app_button_enable) refers to app_button.o(.data) for .data
    app_button.o(i.app_button_init) refers to nrfx_gpiote.o(i.nrfx_gpiote_is_init) for nrfx_gpiote_is_init
    app_button.o(i.app_button_init) refers to nrfx_gpiote.o(i.nrfx_gpiote_init) for nrfx_gpiote_init
    app_button.o(i.app_button_init) refers to memseta.o(.text) for __aeabi_memclr
    app_button.o(i.app_button_init) refers to nrfx_gpiote.o(i.nrfx_gpiote_in_init) for nrfx_gpiote_in_init
    app_button.o(i.app_button_init) refers to app_timer2.o(i.app_timer_create) for app_timer_create
    app_button.o(i.app_button_init) refers to app_button.o(.data) for .data
    app_button.o(i.app_button_init) refers to app_button.o(.bss) for .bss
    app_button.o(i.app_button_init) refers to app_button.o(.constdata) for .constdata
    app_button.o(i.app_button_init) refers to app_button.o(i.gpiote_event_handler) for gpiote_event_handler
    app_button.o(i.app_button_init) refers to app_button.o(i.detection_delay_timeout_handler) for detection_delay_timeout_handler
    app_button.o(i.app_button_is_pushed) refers to nrfx_gpiote.o(i.nrfx_gpiote_in_is_set) for nrfx_gpiote_in_is_set
    app_button.o(i.app_button_is_pushed) refers to app_button.o(.data) for .data
    app_button.o(i.button_get) refers to app_button.o(.data) for .data
    app_button.o(i.detection_delay_timeout_handler) refers to nrfx_gpiote.o(i.nrfx_gpiote_in_is_set) for nrfx_gpiote_in_is_set
    app_button.o(i.detection_delay_timeout_handler) refers to app_button.o(i.evt_handle) for evt_handle
    app_button.o(i.detection_delay_timeout_handler) refers to app_button.o(i.timer_start) for timer_start
    app_button.o(i.detection_delay_timeout_handler) refers to app_button.o(.data) for .data
    app_button.o(i.evt_handle) refers to llshl.o(.text) for __aeabi_llsl
    app_button.o(i.evt_handle) refers to app_button.o(i.state_set) for state_set
    app_button.o(i.evt_handle) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    app_button.o(i.evt_handle) refers to app_button.o(i.usr_event) for usr_event
    app_button.o(i.evt_handle) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    app_button.o(i.evt_handle) refers to app_button.o(.bss) for .bss
    app_button.o(i.evt_handle) refers to app_button.o(.data) for .data
    app_button.o(i.gpiote_event_handler) refers to app_button.o(i.button_get) for button_get
    app_button.o(i.gpiote_event_handler) refers to nrfx_gpiote.o(i.nrfx_gpiote_in_is_set) for nrfx_gpiote_in_is_set
    app_button.o(i.gpiote_event_handler) refers to app_button.o(i.timer_start) for timer_start
    app_button.o(i.gpiote_event_handler) refers to app_button.o(.data) for .data
    app_button.o(i.state_set) refers to app_button.o(.bss) for .bss
    app_button.o(i.timer_start) refers to app_timer2.o(i.app_timer_start) for app_timer_start
    app_button.o(i.timer_start) refers to app_button.o(.data) for .data
    app_button.o(i.timer_start) refers to app_button.o(.constdata) for .constdata
    app_button.o(i.usr_event) refers to app_button.o(i.button_get) for button_get
    app_button.o(.constdata) refers to app_button.o(.bss) for m_detection_delay_timer_id_data
    app_button.o(log_const_data) refers to app_button.o(.conststrlit) for .conststrlit
    app_error.o(i.app_error_handler_bare) refers to app_error_weak.o(i.app_error_fault_handler) for app_error_fault_handler
    app_error.o(i.app_error_save_and_stop) refers to app_error.o(.bss) for .bss
    app_error_handler_keil.o(.emb_text) refers to app_error_weak.o(i.app_error_fault_handler) for app_error_fault_handler
    app_error_weak.o(i.app_error_fault_handler) refers to nrf_log_frontend.o(i.nrf_log_panic) for nrf_log_panic
    app_error_weak.o(i.app_error_fault_handler) refers to nrf_log_frontend.o(i.nrf_log_frontend_dequeue) for nrf_log_frontend_dequeue
    app_error_weak.o(i.app_error_fault_handler) refers to nrf_log_frontend.o(i.nrf_log_frontend_std_0) for nrf_log_frontend_std_0
    app_error_weak.o(i.app_error_fault_handler) refers to nrf_log_frontend.o(log_const_data) for m_nrf_log_app_logs_data_const
    app_fifo.o(i.app_fifo_get) refers to app_fifo.o(i.fifo_get) for fifo_get
    app_fifo.o(i.app_fifo_put) refers to app_fifo.o(i.fifo_put) for fifo_put
    app_fifo.o(i.app_fifo_read) refers to app_fifo.o(i.fifo_get) for fifo_get
    app_fifo.o(i.app_fifo_write) refers to app_fifo.o(i.fifo_put) for fifo_put
    app_scheduler.o(i.app_sched_event_put) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    app_scheduler.o(i.app_sched_event_put) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    app_scheduler.o(i.app_sched_event_put) refers to memcpya.o(.text) for __aeabi_memcpy
    app_scheduler.o(i.app_sched_event_put) refers to app_scheduler.o(.data) for .data
    app_scheduler.o(i.app_sched_execute) refers to app_scheduler.o(.data) for .data
    app_scheduler.o(i.app_sched_init) refers to app_scheduler.o(.data) for .data
    app_scheduler.o(i.app_sched_queue_space_get) refers to app_scheduler.o(.data) for .data
    app_timer2.o(i.app_timer_cnt_get) refers to drv_rtc.o(i.drv_rtc_counter_get) for drv_rtc_counter_get
    app_timer2.o(i.app_timer_cnt_get) refers to app_timer2.o(.data) for .data
    app_timer2.o(i.app_timer_init) refers to nrf_atfifo.o(i.nrf_atfifo_init) for nrf_atfifo_init
    app_timer2.o(i.app_timer_init) refers to drv_rtc.o(i.drv_rtc_init) for drv_rtc_init
    app_timer2.o(i.app_timer_init) refers to drv_rtc.o(i.drv_rtc_overflow_enable) for drv_rtc_overflow_enable
    app_timer2.o(i.app_timer_init) refers to drv_rtc.o(i.drv_rtc_compare_set) for drv_rtc_compare_set
    app_timer2.o(i.app_timer_init) refers to app_timer2.o(.constdata) for .constdata
    app_timer2.o(i.app_timer_init) refers to app_timer2.o(.bss) for .bss
    app_timer2.o(i.app_timer_init) refers to app_timer2.o(i.rtc_irq) for rtc_irq
    app_timer2.o(i.app_timer_init) refers to app_timer2.o(.data) for .data
    app_timer2.o(i.app_timer_pause) refers to drv_rtc.o(i.drv_rtc_stop) for drv_rtc_stop
    app_timer2.o(i.app_timer_pause) refers to app_timer2.o(.data) for .data
    app_timer2.o(i.app_timer_resume) refers to drv_rtc.o(i.drv_rtc_start) for drv_rtc_start
    app_timer2.o(i.app_timer_resume) refers to app_timer2.o(.data) for .data
    app_timer2.o(i.app_timer_start) refers to app_timer2.o(i.get_now) for get_now
    app_timer2.o(i.app_timer_start) refers to app_timer2.o(i.timer_req_schedule) for timer_req_schedule
    app_timer2.o(i.app_timer_stop) refers to app_timer2.o(i.timer_req_schedule) for timer_req_schedule
    app_timer2.o(i.app_timer_stop_all) refers to app_timer2.o(i.timer_req_schedule) for timer_req_schedule
    app_timer2.o(i.app_timer_stop_all) refers to app_timer2.o(.data) for .data
    app_timer2.o(i.get_now) refers to drv_rtc.o(i.drv_rtc_counter_get) for drv_rtc_counter_get
    app_timer2.o(i.get_now) refers to app_timer2.o(.data) for .data
    app_timer2.o(i.rtc_irq) refers to drv_rtc.o(i.drv_rtc_overflow_pending) for drv_rtc_overflow_pending
    app_timer2.o(i.rtc_irq) refers to drv_rtc.o(i.drv_rtc_compare_pending) for drv_rtc_compare_pending
    app_timer2.o(i.rtc_irq) refers to app_timer2.o(i.timer_expire) for timer_expire
    app_timer2.o(i.rtc_irq) refers to app_timer2.o(i.get_now) for get_now
    app_timer2.o(i.rtc_irq) refers to app_timer2.o(i.timer_req_process) for timer_req_process
    app_timer2.o(i.rtc_irq) refers to app_timer2.o(i.rtc_update) for rtc_update
    app_timer2.o(i.rtc_irq) refers to app_timer2.o(.data) for .data
    app_timer2.o(i.rtc_schedule) refers to app_timer2.o(i.get_now) for get_now
    app_timer2.o(i.rtc_schedule) refers to app_timer2.o(i.app_timer_cnt_get) for app_timer_cnt_get
    app_timer2.o(i.rtc_schedule) refers to drv_rtc.o(i.drv_rtc_windowed_compare_set) for drv_rtc_windowed_compare_set
    app_timer2.o(i.rtc_schedule) refers to drv_rtc.o(i.drv_rtc_compare_disable) for drv_rtc_compare_disable
    app_timer2.o(i.rtc_schedule) refers to app_timer2.o(i.timer_expire) for timer_expire
    app_timer2.o(i.rtc_schedule) refers to app_timer2.o(.data) for .data
    app_timer2.o(i.rtc_update) refers to nrf_sortlist.o(i.nrf_sortlist_peek) for nrf_sortlist_peek
    app_timer2.o(i.rtc_update) refers to nrf_sortlist.o(i.nrf_sortlist_add) for nrf_sortlist_add
    app_timer2.o(i.rtc_update) refers to app_timer2.o(i.sortlist_pop) for sortlist_pop
    app_timer2.o(i.rtc_update) refers to app_timer2.o(i.rtc_schedule) for rtc_schedule
    app_timer2.o(i.rtc_update) refers to drv_rtc.o(i.drv_rtc_stop) for drv_rtc_stop
    app_timer2.o(i.rtc_update) refers to drv_rtc.o(i.drv_rtc_start) for drv_rtc_start
    app_timer2.o(i.rtc_update) refers to app_timer2.o(.data) for .data
    app_timer2.o(i.rtc_update) refers to app_timer2.o(.constdata) for .constdata
    app_timer2.o(i.sortlist_pop) refers to nrf_sortlist.o(i.nrf_sortlist_pop) for nrf_sortlist_pop
    app_timer2.o(i.sortlist_pop) refers to app_timer2.o(.constdata) for .constdata
    app_timer2.o(i.timer_expire) refers to app_timer2.o(i.get_now) for get_now
    app_timer2.o(i.timer_expire) refers to nrf_sortlist.o(i.nrf_sortlist_add) for nrf_sortlist_add
    app_timer2.o(i.timer_expire) refers to app_timer2.o(.data) for .data
    app_timer2.o(i.timer_expire) refers to app_timer2.o(.constdata) for .constdata
    app_timer2.o(i.timer_req_process) refers to nrf_atfifo.o(i.nrf_atfifo_item_get) for nrf_atfifo_item_get
    app_timer2.o(i.timer_req_process) refers to nrf_sortlist.o(i.nrf_sortlist_add) for nrf_sortlist_add
    app_timer2.o(i.timer_req_process) refers to nrf_sortlist.o(i.nrf_sortlist_remove) for nrf_sortlist_remove
    app_timer2.o(i.timer_req_process) refers to app_timer2.o(i.sortlist_pop) for sortlist_pop
    app_timer2.o(i.timer_req_process) refers to nrf_atfifo.o(i.nrf_atfifo_item_free) for nrf_atfifo_item_free
    app_timer2.o(i.timer_req_process) refers to app_timer2.o(.constdata) for .constdata
    app_timer2.o(i.timer_req_process) refers to app_timer2.o(.data) for .data
    app_timer2.o(i.timer_req_schedule) refers to nrf_atfifo.o(i.nrf_atfifo_item_alloc) for nrf_atfifo_item_alloc
    app_timer2.o(i.timer_req_schedule) refers to nrf_atfifo.o(i.nrf_atfifo_item_put) for nrf_atfifo_item_put
    app_timer2.o(i.timer_req_schedule) refers to drv_rtc.o(i.drv_rtc_irq_trigger) for drv_rtc_irq_trigger
    app_timer2.o(i.timer_req_schedule) refers to app_timer2.o(.constdata) for .constdata
    app_timer2.o(i.timer_req_schedule) refers to app_timer2.o(.data) for .data
    app_timer2.o(.constdata) refers to app_timer2.o(.bss) for m_req_fifo_inst
    app_timer2.o(.constdata) refers to app_timer2.o(.data) for m_app_timer_sortlist_sortlist_cb
    app_timer2.o(.constdata) refers to app_timer2.o(i.compare_func) for compare_func
    app_timer2.o(log_const_data) refers to app_timer2.o(.conststrlit) for .conststrlit
    app_uart_fifo.o(i.app_uart_close) refers to nrfx_uarte.o(i.nrfx_uarte_uninit) for nrfx_uarte_uninit
    app_uart_fifo.o(i.app_uart_close) refers to nrfx_uart.o(i.nrfx_uart_uninit) for nrfx_uart_uninit
    app_uart_fifo.o(i.app_uart_close) refers to app_uart_fifo.o(.data) for .data
    app_uart_fifo.o(i.app_uart_close) refers to nrf_drv_uart.o(.data) for nrf_drv_uart_use_easy_dma
    app_uart_fifo.o(i.app_uart_flush) refers to app_fifo.o(i.app_fifo_flush) for app_fifo_flush
    app_uart_fifo.o(i.app_uart_flush) refers to app_uart_fifo.o(.bss) for .bss
    app_uart_fifo.o(i.app_uart_get) refers to app_fifo.o(i.app_fifo_get) for app_fifo_get
    app_uart_fifo.o(i.app_uart_get) refers to app_uart_fifo.o(i.nrf_drv_uart_rx) for nrf_drv_uart_rx
    app_uart_fifo.o(i.app_uart_get) refers to app_error.o(i.app_error_handler_bare) for app_error_handler_bare
    app_uart_fifo.o(i.app_uart_get) refers to app_uart_fifo.o(.data) for .data
    app_uart_fifo.o(i.app_uart_get) refers to app_uart_fifo.o(.bss) for .bss
    app_uart_fifo.o(i.app_uart_init) refers to app_fifo.o(i.app_fifo_init) for app_fifo_init
    app_uart_fifo.o(i.app_uart_init) refers to memcpya.o(.text) for __aeabi_memcpy4
    app_uart_fifo.o(i.app_uart_init) refers to nrf_drv_uart.o(i.nrf_drv_uart_init) for nrf_drv_uart_init
    app_uart_fifo.o(i.app_uart_init) refers to app_uart_fifo.o(i.nrf_drv_uart_rx) for nrf_drv_uart_rx
    app_uart_fifo.o(i.app_uart_init) refers to app_uart_fifo.o(.data) for .data
    app_uart_fifo.o(i.app_uart_init) refers to app_uart_fifo.o(.bss) for .bss
    app_uart_fifo.o(i.app_uart_init) refers to app_uart_fifo.o(.constdata) for .constdata
    app_uart_fifo.o(i.app_uart_init) refers to app_uart_fifo.o(i.uart_event_handler) for uart_event_handler
    app_uart_fifo.o(i.app_uart_put) refers to app_fifo.o(i.app_fifo_put) for app_fifo_put
    app_uart_fifo.o(i.app_uart_put) refers to nrfx_uarte.o(i.nrfx_uarte_tx_in_progress) for nrfx_uarte_tx_in_progress
    app_uart_fifo.o(i.app_uart_put) refers to nrfx_uart.o(i.nrfx_uart_tx_in_progress) for nrfx_uart_tx_in_progress
    app_uart_fifo.o(i.app_uart_put) refers to app_fifo.o(i.app_fifo_get) for app_fifo_get
    app_uart_fifo.o(i.app_uart_put) refers to app_uart_fifo.o(i.nrf_drv_uart_tx) for nrf_drv_uart_tx
    app_uart_fifo.o(i.app_uart_put) refers to app_uart_fifo.o(.bss) for .bss
    app_uart_fifo.o(i.app_uart_put) refers to app_uart_fifo.o(.data) for .data
    app_uart_fifo.o(i.app_uart_put) refers to nrf_drv_uart.o(.data) for nrf_drv_uart_use_easy_dma
    app_uart_fifo.o(i.nrf_drv_uart_rx) refers to nrfx_uarte.o(i.nrfx_uarte_rx) for nrfx_uarte_rx
    app_uart_fifo.o(i.nrf_drv_uart_rx) refers to nrfx_uart.o(i.nrfx_uart_rx) for nrfx_uart_rx
    app_uart_fifo.o(i.nrf_drv_uart_rx) refers to nrf_drv_uart.o(.data) for nrf_drv_uart_use_easy_dma
    app_uart_fifo.o(i.nrf_drv_uart_tx) refers to nrfx_uarte.o(i.nrfx_uarte_tx) for nrfx_uarte_tx
    app_uart_fifo.o(i.nrf_drv_uart_tx) refers to nrfx_uart.o(i.nrfx_uart_tx) for nrfx_uart_tx
    app_uart_fifo.o(i.nrf_drv_uart_tx) refers to nrf_drv_uart.o(.data) for nrf_drv_uart_use_easy_dma
    app_uart_fifo.o(i.uart_event_handler) refers to app_uart_fifo.o(i.nrf_drv_uart_rx) for nrf_drv_uart_rx
    app_uart_fifo.o(i.uart_event_handler) refers to app_fifo.o(i.app_fifo_put) for app_fifo_put
    app_uart_fifo.o(i.uart_event_handler) refers to app_fifo.o(i.app_fifo_get) for app_fifo_get
    app_uart_fifo.o(i.uart_event_handler) refers to app_uart_fifo.o(i.nrf_drv_uart_tx) for nrf_drv_uart_tx
    app_uart_fifo.o(i.uart_event_handler) refers to app_uart_fifo.o(.data) for .data
    app_uart_fifo.o(i.uart_event_handler) refers to app_uart_fifo.o(.bss) for .bss
    app_util_platform.o(i.app_util_critical_region_enter) refers to app_util_platform.o(.bss) for .bss
    app_util_platform.o(i.app_util_critical_region_exit) refers to app_util_platform.o(.bss) for .bss
    app_util_platform.o(i.app_util_disable_irq) refers to app_util_platform.o(.data) for .data
    app_util_platform.o(i.app_util_enable_irq) refers to app_util_platform.o(.data) for .data
    drv_rtc.o(i.RTC1_IRQHandler) refers to drv_rtc.o(.data) for .data
    drv_rtc.o(i.drv_rtc_compare_enable) refers to drv_rtc.o(i.evt_enable) for evt_enable
    drv_rtc.o(i.drv_rtc_compare_pending) refers to drv_rtc.o(i.evt_pending) for evt_pending
    drv_rtc.o(i.drv_rtc_compare_set) refers to drv_rtc.o(i.nrf_rtc_event_clear) for nrf_rtc_event_clear
    drv_rtc.o(i.drv_rtc_init) refers to nrf_log_frontend.o(i.nrf_log_frontend_std_0) for nrf_log_frontend_std_0
    drv_rtc.o(i.drv_rtc_init) refers to drv_rtc.o(.data) for .data
    drv_rtc.o(i.drv_rtc_init) refers to app_timer2.o(log_const_data) for m_nrf_log_app_timer_logs_data_const
    drv_rtc.o(i.drv_rtc_overflow_enable) refers to drv_rtc.o(i.evt_enable) for evt_enable
    drv_rtc.o(i.drv_rtc_overflow_pending) refers to drv_rtc.o(i.evt_pending) for evt_pending
    drv_rtc.o(i.drv_rtc_tick_enable) refers to drv_rtc.o(i.evt_enable) for evt_enable
    drv_rtc.o(i.drv_rtc_tick_pending) refers to drv_rtc.o(i.evt_pending) for evt_pending
    drv_rtc.o(i.drv_rtc_uninit) refers to nrf_log_frontend.o(i.nrf_log_frontend_std_0) for nrf_log_frontend_std_0
    drv_rtc.o(i.drv_rtc_uninit) refers to drv_rtc.o(.data) for .data
    drv_rtc.o(i.drv_rtc_uninit) refers to app_timer2.o(log_const_data) for m_nrf_log_app_timer_logs_data_const
    drv_rtc.o(i.drv_rtc_windowed_compare_set) refers to drv_rtc.o(i.nrf_rtc_event_clear) for nrf_rtc_event_clear
    drv_rtc.o(i.drv_rtc_windowed_compare_set) refers to drv_rtc.o(i.nrfx_coredep_delay_us) for nrfx_coredep_delay_us
    drv_rtc.o(i.drv_rtc_windowed_compare_set) refers to drv_rtc.o(i.evt_enable) for evt_enable
    drv_rtc.o(i.evt_pending) refers to drv_rtc.o(i.nrf_rtc_event_clear) for nrf_rtc_event_clear
    drv_rtc.o(i.nrfx_coredep_delay_us) refers to drv_rtc.o(.constdata) for .constdata
    nrf_assert.o(i.assert_nrf_callback) refers to app_error_weak.o(i.app_error_fault_handler) for app_error_fault_handler
    nrf_atfifo.o(i.nrf_atfifo_alloc_put) refers to nrf_atfifo.o(i.nrf_atfifo_item_alloc) for nrf_atfifo_item_alloc
    nrf_atfifo.o(i.nrf_atfifo_alloc_put) refers to memcpya.o(.text) for __aeabi_memcpy
    nrf_atfifo.o(i.nrf_atfifo_alloc_put) refers to nrf_atfifo.o(i.nrf_atfifo_item_put) for nrf_atfifo_item_put
    nrf_atfifo.o(i.nrf_atfifo_clear) refers to nrf_atfifo.o(.emb_text) for __asm___12_nrf_atfifo_c_51f461e1__nrf_atfifo_space_clear
    nrf_atfifo.o(i.nrf_atfifo_get_free) refers to nrf_atfifo.o(i.nrf_atfifo_item_get) for nrf_atfifo_item_get
    nrf_atfifo.o(i.nrf_atfifo_get_free) refers to memcpya.o(.text) for __aeabi_memcpy
    nrf_atfifo.o(i.nrf_atfifo_get_free) refers to nrf_atfifo.o(i.nrf_atfifo_item_free) for nrf_atfifo_item_free
    nrf_atfifo.o(i.nrf_atfifo_item_alloc) refers to nrf_atfifo.o(.emb_text) for __asm___12_nrf_atfifo_c_51f461e1__nrf_atfifo_wspace_req
    nrf_atfifo.o(i.nrf_atfifo_item_free) refers to nrf_atfifo.o(.emb_text) for __asm___12_nrf_atfifo_c_51f461e1__nrf_atfifo_rspace_close
    nrf_atfifo.o(i.nrf_atfifo_item_get) refers to nrf_atfifo.o(.emb_text) for __asm___12_nrf_atfifo_c_51f461e1__nrf_atfifo_rspace_req
    nrf_atfifo.o(i.nrf_atfifo_item_put) refers to nrf_atfifo.o(.emb_text) for __asm___12_nrf_atfifo_c_51f461e1__nrf_atfifo_wspace_close
    nrf_atomic.o(i.nrf_atomic_flag_clear) refers to nrf_atomic.o(i.nrf_atomic_u32_and) for nrf_atomic_u32_and
    nrf_atomic.o(i.nrf_atomic_flag_clear_fetch) refers to nrf_atomic.o(i.nrf_atomic_u32_fetch_and) for nrf_atomic_u32_fetch_and
    nrf_atomic.o(i.nrf_atomic_flag_set) refers to nrf_atomic.o(i.nrf_atomic_u32_or) for nrf_atomic_u32_or
    nrf_atomic.o(i.nrf_atomic_flag_set_fetch) refers to nrf_atomic.o(i.nrf_atomic_u32_fetch_or) for nrf_atomic_u32_fetch_or
    nrf_atomic.o(i.nrf_atomic_u32_add) refers to nrf_atomic.o(.emb_text) for __asm___12_nrf_atomic_c_85ca2469__nrf_atomic_internal_add
    nrf_atomic.o(i.nrf_atomic_u32_and) refers to nrf_atomic.o(.emb_text) for __asm___12_nrf_atomic_c_85ca2469__nrf_atomic_internal_and
    nrf_atomic.o(i.nrf_atomic_u32_cmp_exch) refers to nrf_atomic.o(.emb_text) for __asm___12_nrf_atomic_c_85ca2469__nrf_atomic_internal_cmp_exch
    nrf_atomic.o(i.nrf_atomic_u32_fetch_add) refers to nrf_atomic.o(.emb_text) for __asm___12_nrf_atomic_c_85ca2469__nrf_atomic_internal_add
    nrf_atomic.o(i.nrf_atomic_u32_fetch_and) refers to nrf_atomic.o(.emb_text) for __asm___12_nrf_atomic_c_85ca2469__nrf_atomic_internal_and
    nrf_atomic.o(i.nrf_atomic_u32_fetch_or) refers to nrf_atomic.o(.emb_text) for __asm___12_nrf_atomic_c_85ca2469__nrf_atomic_internal_orr
    nrf_atomic.o(i.nrf_atomic_u32_fetch_store) refers to nrf_atomic.o(.emb_text) for __asm___12_nrf_atomic_c_85ca2469__nrf_atomic_internal_mov
    nrf_atomic.o(i.nrf_atomic_u32_fetch_sub) refers to nrf_atomic.o(.emb_text) for __asm___12_nrf_atomic_c_85ca2469__nrf_atomic_internal_sub
    nrf_atomic.o(i.nrf_atomic_u32_fetch_sub_hs) refers to nrf_atomic.o(.emb_text) for __asm___12_nrf_atomic_c_85ca2469__nrf_atomic_internal_sub_hs
    nrf_atomic.o(i.nrf_atomic_u32_fetch_xor) refers to nrf_atomic.o(.emb_text) for __asm___12_nrf_atomic_c_85ca2469__nrf_atomic_internal_eor
    nrf_atomic.o(i.nrf_atomic_u32_or) refers to nrf_atomic.o(.emb_text) for __asm___12_nrf_atomic_c_85ca2469__nrf_atomic_internal_orr
    nrf_atomic.o(i.nrf_atomic_u32_store) refers to nrf_atomic.o(.emb_text) for __asm___12_nrf_atomic_c_85ca2469__nrf_atomic_internal_mov
    nrf_atomic.o(i.nrf_atomic_u32_sub) refers to nrf_atomic.o(.emb_text) for __asm___12_nrf_atomic_c_85ca2469__nrf_atomic_internal_sub
    nrf_atomic.o(i.nrf_atomic_u32_sub_hs) refers to nrf_atomic.o(.emb_text) for __asm___12_nrf_atomic_c_85ca2469__nrf_atomic_internal_sub_hs
    nrf_atomic.o(i.nrf_atomic_u32_xor) refers to nrf_atomic.o(.emb_text) for __asm___12_nrf_atomic_c_85ca2469__nrf_atomic_internal_eor
    nrf_balloc.o(i.nrf_balloc_alloc) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    nrf_balloc.o(i.nrf_balloc_alloc) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    nrf_balloc.o(i.nrf_balloc_free) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    nrf_balloc.o(i.nrf_balloc_free) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    nrf_fprintf.o(i.nrf_fprintf) refers to nrf_fprintf_format.o(i.nrf_fprintf_fmt) for nrf_fprintf_fmt
    nrf_fprintf_format.o(i.buffer_add) refers to nrf_fprintf.o(i.nrf_fprintf_buffer_flush) for nrf_fprintf_buffer_flush
    nrf_fprintf_format.o(i.int_print) refers to nrf_fprintf_format.o(i.buffer_add) for buffer_add
    nrf_fprintf_format.o(i.int_print) refers to nrf_fprintf_format.o(i.unsigned_print) for unsigned_print
    nrf_fprintf_format.o(i.nrf_fprintf_fmt) refers to nrf_fprintf_format.o(i.buffer_add) for buffer_add
    nrf_fprintf_format.o(i.nrf_fprintf_fmt) refers to nrf_fprintf.o(i.nrf_fprintf_buffer_flush) for nrf_fprintf_buffer_flush
    nrf_fprintf_format.o(i.nrf_fprintf_fmt) refers to nrf_fprintf_format.o(i.int_print) for int_print
    nrf_fprintf_format.o(i.nrf_fprintf_fmt) refers to nrf_fprintf_format.o(i.unsigned_print) for unsigned_print
    nrf_fprintf_format.o(i.nrf_fprintf_fmt) refers to strlen.o(.text) for strlen
    nrf_fprintf_format.o(i.unsigned_print) refers to nrf_fprintf_format.o(i.buffer_add) for buffer_add
    nrf_fprintf_format.o(i.unsigned_print) refers to nrf_fprintf_format.o(.constdata) for .constdata
    nrf_memobj.o(i.memobj_op) refers to memcpya.o(.text) for __aeabi_memcpy
    nrf_memobj.o(i.nrf_memobj_alloc) refers to nrf_balloc.o(i.nrf_balloc_alloc) for nrf_balloc_alloc
    nrf_memobj.o(i.nrf_memobj_alloc) refers to nrf_memobj.o(i.nrf_memobj_free) for nrf_memobj_free
    nrf_memobj.o(i.nrf_memobj_free) refers to nrf_balloc.o(i.nrf_balloc_free) for nrf_balloc_free
    nrf_memobj.o(i.nrf_memobj_get) refers to nrf_atomic.o(i.nrf_atomic_u32_add) for nrf_atomic_u32_add
    nrf_memobj.o(i.nrf_memobj_pool_init) refers to nrf_balloc.o(i.nrf_balloc_init) for nrf_balloc_init
    nrf_memobj.o(i.nrf_memobj_put) refers to nrf_atomic.o(i.nrf_atomic_u32_sub) for nrf_atomic_u32_sub
    nrf_memobj.o(i.nrf_memobj_put) refers to nrf_memobj.o(i.nrf_memobj_free) for nrf_memobj_free
    nrf_memobj.o(i.nrf_memobj_read) refers to nrf_memobj.o(i.memobj_op) for memobj_op
    nrf_memobj.o(i.nrf_memobj_write) refers to nrf_memobj.o(i.memobj_op) for memobj_op
    nrf_pwr_mgmt.o(i.nrf_pwr_mgmt_init) refers to nrf_section_iter.o(i.nrf_section_iter_init) for nrf_section_iter_init
    nrf_pwr_mgmt.o(i.nrf_pwr_mgmt_init) refers to nrf_pwr_mgmt.o(.data) for .data
    nrf_pwr_mgmt.o(i.nrf_pwr_mgmt_init) refers to nrf_pwr_mgmt.o(.constdata) for .constdata
    nrf_pwr_mgmt.o(i.nrf_pwr_mgmt_init) refers to nrf_pwr_mgmt.o(.bss) for .bss
    nrf_pwr_mgmt.o(i.nrf_pwr_mgmt_run) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    nrf_pwr_mgmt.o(i.nrf_pwr_mgmt_run) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    nrf_pwr_mgmt.o(i.nrf_pwr_mgmt_run) refers to nrf_sdh.o(i.nrf_sdh_is_enabled) for nrf_sdh_is_enabled
    nrf_pwr_mgmt.o(i.nrf_pwr_mgmt_shutdown) refers to nrf_atomic.o(i.nrf_atomic_u32_fetch_store) for nrf_atomic_u32_fetch_store
    nrf_pwr_mgmt.o(i.nrf_pwr_mgmt_shutdown) refers to nrf_pwr_mgmt.o(i.shutdown_process) for shutdown_process
    nrf_pwr_mgmt.o(i.nrf_pwr_mgmt_shutdown) refers to nrf_pwr_mgmt.o(.data) for .data
    nrf_pwr_mgmt.o(i.shutdown_process) refers to nrf_section_iter.o(i.nrf_section_iter_next) for nrf_section_iter_next
    nrf_pwr_mgmt.o(i.shutdown_process) refers to nrf_log_frontend.o(i.nrf_log_panic) for nrf_log_panic
    nrf_pwr_mgmt.o(i.shutdown_process) refers to nrf_log_frontend.o(i.nrf_log_frontend_dequeue) for nrf_log_frontend_dequeue
    nrf_pwr_mgmt.o(i.shutdown_process) refers to nrf_sdh.o(i.nrf_sdh_is_enabled) for nrf_sdh_is_enabled
    nrf_pwr_mgmt.o(i.shutdown_process) refers to nrf_pwr_mgmt.o(.bss) for .bss
    nrf_pwr_mgmt.o(i.shutdown_process) refers to nrf_pwr_mgmt.o(.data) for .data
    nrf_pwr_mgmt.o(.constdata) refers to nrf_pwr_mgmt.o(.constdata) for pwr_mgmt_data_array
    nrf_pwr_mgmt.o(log_const_data) refers to nrf_pwr_mgmt.o(.conststrlit) for .conststrlit
    nrf_queue.o(i.nrf_queue_available_get) refers to nrf_queue.o(i.nrf_queue_utilization_get) for nrf_queue_utilization_get
    nrf_queue.o(i.nrf_queue_generic_pop) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    nrf_queue.o(i.nrf_queue_generic_pop) refers to nrf_queue.o(i.nrf_queue_is_empty) for nrf_queue_is_empty
    nrf_queue.o(i.nrf_queue_generic_pop) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    nrf_queue.o(i.nrf_queue_generic_pop) refers to nrf_queue.o(i.nrf_queue_next_idx) for nrf_queue_next_idx
    nrf_queue.o(i.nrf_queue_generic_pop) refers to memcpya.o(.text) for __aeabi_memcpy
    nrf_queue.o(i.nrf_queue_in) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    nrf_queue.o(i.nrf_queue_in) refers to nrf_queue.o(i.nrf_queue_available_get) for nrf_queue_available_get
    nrf_queue.o(i.nrf_queue_in) refers to nrf_queue.o(i.queue_write) for queue_write
    nrf_queue.o(i.nrf_queue_in) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    nrf_queue.o(i.nrf_queue_out) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    nrf_queue.o(i.nrf_queue_out) refers to nrf_queue.o(i.queue_utilization_get) for queue_utilization_get
    nrf_queue.o(i.nrf_queue_out) refers to nrf_queue.o(i.queue_read) for queue_read
    nrf_queue.o(i.nrf_queue_out) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    nrf_queue.o(i.nrf_queue_push) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    nrf_queue.o(i.nrf_queue_push) refers to nrf_queue.o(i.nrf_queue_is_full) for nrf_queue_is_full
    nrf_queue.o(i.nrf_queue_push) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    nrf_queue.o(i.nrf_queue_push) refers to nrf_queue.o(i.nrf_queue_next_idx) for nrf_queue_next_idx
    nrf_queue.o(i.nrf_queue_push) refers to memcpya.o(.text) for __aeabi_memcpy
    nrf_queue.o(i.nrf_queue_push) refers to nrf_queue.o(i.queue_utilization_get) for queue_utilization_get
    nrf_queue.o(i.nrf_queue_read) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    nrf_queue.o(i.nrf_queue_read) refers to nrf_queue.o(i.queue_utilization_get) for queue_utilization_get
    nrf_queue.o(i.nrf_queue_read) refers to nrf_queue.o(i.queue_read) for queue_read
    nrf_queue.o(i.nrf_queue_read) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    nrf_queue.o(i.nrf_queue_reset) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    nrf_queue.o(i.nrf_queue_reset) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    nrf_queue.o(i.nrf_queue_utilization_get) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    nrf_queue.o(i.nrf_queue_utilization_get) refers to nrf_queue.o(i.queue_utilization_get) for queue_utilization_get
    nrf_queue.o(i.nrf_queue_utilization_get) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    nrf_queue.o(i.nrf_queue_write) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    nrf_queue.o(i.nrf_queue_write) refers to nrf_queue.o(i.nrf_queue_available_get) for nrf_queue_available_get
    nrf_queue.o(i.nrf_queue_write) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    nrf_queue.o(i.nrf_queue_write) refers to nrf_queue.o(i.queue_write) for queue_write
    nrf_queue.o(i.queue_read) refers to nrf_queue.o(i.continous_items_get) for continous_items_get
    nrf_queue.o(i.queue_read) refers to memcpya.o(.text) for __aeabi_memcpy
    nrf_queue.o(i.queue_write) refers to nrf_queue.o(i.nrf_queue_available_get) for nrf_queue_available_get
    nrf_queue.o(i.queue_write) refers to nrf_queue.o(i.continous_items_get) for continous_items_get
    nrf_queue.o(i.queue_write) refers to memcpya.o(.text) for __aeabi_memcpy
    nrf_queue.o(i.queue_write) refers to nrf_queue.o(i.nrf_queue_next_idx) for nrf_queue_next_idx
    nrf_queue.o(i.queue_write) refers to nrf_queue.o(i.queue_utilization_get) for queue_utilization_get
    nrf_ringbuf.o(i.nrf_ringbuf_alloc) refers to nrf_atomic.o(i.nrf_atomic_flag_set_fetch) for nrf_atomic_flag_set_fetch
    nrf_ringbuf.o(i.nrf_ringbuf_alloc) refers to nrf_atomic.o(i.nrf_atomic_flag_clear) for nrf_atomic_flag_clear
    nrf_ringbuf.o(i.nrf_ringbuf_cpy_get) refers to nrf_atomic.o(i.nrf_atomic_flag_set_fetch) for nrf_atomic_flag_set_fetch
    nrf_ringbuf.o(i.nrf_ringbuf_cpy_get) refers to memcpya.o(.text) for __aeabi_memcpy
    nrf_ringbuf.o(i.nrf_ringbuf_cpy_get) refers to nrf_atomic.o(i.nrf_atomic_flag_clear) for nrf_atomic_flag_clear
    nrf_ringbuf.o(i.nrf_ringbuf_cpy_put) refers to nrf_atomic.o(i.nrf_atomic_flag_set_fetch) for nrf_atomic_flag_set_fetch
    nrf_ringbuf.o(i.nrf_ringbuf_cpy_put) refers to memcpya.o(.text) for __aeabi_memcpy
    nrf_ringbuf.o(i.nrf_ringbuf_cpy_put) refers to nrf_atomic.o(i.nrf_atomic_flag_clear) for nrf_atomic_flag_clear
    nrf_ringbuf.o(i.nrf_ringbuf_free) refers to nrf_atomic.o(i.nrf_atomic_flag_clear) for nrf_atomic_flag_clear
    nrf_ringbuf.o(i.nrf_ringbuf_get) refers to nrf_atomic.o(i.nrf_atomic_flag_set_fetch) for nrf_atomic_flag_set_fetch
    nrf_ringbuf.o(i.nrf_ringbuf_get) refers to nrf_atomic.o(i.nrf_atomic_flag_clear) for nrf_atomic_flag_clear
    nrf_ringbuf.o(i.nrf_ringbuf_put) refers to nrf_atomic.o(i.nrf_atomic_flag_clear_fetch) for nrf_atomic_flag_clear_fetch
    nrf_section_iter.o(i.nrf_section_iter_init) refers to nrf_section_iter.o(i.nrf_section_iter_item_set) for nrf_section_iter_item_set
    nrf_section_iter.o(i.nrf_section_iter_next) refers to nrf_section_iter.o(i.nrf_section_iter_item_set) for nrf_section_iter_item_set
    nrf_sortlist.o(log_const_data) refers to nrf_sortlist.o(.conststrlit) for .conststrlit
    nrf_strerror.o(i.nrf_strerror_find) refers to nrf_strerror.o(.constdata) for .constdata
    nrf_strerror.o(i.nrf_strerror_get) refers to nrf_strerror.o(i.nrf_strerror_find) for nrf_strerror_find
    nrf_strerror.o(i.nrf_strerror_get) refers to nrf_strerror.o(.constdata) for .constdata
    nrf_strerror.o(.constdata) refers to nrf_strerror.o(.conststring) for .conststring
    retarget.o(i.fgetc) refers to app_uart_fifo.o(i.app_uart_get) for app_uart_get
    retarget.o(i.fputc) refers to app_uart_fifo.o(i.app_uart_put) for app_uart_put
    nrf_log_backend_rtt.o(i.nrf_log_backend_rtt_init) refers to segger_rtt.o(i.SEGGER_RTT_Init) for SEGGER_RTT_Init
    nrf_log_backend_rtt.o(i.nrf_log_backend_rtt_put) refers to nrf_log_backend_serial.o(i.nrf_log_backend_serial_put) for nrf_log_backend_serial_put
    nrf_log_backend_rtt.o(i.nrf_log_backend_rtt_put) refers to nrf_log_backend_rtt.o(i.serial_tx) for serial_tx
    nrf_log_backend_rtt.o(i.nrf_log_backend_rtt_put) refers to nrf_log_backend_rtt.o(.bss) for .bss
    nrf_log_backend_rtt.o(i.serial_tx) refers to segger_rtt.o(i.SEGGER_RTT_WriteNoLock) for SEGGER_RTT_WriteNoLock
    nrf_log_backend_rtt.o(i.serial_tx) refers to nrf_log_backend_rtt.o(.data) for .data
    nrf_log_backend_rtt.o(i.serial_tx) refers to nrf_log_backend_rtt.o(.constdata) for .constdata
    nrf_log_backend_rtt.o(.constdata) refers to nrf_log_backend_rtt.o(i.nrf_log_backend_rtt_put) for nrf_log_backend_rtt_put
    nrf_log_backend_rtt.o(.constdata) refers to nrf_log_backend_rtt.o(i.nrf_log_backend_rtt_panic_set) for nrf_log_backend_rtt_panic_set
    nrf_log_backend_rtt.o(.constdata) refers to nrf_log_backend_rtt.o(i.nrf_log_backend_rtt_flush) for nrf_log_backend_rtt_flush
    nrf_log_backend_serial.o(i.nrf_log_backend_serial_put) refers to nrf_memobj.o(i.nrf_memobj_get) for nrf_memobj_get
    nrf_log_backend_serial.o(i.nrf_log_backend_serial_put) refers to memseta.o(.text) for __aeabi_memclr4
    nrf_log_backend_serial.o(i.nrf_log_backend_serial_put) refers to nrf_memobj.o(i.nrf_memobj_read) for nrf_memobj_read
    nrf_log_backend_serial.o(i.nrf_log_backend_serial_put) refers to nrf_log_str_formatter.o(i.nrf_log_std_entry_process) for nrf_log_std_entry_process
    nrf_log_backend_serial.o(i.nrf_log_backend_serial_put) refers to nrf_log_str_formatter.o(i.nrf_log_hexdump_entry_process) for nrf_log_hexdump_entry_process
    nrf_log_backend_serial.o(i.nrf_log_backend_serial_put) refers to nrf_memobj.o(i.nrf_memobj_put) for nrf_memobj_put
    nrf_log_default_backends.o(i.nrf_log_default_backends_init) refers to nrf_log_backend_rtt.o(i.nrf_log_backend_rtt_init) for nrf_log_backend_rtt_init
    nrf_log_default_backends.o(i.nrf_log_default_backends_init) refers to nrf_log_frontend.o(i.nrf_log_backend_add) for nrf_log_backend_add
    nrf_log_default_backends.o(i.nrf_log_default_backends_init) refers to nrf_log_default_backends.o(log_backends) for log_backends
    nrf_log_default_backends.o(log_backends) refers to nrf_log_backend_rtt.o(.constdata) for nrf_log_backend_rtt_api
    nrf_log_default_backends.o(log_backends) refers to nrf_log_default_backends.o(.conststrlit) for .conststrlit
    nrf_log_default_backends.o(log_backends) refers to nrf_log_default_backends.o(.data) for log_backend_cb_rtt_log_backend
    nrf_log_frontend.o(i.buf_prealloc) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    nrf_log_frontend.o(i.buf_prealloc) refers to nrf_atomic.o(i.nrf_atomic_u32_add) for nrf_atomic_u32_add
    nrf_log_frontend.o(i.buf_prealloc) refers to nrf_log_frontend.o(i.log_skip) for log_skip
    nrf_log_frontend.o(i.buf_prealloc) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    nrf_log_frontend.o(i.buf_prealloc) refers to nrf_log_frontend.o(.bss) for .bss
    nrf_log_frontend.o(i.buf_prealloc) refers to nrf_log_frontend.o(.data) for .data
    nrf_log_frontend.o(i.buffer_is_empty) refers to nrf_log_frontend.o(.bss) for .bss
    nrf_log_frontend.o(i.dropped_sat16_get) refers to nrf_atomic.o(i.nrf_atomic_u32_fetch_store) for nrf_atomic_u32_fetch_store
    nrf_log_frontend.o(i.dropped_sat16_get) refers to nrf_log_frontend.o(.bss) for .bss
    nrf_log_frontend.o(i.log_skip) refers to nrf_atomic.o(i.nrf_atomic_flag_set) for nrf_atomic_flag_set
    nrf_log_frontend.o(i.log_skip) refers to nrf_log_frontend.o(i.invalid_packets_omit) for invalid_packets_omit
    nrf_log_frontend.o(i.log_skip) refers to nrf_atomic.o(i.nrf_atomic_flag_clear_fetch) for nrf_atomic_flag_clear_fetch
    nrf_log_frontend.o(i.log_skip) refers to nrf_log_frontend.o(.bss) for .bss
    nrf_log_frontend.o(i.log_skip) refers to nrf_log_frontend.o(.data) for .data
    nrf_log_frontend.o(i.module_idx_get) refers to nrf_log_frontend.o(i.nrf_log_module_cnt_get) for nrf_log_module_cnt_get
    nrf_log_frontend.o(i.nrf_log_backend_add) refers to nrf_log_frontend.o(.bss) for .bss
    nrf_log_frontend.o(i.nrf_log_backend_remove) refers to nrf_log_frontend.o(.bss) for .bss
    nrf_log_frontend.o(i.nrf_log_frontend_dequeue) refers to nrf_log_frontend.o(i.buffer_is_empty) for buffer_is_empty
    nrf_log_frontend.o(i.nrf_log_frontend_dequeue) refers to nrf_log_frontend.o(i.invalid_packets_omit) for invalid_packets_omit
    nrf_log_frontend.o(i.nrf_log_frontend_dequeue) refers to nrf_memobj.o(i.nrf_memobj_alloc) for nrf_memobj_alloc
    nrf_log_frontend.o(i.nrf_log_frontend_dequeue) refers to nrf_memobj.o(i.nrf_memobj_get) for nrf_memobj_get
    nrf_log_frontend.o(i.nrf_log_frontend_dequeue) refers to nrf_memobj.o(i.nrf_memobj_write) for nrf_memobj_write
    nrf_log_frontend.o(i.nrf_log_frontend_dequeue) refers to nrf_memobj.o(i.nrf_memobj_put) for nrf_memobj_put
    nrf_log_frontend.o(i.nrf_log_frontend_dequeue) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    nrf_log_frontend.o(i.nrf_log_frontend_dequeue) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    nrf_log_frontend.o(i.nrf_log_frontend_dequeue) refers to nrf_log_frontend.o(i.nrf_log_frontend_std_0) for nrf_log_frontend_std_0
    nrf_log_frontend.o(i.nrf_log_frontend_dequeue) refers to nrf_log_frontend.o(.bss) for .bss
    nrf_log_frontend.o(i.nrf_log_frontend_dequeue) refers to nrf_log_frontend.o(.data) for .data
    nrf_log_frontend.o(i.nrf_log_frontend_dequeue) refers to nrf_log_frontend.o(nrf_balloc) for nrf_balloc
    nrf_log_frontend.o(i.nrf_log_frontend_dequeue) refers to nrf_log_frontend.o(log_const_data) for log_const_data
    nrf_log_frontend.o(i.nrf_log_frontend_hexdump) refers to nrf_log_frontend.o(i.buf_prealloc) for buf_prealloc
    nrf_log_frontend.o(i.nrf_log_frontend_hexdump) refers to memcpya.o(.text) for __aeabi_memcpy
    nrf_log_frontend.o(i.nrf_log_frontend_hexdump) refers to nrf_log_frontend.o(i.dropped_sat16_get) for dropped_sat16_get
    nrf_log_frontend.o(i.nrf_log_frontend_hexdump) refers to nrf_log_frontend.o(i.nrf_log_frontend_dequeue) for nrf_log_frontend_dequeue
    nrf_log_frontend.o(i.nrf_log_frontend_hexdump) refers to nrf_log_frontend.o(.data) for .data
    nrf_log_frontend.o(i.nrf_log_frontend_hexdump) refers to nrf_log_frontend.o(.bss) for .bss
    nrf_log_frontend.o(i.nrf_log_frontend_std_0) refers to nrf_log_frontend.o(i.std_n) for std_n
    nrf_log_frontend.o(i.nrf_log_frontend_std_1) refers to nrf_log_frontend.o(i.std_n) for std_n
    nrf_log_frontend.o(i.nrf_log_frontend_std_2) refers to nrf_log_frontend.o(i.std_n) for std_n
    nrf_log_frontend.o(i.nrf_log_frontend_std_3) refers to nrf_log_frontend.o(i.std_n) for std_n
    nrf_log_frontend.o(i.nrf_log_frontend_std_4) refers to nrf_log_frontend.o(i.std_n) for std_n
    nrf_log_frontend.o(i.nrf_log_frontend_std_5) refers to nrf_log_frontend.o(i.std_n) for std_n
    nrf_log_frontend.o(i.nrf_log_frontend_std_6) refers to nrf_log_frontend.o(i.std_n) for std_n
    nrf_log_frontend.o(i.nrf_log_init) refers to nrf_memobj.o(i.nrf_memobj_pool_init) for nrf_memobj_pool_init
    nrf_log_frontend.o(i.nrf_log_init) refers to nrf_ringbuf.o(i.nrf_ringbuf_init) for nrf_ringbuf_init
    nrf_log_frontend.o(i.nrf_log_init) refers to nrf_log_frontend.o(.bss) for .bss
    nrf_log_frontend.o(i.nrf_log_init) refers to nrf_log_frontend.o(nrf_balloc) for nrf_balloc
    nrf_log_frontend.o(i.nrf_log_init) refers to nrf_log_frontend.o(.constdata) for .constdata
    nrf_log_frontend.o(i.nrf_log_module_filter_get) refers to nrf_log_frontend.o(i.module_idx_get) for module_idx_get
    nrf_log_frontend.o(i.nrf_log_module_name_get) refers to nrf_log_frontend.o(i.module_idx_get) for module_idx_get
    nrf_log_frontend.o(i.nrf_log_panic) refers to nrf_log_frontend.o(.bss) for .bss
    nrf_log_frontend.o(i.nrf_log_push) refers to strlen.o(.text) for strlen
    nrf_log_frontend.o(i.nrf_log_push) refers to nrf_ringbuf.o(i.nrf_ringbuf_alloc) for nrf_ringbuf_alloc
    nrf_log_frontend.o(i.nrf_log_push) refers to memcpya.o(.text) for __aeabi_memcpy
    nrf_log_frontend.o(i.nrf_log_push) refers to nrf_ringbuf.o(i.nrf_ringbuf_put) for nrf_ringbuf_put
    nrf_log_frontend.o(i.nrf_log_push) refers to nrf_ringbuf.o(i.nrf_ringbuf_free) for nrf_ringbuf_free
    nrf_log_frontend.o(i.nrf_log_push) refers to nrf_log_frontend.o(.bss) for .bss
    nrf_log_frontend.o(i.nrf_log_push) refers to nrf_log_frontend.o(.constdata) for .constdata
    nrf_log_frontend.o(i.std_n) refers to nrf_log_frontend.o(i.buf_prealloc) for buf_prealloc
    nrf_log_frontend.o(i.std_n) refers to nrf_log_frontend.o(i.dropped_sat16_get) for dropped_sat16_get
    nrf_log_frontend.o(i.std_n) refers to nrf_log_frontend.o(i.nrf_log_frontend_dequeue) for nrf_log_frontend_dequeue
    nrf_log_frontend.o(i.std_n) refers to nrf_log_frontend.o(.data) for .data
    nrf_log_frontend.o(i.std_n) refers to nrf_log_frontend.o(.bss) for .bss
    nrf_log_frontend.o(.constdata) refers to nrf_log_frontend.o(.bss) for m_log_push_ringbuf_buf
    nrf_log_frontend.o(.constdata) refers to nrf_log_frontend.o(.bss) for m_log_push_ringbuf_cb
    nrf_log_frontend.o(log_const_data) refers to nrf_log_frontend.o(.conststrlit) for .conststrlit
    nrf_log_frontend.o(nrf_balloc) refers to nrf_log_frontend.o(.data) for log_mempool_nrf_balloc_cb
    nrf_log_frontend.o(nrf_balloc) refers to nrf_log_frontend.o(.data) for log_mempool_nrf_balloc_pool_stack
    nrf_log_frontend.o(nrf_balloc) refers to nrf_log_frontend.o(.bss) for log_mempool_nrf_balloc_pool_mem
    nrf_log_str_formatter.o(i.nrf_log_hexdump_entry_process) refers to nrf_log_str_formatter.o(i.prefix_process) for prefix_process
    nrf_log_str_formatter.o(i.nrf_log_hexdump_entry_process) refers to nrf_fprintf.o(i.nrf_fprintf) for nrf_fprintf
    nrf_log_str_formatter.o(i.nrf_log_hexdump_entry_process) refers to ctype_o.o(.text) for __rt_ctype_table
    nrf_log_str_formatter.o(i.nrf_log_hexdump_entry_process) refers to nrf_log_str_formatter.o(i.postfix_process) for postfix_process
    nrf_log_str_formatter.o(i.nrf_log_std_entry_process) refers to nrf_log_str_formatter.o(i.prefix_process) for prefix_process
    nrf_log_str_formatter.o(i.nrf_log_std_entry_process) refers to nrf_fprintf.o(i.nrf_fprintf) for nrf_fprintf
    nrf_log_str_formatter.o(i.nrf_log_std_entry_process) refers to nrf_log_str_formatter.o(i.postfix_process) for postfix_process
    nrf_log_str_formatter.o(i.nrf_log_str_formatter_timestamp_freq_set) refers to nrf_log_str_formatter.o(.data) for .data
    nrf_log_str_formatter.o(i.postfix_process) refers to nrf_fprintf.o(i.nrf_fprintf) for nrf_fprintf
    nrf_log_str_formatter.o(i.postfix_process) refers to nrf_fprintf.o(i.nrf_fprintf_buffer_flush) for nrf_fprintf_buffer_flush
    nrf_log_str_formatter.o(i.postfix_process) refers to nrf_log_str_formatter.o(.data) for .data
    nrf_log_str_formatter.o(i.prefix_process) refers to nrf_fprintf.o(i.nrf_fprintf) for nrf_fprintf
    nrf_log_str_formatter.o(i.prefix_process) refers to nrf_log_frontend.o(i.nrf_log_color_id_get) for nrf_log_color_id_get
    nrf_log_str_formatter.o(i.prefix_process) refers to nrf_log_frontend.o(i.nrf_log_module_name_get) for nrf_log_module_name_get
    nrf_log_str_formatter.o(i.prefix_process) refers to nrf_log_str_formatter.o(.data) for .data
    nrf_log_str_formatter.o(.data) refers to nrf_log_str_formatter.o(.conststring) for .conststring
    segger_rtt.o(i.SEGGER_RTT_AllocDownBuffer) refers to segger_rtt.o(i._DoInit) for _DoInit
    segger_rtt.o(i.SEGGER_RTT_AllocDownBuffer) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    segger_rtt.o(i.SEGGER_RTT_AllocDownBuffer) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    segger_rtt.o(i.SEGGER_RTT_AllocDownBuffer) refers to segger_rtt.o(.bss) for .bss
    segger_rtt.o(i.SEGGER_RTT_AllocUpBuffer) refers to segger_rtt.o(i._DoInit) for _DoInit
    segger_rtt.o(i.SEGGER_RTT_AllocUpBuffer) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    segger_rtt.o(i.SEGGER_RTT_AllocUpBuffer) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    segger_rtt.o(i.SEGGER_RTT_AllocUpBuffer) refers to segger_rtt.o(.bss) for .bss
    segger_rtt.o(i.SEGGER_RTT_ConfigDownBuffer) refers to segger_rtt.o(i._DoInit) for _DoInit
    segger_rtt.o(i.SEGGER_RTT_ConfigDownBuffer) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    segger_rtt.o(i.SEGGER_RTT_ConfigDownBuffer) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    segger_rtt.o(i.SEGGER_RTT_ConfigDownBuffer) refers to segger_rtt.o(.bss) for .bss
    segger_rtt.o(i.SEGGER_RTT_ConfigUpBuffer) refers to segger_rtt.o(i._DoInit) for _DoInit
    segger_rtt.o(i.SEGGER_RTT_ConfigUpBuffer) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    segger_rtt.o(i.SEGGER_RTT_ConfigUpBuffer) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    segger_rtt.o(i.SEGGER_RTT_ConfigUpBuffer) refers to segger_rtt.o(.bss) for .bss
    segger_rtt.o(i.SEGGER_RTT_GetKey) refers to segger_rtt.o(i.SEGGER_RTT_Read) for SEGGER_RTT_Read
    segger_rtt.o(i.SEGGER_RTT_HasData) refers to segger_rtt.o(.bss) for .bss
    segger_rtt.o(i.SEGGER_RTT_HasKey) refers to segger_rtt.o(i._DoInit) for _DoInit
    segger_rtt.o(i.SEGGER_RTT_HasKey) refers to segger_rtt.o(.bss) for .bss
    segger_rtt.o(i.SEGGER_RTT_Init) refers to segger_rtt.o(i._DoInit) for _DoInit
    segger_rtt.o(i.SEGGER_RTT_PutChar) refers to segger_rtt.o(i._DoInit) for _DoInit
    segger_rtt.o(i.SEGGER_RTT_PutChar) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    segger_rtt.o(i.SEGGER_RTT_PutChar) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    segger_rtt.o(i.SEGGER_RTT_PutChar) refers to segger_rtt.o(.bss) for .bss
    segger_rtt.o(i.SEGGER_RTT_PutCharSkip) refers to segger_rtt.o(i._DoInit) for _DoInit
    segger_rtt.o(i.SEGGER_RTT_PutCharSkip) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    segger_rtt.o(i.SEGGER_RTT_PutCharSkip) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    segger_rtt.o(i.SEGGER_RTT_PutCharSkip) refers to segger_rtt.o(.bss) for .bss
    segger_rtt.o(i.SEGGER_RTT_PutCharSkipNoLock) refers to segger_rtt.o(.bss) for .bss
    segger_rtt.o(i.SEGGER_RTT_Read) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    segger_rtt.o(i.SEGGER_RTT_Read) refers to segger_rtt.o(i.SEGGER_RTT_ReadNoLock) for SEGGER_RTT_ReadNoLock
    segger_rtt.o(i.SEGGER_RTT_Read) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    segger_rtt.o(i.SEGGER_RTT_ReadNoLock) refers to segger_rtt.o(i._DoInit) for _DoInit
    segger_rtt.o(i.SEGGER_RTT_ReadNoLock) refers to memcpya.o(.text) for __aeabi_memcpy
    segger_rtt.o(i.SEGGER_RTT_ReadNoLock) refers to segger_rtt.o(.bss) for .bss
    segger_rtt.o(i.SEGGER_RTT_SetFlagsDownBuffer) refers to segger_rtt.o(i._DoInit) for _DoInit
    segger_rtt.o(i.SEGGER_RTT_SetFlagsDownBuffer) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    segger_rtt.o(i.SEGGER_RTT_SetFlagsDownBuffer) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    segger_rtt.o(i.SEGGER_RTT_SetFlagsDownBuffer) refers to segger_rtt.o(.bss) for .bss
    segger_rtt.o(i.SEGGER_RTT_SetFlagsUpBuffer) refers to segger_rtt.o(i._DoInit) for _DoInit
    segger_rtt.o(i.SEGGER_RTT_SetFlagsUpBuffer) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    segger_rtt.o(i.SEGGER_RTT_SetFlagsUpBuffer) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    segger_rtt.o(i.SEGGER_RTT_SetFlagsUpBuffer) refers to segger_rtt.o(.bss) for .bss
    segger_rtt.o(i.SEGGER_RTT_SetNameDownBuffer) refers to segger_rtt.o(i._DoInit) for _DoInit
    segger_rtt.o(i.SEGGER_RTT_SetNameDownBuffer) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    segger_rtt.o(i.SEGGER_RTT_SetNameDownBuffer) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    segger_rtt.o(i.SEGGER_RTT_SetNameDownBuffer) refers to segger_rtt.o(.bss) for .bss
    segger_rtt.o(i.SEGGER_RTT_SetNameUpBuffer) refers to segger_rtt.o(i._DoInit) for _DoInit
    segger_rtt.o(i.SEGGER_RTT_SetNameUpBuffer) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    segger_rtt.o(i.SEGGER_RTT_SetNameUpBuffer) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    segger_rtt.o(i.SEGGER_RTT_SetNameUpBuffer) refers to segger_rtt.o(.bss) for .bss
    segger_rtt.o(i.SEGGER_RTT_SetTerminal) refers to segger_rtt.o(i._DoInit) for _DoInit
    segger_rtt.o(i.SEGGER_RTT_SetTerminal) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    segger_rtt.o(i.SEGGER_RTT_SetTerminal) refers to segger_rtt.o(i._GetAvailWriteSpace) for _GetAvailWriteSpace
    segger_rtt.o(i.SEGGER_RTT_SetTerminal) refers to segger_rtt.o(i._WriteNoCheck) for _WriteNoCheck
    segger_rtt.o(i.SEGGER_RTT_SetTerminal) refers to segger_rtt.o(i._WriteBlocking) for _WriteBlocking
    segger_rtt.o(i.SEGGER_RTT_SetTerminal) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    segger_rtt.o(i.SEGGER_RTT_SetTerminal) refers to segger_rtt.o(.bss) for .bss
    segger_rtt.o(i.SEGGER_RTT_SetTerminal) refers to segger_rtt.o(.data) for .data
    segger_rtt.o(i.SEGGER_RTT_TerminalOut) refers to segger_rtt.o(i._DoInit) for _DoInit
    segger_rtt.o(i.SEGGER_RTT_TerminalOut) refers to strlen.o(.text) for strlen
    segger_rtt.o(i.SEGGER_RTT_TerminalOut) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    segger_rtt.o(i.SEGGER_RTT_TerminalOut) refers to segger_rtt.o(i._GetAvailWriteSpace) for _GetAvailWriteSpace
    segger_rtt.o(i.SEGGER_RTT_TerminalOut) refers to segger_rtt.o(i._PostTerminalSwitch) for _PostTerminalSwitch
    segger_rtt.o(i.SEGGER_RTT_TerminalOut) refers to segger_rtt.o(i._WriteBlocking) for _WriteBlocking
    segger_rtt.o(i.SEGGER_RTT_TerminalOut) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    segger_rtt.o(i.SEGGER_RTT_TerminalOut) refers to segger_rtt.o(.bss) for .bss
    segger_rtt.o(i.SEGGER_RTT_TerminalOut) refers to segger_rtt.o(.data) for .data
    segger_rtt.o(i.SEGGER_RTT_WaitKey) refers to segger_rtt.o(i.SEGGER_RTT_GetKey) for SEGGER_RTT_GetKey
    segger_rtt.o(i.SEGGER_RTT_Write) refers to segger_rtt.o(i._DoInit) for _DoInit
    segger_rtt.o(i.SEGGER_RTT_Write) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    segger_rtt.o(i.SEGGER_RTT_Write) refers to segger_rtt.o(i.SEGGER_RTT_WriteNoLock) for SEGGER_RTT_WriteNoLock
    segger_rtt.o(i.SEGGER_RTT_Write) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    segger_rtt.o(i.SEGGER_RTT_Write) refers to segger_rtt.o(.bss) for .bss
    segger_rtt.o(i.SEGGER_RTT_WriteNoLock) refers to segger_rtt.o(i._GetAvailWriteSpace) for _GetAvailWriteSpace
    segger_rtt.o(i.SEGGER_RTT_WriteNoLock) refers to segger_rtt.o(i._WriteNoCheck) for _WriteNoCheck
    segger_rtt.o(i.SEGGER_RTT_WriteNoLock) refers to segger_rtt.o(i._WriteBlocking) for _WriteBlocking
    segger_rtt.o(i.SEGGER_RTT_WriteNoLock) refers to segger_rtt.o(.bss) for .bss
    segger_rtt.o(i.SEGGER_RTT_WriteSkipNoLock) refers to memcpya.o(.text) for __aeabi_memcpy
    segger_rtt.o(i.SEGGER_RTT_WriteSkipNoLock) refers to segger_rtt.o(.bss) for .bss
    segger_rtt.o(i.SEGGER_RTT_WriteString) refers to strlen.o(.text) for strlen
    segger_rtt.o(i.SEGGER_RTT_WriteString) refers to segger_rtt.o(i.SEGGER_RTT_Write) for SEGGER_RTT_Write
    segger_rtt.o(i.SEGGER_RTT_WriteWithOverwriteNoLock) refers to memcpya.o(.text) for __aeabi_memcpy
    segger_rtt.o(i.SEGGER_RTT_WriteWithOverwriteNoLock) refers to segger_rtt.o(.bss) for .bss
    segger_rtt.o(i._DoInit) refers to strcpy.o(.text) for strcpy
    segger_rtt.o(i._DoInit) refers to segger_rtt.o(.bss) for .bss
    segger_rtt.o(i._PostTerminalSwitch) refers to segger_rtt.o(i._WriteBlocking) for _WriteBlocking
    segger_rtt.o(i._PostTerminalSwitch) refers to segger_rtt.o(.data) for .data
    segger_rtt.o(i._WriteBlocking) refers to memcpya.o(.text) for __aeabi_memcpy
    segger_rtt.o(i._WriteNoCheck) refers to memcpya.o(.text) for __aeabi_memcpy
    segger_rtt_printf.o(i.SEGGER_RTT_printf) refers to segger_rtt_printf.o(i.SEGGER_RTT_vprintf) for SEGGER_RTT_vprintf
    segger_rtt_printf.o(i.SEGGER_RTT_vprintf) refers to segger_rtt_printf.o(i._StoreChar) for _StoreChar
    segger_rtt_printf.o(i.SEGGER_RTT_vprintf) refers to segger_rtt_printf.o(i._PrintInt) for _PrintInt
    segger_rtt_printf.o(i.SEGGER_RTT_vprintf) refers to segger_rtt_printf.o(i._PrintUnsigned) for _PrintUnsigned
    segger_rtt_printf.o(i.SEGGER_RTT_vprintf) refers to segger_rtt.o(i.SEGGER_RTT_Write) for SEGGER_RTT_Write
    segger_rtt_printf.o(i._PrintInt) refers to segger_rtt_printf.o(i._StoreChar) for _StoreChar
    segger_rtt_printf.o(i._PrintInt) refers to segger_rtt_printf.o(i._PrintUnsigned) for _PrintUnsigned
    segger_rtt_printf.o(i._PrintUnsigned) refers to segger_rtt_printf.o(i._StoreChar) for _StoreChar
    segger_rtt_printf.o(i._PrintUnsigned) refers to segger_rtt_printf.o(.constdata) for .constdata
    segger_rtt_printf.o(i._StoreChar) refers to segger_rtt.o(i.SEGGER_RTT_Write) for SEGGER_RTT_Write
    nrf_sdh.o(i.SWI2_EGU2_IRQHandler) refers to nrf_sdh.o(i.nrf_sdh_evts_poll) for nrf_sdh_evts_poll
    nrf_sdh.o(i.nrf_sdh_disable_request) refers to nrf_sdh.o(i.sdh_request_observer_notify) for sdh_request_observer_notify
    nrf_sdh.o(i.nrf_sdh_disable_request) refers to nrf_sdh.o(i.sdh_state_observer_notify) for sdh_state_observer_notify
    nrf_sdh.o(i.nrf_sdh_disable_request) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    nrf_sdh.o(i.nrf_sdh_disable_request) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    nrf_sdh.o(i.nrf_sdh_disable_request) refers to nrf_sdh.o(i.softdevice_evt_irq_disable) for softdevice_evt_irq_disable
    nrf_sdh.o(i.nrf_sdh_disable_request) refers to nrf_sdh.o(.data) for .data
    nrf_sdh.o(i.nrf_sdh_enable_request) refers to nrf_sdh.o(i.sdh_request_observer_notify) for sdh_request_observer_notify
    nrf_sdh.o(i.nrf_sdh_enable_request) refers to nrf_sdh.o(i.sdh_state_observer_notify) for sdh_state_observer_notify
    nrf_sdh.o(i.nrf_sdh_enable_request) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    nrf_sdh.o(i.nrf_sdh_enable_request) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    nrf_sdh.o(i.nrf_sdh_enable_request) refers to nrf_sdh.o(i.softdevices_evt_irq_enable) for softdevices_evt_irq_enable
    nrf_sdh.o(i.nrf_sdh_enable_request) refers to nrf_sdh.o(.data) for .data
    nrf_sdh.o(i.nrf_sdh_enable_request) refers to nrf_sdh.o(.constdata) for .constdata
    nrf_sdh.o(i.nrf_sdh_enable_request) refers to app_error_weak.o(i.app_error_fault_handler) for app_error_fault_handler
    nrf_sdh.o(i.nrf_sdh_evts_poll) refers to nrf_section_iter.o(i.nrf_section_iter_init) for nrf_section_iter_init
    nrf_sdh.o(i.nrf_sdh_evts_poll) refers to nrf_section_iter.o(i.nrf_section_iter_next) for nrf_section_iter_next
    nrf_sdh.o(i.nrf_sdh_evts_poll) refers to nrf_sdh.o(.constdata) for .constdata
    nrf_sdh.o(i.nrf_sdh_is_enabled) refers to nrf_sdh.o(.data) for .data
    nrf_sdh.o(i.nrf_sdh_is_suspended) refers to nrf_sdh.o(.data) for .data
    nrf_sdh.o(i.nrf_sdh_request_continue) refers to nrf_sdh.o(i.nrf_sdh_disable_request) for nrf_sdh_disable_request
    nrf_sdh.o(i.nrf_sdh_request_continue) refers to nrf_sdh.o(i.nrf_sdh_enable_request) for nrf_sdh_enable_request
    nrf_sdh.o(i.nrf_sdh_request_continue) refers to nrf_sdh.o(.data) for .data
    nrf_sdh.o(i.nrf_sdh_resume) refers to nrf_sdh.o(i.__sd_nvic_app_accessible_irq) for __sd_nvic_app_accessible_irq
    nrf_sdh.o(i.nrf_sdh_resume) refers to app_error.o(i.app_error_handler_bare) for app_error_handler_bare
    nrf_sdh.o(i.nrf_sdh_resume) refers to nrf_sdh.o(i.softdevices_evt_irq_enable) for softdevices_evt_irq_enable
    nrf_sdh.o(i.nrf_sdh_resume) refers to nrf_sdh.o(.data) for .data
    nrf_sdh.o(i.nrf_sdh_suspend) refers to nrf_sdh.o(i.softdevice_evt_irq_disable) for softdevice_evt_irq_disable
    nrf_sdh.o(i.nrf_sdh_suspend) refers to nrf_sdh.o(.data) for .data
    nrf_sdh.o(i.sdh_request_observer_notify) refers to nrf_section_iter.o(i.nrf_section_iter_init) for nrf_section_iter_init
    nrf_sdh.o(i.sdh_request_observer_notify) refers to nrf_section_iter.o(i.nrf_section_iter_next) for nrf_section_iter_next
    nrf_sdh.o(i.sdh_request_observer_notify) refers to nrf_sdh.o(.constdata) for .constdata
    nrf_sdh.o(i.sdh_state_observer_notify) refers to nrf_section_iter.o(i.nrf_section_iter_init) for nrf_section_iter_init
    nrf_sdh.o(i.sdh_state_observer_notify) refers to nrf_section_iter.o(i.nrf_section_iter_next) for nrf_section_iter_next
    nrf_sdh.o(i.sdh_state_observer_notify) refers to nrf_sdh.o(.constdata) for .constdata
    nrf_sdh.o(i.softdevice_evt_irq_disable) refers to nrf_sdh.o(i.__sd_nvic_app_accessible_irq) for __sd_nvic_app_accessible_irq
    nrf_sdh.o(i.softdevice_evt_irq_disable) refers to app_error.o(i.app_error_handler_bare) for app_error_handler_bare
    nrf_sdh.o(i.softdevice_evt_irq_disable) refers to app_util_platform.o(.bss) for nrf_nvic_state
    nrf_sdh.o(i.softdevices_evt_irq_enable) refers to nrf_sdh.o(i.__sd_nvic_app_accessible_irq) for __sd_nvic_app_accessible_irq
    nrf_sdh.o(i.softdevices_evt_irq_enable) refers to app_error.o(i.app_error_handler_bare) for app_error_handler_bare
    nrf_sdh.o(i.softdevices_evt_irq_enable) refers to app_util_platform.o(.bss) for nrf_nvic_state
    nrf_sdh.o(.constdata) refers to nrf_sdh.o(.constdata) for sdh_req_observers_array
    nrf_sdh.o(.constdata) refers to nrf_sdh.o(.constdata) for sdh_state_observers_array
    nrf_sdh.o(.constdata) refers to nrf_sdh.o(.constdata) for sdh_stack_observers_array
    nrf_sdh.o(log_const_data) refers to nrf_sdh.o(.conststrlit) for .conststrlit
    nrf_sdh_ble.o(i.nrf_sdh_ble_app_ram_start_get) refers to nrf_sdh_ble.o(.constdata) for .constdata
    nrf_sdh_ble.o(i.nrf_sdh_ble_default_cfg_set) refers to nrf_sdh_ble.o(i.nrf_sdh_ble_app_ram_start_get) for nrf_sdh_ble_app_ram_start_get
    nrf_sdh_ble.o(i.nrf_sdh_ble_default_cfg_set) refers to nrf_strerror.o(i.nrf_strerror_get) for nrf_strerror_get
    nrf_sdh_ble.o(i.nrf_sdh_ble_default_cfg_set) refers to nrf_log_frontend.o(i.nrf_log_frontend_std_1) for nrf_log_frontend_std_1
    nrf_sdh_ble.o(i.nrf_sdh_ble_default_cfg_set) refers to nrf_sdh_ble.o(log_const_data) for log_const_data
    nrf_sdh_ble.o(i.nrf_sdh_ble_default_cfg_set) refers to nrf_sdh_ble.o(.conststring) for .conststring
    nrf_sdh_ble.o(i.nrf_sdh_ble_enable) refers to nrf_log_frontend.o(i.nrf_log_frontend_std_0) for nrf_log_frontend_std_0
    nrf_sdh_ble.o(i.nrf_sdh_ble_enable) refers to nrf_log_frontend.o(i.nrf_log_frontend_std_2) for nrf_log_frontend_std_2
    nrf_sdh_ble.o(i.nrf_sdh_ble_enable) refers to nrf_log_frontend.o(i.nrf_log_frontend_std_1) for nrf_log_frontend_std_1
    nrf_sdh_ble.o(i.nrf_sdh_ble_enable) refers to nrf_strerror.o(i.nrf_strerror_get) for nrf_strerror_get
    nrf_sdh_ble.o(i.nrf_sdh_ble_enable) refers to nrf_sdh_ble.o(log_const_data) for log_const_data
    nrf_sdh_ble.o(i.nrf_sdh_ble_enable) refers to nrf_sdh_ble.o(.data) for .data
    nrf_sdh_ble.o(i.nrf_sdh_ble_evts_poll) refers to app_error.o(i.app_error_handler_bare) for app_error_handler_bare
    nrf_sdh_ble.o(i.nrf_sdh_ble_evts_poll) refers to nrf_section_iter.o(i.nrf_section_iter_init) for nrf_section_iter_init
    nrf_sdh_ble.o(i.nrf_sdh_ble_evts_poll) refers to nrf_section_iter.o(i.nrf_section_iter_next) for nrf_section_iter_next
    nrf_sdh_ble.o(i.nrf_sdh_ble_evts_poll) refers to nrf_sdh_ble.o(.data) for .data
    nrf_sdh_ble.o(i.nrf_sdh_ble_evts_poll) refers to nrf_sdh_ble.o(.constdata) for .constdata
    nrf_sdh_ble.o(.constdata) refers to nrf_sdh_ble.o(.constdata) for sdh_ble_observers_array
    nrf_sdh_ble.o(log_const_data) refers to nrf_sdh_ble.o(.conststrlit) for .conststrlit
    nrf_sdh_ble.o(sdh_stack_observers0) refers to nrf_sdh_ble.o(i.nrf_sdh_ble_evts_poll) for nrf_sdh_ble_evts_poll
    nrf_sdh_soc.o(i.nrf_sdh_soc_evts_poll) refers to app_error.o(i.app_error_handler_bare) for app_error_handler_bare
    nrf_sdh_soc.o(i.nrf_sdh_soc_evts_poll) refers to nrf_section_iter.o(i.nrf_section_iter_init) for nrf_section_iter_init
    nrf_sdh_soc.o(i.nrf_sdh_soc_evts_poll) refers to nrf_section_iter.o(i.nrf_section_iter_next) for nrf_section_iter_next
    nrf_sdh_soc.o(i.nrf_sdh_soc_evts_poll) refers to nrf_sdh_soc.o(.constdata) for .constdata
    nrf_sdh_soc.o(.constdata) refers to nrf_sdh_soc.o(.constdata) for sdh_soc_observers_array
    nrf_sdh_soc.o(log_const_data) refers to nrf_sdh_soc.o(.conststrlit) for .conststrlit
    nrf_sdh_soc.o(sdh_stack_observers0) refers to nrf_sdh_soc.o(i.nrf_sdh_soc_evts_poll) for nrf_sdh_soc_evts_poll
    arm_startup_nrf52840.o(RESET) refers to arm_startup_nrf52840.o(STACK) for __initial_sp
    arm_startup_nrf52840.o(RESET) refers to arm_startup_nrf52840.o(.text) for Reset_Handler
    arm_startup_nrf52840.o(RESET) refers to nrfx_clock.o(i.POWER_CLOCK_IRQHandler) for POWER_CLOCK_IRQHandler
    arm_startup_nrf52840.o(RESET) refers to nrfx_prs.o(i.UARTE0_UART0_IRQHandler) for UARTE0_UART0_IRQHandler
    arm_startup_nrf52840.o(RESET) refers to nrfx_gpiote.o(i.GPIOTE_IRQHandler) for GPIOTE_IRQHandler
    arm_startup_nrf52840.o(RESET) refers to drv_rtc.o(i.RTC1_IRQHandler) for RTC1_IRQHandler
    arm_startup_nrf52840.o(RESET) refers to nrf_sdh.o(i.SWI2_EGU2_IRQHandler) for SWI2_EGU2_IRQHandler
    arm_startup_nrf52840.o(.text) refers to system_nrf52840.o(i.SystemInit) for SystemInit
    arm_startup_nrf52840.o(.text) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    system_nrf52840.o(i.SystemCoreClockUpdate) refers to system_nrf52840.o(.data) for .data
    system_nrf52840.o(i.SystemInit) refers to system_nrf52840.o(.data) for .data
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry10a.o(.ARM.Collect$$$$0000000D) for __rt_final_cpp
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry11a.o(.ARM.Collect$$$$0000000F) for __rt_final_exit
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry7b.o(.ARM.Collect$$$$00000008) for _main_clock
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry8b.o(.ARM.Collect$$$$0000000A) for _main_cpp_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry9a.o(.ARM.Collect$$$$0000000B) for _main_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry5.o(.ARM.Collect$$$$00000004) for _main_scatterload
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry2.o(.ARM.Collect$$$$00000001) for _main_stk
    uldiv.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    uldiv.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    ctype_o.o(.text) refers to ctype_o.o(.constdata) for .constdata
    ctype_o.o(.constdata) refers to ctype_o.o(.constdata) for __ctype_table
    isalnum_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isalpha_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isblank_o.o(.text) refers to ctype_o.o(.constdata) for __ctype_table
    iscntrl_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isdigit_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isgraph_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    islower_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isprint_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    ispunct_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isspace_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isupper_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isxdigit_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    entry2.o(.ARM.Collect$$$$00000001) refers to entry2.o(.ARM.Collect$$$$00002712) for __lit__00000000
    entry2.o(.ARM.Collect$$$$00002712) refers to arm_startup_nrf52840.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to arm_startup_nrf52840.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry5.o(.ARM.Collect$$$$00000004) refers to init.o(.text) for __scatterload
    entry9a.o(.ARM.Collect$$$$0000000B) refers to main.o(i.main) for main
    entry9b.o(.ARM.Collect$$$$0000000C) refers to main.o(i.main) for main
    init.o(.text) refers to entry5.o(.ARM.Collect$$$$00000004) for __main_after_scatterload


==============================================================================

Removing Unused input sections from the image.

    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing main.o(.rrx_text), (6 bytes).
    Removing main.o(i.assert_nrf_callback), (16 bytes).
    Removing boards.o(.rev16_text), (4 bytes).
    Removing boards.o(.revsh_text), (4 bytes).
    Removing boards.o(.rrx_text), (6 bytes).
    Removing boards.o(i.bsp_board_led_idx_to_pin), (12 bytes).
    Removing boards.o(i.bsp_board_pin_to_led_idx), (36 bytes).
    Removing bsp.o(.rev16_text), (4 bytes).
    Removing bsp.o(.revsh_text), (4 bytes).
    Removing bsp.o(.rrx_text), (6 bytes).
    Removing bsp.o(i.bsp_buttons_disable), (4 bytes).
    Removing bsp.o(i.bsp_buttons_enable), (4 bytes).
    Removing bsp.o(i.bsp_wakeup_button_disable), (6 bytes).
    Removing bsp_btn_ble.o(.rev16_text), (4 bytes).
    Removing bsp_btn_ble.o(.revsh_text), (4 bytes).
    Removing bsp_btn_ble.o(.rrx_text), (6 bytes).
    Removing utf.o(i.utf16DecodeRune), (74 bytes).
    Removing utf.o(i.utf16EncodeRune), (64 bytes).
    Removing utf.o(i.utf16RuneCount), (60 bytes).
    Removing utf.o(i.utf16UTF8Count), (78 bytes).
    Removing utf.o(i.utf8DecodeRune), (174 bytes).
    Removing utf.o(i.utf8EncodeRune), (160 bytes).
    Removing utf.o(i.utf8RuneCount), (58 bytes).
    Removing utf.o(i.utf8UTF16Count), (76 bytes).
    Removing ble_advdata.o(.rev16_text), (4 bytes).
    Removing ble_advdata.o(.revsh_text), (4 bytes).
    Removing ble_advdata.o(.rrx_text), (6 bytes).
    Removing ble_advdata.o(i.ble_advdata_appearance_find), (52 bytes).
    Removing ble_advdata.o(i.ble_advdata_encode), (386 bytes).
    Removing ble_advdata.o(i.ble_advdata_name_find), (68 bytes).
    Removing ble_advdata.o(i.ble_advdata_parse), (32 bytes).
    Removing ble_advdata.o(i.ble_advdata_short_name_find), (74 bytes).
    Removing ble_advdata.o(i.ble_device_addr_encode), (96 bytes).
    Removing ble_advdata.o(i.conn_int_encode), (136 bytes).
    Removing ble_advdata.o(i.manuf_specific_data_encode), (100 bytes).
    Removing ble_advdata.o(i.name_encode), (166 bytes).
    Removing ble_advdata.o(i.service_data_encode), (136 bytes).
    Removing ble_advdata.o(i.uint16_encode), (10 bytes).
    Removing ble_advdata.o(i.uuid_list_encode), (48 bytes).
    Removing ble_advdata.o(i.uuid_list_sized_encode), (158 bytes).
    Removing ble_db_discovery.o(.rev16_text), (4 bytes).
    Removing ble_db_discovery.o(.revsh_text), (4 bytes).
    Removing ble_db_discovery.o(.rrx_text), (6 bytes).
    Removing ble_db_discovery.o(i.ble_db_discovery_close), (20 bytes).
    Removing ble_db_discovery.o(i.ble_db_discovery_evt_register), (64 bytes).
    Removing ble_db_discovery.o(i.ble_db_discovery_init), (36 bytes).
    Removing ble_db_discovery.o(i.ble_db_discovery_on_ble_evt), (92 bytes).
    Removing ble_db_discovery.o(i.ble_db_discovery_start), (40 bytes).
    Removing ble_db_discovery.o(i.characteristics_discover), (108 bytes).
    Removing ble_db_discovery.o(i.descriptors_discover), (184 bytes).
    Removing ble_db_discovery.o(i.discovery_available_evt_trigger), (48 bytes).
    Removing ble_db_discovery.o(i.discovery_complete_evt_trigger), (180 bytes).
    Removing ble_db_discovery.o(i.discovery_error_handler), (80 bytes).
    Removing ble_db_discovery.o(i.discovery_start), (124 bytes).
    Removing ble_db_discovery.o(i.is_desc_discovery_reqd), (76 bytes).
    Removing ble_db_discovery.o(i.on_characteristic_discovery_rsp), (312 bytes).
    Removing ble_db_discovery.o(i.on_descriptor_discovery_rsp), (218 bytes).
    Removing ble_db_discovery.o(i.on_primary_srv_discovery_rsp), (192 bytes).
    Removing ble_db_discovery.o(i.on_srv_disc_completion), (156 bytes).
    Removing ble_db_discovery.o(i.registered_handler_get), (56 bytes).
    Removing ble_db_discovery.o(.bss), (24 bytes).
    Removing ble_db_discovery.o(.constdata), (124 bytes).
    Removing ble_db_discovery.o(.conststring), (233 bytes).
    Removing ble_db_discovery.o(.data), (16 bytes).
    Removing ble_srv_common.o(.rev16_text), (4 bytes).
    Removing ble_srv_common.o(.revsh_text), (4 bytes).
    Removing ble_srv_common.o(.rrx_text), (6 bytes).
    Removing ble_srv_common.o(i.ble_srv_ascii_to_utf8), (18 bytes).
    Removing ble_srv_common.o(i.ble_srv_is_indication_enabled), (8 bytes).
    Removing ble_srv_common.o(i.ble_srv_is_notification_enabled), (8 bytes).
    Removing ble_srv_common.o(i.ble_srv_report_ref_encode), (12 bytes).
    Removing ble_srv_common.o(i.characteristic_add), (400 bytes).
    Removing ble_srv_common.o(i.descriptor_add), (170 bytes).
    Removing ble_srv_common.o(i.set_security_req), (48 bytes).
    Removing nrf_ble_gatt.o(.rev16_text), (4 bytes).
    Removing nrf_ble_gatt.o(.revsh_text), (4 bytes).
    Removing nrf_ble_gatt.o(.rrx_text), (6 bytes).
    Removing nrf_ble_gatt.o(i.nrf_ble_gatt_att_mtu_central_set), (24 bytes).
    Removing nrf_ble_gatt.o(i.nrf_ble_gatt_att_mtu_periph_set), (24 bytes).
    Removing nrf_ble_gatt.o(i.nrf_ble_gatt_data_length_get), (34 bytes).
    Removing nrf_ble_gatt.o(i.nrf_ble_gatt_data_length_set), (46 bytes).
    Removing nrf_ble_gatt.o(i.nrf_ble_gatt_eff_mtu_get), (12 bytes).
    Removing nrf_ble_gq.o(.rev16_text), (4 bytes).
    Removing nrf_ble_gq.o(.revsh_text), (4 bytes).
    Removing nrf_ble_gq.o(.rrx_text), (6 bytes).
    Removing nrf_ble_gq.o(i.conn_handle_id_find), (30 bytes).
    Removing nrf_ble_gq.o(i.gattc_write_alloc), (44 bytes).
    Removing nrf_ble_gq.o(i.gatts_hvx_alloc), (62 bytes).
    Removing nrf_ble_gq.o(i.nrf_ble_gq_conn_handle_register), (110 bytes).
    Removing nrf_ble_gq.o(i.nrf_ble_gq_item_add), (160 bytes).
    Removing nrf_ble_gq.o(i.nrf_ble_gq_on_ble_evt), (96 bytes).
    Removing nrf_ble_gq.o(i.queue_process), (280 bytes).
    Removing nrf_ble_gq.o(i.queues_purge), (88 bytes).
    Removing nrf_ble_gq.o(i.request_err_code_handle), (72 bytes).
    Removing nrf_ble_gq.o(i.request_process), (180 bytes).
    Removing nrf_ble_gq.o(.constdata), (24 bytes).
    Removing nrf_ble_gq.o(.conststring), (74 bytes).
    Removing nrf_ble_scan.o(.rev16_text), (4 bytes).
    Removing nrf_ble_scan.o(.revsh_text), (4 bytes).
    Removing nrf_ble_scan.o(.rrx_text), (6 bytes).
    Removing nrf_ble_scan.o(i.nrf_ble_scan_all_filter_remove), (10 bytes).
    Removing nrf_ble_scan.o(i.nrf_ble_scan_copy_addr_to_sd_gap_addr), (70 bytes).
    Removing nrf_ble_scan.o(i.nrf_ble_scan_filter_get), (20 bytes).
    Removing nrf_ble_scan.o(i.nrf_ble_scan_params_set), (40 bytes).
    Removing nrf_ble_scan.o(i.nrf_ble_scan_stop), (4 bytes).
    Removing nrf_drv_clock.o(.rev16_text), (4 bytes).
    Removing nrf_drv_clock.o(.revsh_text), (4 bytes).
    Removing nrf_drv_clock.o(.rrx_text), (6 bytes).
    Removing nrf_drv_clock.o(i.item_enqueue), (22 bytes).
    Removing nrf_drv_clock.o(i.nrf_drv_clock_calibration_abort), (4 bytes).
    Removing nrf_drv_clock.o(i.nrf_drv_clock_calibration_start), (4 bytes).
    Removing nrf_drv_clock.o(i.nrf_drv_clock_hfclk_is_running), (44 bytes).
    Removing nrf_drv_clock.o(i.nrf_drv_clock_hfclk_release), (56 bytes).
    Removing nrf_drv_clock.o(i.nrf_drv_clock_hfclk_request), (92 bytes).
    Removing nrf_drv_clock.o(i.nrf_drv_clock_init_check), (12 bytes).
    Removing nrf_drv_clock.o(i.nrf_drv_clock_is_calibrating), (4 bytes).
    Removing nrf_drv_clock.o(i.nrf_drv_clock_lfclk_is_running), (28 bytes).
    Removing nrf_drv_clock.o(i.nrf_drv_clock_lfclk_request), (84 bytes).
    Removing nrf_drv_clock.o(i.nrf_drv_clock_uninit), (24 bytes).
    Removing nrf_drv_clock.o(.constdata), (19 bytes).
    Removing nrf_drv_clock.o(.constdata), (32 bytes).
    Removing nrf_drv_clock.o(.constdata), (32 bytes).
    Removing nrf_drv_clock.o(.constdata), (29 bytes).
    Removing nrf_drv_uart.o(.rev16_text), (4 bytes).
    Removing nrf_drv_uart.o(.revsh_text), (4 bytes).
    Removing nrf_drv_uart.o(.rrx_text), (6 bytes).
    Removing nrf_drv_uart.o(i.nrf_drv_uart_init), (104 bytes).
    Removing nrf_drv_uart.o(i.uart_evt_handler), (48 bytes).
    Removing nrf_drv_uart.o(i.uarte_evt_handler), (48 bytes).
    Removing nrf_drv_uart.o(.data), (20 bytes).
    Removing nrfx_atomic.o(.rev16_text), (4 bytes).
    Removing nrfx_atomic.o(.revsh_text), (4 bytes).
    Removing nrfx_atomic.o(.rrx_text), (6 bytes).
    Removing nrfx_atomic.o(.emb_text), (226 bytes).
    Removing nrfx_atomic.o(i.nrfx_atomic_flag_clear), (6 bytes).
    Removing nrfx_atomic.o(i.nrfx_atomic_flag_clear_fetch), (6 bytes).
    Removing nrfx_atomic.o(i.nrfx_atomic_flag_set), (6 bytes).
    Removing nrfx_atomic.o(i.nrfx_atomic_flag_set_fetch), (6 bytes).
    Removing nrfx_atomic.o(i.nrfx_atomic_u32_add), (12 bytes).
    Removing nrfx_atomic.o(i.nrfx_atomic_u32_and), (12 bytes).
    Removing nrfx_atomic.o(i.nrfx_atomic_u32_cmp_exch), (4 bytes).
    Removing nrfx_atomic.o(i.nrfx_atomic_u32_fetch_add), (10 bytes).
    Removing nrfx_atomic.o(i.nrfx_atomic_u32_fetch_and), (10 bytes).
    Removing nrfx_atomic.o(i.nrfx_atomic_u32_fetch_or), (10 bytes).
    Removing nrfx_atomic.o(i.nrfx_atomic_u32_fetch_store), (10 bytes).
    Removing nrfx_atomic.o(i.nrfx_atomic_u32_fetch_sub), (10 bytes).
    Removing nrfx_atomic.o(i.nrfx_atomic_u32_fetch_sub_hs), (10 bytes).
    Removing nrfx_atomic.o(i.nrfx_atomic_u32_fetch_xor), (10 bytes).
    Removing nrfx_atomic.o(i.nrfx_atomic_u32_or), (12 bytes).
    Removing nrfx_atomic.o(i.nrfx_atomic_u32_store), (12 bytes).
    Removing nrfx_atomic.o(i.nrfx_atomic_u32_sub), (12 bytes).
    Removing nrfx_atomic.o(i.nrfx_atomic_u32_sub_hs), (12 bytes).
    Removing nrfx_atomic.o(i.nrfx_atomic_u32_xor), (12 bytes).
    Removing nrfx_clock.o(.rev16_text), (4 bytes).
    Removing nrfx_clock.o(.revsh_text), (4 bytes).
    Removing nrfx_clock.o(.rrx_text), (6 bytes).
    Removing nrfx_clock.o(i.nrfx_clock_calibration_start), (4 bytes).
    Removing nrfx_clock.o(i.nrfx_clock_calibration_timer_start), (2 bytes).
    Removing nrfx_clock.o(i.nrfx_clock_calibration_timer_stop), (2 bytes).
    Removing nrfx_clock.o(i.nrfx_clock_disable), (30 bytes).
    Removing nrfx_clock.o(i.nrfx_clock_hfclk_start), (22 bytes).
    Removing nrfx_clock.o(i.nrfx_clock_hfclk_stop), (60 bytes).
    Removing nrfx_clock.o(i.nrfx_clock_is_calibrating), (4 bytes).
    Removing nrfx_clock.o(i.nrfx_clock_lfclk_start), (32 bytes).
    Removing nrfx_clock.o(i.nrfx_clock_uninit), (24 bytes).
    Removing nrfx_clock.o(.constdata), (16 bytes).
    Removing nrfx_clock.o(.constdata), (29 bytes).
    Removing nrfx_gpiote.o(.rev16_text), (4 bytes).
    Removing nrfx_gpiote.o(.revsh_text), (4 bytes).
    Removing nrfx_gpiote.o(.rrx_text), (6 bytes).
    Removing nrfx_gpiote.o(i.nrf_gpio_cfg_default), (18 bytes).
    Removing nrfx_gpiote.o(i.nrf_gpio_pin_clear), (20 bytes).
    Removing nrfx_gpiote.o(i.nrf_gpio_pin_set), (20 bytes).
    Removing nrfx_gpiote.o(i.nrfx_gpiote_clr_task_addr_get), (16 bytes).
    Removing nrfx_gpiote.o(i.nrfx_gpiote_clr_task_get), (16 bytes).
    Removing nrfx_gpiote.o(i.nrfx_gpiote_clr_task_trigger), (28 bytes).
    Removing nrfx_gpiote.o(i.nrfx_gpiote_in_event_addr_get), (16 bytes).
    Removing nrfx_gpiote.o(i.nrfx_gpiote_in_event_disable), (72 bytes).
    Removing nrfx_gpiote.o(i.nrfx_gpiote_in_event_get), (34 bytes).
    Removing nrfx_gpiote.o(i.nrfx_gpiote_in_uninit), (88 bytes).
    Removing nrfx_gpiote.o(i.nrfx_gpiote_out_clear), (4 bytes).
    Removing nrfx_gpiote.o(i.nrfx_gpiote_out_init), (184 bytes).
    Removing nrfx_gpiote.o(i.nrfx_gpiote_out_set), (4 bytes).
    Removing nrfx_gpiote.o(i.nrfx_gpiote_out_task_addr_get), (16 bytes).
    Removing nrfx_gpiote.o(i.nrfx_gpiote_out_task_disable), (36 bytes).
    Removing nrfx_gpiote.o(i.nrfx_gpiote_out_task_enable), (36 bytes).
    Removing nrfx_gpiote.o(i.nrfx_gpiote_out_task_force), (40 bytes).
    Removing nrfx_gpiote.o(i.nrfx_gpiote_out_task_get), (12 bytes).
    Removing nrfx_gpiote.o(i.nrfx_gpiote_out_task_trigger), (24 bytes).
    Removing nrfx_gpiote.o(i.nrfx_gpiote_out_toggle), (36 bytes).
    Removing nrfx_gpiote.o(i.nrfx_gpiote_out_uninit), (88 bytes).
    Removing nrfx_gpiote.o(i.nrfx_gpiote_set_task_addr_get), (16 bytes).
    Removing nrfx_gpiote.o(i.nrfx_gpiote_set_task_get), (16 bytes).
    Removing nrfx_gpiote.o(i.nrfx_gpiote_set_task_trigger), (28 bytes).
    Removing nrfx_gpiote.o(i.nrfx_gpiote_uninit), (64 bytes).
    Removing nrfx_gpiote.o(i.pin_configured_check), (20 bytes).
    Removing nrfx_gpiote.o(i.pin_configured_clear), (28 bytes).
    Removing nrfx_gpiote.o(.constdata), (17 bytes).
    Removing nrfx_gpiote.o(.constdata), (21 bytes).
    Removing nrfx_gpiote.o(.constdata), (20 bytes).
    Removing nrfx_prs.o(.rev16_text), (4 bytes).
    Removing nrfx_prs.o(.revsh_text), (4 bytes).
    Removing nrfx_prs.o(.rrx_text), (6 bytes).
    Removing nrfx_prs.o(i.nrfx_prs_acquire), (58 bytes).
    Removing nrfx_prs.o(i.nrfx_prs_release), (18 bytes).
    Removing nrfx_prs.o(i.prs_box_get), (24 bytes).
    Removing nrfx_prs.o(.constdata), (17 bytes).
    Removing nrfx_uart.o(.rev16_text), (4 bytes).
    Removing nrfx_uart.o(.revsh_text), (4 bytes).
    Removing nrfx_uart.o(.rrx_text), (6 bytes).
    Removing nrfx_uart.o(i.apply_config), (118 bytes).
    Removing nrfx_uart.o(i.nrf_gpio_cfg), (50 bytes).
    Removing nrfx_uart.o(i.nrf_gpio_cfg_default), (18 bytes).
    Removing nrfx_uart.o(i.nrf_gpio_cfg_input), (18 bytes).
    Removing nrfx_uart.o(i.nrf_gpio_cfg_output), (20 bytes).
    Removing nrfx_uart.o(i.nrf_gpio_pin_port_decode), (28 bytes).
    Removing nrfx_uart.o(i.nrf_gpio_pin_set), (20 bytes).
    Removing nrfx_uart.o(i.nrf_uart_event_check), (10 bytes).
    Removing nrfx_uart.o(i.nrf_uart_event_clear), (12 bytes).
    Removing nrfx_uart.o(i.nrf_uart_int_enable_check), (12 bytes).
    Removing nrfx_uart.o(i.nrfx_uart_0_irq_handler), (16 bytes).
    Removing nrfx_uart.o(i.nrfx_uart_errorsrc_get), (26 bytes).
    Removing nrfx_uart.o(i.nrfx_uart_init), (212 bytes).
    Removing nrfx_uart.o(i.nrfx_uart_rx), (236 bytes).
    Removing nrfx_uart.o(i.nrfx_uart_rx_abort), (18 bytes).
    Removing nrfx_uart.o(i.nrfx_uart_rx_disable), (36 bytes).
    Removing nrfx_uart.o(i.nrfx_uart_rx_enable), (60 bytes).
    Removing nrfx_uart.o(i.nrfx_uart_rx_ready), (14 bytes).
    Removing nrfx_uart.o(i.nrfx_uart_tx), (168 bytes).
    Removing nrfx_uart.o(i.nrfx_uart_tx_abort), (48 bytes).
    Removing nrfx_uart.o(i.nrfx_uart_tx_in_progress), (32 bytes).
    Removing nrfx_uart.o(i.nrfx_uart_uninit), (192 bytes).
    Removing nrfx_uart.o(i.rx_byte), (50 bytes).
    Removing nrfx_uart.o(i.rx_done_event), (22 bytes).
    Removing nrfx_uart.o(i.rx_enable), (32 bytes).
    Removing nrfx_uart.o(i.tx_byte), (32 bytes).
    Removing nrfx_uart.o(i.tx_done_event), (26 bytes).
    Removing nrfx_uart.o(i.uart_irq_handler), (298 bytes).
    Removing nrfx_uart.o(.bss), (44 bytes).
    Removing nrfx_uart.o(.constdata), (15 bytes).
    Removing nrfx_uart.o(.constdata), (4 bytes).
    Removing nrfx_uart.o(.constdata), (13 bytes).
    Removing nrfx_uart.o(.constdata), (13 bytes).
    Removing nrfx_uarte.o(.rev16_text), (4 bytes).
    Removing nrfx_uarte.o(.revsh_text), (4 bytes).
    Removing nrfx_uarte.o(.rrx_text), (6 bytes).
    Removing nrfx_uarte.o(i.apply_config), (118 bytes).
    Removing nrfx_uarte.o(i.interrupts_enable), (136 bytes).
    Removing nrfx_uarte.o(i.nrf_gpio_cfg), (50 bytes).
    Removing nrfx_uarte.o(i.nrf_gpio_cfg_default), (18 bytes).
    Removing nrfx_uarte.o(i.nrf_gpio_cfg_input), (18 bytes).
    Removing nrfx_uarte.o(i.nrf_gpio_cfg_output), (20 bytes).
    Removing nrfx_uarte.o(i.nrf_gpio_pin_port_decode), (28 bytes).
    Removing nrfx_uarte.o(i.nrf_gpio_pin_set), (20 bytes).
    Removing nrfx_uarte.o(i.nrf_uarte_event_check), (10 bytes).
    Removing nrfx_uarte.o(i.nrf_uarte_event_clear), (12 bytes).
    Removing nrfx_uarte.o(i.nrfx_is_in_ram), (16 bytes).
    Removing nrfx_uarte.o(i.nrfx_uarte_0_irq_handler), (16 bytes).
    Removing nrfx_uarte.o(i.nrfx_uarte_errorsrc_get), (26 bytes).
    Removing nrfx_uarte.o(i.nrfx_uarte_init), (108 bytes).
    Removing nrfx_uarte.o(i.nrfx_uarte_rx), (260 bytes).
    Removing nrfx_uarte.o(i.nrfx_uarte_rx_abort), (44 bytes).
    Removing nrfx_uarte.o(i.nrfx_uarte_rx_ready), (14 bytes).
    Removing nrfx_uarte.o(i.nrfx_uarte_tx), (172 bytes).
    Removing nrfx_uarte.o(i.nrfx_uarte_tx_abort), (56 bytes).
    Removing nrfx_uarte.o(i.nrfx_uarte_tx_in_progress), (28 bytes).
    Removing nrfx_uarte.o(i.nrfx_uarte_uninit), (264 bytes).
    Removing nrfx_uarte.o(i.rx_done_event), (22 bytes).
    Removing nrfx_uarte.o(i.tx_done_event), (26 bytes).
    Removing nrfx_uarte.o(i.uarte_irq_handler), (274 bytes).
    Removing nrfx_uarte.o(.bss), (36 bytes).
    Removing nrfx_uarte.o(.constdata), (16 bytes).
    Removing nrfx_uarte.o(.constdata), (4 bytes).
    Removing nrfx_uarte.o(.constdata), (14 bytes).
    Removing nrfx_uarte.o(.constdata), (14 bytes).
    Removing app_button.o(.rev16_text), (4 bytes).
    Removing app_button.o(.revsh_text), (4 bytes).
    Removing app_button.o(.rrx_text), (6 bytes).
    Removing app_button.o(i.app_button_disable), (72 bytes).
    Removing app_button.o(i.app_button_is_pushed), (36 bytes).
    Removing app_error.o(.rev16_text), (4 bytes).
    Removing app_error.o(.revsh_text), (4 bytes).
    Removing app_error.o(.rrx_text), (6 bytes).
    Removing app_error.o(i.app_error_save_and_stop), (100 bytes).
    Removing app_error.o(.bss), (32 bytes).
    Removing app_error_handler_keil.o(.rev16_text), (4 bytes).
    Removing app_error_handler_keil.o(.revsh_text), (4 bytes).
    Removing app_error_handler_keil.o(.rrx_text), (6 bytes).
    Removing app_error_handler_keil.o(.emb_text), (26 bytes).
    Removing app_error_weak.o(.rev16_text), (4 bytes).
    Removing app_error_weak.o(.revsh_text), (4 bytes).
    Removing app_error_weak.o(.rrx_text), (6 bytes).
    Removing app_fifo.o(.rev16_text), (4 bytes).
    Removing app_fifo.o(.revsh_text), (4 bytes).
    Removing app_fifo.o(.rrx_text), (6 bytes).
    Removing app_fifo.o(i.app_fifo_flush), (8 bytes).
    Removing app_fifo.o(i.app_fifo_get), (22 bytes).
    Removing app_fifo.o(i.app_fifo_init), (32 bytes).
    Removing app_fifo.o(i.app_fifo_peek), (34 bytes).
    Removing app_fifo.o(i.app_fifo_put), (26 bytes).
    Removing app_fifo.o(i.app_fifo_read), (74 bytes).
    Removing app_fifo.o(i.app_fifo_write), (82 bytes).
    Removing app_fifo.o(i.fifo_get), (20 bytes).
    Removing app_fifo.o(i.fifo_put), (18 bytes).
    Removing app_scheduler.o(.rev16_text), (4 bytes).
    Removing app_scheduler.o(.revsh_text), (4 bytes).
    Removing app_scheduler.o(.rrx_text), (6 bytes).
    Removing app_scheduler.o(i.app_sched_event_put), (160 bytes).
    Removing app_scheduler.o(i.app_sched_execute), (64 bytes).
    Removing app_scheduler.o(i.app_sched_init), (44 bytes).
    Removing app_scheduler.o(i.app_sched_queue_space_get), (36 bytes).
    Removing app_scheduler.o(.data), (16 bytes).
    Removing app_timer2.o(.rev16_text), (4 bytes).
    Removing app_timer2.o(.revsh_text), (4 bytes).
    Removing app_timer2.o(.rrx_text), (6 bytes).
    Removing app_timer2.o(i.app_timer_cnt_diff_compute), (8 bytes).
    Removing app_timer2.o(i.app_timer_pause), (12 bytes).
    Removing app_timer2.o(i.app_timer_resume), (12 bytes).
    Removing app_timer2.o(i.app_timer_stop_all), (20 bytes).
    Removing app_uart_fifo.o(.rev16_text), (4 bytes).
    Removing app_uart_fifo.o(.revsh_text), (4 bytes).
    Removing app_uart_fifo.o(.rrx_text), (6 bytes).
    Removing app_uart_fifo.o(i.app_uart_close), (40 bytes).
    Removing app_uart_fifo.o(i.app_uart_flush), (32 bytes).
    Removing app_uart_fifo.o(i.app_uart_get), (52 bytes).
    Removing app_uart_fifo.o(i.app_uart_init), (164 bytes).
    Removing app_uart_fifo.o(i.app_uart_put), (84 bytes).
    Removing app_uart_fifo.o(i.nrf_drv_uart_rx), (32 bytes).
    Removing app_uart_fifo.o(i.nrf_drv_uart_tx), (32 bytes).
    Removing app_uart_fifo.o(i.uart_event_handler), (164 bytes).
    Removing app_uart_fifo.o(.bss), (32 bytes).
    Removing app_uart_fifo.o(.constdata), (32 bytes).
    Removing app_uart_fifo.o(.data), (28 bytes).
    Removing app_util_platform.o(.rev16_text), (4 bytes).
    Removing app_util_platform.o(.revsh_text), (4 bytes).
    Removing app_util_platform.o(.rrx_text), (6 bytes).
    Removing app_util_platform.o(i.app_util_disable_irq), (16 bytes).
    Removing app_util_platform.o(i.app_util_enable_irq), (20 bytes).
    Removing app_util_platform.o(i.current_int_priority_get), (48 bytes).
    Removing app_util_platform.o(i.privilege_level_get), (26 bytes).
    Removing app_util_platform.o(.data), (4 bytes).
    Removing drv_rtc.o(.rev16_text), (4 bytes).
    Removing drv_rtc.o(.revsh_text), (4 bytes).
    Removing drv_rtc.o(.rrx_text), (6 bytes).
    Removing drv_rtc.o(i.drv_rtc_compare_enable), (12 bytes).
    Removing drv_rtc.o(i.drv_rtc_compare_get), (14 bytes).
    Removing drv_rtc.o(i.drv_rtc_overflow_disable), (16 bytes).
    Removing drv_rtc.o(i.drv_rtc_tick_disable), (16 bytes).
    Removing drv_rtc.o(i.drv_rtc_tick_enable), (8 bytes).
    Removing drv_rtc.o(i.drv_rtc_tick_pending), (8 bytes).
    Removing drv_rtc.o(i.drv_rtc_uninit), (128 bytes).
    Removing hardfault_implementation.o(.rev16_text), (4 bytes).
    Removing hardfault_implementation.o(.revsh_text), (4 bytes).
    Removing hardfault_implementation.o(.rrx_text), (6 bytes).
    Removing nrf_assert.o(.rev16_text), (4 bytes).
    Removing nrf_assert.o(.revsh_text), (4 bytes).
    Removing nrf_assert.o(.rrx_text), (6 bytes).
    Removing nrf_assert.o(i.assert_nrf_callback), (20 bytes).
    Removing nrf_atfifo.o(.rev16_text), (4 bytes).
    Removing nrf_atfifo.o(.revsh_text), (4 bytes).
    Removing nrf_atfifo.o(.rrx_text), (6 bytes).
    Removing nrf_atfifo.o(i.nrf_atfifo_alloc_put), (46 bytes).
    Removing nrf_atfifo.o(i.nrf_atfifo_clear), (16 bytes).
    Removing nrf_atfifo.o(i.nrf_atfifo_get_free), (48 bytes).
    Removing nrf_atomic.o(.rev16_text), (4 bytes).
    Removing nrf_atomic.o(.revsh_text), (4 bytes).
    Removing nrf_atomic.o(.rrx_text), (6 bytes).
    Removing nrf_atomic.o(i.nrf_atomic_flag_clear), (6 bytes).
    Removing nrf_atomic.o(i.nrf_atomic_flag_set_fetch), (6 bytes).
    Removing nrf_atomic.o(i.nrf_atomic_u32_and), (12 bytes).
    Removing nrf_atomic.o(i.nrf_atomic_u32_cmp_exch), (4 bytes).
    Removing nrf_atomic.o(i.nrf_atomic_u32_fetch_add), (10 bytes).
    Removing nrf_atomic.o(i.nrf_atomic_u32_fetch_or), (10 bytes).
    Removing nrf_atomic.o(i.nrf_atomic_u32_fetch_sub), (10 bytes).
    Removing nrf_atomic.o(i.nrf_atomic_u32_fetch_sub_hs), (10 bytes).
    Removing nrf_atomic.o(i.nrf_atomic_u32_fetch_xor), (10 bytes).
    Removing nrf_atomic.o(i.nrf_atomic_u32_store), (12 bytes).
    Removing nrf_atomic.o(i.nrf_atomic_u32_sub_hs), (12 bytes).
    Removing nrf_atomic.o(i.nrf_atomic_u32_xor), (12 bytes).
    Removing nrf_balloc.o(.rev16_text), (4 bytes).
    Removing nrf_balloc.o(.revsh_text), (4 bytes).
    Removing nrf_balloc.o(.rrx_text), (6 bytes).
    Removing nrf_fprintf.o(.rev16_text), (4 bytes).
    Removing nrf_fprintf.o(.revsh_text), (4 bytes).
    Removing nrf_fprintf.o(.rrx_text), (6 bytes).
    Removing nrf_fprintf_format.o(.rev16_text), (4 bytes).
    Removing nrf_fprintf_format.o(.revsh_text), (4 bytes).
    Removing nrf_fprintf_format.o(.rrx_text), (6 bytes).
    Removing nrf_memobj.o(.rev16_text), (4 bytes).
    Removing nrf_memobj.o(.revsh_text), (4 bytes).
    Removing nrf_memobj.o(.rrx_text), (6 bytes).
    Removing nrf_pwr_mgmt.o(.rev16_text), (4 bytes).
    Removing nrf_pwr_mgmt.o(.revsh_text), (4 bytes).
    Removing nrf_pwr_mgmt.o(.rrx_text), (6 bytes).
    Removing nrf_pwr_mgmt.o(i.nrf_pwr_mgmt_feed), (2 bytes).
    Removing nrf_queue.o(.rev16_text), (4 bytes).
    Removing nrf_queue.o(.revsh_text), (4 bytes).
    Removing nrf_queue.o(.rrx_text), (6 bytes).
    Removing nrf_queue.o(i.continous_items_get), (38 bytes).
    Removing nrf_queue.o(i.nrf_queue_available_get), (14 bytes).
    Removing nrf_queue.o(i.nrf_queue_generic_pop), (142 bytes).
    Removing nrf_queue.o(i.nrf_queue_in), (70 bytes).
    Removing nrf_queue.o(i.nrf_queue_is_empty), (18 bytes).
    Removing nrf_queue.o(i.nrf_queue_is_full), (30 bytes).
    Removing nrf_queue.o(i.nrf_queue_max_utilization_get), (6 bytes).
    Removing nrf_queue.o(i.nrf_queue_max_utilization_reset), (8 bytes).
    Removing nrf_queue.o(i.nrf_queue_next_idx), (14 bytes).
    Removing nrf_queue.o(i.nrf_queue_out), (58 bytes).
    Removing nrf_queue.o(i.nrf_queue_push), (178 bytes).
    Removing nrf_queue.o(i.nrf_queue_read), (62 bytes).
    Removing nrf_queue.o(i.nrf_queue_reset), (34 bytes).
    Removing nrf_queue.o(i.nrf_queue_utilization_get), (36 bytes).
    Removing nrf_queue.o(i.nrf_queue_write), (68 bytes).
    Removing nrf_queue.o(i.queue_read), (100 bytes).
    Removing nrf_queue.o(i.queue_utilization_get), (24 bytes).
    Removing nrf_queue.o(i.queue_write), (144 bytes).
    Removing nrf_ringbuf.o(.rev16_text), (4 bytes).
    Removing nrf_ringbuf.o(.revsh_text), (4 bytes).
    Removing nrf_ringbuf.o(.rrx_text), (6 bytes).
    Removing nrf_ringbuf.o(i.nrf_ringbuf_alloc), (112 bytes).
    Removing nrf_ringbuf.o(i.nrf_ringbuf_cpy_get), (124 bytes).
    Removing nrf_ringbuf.o(i.nrf_ringbuf_cpy_put), (114 bytes).
    Removing nrf_ringbuf.o(i.nrf_ringbuf_free), (38 bytes).
    Removing nrf_ringbuf.o(i.nrf_ringbuf_get), (112 bytes).
    Removing nrf_ringbuf.o(i.nrf_ringbuf_put), (48 bytes).
    Removing nrf_section_iter.o(.rev16_text), (4 bytes).
    Removing nrf_section_iter.o(.revsh_text), (4 bytes).
    Removing nrf_section_iter.o(.rrx_text), (6 bytes).
    Removing nrf_sortlist.o(.rev16_text), (4 bytes).
    Removing nrf_sortlist.o(.revsh_text), (4 bytes).
    Removing nrf_sortlist.o(.rrx_text), (6 bytes).
    Removing nrf_sortlist.o(i.nrf_sortlist_next), (4 bytes).
    Removing nrf_strerror.o(.rev16_text), (4 bytes).
    Removing nrf_strerror.o(.revsh_text), (4 bytes).
    Removing nrf_strerror.o(.rrx_text), (6 bytes).
    Removing retarget.o(.rev16_text), (4 bytes).
    Removing retarget.o(.revsh_text), (4 bytes).
    Removing retarget.o(.rrx_text), (6 bytes).
    Removing retarget.o(i.fgetc), (18 bytes).
    Removing retarget.o(i.fputc), (14 bytes).
    Removing retarget.o(.data), (4 bytes).
    Removing retarget.o(.data), (4 bytes).
    Removing nrf_log_backend_rtt.o(.rev16_text), (4 bytes).
    Removing nrf_log_backend_rtt.o(.revsh_text), (4 bytes).
    Removing nrf_log_backend_rtt.o(.rrx_text), (6 bytes).
    Removing nrf_log_backend_serial.o(.rev16_text), (4 bytes).
    Removing nrf_log_backend_serial.o(.revsh_text), (4 bytes).
    Removing nrf_log_backend_serial.o(.rrx_text), (6 bytes).
    Removing nrf_log_default_backends.o(.rev16_text), (4 bytes).
    Removing nrf_log_default_backends.o(.revsh_text), (4 bytes).
    Removing nrf_log_default_backends.o(.rrx_text), (6 bytes).
    Removing nrf_log_frontend.o(.rev16_text), (4 bytes).
    Removing nrf_log_frontend.o(.revsh_text), (4 bytes).
    Removing nrf_log_frontend.o(.rrx_text), (6 bytes).
    Removing nrf_log_frontend.o(i.nrf_log_backend_remove), (52 bytes).
    Removing nrf_log_frontend.o(i.nrf_log_frontend_hexdump), (168 bytes).
    Removing nrf_log_frontend.o(i.nrf_log_frontend_std_3), (18 bytes).
    Removing nrf_log_frontend.o(i.nrf_log_frontend_std_4), (20 bytes).
    Removing nrf_log_frontend.o(i.nrf_log_frontend_std_5), (28 bytes).
    Removing nrf_log_frontend.o(i.nrf_log_module_filter_get), (36 bytes).
    Removing nrf_log_frontend.o(i.nrf_log_module_filter_set), (2 bytes).
    Removing nrf_log_frontend.o(i.nrf_log_push), (96 bytes).
    Removing nrf_log_str_formatter.o(.rev16_text), (4 bytes).
    Removing nrf_log_str_formatter.o(.revsh_text), (4 bytes).
    Removing nrf_log_str_formatter.o(.rrx_text), (6 bytes).
    Removing nrf_log_str_formatter.o(i.nrf_log_str_formatter_timestamp_freq_set), (32 bytes).
    Removing segger_rtt.o(.rev16_text), (4 bytes).
    Removing segger_rtt.o(.revsh_text), (4 bytes).
    Removing segger_rtt.o(.rrx_text), (6 bytes).
    Removing segger_rtt.o(i.SEGGER_RTT_AllocDownBuffer), (112 bytes).
    Removing segger_rtt.o(i.SEGGER_RTT_AllocUpBuffer), (112 bytes).
    Removing segger_rtt.o(i.SEGGER_RTT_ConfigDownBuffer), (96 bytes).
    Removing segger_rtt.o(i.SEGGER_RTT_ConfigUpBuffer), (96 bytes).
    Removing segger_rtt.o(i.SEGGER_RTT_GetKey), (28 bytes).
    Removing segger_rtt.o(i.SEGGER_RTT_HasData), (24 bytes).
    Removing segger_rtt.o(i.SEGGER_RTT_HasKey), (32 bytes).
    Removing segger_rtt.o(i.SEGGER_RTT_PutChar), (96 bytes).
    Removing segger_rtt.o(i.SEGGER_RTT_PutCharSkip), (84 bytes).
    Removing segger_rtt.o(i.SEGGER_RTT_PutCharSkipNoLock), (52 bytes).
    Removing segger_rtt.o(i.SEGGER_RTT_Read), (44 bytes).
    Removing segger_rtt.o(i.SEGGER_RTT_ReadNoLock), (120 bytes).
    Removing segger_rtt.o(i.SEGGER_RTT_SetFlagsDownBuffer), (68 bytes).
    Removing segger_rtt.o(i.SEGGER_RTT_SetFlagsUpBuffer), (68 bytes).
    Removing segger_rtt.o(i.SEGGER_RTT_SetNameDownBuffer), (68 bytes).
    Removing segger_rtt.o(i.SEGGER_RTT_SetNameUpBuffer), (68 bytes).
    Removing segger_rtt.o(i.SEGGER_RTT_SetTerminal), (136 bytes).
    Removing segger_rtt.o(i.SEGGER_RTT_TerminalOut), (176 bytes).
    Removing segger_rtt.o(i.SEGGER_RTT_WaitKey), (14 bytes).
    Removing segger_rtt.o(i.SEGGER_RTT_Write), (60 bytes).
    Removing segger_rtt.o(i.SEGGER_RTT_WriteSkipNoLock), (116 bytes).
    Removing segger_rtt.o(i.SEGGER_RTT_WriteString), (26 bytes).
    Removing segger_rtt.o(i.SEGGER_RTT_WriteWithOverwriteNoLock), (148 bytes).
    Removing segger_rtt.o(i._PostTerminalSwitch), (32 bytes).
    Removing segger_rtt.o(.data), (17 bytes).
    Removing segger_rtt_printf.o(.rev16_text), (4 bytes).
    Removing segger_rtt_printf.o(.revsh_text), (4 bytes).
    Removing segger_rtt_printf.o(.rrx_text), (6 bytes).
    Removing segger_rtt_printf.o(i.SEGGER_RTT_printf), (22 bytes).
    Removing segger_rtt_printf.o(i.SEGGER_RTT_vprintf), (406 bytes).
    Removing segger_rtt_printf.o(i._PrintInt), (198 bytes).
    Removing segger_rtt_printf.o(i._PrintUnsigned), (212 bytes).
    Removing segger_rtt_printf.o(i._StoreChar), (62 bytes).
    Removing segger_rtt_printf.o(.constdata), (16 bytes).
    Removing nrf_sdh.o(.rev16_text), (4 bytes).
    Removing nrf_sdh.o(.revsh_text), (4 bytes).
    Removing nrf_sdh.o(.rrx_text), (6 bytes).
    Removing nrf_sdh.o(i.nrf_sdh_disable_request), (84 bytes).
    Removing nrf_sdh.o(i.nrf_sdh_is_suspended), (20 bytes).
    Removing nrf_sdh.o(i.nrf_sdh_request_continue), (28 bytes).
    Removing nrf_sdh.o(i.nrf_sdh_resume), (56 bytes).
    Removing nrf_sdh.o(i.nrf_sdh_suspend), (24 bytes).
    Removing nrf_sdh.o(i.softdevice_evt_irq_disable), (64 bytes).
    Removing nrf_sdh_ble.o(.rev16_text), (4 bytes).
    Removing nrf_sdh_ble.o(.revsh_text), (4 bytes).
    Removing nrf_sdh_ble.o(.rrx_text), (6 bytes).
    Removing nrf_sdh_soc.o(.rev16_text), (4 bytes).
    Removing nrf_sdh_soc.o(.revsh_text), (4 bytes).
    Removing nrf_sdh_soc.o(.rrx_text), (6 bytes).
    Removing arm_startup_nrf52840.o(HEAP), (8192 bytes).
    Removing system_nrf52840.o(.rev16_text), (4 bytes).
    Removing system_nrf52840.o(.revsh_text), (4 bytes).
    Removing system_nrf52840.o(.rrx_text), (6 bytes).
    Removing system_nrf52840.o(i.SystemCoreClockUpdate), (16 bytes).

525 unused section(s) (total 29427 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  ctype_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isdigit_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  iscntrl_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isblank_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isupper_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  islower_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isgraph_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isprint_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isalnum_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  ispunct_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isalpha_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isspace_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isxdigit_o.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uldiv.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry5.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry2.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11a.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llshl.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llushr.o ABSOLUTE
    ../clib/microlib/string/memcmp.c         0x00000000   Number         0  memcmp.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpya.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpyb.o ABSOLUTE
    ../clib/microlib/string/memset.c         0x00000000   Number         0  memseta.o ABSOLUTE
    ../clib/microlib/string/strcpy.c         0x00000000   Number         0  strcpy.o ABSOLUTE
    ../clib/microlib/string/strlen.c         0x00000000   Number         0  strlen.o ABSOLUTE
    ..\..\..\..\..\..\components\ble\ble_db_discovery\ble_db_discovery.c 0x00000000   Number         0  ble_db_discovery.o ABSOLUTE
    ..\..\..\..\..\..\components\ble\common\ble_advdata.c 0x00000000   Number         0  ble_advdata.o ABSOLUTE
    ..\..\..\..\..\..\components\ble\common\ble_srv_common.c 0x00000000   Number         0  ble_srv_common.o ABSOLUTE
    ..\..\..\..\..\..\components\ble\nrf_ble_gatt\nrf_ble_gatt.c 0x00000000   Number         0  nrf_ble_gatt.o ABSOLUTE
    ..\..\..\..\..\..\components\ble\nrf_ble_gq\nrf_ble_gq.c 0x00000000   Number         0  nrf_ble_gq.o ABSOLUTE
    ..\..\..\..\..\..\components\ble\nrf_ble_scan\nrf_ble_scan.c 0x00000000   Number         0  nrf_ble_scan.o ABSOLUTE
    ..\..\..\..\..\..\components\boards\boards.c 0x00000000   Number         0  boards.o ABSOLUTE
    ..\..\..\..\..\..\components\libraries\atomic\nrf_atomic.c 0x00000000   Number         0  nrf_atomic.o ABSOLUTE
    ..\..\..\..\..\..\components\libraries\atomic_fifo\nrf_atfifo.c 0x00000000   Number         0  nrf_atfifo.o ABSOLUTE
    ..\..\..\..\..\..\components\libraries\balloc\nrf_balloc.c 0x00000000   Number         0  nrf_balloc.o ABSOLUTE
    ..\..\..\..\..\..\components\libraries\bsp\bsp.c 0x00000000   Number         0  bsp.o ABSOLUTE
    ..\..\..\..\..\..\components\libraries\bsp\bsp_btn_ble.c 0x00000000   Number         0  bsp_btn_ble.o ABSOLUTE
    ..\..\..\..\..\..\components\libraries\button\app_button.c 0x00000000   Number         0  app_button.o ABSOLUTE
    ..\..\..\..\..\..\components\libraries\experimental_section_vars\nrf_section_iter.c 0x00000000   Number         0  nrf_section_iter.o ABSOLUTE
    ..\..\..\..\..\..\components\libraries\fifo\app_fifo.c 0x00000000   Number         0  app_fifo.o ABSOLUTE
    ..\..\..\..\..\..\components\libraries\hardfault\hardfault_implementation.c 0x00000000   Number         0  hardfault_implementation.o ABSOLUTE
    ..\..\..\..\..\..\components\libraries\log\src\nrf_log_backend_rtt.c 0x00000000   Number         0  nrf_log_backend_rtt.o ABSOLUTE
    ..\..\..\..\..\..\components\libraries\log\src\nrf_log_backend_serial.c 0x00000000   Number         0  nrf_log_backend_serial.o ABSOLUTE
    ..\..\..\..\..\..\components\libraries\log\src\nrf_log_default_backends.c 0x00000000   Number         0  nrf_log_default_backends.o ABSOLUTE
    ..\..\..\..\..\..\components\libraries\log\src\nrf_log_frontend.c 0x00000000   Number         0  nrf_log_frontend.o ABSOLUTE
    ..\..\..\..\..\..\components\libraries\log\src\nrf_log_str_formatter.c 0x00000000   Number         0  nrf_log_str_formatter.o ABSOLUTE
    ..\..\..\..\..\..\components\libraries\memobj\nrf_memobj.c 0x00000000   Number         0  nrf_memobj.o ABSOLUTE
    ..\..\..\..\..\..\components\libraries\pwr_mgmt\nrf_pwr_mgmt.c 0x00000000   Number         0  nrf_pwr_mgmt.o ABSOLUTE
    ..\..\..\..\..\..\components\libraries\queue\nrf_queue.c 0x00000000   Number         0  nrf_queue.o ABSOLUTE
    ..\..\..\..\..\..\components\libraries\ringbuf\nrf_ringbuf.c 0x00000000   Number         0  nrf_ringbuf.o ABSOLUTE
    ..\..\..\..\..\..\components\libraries\scheduler\app_scheduler.c 0x00000000   Number         0  app_scheduler.o ABSOLUTE
    ..\..\..\..\..\..\components\libraries\sortlist\nrf_sortlist.c 0x00000000   Number         0  nrf_sortlist.o ABSOLUTE
    ..\..\..\..\..\..\components\libraries\strerror\nrf_strerror.c 0x00000000   Number         0  nrf_strerror.o ABSOLUTE
    ..\..\..\..\..\..\components\libraries\timer\app_timer2.c 0x00000000   Number         0  app_timer2.o ABSOLUTE
    ..\..\..\..\..\..\components\libraries\timer\drv_rtc.c 0x00000000   Number         0  drv_rtc.o ABSOLUTE
    ..\..\..\..\..\..\components\libraries\uart\app_uart_fifo.c 0x00000000   Number         0  app_uart_fifo.o ABSOLUTE
    ..\..\..\..\..\..\components\libraries\uart\retarget.c 0x00000000   Number         0  retarget.o ABSOLUTE
    ..\..\..\..\..\..\components\libraries\util\app_error.c 0x00000000   Number         0  app_error.o ABSOLUTE
    ..\..\..\..\..\..\components\libraries\util\app_error_handler_keil.c 0x00000000   Number         0  app_error_handler_keil.o ABSOLUTE
    ..\..\..\..\..\..\components\libraries\util\app_error_weak.c 0x00000000   Number         0  app_error_weak.o ABSOLUTE
    ..\..\..\..\..\..\components\libraries\util\app_util_platform.c 0x00000000   Number         0  app_util_platform.o ABSOLUTE
    ..\..\..\..\..\..\components\libraries\util\nrf_assert.c 0x00000000   Number         0  nrf_assert.o ABSOLUTE
    ..\..\..\..\..\..\components\softdevice\common\nrf_sdh.c 0x00000000   Number         0  nrf_sdh.o ABSOLUTE
    ..\..\..\..\..\..\components\softdevice\common\nrf_sdh_ble.c 0x00000000   Number         0  nrf_sdh_ble.o ABSOLUTE
    ..\..\..\..\..\..\components\softdevice\common\nrf_sdh_soc.c 0x00000000   Number         0  nrf_sdh_soc.o ABSOLUTE
    ..\..\..\..\..\..\external\fprintf\nrf_fprintf.c 0x00000000   Number         0  nrf_fprintf.o ABSOLUTE
    ..\..\..\..\..\..\external\fprintf\nrf_fprintf_format.c 0x00000000   Number         0  nrf_fprintf_format.o ABSOLUTE
    ..\..\..\..\..\..\external\segger_rtt\SEGGER_RTT.c 0x00000000   Number         0  segger_rtt.o ABSOLUTE
    ..\..\..\..\..\..\external\segger_rtt\SEGGER_RTT_Syscalls_KEIL.c 0x00000000   Number         0  segger_rtt_syscalls_keil.o ABSOLUTE
    ..\..\..\..\..\..\external\segger_rtt\SEGGER_RTT_printf.c 0x00000000   Number         0  segger_rtt_printf.o ABSOLUTE
    ..\..\..\..\..\..\external\utf_converter\utf.c 0x00000000   Number         0  utf.o ABSOLUTE
    ..\..\..\..\..\..\integration\nrfx\legacy\nrf_drv_clock.c 0x00000000   Number         0  nrf_drv_clock.o ABSOLUTE
    ..\..\..\..\..\..\integration\nrfx\legacy\nrf_drv_uart.c 0x00000000   Number         0  nrf_drv_uart.o ABSOLUTE
    ..\..\..\..\..\..\modules\nrfx\drivers\src\nrfx_clock.c 0x00000000   Number         0  nrfx_clock.o ABSOLUTE
    ..\..\..\..\..\..\modules\nrfx\drivers\src\nrfx_gpiote.c 0x00000000   Number         0  nrfx_gpiote.o ABSOLUTE
    ..\..\..\..\..\..\modules\nrfx\drivers\src\nrfx_uart.c 0x00000000   Number         0  nrfx_uart.o ABSOLUTE
    ..\..\..\..\..\..\modules\nrfx\drivers\src\nrfx_uarte.c 0x00000000   Number         0  nrfx_uarte.o ABSOLUTE
    ..\..\..\..\..\..\modules\nrfx\drivers\src\prs\nrfx_prs.c 0x00000000   Number         0  nrfx_prs.o ABSOLUTE
    ..\..\..\..\..\..\modules\nrfx\soc\nrfx_atomic.c 0x00000000   Number         0  nrfx_atomic.o ABSOLUTE
    ..\..\..\main.c                          0x00000000   Number         0  main.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\ble\\ble_db_discovery\\ble_db_discovery.c 0x00000000   Number         0  ble_db_discovery.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\ble\\common\\ble_advdata.c 0x00000000   Number         0  ble_advdata.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\ble\\common\\ble_srv_common.c 0x00000000   Number         0  ble_srv_common.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\ble\\nrf_ble_gatt\\nrf_ble_gatt.c 0x00000000   Number         0  nrf_ble_gatt.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\ble\\nrf_ble_gq\\nrf_ble_gq.c 0x00000000   Number         0  nrf_ble_gq.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\ble\\nrf_ble_scan\\nrf_ble_scan.c 0x00000000   Number         0  nrf_ble_scan.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\boards\\boards.c 0x00000000   Number         0  boards.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\libraries\\atomic\\nrf_atomic.c 0x00000000   Number         0  nrf_atomic.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\libraries\\atomic_fifo\\nrf_atfifo.c 0x00000000   Number         0  nrf_atfifo.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\libraries\\balloc\\nrf_balloc.c 0x00000000   Number         0  nrf_balloc.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\libraries\\bsp\\bsp.c 0x00000000   Number         0  bsp.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\libraries\\bsp\\bsp_btn_ble.c 0x00000000   Number         0  bsp_btn_ble.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\libraries\\button\\app_button.c 0x00000000   Number         0  app_button.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\libraries\\experimental_section_vars\\nrf_section_iter.c 0x00000000   Number         0  nrf_section_iter.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\libraries\\fifo\\app_fifo.c 0x00000000   Number         0  app_fifo.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\libraries\\hardfault\\hardfault_implementation.c 0x00000000   Number         0  hardfault_implementation.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\libraries\\log\\src\\nrf_log_backend_rtt.c 0x00000000   Number         0  nrf_log_backend_rtt.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\libraries\\log\\src\\nrf_log_backend_serial.c 0x00000000   Number         0  nrf_log_backend_serial.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\libraries\\log\\src\\nrf_log_default_backends.c 0x00000000   Number         0  nrf_log_default_backends.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\libraries\\log\\src\\nrf_log_frontend.c 0x00000000   Number         0  nrf_log_frontend.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\libraries\\log\\src\\nrf_log_str_formatter.c 0x00000000   Number         0  nrf_log_str_formatter.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\libraries\\memobj\\nrf_memobj.c 0x00000000   Number         0  nrf_memobj.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\libraries\\pwr_mgmt\\nrf_pwr_mgmt.c 0x00000000   Number         0  nrf_pwr_mgmt.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\libraries\\queue\\nrf_queue.c 0x00000000   Number         0  nrf_queue.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\libraries\\ringbuf\\nrf_ringbuf.c 0x00000000   Number         0  nrf_ringbuf.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\libraries\\scheduler\\app_scheduler.c 0x00000000   Number         0  app_scheduler.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\libraries\\sortlist\\nrf_sortlist.c 0x00000000   Number         0  nrf_sortlist.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\libraries\\strerror\\nrf_strerror.c 0x00000000   Number         0  nrf_strerror.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\libraries\\timer\\app_timer2.c 0x00000000   Number         0  app_timer2.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\libraries\\timer\\drv_rtc.c 0x00000000   Number         0  drv_rtc.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\libraries\\uart\\app_uart_fifo.c 0x00000000   Number         0  app_uart_fifo.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\libraries\\uart\\retarget.c 0x00000000   Number         0  retarget.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\libraries\\util\\app_error.c 0x00000000   Number         0  app_error.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\libraries\\util\\app_error_handler_keil.c 0x00000000   Number         0  app_error_handler_keil.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\libraries\\util\\app_error_weak.c 0x00000000   Number         0  app_error_weak.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\libraries\\util\\app_util_platform.c 0x00000000   Number         0  app_util_platform.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\libraries\\util\\nrf_assert.c 0x00000000   Number         0  nrf_assert.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\softdevice\\common\\nrf_sdh.c 0x00000000   Number         0  nrf_sdh.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\softdevice\\common\\nrf_sdh_ble.c 0x00000000   Number         0  nrf_sdh_ble.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\softdevice\\common\\nrf_sdh_soc.c 0x00000000   Number         0  nrf_sdh_soc.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\external\\fprintf\\nrf_fprintf.c 0x00000000   Number         0  nrf_fprintf.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\external\\fprintf\\nrf_fprintf_format.c 0x00000000   Number         0  nrf_fprintf_format.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\external\\segger_rtt\\SEGGER_RTT.c 0x00000000   Number         0  segger_rtt.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\external\\segger_rtt\\SEGGER_RTT_printf.c 0x00000000   Number         0  segger_rtt_printf.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\integration\\nrfx\\legacy\\nrf_drv_clock.c 0x00000000   Number         0  nrf_drv_clock.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\integration\\nrfx\\legacy\\nrf_drv_uart.c 0x00000000   Number         0  nrf_drv_uart.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\modules\\nrfx\\drivers\\src\\nrfx_clock.c 0x00000000   Number         0  nrfx_clock.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\modules\\nrfx\\drivers\\src\\nrfx_gpiote.c 0x00000000   Number         0  nrfx_gpiote.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\modules\\nrfx\\drivers\\src\\nrfx_uart.c 0x00000000   Number         0  nrfx_uart.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\modules\\nrfx\\drivers\\src\\nrfx_uarte.c 0x00000000   Number         0  nrfx_uarte.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\modules\\nrfx\\drivers\\src\\prs\\nrfx_prs.c 0x00000000   Number         0  nrfx_prs.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\modules\\nrfx\\soc\\nrfx_atomic.c 0x00000000   Number         0  nrfx_atomic.o ABSOLUTE
    ..\\..\\..\\main.c                       0x00000000   Number         0  main.o ABSOLUTE
    RTE\Device\nRF52840_xxAA\arm_startup_nrf52840.s 0x00000000   Number         0  arm_startup_nrf52840.o ABSOLUTE
    RTE\Device\nRF52840_xxAA\system_nrf52840.c 0x00000000   Number         0  system_nrf52840.o ABSOLUTE
    RTE\\Device\\nRF52840_xxAA\\system_nrf52840.c 0x00000000   Number         0  system_nrf52840.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    handlers.s                               0x00000000   Number         0  handlers.o ABSOLUTE
    init.s                                   0x00000000   Number         0  init.o ABSOLUTE
    RESET                                    0x00027000   Section      512  arm_startup_nrf52840.o(RESET)
    .ARM.Collect$$$$00000000                 0x00027200   Section        0  entry.o(.ARM.Collect$$$$00000000)
    .ARM.Collect$$$$00000001                 0x00027200   Section        4  entry2.o(.ARM.Collect$$$$00000001)
    .ARM.Collect$$$$00000004                 0x00027204   Section        4  entry5.o(.ARM.Collect$$$$00000004)
    .ARM.Collect$$$$00000008                 0x00027208   Section        0  entry7b.o(.ARM.Collect$$$$00000008)
    .ARM.Collect$$$$0000000A                 0x00027208   Section        0  entry8b.o(.ARM.Collect$$$$0000000A)
    .ARM.Collect$$$$0000000B                 0x00027208   Section        8  entry9a.o(.ARM.Collect$$$$0000000B)
    .ARM.Collect$$$$0000000D                 0x00027210   Section        0  entry10a.o(.ARM.Collect$$$$0000000D)
    .ARM.Collect$$$$0000000F                 0x00027210   Section        0  entry11a.o(.ARM.Collect$$$$0000000F)
    .ARM.Collect$$$$00002712                 0x00027210   Section        4  entry2.o(.ARM.Collect$$$$00002712)
    __lit__00000000                          0x00027210   Data           4  entry2.o(.ARM.Collect$$$$00002712)
    .emb_text                                0x00027214   Section      200  nrf_atfifo.o(.emb_text)
    $v0                                      0x00027214   Number         0  nrf_atfifo.o(.emb_text)
    .emb_text                                0x000272dc   Section      226  nrf_atomic.o(.emb_text)
    $v0                                      0x000272dc   Number         0  nrf_atomic.o(.emb_text)
    .text                                    0x000273c0   Section       36  arm_startup_nrf52840.o(.text)
    $v0                                      0x000273c0   Number         0  arm_startup_nrf52840.o(.text)
    .text                                    0x000273e4   Section        0  uldiv.o(.text)
    .text                                    0x00027446   Section        0  llshl.o(.text)
    .text                                    0x00027464   Section        0  ctype_o.o(.text)
    .text                                    0x0002746c   Section        0  memcpya.o(.text)
    .text                                    0x00027490   Section        0  memseta.o(.text)
    .text                                    0x000274b4   Section        0  strlen.o(.text)
    .text                                    0x000274c2   Section        0  memcmp.o(.text)
    .text                                    0x000274dc   Section        0  strcpy.o(.text)
    .text                                    0x000274ee   Section        0  llushr.o(.text)
    .text                                    0x00027510   Section       36  init.o(.text)
    i.GPIOTE_IRQHandler                      0x00027534   Section        0  nrfx_gpiote.o(i.GPIOTE_IRQHandler)
    i.POWER_CLOCK_IRQHandler                 0x000275d8   Section        0  nrfx_clock.o(i.POWER_CLOCK_IRQHandler)
    i.RTC1_IRQHandler                        0x00027630   Section        0  drv_rtc.o(i.RTC1_IRQHandler)
    i.SEGGER_RTT_Init                        0x00027640   Section        0  segger_rtt.o(i.SEGGER_RTT_Init)
    i.SEGGER_RTT_WriteNoLock                 0x00027644   Section        0  segger_rtt.o(i.SEGGER_RTT_WriteNoLock)
    i.SWI2_EGU2_IRQHandler                   0x000276a0   Section        0  nrf_sdh.o(i.SWI2_EGU2_IRQHandler)
    i.SystemInit                             0x000276a4   Section        0  system_nrf52840.o(i.SystemInit)
    i.UARTE0_UART0_IRQHandler                0x000278ec   Section        0  nrfx_prs.o(i.UARTE0_UART0_IRQHandler)
    i._DoInit                                0x000278f8   Section        0  segger_rtt.o(i._DoInit)
    _DoInit                                  0x000278f9   Thumb Code    76  segger_rtt.o(i._DoInit)
    i._GetAvailWriteSpace                    0x00027960   Section        0  segger_rtt.o(i._GetAvailWriteSpace)
    _GetAvailWriteSpace                      0x00027961   Thumb Code    22  segger_rtt.o(i._GetAvailWriteSpace)
    i._WriteBlocking                         0x00027976   Section        0  segger_rtt.o(i._WriteBlocking)
    _WriteBlocking                           0x00027977   Thumb Code    90  segger_rtt.o(i._WriteBlocking)
    i._WriteNoCheck                          0x000279d0   Section        0  segger_rtt.o(i._WriteNoCheck)
    _WriteNoCheck                            0x000279d1   Thumb Code    66  segger_rtt.o(i._WriteNoCheck)
    i.__scatterload_copy                     0x00027a12   Section       14  handlers.o(i.__scatterload_copy)
    i.__scatterload_null                     0x00027a20   Section        2  handlers.o(i.__scatterload_null)
    i.__scatterload_zeroinit                 0x00027a22   Section       14  handlers.o(i.__scatterload_zeroinit)
    i.__sd_nvic_app_accessible_irq           0x00027a30   Section        0  nrf_sdh.o(i.__sd_nvic_app_accessible_irq)
    __sd_nvic_app_accessible_irq             0x00027a31   Thumb Code    32  nrf_sdh.o(i.__sd_nvic_app_accessible_irq)
    i.advertising_buttons_configure          0x00027a54   Section        0  bsp_btn_ble.o(i.advertising_buttons_configure)
    advertising_buttons_configure            0x00027a55   Thumb Code    54  bsp_btn_ble.o(i.advertising_buttons_configure)
    i.alert_timer_handler                    0x00027a8a   Section        0  bsp.o(i.alert_timer_handler)
    alert_timer_handler                      0x00027a8b   Thumb Code     6  bsp.o(i.alert_timer_handler)
    i.app_button_enable                      0x00027a90   Section        0  app_button.o(i.app_button_enable)
    i.app_button_init                        0x00027ab4   Section        0  app_button.o(i.app_button_init)
    i.app_error_fault_handler                0x00027b34   Section        0  app_error_weak.o(i.app_error_fault_handler)
    i.app_error_handler_bare                 0x00027bb8   Section        0  app_error.o(i.app_error_handler_bare)
    i.app_timer_cnt_get                      0x00027bd0   Section        0  app_timer2.o(i.app_timer_cnt_get)
    i.app_timer_create                       0x00027bdc   Section        0  app_timer2.o(i.app_timer_create)
    i.app_timer_init                         0x00027bf4   Section        0  app_timer2.o(i.app_timer_init)
    i.app_timer_start                        0x00027c50   Section        0  app_timer2.o(i.app_timer_start)
    i.app_timer_stop                         0x00027c80   Section        0  app_timer2.o(i.app_timer_stop)
    i.app_util_critical_region_enter         0x00027c8c   Section        0  app_util_platform.o(i.app_util_critical_region_enter)
    i.app_util_critical_region_exit          0x00027cd4   Section        0  app_util_platform.o(i.app_util_critical_region_exit)
    i.ble_advdata_search                     0x00027d08   Section        0  ble_advdata.o(i.ble_advdata_search)
    i.ble_advdata_uuid_find                  0x00027d4e   Section        0  ble_advdata.o(i.ble_advdata_uuid_find)
    i.ble_evt_handler                        0x00027e04   Section        0  main.o(i.ble_evt_handler)
    ble_evt_handler                          0x00027e05   Thumb Code   174  main.o(i.ble_evt_handler)
    i.ble_evt_handler                        0x00027f0c   Section        0  bsp_btn_ble.o(i.ble_evt_handler)
    ble_evt_handler                          0x00027f0d   Thumb Code   106  bsp_btn_ble.o(i.ble_evt_handler)
    i.bsp_board_button_idx_to_pin            0x00027f7c   Section        0  boards.o(i.bsp_board_button_idx_to_pin)
    i.bsp_board_button_state_get             0x00027f88   Section        0  boards.o(i.bsp_board_button_state_get)
    i.bsp_board_init                         0x00027fb0   Section        0  boards.o(i.bsp_board_init)
    i.bsp_board_led_invert                   0x00028004   Section        0  boards.o(i.bsp_board_led_invert)
    i.bsp_board_led_off                      0x00028030   Section        0  boards.o(i.bsp_board_led_off)
    i.bsp_board_led_on                       0x00028040   Section        0  boards.o(i.bsp_board_led_on)
    i.bsp_board_led_state_get                0x00028050   Section        0  boards.o(i.bsp_board_led_state_get)
    i.bsp_board_leds_off                     0x00028078   Section        0  boards.o(i.bsp_board_leds_off)
    i.bsp_board_leds_on                      0x0002808a   Section        0  boards.o(i.bsp_board_leds_on)
    i.bsp_board_pin_to_button_idx            0x0002809c   Section        0  boards.o(i.bsp_board_pin_to_button_idx)
    i.bsp_btn_ble_init                       0x000280c0   Section        0  bsp_btn_ble.o(i.bsp_btn_ble_init)
    i.bsp_btn_ble_sleep_mode_prepare         0x000280f8   Section        0  bsp_btn_ble.o(i.bsp_btn_ble_sleep_mode_prepare)
    i.bsp_button_event_handler               0x00028118   Section        0  bsp.o(i.bsp_button_event_handler)
    bsp_button_event_handler                 0x00028119   Thumb Code   132  bsp.o(i.bsp_button_event_handler)
    i.bsp_button_is_pressed                  0x000281a8   Section        0  bsp.o(i.bsp_button_is_pressed)
    i.bsp_event_handler                      0x000281b4   Section        0  main.o(i.bsp_event_handler)
    i.bsp_event_to_button_action_assign      0x000281d0   Section        0  bsp.o(i.bsp_event_to_button_action_assign)
    i.bsp_indication_set                     0x00028214   Section        0  bsp.o(i.bsp_indication_set)
    i.bsp_init                               0x0002822c   Section        0  bsp.o(i.bsp_init)
    i.bsp_led_indication                     0x000282c0   Section        0  bsp.o(i.bsp_led_indication)
    bsp_led_indication                       0x000282c1   Thumb Code   446  bsp.o(i.bsp_led_indication)
    i.bsp_wakeup_button_enable               0x00028488   Section        0  bsp.o(i.bsp_wakeup_button_enable)
    i.buf_prealloc                           0x00028490   Section        0  nrf_log_frontend.o(i.buf_prealloc)
    buf_prealloc                             0x00028491   Thumb Code   140  nrf_log_frontend.o(i.buf_prealloc)
    i.buffer_add                             0x00028524   Section        0  nrf_fprintf_format.o(i.buffer_add)
    buffer_add                               0x00028525   Thumb Code    46  nrf_fprintf_format.o(i.buffer_add)
    i.buffer_is_empty                        0x00028554   Section        0  nrf_log_frontend.o(i.buffer_is_empty)
    i.button_get                             0x0002856c   Section        0  app_button.o(i.button_get)
    button_get                               0x0002856d   Thumb Code    36  app_button.o(i.button_get)
    i.button_timer_handler                   0x00028594   Section        0  bsp.o(i.button_timer_handler)
    button_timer_handler                     0x00028595   Thumb Code     8  bsp.o(i.button_timer_handler)
    i.channel_free                           0x0002859c   Section        0  nrfx_gpiote.o(i.channel_free)
    channel_free                             0x0002859d   Thumb Code    22  nrfx_gpiote.o(i.channel_free)
    i.channel_port_alloc                     0x000285b8   Section        0  nrfx_gpiote.o(i.channel_port_alloc)
    channel_port_alloc                       0x000285b9   Thumb Code    66  nrfx_gpiote.o(i.channel_port_alloc)
    i.channel_port_get                       0x00028600   Section        0  nrfx_gpiote.o(i.channel_port_get)
    channel_port_get                         0x00028601   Thumb Code    10  nrfx_gpiote.o(i.channel_port_get)
    i.clock_clk_started_notify               0x00028610   Section        0  nrf_drv_clock.o(i.clock_clk_started_notify)
    clock_clk_started_notify                 0x00028611   Thumb Code    34  nrf_drv_clock.o(i.clock_clk_started_notify)
    i.clock_irq_handler                      0x00028638   Section        0  nrf_drv_clock.o(i.clock_irq_handler)
    clock_irq_handler                        0x00028639   Thumb Code    24  nrf_drv_clock.o(i.clock_irq_handler)
    i.compare_func                           0x00028654   Section        0  app_timer2.o(i.compare_func)
    compare_func                             0x00028655   Thumb Code    24  app_timer2.o(i.compare_func)
    i.data_length_update                     0x0002866c   Section        0  nrf_ble_gatt.o(i.data_length_update)
    data_length_update                       0x0002866d   Thumb Code   110  nrf_ble_gatt.o(i.data_length_update)
    i.detection_delay_timeout_handler        0x0002872c   Section        0  app_button.o(i.detection_delay_timeout_handler)
    detection_delay_timeout_handler          0x0002872d   Thumb Code    66  app_button.o(i.detection_delay_timeout_handler)
    i.dropped_sat16_get                      0x00028774   Section        0  nrf_log_frontend.o(i.dropped_sat16_get)
    dropped_sat16_get                        0x00028775   Thumb Code    16  nrf_log_frontend.o(i.dropped_sat16_get)
    i.drv_rtc_compare_disable                0x00028788   Section        0  drv_rtc.o(i.drv_rtc_compare_disable)
    i.drv_rtc_compare_pending                0x0002879c   Section        0  drv_rtc.o(i.drv_rtc_compare_pending)
    i.drv_rtc_compare_set                    0x000287aa   Section        0  drv_rtc.o(i.drv_rtc_compare_set)
    i.drv_rtc_counter_get                    0x000287fa   Section        0  drv_rtc.o(i.drv_rtc_counter_get)
    i.drv_rtc_init                           0x00028804   Section        0  drv_rtc.o(i.drv_rtc_init)
    i.drv_rtc_irq_trigger                    0x000288dc   Section        0  drv_rtc.o(i.drv_rtc_irq_trigger)
    i.drv_rtc_overflow_enable                0x000288fa   Section        0  drv_rtc.o(i.drv_rtc_overflow_enable)
    i.drv_rtc_overflow_pending               0x00028902   Section        0  drv_rtc.o(i.drv_rtc_overflow_pending)
    i.drv_rtc_start                          0x0002890a   Section        0  drv_rtc.o(i.drv_rtc_start)
    i.drv_rtc_stop                           0x00028912   Section        0  drv_rtc.o(i.drv_rtc_stop)
    i.drv_rtc_windowed_compare_set           0x0002891a   Section        0  drv_rtc.o(i.drv_rtc_windowed_compare_set)
    i.evt_enable                             0x000289f8   Section        0  drv_rtc.o(i.evt_enable)
    evt_enable                               0x000289f9   Thumb Code    18  drv_rtc.o(i.evt_enable)
    i.evt_handle                             0x00028a0c   Section        0  app_button.o(i.evt_handle)
    i.evt_pending                            0x00028aec   Section        0  drv_rtc.o(i.evt_pending)
    evt_pending                              0x00028aed   Thumb Code    20  drv_rtc.o(i.evt_pending)
    i.gatt_evt_handler                       0x00028b00   Section        0  main.o(i.gatt_evt_handler)
    i.gatt_init                              0x00028b98   Section        0  main.o(i.gatt_init)
    i.get_now                                0x00028bb8   Section        0  app_timer2.o(i.get_now)
    get_now                                  0x00028bb9   Thumb Code    46  app_timer2.o(i.get_now)
    i.gpiote_event_handler                   0x00028bec   Section        0  app_button.o(i.gpiote_event_handler)
    gpiote_event_handler                     0x00028bed   Thumb Code    56  app_button.o(i.gpiote_event_handler)
    i.int_print                              0x00028c28   Section        0  nrf_fprintf_format.o(i.int_print)
    int_print                                0x00028c29   Thumb Code   166  nrf_fprintf_format.o(i.int_print)
    i.invalid_packets_omit                   0x00028cce   Section        0  nrf_log_frontend.o(i.invalid_packets_omit)
    invalid_packets_omit                     0x00028ccf   Thumb Code    52  nrf_log_frontend.o(i.invalid_packets_omit)
    i.is_whitelist_used                      0x00028d02   Section        0  nrf_ble_scan.o(i.is_whitelist_used)
    i.leds_off                               0x00028d18   Section        0  bsp.o(i.leds_off)
    leds_off                                 0x00028d19   Thumb Code    42  bsp.o(i.leds_off)
    i.leds_timer_handler                     0x00028d48   Section        0  bsp.o(i.leds_timer_handler)
    leds_timer_handler                       0x00028d49   Thumb Code    16  bsp.o(i.leds_timer_handler)
    i.link_init                              0x00028d5c   Section        0  nrf_ble_gatt.o(i.link_init)
    link_init                                0x00028d5d   Thumb Code    24  nrf_ble_gatt.o(i.link_init)
    i.log_skip                               0x00028d74   Section        0  nrf_log_frontend.o(i.log_skip)
    log_skip                                 0x00028d75   Thumb Code   150  nrf_log_frontend.o(i.log_skip)
    i.main                                   0x00028e18   Section        0  main.o(i.main)
    i.memobj_op                              0x00028eec   Section        0  nrf_memobj.o(i.memobj_op)
    memobj_op                                0x00028eed   Thumb Code   126  nrf_memobj.o(i.memobj_op)
    i.module_idx_get                         0x00028f6c   Section        0  nrf_log_frontend.o(i.module_idx_get)
    module_idx_get                           0x00028f6d   Thumb Code    42  nrf_log_frontend.o(i.module_idx_get)
    i.nrf_atfifo_init                        0x00028f9c   Section        0  nrf_atfifo.o(i.nrf_atfifo_init)
    i.nrf_atfifo_item_alloc                  0x00028fc2   Section        0  nrf_atfifo.o(i.nrf_atfifo_item_alloc)
    i.nrf_atfifo_item_free                   0x00028fd8   Section        0  nrf_atfifo.o(i.nrf_atfifo_item_free)
    i.nrf_atfifo_item_get                    0x00028fee   Section        0  nrf_atfifo.o(i.nrf_atfifo_item_get)
    i.nrf_atfifo_item_put                    0x00029004   Section        0  nrf_atfifo.o(i.nrf_atfifo_item_put)
    i.nrf_atomic_flag_clear_fetch            0x0002901a   Section        0  nrf_atomic.o(i.nrf_atomic_flag_clear_fetch)
    i.nrf_atomic_flag_set                    0x00029020   Section        0  nrf_atomic.o(i.nrf_atomic_flag_set)
    i.nrf_atomic_u32_add                     0x00029026   Section        0  nrf_atomic.o(i.nrf_atomic_u32_add)
    i.nrf_atomic_u32_fetch_and               0x00029032   Section        0  nrf_atomic.o(i.nrf_atomic_u32_fetch_and)
    i.nrf_atomic_u32_fetch_store             0x0002903c   Section        0  nrf_atomic.o(i.nrf_atomic_u32_fetch_store)
    i.nrf_atomic_u32_or                      0x00029046   Section        0  nrf_atomic.o(i.nrf_atomic_u32_or)
    i.nrf_atomic_u32_sub                     0x00029052   Section        0  nrf_atomic.o(i.nrf_atomic_u32_sub)
    i.nrf_balloc_alloc                       0x0002905e   Section        0  nrf_balloc.o(i.nrf_balloc_alloc)
    i.nrf_balloc_free                        0x000290a2   Section        0  nrf_balloc.o(i.nrf_balloc_free)
    i.nrf_balloc_init                        0x000290d2   Section        0  nrf_balloc.o(i.nrf_balloc_init)
    i.nrf_bitmask_bit_is_set                 0x00029102   Section        0  nrfx_gpiote.o(i.nrf_bitmask_bit_is_set)
    nrf_bitmask_bit_is_set                   0x00029103   Thumb Code    16  nrfx_gpiote.o(i.nrf_bitmask_bit_is_set)
    i.nrf_ble_gatt_init                      0x00029112   Section        0  nrf_ble_gatt.o(i.nrf_ble_gatt_init)
    i.nrf_ble_gatt_on_ble_evt                0x00029134   Section        0  nrf_ble_gatt.o(i.nrf_ble_gatt_on_ble_evt)
    i.nrf_ble_scan_connect_with_target       0x00029284   Section        0  nrf_ble_scan.o(i.nrf_ble_scan_connect_with_target)
    nrf_ble_scan_connect_with_target         0x00029285   Thumb Code    76  nrf_ble_scan.o(i.nrf_ble_scan_connect_with_target)
    i.nrf_ble_scan_default_conn_param_set    0x000292d0   Section        0  nrf_ble_scan.o(i.nrf_ble_scan_default_conn_param_set)
    nrf_ble_scan_default_conn_param_set      0x000292d1   Thumb Code    20  nrf_ble_scan.o(i.nrf_ble_scan_default_conn_param_set)
    i.nrf_ble_scan_default_param_set         0x000292e4   Section        0  nrf_ble_scan.o(i.nrf_ble_scan_default_param_set)
    nrf_ble_scan_default_param_set           0x000292e5   Thumb Code    30  nrf_ble_scan.o(i.nrf_ble_scan_default_param_set)
    i.nrf_ble_scan_filter_set                0x00029302   Section        0  nrf_ble_scan.o(i.nrf_ble_scan_filter_set)
    i.nrf_ble_scan_filters_disable           0x00029344   Section        0  nrf_ble_scan.o(i.nrf_ble_scan_filters_disable)
    i.nrf_ble_scan_filters_enable            0x00029352   Section        0  nrf_ble_scan.o(i.nrf_ble_scan_filters_enable)
    i.nrf_ble_scan_init                      0x00029388   Section        0  nrf_ble_scan.o(i.nrf_ble_scan_init)
    i.nrf_ble_scan_on_adv_report             0x000293f2   Section        0  nrf_ble_scan.o(i.nrf_ble_scan_on_adv_report)
    nrf_ble_scan_on_adv_report               0x000293f3   Thumb Code   252  nrf_ble_scan.o(i.nrf_ble_scan_on_adv_report)
    i.nrf_ble_scan_on_ble_evt                0x000294ee   Section        0  nrf_ble_scan.o(i.nrf_ble_scan_on_ble_evt)
    i.nrf_ble_scan_start                     0x00029570   Section        0  nrf_ble_scan.o(i.nrf_ble_scan_start)
    i.nrf_clock_event_check                  0x000295f8   Section        0  nrfx_clock.o(i.nrf_clock_event_check)
    nrf_clock_event_check                    0x000295f9   Thumb Code    14  nrfx_clock.o(i.nrf_clock_event_check)
    i.nrf_clock_event_clear                  0x00029606   Section        0  nrfx_clock.o(i.nrf_clock_event_clear)
    nrf_clock_event_clear                    0x00029607   Thumb Code    16  nrfx_clock.o(i.nrf_clock_event_clear)
    i.nrf_drv_clock_init                     0x00029618   Section        0  nrf_drv_clock.o(i.nrf_drv_clock_init)
    i.nrf_drv_clock_lfclk_release            0x00029658   Section        0  nrf_drv_clock.o(i.nrf_drv_clock_lfclk_release)
    i.nrf_fprintf                            0x0002968c   Section        0  nrf_fprintf.o(i.nrf_fprintf)
    i.nrf_fprintf_buffer_flush               0x000296a6   Section        0  nrf_fprintf.o(i.nrf_fprintf_buffer_flush)
    i.nrf_fprintf_fmt                        0x000296be   Section        0  nrf_fprintf_format.o(i.nrf_fprintf_fmt)
    i.nrf_gpio_cfg                           0x00029898   Section        0  boards.o(i.nrf_gpio_cfg)
    nrf_gpio_cfg                             0x00029899   Thumb Code    50  boards.o(i.nrf_gpio_cfg)
    i.nrf_gpio_cfg                           0x000298ca   Section        0  nrfx_gpiote.o(i.nrf_gpio_cfg)
    nrf_gpio_cfg                             0x000298cb   Thumb Code    50  nrfx_gpiote.o(i.nrf_gpio_cfg)
    i.nrf_gpio_cfg_sense_set                 0x000298fc   Section        0  nrfx_gpiote.o(i.nrf_gpio_cfg_sense_set)
    nrf_gpio_cfg_sense_set                   0x000298fd   Thumb Code    48  nrfx_gpiote.o(i.nrf_gpio_cfg_sense_set)
    i.nrf_gpio_latches_read_and_clear        0x0002992c   Section        0  nrfx_gpiote.o(i.nrf_gpio_latches_read_and_clear)
    nrf_gpio_latches_read_and_clear          0x0002992d   Thumb Code    42  nrfx_gpiote.o(i.nrf_gpio_latches_read_and_clear)
    i.nrf_gpio_pin_port_decode               0x0002995c   Section        0  boards.o(i.nrf_gpio_pin_port_decode)
    nrf_gpio_pin_port_decode                 0x0002995d   Thumb Code    22  boards.o(i.nrf_gpio_pin_port_decode)
    i.nrf_gpio_pin_port_decode               0x00029978   Section        0  nrfx_gpiote.o(i.nrf_gpio_pin_port_decode)
    nrf_gpio_pin_port_decode                 0x00029979   Thumb Code    22  nrfx_gpiote.o(i.nrf_gpio_pin_port_decode)
    i.nrf_gpio_pin_present_check             0x00029994   Section        0  nrfx_gpiote.o(i.nrf_gpio_pin_present_check)
    nrf_gpio_pin_present_check               0x00029995   Thumb Code    44  nrfx_gpiote.o(i.nrf_gpio_pin_present_check)
    i.nrf_gpio_pin_read                      0x000299c0   Section        0  nrfx_gpiote.o(i.nrf_gpio_pin_read)
    nrf_gpio_pin_read                        0x000299c1   Thumb Code    22  nrfx_gpiote.o(i.nrf_gpio_pin_read)
    i.nrf_gpio_pin_write                     0x000299d6   Section        0  boards.o(i.nrf_gpio_pin_write)
    nrf_gpio_pin_write                       0x000299d7   Thumb Code    38  boards.o(i.nrf_gpio_pin_write)
    i.nrf_gpiote_event_clear                 0x000299fc   Section        0  nrfx_gpiote.o(i.nrf_gpiote_event_clear)
    nrf_gpiote_event_clear                   0x000299fd   Thumb Code    16  nrfx_gpiote.o(i.nrf_gpiote_event_clear)
    i.nrf_gpiote_event_is_set                0x00029a10   Section        0  nrfx_gpiote.o(i.nrf_gpiote_event_is_set)
    nrf_gpiote_event_is_set                  0x00029a11   Thumb Code    12  nrfx_gpiote.o(i.nrf_gpiote_event_is_set)
    i.nrf_log_backend_add                    0x00029a20   Section        0  nrf_log_frontend.o(i.nrf_log_backend_add)
    i.nrf_log_backend_rtt_flush              0x00029a7c   Section        0  nrf_log_backend_rtt.o(i.nrf_log_backend_rtt_flush)
    nrf_log_backend_rtt_flush                0x00029a7d   Thumb Code     2  nrf_log_backend_rtt.o(i.nrf_log_backend_rtt_flush)
    i.nrf_log_backend_rtt_init               0x00029a7e   Section        0  nrf_log_backend_rtt.o(i.nrf_log_backend_rtt_init)
    i.nrf_log_backend_rtt_panic_set          0x00029a82   Section        0  nrf_log_backend_rtt.o(i.nrf_log_backend_rtt_panic_set)
    nrf_log_backend_rtt_panic_set            0x00029a83   Thumb Code     2  nrf_log_backend_rtt.o(i.nrf_log_backend_rtt_panic_set)
    i.nrf_log_backend_rtt_put                0x00029a84   Section        0  nrf_log_backend_rtt.o(i.nrf_log_backend_rtt_put)
    nrf_log_backend_rtt_put                  0x00029a85   Thumb Code    16  nrf_log_backend_rtt.o(i.nrf_log_backend_rtt_put)
    i.nrf_log_backend_serial_put             0x00029a9c   Section        0  nrf_log_backend_serial.o(i.nrf_log_backend_serial_put)
    i.nrf_log_color_id_get                   0x00029b64   Section        0  nrf_log_frontend.o(i.nrf_log_color_id_get)
    i.nrf_log_default_backends_init          0x00029b94   Section        0  nrf_log_default_backends.o(i.nrf_log_default_backends_init)
    i.nrf_log_frontend_dequeue               0x00029bb0   Section        0  nrf_log_frontend.o(i.nrf_log_frontend_dequeue)
    i.nrf_log_frontend_std_0                 0x00029de8   Section        0  nrf_log_frontend.o(i.nrf_log_frontend_std_0)
    i.nrf_log_frontend_std_1                 0x00029df0   Section        0  nrf_log_frontend.o(i.nrf_log_frontend_std_1)
    i.nrf_log_frontend_std_2                 0x00029dfe   Section        0  nrf_log_frontend.o(i.nrf_log_frontend_std_2)
    i.nrf_log_frontend_std_6                 0x00029e0e   Section        0  nrf_log_frontend.o(i.nrf_log_frontend_std_6)
    i.nrf_log_hexdump_entry_process          0x00029e2c   Section        0  nrf_log_str_formatter.o(i.nrf_log_hexdump_entry_process)
    i.nrf_log_init                           0x00029edc   Section        0  nrf_log_frontend.o(i.nrf_log_init)
    i.nrf_log_module_cnt_get                 0x00029f04   Section        0  nrf_log_frontend.o(i.nrf_log_module_cnt_get)
    i.nrf_log_module_name_get                0x00029f18   Section        0  nrf_log_frontend.o(i.nrf_log_module_name_get)
    i.nrf_log_panic                          0x00029f34   Section        0  nrf_log_frontend.o(i.nrf_log_panic)
    i.nrf_log_std_entry_process              0x00029f5c   Section        0  nrf_log_str_formatter.o(i.nrf_log_std_entry_process)
    i.nrf_memobj_alloc                       0x0002a012   Section        0  nrf_memobj.o(i.nrf_memobj_alloc)
    i.nrf_memobj_free                        0x0002a072   Section        0  nrf_memobj.o(i.nrf_memobj_free)
    i.nrf_memobj_get                         0x0002a0a4   Section        0  nrf_memobj.o(i.nrf_memobj_get)
    i.nrf_memobj_pool_init                   0x0002a0ac   Section        0  nrf_memobj.o(i.nrf_memobj_pool_init)
    i.nrf_memobj_put                         0x0002a0b0   Section        0  nrf_memobj.o(i.nrf_memobj_put)
    i.nrf_memobj_read                        0x0002a0ce   Section        0  nrf_memobj.o(i.nrf_memobj_read)
    i.nrf_memobj_write                       0x0002a0de   Section        0  nrf_memobj.o(i.nrf_memobj_write)
    i.nrf_pwr_mgmt_init                      0x0002a0f0   Section        0  nrf_pwr_mgmt.o(i.nrf_pwr_mgmt_init)
    i.nrf_pwr_mgmt_run                       0x0002a118   Section        0  nrf_pwr_mgmt.o(i.nrf_pwr_mgmt_run)
    i.nrf_pwr_mgmt_shutdown                  0x0002a15c   Section        0  nrf_pwr_mgmt.o(i.nrf_pwr_mgmt_shutdown)
    i.nrf_ringbuf_init                       0x0002a198   Section        0  nrf_ringbuf.o(i.nrf_ringbuf_init)
    i.nrf_rtc_event_clear                    0x0002a1b4   Section        0  drv_rtc.o(i.nrf_rtc_event_clear)
    nrf_rtc_event_clear                      0x0002a1b5   Thumb Code    12  drv_rtc.o(i.nrf_rtc_event_clear)
    i.nrf_sdh_ble_app_ram_start_get          0x0002a1c0   Section        0  nrf_sdh_ble.o(i.nrf_sdh_ble_app_ram_start_get)
    i.nrf_sdh_ble_default_cfg_set            0x0002a1d4   Section        0  nrf_sdh_ble.o(i.nrf_sdh_ble_default_cfg_set)
    i.nrf_sdh_ble_enable                     0x0002a304   Section        0  nrf_sdh_ble.o(i.nrf_sdh_ble_enable)
    i.nrf_sdh_ble_evts_poll                  0x0002a434   Section        0  nrf_sdh_ble.o(i.nrf_sdh_ble_evts_poll)
    nrf_sdh_ble_evts_poll                    0x0002a435   Thumb Code    88  nrf_sdh_ble.o(i.nrf_sdh_ble_evts_poll)
    i.nrf_sdh_enable_request                 0x0002a494   Section        0  nrf_sdh.o(i.nrf_sdh_enable_request)
    i.nrf_sdh_evts_poll                      0x0002a508   Section        0  nrf_sdh.o(i.nrf_sdh_evts_poll)
    i.nrf_sdh_is_enabled                     0x0002a52c   Section        0  nrf_sdh.o(i.nrf_sdh_is_enabled)
    i.nrf_sdh_soc_evts_poll                  0x0002a538   Section        0  nrf_sdh_soc.o(i.nrf_sdh_soc_evts_poll)
    nrf_sdh_soc_evts_poll                    0x0002a539   Thumb Code    56  nrf_sdh_soc.o(i.nrf_sdh_soc_evts_poll)
    i.nrf_section_iter_init                  0x0002a574   Section        0  nrf_section_iter.o(i.nrf_section_iter_init)
    i.nrf_section_iter_item_set              0x0002a57e   Section        0  nrf_section_iter.o(i.nrf_section_iter_item_set)
    nrf_section_iter_item_set                0x0002a57f   Thumb Code    36  nrf_section_iter.o(i.nrf_section_iter_item_set)
    i.nrf_section_iter_next                  0x0002a5a2   Section        0  nrf_section_iter.o(i.nrf_section_iter_next)
    i.nrf_sortlist_add                       0x0002a5c2   Section        0  nrf_sortlist.o(i.nrf_sortlist_add)
    i.nrf_sortlist_peek                      0x0002a5e4   Section        0  nrf_sortlist.o(i.nrf_sortlist_peek)
    i.nrf_sortlist_pop                       0x0002a5ea   Section        0  nrf_sortlist.o(i.nrf_sortlist_pop)
    i.nrf_sortlist_remove                    0x0002a5f8   Section        0  nrf_sortlist.o(i.nrf_sortlist_remove)
    i.nrf_strerror_find                      0x0002a618   Section        0  nrf_strerror.o(i.nrf_strerror_find)
    i.nrf_strerror_get                       0x0002a650   Section        0  nrf_strerror.o(i.nrf_strerror_get)
    i.nrf_wdt_started                        0x0002a664   Section        0  nrf_drv_clock.o(i.nrf_wdt_started)
    nrf_wdt_started                          0x0002a665   Thumb Code    12  nrf_drv_clock.o(i.nrf_wdt_started)
    i.nrfx_clock_enable                      0x0002a674   Section        0  nrfx_clock.o(i.nrfx_clock_enable)
    i.nrfx_clock_init                        0x0002a6a0   Section        0  nrfx_clock.o(i.nrfx_clock_init)
    i.nrfx_clock_lfclk_stop                  0x0002a6c0   Section        0  nrfx_clock.o(i.nrfx_clock_lfclk_stop)
    i.nrfx_coredep_delay_us                  0x0002a6f0   Section        0  drv_rtc.o(i.nrfx_coredep_delay_us)
    nrfx_coredep_delay_us                    0x0002a6f1   Thumb Code    12  drv_rtc.o(i.nrfx_coredep_delay_us)
    i.nrfx_gpiote_in_event_enable            0x0002a700   Section        0  nrfx_gpiote.o(i.nrfx_gpiote_in_event_enable)
    i.nrfx_gpiote_in_init                    0x0002a798   Section        0  nrfx_gpiote.o(i.nrfx_gpiote_in_init)
    i.nrfx_gpiote_in_is_set                  0x0002a868   Section        0  nrfx_gpiote.o(i.nrfx_gpiote_in_is_set)
    i.nrfx_gpiote_init                       0x0002a878   Section        0  nrfx_gpiote.o(i.nrfx_gpiote_init)
    i.nrfx_gpiote_is_init                    0x0002a8f0   Section        0  nrfx_gpiote.o(i.nrfx_gpiote_is_init)
    i.on_connected_evt                       0x0002a904   Section        0  nrf_ble_gatt.o(i.on_connected_evt)
    on_connected_evt                         0x0002a905   Thumb Code   106  nrf_ble_gatt.o(i.on_connected_evt)
    i.on_exchange_mtu_request_evt            0x0002a9ac   Section        0  nrf_ble_gatt.o(i.on_exchange_mtu_request_evt)
    on_exchange_mtu_request_evt              0x0002a9ad   Thumb Code   110  nrf_ble_gatt.o(i.on_exchange_mtu_request_evt)
    i.pin_configured_set                     0x0002aa58   Section        0  nrfx_gpiote.o(i.pin_configured_set)
    pin_configured_set                       0x0002aa59   Thumb Code    22  nrfx_gpiote.o(i.pin_configured_set)
    i.pin_in_use_by_port                     0x0002aa74   Section        0  nrfx_gpiote.o(i.pin_in_use_by_port)
    pin_in_use_by_port                       0x0002aa75   Thumb Code    20  nrfx_gpiote.o(i.pin_in_use_by_port)
    i.pin_in_use_by_te                       0x0002aa8c   Section        0  nrfx_gpiote.o(i.pin_in_use_by_te)
    pin_in_use_by_te                         0x0002aa8d   Thumb Code    20  nrfx_gpiote.o(i.pin_in_use_by_te)
    i.port_event_handle                      0x0002aaa4   Section        0  nrfx_gpiote.o(i.port_event_handle)
    port_event_handle                        0x0002aaa5   Thumb Code   202  nrfx_gpiote.o(i.port_event_handle)
    i.port_handler_polarity_get              0x0002ab74   Section        0  nrfx_gpiote.o(i.port_handler_polarity_get)
    port_handler_polarity_get                0x0002ab75   Thumb Code    12  nrfx_gpiote.o(i.port_handler_polarity_get)
    i.postfix_process                        0x0002ab84   Section        0  nrf_log_str_formatter.o(i.postfix_process)
    postfix_process                          0x0002ab85   Thumb Code    48  nrf_log_str_formatter.o(i.postfix_process)
    i.prefix_process                         0x0002abc0   Section        0  nrf_log_str_formatter.o(i.prefix_process)
    prefix_process                           0x0002abc1   Thumb Code    90  nrf_log_str_formatter.o(i.prefix_process)
    i.rtc_irq                                0x0002ac58   Section        0  app_timer2.o(i.rtc_irq)
    rtc_irq                                  0x0002ac59   Thumb Code    88  app_timer2.o(i.rtc_irq)
    i.rtc_schedule                           0x0002acb4   Section        0  app_timer2.o(i.rtc_schedule)
    rtc_schedule                             0x0002acb5   Thumb Code    92  app_timer2.o(i.rtc_schedule)
    i.rtc_update                             0x0002ad18   Section        0  app_timer2.o(i.rtc_update)
    rtc_update                               0x0002ad19   Thumb Code   108  app_timer2.o(i.rtc_update)
    i.scan_evt_handler                       0x0002ad8c   Section        0  main.o(i.scan_evt_handler)
    scan_evt_handler                         0x0002ad8d   Thumb Code    88  main.o(i.scan_evt_handler)
    i.scan_init                              0x0002ae2c   Section        0  main.o(i.scan_init)
    scan_init                                0x0002ae2d   Thumb Code    74  main.o(i.scan_init)
    i.scan_start                             0x0002ae84   Section        0  main.o(i.scan_start)
    scan_start                               0x0002ae85   Thumb Code    34  main.o(i.scan_start)
    i.sd_state_evt_handler                   0x0002aeac   Section        0  nrf_drv_clock.o(i.sd_state_evt_handler)
    sd_state_evt_handler                     0x0002aead   Thumb Code    84  nrf_drv_clock.o(i.sd_state_evt_handler)
    i.sdh_request_observer_notify            0x0002af04   Section        0  nrf_sdh.o(i.sdh_request_observer_notify)
    sdh_request_observer_notify              0x0002af05   Thumb Code    44  nrf_sdh.o(i.sdh_request_observer_notify)
    i.sdh_state_observer_notify              0x0002af34   Section        0  nrf_sdh.o(i.sdh_state_observer_notify)
    sdh_state_observer_notify                0x0002af35   Thumb Code    38  nrf_sdh.o(i.sdh_state_observer_notify)
    i.serial_tx                              0x0002af60   Section        0  nrf_log_backend_rtt.o(i.serial_tx)
    serial_tx                                0x0002af61   Thumb Code    70  nrf_log_backend_rtt.o(i.serial_tx)
    i.shutdown_handler                       0x0002afb0   Section        0  main.o(i.shutdown_handler)
    shutdown_handler                         0x0002afb1   Thumb Code    32  main.o(i.shutdown_handler)
    i.shutdown_process                       0x0002afd0   Section        0  nrf_pwr_mgmt.o(i.shutdown_process)
    shutdown_process                         0x0002afd1   Thumb Code   104  nrf_pwr_mgmt.o(i.shutdown_process)
    i.soc_evt_handler                        0x0002b04c   Section        0  nrf_drv_clock.o(i.soc_evt_handler)
    soc_evt_handler                          0x0002b04d   Thumb Code    18  nrf_drv_clock.o(i.soc_evt_handler)
    i.softdevices_evt_irq_enable             0x0002b064   Section        0  nrf_sdh.o(i.softdevices_evt_irq_enable)
    softdevices_evt_irq_enable               0x0002b065   Thumb Code    80  nrf_sdh.o(i.softdevices_evt_irq_enable)
    i.sortlist_pop                           0x0002b0bc   Section        0  app_timer2.o(i.sortlist_pop)
    sortlist_pop                             0x0002b0bd   Thumb Code     6  app_timer2.o(i.sortlist_pop)
    i.state_set                              0x0002b0c8   Section        0  app_button.o(i.state_set)
    state_set                                0x0002b0c9   Thumb Code    42  app_button.o(i.state_set)
    i.std_n                                  0x0002b0f8   Section        0  nrf_log_frontend.o(i.std_n)
    std_n                                    0x0002b0f9   Thumb Code   140  nrf_log_frontend.o(i.std_n)
    i.timer_expire                           0x0002b18c   Section        0  app_timer2.o(i.timer_expire)
    timer_expire                             0x0002b18d   Thumb Code    80  app_timer2.o(i.timer_expire)
    i.timer_req_process                      0x0002b1e4   Section        0  app_timer2.o(i.timer_req_process)
    timer_req_process                        0x0002b1e5   Thumb Code   110  app_timer2.o(i.timer_req_process)
    i.timer_req_schedule                     0x0002b25c   Section        0  app_timer2.o(i.timer_req_schedule)
    timer_req_schedule                       0x0002b25d   Thumb Code    46  app_timer2.o(i.timer_req_schedule)
    i.timer_start                            0x0002b294   Section        0  app_button.o(i.timer_start)
    timer_start                              0x0002b295   Thumb Code    16  app_button.o(i.timer_start)
    i.unsigned_print                         0x0002b2ac   Section        0  nrf_fprintf_format.o(i.unsigned_print)
    unsigned_print                           0x0002b2ad   Thumb Code   176  nrf_fprintf_format.o(i.unsigned_print)
    i.usr_event                              0x0002b360   Section        0  app_button.o(i.usr_event)
    usr_event                                0x0002b361   Thumb Code    32  app_button.o(i.usr_event)
    i.wakeup_button_cfg                      0x0002b380   Section        0  bsp.o(i.wakeup_button_cfg)
    wakeup_button_cfg                        0x0002b381   Thumb Code    70  bsp.o(i.wakeup_button_cfg)
    .constdata                               0x0002b3cc   Section        4  main.o(.constdata)
    m_template_uuid                          0x0002b3cc   Data           4  main.o(.constdata)
    .constdata                               0x0002b3d0   Section        8  boards.o(.constdata)
    m_board_led_list                         0x0002b3d0   Data           4  boards.o(.constdata)
    m_board_btn_list                         0x0002b3d4   Data           4  boards.o(.constdata)
    .constdata                               0x0002b3d8   Section       44  bsp.o(.constdata)
    m_bsp_leds_tmr                           0x0002b3d8   Data           4  bsp.o(.constdata)
    m_bsp_alert_tmr                          0x0002b3dc   Data           4  bsp.o(.constdata)
    m_bsp_button_tmr                         0x0002b3e0   Data           4  bsp.o(.constdata)
    app_buttons                              0x0002b3e4   Data          32  bsp.o(.constdata)
    .constdata                               0x0002b404   Section       22  nrf_ble_gatt.o(.constdata)
    .constdata                               0x0002b41c   Section        8  nrfx_gpiote.o(.constdata)
    .constdata                               0x0002b424   Section        8  app_button.o(.constdata)
    m_detection_delay_timer_id               0x0002b428   Data           4  app_button.o(.constdata)
    .constdata                               0x0002b42c   Section       20  app_timer2.o(.constdata)
    m_req_fifo                               0x0002b42c   Data           4  app_timer2.o(.constdata)
    m_app_timer_sortlist                     0x0002b434   Data          12  app_timer2.o(.constdata)
    .constdata                               0x0002b440   Section        6  drv_rtc.o(.constdata)
    delay_machine_code                       0x0002b440   Data           6  drv_rtc.o(.constdata)
    .constdata                               0x0002b446   Section       16  nrf_fprintf_format.o(.constdata)
    _aV2C                                    0x0002b446   Data          16  nrf_fprintf_format.o(.constdata)
    .constdata                               0x0002b458   Section       24  nrf_pwr_mgmt.o(.constdata)
    pwr_mgmt_data_array                      0x0002b458   Data          24  nrf_pwr_mgmt.o(.constdata)
    .constdata                               0x0002b470   Section       12  nrf_pwr_mgmt.o(.constdata)
    pwr_mgmt_data                            0x0002b470   Data          12  nrf_pwr_mgmt.o(.constdata)
    .constdata                               0x0002b47c   Section      316  nrf_strerror.o(.constdata)
    m_unknown_str                            0x0002b47c   Data          19  nrf_strerror.o(.constdata)
    nrf_strerror_array                       0x0002b490   Data         296  nrf_strerror.o(.constdata)
    .constdata                               0x0002b5b8   Section       12  nrf_log_backend_rtt.o(.constdata)
    .constdata                               0x0002b5d0   Section        6  nrf_log_backend_rtt.o(.constdata)
    delay_machine_code                       0x0002b5d0   Data           6  nrf_log_backend_rtt.o(.constdata)
    .constdata                               0x0002b5d8   Section       12  nrf_log_frontend.o(.constdata)
    m_log_push_ringbuf                       0x0002b5d8   Data          12  nrf_log_frontend.o(.constdata)
    .constdata                               0x0002b5e4   Section       16  nrf_sdh.o(.constdata)
    sdh_req_observers_array                  0x0002b5e4   Data          16  nrf_sdh.o(.constdata)
    .constdata                               0x0002b5f4   Section       40  nrf_sdh.o(.constdata)
    sdh_req_observers                        0x0002b5f8   Data          12  nrf_sdh.o(.constdata)
    sdh_state_observers                      0x0002b604   Data          12  nrf_sdh.o(.constdata)
    sdh_stack_observers                      0x0002b610   Data          12  nrf_sdh.o(.constdata)
    .constdata                               0x0002b61c   Section       16  nrf_sdh.o(.constdata)
    sdh_state_observers_array                0x0002b61c   Data          16  nrf_sdh.o(.constdata)
    .constdata                               0x0002b62c   Section       16  nrf_sdh.o(.constdata)
    sdh_stack_observers_array                0x0002b62c   Data          16  nrf_sdh.o(.constdata)
    .constdata                               0x0002b63c   Section       32  nrf_sdh_ble.o(.constdata)
    sdh_ble_observers_array                  0x0002b63c   Data          32  nrf_sdh_ble.o(.constdata)
    .constdata                               0x0002b65c   Section       16  nrf_sdh_ble.o(.constdata)
    sdh_ble_observers                        0x0002b660   Data          12  nrf_sdh_ble.o(.constdata)
    .constdata                               0x0002b66c   Section       16  nrf_sdh_soc.o(.constdata)
    sdh_soc_observers_array                  0x0002b66c   Data          16  nrf_sdh_soc.o(.constdata)
    .constdata                               0x0002b67c   Section       12  nrf_sdh_soc.o(.constdata)
    sdh_soc_observers                        0x0002b67c   Data          12  nrf_sdh_soc.o(.constdata)
    .constdata                               0x0002b688   Section      129  ctype_o.o(.constdata)
    .constdata                               0x0002b70c   Section        4  ctype_o.o(.constdata)
    table                                    0x0002b70c   Data           4  ctype_o.o(.constdata)
    .conststring                             0x0002b710   Section      162  nrf_ble_gatt.o(.conststring)
    .conststring                             0x0002b7b4   Section     1003  nrf_strerror.o(.conststring)
    .conststring                             0x0002bba0   Section      102  nrf_log_str_formatter.o(.conststring)
    .conststring                             0x0002bc08   Section      463  nrf_sdh_ble.o(.conststring)
    .conststrlit                             0x0002bdd8   Section       12  ble_db_discovery.o(.conststrlit)
    .conststrlit                             0x0002bde4   Section       13  nrf_ble_gatt.o(.conststrlit)
    .conststrlit                             0x0002bdf4   Section       11  nrf_ble_gq.o(.conststrlit)
    .conststrlit                             0x0002be00   Section        9  nrf_ble_scan.o(.conststrlit)
    .conststrlit                             0x0002be0c   Section        6  nrf_drv_clock.o(.conststrlit)
    .conststrlit                             0x0002be14   Section        6  nrfx_clock.o(.conststrlit)
    .conststrlit                             0x0002be1c   Section        7  nrfx_gpiote.o(.conststrlit)
    .conststrlit                             0x0002be24   Section        4  nrfx_prs.o(.conststrlit)
    .conststrlit                             0x0002be28   Section        5  nrfx_uart.o(.conststrlit)
    .conststrlit                             0x0002be30   Section        6  nrfx_uarte.o(.conststrlit)
    .conststrlit                             0x0002be38   Section       11  app_button.o(.conststrlit)
    .conststrlit                             0x0002be44   Section       10  app_timer2.o(.conststrlit)
    .conststrlit                             0x0002be50   Section        9  nrf_pwr_mgmt.o(.conststrlit)
    .conststrlit                             0x0002be5c   Section        9  nrf_sortlist.o(.conststrlit)
    .conststrlit                             0x0002be68   Section       16  nrf_log_default_backends.o(.conststrlit)
    .conststrlit                             0x0002be78   Section        4  nrf_log_frontend.o(.conststrlit)
    .conststrlit                             0x0002be7c   Section        8  nrf_sdh.o(.conststrlit)
    .conststrlit                             0x0002be84   Section       12  nrf_sdh_ble.o(.conststrlit)
    .conststrlit                             0x0002be90   Section       12  nrf_sdh_soc.o(.conststrlit)
    log_backends                             0x0002bebc   Section       16  nrf_log_default_backends.o(log_backends)
    __tagsym$$used                           0x0002bebc   Number         0  nrf_log_default_backends.o(log_backends)
    rtt_log_backend                          0x0002bebc   Data          16  nrf_log_default_backends.o(log_backends)
    log_const_data                           0x0002becc   Section        8  ble_db_discovery.o(log_const_data)
    __tagsym$$used                           0x0002becc   Number         0  ble_db_discovery.o(log_const_data)
    log_const_data                           0x0002bed4   Section        8  nrf_ble_gatt.o(log_const_data)
    __tagsym$$used                           0x0002bed4   Number         0  nrf_ble_gatt.o(log_const_data)
    log_const_data                           0x0002bedc   Section        8  nrf_ble_gq.o(log_const_data)
    __tagsym$$used                           0x0002bedc   Number         0  nrf_ble_gq.o(log_const_data)
    log_const_data                           0x0002bee4   Section        8  nrf_ble_scan.o(log_const_data)
    __tagsym$$used                           0x0002bee4   Number         0  nrf_ble_scan.o(log_const_data)
    log_const_data                           0x0002beec   Section        8  nrf_drv_clock.o(log_const_data)
    __tagsym$$used                           0x0002beec   Number         0  nrf_drv_clock.o(log_const_data)
    log_const_data                           0x0002bef4   Section        8  nrfx_clock.o(log_const_data)
    __tagsym$$used                           0x0002bef4   Number         0  nrfx_clock.o(log_const_data)
    log_const_data                           0x0002befc   Section        8  nrfx_gpiote.o(log_const_data)
    __tagsym$$used                           0x0002befc   Number         0  nrfx_gpiote.o(log_const_data)
    log_const_data                           0x0002bf04   Section        8  nrfx_prs.o(log_const_data)
    __tagsym$$used                           0x0002bf04   Number         0  nrfx_prs.o(log_const_data)
    log_const_data                           0x0002bf0c   Section        8  nrfx_uart.o(log_const_data)
    __tagsym$$used                           0x0002bf0c   Number         0  nrfx_uart.o(log_const_data)
    log_const_data                           0x0002bf14   Section        8  nrfx_uarte.o(log_const_data)
    __tagsym$$used                           0x0002bf14   Number         0  nrfx_uarte.o(log_const_data)
    log_const_data                           0x0002bf1c   Section        8  app_button.o(log_const_data)
    __tagsym$$used                           0x0002bf1c   Number         0  app_button.o(log_const_data)
    log_const_data                           0x0002bf24   Section        8  app_timer2.o(log_const_data)
    __tagsym$$used                           0x0002bf24   Number         0  app_timer2.o(log_const_data)
    log_const_data                           0x0002bf2c   Section        8  nrf_pwr_mgmt.o(log_const_data)
    __tagsym$$used                           0x0002bf2c   Number         0  nrf_pwr_mgmt.o(log_const_data)
    log_const_data                           0x0002bf34   Section        8  nrf_sortlist.o(log_const_data)
    __tagsym$$used                           0x0002bf34   Number         0  nrf_sortlist.o(log_const_data)
    log_const_data                           0x0002bf3c   Section        8  nrf_log_frontend.o(log_const_data)
    __tagsym$$used                           0x0002bf3c   Number         0  nrf_log_frontend.o(log_const_data)
    log_const_data                           0x0002bf44   Section        8  nrf_sdh.o(log_const_data)
    __tagsym$$used                           0x0002bf44   Number         0  nrf_sdh.o(log_const_data)
    log_const_data                           0x0002bf4c   Section        8  nrf_sdh_ble.o(log_const_data)
    __tagsym$$used                           0x0002bf4c   Number         0  nrf_sdh_ble.o(log_const_data)
    log_const_data                           0x0002bf54   Section        8  nrf_sdh_soc.o(log_const_data)
    __tagsym$$used                           0x0002bf54   Number         0  nrf_sdh_soc.o(log_const_data)
    nrf_balloc                               0x0002bf5c   Section       20  nrf_log_frontend.o(nrf_balloc)
    __tagsym$$used                           0x0002bf5c   Number         0  nrf_log_frontend.o(nrf_balloc)
    pwr_mgmt_data1                           0x0002bf70   Section        4  main.o(pwr_mgmt_data1)
    __tagsym$$used                           0x0002bf70   Number         0  main.o(pwr_mgmt_data1)
    shutdown_handler_handler_function        0x0002bf70   Data           4  main.o(pwr_mgmt_data1)
    sdh_ble_observers1                       0x0002bf74   Section       16  main.o(sdh_ble_observers1)
    __tagsym$$used                           0x0002bf74   Number         0  main.o(sdh_ble_observers1)
    m_gatt_obs                               0x0002bf74   Data           8  main.o(sdh_ble_observers1)
    __tagsym$$used                           0x0002bf7c   Number         0  main.o(sdh_ble_observers1)
    m_scan_ble_obs                           0x0002bf7c   Data           8  main.o(sdh_ble_observers1)
    sdh_ble_observers1                       0x0002bf84   Section        8  bsp_btn_ble.o(sdh_ble_observers1)
    __tagsym$$used                           0x0002bf84   Number         0  bsp_btn_ble.o(sdh_ble_observers1)
    m_ble_observer                           0x0002bf84   Data           8  bsp_btn_ble.o(sdh_ble_observers1)
    sdh_ble_observers3                       0x0002bf8c   Section        8  main.o(sdh_ble_observers3)
    __tagsym$$used                           0x0002bf8c   Number         0  main.o(sdh_ble_observers3)
    m_ble_observer                           0x0002bf8c   Data           8  main.o(sdh_ble_observers3)
    sdh_soc_observers0                       0x0002bf94   Section        8  nrf_drv_clock.o(sdh_soc_observers0)
    __tagsym$$used                           0x0002bf94   Number         0  nrf_drv_clock.o(sdh_soc_observers0)
    m_soc_evt_observer                       0x0002bf94   Data           8  nrf_drv_clock.o(sdh_soc_observers0)
    sdh_stack_observers0                     0x0002bf9c   Section        8  nrf_sdh_ble.o(sdh_stack_observers0)
    __tagsym$$used                           0x0002bf9c   Number         0  nrf_sdh_ble.o(sdh_stack_observers0)
    m_nrf_sdh_ble_evts_poll                  0x0002bf9c   Data           8  nrf_sdh_ble.o(sdh_stack_observers0)
    sdh_stack_observers0                     0x0002bfa4   Section        8  nrf_sdh_soc.o(sdh_stack_observers0)
    __tagsym$$used                           0x0002bfa4   Number         0  nrf_sdh_soc.o(sdh_stack_observers0)
    m_nrf_sdh_soc_evts_poll                  0x0002bfa4   Data           8  nrf_sdh_soc.o(sdh_stack_observers0)
    sdh_state_observers0                     0x0002bfac   Section        8  nrf_drv_clock.o(sdh_state_observers0)
    __tagsym$$used                           0x0002bfac   Number         0  nrf_drv_clock.o(sdh_state_observers0)
    m_sd_state_observer                      0x0002bfac   Data           8  nrf_drv_clock.o(sdh_state_observers0)
    .data                                    0x20002a38   Section        2  main.o(.data)
    m_ble_template_max_data_len              0x20002a38   Data           2  main.o(.data)
    .data                                    0x20002a3c   Section       16  bsp.o(.data)
    m_stable_state                           0x20002a3c   Data           1  bsp.o(.data)
    m_leds_clear                             0x20002a3d   Data           1  bsp.o(.data)
    m_alert_on                               0x20002a3e   Data           1  bsp.o(.data)
    current_long_push_pin_no                 0x20002a3f   Data           1  bsp.o(.data)
    m_indication_type                        0x20002a40   Data           4  bsp.o(.data)
    m_registered_callback                    0x20002a44   Data           4  bsp.o(.data)
    release_event_at_push                    0x20002a48   Data           4  bsp.o(.data)
    .data                                    0x20002a4c   Section        8  bsp_btn_ble.o(.data)
    m_error_handler                          0x20002a4c   Data           4  bsp_btn_ble.o(.data)
    m_num_connections                        0x20002a50   Data           4  bsp_btn_ble.o(.data)
    .data                                    0x20002a54   Section        8  nrfx_clock.o(.data)
    m_clock_cb                               0x20002a54   Data           8  nrfx_clock.o(.data)
    .data                                    0x20002a5c   Section        8  nrfx_prs.o(.data)
    m_prs_box_4                              0x20002a5c   Data           8  nrfx_prs.o(.data)
    .data                                    0x20002a68   Section       24  app_button.o(.data)
    m_button_count                           0x20002a68   Data           1  app_button.o(.data)
    mp_buttons                               0x20002a6c   Data           4  app_button.o(.data)
    m_detection_delay                        0x20002a70   Data           4  app_button.o(.data)
    m_pin_active                             0x20002a78   Data           8  app_button.o(.data)
    .data                                    0x20002a80   Section       32  app_timer2.o(.data)
    m_global_active                          0x20002a80   Data           1  app_timer2.o(.data)
    mp_active_timer                          0x20002a84   Data           4  app_timer2.o(.data)
    m_rtc_inst                               0x20002a88   Data           8  app_timer2.o(.data)
    m_base_counter                           0x20002a90   Data           8  app_timer2.o(.data)
    m_stamp64                                0x20002a98   Data           8  app_timer2.o(.data)
    .data                                    0x20002aa0   Section        4  app_timer2.o(.data)
    m_app_timer_sortlist_sortlist_cb         0x20002aa0   Data           4  app_timer2.o(.data)
    .data                                    0x20002aa4   Section       12  drv_rtc.o(.data)
    m_handlers                               0x20002aa4   Data           4  drv_rtc.o(.data)
    m_cb                                     0x20002aa8   Data           8  drv_rtc.o(.data)
    .data                                    0x20002ab0   Section        8  nrf_pwr_mgmt.o(.data)
    m_pwr_mgmt_evt                           0x20002ab0   Data           1  nrf_pwr_mgmt.o(.data)
    m_shutdown_started                       0x20002ab1   Data           1  nrf_pwr_mgmt.o(.data)
    m_sysoff_mtx                             0x20002ab4   Data           4  nrf_pwr_mgmt.o(.data)
    .data                                    0x20002ab8   Section        1  nrf_log_backend_rtt.o(.data)
    m_host_present                           0x20002ab8   Data           1  nrf_log_backend_rtt.o(.data)
    .data                                    0x20002abc   Section        8  nrf_log_default_backends.o(.data)
    log_backend_cb_rtt_log_backend           0x20002abc   Data           8  nrf_log_default_backends.o(.data)
    .data                                    0x20002ac4   Section        4  nrf_log_frontend.o(.data)
    m_buffer_mask                            0x20002ac4   Data           4  nrf_log_frontend.o(.data)
    .data                                    0x20002ac8   Section        8  nrf_log_frontend.o(.data)
    log_mempool_nrf_balloc_pool_stack        0x20002ac8   Data           8  nrf_log_frontend.o(.data)
    .data                                    0x20002ad0   Section        8  nrf_log_frontend.o(.data)
    log_mempool_nrf_balloc_cb                0x20002ad0   Data           8  nrf_log_frontend.o(.data)
    .data                                    0x20002ad8   Section       64  nrf_log_str_formatter.o(.data)
    m_freq                                   0x20002ad8   Data           4  nrf_log_str_formatter.o(.data)
    m_timestamp_div                          0x20002adc   Data           4  nrf_log_str_formatter.o(.data)
    severity_names                           0x20002ae0   Data          20  nrf_log_str_formatter.o(.data)
    m_colors                                 0x20002af4   Data          36  nrf_log_str_formatter.o(.data)
    .data                                    0x20002b18   Section        3  nrf_sdh.o(.data)
    m_nrf_sdh_enabled                        0x20002b18   Data           1  nrf_sdh.o(.data)
    m_nrf_sdh_suspended                      0x20002b19   Data           1  nrf_sdh.o(.data)
    m_nrf_sdh_continue                       0x20002b1a   Data           1  nrf_sdh.o(.data)
    .data                                    0x20002b1b   Section        1  nrf_sdh_ble.o(.data)
    m_stack_is_enabled                       0x20002b1b   Data           1  nrf_sdh_ble.o(.data)
    .data                                    0x20002b1c   Section        4  system_nrf52840.o(.data)
    __tagsym$$used                           0x20002b1c   Number         0  system_nrf52840.o(.data)
    .bss                                     0x20002b20   Section      100  main.o(.bss)
    m_gatt                                   0x20002b20   Data          20  main.o(.bss)
    m_scan                                   0x20002b34   Data          80  main.o(.bss)
    .bss                                     0x20002b88   Section       32  bsp.o(.bss)
    m_bsp_leds_tmr_data                      0x20002b88   Data          32  bsp.o(.bss)
    .bss                                     0x20002ba8   Section       32  bsp.o(.bss)
    m_bsp_alert_tmr_data                     0x20002ba8   Data          32  bsp.o(.bss)
    .bss                                     0x20002bc8   Section       12  bsp.o(.bss)
    m_events_list                            0x20002bc8   Data          12  bsp.o(.bss)
    .bss                                     0x20002bd8   Section       32  bsp.o(.bss)
    m_bsp_button_tmr_data                    0x20002bd8   Data          32  bsp.o(.bss)
    .bss                                     0x20002bf8   Section       20  nrf_drv_clock.o(.bss)
    m_clock_cb                               0x20002bf8   Data          20  nrf_drv_clock.o(.bss)
    .bss                                     0x20002c0c   Section      108  nrfx_gpiote.o(.bss)
    m_cb                                     0x20002c0c   Data         108  nrfx_gpiote.o(.bss)
    .bss                                     0x20002c78   Section       32  app_button.o(.bss)
    m_detection_delay_timer_id_data          0x20002c78   Data          32  app_button.o(.bss)
    .bss                                     0x20002c98   Section       32  app_button.o(.bss)
    m_pin_states                             0x20002c98   Data          32  app_button.o(.bss)
    .bss                                     0x20002cb8   Section       16  app_timer2.o(.bss)
    m_req_fifo_inst                          0x20002cb8   Data          16  app_timer2.o(.bss)
    .bss                                     0x20002cc8   Section       88  app_timer2.o(.bss)
    m_req_fifo_data                          0x20002cc8   Data          88  app_timer2.o(.bss)
    .bss                                     0x20002d20   Section       12  app_util_platform.o(.bss)
    .bss                                     0x20002d2c   Section       12  nrf_pwr_mgmt.o(.bss)
    m_handlers_iter                          0x20002d2c   Data          12  nrf_pwr_mgmt.o(.bss)
    .bss                                     0x20002d38   Section       64  nrf_log_backend_rtt.o(.bss)
    m_string_buff                            0x20002d38   Data          64  nrf_log_backend_rtt.o(.bss)
    .bss                                     0x20002d78   Section      192  nrf_log_frontend.o(.bss)
    log_mempool_nrf_balloc_pool_mem          0x20002d78   Data         192  nrf_log_frontend.o(.bss)
    .bss                                     0x20002e38   Section      128  nrf_log_frontend.o(.bss)
    m_log_push_ringbuf_buf                   0x20002e38   Data         128  nrf_log_frontend.o(.bss)
    .bss                                     0x20002eb8   Section       24  nrf_log_frontend.o(.bss)
    m_log_push_ringbuf_cb                    0x20002eb8   Data          24  nrf_log_frontend.o(.bss)
    .bss                                     0x20002ed0   Section     1056  nrf_log_frontend.o(.bss)
    m_log_data                               0x20002ed0   Data        1056  nrf_log_frontend.o(.bss)
    .bss                                     0x200032f0   Section      648  segger_rtt.o(.bss)
    _acUpBuffer                              0x20003368   Data         512  segger_rtt.o(.bss)
    _acDownBuffer                            0x20003568   Data          16  segger_rtt.o(.bss)
    STACK                                    0x20003578   Section     8192  arm_startup_nrf52840.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$E$P$D$K$B$S$7EM$VFPi3$EXTD16$VFPS$VFMA$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OSPACE$EBA8$MICROLIB$REQ8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __decompress                              - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _microlib_exit                            - Undefined Weak Reference
    log_dynamic_data$$Base                    - Undefined Reference
    pwr_mgmt_data0$$Base                      - Undefined Reference
    pwr_mgmt_data0$$Limit                     - Undefined Reference
    pwr_mgmt_data2$$Base                      - Undefined Reference
    pwr_mgmt_data2$$Limit                     - Undefined Reference
    sdh_ble_observers0$$Base                  - Undefined Reference
    sdh_ble_observers0$$Limit                 - Undefined Reference
    sdh_ble_observers2$$Base                  - Undefined Reference
    sdh_ble_observers2$$Limit                 - Undefined Reference
    sdh_req_observers0$$Base                  - Undefined Reference
    sdh_req_observers0$$Limit                 - Undefined Reference
    sdh_req_observers1$$Base                  - Undefined Reference
    sdh_req_observers1$$Limit                 - Undefined Reference
    sdh_soc_observers1$$Base                  - Undefined Reference
    sdh_soc_observers1$$Limit                 - Undefined Reference
    sdh_stack_observers1$$Base                - Undefined Reference
    sdh_stack_observers1$$Limit               - Undefined Reference
    sdh_state_observers1$$Base                - Undefined Reference
    sdh_state_observers1$$Limit               - Undefined Reference
    __Vectors_Size                           0x00000200   Number         0  arm_startup_nrf52840.o ABSOLUTE
    __Vectors                                0x00027000   Data           4  arm_startup_nrf52840.o(RESET)
    __Vectors_End                            0x00027200   Data           0  arm_startup_nrf52840.o(RESET)
    __main                                   0x00027201   Thumb Code     0  entry.o(.ARM.Collect$$$$00000000)
    _main_stk                                0x00027201   Thumb Code     0  entry2.o(.ARM.Collect$$$$00000001)
    _main_scatterload                        0x00027205   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    __main_after_scatterload                 0x00027209   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    _main_clock                              0x00027209   Thumb Code     0  entry7b.o(.ARM.Collect$$$$00000008)
    _main_cpp_init                           0x00027209   Thumb Code     0  entry8b.o(.ARM.Collect$$$$0000000A)
    _main_init                               0x00027209   Thumb Code     0  entry9a.o(.ARM.Collect$$$$0000000B)
    __rt_final_cpp                           0x00027211   Thumb Code     0  entry10a.o(.ARM.Collect$$$$0000000D)
    __rt_final_exit                          0x00027211   Thumb Code     0  entry11a.o(.ARM.Collect$$$$0000000F)
    __asm___12_nrf_atfifo_c_51f461e1__nrf_atfifo_wspace_req 0x00027215   Thumb Code    56  nrf_atfifo.o(.emb_text)
    __asm___12_nrf_atfifo_c_51f461e1__nrf_atfifo_wspace_close 0x0002724d   Thumb Code    18  nrf_atfifo.o(.emb_text)
    __asm___12_nrf_atfifo_c_51f461e1__nrf_atfifo_rspace_req 0x0002725f   Thumb Code    58  nrf_atfifo.o(.emb_text)
    __asm___12_nrf_atfifo_c_51f461e1__nrf_atfifo_rspace_close 0x00027299   Thumb Code    18  nrf_atfifo.o(.emb_text)
    __asm___12_nrf_atfifo_c_51f461e1__nrf_atfifo_space_clear 0x000272ab   Thumb Code    50  nrf_atfifo.o(.emb_text)
    __asm___12_nrf_atomic_c_85ca2469__nrf_atomic_internal_mov 0x000272dd   Thumb Code    24  nrf_atomic.o(.emb_text)
    __asm___12_nrf_atomic_c_85ca2469__nrf_atomic_internal_orr 0x000272f5   Thumb Code    26  nrf_atomic.o(.emb_text)
    __asm___12_nrf_atomic_c_85ca2469__nrf_atomic_internal_and 0x0002730f   Thumb Code    26  nrf_atomic.o(.emb_text)
    __asm___12_nrf_atomic_c_85ca2469__nrf_atomic_internal_eor 0x00027329   Thumb Code    26  nrf_atomic.o(.emb_text)
    __asm___12_nrf_atomic_c_85ca2469__nrf_atomic_internal_add 0x00027343   Thumb Code    26  nrf_atomic.o(.emb_text)
    __asm___12_nrf_atomic_c_85ca2469__nrf_atomic_internal_sub 0x0002735d   Thumb Code    26  nrf_atomic.o(.emb_text)
    __asm___12_nrf_atomic_c_85ca2469__nrf_atomic_internal_cmp_exch 0x00027377   Thumb Code    42  nrf_atomic.o(.emb_text)
    __asm___12_nrf_atomic_c_85ca2469__nrf_atomic_internal_sub_hs 0x000273a1   Thumb Code    30  nrf_atomic.o(.emb_text)
    Reset_Handler                            0x000273c1   Thumb Code     8  arm_startup_nrf52840.o(.text)
    NMI_Handler                              0x000273c9   Thumb Code     2  arm_startup_nrf52840.o(.text)
    HardFault_Handler                        0x000273cb   Thumb Code     2  arm_startup_nrf52840.o(.text)
    MemoryManagement_Handler                 0x000273cd   Thumb Code     2  arm_startup_nrf52840.o(.text)
    BusFault_Handler                         0x000273cf   Thumb Code     2  arm_startup_nrf52840.o(.text)
    UsageFault_Handler                       0x000273d1   Thumb Code     2  arm_startup_nrf52840.o(.text)
    SVC_Handler                              0x000273d3   Thumb Code     2  arm_startup_nrf52840.o(.text)
    DebugMon_Handler                         0x000273d5   Thumb Code     2  arm_startup_nrf52840.o(.text)
    PendSV_Handler                           0x000273d7   Thumb Code     2  arm_startup_nrf52840.o(.text)
    SysTick_Handler                          0x000273d9   Thumb Code     2  arm_startup_nrf52840.o(.text)
    CCM_AAR_IRQHandler                       0x000273db   Thumb Code     0  arm_startup_nrf52840.o(.text)
    COMP_LPCOMP_IRQHandler                   0x000273db   Thumb Code     0  arm_startup_nrf52840.o(.text)
    CRYPTOCELL_IRQHandler                    0x000273db   Thumb Code     0  arm_startup_nrf52840.o(.text)
    ECB_IRQHandler                           0x000273db   Thumb Code     0  arm_startup_nrf52840.o(.text)
    FPU_IRQHandler                           0x000273db   Thumb Code     0  arm_startup_nrf52840.o(.text)
    I2S_IRQHandler                           0x000273db   Thumb Code     0  arm_startup_nrf52840.o(.text)
    MWU_IRQHandler                           0x000273db   Thumb Code     0  arm_startup_nrf52840.o(.text)
    NFCT_IRQHandler                          0x000273db   Thumb Code     0  arm_startup_nrf52840.o(.text)
    PDM_IRQHandler                           0x000273db   Thumb Code     0  arm_startup_nrf52840.o(.text)
    PWM0_IRQHandler                          0x000273db   Thumb Code     0  arm_startup_nrf52840.o(.text)
    PWM1_IRQHandler                          0x000273db   Thumb Code     0  arm_startup_nrf52840.o(.text)
    PWM2_IRQHandler                          0x000273db   Thumb Code     0  arm_startup_nrf52840.o(.text)
    PWM3_IRQHandler                          0x000273db   Thumb Code     0  arm_startup_nrf52840.o(.text)
    QDEC_IRQHandler                          0x000273db   Thumb Code     0  arm_startup_nrf52840.o(.text)
    QSPI_IRQHandler                          0x000273db   Thumb Code     0  arm_startup_nrf52840.o(.text)
    RADIO_IRQHandler                         0x000273db   Thumb Code     0  arm_startup_nrf52840.o(.text)
    RNG_IRQHandler                           0x000273db   Thumb Code     0  arm_startup_nrf52840.o(.text)
    RTC0_IRQHandler                          0x000273db   Thumb Code     0  arm_startup_nrf52840.o(.text)
    RTC2_IRQHandler                          0x000273db   Thumb Code     0  arm_startup_nrf52840.o(.text)
    SAADC_IRQHandler                         0x000273db   Thumb Code     0  arm_startup_nrf52840.o(.text)
    SPIM0_SPIS0_TWIM0_TWIS0_SPI0_TWI0_IRQHandler 0x000273db   Thumb Code     0  arm_startup_nrf52840.o(.text)
    SPIM1_SPIS1_TWIM1_TWIS1_SPI1_TWI1_IRQHandler 0x000273db   Thumb Code     0  arm_startup_nrf52840.o(.text)
    SPIM2_SPIS2_SPI2_IRQHandler              0x000273db   Thumb Code     0  arm_startup_nrf52840.o(.text)
    SPIM3_IRQHandler                         0x000273db   Thumb Code     0  arm_startup_nrf52840.o(.text)
    SWI0_EGU0_IRQHandler                     0x000273db   Thumb Code     0  arm_startup_nrf52840.o(.text)
    SWI1_EGU1_IRQHandler                     0x000273db   Thumb Code     0  arm_startup_nrf52840.o(.text)
    SWI3_EGU3_IRQHandler                     0x000273db   Thumb Code     0  arm_startup_nrf52840.o(.text)
    SWI4_EGU4_IRQHandler                     0x000273db   Thumb Code     0  arm_startup_nrf52840.o(.text)
    SWI5_EGU5_IRQHandler                     0x000273db   Thumb Code     0  arm_startup_nrf52840.o(.text)
    TEMP_IRQHandler                          0x000273db   Thumb Code     0  arm_startup_nrf52840.o(.text)
    TIMER0_IRQHandler                        0x000273db   Thumb Code     0  arm_startup_nrf52840.o(.text)
    TIMER1_IRQHandler                        0x000273db   Thumb Code     0  arm_startup_nrf52840.o(.text)
    TIMER2_IRQHandler                        0x000273db   Thumb Code     0  arm_startup_nrf52840.o(.text)
    TIMER3_IRQHandler                        0x000273db   Thumb Code     0  arm_startup_nrf52840.o(.text)
    TIMER4_IRQHandler                        0x000273db   Thumb Code     0  arm_startup_nrf52840.o(.text)
    UARTE1_IRQHandler                        0x000273db   Thumb Code     0  arm_startup_nrf52840.o(.text)
    USBD_IRQHandler                          0x000273db   Thumb Code     0  arm_startup_nrf52840.o(.text)
    WDT_IRQHandler                           0x000273db   Thumb Code     0  arm_startup_nrf52840.o(.text)
    __aeabi_uldivmod                         0x000273e5   Thumb Code    98  uldiv.o(.text)
    __aeabi_llsl                             0x00027447   Thumb Code    30  llshl.o(.text)
    _ll_shift_l                              0x00027447   Thumb Code     0  llshl.o(.text)
    __rt_ctype_table                         0x00027465   Thumb Code     4  ctype_o.o(.text)
    __aeabi_memcpy                           0x0002746d   Thumb Code    36  memcpya.o(.text)
    __aeabi_memcpy4                          0x0002746d   Thumb Code     0  memcpya.o(.text)
    __aeabi_memcpy8                          0x0002746d   Thumb Code     0  memcpya.o(.text)
    __aeabi_memset                           0x00027491   Thumb Code    14  memseta.o(.text)
    __aeabi_memset4                          0x00027491   Thumb Code     0  memseta.o(.text)
    __aeabi_memset8                          0x00027491   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr                           0x0002749f   Thumb Code     4  memseta.o(.text)
    __aeabi_memclr4                          0x0002749f   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr8                          0x0002749f   Thumb Code     0  memseta.o(.text)
    _memset$wrapper                          0x000274a3   Thumb Code    18  memseta.o(.text)
    strlen                                   0x000274b5   Thumb Code    14  strlen.o(.text)
    memcmp                                   0x000274c3   Thumb Code    26  memcmp.o(.text)
    strcpy                                   0x000274dd   Thumb Code    18  strcpy.o(.text)
    __aeabi_llsr                             0x000274ef   Thumb Code    32  llushr.o(.text)
    _ll_ushift_r                             0x000274ef   Thumb Code     0  llushr.o(.text)
    __scatterload                            0x00027511   Thumb Code    28  init.o(.text)
    __scatterload_rt2                        0x00027511   Thumb Code     0  init.o(.text)
    GPIOTE_IRQHandler                        0x00027535   Thumb Code   156  nrfx_gpiote.o(i.GPIOTE_IRQHandler)
    POWER_CLOCK_IRQHandler                   0x000275d9   Thumb Code    82  nrfx_clock.o(i.POWER_CLOCK_IRQHandler)
    RTC1_IRQHandler                          0x00027631   Thumb Code    12  drv_rtc.o(i.RTC1_IRQHandler)
    SEGGER_RTT_Init                          0x00027641   Thumb Code     4  segger_rtt.o(i.SEGGER_RTT_Init)
    SEGGER_RTT_WriteNoLock                   0x00027645   Thumb Code    86  segger_rtt.o(i.SEGGER_RTT_WriteNoLock)
    SWI2_EGU2_IRQHandler                     0x000276a1   Thumb Code     4  nrf_sdh.o(i.SWI2_EGU2_IRQHandler)
    SystemInit                               0x000276a5   Thumb Code   516  system_nrf52840.o(i.SystemInit)
    UARTE0_UART0_IRQHandler                  0x000278ed   Thumb Code     6  nrfx_prs.o(i.UARTE0_UART0_IRQHandler)
    __scatterload_copy                       0x00027a13   Thumb Code    14  handlers.o(i.__scatterload_copy)
    __scatterload_null                       0x00027a21   Thumb Code     2  handlers.o(i.__scatterload_null)
    __scatterload_zeroinit                   0x00027a23   Thumb Code    14  handlers.o(i.__scatterload_zeroinit)
    app_button_enable                        0x00027a91   Thumb Code    32  app_button.o(i.app_button_enable)
    app_button_init                          0x00027ab5   Thumb Code   106  app_button.o(i.app_button_init)
    app_error_fault_handler                  0x00027b35   Thumb Code    86  app_error_weak.o(i.app_error_fault_handler)
    app_error_handler_bare                   0x00027bb9   Thumb Code    22  app_error.o(i.app_error_handler_bare)
    app_timer_cnt_get                        0x00027bd1   Thumb Code     6  app_timer2.o(i.app_timer_cnt_get)
    app_timer_create                         0x00027bdd   Thumb Code    22  app_timer2.o(i.app_timer_create)
    app_timer_init                           0x00027bf5   Thumb Code    70  app_timer2.o(i.app_timer_init)
    app_timer_start                          0x00027c51   Thumb Code    48  app_timer2.o(i.app_timer_start)
    app_timer_stop                           0x00027c81   Thumb Code    12  app_timer2.o(i.app_timer_stop)
    app_util_critical_region_enter           0x00027c8d   Thumb Code    64  app_util_platform.o(i.app_util_critical_region_enter)
    app_util_critical_region_exit            0x00027cd5   Thumb Code    46  app_util_platform.o(i.app_util_critical_region_exit)
    ble_advdata_search                       0x00027d09   Thumb Code    70  ble_advdata.o(i.ble_advdata_search)
    ble_advdata_uuid_find                    0x00027d4f   Thumb Code   182  ble_advdata.o(i.ble_advdata_uuid_find)
    bsp_board_button_idx_to_pin              0x00027f7d   Thumb Code     6  boards.o(i.bsp_board_button_idx_to_pin)
    bsp_board_button_state_get               0x00027f89   Thumb Code    36  boards.o(i.bsp_board_button_state_get)
    bsp_board_init                           0x00027fb1   Thumb Code    80  boards.o(i.bsp_board_init)
    bsp_board_led_invert                     0x00028005   Thumb Code    40  boards.o(i.bsp_board_led_invert)
    bsp_board_led_off                        0x00028031   Thumb Code    10  boards.o(i.bsp_board_led_off)
    bsp_board_led_on                         0x00028041   Thumb Code    10  boards.o(i.bsp_board_led_on)
    bsp_board_led_state_get                  0x00028051   Thumb Code    36  boards.o(i.bsp_board_led_state_get)
    bsp_board_leds_off                       0x00028079   Thumb Code    18  boards.o(i.bsp_board_leds_off)
    bsp_board_leds_on                        0x0002808b   Thumb Code    18  boards.o(i.bsp_board_leds_on)
    bsp_board_pin_to_button_idx              0x0002809d   Thumb Code    30  boards.o(i.bsp_board_pin_to_button_idx)
    bsp_btn_ble_init                         0x000280c1   Thumb Code    52  bsp_btn_ble.o(i.bsp_btn_ble_init)
    bsp_btn_ble_sleep_mode_prepare           0x000280f9   Thumb Code    30  bsp_btn_ble.o(i.bsp_btn_ble_sleep_mode_prepare)
    bsp_button_is_pressed                    0x000281a9   Thumb Code    12  bsp.o(i.bsp_button_is_pressed)
    bsp_event_handler                        0x000281b5   Thumb Code    28  main.o(i.bsp_event_handler)
    bsp_event_to_button_action_assign        0x000281d1   Thumb Code    64  bsp.o(i.bsp_event_to_button_action_assign)
    bsp_indication_set                       0x00028215   Thumb Code    20  bsp.o(i.bsp_indication_set)
    bsp_init                                 0x0002822d   Thumb Code   128  bsp.o(i.bsp_init)
    bsp_wakeup_button_enable                 0x00028489   Thumb Code     6  bsp.o(i.bsp_wakeup_button_enable)
    buffer_is_empty                          0x00028555   Thumb Code    18  nrf_log_frontend.o(i.buffer_is_empty)
    drv_rtc_compare_disable                  0x00028789   Thumb Code    20  drv_rtc.o(i.drv_rtc_compare_disable)
    drv_rtc_compare_pending                  0x0002879d   Thumb Code    14  drv_rtc.o(i.drv_rtc_compare_pending)
    drv_rtc_compare_set                      0x000287ab   Thumb Code    80  drv_rtc.o(i.drv_rtc_compare_set)
    drv_rtc_counter_get                      0x000287fb   Thumb Code     8  drv_rtc.o(i.drv_rtc_counter_get)
    drv_rtc_init                             0x00028805   Thumb Code   148  drv_rtc.o(i.drv_rtc_init)
    drv_rtc_irq_trigger                      0x000288dd   Thumb Code    30  drv_rtc.o(i.drv_rtc_irq_trigger)
    drv_rtc_overflow_enable                  0x000288fb   Thumb Code     8  drv_rtc.o(i.drv_rtc_overflow_enable)
    drv_rtc_overflow_pending                 0x00028903   Thumb Code     8  drv_rtc.o(i.drv_rtc_overflow_pending)
    drv_rtc_start                            0x0002890b   Thumb Code     8  drv_rtc.o(i.drv_rtc_start)
    drv_rtc_stop                             0x00028913   Thumb Code     8  drv_rtc.o(i.drv_rtc_stop)
    drv_rtc_windowed_compare_set             0x0002891b   Thumb Code   222  drv_rtc.o(i.drv_rtc_windowed_compare_set)
    evt_handle                               0x00028a0d   Thumb Code   216  app_button.o(i.evt_handle)
    gatt_evt_handler                         0x00028b01   Thumb Code    62  main.o(i.gatt_evt_handler)
    gatt_init                                0x00028b99   Thumb Code    24  main.o(i.gatt_init)
    is_whitelist_used                        0x00028d03   Thumb Code    22  nrf_ble_scan.o(i.is_whitelist_used)
    main                                     0x00028e19   Thumb Code   158  main.o(i.main)
    nrf_atfifo_init                          0x00028f9d   Thumb Code    38  nrf_atfifo.o(i.nrf_atfifo_init)
    nrf_atfifo_item_alloc                    0x00028fc3   Thumb Code    22  nrf_atfifo.o(i.nrf_atfifo_item_alloc)
    nrf_atfifo_item_free                     0x00028fd9   Thumb Code    22  nrf_atfifo.o(i.nrf_atfifo_item_free)
    nrf_atfifo_item_get                      0x00028fef   Thumb Code    22  nrf_atfifo.o(i.nrf_atfifo_item_get)
    nrf_atfifo_item_put                      0x00029005   Thumb Code    22  nrf_atfifo.o(i.nrf_atfifo_item_put)
    nrf_atomic_flag_clear_fetch              0x0002901b   Thumb Code     6  nrf_atomic.o(i.nrf_atomic_flag_clear_fetch)
    nrf_atomic_flag_set                      0x00029021   Thumb Code     6  nrf_atomic.o(i.nrf_atomic_flag_set)
    nrf_atomic_u32_add                       0x00029027   Thumb Code    12  nrf_atomic.o(i.nrf_atomic_u32_add)
    nrf_atomic_u32_fetch_and                 0x00029033   Thumb Code    10  nrf_atomic.o(i.nrf_atomic_u32_fetch_and)
    nrf_atomic_u32_fetch_store               0x0002903d   Thumb Code    10  nrf_atomic.o(i.nrf_atomic_u32_fetch_store)
    nrf_atomic_u32_or                        0x00029047   Thumb Code    12  nrf_atomic.o(i.nrf_atomic_u32_or)
    nrf_atomic_u32_sub                       0x00029053   Thumb Code    12  nrf_atomic.o(i.nrf_atomic_u32_sub)
    nrf_balloc_alloc                         0x0002905f   Thumb Code    68  nrf_balloc.o(i.nrf_balloc_alloc)
    nrf_balloc_free                          0x000290a3   Thumb Code    48  nrf_balloc.o(i.nrf_balloc_free)
    nrf_balloc_init                          0x000290d3   Thumb Code    48  nrf_balloc.o(i.nrf_balloc_init)
    nrf_ble_gatt_init                        0x00029113   Thumb Code    34  nrf_ble_gatt.o(i.nrf_ble_gatt_init)
    nrf_ble_gatt_on_ble_evt                  0x00029135   Thumb Code   270  nrf_ble_gatt.o(i.nrf_ble_gatt_on_ble_evt)
    nrf_ble_scan_filter_set                  0x00029303   Thumb Code    66  nrf_ble_scan.o(i.nrf_ble_scan_filter_set)
    nrf_ble_scan_filters_disable             0x00029345   Thumb Code    14  nrf_ble_scan.o(i.nrf_ble_scan_filters_disable)
    nrf_ble_scan_filters_enable              0x00029353   Thumb Code    54  nrf_ble_scan.o(i.nrf_ble_scan_filters_enable)
    nrf_ble_scan_init                        0x00029389   Thumb Code   106  nrf_ble_scan.o(i.nrf_ble_scan_init)
    nrf_ble_scan_on_ble_evt                  0x000294ef   Thumb Code   128  nrf_ble_scan.o(i.nrf_ble_scan_on_ble_evt)
    nrf_ble_scan_start                       0x00029571   Thumb Code    92  nrf_ble_scan.o(i.nrf_ble_scan_start)
    nrf_drv_clock_init                       0x00029619   Thumb Code    56  nrf_drv_clock.o(i.nrf_drv_clock_init)
    nrf_drv_clock_lfclk_release              0x00029659   Thumb Code    48  nrf_drv_clock.o(i.nrf_drv_clock_lfclk_release)
    nrf_fprintf                              0x0002968d   Thumb Code    26  nrf_fprintf.o(i.nrf_fprintf)
    nrf_fprintf_buffer_flush                 0x000296a7   Thumb Code    24  nrf_fprintf.o(i.nrf_fprintf_buffer_flush)
    nrf_fprintf_fmt                          0x000296bf   Thumb Code   474  nrf_fprintf_format.o(i.nrf_fprintf_fmt)
    nrf_log_backend_add                      0x00029a21   Thumb Code    86  nrf_log_frontend.o(i.nrf_log_backend_add)
    nrf_log_backend_rtt_init                 0x00029a7f   Thumb Code     4  nrf_log_backend_rtt.o(i.nrf_log_backend_rtt_init)
    nrf_log_backend_serial_put               0x00029a9d   Thumb Code   200  nrf_log_backend_serial.o(i.nrf_log_backend_serial_put)
    nrf_log_color_id_get                     0x00029b65   Thumb Code    42  nrf_log_frontend.o(i.nrf_log_color_id_get)
    nrf_log_default_backends_init            0x00029b95   Thumb Code    24  nrf_log_default_backends.o(i.nrf_log_default_backends_init)
    nrf_log_frontend_dequeue                 0x00029bb1   Thumb Code   528  nrf_log_frontend.o(i.nrf_log_frontend_dequeue)
    nrf_log_frontend_std_0                   0x00029de9   Thumb Code     8  nrf_log_frontend.o(i.nrf_log_frontend_std_0)
    nrf_log_frontend_std_1                   0x00029df1   Thumb Code    14  nrf_log_frontend.o(i.nrf_log_frontend_std_1)
    nrf_log_frontend_std_2                   0x00029dff   Thumb Code    16  nrf_log_frontend.o(i.nrf_log_frontend_std_2)
    nrf_log_frontend_std_6                   0x00029e0f   Thumb Code    28  nrf_log_frontend.o(i.nrf_log_frontend_std_6)
    nrf_log_hexdump_entry_process            0x00029e2d   Thumb Code   150  nrf_log_str_formatter.o(i.nrf_log_hexdump_entry_process)
    nrf_log_init                             0x00029edd   Thumb Code    28  nrf_log_frontend.o(i.nrf_log_init)
    nrf_log_module_cnt_get                   0x00029f05   Thumb Code    10  nrf_log_frontend.o(i.nrf_log_module_cnt_get)
    nrf_log_module_name_get                  0x00029f19   Thumb Code    24  nrf_log_frontend.o(i.nrf_log_module_name_get)
    nrf_log_panic                            0x00029f35   Thumb Code    36  nrf_log_frontend.o(i.nrf_log_panic)
    nrf_log_std_entry_process                0x00029f5d   Thumb Code   182  nrf_log_str_formatter.o(i.nrf_log_std_entry_process)
    nrf_memobj_alloc                         0x0002a013   Thumb Code    96  nrf_memobj.o(i.nrf_memobj_alloc)
    nrf_memobj_free                          0x0002a073   Thumb Code    50  nrf_memobj.o(i.nrf_memobj_free)
    nrf_memobj_get                           0x0002a0a5   Thumb Code     8  nrf_memobj.o(i.nrf_memobj_get)
    nrf_memobj_pool_init                     0x0002a0ad   Thumb Code     4  nrf_memobj.o(i.nrf_memobj_pool_init)
    nrf_memobj_put                           0x0002a0b1   Thumb Code    30  nrf_memobj.o(i.nrf_memobj_put)
    nrf_memobj_read                          0x0002a0cf   Thumb Code    16  nrf_memobj.o(i.nrf_memobj_read)
    nrf_memobj_write                         0x0002a0df   Thumb Code    16  nrf_memobj.o(i.nrf_memobj_write)
    nrf_pwr_mgmt_init                        0x0002a0f1   Thumb Code    28  nrf_pwr_mgmt.o(i.nrf_pwr_mgmt_init)
    nrf_pwr_mgmt_run                         0x0002a119   Thumb Code    64  nrf_pwr_mgmt.o(i.nrf_pwr_mgmt_run)
    nrf_pwr_mgmt_shutdown                    0x0002a15d   Thumb Code    54  nrf_pwr_mgmt.o(i.nrf_pwr_mgmt_shutdown)
    nrf_ringbuf_init                         0x0002a199   Thumb Code    28  nrf_ringbuf.o(i.nrf_ringbuf_init)
    nrf_sdh_ble_app_ram_start_get            0x0002a1c1   Thumb Code    16  nrf_sdh_ble.o(i.nrf_sdh_ble_app_ram_start_get)
    nrf_sdh_ble_default_cfg_set              0x0002a1d5   Thumb Code   272  nrf_sdh_ble.o(i.nrf_sdh_ble_default_cfg_set)
    nrf_sdh_ble_enable                       0x0002a305   Thumb Code   116  nrf_sdh_ble.o(i.nrf_sdh_ble_enable)
    nrf_sdh_enable_request                   0x0002a495   Thumb Code   102  nrf_sdh.o(i.nrf_sdh_enable_request)
    nrf_sdh_evts_poll                        0x0002a509   Thumb Code    32  nrf_sdh.o(i.nrf_sdh_evts_poll)
    nrf_sdh_is_enabled                       0x0002a52d   Thumb Code     6  nrf_sdh.o(i.nrf_sdh_is_enabled)
    nrf_section_iter_init                    0x0002a575   Thumb Code    10  nrf_section_iter.o(i.nrf_section_iter_init)
    nrf_section_iter_next                    0x0002a5a3   Thumb Code    32  nrf_section_iter.o(i.nrf_section_iter_next)
    nrf_sortlist_add                         0x0002a5c3   Thumb Code    34  nrf_sortlist.o(i.nrf_sortlist_add)
    nrf_sortlist_peek                        0x0002a5e5   Thumb Code     6  nrf_sortlist.o(i.nrf_sortlist_peek)
    nrf_sortlist_pop                         0x0002a5eb   Thumb Code    14  nrf_sortlist.o(i.nrf_sortlist_pop)
    nrf_sortlist_remove                      0x0002a5f9   Thumb Code    30  nrf_sortlist.o(i.nrf_sortlist_remove)
    nrf_strerror_find                        0x0002a619   Thumb Code    52  nrf_strerror.o(i.nrf_strerror_find)
    nrf_strerror_get                         0x0002a651   Thumb Code    14  nrf_strerror.o(i.nrf_strerror_get)
    nrfx_clock_enable                        0x0002a675   Thumb Code    34  nrfx_clock.o(i.nrfx_clock_enable)
    nrfx_clock_init                          0x0002a6a1   Thumb Code    26  nrfx_clock.o(i.nrfx_clock_init)
    nrfx_clock_lfclk_stop                    0x0002a6c1   Thumb Code    38  nrfx_clock.o(i.nrfx_clock_lfclk_stop)
    nrfx_gpiote_in_event_enable              0x0002a701   Thumb Code   144  nrfx_gpiote.o(i.nrfx_gpiote_in_event_enable)
    nrfx_gpiote_in_init                      0x0002a799   Thumb Code   196  nrfx_gpiote.o(i.nrfx_gpiote_in_init)
    nrfx_gpiote_in_is_set                    0x0002a869   Thumb Code    14  nrfx_gpiote.o(i.nrfx_gpiote_in_is_set)
    nrfx_gpiote_init                         0x0002a879   Thumb Code   108  nrfx_gpiote.o(i.nrfx_gpiote_init)
    nrfx_gpiote_is_init                      0x0002a8f1   Thumb Code    14  nrfx_gpiote.o(i.nrfx_gpiote_is_init)
    nrf_log_backend_rtt_api                  0x0002b5b8   Data          12  nrf_log_backend_rtt.o(.constdata)
    m_ram_start                              0x0002b65c   Data           4  nrf_sdh_ble.o(.constdata)
    __ctype_table                            0x0002b688   Data         129  ctype_o.o(.constdata)
    Region$$Table$$Base                      0x0002be9c   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x0002bebc   Number         0  anon$$obj.o(Region$$Table)
    log_const_data$$Base                     0x0002becc   Number         0  ble_db_discovery.o(log_const_data)
    m_nrf_log_ble_db_disc_logs_data_const    0x0002becc   Data           8  ble_db_discovery.o(log_const_data)
    m_nrf_log_nrf_ble_gatt_logs_data_const   0x0002bed4   Data           8  nrf_ble_gatt.o(log_const_data)
    m_nrf_log_nrf_ble_gq_logs_data_const     0x0002bedc   Data           8  nrf_ble_gq.o(log_const_data)
    m_nrf_log_ble_scan_logs_data_const       0x0002bee4   Data           8  nrf_ble_scan.o(log_const_data)
    m_nrf_log_clock_logs_data_const          0x0002beec   Data           8  nrf_drv_clock.o(log_const_data)
    m_nrf_log_CLOCK_logs_data_const          0x0002bef4   Data           8  nrfx_clock.o(log_const_data)
    m_nrf_log_GPIOTE_logs_data_const         0x0002befc   Data           8  nrfx_gpiote.o(log_const_data)
    m_nrf_log_PRS_logs_data_const            0x0002bf04   Data           8  nrfx_prs.o(log_const_data)
    m_nrf_log_UART_logs_data_const           0x0002bf0c   Data           8  nrfx_uart.o(log_const_data)
    m_nrf_log_UARTE_logs_data_const          0x0002bf14   Data           8  nrfx_uarte.o(log_const_data)
    m_nrf_log_app_button_logs_data_const     0x0002bf1c   Data           8  app_button.o(log_const_data)
    m_nrf_log_app_timer_logs_data_const      0x0002bf24   Data           8  app_timer2.o(log_const_data)
    m_nrf_log_pwr_mgmt_logs_data_const       0x0002bf2c   Data           8  nrf_pwr_mgmt.o(log_const_data)
    m_nrf_log_sortlist_logs_data_const       0x0002bf34   Data           8  nrf_sortlist.o(log_const_data)
    m_nrf_log_app_logs_data_const            0x0002bf3c   Data           8  nrf_log_frontend.o(log_const_data)
    m_nrf_log_nrf_sdh_logs_data_const        0x0002bf44   Data           8  nrf_sdh.o(log_const_data)
    m_nrf_log_nrf_sdh_ble_logs_data_const    0x0002bf4c   Data           8  nrf_sdh_ble.o(log_const_data)
    m_nrf_log_nrf_sdh_soc_logs_data_const    0x0002bf54   Data           8  nrf_sdh_soc.o(log_const_data)
    log_const_data$$Limit                    0x0002bf5c   Number         0  nrf_sdh_soc.o(log_const_data)
    log_mempool                              0x0002bf5c   Data          20  nrf_log_frontend.o(nrf_balloc)
    pwr_mgmt_data1$$Base                     0x0002bf70   Number         0  main.o(pwr_mgmt_data1)
    pwr_mgmt_data1$$Limit                    0x0002bf74   Number         0  main.o(pwr_mgmt_data1)
    sdh_ble_observers1$$Base                 0x0002bf74   Number         0  main.o(sdh_ble_observers1)
    sdh_ble_observers1$$Limit                0x0002bf8c   Number         0  bsp_btn_ble.o(sdh_ble_observers1)
    sdh_ble_observers3$$Base                 0x0002bf8c   Number         0  main.o(sdh_ble_observers3)
    sdh_ble_observers3$$Limit                0x0002bf94   Number         0  main.o(sdh_ble_observers3)
    sdh_soc_observers0$$Base                 0x0002bf94   Number         0  nrf_drv_clock.o(sdh_soc_observers0)
    sdh_soc_observers0$$Limit                0x0002bf9c   Number         0  nrf_drv_clock.o(sdh_soc_observers0)
    sdh_stack_observers0$$Base               0x0002bf9c   Number         0  nrf_sdh_ble.o(sdh_stack_observers0)
    sdh_stack_observers0$$Limit              0x0002bfac   Number         0  nrf_sdh_soc.o(sdh_stack_observers0)
    sdh_state_observers0$$Base               0x0002bfac   Number         0  nrf_drv_clock.o(sdh_state_observers0)
    sdh_state_observers0$$Limit              0x0002bfb4   Number         0  nrf_drv_clock.o(sdh_state_observers0)
    Image$$RW_IRAM1$$Base                    0x20002a38   Number         0  anon$$obj.o ABSOLUTE
    SystemCoreClock                          0x20002b1c   Data           4  system_nrf52840.o(.data)
    nrf_nvic_state                           0x20002d20   Data          12  app_util_platform.o(.bss)
    _SEGGER_RTT                              0x200032f0   Data         120  segger_rtt.o(.bss)
    __initial_sp                             0x20005578   Data           0  arm_startup_nrf52840.o(STACK)



==============================================================================

Memory Map of the image

  Image Entry point : 0x00027201

  Load Region LR_IROM1 (Base: 0x00027000, Size: 0x0000509c, Max: 0x000d9000, ABSOLUTE)

    Execution Region ER_IROM1 (Base: 0x00027000, Size: 0x00004fb4, Max: 0x000d9000, ABSOLUTE)

    Base Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x00027000   0x00000200   Data   RO         5034    RESET               arm_startup_nrf52840.o
    0x00027200   0x00000000   Code   RO         5090  * .ARM.Collect$$$$00000000  mc_w.l(entry.o)
    0x00027200   0x00000004   Code   RO         5137    .ARM.Collect$$$$00000001  mc_w.l(entry2.o)
    0x00027204   0x00000004   Code   RO         5140    .ARM.Collect$$$$00000004  mc_w.l(entry5.o)
    0x00027208   0x00000000   Code   RO         5142    .ARM.Collect$$$$00000008  mc_w.l(entry7b.o)
    0x00027208   0x00000000   Code   RO         5144    .ARM.Collect$$$$0000000A  mc_w.l(entry8b.o)
    0x00027208   0x00000008   Code   RO         5145    .ARM.Collect$$$$0000000B  mc_w.l(entry9a.o)
    0x00027210   0x00000000   Code   RO         5147    .ARM.Collect$$$$0000000D  mc_w.l(entry10a.o)
    0x00027210   0x00000000   Code   RO         5149    .ARM.Collect$$$$0000000F  mc_w.l(entry11a.o)
    0x00027210   0x00000004   Code   RO         5138    .ARM.Collect$$$$00002712  mc_w.l(entry2.o)
    0x00027214   0x000000c8   Code   RO         3370    .emb_text           nrf_atfifo.o
    0x000272dc   0x000000e2   Code   RO         3440    .emb_text           nrf_atomic.o
    0x000273be   0x00000002   PAD
    0x000273c0   0x00000024   Code   RO         5035    .text               arm_startup_nrf52840.o
    0x000273e4   0x00000062   Code   RO         5093    .text               mc_w.l(uldiv.o)
    0x00027446   0x0000001e   Code   RO         5095    .text               mc_w.l(llshl.o)
    0x00027464   0x00000008   Code   RO         5097    .text               mc_w.l(ctype_o.o)
    0x0002746c   0x00000024   Code   RO         5125    .text               mc_w.l(memcpya.o)
    0x00027490   0x00000024   Code   RO         5127    .text               mc_w.l(memseta.o)
    0x000274b4   0x0000000e   Code   RO         5129    .text               mc_w.l(strlen.o)
    0x000274c2   0x0000001a   Code   RO         5131    .text               mc_w.l(memcmp.o)
    0x000274dc   0x00000012   Code   RO         5133    .text               mc_w.l(strcpy.o)
    0x000274ee   0x00000020   Code   RO         5151    .text               mc_w.l(llushr.o)
    0x0002750e   0x00000002   PAD
    0x00027510   0x00000024   Code   RO         5153    .text               mc_w.l(init.o)
    0x00027534   0x000000a4   Code   RO         1806    i.GPIOTE_IRQHandler  nrfx_gpiote.o
    0x000275d8   0x00000058   Code   RO         1667    i.POWER_CLOCK_IRQHandler  nrfx_clock.o
    0x00027630   0x00000010   Code   RO         3156    i.RTC1_IRQHandler   drv_rtc.o
    0x00027640   0x00000004   Code   RO         4574    i.SEGGER_RTT_Init   segger_rtt.o
    0x00027644   0x0000005c   Code   RO         4588    i.SEGGER_RTT_WriteNoLock  segger_rtt.o
    0x000276a0   0x00000004   Code   RO         4825    i.SWI2_EGU2_IRQHandler  nrf_sdh.o
    0x000276a4   0x00000248   Code   RO         5043    i.SystemInit        system_nrf52840.o
    0x000278ec   0x0000000c   Code   RO         2131    i.UARTE0_UART0_IRQHandler  nrfx_prs.o
    0x000278f8   0x00000068   Code   RO         4592    i._DoInit           segger_rtt.o
    0x00027960   0x00000016   Code   RO         4593    i._GetAvailWriteSpace  segger_rtt.o
    0x00027976   0x0000005a   Code   RO         4595    i._WriteBlocking    segger_rtt.o
    0x000279d0   0x00000042   Code   RO         4596    i._WriteNoCheck     segger_rtt.o
    0x00027a12   0x0000000e   Code   RO         5157    i.__scatterload_copy  mc_w.l(handlers.o)
    0x00027a20   0x00000002   Code   RO         5158    i.__scatterload_null  mc_w.l(handlers.o)
    0x00027a22   0x0000000e   Code   RO         5159    i.__scatterload_zeroinit  mc_w.l(handlers.o)
    0x00027a30   0x00000024   Code   RO         4826    i.__sd_nvic_app_accessible_irq  nrf_sdh.o
    0x00027a54   0x00000036   Code   RO          591    i.advertising_buttons_configure  bsp_btn_ble.o
    0x00027a8a   0x00000006   Code   RO          474    i.alert_timer_handler  bsp.o
    0x00027a90   0x00000024   Code   RO         2554    i.app_button_enable  app_button.o
    0x00027ab4   0x00000080   Code   RO         2555    i.app_button_init   app_button.o
    0x00027b34   0x00000084   Code   RO         2708    i.app_error_fault_handler  app_error_weak.o
    0x00027bb8   0x00000016   Code   RO         2652    i.app_error_handler_bare  app_error.o
    0x00027bce   0x00000002   PAD
    0x00027bd0   0x0000000c   Code   RO         2867    i.app_timer_cnt_get  app_timer2.o
    0x00027bdc   0x00000016   Code   RO         2868    i.app_timer_create  app_timer2.o
    0x00027bf2   0x00000002   PAD
    0x00027bf4   0x0000005c   Code   RO         2869    i.app_timer_init    app_timer2.o
    0x00027c50   0x00000030   Code   RO         2872    i.app_timer_start   app_timer2.o
    0x00027c80   0x0000000c   Code   RO         2873    i.app_timer_stop    app_timer2.o
    0x00027c8c   0x00000048   Code   RO         3088    i.app_util_critical_region_enter  app_util_platform.o
    0x00027cd4   0x00000034   Code   RO         3089    i.app_util_critical_region_exit  app_util_platform.o
    0x00027d08   0x00000046   Code   RO          698    i.ble_advdata_search  ble_advdata.o
    0x00027d4e   0x000000b6   Code   RO          700    i.ble_advdata_uuid_find  ble_advdata.o
    0x00027e04   0x00000108   Code   RO            5    i.ble_evt_handler   main.o
    0x00027f0c   0x00000070   Code   RO          592    i.ble_evt_handler   bsp_btn_ble.o
    0x00027f7c   0x0000000c   Code   RO          356    i.bsp_board_button_idx_to_pin  boards.o
    0x00027f88   0x00000028   Code   RO          357    i.bsp_board_button_state_get  boards.o
    0x00027fb0   0x00000054   Code   RO          358    i.bsp_board_init    boards.o
    0x00028004   0x0000002c   Code   RO          360    i.bsp_board_led_invert  boards.o
    0x00028030   0x00000010   Code   RO          361    i.bsp_board_led_off  boards.o
    0x00028040   0x00000010   Code   RO          362    i.bsp_board_led_on  boards.o
    0x00028050   0x00000028   Code   RO          363    i.bsp_board_led_state_get  boards.o
    0x00028078   0x00000012   Code   RO          364    i.bsp_board_leds_off  boards.o
    0x0002808a   0x00000012   Code   RO          365    i.bsp_board_leds_on  boards.o
    0x0002809c   0x00000024   Code   RO          366    i.bsp_board_pin_to_button_idx  boards.o
    0x000280c0   0x00000038   Code   RO          593    i.bsp_btn_ble_init  bsp_btn_ble.o
    0x000280f8   0x0000001e   Code   RO          594    i.bsp_btn_ble_sleep_mode_prepare  bsp_btn_ble.o
    0x00028116   0x00000002   PAD
    0x00028118   0x00000090   Code   RO          475    i.bsp_button_event_handler  bsp.o
    0x000281a8   0x0000000c   Code   RO          476    i.bsp_button_is_pressed  bsp.o
    0x000281b4   0x0000001c   Code   RO            6    i.bsp_event_handler  main.o
    0x000281d0   0x00000044   Code   RO          479    i.bsp_event_to_button_action_assign  bsp.o
    0x00028214   0x00000018   Code   RO          480    i.bsp_indication_set  bsp.o
    0x0002822c   0x00000094   Code   RO          481    i.bsp_init          bsp.o
    0x000282c0   0x000001c8   Code   RO          482    i.bsp_led_indication  bsp.o
    0x00028488   0x00000006   Code   RO          484    i.bsp_wakeup_button_enable  bsp.o
    0x0002848e   0x00000002   PAD
    0x00028490   0x00000094   Code   RO         4328    i.buf_prealloc      nrf_log_frontend.o
    0x00028524   0x0000002e   Code   RO         3655    i.buffer_add        nrf_fprintf_format.o
    0x00028552   0x00000002   PAD
    0x00028554   0x00000018   Code   RO         4329    i.buffer_is_empty   nrf_log_frontend.o
    0x0002856c   0x00000028   Code   RO         2557    i.button_get        app_button.o
    0x00028594   0x00000008   Code   RO          485    i.button_timer_handler  bsp.o
    0x0002859c   0x0000001c   Code   RO         1807    i.channel_free      nrfx_gpiote.o
    0x000285b8   0x00000048   Code   RO         1808    i.channel_port_alloc  nrfx_gpiote.o
    0x00028600   0x00000010   Code   RO         1809    i.channel_port_get  nrfx_gpiote.o
    0x00028610   0x00000028   Code   RO         1319    i.clock_clk_started_notify  nrf_drv_clock.o
    0x00028638   0x0000001c   Code   RO         1320    i.clock_irq_handler  nrf_drv_clock.o
    0x00028654   0x00000018   Code   RO         2875    i.compare_func      app_timer2.o
    0x0002866c   0x000000c0   Code   RO         1017    i.data_length_update  nrf_ble_gatt.o
    0x0002872c   0x00000048   Code   RO         2558    i.detection_delay_timeout_handler  app_button.o
    0x00028774   0x00000014   Code   RO         4330    i.dropped_sat16_get  nrf_log_frontend.o
    0x00028788   0x00000014   Code   RO         3157    i.drv_rtc_compare_disable  drv_rtc.o
    0x0002879c   0x0000000e   Code   RO         3160    i.drv_rtc_compare_pending  drv_rtc.o
    0x000287aa   0x00000050   Code   RO         3161    i.drv_rtc_compare_set  drv_rtc.o
    0x000287fa   0x00000008   Code   RO         3162    i.drv_rtc_counter_get  drv_rtc.o
    0x00028802   0x00000002   PAD
    0x00028804   0x000000d8   Code   RO         3163    i.drv_rtc_init      drv_rtc.o
    0x000288dc   0x0000001e   Code   RO         3164    i.drv_rtc_irq_trigger  drv_rtc.o
    0x000288fa   0x00000008   Code   RO         3166    i.drv_rtc_overflow_enable  drv_rtc.o
    0x00028902   0x00000008   Code   RO         3167    i.drv_rtc_overflow_pending  drv_rtc.o
    0x0002890a   0x00000008   Code   RO         3168    i.drv_rtc_start     drv_rtc.o
    0x00028912   0x00000008   Code   RO         3169    i.drv_rtc_stop      drv_rtc.o
    0x0002891a   0x000000de   Code   RO         3174    i.drv_rtc_windowed_compare_set  drv_rtc.o
    0x000289f8   0x00000012   Code   RO         3175    i.evt_enable        drv_rtc.o
    0x00028a0a   0x00000002   PAD
    0x00028a0c   0x000000e0   Code   RO         2559    i.evt_handle        app_button.o
    0x00028aec   0x00000014   Code   RO         3176    i.evt_pending       drv_rtc.o
    0x00028b00   0x00000098   Code   RO            7    i.gatt_evt_handler  main.o
    0x00028b98   0x00000020   Code   RO            8    i.gatt_init         main.o
    0x00028bb8   0x00000034   Code   RO         2876    i.get_now           app_timer2.o
    0x00028bec   0x0000003c   Code   RO         2560    i.gpiote_event_handler  app_button.o
    0x00028c28   0x000000a6   Code   RO         3656    i.int_print         nrf_fprintf_format.o
    0x00028cce   0x00000034   Code   RO         4331    i.invalid_packets_omit  nrf_log_frontend.o
    0x00028d02   0x00000016   Code   RO         1200    i.is_whitelist_used  nrf_ble_scan.o
    0x00028d18   0x00000030   Code   RO          486    i.leds_off          bsp.o
    0x00028d48   0x00000014   Code   RO          487    i.leds_timer_handler  bsp.o
    0x00028d5c   0x00000018   Code   RO         1018    i.link_init         nrf_ble_gatt.o
    0x00028d74   0x000000a4   Code   RO         4332    i.log_skip          nrf_log_frontend.o
    0x00028e18   0x000000d4   Code   RO            9    i.main              main.o
    0x00028eec   0x0000007e   Code   RO         3695    i.memobj_op         nrf_memobj.o
    0x00028f6a   0x00000002   PAD
    0x00028f6c   0x00000030   Code   RO         4333    i.module_idx_get    nrf_log_frontend.o
    0x00028f9c   0x00000026   Code   RO         3374    i.nrf_atfifo_init   nrf_atfifo.o
    0x00028fc2   0x00000016   Code   RO         3375    i.nrf_atfifo_item_alloc  nrf_atfifo.o
    0x00028fd8   0x00000016   Code   RO         3376    i.nrf_atfifo_item_free  nrf_atfifo.o
    0x00028fee   0x00000016   Code   RO         3377    i.nrf_atfifo_item_get  nrf_atfifo.o
    0x00029004   0x00000016   Code   RO         3378    i.nrf_atfifo_item_put  nrf_atfifo.o
    0x0002901a   0x00000006   Code   RO         3442    i.nrf_atomic_flag_clear_fetch  nrf_atomic.o
    0x00029020   0x00000006   Code   RO         3443    i.nrf_atomic_flag_set  nrf_atomic.o
    0x00029026   0x0000000c   Code   RO         3445    i.nrf_atomic_u32_add  nrf_atomic.o
    0x00029032   0x0000000a   Code   RO         3449    i.nrf_atomic_u32_fetch_and  nrf_atomic.o
    0x0002903c   0x0000000a   Code   RO         3451    i.nrf_atomic_u32_fetch_store  nrf_atomic.o
    0x00029046   0x0000000c   Code   RO         3455    i.nrf_atomic_u32_or  nrf_atomic.o
    0x00029052   0x0000000c   Code   RO         3457    i.nrf_atomic_u32_sub  nrf_atomic.o
    0x0002905e   0x00000044   Code   RO         3579    i.nrf_balloc_alloc  nrf_balloc.o
    0x000290a2   0x00000030   Code   RO         3580    i.nrf_balloc_free   nrf_balloc.o
    0x000290d2   0x00000030   Code   RO         3581    i.nrf_balloc_init   nrf_balloc.o
    0x00029102   0x00000010   Code   RO         1810    i.nrf_bitmask_bit_is_set  nrfx_gpiote.o
    0x00029112   0x00000022   Code   RO         1024    i.nrf_ble_gatt_init  nrf_ble_gatt.o
    0x00029134   0x00000150   Code   RO         1025    i.nrf_ble_gatt_on_ble_evt  nrf_ble_gatt.o
    0x00029284   0x0000004c   Code   RO         1202    i.nrf_ble_scan_connect_with_target  nrf_ble_scan.o
    0x000292d0   0x00000014   Code   RO         1204    i.nrf_ble_scan_default_conn_param_set  nrf_ble_scan.o
    0x000292e4   0x0000001e   Code   RO         1205    i.nrf_ble_scan_default_param_set  nrf_ble_scan.o
    0x00029302   0x00000042   Code   RO         1207    i.nrf_ble_scan_filter_set  nrf_ble_scan.o
    0x00029344   0x0000000e   Code   RO         1208    i.nrf_ble_scan_filters_disable  nrf_ble_scan.o
    0x00029352   0x00000036   Code   RO         1209    i.nrf_ble_scan_filters_enable  nrf_ble_scan.o
    0x00029388   0x0000006a   Code   RO         1210    i.nrf_ble_scan_init  nrf_ble_scan.o
    0x000293f2   0x000000fc   Code   RO         1211    i.nrf_ble_scan_on_adv_report  nrf_ble_scan.o
    0x000294ee   0x00000080   Code   RO         1212    i.nrf_ble_scan_on_ble_evt  nrf_ble_scan.o
    0x0002956e   0x00000002   PAD
    0x00029570   0x00000088   Code   RO         1214    i.nrf_ble_scan_start  nrf_ble_scan.o
    0x000295f8   0x0000000e   Code   RO         1668    i.nrf_clock_event_check  nrfx_clock.o
    0x00029606   0x00000010   Code   RO         1669    i.nrf_clock_event_clear  nrfx_clock.o
    0x00029616   0x00000002   PAD
    0x00029618   0x00000040   Code   RO         1327    i.nrf_drv_clock_init  nrf_drv_clock.o
    0x00029658   0x00000034   Code   RO         1331    i.nrf_drv_clock_lfclk_release  nrf_drv_clock.o
    0x0002968c   0x0000001a   Code   RO         3616    i.nrf_fprintf       nrf_fprintf.o
    0x000296a6   0x00000018   Code   RO         3617    i.nrf_fprintf_buffer_flush  nrf_fprintf.o
    0x000296be   0x000001da   Code   RO         3657    i.nrf_fprintf_fmt   nrf_fprintf_format.o
    0x00029898   0x00000032   Code   RO          368    i.nrf_gpio_cfg      boards.o
    0x000298ca   0x00000032   Code   RO         1811    i.nrf_gpio_cfg      nrfx_gpiote.o
    0x000298fc   0x00000030   Code   RO         1813    i.nrf_gpio_cfg_sense_set  nrfx_gpiote.o
    0x0002992c   0x00000030   Code   RO         1814    i.nrf_gpio_latches_read_and_clear  nrfx_gpiote.o
    0x0002995c   0x0000001c   Code   RO          369    i.nrf_gpio_pin_port_decode  boards.o
    0x00029978   0x0000001c   Code   RO         1816    i.nrf_gpio_pin_port_decode  nrfx_gpiote.o
    0x00029994   0x0000002c   Code   RO         1817    i.nrf_gpio_pin_present_check  nrfx_gpiote.o
    0x000299c0   0x00000016   Code   RO         1818    i.nrf_gpio_pin_read  nrfx_gpiote.o
    0x000299d6   0x00000026   Code   RO          370    i.nrf_gpio_pin_write  boards.o
    0x000299fc   0x00000014   Code   RO         1820    i.nrf_gpiote_event_clear  nrfx_gpiote.o
    0x00029a10   0x00000010   Code   RO         1821    i.nrf_gpiote_event_is_set  nrfx_gpiote.o
    0x00029a20   0x0000005c   Code   RO         4334    i.nrf_log_backend_add  nrf_log_frontend.o
    0x00029a7c   0x00000002   Code   RO         4189    i.nrf_log_backend_rtt_flush  nrf_log_backend_rtt.o
    0x00029a7e   0x00000004   Code   RO         4190    i.nrf_log_backend_rtt_init  nrf_log_backend_rtt.o
    0x00029a82   0x00000002   Code   RO         4191    i.nrf_log_backend_rtt_panic_set  nrf_log_backend_rtt.o
    0x00029a84   0x00000018   Code   RO         4192    i.nrf_log_backend_rtt_put  nrf_log_backend_rtt.o
    0x00029a9c   0x000000c8   Code   RO         4258    i.nrf_log_backend_serial_put  nrf_log_backend_serial.o
    0x00029b64   0x00000030   Code   RO         4336    i.nrf_log_color_id_get  nrf_log_frontend.o
    0x00029b94   0x0000001c   Code   RO         4292    i.nrf_log_default_backends_init  nrf_log_default_backends.o
    0x00029bb0   0x00000238   Code   RO         4337    i.nrf_log_frontend_dequeue  nrf_log_frontend.o
    0x00029de8   0x00000008   Code   RO         4339    i.nrf_log_frontend_std_0  nrf_log_frontend.o
    0x00029df0   0x0000000e   Code   RO         4340    i.nrf_log_frontend_std_1  nrf_log_frontend.o
    0x00029dfe   0x00000010   Code   RO         4341    i.nrf_log_frontend_std_2  nrf_log_frontend.o
    0x00029e0e   0x0000001c   Code   RO         4345    i.nrf_log_frontend_std_6  nrf_log_frontend.o
    0x00029e2a   0x00000002   PAD
    0x00029e2c   0x000000b0   Code   RO         4515    i.nrf_log_hexdump_entry_process  nrf_log_str_formatter.o
    0x00029edc   0x00000028   Code   RO         4346    i.nrf_log_init      nrf_log_frontend.o
    0x00029f04   0x00000014   Code   RO         4347    i.nrf_log_module_cnt_get  nrf_log_frontend.o
    0x00029f18   0x0000001c   Code   RO         4350    i.nrf_log_module_name_get  nrf_log_frontend.o
    0x00029f34   0x00000028   Code   RO         4351    i.nrf_log_panic     nrf_log_frontend.o
    0x00029f5c   0x000000b6   Code   RO         4516    i.nrf_log_std_entry_process  nrf_log_str_formatter.o
    0x0002a012   0x00000060   Code   RO         3696    i.nrf_memobj_alloc  nrf_memobj.o
    0x0002a072   0x00000032   Code   RO         3697    i.nrf_memobj_free   nrf_memobj.o
    0x0002a0a4   0x00000008   Code   RO         3698    i.nrf_memobj_get    nrf_memobj.o
    0x0002a0ac   0x00000004   Code   RO         3699    i.nrf_memobj_pool_init  nrf_memobj.o
    0x0002a0b0   0x0000001e   Code   RO         3700    i.nrf_memobj_put    nrf_memobj.o
    0x0002a0ce   0x00000010   Code   RO         3701    i.nrf_memobj_read   nrf_memobj.o
    0x0002a0de   0x00000010   Code   RO         3702    i.nrf_memobj_write  nrf_memobj.o
    0x0002a0ee   0x00000002   PAD
    0x0002a0f0   0x00000028   Code   RO         3774    i.nrf_pwr_mgmt_init  nrf_pwr_mgmt.o
    0x0002a118   0x00000044   Code   RO         3775    i.nrf_pwr_mgmt_run  nrf_pwr_mgmt.o
    0x0002a15c   0x0000003c   Code   RO         3776    i.nrf_pwr_mgmt_shutdown  nrf_pwr_mgmt.o
    0x0002a198   0x0000001c   Code   RO         3969    i.nrf_ringbuf_init  nrf_ringbuf.o
    0x0002a1b4   0x0000000c   Code   RO         3177    i.nrf_rtc_event_clear  drv_rtc.o
    0x0002a1c0   0x00000014   Code   RO         4946    i.nrf_sdh_ble_app_ram_start_get  nrf_sdh_ble.o
    0x0002a1d4   0x00000130   Code   RO         4947    i.nrf_sdh_ble_default_cfg_set  nrf_sdh_ble.o
    0x0002a304   0x00000130   Code   RO         4948    i.nrf_sdh_ble_enable  nrf_sdh_ble.o
    0x0002a434   0x00000060   Code   RO         4949    i.nrf_sdh_ble_evts_poll  nrf_sdh_ble.o
    0x0002a494   0x00000074   Code   RO         4828    i.nrf_sdh_enable_request  nrf_sdh.o
    0x0002a508   0x00000024   Code   RO         4829    i.nrf_sdh_evts_poll  nrf_sdh.o
    0x0002a52c   0x0000000c   Code   RO         4830    i.nrf_sdh_is_enabled  nrf_sdh.o
    0x0002a538   0x0000003c   Code   RO         5000    i.nrf_sdh_soc_evts_poll  nrf_sdh_soc.o
    0x0002a574   0x0000000a   Code   RO         4027    i.nrf_section_iter_init  nrf_section_iter.o
    0x0002a57e   0x00000024   Code   RO         4028    i.nrf_section_iter_item_set  nrf_section_iter.o
    0x0002a5a2   0x00000020   Code   RO         4029    i.nrf_section_iter_next  nrf_section_iter.o
    0x0002a5c2   0x00000022   Code   RO         4062    i.nrf_sortlist_add  nrf_sortlist.o
    0x0002a5e4   0x00000006   Code   RO         4064    i.nrf_sortlist_peek  nrf_sortlist.o
    0x0002a5ea   0x0000000e   Code   RO         4065    i.nrf_sortlist_pop  nrf_sortlist.o
    0x0002a5f8   0x0000001e   Code   RO         4066    i.nrf_sortlist_remove  nrf_sortlist.o
    0x0002a616   0x00000002   PAD
    0x0002a618   0x00000038   Code   RO         4118    i.nrf_strerror_find  nrf_strerror.o
    0x0002a650   0x00000014   Code   RO         4119    i.nrf_strerror_get  nrf_strerror.o
    0x0002a664   0x00000010   Code   RO         1334    i.nrf_wdt_started   nrf_drv_clock.o
    0x0002a674   0x0000002c   Code   RO         1674    i.nrfx_clock_enable  nrfx_clock.o
    0x0002a6a0   0x00000020   Code   RO         1677    i.nrfx_clock_init   nrfx_clock.o
    0x0002a6c0   0x00000030   Code   RO         1680    i.nrfx_clock_lfclk_stop  nrfx_clock.o
    0x0002a6f0   0x00000010   Code   RO         3178    i.nrfx_coredep_delay_us  drv_rtc.o
    0x0002a700   0x00000098   Code   RO         1827    i.nrfx_gpiote_in_event_enable  nrfx_gpiote.o
    0x0002a798   0x000000d0   Code   RO         1829    i.nrfx_gpiote_in_init  nrfx_gpiote.o
    0x0002a868   0x0000000e   Code   RO         1830    i.nrfx_gpiote_in_is_set  nrfx_gpiote.o
    0x0002a876   0x00000002   PAD
    0x0002a878   0x00000078   Code   RO         1832    i.nrfx_gpiote_init  nrfx_gpiote.o
    0x0002a8f0   0x00000014   Code   RO         1833    i.nrfx_gpiote_is_init  nrfx_gpiote.o
    0x0002a904   0x000000a8   Code   RO         1026    i.on_connected_evt  nrf_ble_gatt.o
    0x0002a9ac   0x000000ac   Code   RO         1027    i.on_exchange_mtu_request_evt  nrf_ble_gatt.o
    0x0002aa58   0x0000001c   Code   RO         1851    i.pin_configured_set  nrfx_gpiote.o
    0x0002aa74   0x00000018   Code   RO         1852    i.pin_in_use_by_port  nrfx_gpiote.o
    0x0002aa8c   0x00000018   Code   RO         1853    i.pin_in_use_by_te  nrfx_gpiote.o
    0x0002aaa4   0x000000d0   Code   RO         1854    i.port_event_handle  nrfx_gpiote.o
    0x0002ab74   0x00000010   Code   RO         1855    i.port_handler_polarity_get  nrfx_gpiote.o
    0x0002ab84   0x0000003c   Code   RO         4518    i.postfix_process   nrf_log_str_formatter.o
    0x0002abc0   0x00000098   Code   RO         4519    i.prefix_process    nrf_log_str_formatter.o
    0x0002ac58   0x0000005c   Code   RO         2877    i.rtc_irq           app_timer2.o
    0x0002acb4   0x00000064   Code   RO         2878    i.rtc_schedule      app_timer2.o
    0x0002ad18   0x00000074   Code   RO         2879    i.rtc_update        app_timer2.o
    0x0002ad8c   0x000000a0   Code   RO           10    i.scan_evt_handler  main.o
    0x0002ae2c   0x00000058   Code   RO           11    i.scan_init         main.o
    0x0002ae84   0x00000028   Code   RO           12    i.scan_start        main.o
    0x0002aeac   0x00000058   Code   RO         1335    i.sd_state_evt_handler  nrf_drv_clock.o
    0x0002af04   0x00000030   Code   RO         4835    i.sdh_request_observer_notify  nrf_sdh.o
    0x0002af34   0x0000002c   Code   RO         4836    i.sdh_state_observer_notify  nrf_sdh.o
    0x0002af60   0x00000050   Code   RO         4193    i.serial_tx         nrf_log_backend_rtt.o
    0x0002afb0   0x00000020   Code   RO           13    i.shutdown_handler  main.o
    0x0002afd0   0x0000007c   Code   RO         3777    i.shutdown_process  nrf_pwr_mgmt.o
    0x0002b04c   0x00000018   Code   RO         1336    i.soc_evt_handler   nrf_drv_clock.o
    0x0002b064   0x00000058   Code   RO         4838    i.softdevices_evt_irq_enable  nrf_sdh.o
    0x0002b0bc   0x0000000c   Code   RO         2880    i.sortlist_pop      app_timer2.o
    0x0002b0c8   0x00000030   Code   RO         2561    i.state_set         app_button.o
    0x0002b0f8   0x00000094   Code   RO         4353    i.std_n             nrf_log_frontend.o
    0x0002b18c   0x00000058   Code   RO         2881    i.timer_expire      app_timer2.o
    0x0002b1e4   0x00000078   Code   RO         2882    i.timer_req_process  app_timer2.o
    0x0002b25c   0x00000038   Code   RO         2883    i.timer_req_schedule  app_timer2.o
    0x0002b294   0x00000018   Code   RO         2562    i.timer_start       app_button.o
    0x0002b2ac   0x000000b4   Code   RO         3658    i.unsigned_print    nrf_fprintf_format.o
    0x0002b360   0x00000020   Code   RO         2563    i.usr_event         app_button.o
    0x0002b380   0x0000004c   Code   RO          488    i.wakeup_button_cfg  bsp.o
    0x0002b3cc   0x00000004   Data   RO           15    .constdata          main.o
    0x0002b3d0   0x00000008   Data   RO          371    .constdata          boards.o
    0x0002b3d8   0x0000002c   Data   RO          493    .constdata          bsp.o
    0x0002b404   0x00000016   Data   RO         1028    .constdata          nrf_ble_gatt.o
    0x0002b41a   0x00000002   PAD
    0x0002b41c   0x00000008   Data   RO         1857    .constdata          nrfx_gpiote.o
    0x0002b424   0x00000008   Data   RO         2566    .constdata          app_button.o
    0x0002b42c   0x00000014   Data   RO         2886    .constdata          app_timer2.o
    0x0002b440   0x00000006   Data   RO         3179    .constdata          drv_rtc.o
    0x0002b446   0x00000010   Data   RO         3659    .constdata          nrf_fprintf_format.o
    0x0002b456   0x00000002   PAD
    0x0002b458   0x00000018   Data   RO         3779    .constdata          nrf_pwr_mgmt.o
    0x0002b470   0x0000000c   Data   RO         3780    .constdata          nrf_pwr_mgmt.o
    0x0002b47c   0x0000013c   Data   RO         4120    .constdata          nrf_strerror.o
    0x0002b5b8   0x0000000c   Data   RO         4195    .constdata          nrf_log_backend_rtt.o
    0x0002b5c4   0x0000000c   PAD
    0x0002b5d0   0x00000006   Data   RO         4196    .constdata          nrf_log_backend_rtt.o
    0x0002b5d6   0x00000002   PAD
    0x0002b5d8   0x0000000c   Data   RO         4358    .constdata          nrf_log_frontend.o
    0x0002b5e4   0x00000010   Data   RO         4839    .constdata          nrf_sdh.o
    0x0002b5f4   0x00000028   Data   RO         4840    .constdata          nrf_sdh.o
    0x0002b61c   0x00000010   Data   RO         4841    .constdata          nrf_sdh.o
    0x0002b62c   0x00000010   Data   RO         4842    .constdata          nrf_sdh.o
    0x0002b63c   0x00000020   Data   RO         4950    .constdata          nrf_sdh_ble.o
    0x0002b65c   0x00000010   Data   RO         4951    .constdata          nrf_sdh_ble.o
    0x0002b66c   0x00000010   Data   RO         5001    .constdata          nrf_sdh_soc.o
    0x0002b67c   0x0000000c   Data   RO         5002    .constdata          nrf_sdh_soc.o
    0x0002b688   0x00000081   Data   RO         5098    .constdata          mc_w.l(ctype_o.o)
    0x0002b709   0x00000003   PAD
    0x0002b70c   0x00000004   Data   RO         5099    .constdata          mc_w.l(ctype_o.o)
    0x0002b710   0x000000a2   Data   RO         1029    .conststring        nrf_ble_gatt.o
    0x0002b7b2   0x00000002   PAD
    0x0002b7b4   0x000003eb   Data   RO         4121    .conststring        nrf_strerror.o
    0x0002bb9f   0x00000001   PAD
    0x0002bba0   0x00000066   Data   RO         4520    .conststring        nrf_log_str_formatter.o
    0x0002bc06   0x00000002   PAD
    0x0002bc08   0x000001cf   Data   RO         4952    .conststring        nrf_sdh_ble.o
    0x0002bdd7   0x00000001   PAD
    0x0002bdd8   0x0000000c   Data   RO          827    .conststrlit        ble_db_discovery.o
    0x0002bde4   0x0000000d   Data   RO         1030    .conststrlit        nrf_ble_gatt.o
    0x0002bdf1   0x00000003   PAD
    0x0002bdf4   0x0000000b   Data   RO         1131    .conststrlit        nrf_ble_gq.o
    0x0002bdff   0x00000001   PAD
    0x0002be00   0x00000009   Data   RO         1216    .conststrlit        nrf_ble_scan.o
    0x0002be09   0x00000003   PAD
    0x0002be0c   0x00000006   Data   RO         1342    .conststrlit        nrf_drv_clock.o
    0x0002be12   0x00000002   PAD
    0x0002be14   0x00000006   Data   RO         1684    .conststrlit        nrfx_clock.o
    0x0002be1a   0x00000002   PAD
    0x0002be1c   0x00000007   Data   RO         1861    .conststrlit        nrfx_gpiote.o
    0x0002be23   0x00000001   PAD
    0x0002be24   0x00000004   Data   RO         2136    .conststrlit        nrfx_prs.o
    0x0002be28   0x00000005   Data   RO         2218    .conststrlit        nrfx_uart.o
    0x0002be2d   0x00000003   PAD
    0x0002be30   0x00000006   Data   RO         2412    .conststrlit        nrfx_uarte.o
    0x0002be36   0x00000002   PAD
    0x0002be38   0x0000000b   Data   RO         2567    .conststrlit        app_button.o
    0x0002be43   0x00000001   PAD
    0x0002be44   0x0000000a   Data   RO         2887    .conststrlit        app_timer2.o
    0x0002be4e   0x00000002   PAD
    0x0002be50   0x00000009   Data   RO         3781    .conststrlit        nrf_pwr_mgmt.o
    0x0002be59   0x00000003   PAD
    0x0002be5c   0x00000009   Data   RO         4067    .conststrlit        nrf_sortlist.o
    0x0002be65   0x00000003   PAD
    0x0002be68   0x00000010   Data   RO         4293    .conststrlit        nrf_log_default_backends.o
    0x0002be78   0x00000004   Data   RO         4359    .conststrlit        nrf_log_frontend.o
    0x0002be7c   0x00000008   Data   RO         4843    .conststrlit        nrf_sdh.o
    0x0002be84   0x0000000c   Data   RO         4953    .conststrlit        nrf_sdh_ble.o
    0x0002be90   0x0000000c   Data   RO         5003    .conststrlit        nrf_sdh_soc.o
    0x0002be9c   0x00000020   Data   RO         5155    Region$$Table       anon$$obj.o
    0x0002bebc   0x00000010   Data   RO         4295    log_backends        nrf_log_default_backends.o
    0x0002becc   0x00000008   Data   RO          829    log_const_data      ble_db_discovery.o
    0x0002bed4   0x00000008   Data   RO         1031    log_const_data      nrf_ble_gatt.o
    0x0002bedc   0x00000008   Data   RO         1132    log_const_data      nrf_ble_gq.o
    0x0002bee4   0x00000008   Data   RO         1217    log_const_data      nrf_ble_scan.o
    0x0002beec   0x00000008   Data   RO         1343    log_const_data      nrf_drv_clock.o
    0x0002bef4   0x00000008   Data   RO         1686    log_const_data      nrfx_clock.o
    0x0002befc   0x00000008   Data   RO         1862    log_const_data      nrfx_gpiote.o
    0x0002bf04   0x00000008   Data   RO         2138    log_const_data      nrfx_prs.o
    0x0002bf0c   0x00000008   Data   RO         2219    log_const_data      nrfx_uart.o
    0x0002bf14   0x00000008   Data   RO         2413    log_const_data      nrfx_uarte.o
    0x0002bf1c   0x00000008   Data   RO         2569    log_const_data      app_button.o
    0x0002bf24   0x00000008   Data   RO         2890    log_const_data      app_timer2.o
    0x0002bf2c   0x00000008   Data   RO         3783    log_const_data      nrf_pwr_mgmt.o
    0x0002bf34   0x00000008   Data   RO         4068    log_const_data      nrf_sortlist.o
    0x0002bf3c   0x00000008   Data   RO         4363    log_const_data      nrf_log_frontend.o
    0x0002bf44   0x00000008   Data   RO         4845    log_const_data      nrf_sdh.o
    0x0002bf4c   0x00000008   Data   RO         4955    log_const_data      nrf_sdh_ble.o
    0x0002bf54   0x00000008   Data   RO         5004    log_const_data      nrf_sdh_soc.o
    0x0002bf5c   0x00000014   Data   RO         4364    nrf_balloc          nrf_log_frontend.o
    0x0002bf70   0x00000004   Data   RO           17    pwr_mgmt_data1      main.o
    0x0002bf74   0x00000010   Data   RO           18    sdh_ble_observers1  main.o
    0x0002bf84   0x00000008   Data   RO          596    sdh_ble_observers1  bsp_btn_ble.o
    0x0002bf8c   0x00000008   Data   RO           19    sdh_ble_observers3  main.o
    0x0002bf94   0x00000008   Data   RO         1344    sdh_soc_observers0  nrf_drv_clock.o
    0x0002bf9c   0x00000008   Data   RO         4956    sdh_stack_observers0  nrf_sdh_ble.o
    0x0002bfa4   0x00000008   Data   RO         5005    sdh_stack_observers0  nrf_sdh_soc.o
    0x0002bfac   0x00000008   Data   RO         1345    sdh_state_observers0  nrf_drv_clock.o


    Execution Region RW_IRAM1 (Base: 0x20002a38, Size: 0x00002b40, Max: 0x0003d5c8, ABSOLUTE)

    Base Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20002a38   0x00000002   Data   RW           16    .data               main.o
    0x20002a3a   0x00000002   PAD
    0x20002a3c   0x00000010   Data   RW          494    .data               bsp.o
    0x20002a4c   0x00000008   Data   RW          595    .data               bsp_btn_ble.o
    0x20002a54   0x00000008   Data   RW         1685    .data               nrfx_clock.o
    0x20002a5c   0x00000008   Data   RW         2137    .data               nrfx_prs.o
    0x20002a64   0x00000004   PAD
    0x20002a68   0x00000018   Data   RW         2568    .data               app_button.o
    0x20002a80   0x00000020   Data   RW         2888    .data               app_timer2.o
    0x20002aa0   0x00000004   Data   RW         2889    .data               app_timer2.o
    0x20002aa4   0x0000000c   Data   RW         3180    .data               drv_rtc.o
    0x20002ab0   0x00000008   Data   RW         3782    .data               nrf_pwr_mgmt.o
    0x20002ab8   0x00000001   Data   RW         4197    .data               nrf_log_backend_rtt.o
    0x20002ab9   0x00000003   PAD
    0x20002abc   0x00000008   Data   RW         4294    .data               nrf_log_default_backends.o
    0x20002ac4   0x00000004   Data   RW         4360    .data               nrf_log_frontend.o
    0x20002ac8   0x00000008   Data   RW         4361    .data               nrf_log_frontend.o
    0x20002ad0   0x00000008   Data   RW         4362    .data               nrf_log_frontend.o
    0x20002ad8   0x00000040   Data   RW         4521    .data               nrf_log_str_formatter.o
    0x20002b18   0x00000003   Data   RW         4844    .data               nrf_sdh.o
    0x20002b1b   0x00000001   Data   RW         4954    .data               nrf_sdh_ble.o
    0x20002b1c   0x00000004   Data   RW         5044    .data               system_nrf52840.o
    0x20002b20   0x00000064   Zero   RW           14    .bss                main.o
    0x20002b84   0x00000004   PAD
    0x20002b88   0x00000020   Zero   RW          489    .bss                bsp.o
    0x20002ba8   0x00000020   Zero   RW          490    .bss                bsp.o
    0x20002bc8   0x0000000c   Zero   RW          491    .bss                bsp.o
    0x20002bd4   0x00000004   PAD
    0x20002bd8   0x00000020   Zero   RW          492    .bss                bsp.o
    0x20002bf8   0x00000014   Zero   RW         1337    .bss                nrf_drv_clock.o
    0x20002c0c   0x0000006c   Zero   RW         1856    .bss                nrfx_gpiote.o
    0x20002c78   0x00000020   Zero   RW         2564    .bss                app_button.o
    0x20002c98   0x00000020   Zero   RW         2565    .bss                app_button.o
    0x20002cb8   0x00000010   Zero   RW         2884    .bss                app_timer2.o
    0x20002cc8   0x00000058   Zero   RW         2885    .bss                app_timer2.o
    0x20002d20   0x0000000c   Zero   RW         3094    .bss                app_util_platform.o
    0x20002d2c   0x0000000c   Zero   RW         3778    .bss                nrf_pwr_mgmt.o
    0x20002d38   0x00000040   Zero   RW         4194    .bss                nrf_log_backend_rtt.o
    0x20002d78   0x000000c0   Zero   RW         4354    .bss                nrf_log_frontend.o
    0x20002e38   0x00000080   Zero   RW         4355    .bss                nrf_log_frontend.o
    0x20002eb8   0x00000018   Zero   RW         4356    .bss                nrf_log_frontend.o
    0x20002ed0   0x00000420   Zero   RW         4357    .bss                nrf_log_frontend.o
    0x200032f0   0x00000288   Zero   RW         4597    .bss                segger_rtt.o
    0x20003578   0x00002000   Zero   RW         5032    STACK               arm_startup_nrf52840.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       664         68         27         24         64      18610   app_button.o
        22          0          0          0          0       2617   app_error.o
       132         46          0          0          0      34177   app_error_weak.o
       846         88         38         36        104      17673   app_timer2.o
       124         14          0          0         12      10890   app_util_platform.o
        36          8        512          0       8192        964   arm_startup_nrf52840.o
       252          0          0          0          0      44995   ble_advdata.o
         0          0         20          0          0      17476   ble_db_discovery.o
       440         46          8          0          0      15927   boards.o
      1016         90         44         16        108      16923   bsp.o
       252         10          8          8          0       5152   bsp_btn_ble.o
       704         76          6         12          0      49733   drv_rtc.o
      1008        334         32          2        100     130703   main.o
       326          0          0          0          0       6327   nrf_atfifo.o
       294          0          0          0          0       8423   nrf_atomic.o
       164          0          0          0          0       3864   nrf_balloc.o
       926        272        205          0          0      37967   nrf_ble_gatt.o
         0          0         19          0          0       1679   nrf_ble_gq.o
       904         44         17          0          0      13411   nrf_ble_scan.o
       312         36         30          0         20      44488   nrf_drv_clock.o
        50          0          0          0          0       3765   nrf_fprintf.o
       866          4         16          0          0       6842   nrf_fprintf_format.o
       112         18         18          1         64       6657   nrf_log_backend_rtt.o
       200          0          0          0          0      11141   nrf_log_backend_serial.o
        28          4         32          8          0       3517   nrf_log_default_backends.o
      1506        128         44         20       1400      31935   nrf_log_frontend.o
       570        108        102         64          0       6004   nrf_log_str_formatter.o
       346          0          0          0          0      10091   nrf_memobj.o
       292         42         53          8         12      43062   nrf_pwr_mgmt.o
        28          0          0          0          0       2543   nrf_ringbuf.o
       384         46        104          3          0      43407   nrf_sdh.o
       724        232        539          1          0       6273   nrf_sdh_ble.o
        60          4         56          0          0       2644   nrf_sdh_soc.o
        78          0          0          0          0       3012   nrf_section_iter.o
        84          0         17          0          0       4457   nrf_sortlist.o
        76         10       1319          0          0       2975   nrf_strerror.o
       242         32         14          8          0      47816   nrfx_clock.o
      1386        108         23          0        108      68469   nrfx_gpiote.o
        12          6         12          8          0       2310   nrfx_prs.o
         0          0         13          0          0      11120   nrfx_uart.o
         0          0         14          0          0      10986   nrfx_uarte.o
       378         34          0          0        648       9521   segger_rtt.o
       584         68          0          4          0      12703   system_nrf52840.o

    ----------------------------------------------------------------------
     16458       <USER>       <GROUP>        232      10840     833249   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        30          0         50          9          8          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

         8          4        133          0          0         68   ctype_o.o
         0          0          0          0          0          0   entry.o
         0          0          0          0          0          0   entry10a.o
         0          0          0          0          0          0   entry11a.o
         8          4          0          0          0          0   entry2.o
         4          0          0          0          0          0   entry5.o
         0          0          0          0          0          0   entry7b.o
         0          0          0          0          0          0   entry8b.o
         8          4          0          0          0          0   entry9a.o
        30          0          0          0          0          0   handlers.o
        36          8          0          0          0         68   init.o
        30          0          0          0          0         68   llshl.o
        32          0          0          0          0         68   llushr.o
        26          0          0          0          0         80   memcmp.o
        36          0          0          0          0         68   memcpya.o
        36          0          0          0          0        108   memseta.o
        18          0          0          0          0         68   strcpy.o
        14          0          0          0          0         68   strlen.o
        98          0          0          0          0         92   uldiv.o

    ----------------------------------------------------------------------
       386         <USER>        <GROUP>          0          0        756   Library Totals
         2          0          3          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

       384         20        133          0          0        756   mc_w.l

    ----------------------------------------------------------------------
       386         <USER>        <GROUP>          0          0        756   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     16844       1996       3560        232      10840     811449   Grand Totals
     16844       1996       3560        232      10840     811449   ELF Image Totals
     16844       1996       3560        232          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                20404 (  19.93kB)
    Total RW  Size (RW Data + ZI Data)             11072 (  10.81kB)
    Total ROM Size (Code + RO Data + RW Data)      20636 (  20.15kB)

==============================================================================

