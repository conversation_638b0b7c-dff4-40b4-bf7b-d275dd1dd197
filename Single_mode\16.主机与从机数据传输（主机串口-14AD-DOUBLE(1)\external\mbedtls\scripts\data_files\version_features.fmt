/*
 *  Version feature information
 *
 *  Copyright (C) 2006-2015, ARM Limited, All Rights Reserved
 *  SPDX-License-Identifier: Apache-2.0
 *
 *  Licensed under the Apache License, Version 2.0 (the "License"); you may
 *  not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 *  WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 *  This file is part of mbed TLS (https://tls.mbed.org)
 */

#if !defined(MBEDTLS_CONFIG_FILE)
#include "mbedtls/config.h"
#else
#include MBEDTLS_CONFIG_FILE
#endif

#if defined(MBEDTLS_VERSION_C)

#include "mbedtls/version.h"

#include <string.h>

static const char *features[] = {
#if defined(MBEDTLS_VERSION_FEATURES)
FEATURE_DEFINES
#endif /* MBEDTLS_VERSION_FEATURES */
    NULL
};

int mbedtls_version_check_feature( const char *feature )
{
    const char **idx = features;

    if( *idx == NULL )
        return( -2 );

    if( feature == NULL )
        return( -1 );

    while( *idx != NULL )
    {
        if( !strcmp( *idx, feature ) )
            return( 0 );
        idx++;
    }
    return( -1 );
}

#endif /* MBEDTLS_VERSION_C */
