<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html lang="en">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=ISO-8859-1">
<meta http-equiv="Content-Style-Type" content="text/css">
<link rel="up" title="FatFs" href="../00index_e.html">
<link rel="alternate" hreflang="ja" title="Japanese" href="../ja/getlabel.html">
<link rel="stylesheet" href="../css_e.css" type="text/css" media="screen" title="ELM Default">
<title>FatFs - f_getlabel</title>
</head>

<body>

<div class="para func">
<h2>f_getlabel</h2>
<p>The f_getlabel function returns volume label and volume serial number of a drive.</p>
<pre>
FRESULT f_getlabel (
  const TCHAR* <span class="arg">path</span>,  <span class="c">/* [IN] Drive number */</span>
  TCHAR* <span class="arg">label</span>,       <span class="c">/* [OUT] Volume label */</span>
  DWORD* <span class="arg">vsn</span>          <span class="c">/* [OUT] Volume serial number */</span>
);
</pre>
</div>

<div class="para arg">
<h4>Parameters</h4>
<dl class="par">
<dt>path</dt>
<dd>Pointer to the null-terminated string that specifies the <a href="filename.html">logical drive</a>. Null-string specifies the default drive.</dd>
<dt>label</dt>
<dd>Pointer to the buffer to store the volume label. The buffer size must be at least 24 items at <tt>_LFN_UNICODE == 0</tt> or 12 items at <tt>_LFN_UNICODE == 1</tt>. If the volume has no label, a null-string will be returned. Set null pointer if this information is not needed.</dd>
<dt>vsn</dt>
<dd>Pointer to the <tt>DWORD</tt> variable to store the volume serial number. Set null pointer if this information is not needed.</dd>
</dl>
</div>


<div class="para ret">
<h4>Return Values</h4>
<p>
<a href="rc.html#ok">FR_OK</a>,
<a href="rc.html#de">FR_DISK_ERR</a>,
<a href="rc.html#ie">FR_INT_ERR</a>,
<a href="rc.html#nr">FR_NOT_READY</a>,
<a href="rc.html#id">FR_INVALID_DRIVE</a>,
<a href="rc.html#ne">FR_NOT_ENABLED</a>,
<a href="rc.html#ns">FR_NO_FILESYSTEM</a>,
<a href="rc.html#tm">FR_TIMEOUT</a>
</p>
</div>


<div class="para comp">
<h4>QuickInfo</h4>
<p>Available when <tt>_USE_LABEL == 1</tt>.</p>
</div>


<div class="para use">
<h4>Example</h4>
<pre>
    char str[24];

    <span class="c">/* Get volume label of the default drive */</span>
    f_getlabel("", str, 0);

    <span class="c">/* Get volume label of the drive 2 */</span>
    f_getlabel("2:", str, 0);
</pre>
</div>


<div class="para ref">
<h4>See Also</h4>
<tt><a href="setlabel.html">f_setlabel</a></tt>
</div>


<p class="foot"><a href="../00index_e.html">Return</a></p>
</body>
</html>
