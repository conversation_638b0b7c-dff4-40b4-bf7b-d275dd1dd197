<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html lang="ja">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta http-equiv="Content-Style-Type" content="text/css">
<link rel="up" title="FatFs" href="../00index_j.html">
<link rel="alternate" hreflang="en" title="English" href="../en/lseek.html">
<link rel="stylesheet" href="../css_j.css" type="text/css" media="screen" title="ELM Default">
<title>FatFs - f_lseek</title>
</head>

<body>

<div class="para func">
<h2>f_lseek</h2>
<p>ファイルのリード/ライト ポインタを移動します。また、高速シーク機能使用時にはCLMT(後述)の作成にも使用します。</p>
<pre>
FRESULT f_lseek (
  FIL* <span class="arg">fp</span>,      <span class="c">/* [IN] ファイル オブジェクト構造体へのポインタ */</span>
  FSIZE_t <span class="arg">ofs</span>   <span class="c">/* [IN] 移動先オフセット */</span>
);
</pre>
</div>

<div class="para arg">
<h4>引数</h4>
<dl class="par">
<dt>fp</dt>
<dd>対象となるファイル オブジェクト構造体へのポインタを指定します。</dd>
<dt>ofs</dt>
<dd>移動先のオフセット(リード/ライト ポインタ)値。ファイル先頭からのオフセットをバイト単位で指定します。データ型<tt>FSIZE_t</tt>は、<tt>DWORD</tt>(32-bit)または<tt>QWORD</tt>(64-bit)のエリアスで、exFATサポートの有無により切り替わります。</dd>
</dl>
</div>


<div class="para ret">
<h4>戻り値</h4>
<p>
<a href="rc.html#ok">FR_OK</a>,
<a href="rc.html#de">FR_DISK_ERR</a>,
<a href="rc.html#ie">FR_INT_ERR</a>,
<a href="rc.html#io">FR_INVALID_OBJECT</a>,
<a href="rc.html#tm">FR_TIMEOUT</a>,
<a href="rc.html#nc">FR_NOT_ENOUGH_CORE</a>
</p>
</div>


<div class="para desc">
<h4>解説</h4>
<p>ファイルのリード/ライト ポインタ(次に読み出し・書き込みされるバイトのオフセット)を移動します。オフセットの原点はファイル先頭です。書き込みモードでファイル サイズより大きな値を指定すると、そこまでファイル サイズが拡張され、拡張された部分のデータは未定義となります。データを遅延無く高速に書き込みたいときは、予めこの関数で必要なサイズまでファイル サイズを拡張しておくと良いでしょう。ファイルに連続したデータ領域を割り当てる必要があるときは、<tt>f_expand</tt>関数を使用してください。<tt>f_lseek</tt>関数が正常終了したあとは、リード/ライト ポインタが正しく移動したかチェックするべきです。リード/ライト ポインタが指定より小さいときは、次の原因が考えられます。</p>
<ul>
<li>非書き込みモードまたは高速シーク モードのため、ファイル サイズでクリップされた。</li>
<li>ファイル拡張中にディスクが満杯になった。</li>
</ul>
<p>高速シーク モードは、ファイルのクラスタ配置情報(CLMT)をメモリ上に保持しておくことにより、FATにアクセスすることなく後方シークやロング シークを高速に行う機能で、シーク動作のほか<tt>f_read/f_wtite</tt>関数の動作にも適用されます。ファイルが高速シーク モードの間は<tt>f_wtite/f_lseek</tt>関数によるファイル サイズの拡張はできません。</p>
<p>高速シーク モードは、ファイル オブジェクトのメンバ<tt>cltbl</tt>(<tt>f_open</tt>関数でNULLになる)にNULL以外を設定したとき有効になるので、まずCLMTを作成しておく必要があります。これを作成するには、まずCLMT格納バッファ(<tt>DWORD</tt>型配列)を準備し、<tt>cltbl</tt>にそのポインタをセットします。そして、配列の先頭要素にその配列のサイズ(要素数)を入れ、<tt>f_lseek</tt>関数を<tt class="arg">ofs</tt>に<tt>CREATE_LINKMAP</tt>を指定して呼び出します。関数が成功するとCLMTが作成され、以降の<tt>f_read/f_write/f_lseek</tt>関数ではFATへのアクセスは発生しません。CLMTの先頭要素には実際に使用した(または必要となる)要素数が返されます。使用される要素数は、(ファイルの分割数 + 1) * 2 です。たとえば、ファイルが5つのフラグメントに分断されているときは、12要素が使用されます。<tt>FR_NOT_ENOUGH_CORE</tt>で失敗したときは、配列サイズが不足です。</p>
</div>


<div class="para comp">
<h4>対応情報</h4>
<p><tt>_FS_MINIMIZE &lt; 3</tt>のとき使用可能です。高速シーク モードを利用するときは、<tt><a href="config.html#use_fastseek">_USE_FASTSEEK</a> == 1</tt>である必要があります。</p>
</div>


<div class="para use">
<h4>使用例</h4>
<pre>
    <span class="c">/* ファイルを開く */</span>
    fp = malloc(sizeof (FIL));
    res = f_open(fp, "file.dat", FA_READ|FA_WRITE);
    if (res) ...

    <span class="c">/* ファイル オフセット5000へ移動 */</span>
    res = f_lseek(fp, 5000);

    <span class="c">/* ファイル終端へ移動(ファイル追記の準備) */</span>
    res = f_lseek(fp, f_size(fp));

    <span class="c">/* 3000バイト進める */</span>
    res = f_lseek(fp, f_tell(fp) + 3000);

    <span class="c">/* 2000バイト戻す (ラップアラウンドに注意) */</span>
    res = f_lseek(fp, f_tell(fp) - 2000);
</pre>
<pre>
<span class="c">/* クラスタ先行割り当て (ストリーミング ライト時のバッファ オーバーラン防止) */</span>

    res = f_open(fp, "record.wav", FA_CREATE_NEW | FA_WRITE);    <span class="c">/* ファイル作成 */</span>

    res = f_lseek(fp, MAX_SIZE);             <span class="c">/* 十分なクラスタの先行割り当て */</span>
    if (res || f_tell(fp) != PRE_SIZE) ...   <span class="c">/* 正しくファイルが拡張されたかチェック */</span>

    res = f_lseek(fp, DATA_START);           <span class="c">/* データ ストリームの記録(アロケーションディレイ無し) */</span>
    ...

    res = f_truncate(fp);                    <span class="c">/* 不要領域の切り捨て */</span>
    res = f_lseek(fp, 0);                    <span class="c">/* ヘッダの記録 */</span>
    ...

    res = f_close(fp);
</pre>
<pre>
<span class="c">/* 高速シーク機能を使う */</span>

    DWORD clmt[SZ_TBL];                    <span class="c">/* リンク マップ テーブル格納バッファ */</span>

    res = f_open(fp, fname, FA_READ | FA_WRITE);   <span class="c">/* ファイルを開く */</span>

    res = f_lseek(fp, ofs1);               <span class="c">/* 通常シーク (オープン時、cltblはNULLに初期化される) */</span>

    fp-&gt;cltbl = clmt;                      <span class="c">/* 高速シーク機能の有効化 */</span>
    clmt[0] = SZ_TBL;                      <span class="c">/* 先頭要素に配列要素数をセット */</span>
    res = f_lseek(fp, CREATE_LINKMAP);     <span class="c">/* CLMTの作成 */</span>
    ...

    res = f_lseek(fp, ofs2);               <span class="c">/* 以降、f_read/f_write/f_lseekでFATアクセスは発生しない */</span>
</pre>
</div>


<div class="para ref">
<h4>参照</h4>
<p><tt><a href="open.html">f_open</a>, <a href="truncate.html">f_truncate</a>, <a href="expand.html">f_expand</a>, <a href="sfile.html">FIL</a></tt></p>
</div>

<p class="foot"><a href="../00index_j.html">戻る</a></p>
</body>
</html>
