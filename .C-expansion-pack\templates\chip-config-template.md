template:
  id: chip-config-template
  name: 芯片配置文档模板
  version: 1.0
  output:
    format: markdown
    filename: "docs/{{chip_model}}-config.md"
    title: "{{chip_model}} 芯片配置文档"

workflow:
  mode: interactive
  elicitation: advanced-elicitation

sections:
  - id: overview
    title: 配置概述
    instruction: |
      创建芯片配置的总体概述，包括芯片型号、主要特性和配置目标。
      [[LLM: 首先收集芯片基本信息，然后提供配置策略建议]]
    elicit: true
    template: |
      - **芯片型号**: {{chip_model}}
      - **封装类型**: {{package_type}}
      - **工作电压**: {{operating_voltage}}
      - **工作频率**: {{operating_frequency}}
      - **主要特性**: {{key_features}}
      - **配置目标**: {{configuration_goals}}

  - id: clock-system
    title: 时钟系统配置
    instruction: |
      详细配置芯片的时钟系统，包括外部晶振、内部振荡器、PLL设置等。
      [[LLM: 根据芯片型号提供具体的时钟配置代码和参数]]
    condition: "{{chip_model}} == 'nrf52832' || {{chip_model}} == 'ads129x'"
    sections:
      - id: external-crystal
        title: 外部晶振配置
        instruction: 配置外部高频和低频晶振参数
        template: |
          **高频晶振 (HFXO)**
          - 频率: {{hf_crystal_freq}} MHz
          - 负载电容: {{hf_load_cap}} pF
          - 启动时间: {{hf_startup_time}} μs
          
          **低频晶振 (LFXO)**
          - 频率: {{lf_crystal_freq}} kHz
          - 负载电容: {{lf_load_cap}} pF
          - 启动时间: {{lf_startup_time}} ms

      - id: internal-oscillator
        title: 内部振荡器配置
        instruction: 配置内部RC振荡器参数和校准
        template: |
          **内部高频振荡器 (HFRC)**
          - 标称频率: {{hfrc_freq}} MHz
          - 精度: ±{{hfrc_accuracy}}%
          - 校准方式: {{hfrc_calibration}}
          
          **内部低频振荡器 (LFRC)**
          - 标称频率: {{lfrc_freq}} kHz
          - 精度: ±{{lfrc_accuracy}}%

      - id: pll-configuration
        title: PLL配置
        instruction: 配置锁相环参数和倍频设置
        condition: "{{has_pll}} == true"
        template: |
          **PLL参数**
          - 输入频率: {{pll_input_freq}} MHz
          - 倍频系数: {{pll_multiplier}}
          - 输出频率: {{pll_output_freq}} MHz
          - 锁定时间: {{pll_lock_time}} μs

  - id: power-management
    title: 电源管理配置
    instruction: |
      配置芯片的电源管理系统，包括电源域、低功耗模式、电压调节器等。
      [[LLM: 根据功耗要求提供最优的电源配置方案]]
    elicit: true
    sections:
      - id: power-domains
        title: 电源域配置
        instruction: 定义各个电源域的配置和控制策略
        template: |
          **核心电源域**
          - 电压范围: {{core_voltage_range}} V
          - 调节器类型: {{core_regulator_type}}
          - 低功耗模式: {{core_lp_modes}}
          
          **外设电源域**
          - 电压范围: {{periph_voltage_range}} V
          - 独立控制: {{periph_independent_control}}
          - 按需供电: {{periph_on_demand}}

      - id: low-power-modes
        title: 低功耗模式
        instruction: 配置各种低功耗模式的参数和唤醒条件
        type: table
        template: |
          | 模式 | 功耗 | 唤醒时间 | 保持状态 | 唤醒源 |
          |------|------|----------|----------|--------|
          {{#each low_power_modes}}
          | {{mode_name}} | {{power_consumption}} | {{wakeup_time}} | {{retained_state}} | {{wakeup_sources}} |
          {{/each}}

  - id: gpio-configuration
    title: GPIO配置
    instruction: |
      配置GPIO引脚的功能分配、电气特性和初始状态。
      [[LLM: 根据硬件原理图提供完整的GPIO配置表]]
    elicit: true
    sections:
      - id: pin-assignment
        title: 引脚分配
        instruction: 定义每个GPIO引脚的功能分配
        type: table
        template: |
          | 引脚 | 功能 | 方向 | 初始状态 | 上下拉 | 驱动强度 |
          |------|------|------|----------|--------|----------|
          {{#each gpio_pins}}
          | P{{pin_number}} | {{function}} | {{direction}} | {{initial_state}} | {{pull_config}} | {{drive_strength}} |
          {{/each}}

      - id: special-functions
        title: 特殊功能引脚
        instruction: 配置具有特殊功能的引脚
        template: |
          **复位引脚**
          - 引脚: {{reset_pin}}
          - 复位电平: {{reset_level}}
          - 复位时间: {{reset_duration}} ms
          
          **调试接口**
          - SWDIO: {{swdio_pin}}
          - SWCLK: {{swclk_pin}}
          - SWO: {{swo_pin}}
          
          **外部中断**
          {{#each external_interrupts}}
          - {{irq_name}}: P{{pin_number}}, 触发方式: {{trigger_type}}, 优先级: {{priority}}
          {{/each}}

  - id: peripheral-config
    title: 外设配置
    instruction: |
      配置芯片内置外设的参数和工作模式。
      [[LLM: 根据项目需求选择和配置相关外设]]
    condition: "{{chip_model}} == 'nrf52832'"
    sections:
      - id: communication-interfaces
        title: 通信接口配置
        instruction: 配置SPI、I2C、UART等通信接口
        repeatable: true
        template: |
          **{{interface_type}} 配置**
          - 实例: {{instance_number}}
          - 引脚配置:
            {{#if is_spi}}
            - SCK: P{{sck_pin}}
            - MOSI: P{{mosi_pin}}
            - MISO: P{{miso_pin}}
            - CS: P{{cs_pin}}
            {{/if}}
            {{#if is_i2c}}
            - SCL: P{{scl_pin}}
            - SDA: P{{sda_pin}}
            {{/if}}
            {{#if is_uart}}
            - TX: P{{tx_pin}}
            - RX: P{{rx_pin}}
            - RTS: P{{rts_pin}}
            - CTS: P{{cts_pin}}
            {{/if}}
          - 工作参数:
            - 频率/波特率: {{frequency_baudrate}}
            - 工作模式: {{operating_mode}}
            - 中断优先级: {{interrupt_priority}}

      - id: timers-pwm
        title: 定时器和PWM配置
        instruction: 配置定时器和PWM模块
        repeatable: true
        template: |
          **定时器{{timer_id}} 配置**
          - 工作模式: {{timer_mode}}
          - 时钟源: {{clock_source}}
          - 预分频: {{prescaler}}
          - 比较通道:
            {{#each compare_channels}}
            - CC{{channel_id}}: {{compare_value}}, 输出引脚: P{{output_pin}}
            {{/each}}

      - id: adc-configuration
        title: ADC配置
        instruction: 配置模数转换器参数
        condition: "{{has_adc}} == true"
        template: |
          **ADC配置**
          - 分辨率: {{adc_resolution}} 位
          - 参考电压: {{reference_voltage}} V
          - 采样时间: {{sampling_time}} μs
          - 输入通道:
            {{#each adc_channels}}
            - AIN{{channel_id}}: P{{input_pin}}, 增益: {{gain}}, 偏移: {{offset}}
            {{/each}}

  - id: ads129x-specific
    title: ADS129x特定配置
    instruction: |
      配置ADS129x系列芯片的特定参数和寄存器设置。
      [[LLM: 根据应用需求提供最优的ADS129x配置]]
    condition: "{{chip_model}} == 'ads129x'"
    elicit: true
    sections:
      - id: register-settings
        title: 寄存器配置
        instruction: 配置ADS129x的关键寄存器
        type: table
        template: |
          | 寄存器 | 地址 | 配置值 | 功能描述 |
          |--------|------|--------|----------|
          | CONFIG1 | 0x01 | {{config1_value}} | {{config1_description}} |
          | CONFIG2 | 0x02 | {{config2_value}} | {{config2_description}} |
          | CONFIG3 | 0x03 | {{config3_value}} | {{config3_description}} |
          | LOFF | 0x04 | {{loff_value}} | {{loff_description}} |
          {{#each channel_configs}}
          | CH{{channel_id}}SET | {{register_addr}} | {{config_value}} | {{description}} |
          {{/each}}

      - id: sampling-configuration
        title: 采样配置
        instruction: 配置采样率、滤波器和数据格式
        template: |
          **采样参数**
          - 采样率: {{sampling_rate}} SPS
          - 数据格式: {{data_format}}
          - 滤波器: {{filter_type}}
          - 增益设置: {{gain_setting}}
          
          **通道配置**
          {{#each channels}}
          - 通道{{channel_id}}: {{enabled}}, 增益: {{gain}}, 输入: {{input_type}}
          {{/each}}

  - id: initialization-code
    title: 初始化代码
    instruction: |
      生成完整的芯片初始化代码，包括所有配置的实现。
      [[LLM: 根据前面的配置生成可编译的C代码]]
    type: code-block
    template: |
      ```c
      // {{chip_model}} 初始化代码
      // 生成时间: {{generation_timestamp}}
      
      #include "{{chip_model}}_config.h"
      
      /**
       * @brief 芯片初始化函数
       * @return 初始化结果
       */
      int {{chip_model}}_init(void) {
          int result = 0;
          
          // 时钟系统初始化
          result = clock_system_init();
          if (result != 0) return result;
          
          // 电源管理初始化
          result = power_management_init();
          if (result != 0) return result;
          
          // GPIO初始化
          result = gpio_init();
          if (result != 0) return result;
          
          // 外设初始化
          result = peripheral_init();
          if (result != 0) return result;
          
          return 0;
      }
      
      {{initialization_functions}}
      ```

  - id: verification-checklist
    title: 验证检查清单
    instruction: 提供配置验证的检查清单
    type: checklist
    template: |
      **功能验证**
      - [ ] 时钟系统工作正常
      - [ ] 电源管理功能正确
      - [ ] GPIO状态符合预期
      - [ ] 外设响应正常
      
      **性能验证**
      - [ ] 启动时间: < {{max_startup_time}} ms
      - [ ] 功耗: < {{max_power_consumption}} mA
      - [ ] 时钟精度: ±{{clock_accuracy}}%
      
      **可靠性验证**
      - [ ] 复位序列正确
      - [ ] 异常处理完善
      - [ ] 看门狗功能有效
