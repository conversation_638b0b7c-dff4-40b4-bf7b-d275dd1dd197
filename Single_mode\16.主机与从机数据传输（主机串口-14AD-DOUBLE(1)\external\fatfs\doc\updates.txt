R0.12b (September 4, 2016)
  Improved f_rename() to be able to rename objects with the same name but case.
  Fixed an error in the case conversion teble of code page 866. (ff.c)
  Fixed writing data is truncated at the file offset 4GiB on the exFAT volume. (appeared at R0.12)
  Fixed creating a file in the root directory of exFAT volume can fail. (appeared at R0.12)
  Fixed f_mkfs() creating exFAT volume with too small cluster size can collapse unallocated memory. (appeared at R0.12)
  Fixed wrong object name can be returned when read directory at Unicode cfg. (appeared at R0.12)
  Fixed large file allocation/removing on the exFAT volume collapses allocation bitmap. (appeared at R0.12)
  Fixed some internal errors in f_expand() and f_lseek(). (appeared at R0.12)

R0.12a (July 10, 2016)
  Added support for creating exFAT volume with some changes of f_mkfs().
  Added a file open method FA_OPEN_APPEND. An f_lseek() following f_open() is no longer needed.
  f_forward() is available regardless of _FS_TINY.
  Fixed f_mkfs() creates wrong volume. (appeared at R0.12)
  Fixed wrong memory read in create_name(). (appeared at R0.12)
  Fixed compilation fails at some configurations, _USE_FASTSEEK and _USE_FORWARD.

R0.12 (April 12, 2016)
  Added support for exFAT file system. (_FS_EXFAT)
  Added f_expand(). (_USE_EXPAND)
  Changed some members in FINFO structure and behavior of f_readdir().
  Added an option _USE_CHMOD and removed an option _WORD_ACCESS.
  Fixed errors in the case conversion teble of Unicode (cc*.c).

R0.11a (September 5, 2015)
  Fixed wrong media change can lead a deadlock at thread-safe configuration.
  Added code page 771, 860, 861, 863, 864, 865 and 869. (_CODE_PAGE)
  Removed some code pages actually not exist on the standard systems. (_CODE_PAGE)
  Fixed errors in the case conversion teble of code page 437 and 850 (ff.c).
  Fixed errors in the case conversion teble of Unicode (cc*.c).

R0.11 (February 9, 2015)
  Added f_findfirst() and f_findnext(). (_USE_FIND)
  Fixed f_unlink() does not remove cluster chain of the file. (appeared at R0.10c)
  Fixed _FS_NORTC option does not work properly. (appeared at R0.10c)

R0.10c (November 9, 2014)
  Added a configuration option for the platforms without RTC. (_FS_NORTC)
  Fixed volume label created by Mac OS X cannot be retrieved with f_getlabel(). (appeared at R0.09b)
  Fixed a potential problem of FAT access that can appear on disk error.
  Fixed null pointer dereference on attempting to delete the root direcotry. (appeared at R0.08)

R0.10b (May 19, 2014)
  Fixed a hard error in the disk I/O layer can collapse the directory entry.
  Fixed LFN entry is not deleted on delete/rename an object with its lossy converted SFN. (appeared at R0.07)

R0.10a (January 15, 2014)
  Added arbitrary strings as drive number in the path name. (_STR_VOLUME_ID)
  Added an option for minimum sector size. (_MIN_SS)
  2nd argument of f_rename() can have a drive number and it will be ignored.
  Fixed f_mount() with forced mount fails when drive number is larger than 0. (appeared at R0.10)
  Fixed f_close() invalidates the file object without volume lock.
  Fixed volume lock is left acquired after return from f_closedir(). (appeared at R0.10)
  Fixed creation of a directory entry with LFN fails on too many SFN collisions. (appeared at R0.07)

R0.10 (October 2, 2013)
  Added an option for character encoding on the file. (_STRF_ENCODE)
  Added f_closedir().
  Added forced full FAT scan option for f_getfree(). (_FS_NOFSINFO)
  Added forced mount option with changes of f_mount().
  Improved behavior of volume auto detection.
  Improved write throughput of f_puts() and f_printf().
  Changed argument of f_chdrive(), f_mkfs(), disk_read() and disk_write().
  Fixed f_write() can be truncated when the file size is close to 4GB.
  Fixed f_open(), f_mkdir() and f_setlabel() can return incorrect result code on error.

R0.09b (January 24, 2013)
  Added f_getlabel() and f_setlabel(). (_USE_LABEL = 1)

R0.09a (August 27, 2012)
  Fixed assertion failure due to OS/2 EA on FAT12/16 volume.
  Changed file functions reject null object pointer to avoid crash.
  Changed option name _FS_SHARE to _FS_LOCK.

R0.09 (September 6, 2011)
  f_mkfs() supports multiple partition on a physical drive.
  Added f_fdisk(). (_MULTI_PARTITION = 2)

R0.08b (January 15, 2011)
  Fast seek function is also applied to f_read() and f_write().
  f_lseek() reports required table size on creating CLMP.
  Extended format syntax of f_printf().
  Ignores duplicated directory separators in given path names.

R0.08a (August 16, 2010)
  Added f_getcwd(). (_FS_RPATH = 2)
  Added sector erase function. (_USE_ERASE)
  Moved file lock semaphore table from fs object to the bss.
  Fixed a wrong directory entry is created on non-LFN cfg when the given name contains ';'.
  Fixed f_mkfs() creates wrong FAT32 volume.

R0.08 (May 15, 2010)
  Added a memory configuration option. (_USE_LFN)
  Added support of file lock. (_FS_SHARE)
  Added fast seek function. (_USE_FASTSEEK)
  Changed some types on the API, XCHAR->TCHAR.
  Changed fname member in the FILINFO structure on Unicode cfg.
  String functions support UTF-8 encoding files on Unicode cfg.

R0.07e (November 3, 2009)
  Separated out configuration options from ff.h to ffconf.h.
  Added a configuration option, _LFN_UNICODE.
  Fixed f_unlink() fails to remove a sub-dir on _FS_RPATH.
  Fixed name matching error on the 13 char boundary.
  Changed f_readdir() to return the SFN with always upper case on non-LFN cfg.

R0.07c (Junuary 21, 2009)
  Fixed f_unlink() may return FR_OK on error.
  Fixed wrong cache control in f_lseek().
  Added support of relative path.
  Added f_chdir().
  Added f_chdrive().
  Added proper case conversion to extended characters.

R0.07a (April 14, 2009)
  Separated out OS dependent code on re-entrant configuration.
  Added multiple sector size support.

R0.07 (April 1, 2009)
  Merged Tiny-FatFs into FatFs as a buffer configuration option.
  Added long file name support.
  Added multiple code page support.
  Added re-entrancy for multitask operation.
  Added auto cluster size selection to f_mkfs().
  Added rewind option to f_readdir().
  Changed result code of critical errors.
  Renamed string functions to avoid name collision.

R0.06 (April 1, 2008)
  Added f_forward. (Tiny-FatFs)
  Added string functions: fgets, fputc, fputs and fprintf.
  Improved performance of f_lseek on moving to the same or following cluster.

R0.05a (February 3, 2008)
  Added f_truncate.
  Added f_utime.
  Fixed off by one error at FAT sub-type determination.
  Fixed btr in f_read can be mistruncated.
  Fixed cached sector is left not flushed when create and close without write.

R0.05 (August 26, 2007)
  Changed arguments of f_read, f_write.
  Changed arguments of f_mkfs. (FatFs)
  Fixed f_mkfs on FAT32 creates incorrect FSInfo. (FatFs)
  Fixed f_mkdir on FAT32 creates incorrect directory. (FatFs)

R0.04b (May 5, 2007)
  Added _USE_NTFLAG option.
  Added FSInfo support.
  Fixed some problems corresponds to FAT32. (Tiny-FatFs)
  Fixed DBCS name can result FR_INVALID_NAME.
  Fixed short seek (<= csize) collapses the file object.

R0.04a (April 1, 2007)
  Supported multiple partitions on a plysical drive. (FatFs)
  Added minimization level 3.
  Added a capability of extending file size to f_lseek.
  Fixed an endian sensitive code in f_mkfs. (FatFs)
  Fixed a problem corresponds to FAT32 support. (Tiny-FatFs)

R0.04 (February 4, 2007)
  Supported multiple drive system. (FatFs)
  Changed some APIs for multiple drive system.
  Added f_mkfs. (FatFs)
  Added _USE_FAT32 option. (Tiny-FatFs)

R0.03a (December 11, 2006)
  Improved cluster scan algolithm to write files fast.
  Fixed f_mkdir creates incorrect directory on FAT32.

R0.03 (September 22, 2006)
  Added f_rename.
  Changed option _FS_MINIMUM to _FS_MINIMIZE.

R0.02a (June 10, 2006)
  Added a configuration option _FS_MINIMUM.

R0.02 (Jun 01, 2006)
  Added FAT12.
  Removed unbuffered mode.
  Fixed a problem on small (<32M) patition.

R0.01 (April 29, 2006)
  First release

R0.00 (February 26, 2006)
  Prototype (not released)

