<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html lang="ja">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta http-equiv="Content-Style-Type" content="text/css">
<link rel="up" title="FatFs" href="../00index_j.html">
<link rel="alternate" hreflang="en" title="English" href="../en/forward.html">
<link rel="stylesheet" href="../css_j.css" type="text/css" media="screen" title="ELM Default">
<title>FatFs - f_forward</title>
</head>

<body>

<div class="para func">
<h2>f_forward</h2>
<p>ファイルからデータを読み出し、送信ストリームに直接転送します。</p>
<pre>
FRESULT f_forward (
  FIL* <span class="arg">fp</span>,                         <span class="c">/* [IN] ファイル オブジェクト構造体 */</span>
  UINT (*<span class="arg">func</span>)(const BYTE*,UINT),  <span class="c">/* [IN] データ転送関数 */</span>
  UINT <span class="arg">btf</span>,                        <span class="c">/* [IN] 転送するバイト数 */</span>
  UINT* <span class="arg">bf</span>                         <span class="c">/* [OUT] 転送されたバイト数 */</span>
);
</pre>
</div>

<div class="para arg">
<h4>引数</h4>
<dl class="par">
<dt>fp</dt>
<dd>ファイル オブジェクト構造体へのポインタを指定します。</dd>
<dt>func</dt>
<dd>データを渡すユーザ定義関数へのポインタを指定します。この関数の仕様はサンプルを参照してください。</dd>
<dt>btf</dt>
<dd>転送するバイト数(0～UINTの最大値)を指定します。</dd>
<dt>bf</dt>
<dd>実際に転送されたバイト数を格納する変数を指すポインタを指定します。</dd>
</dl>
</div>


<div class="para ret">
<h4>戻り値</h4>
<p>
<a href="rc.html#ok">FR_OK</a>,
<a href="rc.html#de">FR_DISK_ERR</a>,
<a href="rc.html#ie">FR_INT_ERR</a>,
<a href="rc.html#de">FR_DENIED</a>,
<a href="rc.html#io">FR_INVALID_OBJECT</a>,
<a href="rc.html#tm">FR_TIMEOUT</a>
</p>
</div>


<div class="para desc">
<h4>解説</h4>
<p>ファイルのデータをバッファに読み出さずに送信ストリームに直接転送します。アプリケーション側でデータ バッファを必要としないので、メモリの限られた環境で有効です。リード/ライト ポインタは転送されたバイト数だけ進みます。指定されたバイト数の転送中にファイルの終端に達した場合や送信ストリームがビジーになった場合、<tt class="arg">*bf</tt>は<tt class="arg">btf</tt>よりも小さくなります。</p>
</div>


<div class="para comp">
<h4>対応情報</h4>
<p><tt>_USE_FORWARD == 1</tt>のときに使用可能です。</p>
</div>


<div class="para use">
<h4>使用例（オーディオ再生）</h4>
<pre>
<span class="c">/*-----------------------------------------------------------------------*/</span>
<span class="c">/* f_forward関数から呼ばれるデータ送信関数の例                           */</span>
<span class="c">/*-----------------------------------------------------------------------*/</span>

UINT out_stream (   <span class="c">/* 戻り値: 転送されたバイト数またはストリームの状態 */</span>
    const BYTE *p,  <span class="c">/* 転送するデータを指すポインタ */</span>
    UINT btf        <span class="c">/* &gt;0: 転送を行う(バイト数). 0: ストリームの状態を調べる */</span>
)
{
    UINT cnt = 0;


    if (btf == 0) {     <span class="c">/* センス要求 */</span>
        <span class="c">/* ストリームの状態を返す (0: ビジー, 1: レディ) */</span>
        <span class="c">/* 一旦、レディを返したら、続く転送要求で少なくとも1バイトは */</span>
        <span class="c">/* 転送されないと f_forward関数は FR_INT_ERR となる。 */</span>
        if (FIFO_READY) cnt = 1;
    }
    else {              <span class="c">/* 転送要求 */</span>
        do {    <span class="c">/* 全てのバイトを転送するか、ストリームがビジーになるまで繰り返す */</span>
            FIFO_PORT = *p++;
            cnt++;
        } while (cnt &lt; btf &amp;&amp; FIFO_READY);
    }

    return cnt;
}


<span class="c">/*-----------------------------------------------------------------------*/</span>
<span class="c">/* f_forward関数の使用例                                                 */</span>
<span class="c">/*-----------------------------------------------------------------------*/</span>

FRESULT play_file (
    char *fn        <span class="c">/* 再生するオーディオ ファイル名を指すポインタ */</span>
)
{
    FRESULT rc;
    FIL fil;
    UINT dmy;

    <span class="c">/* ファイルを読み出しモードで開く */</span>
    rc = f_open(&amp;fil, fn, FA_READ);
    if (rc) return rc;

    <span class="c">/* 全てのデータが転送されるかエラーが発生するまで続ける */</span>
    while (rc == FR_OK &amp;&amp; !f_eof(&amp;fil)) {

        <span class="c">/* ほかの処理... */</span>

        <span class="c">/* 定期的または要求に応じてデータをストリームに送出する */</span>
        rc = f_forward(&amp;fil, out_stream, 1000, &amp;dmy);
    }

    <span class="c">/* ファイルを閉じて戻る */</span>
    f_close(&amp;fil);
    return rc;
}
</pre>
</div>


<div class="para ref">
<h4>参照</h4>
<p><tt><a href="open.html">f_open</a>, <a href="gets.html">fgets</a>, <a href="write.html">f_write</a>, <a href="close.html">f_close</a>, <a href="sfile.html">FIL</a></tt></p>
</div>

<p class="foot"><a href="../00index_j.html">戻る</a></p>
</body>
</html>
