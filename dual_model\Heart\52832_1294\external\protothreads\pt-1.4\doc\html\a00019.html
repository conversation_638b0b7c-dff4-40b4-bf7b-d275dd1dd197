<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=iso-8859-1">
<title>The Protothreads Library 1.4: lc-switch.h Source File</title>
<link href="doxygen.css" rel="stylesheet" type="text/css">
<link href="tabs.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.4.6 -->
<div class="tabs">
  <ul>
    <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
    <li><a href="modules.html"><span>Modules</span></a></li>
    <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    <li id="current"><a href="files.html"><span>Files</span></a></li>
  </ul></div>
<div class="tabs">
  <ul>
    <li><a href="files.html"><span>File&nbsp;List</span></a></li>
    <li><a href="globals.html"><span>Globals</span></a></li>
  </ul></div>
<h1>lc-switch.h</h1><a href="a00010.html">Go to the documentation of this file.</a><div class="fragment"><pre class="fragment"><a name="l00001"></a>00001 <span class="comment">/*</span>
<a name="l00002"></a>00002 <span class="comment"> * Copyright (c) 2004-2005, Swedish Institute of Computer Science.</span>
<a name="l00003"></a>00003 <span class="comment"> * All rights reserved.</span>
<a name="l00004"></a>00004 <span class="comment"> *</span>
<a name="l00005"></a>00005 <span class="comment"> * Redistribution and use in source and binary forms, with or without</span>
<a name="l00006"></a>00006 <span class="comment"> * modification, are permitted provided that the following conditions</span>
<a name="l00007"></a>00007 <span class="comment"> * are met:</span>
<a name="l00008"></a>00008 <span class="comment"> * 1. Redistributions of source code must retain the above copyright</span>
<a name="l00009"></a>00009 <span class="comment"> *    notice, this list of conditions and the following disclaimer.</span>
<a name="l00010"></a>00010 <span class="comment"> * 2. Redistributions in binary form must reproduce the above copyright</span>
<a name="l00011"></a>00011 <span class="comment"> *    notice, this list of conditions and the following disclaimer in the</span>
<a name="l00012"></a>00012 <span class="comment"> *    documentation and/or other materials provided with the distribution.</span>
<a name="l00013"></a>00013 <span class="comment"> * 3. Neither the name of the Institute nor the names of its contributors</span>
<a name="l00014"></a>00014 <span class="comment"> *    may be used to endorse or promote products derived from this software</span>
<a name="l00015"></a>00015 <span class="comment"> *    without specific prior written permission.</span>
<a name="l00016"></a>00016 <span class="comment"> *</span>
<a name="l00017"></a>00017 <span class="comment"> * THIS SOFTWARE IS PROVIDED BY THE INSTITUTE AND CONTRIBUTORS ``AS IS'' AND</span>
<a name="l00018"></a>00018 <span class="comment"> * ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE</span>
<a name="l00019"></a>00019 <span class="comment"> * IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE</span>
<a name="l00020"></a>00020 <span class="comment"> * ARE DISCLAIMED.  IN NO EVENT SHALL THE INSTITUTE OR CONTRIBUTORS BE LIABLE</span>
<a name="l00021"></a>00021 <span class="comment"> * FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL</span>
<a name="l00022"></a>00022 <span class="comment"> * DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS</span>
<a name="l00023"></a>00023 <span class="comment"> * OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)</span>
<a name="l00024"></a>00024 <span class="comment"> * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT</span>
<a name="l00025"></a>00025 <span class="comment"> * LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY</span>
<a name="l00026"></a>00026 <span class="comment"> * OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF</span>
<a name="l00027"></a>00027 <span class="comment"> * SUCH DAMAGE.</span>
<a name="l00028"></a>00028 <span class="comment"> *</span>
<a name="l00029"></a>00029 <span class="comment"> * This file is part of the Contiki operating system.</span>
<a name="l00030"></a>00030 <span class="comment"> *</span>
<a name="l00031"></a>00031 <span class="comment"> * Author: Adam Dunkels &lt;<EMAIL>&gt;</span>
<a name="l00032"></a>00032 <span class="comment"> *</span>
<a name="l00033"></a>00033 <span class="comment"> * $Id: lc-switch.h,v 1.4 2006/06/03 11:29:43 adam Exp $</span>
<a name="l00034"></a>00034 <span class="comment"> */</span>
<a name="l00035"></a>00035 <span class="comment"></span>
<a name="l00036"></a>00036 <span class="comment">/**</span>
<a name="l00037"></a>00037 <span class="comment"> * \addtogroup lc</span>
<a name="l00038"></a>00038 <span class="comment"> * @{</span>
<a name="l00039"></a>00039 <span class="comment"> */</span>
<a name="l00040"></a>00040 <span class="comment"></span>
<a name="l00041"></a>00041 <span class="comment">/**</span>
<a name="l00042"></a>00042 <span class="comment"> * \file</span>
<a name="l00043"></a>00043 <span class="comment"> * Implementation of local continuations based on switch() statment</span>
<a name="l00044"></a>00044 <span class="comment"> * \author Adam Dunkels &lt;<EMAIL>&gt;</span>
<a name="l00045"></a>00045 <span class="comment"> *</span>
<a name="l00046"></a>00046 <span class="comment"> * This implementation of local continuations uses the C switch()</span>
<a name="l00047"></a>00047 <span class="comment"> * statement to resume execution of a function somewhere inside the</span>
<a name="l00048"></a>00048 <span class="comment"> * function's body. The implementation is based on the fact that</span>
<a name="l00049"></a>00049 <span class="comment"> * switch() statements are able to jump directly into the bodies of</span>
<a name="l00050"></a>00050 <span class="comment"> * control structures such as if() or while() statmenets.</span>
<a name="l00051"></a>00051 <span class="comment"> *</span>
<a name="l00052"></a>00052 <span class="comment"> * This implementation borrows heavily from Simon Tatham's coroutines</span>
<a name="l00053"></a>00053 <span class="comment"> * implementation in C:</span>
<a name="l00054"></a>00054 <span class="comment"> * http://www.chiark.greenend.org.uk/~sgtatham/coroutines.html</span>
<a name="l00055"></a>00055 <span class="comment"> */</span>
<a name="l00056"></a>00056 
<a name="l00057"></a>00057 <span class="preprocessor">#ifndef __LC_SWITCH_H__</span>
<a name="l00058"></a>00058 <span class="preprocessor"></span><span class="preprocessor">#define __LC_SWITCH_H__</span>
<a name="l00059"></a>00059 <span class="preprocessor"></span>
<a name="l00060"></a>00060 <span class="comment">/* WARNING! lc implementation using switch() does not work if an</span>
<a name="l00061"></a>00061 <span class="comment">   LC_SET() is done within another switch() statement! */</span>
<a name="l00062"></a>00062 <span class="comment"></span>
<a name="l00063"></a>00063 <span class="comment">/** \hideinitializer */</span>
<a name="l00064"></a><a class="code" href="a00017.html#gfad6704adb116cc16edb80f744e7239d">00064</a> <span class="keyword">typedef</span> <span class="keywordtype">unsigned</span> <span class="keywordtype">short</span> <a class="code" href="a00017.html#gfad6704adb116cc16edb80f744e7239d">lc_t</a>;
<a name="l00065"></a>00065 
<a name="l00066"></a><a class="code" href="a00017.html#g2c1bb4fa6d7a6ff951a41c73fc721109">00066</a> <span class="preprocessor">#define LC_INIT(s) s = 0;</span>
<a name="l00067"></a>00067 <span class="preprocessor"></span>
<a name="l00068"></a><a class="code" href="a00017.html#g1ec8b8f4710dce1fa7fb87d3a31541ae">00068</a> <span class="preprocessor">#define LC_RESUME(s) switch(s) { case 0:</span>
<a name="l00069"></a>00069 <span class="preprocessor"></span>
<a name="l00070"></a><a class="code" href="a00017.html#gd8eec328a4868d767f0c00c8d1c6cfc1">00070</a> <span class="preprocessor">#define LC_SET(s) s = __LINE__; case __LINE__:</span>
<a name="l00071"></a>00071 <span class="preprocessor"></span>
<a name="l00072"></a><a class="code" href="a00017.html#gca51ceb2f5d855dfde55bcedf8d3b92d">00072</a> <span class="preprocessor">#define LC_END(s) }</span>
<a name="l00073"></a>00073 <span class="preprocessor"></span>
<a name="l00074"></a>00074 <span class="preprocessor">#endif </span><span class="comment">/* __LC_SWITCH_H__ */</span>
<a name="l00075"></a>00075 <span class="comment"></span>
<a name="l00076"></a>00076 <span class="comment">/** @} */</span>
</pre></div><hr size="1"><address style="align: right;"><small>Generated on Mon Oct 2 10:06:29 2006 for The Protothreads Library 1.4 by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.4.6 </small></address>
</body>
</html>
