<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html lang="en">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<meta http-equiv="Content-Style-Type" content="text/css">
<link rel="up" title="FatFs" href="../00index_e.html">
<link rel="alternate" hreflang="ja" title="Japanese" href="../ja/chmod.html">
<link rel="stylesheet" href="../css_e.css" type="text/css" media="screen" title="ELM Default">
<title>FatFs - f_chmod</title>
</head>

<body>

<div class="para func">
<h2>f_chmod</h2>
<p>The f_chmod function changes the attribute of a file or sub-directory.</p>
<pre>
FRESULT f_chmod (
  const TCHAR* <span class="arg">path</span>, <span class="c">/* [IN] Object name */</span>
  BYTE <span class="arg">attr</span>,         <span class="c">/* [IN] Attribute flags */</span>
  BYTE <span class="arg">mask</span>          <span class="c">/* [IN] Attribute masks */</span>
);
</pre>
</div>

<div class="para arg">
<h4>Parameters</h4>
<dl class="par">
<dt>path</dt>
<dd>Pointer to the null-terminated string that specifies an <a href="filename.html">object</a> to be changed</dd>
<dt>attr</dt>
<dd>Attribute flags to be set in one or more combination of the following flags. The specified flags are set and others are cleard.<br>
<table class="lst">
<tr><th>Attribute</th><th>Description</th></tr>
<tr><td>AM_RDO</td><td>Read only</td></tr>
<tr><td>AM_ARC</td><td>Archive</td></tr>
<tr><td>AM_SYS</td><td>System</td></tr>
<tr><td>AM_HID</td><td>Hidden</td></tr>
</table>
</dd>
<dt>mask</dt>
<dd>Attribute mask that specifies which attribute is changed. The specified attributes are set or cleard and others are left unchanged.</dd>
</dl>
</div>


<div class="para ret">
<h4>Return Values</h4>
<p>
<a href="rc.html#ok">FR_OK</a>,
<a href="rc.html#de">FR_DISK_ERR</a>,
<a href="rc.html#ie">FR_INT_ERR</a>,
<a href="rc.html#nr">FR_NOT_READY</a>,
<a href="rc.html#ok">FR_NO_FILE</a>,
<a href="rc.html#np">FR_NO_PATH</a>,
<a href="rc.html#in">FR_INVALID_NAME</a>,
<a href="rc.html#wp">FR_WRITE_PROTECTED</a>,
<a href="rc.html#id">FR_INVALID_DRIVE</a>,
<a href="rc.html#ne">FR_NOT_ENABLED</a>,
<a href="rc.html#ns">FR_NO_FILESYSTEM</a>,
<a href="rc.html#tm">FR_TIMEOUT</a>,
<a href="rc.html#nc">FR_NOT_ENOUGH_CORE</a>
</p>
</div>


<div class="para desc">
<h4>Description</h4>
<p>The <tt>f_chmod</tt> function changes the attribute of a file or sub-directory.</p>
</div>


<div class="para comp">
<h4>QuickInfo</h4>
<p>Available when <tt>_FS_READONLY == 0</tt> and <tt>_USE_CHMOD == 1</tt>.</p>
</div>


<div class="para use">
<h4>Example</h4>
<pre>
    <span class="c">/* Set read-only flag, clear archive flag and others are left unchanged. */</span>
    f_chmod("file.txt", AM_RDO, AM_RDO | AM_ARC);
</pre>
</div>

<p class="foot"><a href="../00index_e.html">Return</a></p>
</body>
</html>
