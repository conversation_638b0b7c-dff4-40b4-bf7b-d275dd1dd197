.\_build\nrf_drv_uart.o: ..\..\..\..\..\..\integration\nrfx\legacy\nrf_drv_uart.c
.\_build\nrf_drv_uart.o: ..\..\..\..\..\..\integration\nrfx\legacy\nrf_drv_uart.h
.\_build\nrf_drv_uart.o: ..\..\..\..\..\..\modules\nrfx\nrfx.h
.\_build\nrf_drv_uart.o: ..\..\..\..\..\..\integration\nrfx\nrfx_config.h
.\_build\nrf_drv_uart.o: ..\config\sdk_config.h
.\_build\nrf_drv_uart.o: ..\..\..\..\..\..\modules\nrfx\drivers/nrfx_common.h
.\_build\nrf_drv_uart.o: D:\RJ\MDK\ARM\ARMCC\Bin\..\include\stdint.h
.\_build\nrf_drv_uart.o: D:\RJ\MDK\ARM\ARMCC\Bin\..\include\stddef.h
.\_build\nrf_drv_uart.o: D:\RJ\MDK\ARM\ARMCC\Bin\..\include\stdbool.h
.\_build\nrf_drv_uart.o: D:\RJ\MDK\Packs\NordicSemiconductor\nRF_DeviceFamilyPack\8.32.1\Device\Include\nrf.h
.\_build\nrf_drv_uart.o: D:\RJ\MDK\Packs\NordicSemiconductor\nRF_DeviceFamilyPack\8.32.1\Device\Include\nrf52.h
.\_build\nrf_drv_uart.o: D:\RJ\MDK\Packs\ARM\CMSIS\5.6.0\CMSIS\Core\Include\core_cm4.h
.\_build\nrf_drv_uart.o: D:\RJ\MDK\Packs\ARM\CMSIS\5.6.0\CMSIS\Core\Include\cmsis_version.h
.\_build\nrf_drv_uart.o: D:\RJ\MDK\Packs\ARM\CMSIS\5.6.0\CMSIS\Core\Include\cmsis_compiler.h
.\_build\nrf_drv_uart.o: D:\RJ\MDK\Packs\ARM\CMSIS\5.6.0\CMSIS\Core\Include\cmsis_armcc.h
.\_build\nrf_drv_uart.o: D:\RJ\MDK\Packs\ARM\CMSIS\5.6.0\CMSIS\Core\Include\mpu_armv7.h
.\_build\nrf_drv_uart.o: D:\RJ\MDK\Packs\NordicSemiconductor\nRF_DeviceFamilyPack\8.32.1\Device\Include\system_nrf52.h
.\_build\nrf_drv_uart.o: D:\RJ\MDK\Packs\NordicSemiconductor\nRF_DeviceFamilyPack\8.32.1\Device\Include\nrf52_bitfields.h
.\_build\nrf_drv_uart.o: D:\RJ\MDK\Packs\NordicSemiconductor\nRF_DeviceFamilyPack\8.32.1\Device\Include\nrf51_to_nrf52.h
.\_build\nrf_drv_uart.o: D:\RJ\MDK\Packs\NordicSemiconductor\nRF_DeviceFamilyPack\8.32.1\Device\Include\nrf52_name_change.h
.\_build\nrf_drv_uart.o: D:\RJ\MDK\Packs\NordicSemiconductor\nRF_DeviceFamilyPack\8.32.1\Device\Include\compiler_abstraction.h
.\_build\nrf_drv_uart.o: D:\RJ\MDK\Packs\NordicSemiconductor\nRF_DeviceFamilyPack\8.32.1\Device\Include\nrf_peripherals.h
.\_build\nrf_drv_uart.o: D:\RJ\MDK\Packs\NordicSemiconductor\nRF_DeviceFamilyPack\8.32.1\Device\Include\nrf52832_peripherals.h
.\_build\nrf_drv_uart.o: ..\..\..\..\..\..\integration\nrfx\nrfx_glue.h
.\_build\nrf_drv_uart.o: ..\..\..\..\..\..\integration\nrfx\legacy/apply_old_config.h
.\_build\nrf_drv_uart.o: ..\..\..\..\..\..\modules\nrfx\soc/nrfx_irqs.h
.\_build\nrf_drv_uart.o: ..\..\..\..\..\..\modules\nrfx\soc/nrfx_irqs_nrf52832.h
.\_build\nrf_drv_uart.o: ..\..\..\..\..\..\components\libraries\util\nrf_assert.h
.\_build\nrf_drv_uart.o: ..\..\..\..\..\..\components\libraries\util\app_util.h
.\_build\nrf_drv_uart.o: ..\..\..\..\..\..\components\libraries\util\nordic_common.h
.\_build\nrf_drv_uart.o: ..\..\..\..\..\..\components\softdevice\s132\headers\nrf52\nrf_mbr.h
.\_build\nrf_drv_uart.o: ..\..\..\..\..\..\components\softdevice\s132\headers\nrf_svc.h
.\_build\nrf_drv_uart.o: ..\..\..\..\..\..\components\libraries\util\app_util_platform.h
.\_build\nrf_drv_uart.o: ..\..\..\..\..\..\components\softdevice\s132\headers\nrf_soc.h
.\_build\nrf_drv_uart.o: ..\..\..\..\..\..\components\softdevice\s132\headers\nrf_error.h
.\_build\nrf_drv_uart.o: ..\..\..\..\..\..\components\softdevice\s132\headers\nrf_error_soc.h
.\_build\nrf_drv_uart.o: ..\..\..\..\..\..\components\softdevice\s132\headers\nrf_nvic.h
.\_build\nrf_drv_uart.o: ..\..\..\..\..\..\components\libraries\util\app_error.h
.\_build\nrf_drv_uart.o: D:\RJ\MDK\ARM\ARMCC\Bin\..\include\stdio.h
.\_build\nrf_drv_uart.o: ..\..\..\..\..\..\components\libraries\util\sdk_errors.h
.\_build\nrf_drv_uart.o: ..\..\..\..\..\..\components\libraries\util\app_error_weak.h
.\_build\nrf_drv_uart.o: ..\..\..\..\..\..\modules\nrfx\soc/nrfx_coredep.h
.\_build\nrf_drv_uart.o: ..\..\..\..\..\..\modules\nrfx\soc/nrfx_atomic.h
.\_build\nrf_drv_uart.o: ..\..\..\..\..\..\modules\nrfx\nrfx.h
.\_build\nrf_drv_uart.o: ..\..\..\..\..\..\components\libraries\util\sdk_resources.h
.\_build\nrf_drv_uart.o: ..\..\..\..\..\..\components\softdevice\s132\headers\nrf_sd_def.h
.\_build\nrf_drv_uart.o: ..\..\..\..\..\..\modules\nrfx\drivers/nrfx_errors.h
.\_build\nrf_drv_uart.o: ..\..\..\..\..\..\modules\nrfx\drivers\include\nrfx_uarte.h
.\_build\nrf_drv_uart.o: ..\..\..\..\..\..\modules\nrfx\hal/nrf_uarte.h
.\_build\nrf_drv_uart.o: ..\..\..\..\..\..\modules\nrfx\drivers\include\nrfx_uart.h
.\_build\nrf_drv_uart.o: ..\..\..\..\..\..\modules\nrfx\hal/nrf_uart.h
