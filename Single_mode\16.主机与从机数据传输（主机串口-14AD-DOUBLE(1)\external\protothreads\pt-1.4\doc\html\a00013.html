<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=iso-8859-1">
<title>The Protothreads Library 1.4: pt.h File Reference</title>
<link href="doxygen.css" rel="stylesheet" type="text/css">
<link href="tabs.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.4.6 -->
<div class="tabs">
  <ul>
    <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
    <li><a href="modules.html"><span>Modules</span></a></li>
    <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    <li id="current"><a href="files.html"><span>Files</span></a></li>
  </ul></div>
<div class="tabs">
  <ul>
    <li><a href="files.html"><span>File&nbsp;List</span></a></li>
    <li><a href="globals.html"><span>Globals</span></a></li>
  </ul></div>
<h1>pt.h File Reference</h1><hr><a name="_details"></a><h2>Detailed Description</h2>
Protothreads implementation. 
<p>
<dl compact><dt><b>Author:</b></dt><dd>Adam Dunkels &lt;<a href="mailto:<EMAIL>"><EMAIL></a>&gt; </dd></dl>

<p>
Definition in file <a class="el" href="a00022.html">pt.h</a>.
<p>
<code>#include &quot;<a class="el" href="a00020.html">lc.h</a>&quot;</code><br>

<p>
<a href="a00022.html">Go to the source code of this file.</a><table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Data Structures</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00005.html">pt</a></td></tr>

<tr><td colspan="2"><br><h2>Initialization</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">#define&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00014.html#ge6bae7dc0225468c8a5ac269df549892">PT_INIT</a>(<a class="el" href="a00005.html">pt</a>)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Initialize a protothread.  <a href="a00014.html#ge6bae7dc0225468c8a5ac269df549892"></a><br></td></tr>
<tr><td colspan="2"><br><h2>Declaration and definition</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">#define&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00014.html#g3d4c8bd4aada659eb34f5d2ffd3e7901">PT_THREAD</a>(name_args)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Declaration of a protothread.  <a href="a00014.html#g3d4c8bd4aada659eb34f5d2ffd3e7901"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">#define&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00014.html#g2ffbb9e554e08a343ae2f9de4bedfdfc">PT_BEGIN</a>(<a class="el" href="a00005.html">pt</a>)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Declare the start of a protothread inside the C function implementing the protothread.  <a href="a00014.html#g2ffbb9e554e08a343ae2f9de4bedfdfc"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">#define&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00014.html#g7b04a0035bef29d905496c23bae066d2">PT_END</a>(<a class="el" href="a00005.html">pt</a>)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Declare the end of a protothread.  <a href="a00014.html#g7b04a0035bef29d905496c23bae066d2"></a><br></td></tr>
<tr><td colspan="2"><br><h2>Blocked wait</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">#define&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00014.html#g99e43010ec61327164466aa2d902de45">PT_WAIT_UNTIL</a>(<a class="el" href="a00005.html">pt</a>, condition)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Block and wait until condition is true.  <a href="a00014.html#g99e43010ec61327164466aa2d902de45"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">#define&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00014.html#gad14bbbf092b90aa0a5a4f9169504a8d">PT_WAIT_WHILE</a>(<a class="el" href="a00005.html">pt</a>, cond)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Block and wait while condition is true.  <a href="a00014.html#gad14bbbf092b90aa0a5a4f9169504a8d"></a><br></td></tr>
<tr><td colspan="2"><br><h2>Hierarchical protothreads</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">#define&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00014.html#g2f8f70c30b9ee08a103fbd69a4365c4c">PT_WAIT_THREAD</a>(<a class="el" href="a00005.html">pt</a>, thread)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Block and wait until a child protothread completes.  <a href="a00014.html#g2f8f70c30b9ee08a103fbd69a4365c4c"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">#define&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00014.html#g9e97a0b4d5cc7764d8e19758f5da53ae">PT_SPAWN</a>(<a class="el" href="a00005.html">pt</a>, child, thread)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Spawn a child protothread and wait until it exits.  <a href="a00014.html#g9e97a0b4d5cc7764d8e19758f5da53ae"></a><br></td></tr>
<tr><td colspan="2"><br><h2>Exiting and restarting</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">#define&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00014.html#gcd3ac045f0a4ae63412e3b3d8780e8ab">PT_RESTART</a>(<a class="el" href="a00005.html">pt</a>)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Restart the protothread.  <a href="a00014.html#gcd3ac045f0a4ae63412e3b3d8780e8ab"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">#define&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00014.html#g905451249dca72ce0385bf2a9ff178ee">PT_EXIT</a>(<a class="el" href="a00005.html">pt</a>)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Exit the protothread.  <a href="a00014.html#g905451249dca72ce0385bf2a9ff178ee"></a><br></td></tr>
<tr><td colspan="2"><br><h2>Calling a protothread</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">#define&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00014.html#gfa82b860a64b67d25ab3abc21811896f">PT_SCHEDULE</a>(f)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Schedule a protothread.  <a href="a00014.html#gfa82b860a64b67d25ab3abc21811896f"></a><br></td></tr>
<tr><td colspan="2"><br><h2>Yielding from a protothread</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">#define&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00014.html#g155cba6121323726d02c00284428fed6">PT_YIELD</a>(<a class="el" href="a00005.html">pt</a>)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Yield from the current protothread.  <a href="a00014.html#g155cba6121323726d02c00284428fed6"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">#define&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00014.html#ge3c821e3a388615528efda9d23c7d115">PT_YIELD_UNTIL</a>(<a class="el" href="a00005.html">pt</a>, cond)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Yield from the protothread until a condition occurs.  <a href="a00014.html#ge3c821e3a388615528efda9d23c7d115"></a><br></td></tr>
<tr><td colspan="2"><br><h2>Defines</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="anchor" name="g7b5319b5b65761a845fcd1500fde4cdc"></a><!-- doxytag: member="pt.h::PT_WAITING" ref="g7b5319b5b65761a845fcd1500fde4cdc" args="" -->
#define&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00014.html#g7b5319b5b65761a845fcd1500fde4cdc">PT_WAITING</a>&nbsp;&nbsp;&nbsp;0</td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="anchor" name="ge469332907e0617d72d5e2dd4297119d"></a><!-- doxytag: member="pt.h::PT_YIELDED" ref="ge469332907e0617d72d5e2dd4297119d" args="" -->
#define&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00014.html#ge469332907e0617d72d5e2dd4297119d">PT_YIELDED</a>&nbsp;&nbsp;&nbsp;1</td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="anchor" name="gcfae9053e5c107a1aed6b228c917d2ea"></a><!-- doxytag: member="pt.h::PT_EXITED" ref="gcfae9053e5c107a1aed6b228c917d2ea" args="" -->
#define&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00014.html#gcfae9053e5c107a1aed6b228c917d2ea">PT_EXITED</a>&nbsp;&nbsp;&nbsp;2</td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="anchor" name="g9ff1e8936a8a26bff54c05f8a989b93b"></a><!-- doxytag: member="pt.h::PT_ENDED" ref="g9ff1e8936a8a26bff54c05f8a989b93b" args="" -->
#define&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00014.html#g9ff1e8936a8a26bff54c05f8a989b93b">PT_ENDED</a>&nbsp;&nbsp;&nbsp;3</td></tr>

</table>
<hr size="1"><address style="align: right;"><small>Generated on Mon Oct 2 10:06:29 2006 for The Protothreads Library 1.4 by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.4.6 </small></address>
</body>
</html>
