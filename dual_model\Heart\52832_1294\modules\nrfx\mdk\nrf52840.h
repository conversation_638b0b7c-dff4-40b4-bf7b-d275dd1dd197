/*
 * Copyright (c) 2010 - 2020, Nordic Semiconductor ASA
 * 
 * All rights reserved.
 * 
 * Redistribution and use in source and binary forms, with or without modification,
 * are permitted provided that the following conditions are met:
 * 
 * 1. Redistributions of source code must retain the above copyright notice, this
 * list of conditions and the following disclaimer.
 * 
 * 2. Redistributions in binary form, except as embedded into a Nordic
 * Semiconductor ASA integrated circuit in a product or a software update for
 * such product, must reproduce the above copyright notice, this list of
 * conditions and the following disclaimer in the documentation and/or other
 * materials provided with the distribution.
 * 
 * 3. Neither the name of Nordic Semiconductor ASA nor the names of its
 * contributors may be used to endorse or promote products derived from this
 * software without specific prior written permission.
 * 
 * 4. This software, with or without modification, must only be used with a
 * Nordic Semiconductor ASA integrated circuit.
 * 
 * 5. Any software provided in binary form under this license must not be reverse
 * engineered, decompiled, modified and/or disassembled.
 * 
 * THIS SOFTWARE IS PROVIDED BY NORDIC SEMICONDUCTOR ASA "AS IS" AND ANY EXPRESS
 * OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY, NONINFRINGEMENT, AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL NORDIC SEMICONDUCTOR ASA OR CONTRIBUTORS BE
 * LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
 * CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE
 * GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
 * LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT
 * OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 * @file     nrf52840.h
 * @brief    CMSIS HeaderFile
 * @version  1
 * @date     04. March 2020
 * @note     Generated by SVDConv V3.3.25 on Wednesday, 04.03.2020 14:59:25
 *           from File 'nrf52840.svd',
 *           last modified on Wednesday, 04.03.2020 13:59:17
 */



/** @addtogroup Nordic Semiconductor
  * @{
  */


/** @addtogroup nrf52840
  * @{
  */


#ifndef NRF52840_H
#define NRF52840_H

#ifdef __cplusplus
extern "C" {
#endif


/** @addtogroup Configuration_of_CMSIS
  * @{
  */



/* =========================================================================================================================== */
/* ================                                Interrupt Number Definition                                ================ */
/* =========================================================================================================================== */

typedef enum {
/* =======================================  ARM Cortex-M4 Specific Interrupt Numbers  ======================================== */
  Reset_IRQn                = -15,              /*!< -15  Reset Vector, invoked on Power up and warm reset                     */
  NonMaskableInt_IRQn       = -14,              /*!< -14  Non maskable Interrupt, cannot be stopped or preempted               */
  HardFault_IRQn            = -13,              /*!< -13  Hard Fault, all classes of Fault                                     */
  MemoryManagement_IRQn     = -12,              /*!< -12  Memory Management, MPU mismatch, including Access Violation
                                                     and No Match                                                              */
  BusFault_IRQn             = -11,              /*!< -11  Bus Fault, Pre-Fetch-, Memory Access Fault, other address/memory
                                                     related Fault                                                             */
  UsageFault_IRQn           = -10,              /*!< -10  Usage Fault, i.e. Undef Instruction, Illegal State Transition        */
  SVCall_IRQn               =  -5,              /*!< -5 System Service Call via SVC instruction                                */
  DebugMonitor_IRQn         =  -4,              /*!< -4 Debug Monitor                                                          */
  PendSV_IRQn               =  -2,              /*!< -2 Pendable request for system service                                    */
  SysTick_IRQn              =  -1,              /*!< -1 System Tick Timer                                                      */
/* ==========================================  nrf52840 Specific Interrupt Numbers  ========================================== */
  POWER_CLOCK_IRQn          =   0,              /*!< 0  POWER_CLOCK                                                            */
  RADIO_IRQn                =   1,              /*!< 1  RADIO                                                                  */
  UARTE0_UART0_IRQn         =   2,              /*!< 2  UARTE0_UART0                                                           */
  SPIM0_SPIS0_TWIM0_TWIS0_SPI0_TWI0_IRQn=   3,  /*!< 3  SPIM0_SPIS0_TWIM0_TWIS0_SPI0_TWI0                                      */
  SPIM1_SPIS1_TWIM1_TWIS1_SPI1_TWI1_IRQn=   4,  /*!< 4  SPIM1_SPIS1_TWIM1_TWIS1_SPI1_TWI1                                      */
  NFCT_IRQn                 =   5,              /*!< 5  NFCT                                                                   */
  GPIOTE_IRQn               =   6,              /*!< 6  GPIOTE                                                                 */
  SAADC_IRQn                =   7,              /*!< 7  SAADC                                                                  */
  TIMER0_IRQn               =   8,              /*!< 8  TIMER0                                                                 */
  TIMER1_IRQn               =   9,              /*!< 9  TIMER1                                                                 */
  TIMER2_IRQn               =  10,              /*!< 10 TIMER2                                                                 */
  RTC0_IRQn                 =  11,              /*!< 11 RTC0                                                                   */
  TEMP_IRQn                 =  12,              /*!< 12 TEMP                                                                   */
  RNG_IRQn                  =  13,              /*!< 13 RNG                                                                    */
  ECB_IRQn                  =  14,              /*!< 14 ECB                                                                    */
  CCM_AAR_IRQn              =  15,              /*!< 15 CCM_AAR                                                                */
  WDT_IRQn                  =  16,              /*!< 16 WDT                                                                    */
  RTC1_IRQn                 =  17,              /*!< 17 RTC1                                                                   */
  QDEC_IRQn                 =  18,              /*!< 18 QDEC                                                                   */
  COMP_LPCOMP_IRQn          =  19,              /*!< 19 COMP_LPCOMP                                                            */
  SWI0_EGU0_IRQn            =  20,              /*!< 20 SWI0_EGU0                                                              */
  SWI1_EGU1_IRQn            =  21,              /*!< 21 SWI1_EGU1                                                              */
  SWI2_EGU2_IRQn            =  22,              /*!< 22 SWI2_EGU2                                                              */
  SWI3_EGU3_IRQn            =  23,              /*!< 23 SWI3_EGU3                                                              */
  SWI4_EGU4_IRQn            =  24,              /*!< 24 SWI4_EGU4                                                              */
  SWI5_EGU5_IRQn            =  25,              /*!< 25 SWI5_EGU5                                                              */
  TIMER3_IRQn               =  26,              /*!< 26 TIMER3                                                                 */
  TIMER4_IRQn               =  27,              /*!< 27 TIMER4                                                                 */
  PWM0_IRQn                 =  28,              /*!< 28 PWM0                                                                   */
  PDM_IRQn                  =  29,              /*!< 29 PDM                                                                    */
  MWU_IRQn                  =  32,              /*!< 32 MWU                                                                    */
  PWM1_IRQn                 =  33,              /*!< 33 PWM1                                                                   */
  PWM2_IRQn                 =  34,              /*!< 34 PWM2                                                                   */
  SPIM2_SPIS2_SPI2_IRQn     =  35,              /*!< 35 SPIM2_SPIS2_SPI2                                                       */
  RTC2_IRQn                 =  36,              /*!< 36 RTC2                                                                   */
  I2S_IRQn                  =  37,              /*!< 37 I2S                                                                    */
  FPU_IRQn                  =  38,              /*!< 38 FPU                                                                    */
  USBD_IRQn                 =  39,              /*!< 39 USBD                                                                   */
  UARTE1_IRQn               =  40,              /*!< 40 UARTE1                                                                 */
  QSPI_IRQn                 =  41,              /*!< 41 QSPI                                                                   */
  CRYPTOCELL_IRQn           =  42,              /*!< 42 CRYPTOCELL                                                             */
  PWM3_IRQn                 =  45,              /*!< 45 PWM3                                                                   */
  SPIM3_IRQn                =  47               /*!< 47 SPIM3                                                                  */
} IRQn_Type;



/* =========================================================================================================================== */
/* ================                           Processor and Core Peripheral Section                           ================ */
/* =========================================================================================================================== */

/* ===========================  Configuration of the ARM Cortex-M4 Processor and Core Peripherals  =========================== */
#define __CM4_REV                 0x0001U       /*!< CM4 Core Revision                                                         */
#define __DSP_PRESENT                  1        /*!< DSP present or not                                                        */
#define __VTOR_PRESENT                 1        /*!< Set to 1 if CPU supports Vector Table Offset Register                     */
#define __NVIC_PRIO_BITS               3        /*!< Number of Bits used for Priority Levels                                   */
#define __Vendor_SysTickConfig         0        /*!< Set to 1 if different SysTick Config is used                              */
#define __MPU_PRESENT                  1        /*!< MPU present                                                               */
#define __FPU_PRESENT                  1        /*!< FPU present                                                               */


/** @} */ /* End of group Configuration_of_CMSIS */

#include "core_cm4.h"                           /*!< ARM Cortex-M4 processor and core peripherals                              */
#include "system_nrf52840.h"                    /*!< nrf52840 System                                                           */

#ifndef __IM                                    /*!< Fallback for older CMSIS versions                                         */
  #define __IM   __I
#endif
#ifndef __OM                                    /*!< Fallback for older CMSIS versions                                         */
  #define __OM   __O
#endif
#ifndef __IOM                                   /*!< Fallback for older CMSIS versions                                         */
  #define __IOM  __IO
#endif


/* ========================================  Start of section using anonymous unions  ======================================== */
#if defined (__CC_ARM)
  #pragma push
  #pragma anon_unions
#elif defined (__ICCARM__)
  #pragma language=extended
#elif defined(__ARMCC_VERSION) && (__ARMCC_VERSION >= 6010050)
  #pragma clang diagnostic push
  #pragma clang diagnostic ignored "-Wc11-extensions"
  #pragma clang diagnostic ignored "-Wreserved-id-macro"
  #pragma clang diagnostic ignored "-Wgnu-anonymous-struct"
  #pragma clang diagnostic ignored "-Wnested-anon-types"
#elif defined (__GNUC__)
  /* anonymous unions are enabled by default */
#elif defined (__TMS470__)
  /* anonymous unions are enabled by default */
#elif defined (__TASKING__)
  #pragma warning 586
#elif defined (__CSMC__)
  /* anonymous unions are enabled by default */
#else
  #warning Not supported compiler type
#endif


/* =========================================================================================================================== */
/* ================                              Device Specific Cluster Section                              ================ */
/* =========================================================================================================================== */


/** @addtogroup Device_Peripheral_clusters
  * @{
  */


/**
  * @brief FICR_INFO [INFO] (Device info)
  */
typedef struct {
  __IM  uint32_t  PART;                         /*!< (@ 0x00000000) Part code                                                  */
  __IM  uint32_t  VARIANT;                      /*!< (@ 0x00000004) Build code (hardware version and production configuration) */
  __IM  uint32_t  PACKAGE;                      /*!< (@ 0x00000008) Package option                                             */
  __IM  uint32_t  RAM;                          /*!< (@ 0x0000000C) RAM variant                                                */
  __IM  uint32_t  FLASH;                        /*!< (@ 0x00000010) Flash variant                                              */
} FICR_INFO_Type;                               /*!< Size = 20 (0x14)                                                          */


/**
  * @brief FICR_TEMP [TEMP] (Registers storing factory TEMP module linearization coefficients)
  */
typedef struct {
  __IM  uint32_t  A0;                           /*!< (@ 0x00000000) Slope definition A0                                        */
  __IM  uint32_t  A1;                           /*!< (@ 0x00000004) Slope definition A1                                        */
  __IM  uint32_t  A2;                           /*!< (@ 0x00000008) Slope definition A2                                        */
  __IM  uint32_t  A3;                           /*!< (@ 0x0000000C) Slope definition A3                                        */
  __IM  uint32_t  A4;                           /*!< (@ 0x00000010) Slope definition A4                                        */
  __IM  uint32_t  A5;                           /*!< (@ 0x00000014) Slope definition A5                                        */
  __IM  uint32_t  B0;                           /*!< (@ 0x00000018) Y-intercept B0                                             */
  __IM  uint32_t  B1;                           /*!< (@ 0x0000001C) Y-intercept B1                                             */
  __IM  uint32_t  B2;                           /*!< (@ 0x00000020) Y-intercept B2                                             */
  __IM  uint32_t  B3;                           /*!< (@ 0x00000024) Y-intercept B3                                             */
  __IM  uint32_t  B4;                           /*!< (@ 0x00000028) Y-intercept B4                                             */
  __IM  uint32_t  B5;                           /*!< (@ 0x0000002C) Y-intercept B5                                             */
  __IM  uint32_t  T0;                           /*!< (@ 0x00000030) Segment end T0                                             */
  __IM  uint32_t  T1;                           /*!< (@ 0x00000034) Segment end T1                                             */
  __IM  uint32_t  T2;                           /*!< (@ 0x00000038) Segment end T2                                             */
  __IM  uint32_t  T3;                           /*!< (@ 0x0000003C) Segment end T3                                             */
  __IM  uint32_t  T4;                           /*!< (@ 0x00000040) Segment end T4                                             */
} FICR_TEMP_Type;                               /*!< Size = 68 (0x44)                                                          */


/**
  * @brief FICR_NFC [NFC] (Unspecified)
  */
typedef struct {
  __IM  uint32_t  TAGHEADER0;                   /*!< (@ 0x00000000) Default header for NFC tag. Software can read
                                                                    these values to populate NFCID1_3RD_LAST,
                                                                    NFCID1_2ND_LAST, and NFCID1_LAST.                          */
  __IM  uint32_t  TAGHEADER1;                   /*!< (@ 0x00000004) Default header for NFC tag. Software can read
                                                                    these values to populate NFCID1_3RD_LAST,
                                                                    NFCID1_2ND_LAST, and NFCID1_LAST.                          */
  __IM  uint32_t  TAGHEADER2;                   /*!< (@ 0x00000008) Default header for NFC tag. Software can read
                                                                    these values to populate NFCID1_3RD_LAST,
                                                                    NFCID1_2ND_LAST, and NFCID1_LAST.                          */
  __IM  uint32_t  TAGHEADER3;                   /*!< (@ 0x0000000C) Default header for NFC tag. Software can read
                                                                    these values to populate NFCID1_3RD_LAST,
                                                                    NFCID1_2ND_LAST, and NFCID1_LAST.                          */
} FICR_NFC_Type;                                /*!< Size = 16 (0x10)                                                          */


/**
  * @brief FICR_TRNG90B [TRNG90B] (NIST800-90B RNG calibration data)
  */
typedef struct {
  __IM  uint32_t  BYTES;                        /*!< (@ 0x00000000) Amount of bytes for the required entropy bits              */
  __IM  uint32_t  RCCUTOFF;                     /*!< (@ 0x00000004) Repetition counter cutoff                                  */
  __IM  uint32_t  APCUTOFF;                     /*!< (@ 0x00000008) Adaptive proportion cutoff                                 */
  __IM  uint32_t  STARTUP;                      /*!< (@ 0x0000000C) Amount of bytes for the startup tests                      */
  __IM  uint32_t  ROSC1;                        /*!< (@ 0x00000010) Sample count for ring oscillator 1                         */
  __IM  uint32_t  ROSC2;                        /*!< (@ 0x00000014) Sample count for ring oscillator 2                         */
  __IM  uint32_t  ROSC3;                        /*!< (@ 0x00000018) Sample count for ring oscillator 3                         */
  __IM  uint32_t  ROSC4;                        /*!< (@ 0x0000001C) Sample count for ring oscillator 4                         */
} FICR_TRNG90B_Type;                            /*!< Size = 32 (0x20)                                                          */


/**
  * @brief POWER_RAM [RAM] (Unspecified)
  */
typedef struct {
  __IOM uint32_t  POWER;                        /*!< (@ 0x00000000) Description cluster: RAMn power control register           */
  __OM  uint32_t  POWERSET;                     /*!< (@ 0x00000004) Description cluster: RAMn power control set register       */
  __OM  uint32_t  POWERCLR;                     /*!< (@ 0x00000008) Description cluster: RAMn power control clear
                                                                    register                                                   */
  __IM  uint32_t  RESERVED;
} POWER_RAM_Type;                               /*!< Size = 16 (0x10)                                                          */


/**
  * @brief UART_PSEL [PSEL] (Unspecified)
  */
typedef struct {
  __IOM uint32_t  RTS;                          /*!< (@ 0x00000000) Pin select for RTS                                         */
  __IOM uint32_t  TXD;                          /*!< (@ 0x00000004) Pin select for TXD                                         */
  __IOM uint32_t  CTS;                          /*!< (@ 0x00000008) Pin select for CTS                                         */
  __IOM uint32_t  RXD;                          /*!< (@ 0x0000000C) Pin select for RXD                                         */
} UART_PSEL_Type;                               /*!< Size = 16 (0x10)                                                          */


/**
  * @brief UARTE_PSEL [PSEL] (Unspecified)
  */
typedef struct {
  __IOM uint32_t  RTS;                          /*!< (@ 0x00000000) Pin select for RTS signal                                  */
  __IOM uint32_t  TXD;                          /*!< (@ 0x00000004) Pin select for TXD signal                                  */
  __IOM uint32_t  CTS;                          /*!< (@ 0x00000008) Pin select for CTS signal                                  */
  __IOM uint32_t  RXD;                          /*!< (@ 0x0000000C) Pin select for RXD signal                                  */
} UARTE_PSEL_Type;                              /*!< Size = 16 (0x10)                                                          */


/**
  * @brief UARTE_RXD [RXD] (RXD EasyDMA channel)
  */
typedef struct {
  __IOM uint32_t  PTR;                          /*!< (@ 0x00000000) Data pointer                                               */
  __IOM uint32_t  MAXCNT;                       /*!< (@ 0x00000004) Maximum number of bytes in receive buffer                  */
  __IM  uint32_t  AMOUNT;                       /*!< (@ 0x00000008) Number of bytes transferred in the last transaction        */
} UARTE_RXD_Type;                               /*!< Size = 12 (0xc)                                                           */


/**
  * @brief UARTE_TXD [TXD] (TXD EasyDMA channel)
  */
typedef struct {
  __IOM uint32_t  PTR;                          /*!< (@ 0x00000000) Data pointer                                               */
  __IOM uint32_t  MAXCNT;                       /*!< (@ 0x00000004) Maximum number of bytes in transmit buffer                 */
  __IM  uint32_t  AMOUNT;                       /*!< (@ 0x00000008) Number of bytes transferred in the last transaction        */
} UARTE_TXD_Type;                               /*!< Size = 12 (0xc)                                                           */


/**
  * @brief SPI_PSEL [PSEL] (Unspecified)
  */
typedef struct {
  __IOM uint32_t  SCK;                          /*!< (@ 0x00000000) Pin select for SCK                                         */
  __IOM uint32_t  MOSI;                         /*!< (@ 0x00000004) Pin select for MOSI signal                                 */
  __IOM uint32_t  MISO;                         /*!< (@ 0x00000008) Pin select for MISO signal                                 */
} SPI_PSEL_Type;                                /*!< Size = 12 (0xc)                                                           */


/**
  * @brief SPIM_PSEL [PSEL] (Unspecified)
  */
typedef struct {
  __IOM uint32_t  SCK;                          /*!< (@ 0x00000000) Pin select for SCK                                         */
  __IOM uint32_t  MOSI;                         /*!< (@ 0x00000004) Pin select for MOSI signal                                 */
  __IOM uint32_t  MISO;                         /*!< (@ 0x00000008) Pin select for MISO signal                                 */
  __IOM uint32_t  CSN;                          /*!< (@ 0x0000000C) Pin select for CSN                                         */
} SPIM_PSEL_Type;                               /*!< Size = 16 (0x10)                                                          */


/**
  * @brief SPIM_RXD [RXD] (RXD EasyDMA channel)
  */
typedef struct {
  __IOM uint32_t  PTR;                          /*!< (@ 0x00000000) Data pointer                                               */
  __IOM uint32_t  MAXCNT;                       /*!< (@ 0x00000004) Maximum number of bytes in receive buffer                  */
  __IM  uint32_t  AMOUNT;                       /*!< (@ 0x00000008) Number of bytes transferred in the last transaction        */
  __IOM uint32_t  LIST;                         /*!< (@ 0x0000000C) EasyDMA list type                                          */
} SPIM_RXD_Type;                                /*!< Size = 16 (0x10)                                                          */


/**
  * @brief SPIM_TXD [TXD] (TXD EasyDMA channel)
  */
typedef struct {
  __IOM uint32_t  PTR;                          /*!< (@ 0x00000000) Data pointer                                               */
  __IOM uint32_t  MAXCNT;                       /*!< (@ 0x00000004) Number of bytes in transmit buffer                         */
  __IM  uint32_t  AMOUNT;                       /*!< (@ 0x00000008) Number of bytes transferred in the last transaction        */
  __IOM uint32_t  LIST;                         /*!< (@ 0x0000000C) EasyDMA list type                                          */
} SPIM_TXD_Type;                                /*!< Size = 16 (0x10)                                                          */


/**
  * @brief SPIM_IFTIMING [IFTIMING] (Unspecified)
  */
typedef struct {
  __IOM uint32_t  RXDELAY;                      /*!< (@ 0x00000000) Sample delay for input serial data on MISO                 */
  __IOM uint32_t  CSNDUR;                       /*!< (@ 0x00000004) Minimum duration between edge of CSN and edge
                                                                    of SCK and minimum duration CSN must stay
                                                                    high between transactions                                  */
} SPIM_IFTIMING_Type;                           /*!< Size = 8 (0x8)                                                            */


/**
  * @brief SPIS_PSEL [PSEL] (Unspecified)
  */
typedef struct {
  __IOM uint32_t  SCK;                          /*!< (@ 0x00000000) Pin select for SCK                                         */
  __IOM uint32_t  MISO;                         /*!< (@ 0x00000004) Pin select for MISO signal                                 */
  __IOM uint32_t  MOSI;                         /*!< (@ 0x00000008) Pin select for MOSI signal                                 */
  __IOM uint32_t  CSN;                          /*!< (@ 0x0000000C) Pin select for CSN signal                                  */
} SPIS_PSEL_Type;                               /*!< Size = 16 (0x10)                                                          */


/**
  * @brief SPIS_RXD [RXD] (Unspecified)
  */
typedef struct {
  __IOM uint32_t  PTR;                          /*!< (@ 0x00000000) RXD data pointer                                           */
  __IOM uint32_t  MAXCNT;                       /*!< (@ 0x00000004) Maximum number of bytes in receive buffer                  */
  __IM  uint32_t  AMOUNT;                       /*!< (@ 0x00000008) Number of bytes received in last granted transaction       */
  __IOM uint32_t  LIST;                         /*!< (@ 0x0000000C) EasyDMA list type                                          */
} SPIS_RXD_Type;                                /*!< Size = 16 (0x10)                                                          */


/**
  * @brief SPIS_TXD [TXD] (Unspecified)
  */
typedef struct {
  __IOM uint32_t  PTR;                          /*!< (@ 0x00000000) TXD data pointer                                           */
  __IOM uint32_t  MAXCNT;                       /*!< (@ 0x00000004) Maximum number of bytes in transmit buffer                 */
  __IM  uint32_t  AMOUNT;                       /*!< (@ 0x00000008) Number of bytes transmitted in last granted transaction    */
  __IOM uint32_t  LIST;                         /*!< (@ 0x0000000C) EasyDMA list type                                          */
} SPIS_TXD_Type;                                /*!< Size = 16 (0x10)                                                          */


/**
  * @brief TWI_PSEL [PSEL] (Unspecified)
  */
typedef struct {
  __IOM uint32_t  SCL;                          /*!< (@ 0x00000000) Pin select for SCL                                         */
  __IOM uint32_t  SDA;                          /*!< (@ 0x00000004) Pin select for SDA                                         */
} TWI_PSEL_Type;                                /*!< Size = 8 (0x8)                                                            */


/**
  * @brief TWIM_PSEL [PSEL] (Unspecified)
  */
typedef struct {
  __IOM uint32_t  SCL;                          /*!< (@ 0x00000000) Pin select for SCL signal                                  */
  __IOM uint32_t  SDA;                          /*!< (@ 0x00000004) Pin select for SDA signal                                  */
} TWIM_PSEL_Type;                               /*!< Size = 8 (0x8)                                                            */


/**
  * @brief TWIM_RXD [RXD] (RXD EasyDMA channel)
  */
typedef struct {
  __IOM uint32_t  PTR;                          /*!< (@ 0x00000000) Data pointer                                               */
  __IOM uint32_t  MAXCNT;                       /*!< (@ 0x00000004) Maximum number of bytes in receive buffer                  */
  __IM  uint32_t  AMOUNT;                       /*!< (@ 0x00000008) Number of bytes transferred in the last transaction        */
  __IOM uint32_t  LIST;                         /*!< (@ 0x0000000C) EasyDMA list type                                          */
} TWIM_RXD_Type;                                /*!< Size = 16 (0x10)                                                          */


/**
  * @brief TWIM_TXD [TXD] (TXD EasyDMA channel)
  */
typedef struct {
  __IOM uint32_t  PTR;                          /*!< (@ 0x00000000) Data pointer                                               */
  __IOM uint32_t  MAXCNT;                       /*!< (@ 0x00000004) Maximum number of bytes in transmit buffer                 */
  __IM  uint32_t  AMOUNT;                       /*!< (@ 0x00000008) Number of bytes transferred in the last transaction        */
  __IOM uint32_t  LIST;                         /*!< (@ 0x0000000C) EasyDMA list type                                          */
} TWIM_TXD_Type;                                /*!< Size = 16 (0x10)                                                          */


/**
  * @brief TWIS_PSEL [PSEL] (Unspecified)
  */
typedef struct {
  __IOM uint32_t  SCL;                          /*!< (@ 0x00000000) Pin select for SCL signal                                  */
  __IOM uint32_t  SDA;                          /*!< (@ 0x00000004) Pin select for SDA signal                                  */
} TWIS_PSEL_Type;                               /*!< Size = 8 (0x8)                                                            */


/**
  * @brief TWIS_RXD [RXD] (RXD EasyDMA channel)
  */
typedef struct {
  __IOM uint32_t  PTR;                          /*!< (@ 0x00000000) RXD Data pointer                                           */
  __IOM uint32_t  MAXCNT;                       /*!< (@ 0x00000004) Maximum number of bytes in RXD buffer                      */
  __IM  uint32_t  AMOUNT;                       /*!< (@ 0x00000008) Number of bytes transferred in the last RXD transaction    */
  __IOM uint32_t  LIST;                         /*!< (@ 0x0000000C) EasyDMA list type                                          */
} TWIS_RXD_Type;                                /*!< Size = 16 (0x10)                                                          */


/**
  * @brief TWIS_TXD [TXD] (TXD EasyDMA channel)
  */
typedef struct {
  __IOM uint32_t  PTR;                          /*!< (@ 0x00000000) TXD Data pointer                                           */
  __IOM uint32_t  MAXCNT;                       /*!< (@ 0x00000004) Maximum number of bytes in TXD buffer                      */
  __IM  uint32_t  AMOUNT;                       /*!< (@ 0x00000008) Number of bytes transferred in the last TXD transaction    */
  __IOM uint32_t  LIST;                         /*!< (@ 0x0000000C) EasyDMA list type                                          */
} TWIS_TXD_Type;                                /*!< Size = 16 (0x10)                                                          */


/**
  * @brief NFCT_FRAMESTATUS [FRAMESTATUS] (Unspecified)
  */
typedef struct {
  __IOM uint32_t  RX;                           /*!< (@ 0x00000000) Result of last incoming frame                              */
} NFCT_FRAMESTATUS_Type;                        /*!< Size = 4 (0x4)                                                            */


/**
  * @brief NFCT_TXD [TXD] (Unspecified)
  */
typedef struct {
  __IOM uint32_t  FRAMECONFIG;                  /*!< (@ 0x00000000) Configuration of outgoing frames                           */
  __IOM uint32_t  AMOUNT;                       /*!< (@ 0x00000004) Size of outgoing frame                                     */
} NFCT_TXD_Type;                                /*!< Size = 8 (0x8)                                                            */


/**
  * @brief NFCT_RXD [RXD] (Unspecified)
  */
typedef struct {
  __IOM uint32_t  FRAMECONFIG;                  /*!< (@ 0x00000000) Configuration of incoming frames                           */
  __IM  uint32_t  AMOUNT;                       /*!< (@ 0x00000004) Size of last incoming frame                                */
} NFCT_RXD_Type;                                /*!< Size = 8 (0x8)                                                            */


/**
  * @brief SAADC_EVENTS_CH [EVENTS_CH] (Peripheral events.)
  */
typedef struct {
  __IOM uint32_t  LIMITH;                       /*!< (@ 0x00000000) Description cluster: Last result is equal or
                                                                    above CH[n].LIMIT.HIGH                                     */
  __IOM uint32_t  LIMITL;                       /*!< (@ 0x00000004) Description cluster: Last result is equal or
                                                                    below CH[n].LIMIT.LOW                                      */
} SAADC_EVENTS_CH_Type;                         /*!< Size = 8 (0x8)                                                            */


/**
  * @brief SAADC_CH [CH] (Unspecified)
  */
typedef struct {
  __IOM uint32_t  PSELP;                        /*!< (@ 0x00000000) Description cluster: Input positive pin selection
                                                                    for CH[n]                                                  */
  __IOM uint32_t  PSELN;                        /*!< (@ 0x00000004) Description cluster: Input negative pin selection
                                                                    for CH[n]                                                  */
  __IOM uint32_t  CONFIG;                       /*!< (@ 0x00000008) Description cluster: Input configuration for
                                                                    CH[n]                                                      */
  __IOM uint32_t  LIMIT;                        /*!< (@ 0x0000000C) Description cluster: High/low limits for event
                                                                    monitoring of a channel                                    */
} SAADC_CH_Type;                                /*!< Size = 16 (0x10)                                                          */


/**
  * @brief SAADC_RESULT [RESULT] (RESULT EasyDMA channel)
  */
typedef struct {
  __IOM uint32_t  PTR;                          /*!< (@ 0x00000000) Data pointer                                               */
  __IOM uint32_t  MAXCNT;                       /*!< (@ 0x00000004) Maximum number of 16-bit samples to be written
                                                                    to output RAM buffer                                       */
  __IM  uint32_t  AMOUNT;                       /*!< (@ 0x00000008) Number of 16-bit samples written to output RAM
                                                                    buffer since the previous START task                       */
} SAADC_RESULT_Type;                            /*!< Size = 12 (0xc)                                                           */


/**
  * @brief QDEC_PSEL [PSEL] (Unspecified)
  */
typedef struct {
  __IOM uint32_t  LED;                          /*!< (@ 0x00000000) Pin select for LED signal                                  */
  __IOM uint32_t  A;                            /*!< (@ 0x00000004) Pin select for A signal                                    */
  __IOM uint32_t  B;                            /*!< (@ 0x00000008) Pin select for B signal                                    */
} QDEC_PSEL_Type;                               /*!< Size = 12 (0xc)                                                           */


/**
  * @brief PWM_SEQ [SEQ] (Unspecified)
  */
typedef struct {
  __IOM uint32_t  PTR;                          /*!< (@ 0x00000000) Description cluster: Beginning address in RAM
                                                                    of this sequence                                           */
  __IOM uint32_t  CNT;                          /*!< (@ 0x00000004) Description cluster: Number of values (duty cycles)
                                                                    in this sequence                                           */
  __IOM uint32_t  REFRESH;                      /*!< (@ 0x00000008) Description cluster: Number of additional PWM
                                                                    periods between samples loaded into compare
                                                                    register                                                   */
  __IOM uint32_t  ENDDELAY;                     /*!< (@ 0x0000000C) Description cluster: Time added after the sequence         */
  __IM  uint32_t  RESERVED[4];
} PWM_SEQ_Type;                                 /*!< Size = 32 (0x20)                                                          */


/**
  * @brief PWM_PSEL [PSEL] (Unspecified)
  */
typedef struct {
  __IOM uint32_t  OUT[4];                       /*!< (@ 0x00000000) Description collection: Output pin select for
                                                                    PWM channel n                                              */
} PWM_PSEL_Type;                                /*!< Size = 16 (0x10)                                                          */


/**
  * @brief PDM_PSEL [PSEL] (Unspecified)
  */
typedef struct {
  __IOM uint32_t  CLK;                          /*!< (@ 0x00000000) Pin number configuration for PDM CLK signal                */
  __IOM uint32_t  DIN;                          /*!< (@ 0x00000004) Pin number configuration for PDM DIN signal                */
} PDM_PSEL_Type;                                /*!< Size = 8 (0x8)                                                            */


/**
  * @brief PDM_SAMPLE [SAMPLE] (Unspecified)
  */
typedef struct {
  __IOM uint32_t  PTR;                          /*!< (@ 0x00000000) RAM address pointer to write samples to with
                                                                    EasyDMA                                                    */
  __IOM uint32_t  MAXCNT;                       /*!< (@ 0x00000004) Number of samples to allocate memory for in EasyDMA
                                                                    mode                                                       */
} PDM_SAMPLE_Type;                              /*!< Size = 8 (0x8)                                                            */


/**
  * @brief ACL_ACL [ACL] (Unspecified)
  */
typedef struct {
  __IOM uint32_t  ADDR;                         /*!< (@ 0x00000000) Description cluster: Configure the word-aligned
                                                                    start address of region n to protect                       */
  __IOM uint32_t  SIZE;                         /*!< (@ 0x00000004) Description cluster: Size of region to protect
                                                                    counting from address ACL[n].ADDR. Write
                                                                    '0' as no effect.                                          */
  __IOM uint32_t  PERM;                         /*!< (@ 0x00000008) Description cluster: Access permissions for region
                                                                    n as defined by start address ACL[n].ADDR
                                                                    and size ACL[n].SIZE                                       */
  __IM  uint32_t  RESERVED;
} ACL_ACL_Type;                                 /*!< Size = 16 (0x10)                                                          */


/**
  * @brief PPI_TASKS_CHG [TASKS_CHG] (Channel group tasks)
  */
typedef struct {
  __OM  uint32_t  EN;                           /*!< (@ 0x00000000) Description cluster: Enable channel group n                */
  __OM  uint32_t  DIS;                          /*!< (@ 0x00000004) Description cluster: Disable channel group n               */
} PPI_TASKS_CHG_Type;                           /*!< Size = 8 (0x8)                                                            */


/**
  * @brief PPI_CH [CH] (PPI Channel)
  */
typedef struct {
  __IOM uint32_t  EEP;                          /*!< (@ 0x00000000) Description cluster: Channel n event end-point             */
  __IOM uint32_t  TEP;                          /*!< (@ 0x00000004) Description cluster: Channel n task end-point              */
} PPI_CH_Type;                                  /*!< Size = 8 (0x8)                                                            */


/**
  * @brief PPI_FORK [FORK] (Fork)
  */
typedef struct {
  __IOM uint32_t  TEP;                          /*!< (@ 0x00000000) Description cluster: Channel n task end-point              */
} PPI_FORK_Type;                                /*!< Size = 4 (0x4)                                                            */


/**
  * @brief MWU_EVENTS_REGION [EVENTS_REGION] (Peripheral events.)
  */
typedef struct {
  __IOM uint32_t  WA;                           /*!< (@ 0x00000000) Description cluster: Write access to region n
                                                                    detected                                                   */
  __IOM uint32_t  RA;                           /*!< (@ 0x00000004) Description cluster: Read access to region n
                                                                    detected                                                   */
} MWU_EVENTS_REGION_Type;                       /*!< Size = 8 (0x8)                                                            */


/**
  * @brief MWU_EVENTS_PREGION [EVENTS_PREGION] (Peripheral events.)
  */
typedef struct {
  __IOM uint32_t  WA;                           /*!< (@ 0x00000000) Description cluster: Write access to peripheral
                                                                    region n detected                                          */
  __IOM uint32_t  RA;                           /*!< (@ 0x00000004) Description cluster: Read access to peripheral
                                                                    region n detected                                          */
} MWU_EVENTS_PREGION_Type;                      /*!< Size = 8 (0x8)                                                            */


/**
  * @brief MWU_PERREGION [PERREGION] (Unspecified)
  */
typedef struct {
  __IOM uint32_t  SUBSTATWA;                    /*!< (@ 0x00000000) Description cluster: Source of event/interrupt
                                                                    in region n, write access detected while
                                                                    corresponding subregion was enabled for
                                                                    watching                                                   */
  __IOM uint32_t  SUBSTATRA;                    /*!< (@ 0x00000004) Description cluster: Source of event/interrupt
                                                                    in region n, read access detected while
                                                                    corresponding subregion was enabled for
                                                                    watching                                                   */
} MWU_PERREGION_Type;                           /*!< Size = 8 (0x8)                                                            */


/**
  * @brief MWU_REGION [REGION] (Unspecified)
  */
typedef struct {
  __IOM uint32_t  START;                        /*!< (@ 0x00000000) Description cluster: Start address for region
                                                                    n                                                          */
  __IOM uint32_t  END;                          /*!< (@ 0x00000004) Description cluster: End address of region n               */
  __IM  uint32_t  RESERVED[2];
} MWU_REGION_Type;                              /*!< Size = 16 (0x10)                                                          */


/**
  * @brief MWU_PREGION [PREGION] (Unspecified)
  */
typedef struct {
  __IM  uint32_t  START;                        /*!< (@ 0x00000000) Description cluster: Reserved for future use               */
  __IM  uint32_t  END;                          /*!< (@ 0x00000004) Description cluster: Reserved for future use               */
  __IOM uint32_t  SUBS;                         /*!< (@ 0x00000008) Description cluster: Subregions of region n                */
  __IM  uint32_t  RESERVED;
} MWU_PREGION_Type;                             /*!< Size = 16 (0x10)                                                          */


/**
  * @brief I2S_CONFIG [CONFIG] (Unspecified)
  */
typedef struct {
  __IOM uint32_t  MODE;                         /*!< (@ 0x00000000) I2S mode.                                                  */
  __IOM uint32_t  RXEN;                         /*!< (@ 0x00000004) Reception (RX) enable.                                     */
  __IOM uint32_t  TXEN;                         /*!< (@ 0x00000008) Transmission (TX) enable.                                  */
  __IOM uint32_t  MCKEN;                        /*!< (@ 0x0000000C) Master clock generator enable.                             */
  __IOM uint32_t  MCKFREQ;                      /*!< (@ 0x00000010) Master clock generator frequency.                          */
  __IOM uint32_t  RATIO;                        /*!< (@ 0x00000014) MCK / LRCK ratio.                                          */
  __IOM uint32_t  SWIDTH;                       /*!< (@ 0x00000018) Sample width.                                              */
  __IOM uint32_t  ALIGN;                        /*!< (@ 0x0000001C) Alignment of sample within a frame.                        */
  __IOM uint32_t  FORMAT;                       /*!< (@ 0x00000020) Frame format.                                              */
  __IOM uint32_t  CHANNELS;                     /*!< (@ 0x00000024) Enable channels.                                           */
} I2S_CONFIG_Type;                              /*!< Size = 40 (0x28)                                                          */


/**
  * @brief I2S_RXD [RXD] (Unspecified)
  */
typedef struct {
  __IOM uint32_t  PTR;                          /*!< (@ 0x00000000) Receive buffer RAM start address.                          */
} I2S_RXD_Type;                                 /*!< Size = 4 (0x4)                                                            */


/**
  * @brief I2S_TXD [TXD] (Unspecified)
  */
typedef struct {
  __IOM uint32_t  PTR;                          /*!< (@ 0x00000000) Transmit buffer RAM start address.                         */
} I2S_TXD_Type;                                 /*!< Size = 4 (0x4)                                                            */


/**
  * @brief I2S_RXTXD [RXTXD] (Unspecified)
  */
typedef struct {
  __IOM uint32_t  MAXCNT;                       /*!< (@ 0x00000000) Size of RXD and TXD buffers.                               */
} I2S_RXTXD_Type;                               /*!< Size = 4 (0x4)                                                            */


/**
  * @brief I2S_PSEL [PSEL] (Unspecified)
  */
typedef struct {
  __IOM uint32_t  MCK;                          /*!< (@ 0x00000000) Pin select for MCK signal.                                 */
  __IOM uint32_t  SCK;                          /*!< (@ 0x00000004) Pin select for SCK signal.                                 */
  __IOM uint32_t  LRCK;                         /*!< (@ 0x00000008) Pin select for LRCK signal.                                */
  __IOM uint32_t  SDIN;                         /*!< (@ 0x0000000C) Pin select for SDIN signal.                                */
  __IOM uint32_t  SDOUT;                        /*!< (@ 0x00000010) Pin select for SDOUT signal.                               */
} I2S_PSEL_Type;                                /*!< Size = 20 (0x14)                                                          */


/**
  * @brief USBD_HALTED [HALTED] (Unspecified)
  */
typedef struct {
  __IM  uint32_t  EPIN[8];                      /*!< (@ 0x00000000) Description collection: IN endpoint halted status.
                                                                    Can be used as is as response to a GetStatus()
                                                                    request to endpoint.                                       */
  __IM  uint32_t  RESERVED;
  __IM  uint32_t  EPOUT[8];                     /*!< (@ 0x00000024) Description collection: OUT endpoint halted status.
                                                                    Can be used as is as response to a GetStatus()
                                                                    request to endpoint.                                       */
} USBD_HALTED_Type;                             /*!< Size = 68 (0x44)                                                          */


/**
  * @brief USBD_SIZE [SIZE] (Unspecified)
  */
typedef struct {
  __IOM uint32_t  EPOUT[8];                     /*!< (@ 0x00000000) Description collection: Number of bytes received
                                                                    last in the data stage of this OUT endpoint                */
  __IM  uint32_t  ISOOUT;                       /*!< (@ 0x00000020) Number of bytes received last on this ISO OUT
                                                                    data endpoint                                              */
} USBD_SIZE_Type;                               /*!< Size = 36 (0x24)                                                          */


/**
  * @brief USBD_EPIN [EPIN] (Unspecified)
  */
typedef struct {
  __IOM uint32_t  PTR;                          /*!< (@ 0x00000000) Description cluster: Data pointer                          */
  __IOM uint32_t  MAXCNT;                       /*!< (@ 0x00000004) Description cluster: Maximum number of bytes
                                                                    to transfer                                                */
  __IM  uint32_t  AMOUNT;                       /*!< (@ 0x00000008) Description cluster: Number of bytes transferred
                                                                    in the last transaction                                    */
  __IM  uint32_t  RESERVED[2];
} USBD_EPIN_Type;                               /*!< Size = 20 (0x14)                                                          */


/**
  * @brief USBD_ISOIN [ISOIN] (Unspecified)
  */
typedef struct {
  __IOM uint32_t  PTR;                          /*!< (@ 0x00000000) Data pointer                                               */
  __IOM uint32_t  MAXCNT;                       /*!< (@ 0x00000004) Maximum number of bytes to transfer                        */
  __IM  uint32_t  AMOUNT;                       /*!< (@ 0x00000008) Number of bytes transferred in the last transaction        */
} USBD_ISOIN_Type;                              /*!< Size = 12 (0xc)                                                           */


/**
  * @brief USBD_EPOUT [EPOUT] (Unspecified)
  */
typedef struct {
  __IOM uint32_t  PTR;                          /*!< (@ 0x00000000) Description cluster: Data pointer                          */
  __IOM uint32_t  MAXCNT;                       /*!< (@ 0x00000004) Description cluster: Maximum number of bytes
                                                                    to transfer                                                */
  __IM  uint32_t  AMOUNT;                       /*!< (@ 0x00000008) Description cluster: Number of bytes transferred
                                                                    in the last transaction                                    */
  __IM  uint32_t  RESERVED[2];
} USBD_EPOUT_Type;                              /*!< Size = 20 (0x14)                                                          */


/**
  * @brief USBD_ISOOUT [ISOOUT] (Unspecified)
  */
typedef struct {
  __IOM uint32_t  PTR;                          /*!< (@ 0x00000000) Data pointer                                               */
  __IOM uint32_t  MAXCNT;                       /*!< (@ 0x00000004) Maximum number of bytes to transfer                        */
  __IM  uint32_t  AMOUNT;                       /*!< (@ 0x00000008) Number of bytes transferred in the last transaction        */
} USBD_ISOOUT_Type;                             /*!< Size = 12 (0xc)                                                           */


/**
  * @brief QSPI_READ [READ] (Unspecified)
  */
typedef struct {
  __IOM uint32_t  SRC;                          /*!< (@ 0x00000000) Flash memory source address                                */
  __IOM uint32_t  DST;                          /*!< (@ 0x00000004) RAM destination address                                    */
  __IOM uint32_t  CNT;                          /*!< (@ 0x00000008) Read transfer length                                       */
} QSPI_READ_Type;                               /*!< Size = 12 (0xc)                                                           */


/**
  * @brief QSPI_WRITE [WRITE] (Unspecified)
  */
typedef struct {
  __IOM uint32_t  DST;                          /*!< (@ 0x00000000) Flash destination address                                  */
  __IOM uint32_t  SRC;                          /*!< (@ 0x00000004) RAM source address                                         */
  __IOM uint32_t  CNT;                          /*!< (@ 0x00000008) Write transfer length                                      */
} QSPI_WRITE_Type;                              /*!< Size = 12 (0xc)                                                           */


/**
  * @brief QSPI_ERASE [ERASE] (Unspecified)
  */
typedef struct {
  __IOM uint32_t  PTR;                          /*!< (@ 0x00000000) Start address of flash block to be erased                  */
  __IOM uint32_t  LEN;                          /*!< (@ 0x00000004) Size of block to be erased.                                */
} QSPI_ERASE_Type;                              /*!< Size = 8 (0x8)                                                            */


/**
  * @brief QSPI_PSEL [PSEL] (Unspecified)
  */
typedef struct {
  __IOM uint32_t  SCK;                          /*!< (@ 0x00000000) Pin select for serial clock SCK                            */
  __IOM uint32_t  CSN;                          /*!< (@ 0x00000004) Pin select for chip select signal CSN.                     */
  __IM  uint32_t  RESERVED;
  __IOM uint32_t  IO0;                          /*!< (@ 0x0000000C) Pin select for serial data MOSI/IO0.                       */
  __IOM uint32_t  IO1;                          /*!< (@ 0x00000010) Pin select for serial data MISO/IO1.                       */
  __IOM uint32_t  IO2;                          /*!< (@ 0x00000014) Pin select for serial data IO2.                            */
  __IOM uint32_t  IO3;                          /*!< (@ 0x00000018) Pin select for serial data IO3.                            */
} QSPI_PSEL_Type;                               /*!< Size = 28 (0x1c)                                                          */


/** @} */ /* End of group Device_Peripheral_clusters */


/* =========================================================================================================================== */
/* ================                            Device Specific Peripheral Section                             ================ */
/* =========================================================================================================================== */


/** @addtogroup Device_Peripheral_peripherals
  * @{
  */



/* =========================================================================================================================== */
/* ================                                           FICR                                            ================ */
/* =========================================================================================================================== */


/**
  * @brief Factory information configuration registers (FICR)
  */

typedef struct {                                /*!< (@ 0x10000000) FICR Structure                                             */
  __IM  uint32_t  RESERVED[4];
  __IM  uint32_t  CODEPAGESIZE;                 /*!< (@ 0x00000010) Code memory page size                                      */
  __IM  uint32_t  CODESIZE;                     /*!< (@ 0x00000014) Code memory size                                           */
  __IM  uint32_t  RESERVED1[18];
  __IM  uint32_t  DEVICEID[2];                  /*!< (@ 0x00000060) Description collection: Device identifier                  */
  __IM  uint32_t  RESERVED2[6];
  __IM  uint32_t  ER[4];                        /*!< (@ 0x00000080) Description collection: Encryption root, word
                                                                    n                                                          */
  __IM  uint32_t  IR[4];                        /*!< (@ 0x00000090) Description collection: Identity Root, word n              */
  __IM  uint32_t  DEVICEADDRTYPE;               /*!< (@ 0x000000A0) Device address type                                        */
  __IM  uint32_t  DEVICEADDR[2];                /*!< (@ 0x000000A4) Description collection: Device address n                   */
  __IM  uint32_t  RESERVED3[21];
  __IM  FICR_INFO_Type INFO;                    /*!< (@ 0x00000100) Device info                                                */
  __IM  uint32_t  RESERVED4[143];
  __IM  uint32_t  PRODTEST[3];                  /*!< (@ 0x00000350) Description collection: Production test signature
                                                                    n                                                          */
  __IM  uint32_t  RESERVED5[42];
  __IM  FICR_TEMP_Type TEMP;                    /*!< (@ 0x00000404) Registers storing factory TEMP module linearization
                                                                    coefficients                                               */
  __IM  uint32_t  RESERVED6[2];
  __IOM FICR_NFC_Type NFC;                      /*!< (@ 0x00000450) Unspecified                                                */
  __IM  uint32_t  RESERVED7[488];
  __IOM FICR_TRNG90B_Type TRNG90B;              /*!< (@ 0x00000C00) NIST800-90B RNG calibration data                           */
} NRF_FICR_Type;                                /*!< Size = 3104 (0xc20)                                                       */



/* =========================================================================================================================== */
/* ================                                           UICR                                            ================ */
/* =========================================================================================================================== */


/**
  * @brief User information configuration registers (UICR)
  */

typedef struct {                                /*!< (@ 0x10001000) UICR Structure                                             */
  __IM  uint32_t  RESERVED[5];
  __IOM uint32_t  NRFFW[13];                    /*!< (@ 0x00000014) Description collection: Reserved for Nordic firmware
                                                                    design                                                     */
  __IM  uint32_t  RESERVED1[2];
  __IOM uint32_t  NRFHW[12];                    /*!< (@ 0x00000050) Description collection: Reserved for Nordic hardware
                                                                    design                                                     */
  __IOM uint32_t  CUSTOMER[32];                 /*!< (@ 0x00000080) Description collection: Reserved for customer              */
  __IM  uint32_t  RESERVED2[64];
  __IOM uint32_t  PSELRESET[2];                 /*!< (@ 0x00000200) Description collection: Mapping of the nRESET
                                                                    function (see POWER chapter for details)                   */
  __IOM uint32_t  APPROTECT;                    /*!< (@ 0x00000208) Access port protection                                     */
  __IOM uint32_t  NFCPINS;                      /*!< (@ 0x0000020C) Setting of pins dedicated to NFC functionality:
                                                                    NFC antenna or GPIO                                        */
  __IOM uint32_t  DEBUGCTRL;                    /*!< (@ 0x00000210) Processor debug control                                    */
  __IM  uint32_t  RESERVED3[60];
  __IOM uint32_t  REGOUT0;                      /*!< (@ 0x00000304) Output voltage from REG0 regulator stage. The
                                                                    maximum output voltage from this stage is
                                                                    given as VDDH - VREG0DROP.                                 */
} NRF_UICR_Type;                                /*!< Size = 776 (0x308)                                                        */



/* =========================================================================================================================== */
/* ================                                           CLOCK                                           ================ */
/* =========================================================================================================================== */


/**
  * @brief Clock control (CLOCK)
  */

typedef struct {                                /*!< (@ 0x40000000) CLOCK Structure                                            */
  __OM  uint32_t  TASKS_HFCLKSTART;             /*!< (@ 0x00000000) Start HFXO crystal oscillator                              */
  __OM  uint32_t  TASKS_HFCLKSTOP;              /*!< (@ 0x00000004) Stop HFXO crystal oscillator                               */
  __OM  uint32_t  TASKS_LFCLKSTART;             /*!< (@ 0x00000008) Start LFCLK                                                */
  __OM  uint32_t  TASKS_LFCLKSTOP;              /*!< (@ 0x0000000C) Stop LFCLK                                                 */
  __OM  uint32_t  TASKS_CAL;                    /*!< (@ 0x00000010) Start calibration of LFRC                                  */
  __OM  uint32_t  TASKS_CTSTART;                /*!< (@ 0x00000014) Start calibration timer                                    */
  __OM  uint32_t  TASKS_CTSTOP;                 /*!< (@ 0x00000018) Stop calibration timer                                     */
  __IM  uint32_t  RESERVED[57];
  __IOM uint32_t  EVENTS_HFCLKSTARTED;          /*!< (@ 0x00000100) HFXO crystal oscillator started                            */
  __IOM uint32_t  EVENTS_LFCLKSTARTED;          /*!< (@ 0x00000104) LFCLK started                                              */
  __IM  uint32_t  RESERVED1;
  __IOM uint32_t  EVENTS_DONE;                  /*!< (@ 0x0000010C) Calibration of LFRC completed                              */
  __IOM uint32_t  EVENTS_CTTO;                  /*!< (@ 0x00000110) Calibration timer timeout                                  */
  __IM  uint32_t  RESERVED2[5];
  __IOM uint32_t  EVENTS_CTSTARTED;             /*!< (@ 0x00000128) Calibration timer has been started and is ready
                                                                    to process new tasks                                       */
  __IOM uint32_t  EVENTS_CTSTOPPED;             /*!< (@ 0x0000012C) Calibration timer has been stopped and is ready
                                                                    to process new tasks                                       */
  __IM  uint32_t  RESERVED3[117];
  __IOM uint32_t  INTENSET;                     /*!< (@ 0x00000304) Enable interrupt                                           */
  __IOM uint32_t  INTENCLR;                     /*!< (@ 0x00000308) Disable interrupt                                          */
  __IM  uint32_t  RESERVED4[63];
  __IM  uint32_t  HFCLKRUN;                     /*!< (@ 0x00000408) Status indicating that HFCLKSTART task has been
                                                                    triggered                                                  */
  __IM  uint32_t  HFCLKSTAT;                    /*!< (@ 0x0000040C) HFCLK status                                               */
  __IM  uint32_t  RESERVED5;
  __IM  uint32_t  LFCLKRUN;                     /*!< (@ 0x00000414) Status indicating that LFCLKSTART task has been
                                                                    triggered                                                  */
  __IM  uint32_t  LFCLKSTAT;                    /*!< (@ 0x00000418) LFCLK status                                               */
  __IM  uint32_t  LFCLKSRCCOPY;                 /*!< (@ 0x0000041C) Copy of LFCLKSRC register, set when LFCLKSTART
                                                                    task was triggered                                         */
  __IM  uint32_t  RESERVED6[62];
  __IOM uint32_t  LFCLKSRC;                     /*!< (@ 0x00000518) Clock source for the LFCLK                                 */
  __IM  uint32_t  RESERVED7[3];
  __IOM uint32_t  HFXODEBOUNCE;                 /*!< (@ 0x00000528) HFXO debounce time. The HFXO is started by triggering
                                                                    the TASKS_HFCLKSTART task.                                 */
  __IM  uint32_t  RESERVED8[3];
  __IOM uint32_t  CTIV;                         /*!< (@ 0x00000538) Calibration timer interval                                 */
  __IM  uint32_t  RESERVED9[8];
  __IOM uint32_t  TRACECONFIG;                  /*!< (@ 0x0000055C) Clocking options for the trace port debug interface        */
  __IM  uint32_t  RESERVED10[21];
  __IOM uint32_t  LFRCMODE;                     /*!< (@ 0x000005B4) LFRC mode configuration                                    */
} NRF_CLOCK_Type;                               /*!< Size = 1464 (0x5b8)                                                       */



/* =========================================================================================================================== */
/* ================                                           POWER                                           ================ */
/* =========================================================================================================================== */


/**
  * @brief Power control (POWER)
  */

typedef struct {                                /*!< (@ 0x40000000) POWER Structure                                            */
  __IM  uint32_t  RESERVED[30];
  __OM  uint32_t  TASKS_CONSTLAT;               /*!< (@ 0x00000078) Enable Constant Latency mode                               */
  __OM  uint32_t  TASKS_LOWPWR;                 /*!< (@ 0x0000007C) Enable Low-power mode (variable latency)                   */
  __IM  uint32_t  RESERVED1[34];
  __IOM uint32_t  EVENTS_POFWARN;               /*!< (@ 0x00000108) Power failure warning                                      */
  __IM  uint32_t  RESERVED2[2];
  __IOM uint32_t  EVENTS_SLEEPENTER;            /*!< (@ 0x00000114) CPU entered WFI/WFE sleep                                  */
  __IOM uint32_t  EVENTS_SLEEPEXIT;             /*!< (@ 0x00000118) CPU exited WFI/WFE sleep                                   */
  __IOM uint32_t  EVENTS_USBDETECTED;           /*!< (@ 0x0000011C) Voltage supply detected on VBUS                            */
  __IOM uint32_t  EVENTS_USBREMOVED;            /*!< (@ 0x00000120) Voltage supply removed from VBUS                           */
  __IOM uint32_t  EVENTS_USBPWRRDY;             /*!< (@ 0x00000124) USB 3.3 V supply ready                                     */
  __IM  uint32_t  RESERVED3[119];
  __IOM uint32_t  INTENSET;                     /*!< (@ 0x00000304) Enable interrupt                                           */
  __IOM uint32_t  INTENCLR;                     /*!< (@ 0x00000308) Disable interrupt                                          */
  __IM  uint32_t  RESERVED4[61];
  __IOM uint32_t  RESETREAS;                    /*!< (@ 0x00000400) Reset reason                                               */
  __IM  uint32_t  RESERVED5[9];
  __IM  uint32_t  RAMSTATUS;                    /*!< (@ 0x00000428) Deprecated register - RAM status register                  */
  __IM  uint32_t  RESERVED6[3];
  __IM  uint32_t  USBREGSTATUS;                 /*!< (@ 0x00000438) USB supply status                                          */
  __IM  uint32_t  RESERVED7[49];
  __OM  uint32_t  SYSTEMOFF;                    /*!< (@ 0x00000500) System OFF register                                        */
  __IM  uint32_t  RESERVED8[3];
  __IOM uint32_t  POFCON;                       /*!< (@ 0x00000510) Power-fail comparator configuration                        */
  __IM  uint32_t  RESERVED9[2];
  __IOM uint32_t  GPREGRET;                     /*!< (@ 0x0000051C) General purpose retention register                         */
  __IOM uint32_t  GPREGRET2;                    /*!< (@ 0x00000520) General purpose retention register                         */
  __IM  uint32_t  RESERVED10[21];
  __IOM uint32_t  DCDCEN;                       /*!< (@ 0x00000578) Enable DC/DC converter for REG1 stage                      */
  __IM  uint32_t  RESERVED11;
  __IOM uint32_t  DCDCEN0;                      /*!< (@ 0x00000580) Enable DC/DC converter for REG0 stage                      */
  __IM  uint32_t  RESERVED12[47];
  __IM  uint32_t  MAINREGSTATUS;                /*!< (@ 0x00000640) Main supply status                                         */
  __IM  uint32_t  RESERVED13[175];
  __IOM POWER_RAM_Type RAM[9];                  /*!< (@ 0x00000900) Unspecified                                                */
} NRF_POWER_Type;                               /*!< Size = 2448 (0x990)                                                       */



/* =========================================================================================================================== */
/* ================                                            P0                                             ================ */
/* =========================================================================================================================== */


/**
  * @brief GPIO Port 1 (P0)
  */

typedef struct {                                /*!< (@ 0x50000000) P0 Structure                                               */
  __IM  uint32_t  RESERVED[321];
  __IOM uint32_t  OUT;                          /*!< (@ 0x00000504) Write GPIO port                                            */
  __IOM uint32_t  OUTSET;                       /*!< (@ 0x00000508) Set individual bits in GPIO port                           */
  __IOM uint32_t  OUTCLR;                       /*!< (@ 0x0000050C) Clear individual bits in GPIO port                         */
  __IM  uint32_t  IN;                           /*!< (@ 0x00000510) Read GPIO port                                             */
  __IOM uint32_t  DIR;                          /*!< (@ 0x00000514) Direction of GPIO pins                                     */
  __IOM uint32_t  DIRSET;                       /*!< (@ 0x00000518) DIR set register                                           */
  __IOM uint32_t  DIRCLR;                       /*!< (@ 0x0000051C) DIR clear register                                         */
  __IOM uint32_t  LATCH;                        /*!< (@ 0x00000520) Latch register indicating what GPIO pins that
                                                                    have met the criteria set in the PIN_CNF[n].SENSE
                                                                    registers                                                  */
  __IOM uint32_t  DETECTMODE;                   /*!< (@ 0x00000524) Select between default DETECT signal behaviour
                                                                    and LDETECT mode                                           */
  __IM  uint32_t  RESERVED1[118];
  __IOM uint32_t  PIN_CNF[32];                  /*!< (@ 0x00000700) Description collection: Configuration of GPIO
                                                                    pins                                                       */
} NRF_GPIO_Type;                                /*!< Size = 1920 (0x780)                                                       */



/* =========================================================================================================================== */
/* ================                                           RADIO                                           ================ */
/* =========================================================================================================================== */


/**
  * @brief 2.4 GHz radio (RADIO)
  */

typedef struct {                                /*!< (@ 0x40001000) RADIO Structure                                            */
  __OM  uint32_t  TASKS_TXEN;                   /*!< (@ 0x00000000) Enable RADIO in TX mode                                    */
  __OM  uint32_t  TASKS_RXEN;                   /*!< (@ 0x00000004) Enable RADIO in RX mode                                    */
  __OM  uint32_t  TASKS_START;                  /*!< (@ 0x00000008) Start RADIO                                                */
  __OM  uint32_t  TASKS_STOP;                   /*!< (@ 0x0000000C) Stop RADIO                                                 */
  __OM  uint32_t  TASKS_DISABLE;                /*!< (@ 0x00000010) Disable RADIO                                              */
  __OM  uint32_t  TASKS_RSSISTART;              /*!< (@ 0x00000014) Start the RSSI and take one single sample of
                                                                    the receive signal strength                                */
  __OM  uint32_t  TASKS_RSSISTOP;               /*!< (@ 0x00000018) Stop the RSSI measurement                                  */
  __OM  uint32_t  TASKS_BCSTART;                /*!< (@ 0x0000001C) Start the bit counter                                      */
  __OM  uint32_t  TASKS_BCSTOP;                 /*!< (@ 0x00000020) Stop the bit counter                                       */
  __OM  uint32_t  TASKS_EDSTART;                /*!< (@ 0x00000024) Start the energy detect measurement used in IEEE
                                                                    802.15.4 mode                                              */
  __OM  uint32_t  TASKS_EDSTOP;                 /*!< (@ 0x00000028) Stop the energy detect measurement                         */
  __OM  uint32_t  TASKS_CCASTART;               /*!< (@ 0x0000002C) Start the clear channel assessment used in IEEE
                                                                    802.15.4 mode                                              */
  __OM  uint32_t  TASKS_CCASTOP;                /*!< (@ 0x00000030) Stop the clear channel assessment                          */
  __IM  uint32_t  RESERVED[51];
  __IOM uint32_t  EVENTS_READY;                 /*!< (@ 0x00000100) RADIO has ramped up and is ready to be started             */
  __IOM uint32_t  EVENTS_ADDRESS;               /*!< (@ 0x00000104) Address sent or received                                   */
  __IOM uint32_t  EVENTS_PAYLOAD;               /*!< (@ 0x00000108) Packet payload sent or received                            */
  __IOM uint32_t  EVENTS_END;                   /*!< (@ 0x0000010C) Packet sent or received                                    */
  __IOM uint32_t  EVENTS_DISABLED;              /*!< (@ 0x00000110) RADIO has been disabled                                    */
  __IOM uint32_t  EVENTS_DEVMATCH;              /*!< (@ 0x00000114) A device address match occurred on the last received
                                                                    packet                                                     */
  __IOM uint32_t  EVENTS_DEVMISS;               /*!< (@ 0x00000118) No device address match occurred on the last
                                                                    received packet                                            */
  __IOM uint32_t  EVENTS_RSSIEND;               /*!< (@ 0x0000011C) Sampling of receive signal strength complete               */
  __IM  uint32_t  RESERVED1[2];
  __IOM uint32_t  EVENTS_BCMATCH;               /*!< (@ 0x00000128) Bit counter reached bit count value                        */
  __IM  uint32_t  RESERVED2;
  __IOM uint32_t  EVENTS_CRCOK;                 /*!< (@ 0x00000130) Packet received with CRC ok                                */
  __IOM uint32_t  EVENTS_CRCERROR;              /*!< (@ 0x00000134) Packet received with CRC error                             */
  __IOM uint32_t  EVENTS_FRAMESTART;            /*!< (@ 0x00000138) IEEE 802.15.4 length field received                        */
  __IOM uint32_t  EVENTS_EDEND;                 /*!< (@ 0x0000013C) Sampling of energy detection complete. A new
                                                                    ED sample is ready for readout from the
                                                                    RADIO.EDSAMPLE register.                                   */
  __IOM uint32_t  EVENTS_EDSTOPPED;             /*!< (@ 0x00000140) The sampling of energy detection has stopped               */
  __IOM uint32_t  EVENTS_CCAIDLE;               /*!< (@ 0x00000144) Wireless medium in idle - clear to send                    */
  __IOM uint32_t  EVENTS_CCABUSY;               /*!< (@ 0x00000148) Wireless medium busy - do not send                         */
  __IOM uint32_t  EVENTS_CCASTOPPED;            /*!< (@ 0x0000014C) The CCA has stopped                                        */
  __IOM uint32_t  EVENTS_RATEBOOST;             /*!< (@ 0x00000150) Ble_LR CI field received, receive mode is changed
                                                                    from Ble_LR125Kbit to Ble_LR500Kbit.                       */
  __IOM uint32_t  EVENTS_TXREADY;               /*!< (@ 0x00000154) RADIO has ramped up and is ready to be started
                                                                    TX path                                                    */
  __IOM uint32_t  EVENTS_RXREADY;               /*!< (@ 0x00000158) RADIO has ramped up and is ready to be started
                                                                    RX path                                                    */
  __IOM uint32_t  EVENTS_MHRMATCH;              /*!< (@ 0x0000015C) MAC header match found                                     */
  __IM  uint32_t  RESERVED3[3];
  __IOM uint32_t  EVENTS_PHYEND;                /*!< (@ 0x0000016C) Generated in Ble_LR125Kbit, Ble_LR500Kbit and
                                                                    Ieee802154_250Kbit modes when last bit is
                                                                    sent on air.                                               */
  __IM  uint32_t  RESERVED4[36];
  __IOM uint32_t  SHORTS;                       /*!< (@ 0x00000200) Shortcuts between local events and tasks                   */
  __IM  uint32_t  RESERVED5[64];
  __IOM uint32_t  INTENSET;                     /*!< (@ 0x00000304) Enable interrupt                                           */
  __IOM uint32_t  INTENCLR;                     /*!< (@ 0x00000308) Disable interrupt                                          */
  __IM  uint32_t  RESERVED6[61];
  __IM  uint32_t  CRCSTATUS;                    /*!< (@ 0x00000400) CRC status                                                 */
  __IM  uint32_t  RESERVED7;
  __IM  uint32_t  RXMATCH;                      /*!< (@ 0x00000408) Received address                                           */
  __IM  uint32_t  RXCRC;                        /*!< (@ 0x0000040C) CRC field of previously received packet                    */
  __IM  uint32_t  DAI;                          /*!< (@ 0x00000410) Device address match index                                 */
  __IM  uint32_t  PDUSTAT;                      /*!< (@ 0x00000414) Payload status                                             */
  __IM  uint32_t  RESERVED8[59];
  __IOM uint32_t  PACKETPTR;                    /*!< (@ 0x00000504) Packet pointer                                             */
  __IOM uint32_t  FREQUENCY;                    /*!< (@ 0x00000508) Frequency                                                  */
  __IOM uint32_t  TXPOWER;                      /*!< (@ 0x0000050C) Output power                                               */
  __IOM uint32_t  MODE;                         /*!< (@ 0x00000510) Data rate and modulation                                   */
  __IOM uint32_t  PCNF0;                        /*!< (@ 0x00000514) Packet configuration register 0                            */
  __IOM uint32_t  PCNF1;                        /*!< (@ 0x00000518) Packet configuration register 1                            */
  __IOM uint32_t  BASE0;                        /*!< (@ 0x0000051C) Base address 0                                             */
  __IOM uint32_t  BASE1;                        /*!< (@ 0x00000520) Base address 1                                             */
  __IOM uint32_t  PREFIX0;                      /*!< (@ 0x00000524) Prefixes bytes for logical addresses 0-3                   */
  __IOM uint32_t  PREFIX1;                      /*!< (@ 0x00000528) Prefixes bytes for logical addresses 4-7                   */
  __IOM uint32_t  TXADDRESS;                    /*!< (@ 0x0000052C) Transmit address select                                    */
  __IOM uint32_t  RXADDRESSES;                  /*!< (@ 0x00000530) Receive address select                                     */
  __IOM uint32_t  CRCCNF;                       /*!< (@ 0x00000534) CRC configuration                                          */
  __IOM uint32_t  CRCPOLY;                      /*!< (@ 0x00000538) CRC polynomial                                             */
  __IOM uint32_t  CRCINIT;                      /*!< (@ 0x0000053C) CRC initial value                                          */
  __IM  uint32_t  RESERVED9;
  __IOM uint32_t  TIFS;                         /*!< (@ 0x00000544) Interframe spacing in us                                   */
  __IM  uint32_t  RSSISAMPLE;                   /*!< (@ 0x00000548) RSSI sample                                                */
  __IM  uint32_t  RESERVED10;
  __IM  uint32_t  STATE;                        /*!< (@ 0x00000550) Current radio state                                        */
  __IOM uint32_t  DATAWHITEIV;                  /*!< (@ 0x00000554) Data whitening initial value                               */
  __IM  uint32_t  RESERVED11[2];
  __IOM uint32_t  BCC;                          /*!< (@ 0x00000560) Bit counter compare                                        */
  __IM  uint32_t  RESERVED12[39];
  __IOM uint32_t  DAB[8];                       /*!< (@ 0x00000600) Description collection: Device address base segment
                                                                    n                                                          */
  __IOM uint32_t  DAP[8];                       /*!< (@ 0x00000620) Description collection: Device address prefix
                                                                    n                                                          */
  __IOM uint32_t  DACNF;                        /*!< (@ 0x00000640) Device address match configuration                         */
  __IOM uint32_t  MHRMATCHCONF;                 /*!< (@ 0x00000644) Search pattern configuration                               */
  __IOM uint32_t  MHRMATCHMAS;                  /*!< (@ 0x00000648) Pattern mask                                               */
  __IM  uint32_t  RESERVED13;
  __IOM uint32_t  MODECNF0;                     /*!< (@ 0x00000650) Radio mode configuration register 0                        */
  __IM  uint32_t  RESERVED14[3];
  __IOM uint32_t  SFD;                          /*!< (@ 0x00000660) IEEE 802.15.4 start of frame delimiter                     */
  __IOM uint32_t  EDCNT;                        /*!< (@ 0x00000664) IEEE 802.15.4 energy detect loop count                     */
  __IOM uint32_t  EDSAMPLE;                     /*!< (@ 0x00000668) IEEE 802.15.4 energy detect level                          */
  __IOM uint32_t  CCACTRL;                      /*!< (@ 0x0000066C) IEEE 802.15.4 clear channel assessment control             */
  __IM  uint32_t  RESERVED15[611];
  __IOM uint32_t  POWER;                        /*!< (@ 0x00000FFC) Peripheral power control                                   */
} NRF_RADIO_Type;                               /*!< Size = 4096 (0x1000)                                                      */



/* =========================================================================================================================== */
/* ================                                           UART0                                           ================ */
/* =========================================================================================================================== */


/**
  * @brief Universal Asynchronous Receiver/Transmitter (UART0)
  */

typedef struct {                                /*!< (@ 0x40002000) UART0 Structure                                            */
  __OM  uint32_t  TASKS_STARTRX;                /*!< (@ 0x00000000) Start UART receiver                                        */
  __OM  uint32_t  TASKS_STOPRX;                 /*!< (@ 0x00000004) Stop UART receiver                                         */
  __OM  uint32_t  TASKS_STARTTX;                /*!< (@ 0x00000008) Start UART transmitter                                     */
  __OM  uint32_t  TASKS_STOPTX;                 /*!< (@ 0x0000000C) Stop UART transmitter                                      */
  __IM  uint32_t  RESERVED[3];
  __OM  uint32_t  TASKS_SUSPEND;                /*!< (@ 0x0000001C) Suspend UART                                               */
  __IM  uint32_t  RESERVED1[56];
  __IOM uint32_t  EVENTS_CTS;                   /*!< (@ 0x00000100) CTS is activated (set low). Clear To Send.                 */
  __IOM uint32_t  EVENTS_NCTS;                  /*!< (@ 0x00000104) CTS is deactivated (set high). Not Clear To Send.          */
  __IOM uint32_t  EVENTS_RXDRDY;                /*!< (@ 0x00000108) Data received in RXD                                       */
  __IM  uint32_t  RESERVED2[4];
  __IOM uint32_t  EVENTS_TXDRDY;                /*!< (@ 0x0000011C) Data sent from TXD                                         */
  __IM  uint32_t  RESERVED3;
  __IOM uint32_t  EVENTS_ERROR;                 /*!< (@ 0x00000124) Error detected                                             */
  __IM  uint32_t  RESERVED4[7];
  __IOM uint32_t  EVENTS_RXTO;                  /*!< (@ 0x00000144) Receiver timeout                                           */
  __IM  uint32_t  RESERVED5[46];
  __IOM uint32_t  SHORTS;                       /*!< (@ 0x00000200) Shortcuts between local events and tasks                   */
  __IM  uint32_t  RESERVED6[64];
  __IOM uint32_t  INTENSET;                     /*!< (@ 0x00000304) Enable interrupt                                           */
  __IOM uint32_t  INTENCLR;                     /*!< (@ 0x00000308) Disable interrupt                                          */
  __IM  uint32_t  RESERVED7[93];
  __IOM uint32_t  ERRORSRC;                     /*!< (@ 0x00000480) Error source                                               */
  __IM  uint32_t  RESERVED8[31];
  __IOM uint32_t  ENABLE;                       /*!< (@ 0x00000500) Enable UART                                                */
  __IM  uint32_t  RESERVED9;
  __IOM UART_PSEL_Type PSEL;                    /*!< (@ 0x00000508) Unspecified                                                */
  __IM  uint32_t  RXD;                          /*!< (@ 0x00000518) RXD register                                               */
  __OM  uint32_t  TXD;                          /*!< (@ 0x0000051C) TXD register                                               */
  __IM  uint32_t  RESERVED10;
  __IOM uint32_t  BAUDRATE;                     /*!< (@ 0x00000524) Baud rate. Accuracy depends on the HFCLK source
                                                                    selected.                                                  */
  __IM  uint32_t  RESERVED11[17];
  __IOM uint32_t  CONFIG;                       /*!< (@ 0x0000056C) Configuration of parity and hardware flow control          */
} NRF_UART_Type;                                /*!< Size = 1392 (0x570)                                                       */



/* =========================================================================================================================== */
/* ================                                          UARTE0                                           ================ */
/* =========================================================================================================================== */


/**
  * @brief UART with EasyDMA 0 (UARTE0)
  */

typedef struct {                                /*!< (@ 0x40002000) UARTE0 Structure                                           */
  __OM  uint32_t  TASKS_STARTRX;                /*!< (@ 0x00000000) Start UART receiver                                        */
  __OM  uint32_t  TASKS_STOPRX;                 /*!< (@ 0x00000004) Stop UART receiver                                         */
  __OM  uint32_t  TASKS_STARTTX;                /*!< (@ 0x00000008) Start UART transmitter                                     */
  __OM  uint32_t  TASKS_STOPTX;                 /*!< (@ 0x0000000C) Stop UART transmitter                                      */
  __IM  uint32_t  RESERVED[7];
  __OM  uint32_t  TASKS_FLUSHRX;                /*!< (@ 0x0000002C) Flush RX FIFO into RX buffer                               */
  __IM  uint32_t  RESERVED1[52];
  __IOM uint32_t  EVENTS_CTS;                   /*!< (@ 0x00000100) CTS is activated (set low). Clear To Send.                 */
  __IOM uint32_t  EVENTS_NCTS;                  /*!< (@ 0x00000104) CTS is deactivated (set high). Not Clear To Send.          */
  __IOM uint32_t  EVENTS_RXDRDY;                /*!< (@ 0x00000108) Data received in RXD (but potentially not yet
                                                                    transferred to Data RAM)                                   */
  __IM  uint32_t  RESERVED2;
  __IOM uint32_t  EVENTS_ENDRX;                 /*!< (@ 0x00000110) Receive buffer is filled up                                */
  __IM  uint32_t  RESERVED3[2];
  __IOM uint32_t  EVENTS_TXDRDY;                /*!< (@ 0x0000011C) Data sent from TXD                                         */
  __IOM uint32_t  EVENTS_ENDTX;                 /*!< (@ 0x00000120) Last TX byte transmitted                                   */
  __IOM uint32_t  EVENTS_ERROR;                 /*!< (@ 0x00000124) Error detected                                             */
  __IM  uint32_t  RESERVED4[7];
  __IOM uint32_t  EVENTS_RXTO;                  /*!< (@ 0x00000144) Receiver timeout                                           */
  __IM  uint32_t  RESERVED5;
  __IOM uint32_t  EVENTS_RXSTARTED;             /*!< (@ 0x0000014C) UART receiver has started                                  */
  __IOM uint32_t  EVENTS_TXSTARTED;             /*!< (@ 0x00000150) UART transmitter has started                               */
  __IM  uint32_t  RESERVED6;
  __IOM uint32_t  EVENTS_TXSTOPPED;             /*!< (@ 0x00000158) Transmitter stopped                                        */
  __IM  uint32_t  RESERVED7[41];
  __IOM uint32_t  SHORTS;                       /*!< (@ 0x00000200) Shortcuts between local events and tasks                   */
  __IM  uint32_t  RESERVED8[63];
  __IOM uint32_t  INTEN;                        /*!< (@ 0x00000300) Enable or disable interrupt                                */
  __IOM uint32_t  INTENSET;                     /*!< (@ 0x00000304) Enable interrupt                                           */
  __IOM uint32_t  INTENCLR;                     /*!< (@ 0x00000308) Disable interrupt                                          */
  __IM  uint32_t  RESERVED9[93];
  __IOM uint32_t  ERRORSRC;                     /*!< (@ 0x00000480) Error source Note : this register is read / write
                                                                    one to clear.                                              */
  __IM  uint32_t  RESERVED10[31];
  __IOM uint32_t  ENABLE;                       /*!< (@ 0x00000500) Enable UART                                                */
  __IM  uint32_t  RESERVED11;
  __IOM UARTE_PSEL_Type PSEL;                   /*!< (@ 0x00000508) Unspecified                                                */
  __IM  uint32_t  RESERVED12[3];
  __IOM uint32_t  BAUDRATE;                     /*!< (@ 0x00000524) Baud rate. Accuracy depends on the HFCLK source
                                                                    selected.                                                  */
  __IM  uint32_t  RESERVED13[3];
  __IOM UARTE_RXD_Type RXD;                     /*!< (@ 0x00000534) RXD EasyDMA channel                                        */
  __IM  uint32_t  RESERVED14;
  __IOM UARTE_TXD_Type TXD;                     /*!< (@ 0x00000544) TXD EasyDMA channel                                        */
  __IM  uint32_t  RESERVED15[7];
  __IOM uint32_t  CONFIG;                       /*!< (@ 0x0000056C) Configuration of parity and hardware flow control          */
} NRF_UARTE_Type;                               /*!< Size = 1392 (0x570)                                                       */



/* =========================================================================================================================== */
/* ================                                           SPI0                                            ================ */
/* =========================================================================================================================== */


/**
  * @brief Serial Peripheral Interface 0 (SPI0)
  */

typedef struct {                                /*!< (@ 0x40003000) SPI0 Structure                                             */
  __IM  uint32_t  RESERVED[66];
  __IOM uint32_t  EVENTS_READY;                 /*!< (@ 0x00000108) TXD byte sent and RXD byte received                        */
  __IM  uint32_t  RESERVED1[126];
  __IOM uint32_t  INTENSET;                     /*!< (@ 0x00000304) Enable interrupt                                           */
  __IOM uint32_t  INTENCLR;                     /*!< (@ 0x00000308) Disable interrupt                                          */
  __IM  uint32_t  RESERVED2[125];
  __IOM uint32_t  ENABLE;                       /*!< (@ 0x00000500) Enable SPI                                                 */
  __IM  uint32_t  RESERVED3;
  __IOM SPI_PSEL_Type PSEL;                     /*!< (@ 0x00000508) Unspecified                                                */
  __IM  uint32_t  RESERVED4;
  __IM  uint32_t  RXD;                          /*!< (@ 0x00000518) RXD register                                               */
  __IOM uint32_t  TXD;                          /*!< (@ 0x0000051C) TXD register                                               */
  __IM  uint32_t  RESERVED5;
  __IOM uint32_t  FREQUENCY;                    /*!< (@ 0x00000524) SPI frequency. Accuracy depends on the HFCLK
                                                                    source selected.                                           */
  __IM  uint32_t  RESERVED6[11];
  __IOM uint32_t  CONFIG;                       /*!< (@ 0x00000554) Configuration register                                     */
} NRF_SPI_Type;                                 /*!< Size = 1368 (0x558)                                                       */



/* =========================================================================================================================== */
/* ================                                           SPIM0                                           ================ */
/* =========================================================================================================================== */


/**
  * @brief Serial Peripheral Interface Master with EasyDMA 0 (SPIM0)
  */

typedef struct {                                /*!< (@ 0x40003000) SPIM0 Structure                                            */
  __IM  uint32_t  RESERVED[4];
  __OM  uint32_t  TASKS_START;                  /*!< (@ 0x00000010) Start SPI transaction                                      */
  __OM  uint32_t  TASKS_STOP;                   /*!< (@ 0x00000014) Stop SPI transaction                                       */
  __IM  uint32_t  RESERVED1;
  __OM  uint32_t  TASKS_SUSPEND;                /*!< (@ 0x0000001C) Suspend SPI transaction                                    */
  __OM  uint32_t  TASKS_RESUME;                 /*!< (@ 0x00000020) Resume SPI transaction                                     */
  __IM  uint32_t  RESERVED2[56];
  __IOM uint32_t  EVENTS_STOPPED;               /*!< (@ 0x00000104) SPI transaction has stopped                                */
  __IM  uint32_t  RESERVED3[2];
  __IOM uint32_t  EVENTS_ENDRX;                 /*!< (@ 0x00000110) End of RXD buffer reached                                  */
  __IM  uint32_t  RESERVED4;
  __IOM uint32_t  EVENTS_END;                   /*!< (@ 0x00000118) End of RXD buffer and TXD buffer reached                   */
  __IM  uint32_t  RESERVED5;
  __IOM uint32_t  EVENTS_ENDTX;                 /*!< (@ 0x00000120) End of TXD buffer reached                                  */
  __IM  uint32_t  RESERVED6[10];
  __IOM uint32_t  EVENTS_STARTED;               /*!< (@ 0x0000014C) Transaction started                                        */
  __IM  uint32_t  RESERVED7[44];
  __IOM uint32_t  SHORTS;                       /*!< (@ 0x00000200) Shortcuts between local events and tasks                   */
  __IM  uint32_t  RESERVED8[64];
  __IOM uint32_t  INTENSET;                     /*!< (@ 0x00000304) Enable interrupt                                           */
  __IOM uint32_t  INTENCLR;                     /*!< (@ 0x00000308) Disable interrupt                                          */
  __IM  uint32_t  RESERVED9[61];
  __IOM uint32_t  STALLSTAT;                    /*!< (@ 0x00000400) Stall status for EasyDMA RAM accesses. The fields
                                                                    in this register is set to STALL by hardware
                                                                    whenever a stall occurres and can be cleared
                                                                    (set to NOSTALL) by the CPU.                               */
  __IM  uint32_t  RESERVED10[63];
  __IOM uint32_t  ENABLE;                       /*!< (@ 0x00000500) Enable SPIM                                                */
  __IM  uint32_t  RESERVED11;
  __IOM SPIM_PSEL_Type PSEL;                    /*!< (@ 0x00000508) Unspecified                                                */
  __IM  uint32_t  RESERVED12[3];
  __IOM uint32_t  FREQUENCY;                    /*!< (@ 0x00000524) SPI frequency. Accuracy depends on the HFCLK
                                                                    source selected.                                           */
  __IM  uint32_t  RESERVED13[3];
  __IOM SPIM_RXD_Type RXD;                      /*!< (@ 0x00000534) RXD EasyDMA channel                                        */
  __IOM SPIM_TXD_Type TXD;                      /*!< (@ 0x00000544) TXD EasyDMA channel                                        */
  __IOM uint32_t  CONFIG;                       /*!< (@ 0x00000554) Configuration register                                     */
  __IM  uint32_t  RESERVED14[2];
  __IOM SPIM_IFTIMING_Type IFTIMING;            /*!< (@ 0x00000560) Unspecified                                                */
  __IOM uint32_t  CSNPOL;                       /*!< (@ 0x00000568) Polarity of CSN output                                     */
  __IOM uint32_t  PSELDCX;                      /*!< (@ 0x0000056C) Pin select for DCX signal                                  */
  __IOM uint32_t  DCXCNT;                       /*!< (@ 0x00000570) DCX configuration                                          */
  __IM  uint32_t  RESERVED15[19];
  __IOM uint32_t  ORC;                          /*!< (@ 0x000005C0) Byte transmitted after TXD.MAXCNT bytes have
                                                                    been transmitted in the case when RXD.MAXCNT
                                                                    is greater than TXD.MAXCNT                                 */
} NRF_SPIM_Type;                                /*!< Size = 1476 (0x5c4)                                                       */



/* =========================================================================================================================== */
/* ================                                           SPIS0                                           ================ */
/* =========================================================================================================================== */


/**
  * @brief SPI Slave 0 (SPIS0)
  */

typedef struct {                                /*!< (@ 0x40003000) SPIS0 Structure                                            */
  __IM  uint32_t  RESERVED[9];
  __OM  uint32_t  TASKS_ACQUIRE;                /*!< (@ 0x00000024) Acquire SPI semaphore                                      */
  __OM  uint32_t  TASKS_RELEASE;                /*!< (@ 0x00000028) Release SPI semaphore, enabling the SPI slave
                                                                    to acquire it                                              */
  __IM  uint32_t  RESERVED1[54];
  __IOM uint32_t  EVENTS_END;                   /*!< (@ 0x00000104) Granted transaction completed                              */
  __IM  uint32_t  RESERVED2[2];
  __IOM uint32_t  EVENTS_ENDRX;                 /*!< (@ 0x00000110) End of RXD buffer reached                                  */
  __IM  uint32_t  RESERVED3[5];
  __IOM uint32_t  EVENTS_ACQUIRED;              /*!< (@ 0x00000128) Semaphore acquired                                         */
  __IM  uint32_t  RESERVED4[53];
  __IOM uint32_t  SHORTS;                       /*!< (@ 0x00000200) Shortcuts between local events and tasks                   */
  __IM  uint32_t  RESERVED5[64];
  __IOM uint32_t  INTENSET;                     /*!< (@ 0x00000304) Enable interrupt                                           */
  __IOM uint32_t  INTENCLR;                     /*!< (@ 0x00000308) Disable interrupt                                          */
  __IM  uint32_t  RESERVED6[61];
  __IM  uint32_t  SEMSTAT;                      /*!< (@ 0x00000400) Semaphore status register                                  */
  __IM  uint32_t  RESERVED7[15];
  __IOM uint32_t  STATUS;                       /*!< (@ 0x00000440) Status from last transaction                               */
  __IM  uint32_t  RESERVED8[47];
  __IOM uint32_t  ENABLE;                       /*!< (@ 0x00000500) Enable SPI slave                                           */
  __IM  uint32_t  RESERVED9;
  __IOM SPIS_PSEL_Type PSEL;                    /*!< (@ 0x00000508) Unspecified                                                */
  __IM  uint32_t  RESERVED10[7];
  __IOM SPIS_RXD_Type RXD;                      /*!< (@ 0x00000534) Unspecified                                                */
  __IOM SPIS_TXD_Type TXD;                      /*!< (@ 0x00000544) Unspecified                                                */
  __IOM uint32_t  CONFIG;                       /*!< (@ 0x00000554) Configuration register                                     */
  __IM  uint32_t  RESERVED11;
  __IOM uint32_t  DEF;                          /*!< (@ 0x0000055C) Default character. Character clocked out in case
                                                                    of an ignored transaction.                                 */
  __IM  uint32_t  RESERVED12[24];
  __IOM uint32_t  ORC;                          /*!< (@ 0x000005C0) Over-read character                                        */
} NRF_SPIS_Type;                                /*!< Size = 1476 (0x5c4)                                                       */



/* =========================================================================================================================== */
/* ================                                           TWI0                                            ================ */
/* =========================================================================================================================== */


/**
  * @brief I2C compatible Two-Wire Interface 0 (TWI0)
  */

typedef struct {                                /*!< (@ 0x40003000) TWI0 Structure                                             */
  __OM  uint32_t  TASKS_STARTRX;                /*!< (@ 0x00000000) Start TWI receive sequence                                 */
  __IM  uint32_t  RESERVED;
  __OM  uint32_t  TASKS_STARTTX;                /*!< (@ 0x00000008) Start TWI transmit sequence                                */
  __IM  uint32_t  RESERVED1[2];
  __OM  uint32_t  TASKS_STOP;                   /*!< (@ 0x00000014) Stop TWI transaction                                       */
  __IM  uint32_t  RESERVED2;
  __OM  uint32_t  TASKS_SUSPEND;                /*!< (@ 0x0000001C) Suspend TWI transaction                                    */
  __OM  uint32_t  TASKS_RESUME;                 /*!< (@ 0x00000020) Resume TWI transaction                                     */
  __IM  uint32_t  RESERVED3[56];
  __IOM uint32_t  EVENTS_STOPPED;               /*!< (@ 0x00000104) TWI stopped                                                */
  __IOM uint32_t  EVENTS_RXDREADY;              /*!< (@ 0x00000108) TWI RXD byte received                                      */
  __IM  uint32_t  RESERVED4[4];
  __IOM uint32_t  EVENTS_TXDSENT;               /*!< (@ 0x0000011C) TWI TXD byte sent                                          */
  __IM  uint32_t  RESERVED5;
  __IOM uint32_t  EVENTS_ERROR;                 /*!< (@ 0x00000124) TWI error                                                  */
  __IM  uint32_t  RESERVED6[4];
  __IOM uint32_t  EVENTS_BB;                    /*!< (@ 0x00000138) TWI byte boundary, generated before each byte
                                                                    that is sent or received                                   */
  __IM  uint32_t  RESERVED7[3];
  __IOM uint32_t  EVENTS_SUSPENDED;             /*!< (@ 0x00000148) TWI entered the suspended state                            */
  __IM  uint32_t  RESERVED8[45];
  __IOM uint32_t  SHORTS;                       /*!< (@ 0x00000200) Shortcuts between local events and tasks                   */
  __IM  uint32_t  RESERVED9[64];
  __IOM uint32_t  INTENSET;                     /*!< (@ 0x00000304) Enable interrupt                                           */
  __IOM uint32_t  INTENCLR;                     /*!< (@ 0x00000308) Disable interrupt                                          */
  __IM  uint32_t  RESERVED10[110];
  __IOM uint32_t  ERRORSRC;                     /*!< (@ 0x000004C4) Error source                                               */
  __IM  uint32_t  RESERVED11[14];
  __IOM uint32_t  ENABLE;                       /*!< (@ 0x00000500) Enable TWI                                                 */
  __IM  uint32_t  RESERVED12;
  __IOM TWI_PSEL_Type PSEL;                     /*!< (@ 0x00000508) Unspecified                                                */
  __IM  uint32_t  RESERVED13[2];
  __IM  uint32_t  RXD;                          /*!< (@ 0x00000518) RXD register                                               */
  __IOM uint32_t  TXD;                          /*!< (@ 0x0000051C) TXD register                                               */
  __IM  uint32_t  RESERVED14;
  __IOM uint32_t  FREQUENCY;                    /*!< (@ 0x00000524) TWI frequency. Accuracy depends on the HFCLK
                                                                    source selected.                                           */
  __IM  uint32_t  RESERVED15[24];
  __IOM uint32_t  ADDRESS;                      /*!< (@ 0x00000588) Address used in the TWI transfer                           */
} NRF_TWI_Type;                                 /*!< Size = 1420 (0x58c)                                                       */



/* =========================================================================================================================== */
/* ================                                           TWIM0                                           ================ */
/* =========================================================================================================================== */


/**
  * @brief I2C compatible Two-Wire Master Interface with EasyDMA 0 (TWIM0)
  */

typedef struct {                                /*!< (@ 0x40003000) TWIM0 Structure                                            */
  __OM  uint32_t  TASKS_STARTRX;                /*!< (@ 0x00000000) Start TWI receive sequence                                 */
  __IM  uint32_t  RESERVED;
  __OM  uint32_t  TASKS_STARTTX;                /*!< (@ 0x00000008) Start TWI transmit sequence                                */
  __IM  uint32_t  RESERVED1[2];
  __OM  uint32_t  TASKS_STOP;                   /*!< (@ 0x00000014) Stop TWI transaction. Must be issued while the
                                                                    TWI master is not suspended.                               */
  __IM  uint32_t  RESERVED2;
  __OM  uint32_t  TASKS_SUSPEND;                /*!< (@ 0x0000001C) Suspend TWI transaction                                    */
  __OM  uint32_t  TASKS_RESUME;                 /*!< (@ 0x00000020) Resume TWI transaction                                     */
  __IM  uint32_t  RESERVED3[56];
  __IOM uint32_t  EVENTS_STOPPED;               /*!< (@ 0x00000104) TWI stopped                                                */
  __IM  uint32_t  RESERVED4[7];
  __IOM uint32_t  EVENTS_ERROR;                 /*!< (@ 0x00000124) TWI error                                                  */
  __IM  uint32_t  RESERVED5[8];
  __IOM uint32_t  EVENTS_SUSPENDED;             /*!< (@ 0x00000148) Last byte has been sent out after the SUSPEND
                                                                    task has been issued, TWI traffic is now
                                                                    suspended.                                                 */
  __IOM uint32_t  EVENTS_RXSTARTED;             /*!< (@ 0x0000014C) Receive sequence started                                   */
  __IOM uint32_t  EVENTS_TXSTARTED;             /*!< (@ 0x00000150) Transmit sequence started                                  */
  __IM  uint32_t  RESERVED6[2];
  __IOM uint32_t  EVENTS_LASTRX;                /*!< (@ 0x0000015C) Byte boundary, starting to receive the last byte           */
  __IOM uint32_t  EVENTS_LASTTX;                /*!< (@ 0x00000160) Byte boundary, starting to transmit the last
                                                                    byte                                                       */
  __IM  uint32_t  RESERVED7[39];
  __IOM uint32_t  SHORTS;                       /*!< (@ 0x00000200) Shortcuts between local events and tasks                   */
  __IM  uint32_t  RESERVED8[63];
  __IOM uint32_t  INTEN;                        /*!< (@ 0x00000300) Enable or disable interrupt                                */
  __IOM uint32_t  INTENSET;                     /*!< (@ 0x00000304) Enable interrupt                                           */
  __IOM uint32_t  INTENCLR;                     /*!< (@ 0x00000308) Disable interrupt                                          */
  __IM  uint32_t  RESERVED9[110];
  __IOM uint32_t  ERRORSRC;                     /*!< (@ 0x000004C4) Error source                                               */
  __IM  uint32_t  RESERVED10[14];
  __IOM uint32_t  ENABLE;                       /*!< (@ 0x00000500) Enable TWIM                                                */
  __IM  uint32_t  RESERVED11;
  __IOM TWIM_PSEL_Type PSEL;                    /*!< (@ 0x00000508) Unspecified                                                */
  __IM  uint32_t  RESERVED12[5];
  __IOM uint32_t  FREQUENCY;                    /*!< (@ 0x00000524) TWI frequency. Accuracy depends on the HFCLK
                                                                    source selected.                                           */
  __IM  uint32_t  RESERVED13[3];
  __IOM TWIM_RXD_Type RXD;                      /*!< (@ 0x00000534) RXD EasyDMA channel                                        */
  __IOM TWIM_TXD_Type TXD;                      /*!< (@ 0x00000544) TXD EasyDMA channel                                        */
  __IM  uint32_t  RESERVED14[13];
  __IOM uint32_t  ADDRESS;                      /*!< (@ 0x00000588) Address used in the TWI transfer                           */
} NRF_TWIM_Type;                                /*!< Size = 1420 (0x58c)                                                       */



/* =========================================================================================================================== */
/* ================                                           TWIS0                                           ================ */
/* =========================================================================================================================== */


/**
  * @brief I2C compatible Two-Wire Slave Interface with EasyDMA 0 (TWIS0)
  */

typedef struct {                                /*!< (@ 0x40003000) TWIS0 Structure                                            */
  __IM  uint32_t  RESERVED[5];
  __OM  uint32_t  TASKS_STOP;                   /*!< (@ 0x00000014) Stop TWI transaction                                       */
  __IM  uint32_t  RESERVED1;
  __OM  uint32_t  TASKS_SUSPEND;                /*!< (@ 0x0000001C) Suspend TWI transaction                                    */
  __OM  uint32_t  TASKS_RESUME;                 /*!< (@ 0x00000020) Resume TWI transaction                                     */
  __IM  uint32_t  RESERVED2[3];
  __OM  uint32_t  TASKS_PREPARERX;              /*!< (@ 0x00000030) Prepare the TWI slave to respond to a write command        */
  __OM  uint32_t  TASKS_PREPARETX;              /*!< (@ 0x00000034) Prepare the TWI slave to respond to a read command         */
  __IM  uint32_t  RESERVED3[51];
  __IOM uint32_t  EVENTS_STOPPED;               /*!< (@ 0x00000104) TWI stopped                                                */
  __IM  uint32_t  RESERVED4[7];
  __IOM uint32_t  EVENTS_ERROR;                 /*!< (@ 0x00000124) TWI error                                                  */
  __IM  uint32_t  RESERVED5[9];
  __IOM uint32_t  EVENTS_RXSTARTED;             /*!< (@ 0x0000014C) Receive sequence started                                   */
  __IOM uint32_t  EVENTS_TXSTARTED;             /*!< (@ 0x00000150) Transmit sequence started                                  */
  __IM  uint32_t  RESERVED6[4];
  __IOM uint32_t  EVENTS_WRITE;                 /*!< (@ 0x00000164) Write command received                                     */
  __IOM uint32_t  EVENTS_READ;                  /*!< (@ 0x00000168) Read command received                                      */
  __IM  uint32_t  RESERVED7[37];
  __IOM uint32_t  SHORTS;                       /*!< (@ 0x00000200) Shortcuts between local events and tasks                   */
  __IM  uint32_t  RESERVED8[63];
  __IOM uint32_t  INTEN;                        /*!< (@ 0x00000300) Enable or disable interrupt                                */
  __IOM uint32_t  INTENSET;                     /*!< (@ 0x00000304) Enable interrupt                                           */
  __IOM uint32_t  INTENCLR;                     /*!< (@ 0x00000308) Disable interrupt                                          */
  __IM  uint32_t  RESERVED9[113];
  __IOM uint32_t  ERRORSRC;                     /*!< (@ 0x000004D0) Error source                                               */
  __IM  uint32_t  MATCH;                        /*!< (@ 0x000004D4) Status register indicating which address had
                                                                    a match                                                    */
  __IM  uint32_t  RESERVED10[10];
  __IOM uint32_t  ENABLE;                       /*!< (@ 0x00000500) Enable TWIS                                                */
  __IM  uint32_t  RESERVED11;
  __IOM TWIS_PSEL_Type PSEL;                    /*!< (@ 0x00000508) Unspecified                                                */
  __IM  uint32_t  RESERVED12[9];
  __IOM TWIS_RXD_Type RXD;                      /*!< (@ 0x00000534) RXD EasyDMA channel                                        */
  __IOM TWIS_TXD_Type TXD;                      /*!< (@ 0x00000544) TXD EasyDMA channel                                        */
  __IM  uint32_t  RESERVED13[13];
  __IOM uint32_t  ADDRESS[2];                   /*!< (@ 0x00000588) Description collection: TWI slave address n                */
  __IM  uint32_t  RESERVED14;
  __IOM uint32_t  CONFIG;                       /*!< (@ 0x00000594) Configuration register for the address match
                                                                    mechanism                                                  */
  __IM  uint32_t  RESERVED15[10];
  __IOM uint32_t  ORC;                          /*!< (@ 0x000005C0) Over-read character. Character sent out in case
                                                                    of an over-read of the transmit buffer.                    */
} NRF_TWIS_Type;                                /*!< Size = 1476 (0x5c4)                                                       */



/* =========================================================================================================================== */
/* ================                                           NFCT                                            ================ */
/* =========================================================================================================================== */


/**
  * @brief NFC-A compatible radio (NFCT)
  */

typedef struct {                                /*!< (@ 0x40005000) NFCT Structure                                             */
  __OM  uint32_t  TASKS_ACTIVATE;               /*!< (@ 0x00000000) Activate NFCT peripheral for incoming and outgoing
                                                                    frames, change state to activated                          */
  __OM  uint32_t  TASKS_DISABLE;                /*!< (@ 0x00000004) Disable NFCT peripheral                                    */
  __OM  uint32_t  TASKS_SENSE;                  /*!< (@ 0x00000008) Enable NFC sense field mode, change state to
                                                                    sense mode                                                 */
  __OM  uint32_t  TASKS_STARTTX;                /*!< (@ 0x0000000C) Start transmission of an outgoing frame, change
                                                                    state to transmit                                          */
  __IM  uint32_t  RESERVED[3];
  __OM  uint32_t  TASKS_ENABLERXDATA;           /*!< (@ 0x0000001C) Initializes the EasyDMA for receive.                       */
  __IM  uint32_t  RESERVED1;
  __OM  uint32_t  TASKS_GOIDLE;                 /*!< (@ 0x00000024) Force state machine to IDLE state                          */
  __OM  uint32_t  TASKS_GOSLEEP;                /*!< (@ 0x00000028) Force state machine to SLEEP_A state                       */
  __IM  uint32_t  RESERVED2[53];
  __IOM uint32_t  EVENTS_READY;                 /*!< (@ 0x00000100) The NFCT peripheral is ready to receive and send
                                                                    frames                                                     */
  __IOM uint32_t  EVENTS_FIELDDETECTED;         /*!< (@ 0x00000104) Remote NFC field detected                                  */
  __IOM uint32_t  EVENTS_FIELDLOST;             /*!< (@ 0x00000108) Remote NFC field lost                                      */
  __IOM uint32_t  EVENTS_TXFRAMESTART;          /*!< (@ 0x0000010C) Marks the start of the first symbol of a transmitted
                                                                    frame                                                      */
  __IOM uint32_t  EVENTS_TXFRAMEEND;            /*!< (@ 0x00000110) Marks the end of the last transmitted on-air
                                                                    symbol of a frame                                          */
  __IOM uint32_t  EVENTS_RXFRAMESTART;          /*!< (@ 0x00000114) Marks the end of the first symbol of a received
                                                                    frame                                                      */
  __IOM uint32_t  EVENTS_RXFRAMEEND;            /*!< (@ 0x00000118) Received data has been checked (CRC, parity)
                                                                    and transferred to RAM, and EasyDMA has
                                                                    ended accessing the RX buffer                              */
  __IOM uint32_t  EVENTS_ERROR;                 /*!< (@ 0x0000011C) NFC error reported. The ERRORSTATUS register
                                                                    contains details on the source of the error.               */
  __IM  uint32_t  RESERVED3[2];
  __IOM uint32_t  EVENTS_RXERROR;               /*!< (@ 0x00000128) NFC RX frame error reported. The FRAMESTATUS.RX
                                                                    register contains details on the source
                                                                    of the error.                                              */
  __IOM uint32_t  EVENTS_ENDRX;                 /*!< (@ 0x0000012C) RX buffer (as defined by PACKETPTR and MAXLEN)
                                                                    in Data RAM full.                                          */
  __IOM uint32_t  EVENTS_ENDTX;                 /*!< (@ 0x00000130) Transmission of data in RAM has ended, and EasyDMA
                                                                    has ended accessing the TX buffer                          */
  __IM  uint32_t  RESERVED4;
  __IOM uint32_t  EVENTS_AUTOCOLRESSTARTED;     /*!< (@ 0x00000138) Auto collision resolution process has started              */
  __IM  uint32_t  RESERVED5[3];
  __IOM uint32_t  EVENTS_COLLISION;             /*!< (@ 0x00000148) NFC auto collision resolution error reported.              */
  __IOM uint32_t  EVENTS_SELECTED;              /*!< (@ 0x0000014C) NFC auto collision resolution successfully completed       */
  __IOM uint32_t  EVENTS_STARTED;               /*!< (@ 0x00000150) EasyDMA is ready to receive or send frames.                */
  __IM  uint32_t  RESERVED6[43];
  __IOM uint32_t  SHORTS;                       /*!< (@ 0x00000200) Shortcuts between local events and tasks                   */
  __IM  uint32_t  RESERVED7[63];
  __IOM uint32_t  INTEN;                        /*!< (@ 0x00000300) Enable or disable interrupt                                */
  __IOM uint32_t  INTENSET;                     /*!< (@ 0x00000304) Enable interrupt                                           */
  __IOM uint32_t  INTENCLR;                     /*!< (@ 0x00000308) Disable interrupt                                          */
  __IM  uint32_t  RESERVED8[62];
  __IOM uint32_t  ERRORSTATUS;                  /*!< (@ 0x00000404) NFC Error Status register                                  */
  __IM  uint32_t  RESERVED9;
  __IOM NFCT_FRAMESTATUS_Type FRAMESTATUS;      /*!< (@ 0x0000040C) Unspecified                                                */
  __IM  uint32_t  NFCTAGSTATE;                  /*!< (@ 0x00000410) NfcTag state register                                      */
  __IM  uint32_t  RESERVED10[3];
  __IM  uint32_t  SLEEPSTATE;                   /*!< (@ 0x00000420) Sleep state during automatic collision resolution          */
  __IM  uint32_t  RESERVED11[6];
  __IM  uint32_t  FIELDPRESENT;                 /*!< (@ 0x0000043C) Indicates the presence or not of a valid field             */
  __IM  uint32_t  RESERVED12[49];
  __IOM uint32_t  FRAMEDELAYMIN;                /*!< (@ 0x00000504) Minimum frame delay                                        */
  __IOM uint32_t  FRAMEDELAYMAX;                /*!< (@ 0x00000508) Maximum frame delay                                        */
  __IOM uint32_t  FRAMEDELAYMODE;               /*!< (@ 0x0000050C) Configuration register for the Frame Delay Timer           */
  __IOM uint32_t  PACKETPTR;                    /*!< (@ 0x00000510) Packet pointer for TXD and RXD data storage in
                                                                    Data RAM                                                   */
  __IOM uint32_t  MAXLEN;                       /*!< (@ 0x00000514) Size of the RAM buffer allocated to TXD and RXD
                                                                    data storage each                                          */
  __IOM NFCT_TXD_Type TXD;                      /*!< (@ 0x00000518) Unspecified                                                */
  __IOM NFCT_RXD_Type RXD;                      /*!< (@ 0x00000520) Unspecified                                                */
  __IM  uint32_t  RESERVED13[26];
  __IOM uint32_t  NFCID1_LAST;                  /*!< (@ 0x00000590) Last NFCID1 part (4, 7 or 10 bytes ID)                     */
  __IOM uint32_t  NFCID1_2ND_LAST;              /*!< (@ 0x00000594) Second last NFCID1 part (7 or 10 bytes ID)                 */
  __IOM uint32_t  NFCID1_3RD_LAST;              /*!< (@ 0x00000598) Third last NFCID1 part (10 bytes ID)                       */
  __IOM uint32_t  AUTOCOLRESCONFIG;             /*!< (@ 0x0000059C) Controls the auto collision resolution function.
                                                                    This setting must be done before the NFCT
                                                                    peripheral is enabled.                                     */
  __IOM uint32_t  SENSRES;                      /*!< (@ 0x000005A0) NFC-A SENS_RES auto-response settings                      */
  __IOM uint32_t  SELRES;                       /*!< (@ 0x000005A4) NFC-A SEL_RES auto-response settings                       */
} NRF_NFCT_Type;                                /*!< Size = 1448 (0x5a8)                                                       */



/* =========================================================================================================================== */
/* ================                                          GPIOTE                                           ================ */
/* =========================================================================================================================== */


/**
  * @brief GPIO Tasks and Events (GPIOTE)
  */

typedef struct {                                /*!< (@ 0x40006000) GPIOTE Structure                                           */
  __OM  uint32_t  TASKS_OUT[8];                 /*!< (@ 0x00000000) Description collection: Task for writing to pin
                                                                    specified in CONFIG[n].PSEL. Action on pin
                                                                    is configured in CONFIG[n].POLARITY.                       */
  __IM  uint32_t  RESERVED[4];
  __OM  uint32_t  TASKS_SET[8];                 /*!< (@ 0x00000030) Description collection: Task for writing to pin
                                                                    specified in CONFIG[n].PSEL. Action on pin
                                                                    is to set it high.                                         */
  __IM  uint32_t  RESERVED1[4];
  __OM  uint32_t  TASKS_CLR[8];                 /*!< (@ 0x00000060) Description collection: Task for writing to pin
                                                                    specified in CONFIG[n].PSEL. Action on pin
                                                                    is to set it low.                                          */
  __IM  uint32_t  RESERVED2[32];
  __IOM uint32_t  EVENTS_IN[8];                 /*!< (@ 0x00000100) Description collection: Event generated from
                                                                    pin specified in CONFIG[n].PSEL                            */
  __IM  uint32_t  RESERVED3[23];
  __IOM uint32_t  EVENTS_PORT;                  /*!< (@ 0x0000017C) Event generated from multiple input GPIO pins
                                                                    with SENSE mechanism enabled                               */
  __IM  uint32_t  RESERVED4[97];
  __IOM uint32_t  INTENSET;                     /*!< (@ 0x00000304) Enable interrupt                                           */
  __IOM uint32_t  INTENCLR;                     /*!< (@ 0x00000308) Disable interrupt                                          */
  __IM  uint32_t  RESERVED5[129];
  __IOM uint32_t  CONFIG[8];                    /*!< (@ 0x00000510) Description collection: Configuration for OUT[n],
                                                                    SET[n] and CLR[n] tasks and IN[n] event                    */
} NRF_GPIOTE_Type;                              /*!< Size = 1328 (0x530)                                                       */



/* =========================================================================================================================== */
/* ================                                           SAADC                                           ================ */
/* =========================================================================================================================== */


/**
  * @brief Successive approximation register (SAR) analog-to-digital converter (SAADC)
  */

typedef struct {                                /*!< (@ 0x40007000) SAADC Structure                                            */
  __OM  uint32_t  TASKS_START;                  /*!< (@ 0x00000000) Starts the SAADC and prepares the result buffer
                                                                    in RAM                                                     */
  __OM  uint32_t  TASKS_SAMPLE;                 /*!< (@ 0x00000004) Takes one SAADC sample                                     */
  __OM  uint32_t  TASKS_STOP;                   /*!< (@ 0x00000008) Stops the SAADC and terminates all on-going conversions    */
  __OM  uint32_t  TASKS_CALIBRATEOFFSET;        /*!< (@ 0x0000000C) Starts offset auto-calibration                             */
  __IM  uint32_t  RESERVED[60];
  __IOM uint32_t  EVENTS_STARTED;               /*!< (@ 0x00000100) The SAADC has started                                      */
  __IOM uint32_t  EVENTS_END;                   /*!< (@ 0x00000104) The SAADC has filled up the result buffer                  */
  __IOM uint32_t  EVENTS_DONE;                  /*!< (@ 0x00000108) A conversion task has been completed. Depending
                                                                    on the configuration, multiple conversions
                                                                    might be needed for a result to be transferred
                                                                    to RAM.                                                    */
  __IOM uint32_t  EVENTS_RESULTDONE;            /*!< (@ 0x0000010C) Result ready for transfer to RAM                           */
  __IOM uint32_t  EVENTS_CALIBRATEDONE;         /*!< (@ 0x00000110) Calibration is complete                                    */
  __IOM uint32_t  EVENTS_STOPPED;               /*!< (@ 0x00000114) The SAADC has stopped                                      */
  __IOM SAADC_EVENTS_CH_Type EVENTS_CH[8];      /*!< (@ 0x00000118) Peripheral events.                                         */
  __IM  uint32_t  RESERVED1[106];
  __IOM uint32_t  INTEN;                        /*!< (@ 0x00000300) Enable or disable interrupt                                */
  __IOM uint32_t  INTENSET;                     /*!< (@ 0x00000304) Enable interrupt                                           */
  __IOM uint32_t  INTENCLR;                     /*!< (@ 0x00000308) Disable interrupt                                          */
  __IM  uint32_t  RESERVED2[61];
  __IM  uint32_t  STATUS;                       /*!< (@ 0x00000400) Status                                                     */
  __IM  uint32_t  RESERVED3[63];
  __IOM uint32_t  ENABLE;                       /*!< (@ 0x00000500) Enable or disable SAADC                                    */
  __IM  uint32_t  RESERVED4[3];
  __IOM SAADC_CH_Type CH[8];                    /*!< (@ 0x00000510) Unspecified                                                */
  __IM  uint32_t  RESERVED5[24];
  __IOM uint32_t  RESOLUTION;                   /*!< (@ 0x000005F0) Resolution configuration                                   */
  __IOM uint32_t  OVERSAMPLE;                   /*!< (@ 0x000005F4) Oversampling configuration. The RESOLUTION is
                                                                    applied before averaging, thus for high
                                                                    OVERSAMPLE a higher RESOLUTION should be
                                                                    used.                                                      */
  __IOM uint32_t  SAMPLERATE;                   /*!< (@ 0x000005F8) Controls normal or continuous sample rate                  */
  __IM  uint32_t  RESERVED6[12];
  __IOM SAADC_RESULT_Type RESULT;               /*!< (@ 0x0000062C) RESULT EasyDMA channel                                     */
} NRF_SAADC_Type;                               /*!< Size = 1592 (0x638)                                                       */



/* =========================================================================================================================== */
/* ================                                          TIMER0                                           ================ */
/* =========================================================================================================================== */


/**
  * @brief Timer/Counter 0 (TIMER0)
  */

typedef struct {                                /*!< (@ 0x40008000) TIMER0 Structure                                           */
  __OM  uint32_t  TASKS_START;                  /*!< (@ 0x00000000) Start Timer                                                */
  __OM  uint32_t  TASKS_STOP;                   /*!< (@ 0x00000004) Stop Timer                                                 */
  __OM  uint32_t  TASKS_COUNT;                  /*!< (@ 0x00000008) Increment Timer (Counter mode only)                        */
  __OM  uint32_t  TASKS_CLEAR;                  /*!< (@ 0x0000000C) Clear time                                                 */
  __OM  uint32_t  TASKS_SHUTDOWN;               /*!< (@ 0x00000010) Deprecated register - Shut down timer                      */
  __IM  uint32_t  RESERVED[11];
  __OM  uint32_t  TASKS_CAPTURE[6];             /*!< (@ 0x00000040) Description collection: Capture Timer value to
                                                                    CC[n] register                                             */
  __IM  uint32_t  RESERVED1[58];
  __IOM uint32_t  EVENTS_COMPARE[6];            /*!< (@ 0x00000140) Description collection: Compare event on CC[n]
                                                                    match                                                      */
  __IM  uint32_t  RESERVED2[42];
  __IOM uint32_t  SHORTS;                       /*!< (@ 0x00000200) Shortcuts between local events and tasks                   */
  __IM  uint32_t  RESERVED3[64];
  __IOM uint32_t  INTENSET;                     /*!< (@ 0x00000304) Enable interrupt                                           */
  __IOM uint32_t  INTENCLR;                     /*!< (@ 0x00000308) Disable interrupt                                          */
  __IM  uint32_t  RESERVED4[126];
  __IOM uint32_t  MODE;                         /*!< (@ 0x00000504) Timer mode selection                                       */
  __IOM uint32_t  BITMODE;                      /*!< (@ 0x00000508) Configure the number of bits used by the TIMER             */
  __IM  uint32_t  RESERVED5;
  __IOM uint32_t  PRESCALER;                    /*!< (@ 0x00000510) Timer prescaler register                                   */
  __IM  uint32_t  RESERVED6[11];
  __IOM uint32_t  CC[6];                        /*!< (@ 0x00000540) Description collection: Capture/Compare register
                                                                    n                                                          */
} NRF_TIMER_Type;                               /*!< Size = 1368 (0x558)                                                       */



/* =========================================================================================================================== */
/* ================                                           RTC0                                            ================ */
/* =========================================================================================================================== */


/**
  * @brief Real time counter 0 (RTC0)
  */

typedef struct {                                /*!< (@ 0x4000B000) RTC0 Structure                                             */
  __OM  uint32_t  TASKS_START;                  /*!< (@ 0x00000000) Start RTC COUNTER                                          */
  __OM  uint32_t  TASKS_STOP;                   /*!< (@ 0x00000004) Stop RTC COUNTER                                           */
  __OM  uint32_t  TASKS_CLEAR;                  /*!< (@ 0x00000008) Clear RTC COUNTER                                          */
  __OM  uint32_t  TASKS_TRIGOVRFLW;             /*!< (@ 0x0000000C) Set COUNTER to 0xFFFFF0                                    */
  __IM  uint32_t  RESERVED[60];
  __IOM uint32_t  EVENTS_TICK;                  /*!< (@ 0x00000100) Event on COUNTER increment                                 */
  __IOM uint32_t  EVENTS_OVRFLW;                /*!< (@ 0x00000104) Event on COUNTER overflow                                  */
  __IM  uint32_t  RESERVED1[14];
  __IOM uint32_t  EVENTS_COMPARE[4];            /*!< (@ 0x00000140) Description collection: Compare event on CC[n]
                                                                    match                                                      */
  __IM  uint32_t  RESERVED2[109];
  __IOM uint32_t  INTENSET;                     /*!< (@ 0x00000304) Enable interrupt                                           */
  __IOM uint32_t  INTENCLR;                     /*!< (@ 0x00000308) Disable interrupt                                          */
  __IM  uint32_t  RESERVED3[13];
  __IOM uint32_t  EVTEN;                        /*!< (@ 0x00000340) Enable or disable event routing                            */
  __IOM uint32_t  EVTENSET;                     /*!< (@ 0x00000344) Enable event routing                                       */
  __IOM uint32_t  EVTENCLR;                     /*!< (@ 0x00000348) Disable event routing                                      */
  __IM  uint32_t  RESERVED4[110];
  __IM  uint32_t  COUNTER;                      /*!< (@ 0x00000504) Current COUNTER value                                      */
  __IOM uint32_t  PRESCALER;                    /*!< (@ 0x00000508) 12 bit prescaler for COUNTER frequency (32768/(PRESCALER+1)).Mu
                                                                    t be written when RTC is stopped                           */
  __IM  uint32_t  RESERVED5[13];
  __IOM uint32_t  CC[4];                        /*!< (@ 0x00000540) Description collection: Compare register n                 */
} NRF_RTC_Type;                                 /*!< Size = 1360 (0x550)                                                       */



/* =========================================================================================================================== */
/* ================                                           TEMP                                            ================ */
/* =========================================================================================================================== */


/**
  * @brief Temperature Sensor (TEMP)
  */

typedef struct {                                /*!< (@ 0x4000C000) TEMP Structure                                             */
  __OM  uint32_t  TASKS_START;                  /*!< (@ 0x00000000) Start temperature measurement                              */
  __OM  uint32_t  TASKS_STOP;                   /*!< (@ 0x00000004) Stop temperature measurement                               */
  __IM  uint32_t  RESERVED[62];
  __IOM uint32_t  EVENTS_DATARDY;               /*!< (@ 0x00000100) Temperature measurement complete, data ready               */
  __IM  uint32_t  RESERVED1[128];
  __IOM uint32_t  INTENSET;                     /*!< (@ 0x00000304) Enable interrupt                                           */
  __IOM uint32_t  INTENCLR;                     /*!< (@ 0x00000308) Disable interrupt                                          */
  __IM  uint32_t  RESERVED2[127];
  __IM  int32_t   TEMP;                         /*!< (@ 0x00000508) Temperature in degC (0.25deg steps)                        */
  __IM  uint32_t  RESERVED3[5];
  __IOM uint32_t  A0;                           /*!< (@ 0x00000520) Slope of 1st piece wise linear function                    */
  __IOM uint32_t  A1;                           /*!< (@ 0x00000524) Slope of 2nd piece wise linear function                    */
  __IOM uint32_t  A2;                           /*!< (@ 0x00000528) Slope of 3rd piece wise linear function                    */
  __IOM uint32_t  A3;                           /*!< (@ 0x0000052C) Slope of 4th piece wise linear function                    */
  __IOM uint32_t  A4;                           /*!< (@ 0x00000530) Slope of 5th piece wise linear function                    */
  __IOM uint32_t  A5;                           /*!< (@ 0x00000534) Slope of 6th piece wise linear function                    */
  __IM  uint32_t  RESERVED4[2];
  __IOM uint32_t  B0;                           /*!< (@ 0x00000540) y-intercept of 1st piece wise linear function              */
  __IOM uint32_t  B1;                           /*!< (@ 0x00000544) y-intercept of 2nd piece wise linear function              */
  __IOM uint32_t  B2;                           /*!< (@ 0x00000548) y-intercept of 3rd piece wise linear function              */
  __IOM uint32_t  B3;                           /*!< (@ 0x0000054C) y-intercept of 4th piece wise linear function              */
  __IOM uint32_t  B4;                           /*!< (@ 0x00000550) y-intercept of 5th piece wise linear function              */
  __IOM uint32_t  B5;                           /*!< (@ 0x00000554) y-intercept of 6th piece wise linear function              */
  __IM  uint32_t  RESERVED5[2];
  __IOM uint32_t  T0;                           /*!< (@ 0x00000560) End point of 1st piece wise linear function                */
  __IOM uint32_t  T1;                           /*!< (@ 0x00000564) End point of 2nd piece wise linear function                */
  __IOM uint32_t  T2;                           /*!< (@ 0x00000568) End point of 3rd piece wise linear function                */
  __IOM uint32_t  T3;                           /*!< (@ 0x0000056C) End point of 4th piece wise linear function                */
  __IOM uint32_t  T4;                           /*!< (@ 0x00000570) End point of 5th piece wise linear function                */
} NRF_TEMP_Type;                                /*!< Size = 1396 (0x574)                                                       */



/* =========================================================================================================================== */
/* ================                                            RNG                                            ================ */
/* =========================================================================================================================== */


/**
  * @brief Random Number Generator (RNG)
  */

typedef struct {                                /*!< (@ 0x4000D000) RNG Structure                                              */
  __OM  uint32_t  TASKS_START;                  /*!< (@ 0x00000000) Task starting the random number generator                  */
  __OM  uint32_t  TASKS_STOP;                   /*!< (@ 0x00000004) Task stopping the random number generator                  */
  __IM  uint32_t  RESERVED[62];
  __IOM uint32_t  EVENTS_VALRDY;                /*!< (@ 0x00000100) Event being generated for every new random number
                                                                    written to the VALUE register                              */
  __IM  uint32_t  RESERVED1[63];
  __IOM uint32_t  SHORTS;                       /*!< (@ 0x00000200) Shortcuts between local events and tasks                   */
  __IM  uint32_t  RESERVED2[64];
  __IOM uint32_t  INTENSET;                     /*!< (@ 0x00000304) Enable interrupt                                           */
  __IOM uint32_t  INTENCLR;                     /*!< (@ 0x00000308) Disable interrupt                                          */
  __IM  uint32_t  RESERVED3[126];
  __IOM uint32_t  CONFIG;                       /*!< (@ 0x00000504) Configuration register                                     */
  __IM  uint32_t  VALUE;                        /*!< (@ 0x00000508) Output random number                                       */
} NRF_RNG_Type;                                 /*!< Size = 1292 (0x50c)                                                       */



/* =========================================================================================================================== */
/* ================                                            ECB                                            ================ */
/* =========================================================================================================================== */


/**
  * @brief AES ECB Mode Encryption (ECB)
  */

typedef struct {                                /*!< (@ 0x4000E000) ECB Structure                                              */
  __OM  uint32_t  TASKS_STARTECB;               /*!< (@ 0x00000000) Start ECB block encrypt                                    */
  __OM  uint32_t  TASKS_STOPECB;                /*!< (@ 0x00000004) Abort a possible executing ECB operation                   */
  __IM  uint32_t  RESERVED[62];
  __IOM uint32_t  EVENTS_ENDECB;                /*!< (@ 0x00000100) ECB block encrypt complete                                 */
  __IOM uint32_t  EVENTS_ERRORECB;              /*!< (@ 0x00000104) ECB block encrypt aborted because of a STOPECB
                                                                    task or due to an error                                    */
  __IM  uint32_t  RESERVED1[127];
  __IOM uint32_t  INTENSET;                     /*!< (@ 0x00000304) Enable interrupt                                           */
  __IOM uint32_t  INTENCLR;                     /*!< (@ 0x00000308) Disable interrupt                                          */
  __IM  uint32_t  RESERVED2[126];
  __IOM uint32_t  ECBDATAPTR;                   /*!< (@ 0x00000504) ECB block encrypt memory pointers                          */
} NRF_ECB_Type;                                 /*!< Size = 1288 (0x508)                                                       */



/* =========================================================================================================================== */
/* ================                                            AAR                                            ================ */
/* =========================================================================================================================== */


/**
  * @brief Accelerated Address Resolver (AAR)
  */

typedef struct {                                /*!< (@ 0x4000F000) AAR Structure                                              */
  __OM  uint32_t  TASKS_START;                  /*!< (@ 0x00000000) Start resolving addresses based on IRKs specified
                                                                    in the IRK data structure                                  */
  __IM  uint32_t  RESERVED;
  __OM  uint32_t  TASKS_STOP;                   /*!< (@ 0x00000008) Stop resolving addresses                                   */
  __IM  uint32_t  RESERVED1[61];
  __IOM uint32_t  EVENTS_END;                   /*!< (@ 0x00000100) Address resolution procedure complete                      */
  __IOM uint32_t  EVENTS_RESOLVED;              /*!< (@ 0x00000104) Address resolved                                           */
  __IOM uint32_t  EVENTS_NOTRESOLVED;           /*!< (@ 0x00000108) Address not resolved                                       */
  __IM  uint32_t  RESERVED2[126];
  __IOM uint32_t  INTENSET;                     /*!< (@ 0x00000304) Enable interrupt                                           */
  __IOM uint32_t  INTENCLR;                     /*!< (@ 0x00000308) Disable interrupt                                          */
  __IM  uint32_t  RESERVED3[61];
  __IM  uint32_t  STATUS;                       /*!< (@ 0x00000400) Resolution status                                          */
  __IM  uint32_t  RESERVED4[63];
  __IOM uint32_t  ENABLE;                       /*!< (@ 0x00000500) Enable AAR                                                 */
  __IOM uint32_t  NIRK;                         /*!< (@ 0x00000504) Number of IRKs                                             */
  __IOM uint32_t  IRKPTR;                       /*!< (@ 0x00000508) Pointer to IRK data structure                              */
  __IM  uint32_t  RESERVED5;
  __IOM uint32_t  ADDRPTR;                      /*!< (@ 0x00000510) Pointer to the resolvable address                          */
  __IOM uint32_t  SCRATCHPTR;                   /*!< (@ 0x00000514) Pointer to data area used for temporary storage            */
} NRF_AAR_Type;                                 /*!< Size = 1304 (0x518)                                                       */



/* =========================================================================================================================== */
/* ================                                            CCM                                            ================ */
/* =========================================================================================================================== */


/**
  * @brief AES CCM Mode Encryption (CCM)
  */

typedef struct {                                /*!< (@ 0x4000F000) CCM Structure                                              */
  __OM  uint32_t  TASKS_KSGEN;                  /*!< (@ 0x00000000) Start generation of key-stream. This operation
                                                                    will stop by itself when completed.                        */
  __OM  uint32_t  TASKS_CRYPT;                  /*!< (@ 0x00000004) Start encryption/decryption. This operation will
                                                                    stop by itself when completed.                             */
  __OM  uint32_t  TASKS_STOP;                   /*!< (@ 0x00000008) Stop encryption/decryption                                 */
  __OM  uint32_t  TASKS_RATEOVERRIDE;           /*!< (@ 0x0000000C) Override DATARATE setting in MODE register with
                                                                    the contents of the RATEOVERRIDE register
                                                                    for any ongoing encryption/decryption                      */
  __IM  uint32_t  RESERVED[60];
  __IOM uint32_t  EVENTS_ENDKSGEN;              /*!< (@ 0x00000100) Key-stream generation complete                             */
  __IOM uint32_t  EVENTS_ENDCRYPT;              /*!< (@ 0x00000104) Encrypt/decrypt complete                                   */
  __IOM uint32_t  EVENTS_ERROR;                 /*!< (@ 0x00000108) Deprecated register - CCM error event                      */
  __IM  uint32_t  RESERVED1[61];
  __IOM uint32_t  SHORTS;                       /*!< (@ 0x00000200) Shortcuts between local events and tasks                   */
  __IM  uint32_t  RESERVED2[64];
  __IOM uint32_t  INTENSET;                     /*!< (@ 0x00000304) Enable interrupt                                           */
  __IOM uint32_t  INTENCLR;                     /*!< (@ 0x00000308) Disable interrupt                                          */
  __IM  uint32_t  RESERVED3[61];
  __IM  uint32_t  MICSTATUS;                    /*!< (@ 0x00000400) MIC check result                                           */
  __IM  uint32_t  RESERVED4[63];
  __IOM uint32_t  ENABLE;                       /*!< (@ 0x00000500) Enable                                                     */
  __IOM uint32_t  MODE;                         /*!< (@ 0x00000504) Operation mode                                             */
  __IOM uint32_t  CNFPTR;                       /*!< (@ 0x00000508) Pointer to data structure holding AES key and
                                                                    NONCE vector                                               */
  __IOM uint32_t  INPTR;                        /*!< (@ 0x0000050C) Input pointer                                              */
  __IOM uint32_t  OUTPTR;                       /*!< (@ 0x00000510) Output pointer                                             */
  __IOM uint32_t  SCRATCHPTR;                   /*!< (@ 0x00000514) Pointer to data area used for temporary storage            */
  __IOM uint32_t  MAXPACKETSIZE;                /*!< (@ 0x00000518) Length of key-stream generated when MODE.LENGTH
                                                                    = Extended.                                                */
  __IOM uint32_t  RATEOVERRIDE;                 /*!< (@ 0x0000051C) Data rate override setting.                                */
} NRF_CCM_Type;                                 /*!< Size = 1312 (0x520)                                                       */



/* =========================================================================================================================== */
/* ================                                            WDT                                            ================ */
/* =========================================================================================================================== */


/**
  * @brief Watchdog Timer (WDT)
  */

typedef struct {                                /*!< (@ 0x40010000) WDT Structure                                              */
  __OM  uint32_t  TASKS_START;                  /*!< (@ 0x00000000) Start the watchdog                                         */
  __IM  uint32_t  RESERVED[63];
  __IOM uint32_t  EVENTS_TIMEOUT;               /*!< (@ 0x00000100) Watchdog timeout                                           */
  __IM  uint32_t  RESERVED1[128];
  __IOM uint32_t  INTENSET;                     /*!< (@ 0x00000304) Enable interrupt                                           */
  __IOM uint32_t  INTENCLR;                     /*!< (@ 0x00000308) Disable interrupt                                          */
  __IM  uint32_t  RESERVED2[61];
  __IM  uint32_t  RUNSTATUS;                    /*!< (@ 0x00000400) Run status                                                 */
  __IM  uint32_t  REQSTATUS;                    /*!< (@ 0x00000404) Request status                                             */
  __IM  uint32_t  RESERVED3[63];
  __IOM uint32_t  CRV;                          /*!< (@ 0x00000504) Counter reload value                                       */
  __IOM uint32_t  RREN;                         /*!< (@ 0x00000508) Enable register for reload request registers               */
  __IOM uint32_t  CONFIG;                       /*!< (@ 0x0000050C) Configuration register                                     */
  __IM  uint32_t  RESERVED4[60];
  __OM  uint32_t  RR[8];                        /*!< (@ 0x00000600) Description collection: Reload request n                   */
} NRF_WDT_Type;                                 /*!< Size = 1568 (0x620)                                                       */



/* =========================================================================================================================== */
/* ================                                           QDEC                                            ================ */
/* =========================================================================================================================== */


/**
  * @brief Quadrature Decoder (QDEC)
  */

typedef struct {                                /*!< (@ 0x40012000) QDEC Structure                                             */
  __OM  uint32_t  TASKS_START;                  /*!< (@ 0x00000000) Task starting the quadrature decoder                       */
  __OM  uint32_t  TASKS_STOP;                   /*!< (@ 0x00000004) Task stopping the quadrature decoder                       */
  __OM  uint32_t  TASKS_READCLRACC;             /*!< (@ 0x00000008) Read and clear ACC and ACCDBL                              */
  __OM  uint32_t  TASKS_RDCLRACC;               /*!< (@ 0x0000000C) Read and clear ACC                                         */
  __OM  uint32_t  TASKS_RDCLRDBL;               /*!< (@ 0x00000010) Read and clear ACCDBL                                      */
  __IM  uint32_t  RESERVED[59];
  __IOM uint32_t  EVENTS_SAMPLERDY;             /*!< (@ 0x00000100) Event being generated for every new sample value
                                                                    written to the SAMPLE register                             */
  __IOM uint32_t  EVENTS_REPORTRDY;             /*!< (@ 0x00000104) Non-null report ready                                      */
  __IOM uint32_t  EVENTS_ACCOF;                 /*!< (@ 0x00000108) ACC or ACCDBL register overflow                            */
  __IOM uint32_t  EVENTS_DBLRDY;                /*!< (@ 0x0000010C) Double displacement(s) detected                            */
  __IOM uint32_t  EVENTS_STOPPED;               /*!< (@ 0x00000110) QDEC has been stopped                                      */
  __IM  uint32_t  RESERVED1[59];
  __IOM uint32_t  SHORTS;                       /*!< (@ 0x00000200) Shortcuts between local events and tasks                   */
  __IM  uint32_t  RESERVED2[64];
  __IOM uint32_t  INTENSET;                     /*!< (@ 0x00000304) Enable interrupt                                           */
  __IOM uint32_t  INTENCLR;                     /*!< (@ 0x00000308) Disable interrupt                                          */
  __IM  uint32_t  RESERVED3[125];
  __IOM uint32_t  ENABLE;                       /*!< (@ 0x00000500) Enable the quadrature decoder                              */
  __IOM uint32_t  LEDPOL;                       /*!< (@ 0x00000504) LED output pin polarity                                    */
  __IOM uint32_t  SAMPLEPER;                    /*!< (@ 0x00000508) Sample period                                              */
  __IM  int32_t   SAMPLE;                       /*!< (@ 0x0000050C) Motion sample value                                        */
  __IOM uint32_t  REPORTPER;                    /*!< (@ 0x00000510) Number of samples to be taken before REPORTRDY
                                                                    and DBLRDY events can be generated                         */
  __IM  int32_t   ACC;                          /*!< (@ 0x00000514) Register accumulating the valid transitions                */
  __IM  int32_t   ACCREAD;                      /*!< (@ 0x00000518) Snapshot of the ACC register, updated by the
                                                                    READCLRACC or RDCLRACC task                                */
  __IOM QDEC_PSEL_Type PSEL;                    /*!< (@ 0x0000051C) Unspecified                                                */
  __IOM uint32_t  DBFEN;                        /*!< (@ 0x00000528) Enable input debounce filters                              */
  __IM  uint32_t  RESERVED4[5];
  __IOM uint32_t  LEDPRE;                       /*!< (@ 0x00000540) Time period the LED is switched ON prior to sampling       */
  __IM  uint32_t  ACCDBL;                       /*!< (@ 0x00000544) Register accumulating the number of detected
                                                                    double transitions                                         */
  __IM  uint32_t  ACCDBLREAD;                   /*!< (@ 0x00000548) Snapshot of the ACCDBL, updated by the READCLRACC
                                                                    or RDCLRDBL task                                           */
} NRF_QDEC_Type;                                /*!< Size = 1356 (0x54c)                                                       */



/* =========================================================================================================================== */
/* ================                                           COMP                                            ================ */
/* =========================================================================================================================== */


/**
  * @brief Comparator (COMP)
  */

typedef struct {                                /*!< (@ 0x40013000) COMP Structure                                             */
  __OM  uint32_t  TASKS_START;                  /*!< (@ 0x00000000) Start comparator                                           */
  __OM  uint32_t  TASKS_STOP;                   /*!< (@ 0x00000004) Stop comparator                                            */
  __OM  uint32_t  TASKS_SAMPLE;                 /*!< (@ 0x00000008) Sample comparator value                                    */
  __IM  uint32_t  RESERVED[61];
  __IOM uint32_t  EVENTS_READY;                 /*!< (@ 0x00000100) COMP is ready and output is valid                          */
  __IOM uint32_t  EVENTS_DOWN;                  /*!< (@ 0x00000104) Downward crossing                                          */
  __IOM uint32_t  EVENTS_UP;                    /*!< (@ 0x00000108) Upward crossing                                            */
  __IOM uint32_t  EVENTS_CROSS;                 /*!< (@ 0x0000010C) Downward or upward crossing                                */
  __IM  uint32_t  RESERVED1[60];
  __IOM uint32_t  SHORTS;                       /*!< (@ 0x00000200) Shortcuts between local events and tasks                   */
  __IM  uint32_t  RESERVED2[63];
  __IOM uint32_t  INTEN;                        /*!< (@ 0x00000300) Enable or disable interrupt                                */
  __IOM uint32_t  INTENSET;                     /*!< (@ 0x00000304) Enable interrupt                                           */
  __IOM uint32_t  INTENCLR;                     /*!< (@ 0x00000308) Disable interrupt                                          */
  __IM  uint32_t  RESERVED3[61];
  __IM  uint32_t  RESULT;                       /*!< (@ 0x00000400) Compare result                                             */
  __IM  uint32_t  RESERVED4[63];
  __IOM uint32_t  ENABLE;                       /*!< (@ 0x00000500) COMP enable                                                */
  __IOM uint32_t  PSEL;                         /*!< (@ 0x00000504) Pin select                                                 */
  __IOM uint32_t  REFSEL;                       /*!< (@ 0x00000508) Reference source select for single-ended mode              */
  __IOM uint32_t  EXTREFSEL;                    /*!< (@ 0x0000050C) External reference select                                  */
  __IM  uint32_t  RESERVED5[8];
  __IOM uint32_t  TH;                           /*!< (@ 0x00000530) Threshold configuration for hysteresis unit                */
  __IOM uint32_t  MODE;                         /*!< (@ 0x00000534) Mode configuration                                         */
  __IOM uint32_t  HYST;                         /*!< (@ 0x00000538) Comparator hysteresis enable                               */
} NRF_COMP_Type;                                /*!< Size = 1340 (0x53c)                                                       */



/* =========================================================================================================================== */
/* ================                                          LPCOMP                                           ================ */
/* =========================================================================================================================== */


/**
  * @brief Low Power Comparator (LPCOMP)
  */

typedef struct {                                /*!< (@ 0x40013000) LPCOMP Structure                                           */
  __OM  uint32_t  TASKS_START;                  /*!< (@ 0x00000000) Start comparator                                           */
  __OM  uint32_t  TASKS_STOP;                   /*!< (@ 0x00000004) Stop comparator                                            */
  __OM  uint32_t  TASKS_SAMPLE;                 /*!< (@ 0x00000008) Sample comparator value                                    */
  __IM  uint32_t  RESERVED[61];
  __IOM uint32_t  EVENTS_READY;                 /*!< (@ 0x00000100) LPCOMP is ready and output is valid                        */
  __IOM uint32_t  EVENTS_DOWN;                  /*!< (@ 0x00000104) Downward crossing                                          */
  __IOM uint32_t  EVENTS_UP;                    /*!< (@ 0x00000108) Upward crossing                                            */
  __IOM uint32_t  EVENTS_CROSS;                 /*!< (@ 0x0000010C) Downward or upward crossing                                */
  __IM  uint32_t  RESERVED1[60];
  __IOM uint32_t  SHORTS;                       /*!< (@ 0x00000200) Shortcuts between local events and tasks                   */
  __IM  uint32_t  RESERVED2[64];
  __IOM uint32_t  INTENSET;                     /*!< (@ 0x00000304) Enable interrupt                                           */
  __IOM uint32_t  INTENCLR;                     /*!< (@ 0x00000308) Disable interrupt                                          */
  __IM  uint32_t  RESERVED3[61];
  __IM  uint32_t  RESULT;                       /*!< (@ 0x00000400) Compare result                                             */
  __IM  uint32_t  RESERVED4[63];
  __IOM uint32_t  ENABLE;                       /*!< (@ 0x00000500) Enable LPCOMP                                              */
  __IOM uint32_t  PSEL;                         /*!< (@ 0x00000504) Input pin select                                           */
  __IOM uint32_t  REFSEL;                       /*!< (@ 0x00000508) Reference select                                           */
  __IOM uint32_t  EXTREFSEL;                    /*!< (@ 0x0000050C) External reference select                                  */
  __IM  uint32_t  RESERVED5[4];
  __IOM uint32_t  ANADETECT;                    /*!< (@ 0x00000520) Analog detect configuration                                */
  __IM  uint32_t  RESERVED6[5];
  __IOM uint32_t  HYST;                         /*!< (@ 0x00000538) Comparator hysteresis enable                               */
} NRF_LPCOMP_Type;                              /*!< Size = 1340 (0x53c)                                                       */



/* =========================================================================================================================== */
/* ================                                           EGU0                                            ================ */
/* =========================================================================================================================== */


/**
  * @brief Event Generator Unit 0 (EGU0)
  */

typedef struct {                                /*!< (@ 0x40014000) EGU0 Structure                                             */
  __OM  uint32_t  TASKS_TRIGGER[16];            /*!< (@ 0x00000000) Description collection: Trigger n for triggering
                                                                    the corresponding TRIGGERED[n] event                       */
  __IM  uint32_t  RESERVED[48];
  __IOM uint32_t  EVENTS_TRIGGERED[16];         /*!< (@ 0x00000100) Description collection: Event number n generated
                                                                    by triggering the corresponding TRIGGER[n]
                                                                    task                                                       */
  __IM  uint32_t  RESERVED1[112];
  __IOM uint32_t  INTEN;                        /*!< (@ 0x00000300) Enable or disable interrupt                                */
  __IOM uint32_t  INTENSET;                     /*!< (@ 0x00000304) Enable interrupt                                           */
  __IOM uint32_t  INTENCLR;                     /*!< (@ 0x00000308) Disable interrupt                                          */
} NRF_EGU_Type;                                 /*!< Size = 780 (0x30c)                                                        */



/* =========================================================================================================================== */
/* ================                                           SWI0                                            ================ */
/* =========================================================================================================================== */


/**
  * @brief Software interrupt 0 (SWI0)
  */

typedef struct {                                /*!< (@ 0x40014000) SWI0 Structure                                             */
  __IM  uint32_t  UNUSED;                       /*!< (@ 0x00000000) Unused.                                                    */
} NRF_SWI_Type;                                 /*!< Size = 4 (0x4)                                                            */



/* =========================================================================================================================== */
/* ================                                           PWM0                                            ================ */
/* =========================================================================================================================== */


/**
  * @brief Pulse width modulation unit 0 (PWM0)
  */

typedef struct {                                /*!< (@ 0x4001C000) PWM0 Structure                                             */
  __IM  uint32_t  RESERVED;
  __OM  uint32_t  TASKS_STOP;                   /*!< (@ 0x00000004) Stops PWM pulse generation on all channels at
                                                                    the end of current PWM period, and stops
                                                                    sequence playback                                          */
  __OM  uint32_t  TASKS_SEQSTART[2];            /*!< (@ 0x00000008) Description collection: Loads the first PWM value
                                                                    on all enabled channels from sequence n,
                                                                    and starts playing that sequence at the
                                                                    rate defined in SEQ[n]REFRESH and/or DECODER.MODE.
                                                                    Causes PWM generation to start if not running.             */
  __OM  uint32_t  TASKS_NEXTSTEP;               /*!< (@ 0x00000010) Steps by one value in the current sequence on
                                                                    all enabled channels if DECODER.MODE=NextStep.
                                                                    Does not cause PWM generation to start if
                                                                    not running.                                               */
  __IM  uint32_t  RESERVED1[60];
  __IOM uint32_t  EVENTS_STOPPED;               /*!< (@ 0x00000104) Response to STOP task, emitted when PWM pulses
                                                                    are no longer generated                                    */
  __IOM uint32_t  EVENTS_SEQSTARTED[2];         /*!< (@ 0x00000108) Description collection: First PWM period started
                                                                    on sequence n                                              */
  __IOM uint32_t  EVENTS_SEQEND[2];             /*!< (@ 0x00000110) Description collection: Emitted at end of every
                                                                    sequence n, when last value from RAM has
                                                                    been applied to wave counter                               */
  __IOM uint32_t  EVENTS_PWMPERIODEND;          /*!< (@ 0x00000118) Emitted at the end of each PWM period                      */
  __IOM uint32_t  EVENTS_LOOPSDONE;             /*!< (@ 0x0000011C) Concatenated sequences have been played the amount
                                                                    of times defined in LOOP.CNT                               */
  __IM  uint32_t  RESERVED2[56];
  __IOM uint32_t  SHORTS;                       /*!< (@ 0x00000200) Shortcuts between local events and tasks                   */
  __IM  uint32_t  RESERVED3[63];
  __IOM uint32_t  INTEN;                        /*!< (@ 0x00000300) Enable or disable interrupt                                */
  __IOM uint32_t  INTENSET;                     /*!< (@ 0x00000304) Enable interrupt                                           */
  __IOM uint32_t  INTENCLR;                     /*!< (@ 0x00000308) Disable interrupt                                          */
  __IM  uint32_t  RESERVED4[125];
  __IOM uint32_t  ENABLE;                       /*!< (@ 0x00000500) PWM module enable register                                 */
  __IOM uint32_t  MODE;                         /*!< (@ 0x00000504) Selects operating mode of the wave counter                 */
  __IOM uint32_t  COUNTERTOP;                   /*!< (@ 0x00000508) Value up to which the pulse generator counter
                                                                    counts                                                     */
  __IOM uint32_t  PRESCALER;                    /*!< (@ 0x0000050C) Configuration for PWM_CLK                                  */
  __IOM uint32_t  DECODER;                      /*!< (@ 0x00000510) Configuration of the decoder                               */
  __IOM uint32_t  LOOP;                         /*!< (@ 0x00000514) Number of playbacks of a loop                              */
  __IM  uint32_t  RESERVED5[2];
  __IOM PWM_SEQ_Type SEQ[2];                    /*!< (@ 0x00000520) Unspecified                                                */
  __IOM PWM_PSEL_Type PSEL;                     /*!< (@ 0x00000560) Unspecified                                                */
} NRF_PWM_Type;                                 /*!< Size = 1392 (0x570)                                                       */



/* =========================================================================================================================== */
/* ================                                            PDM                                            ================ */
/* =========================================================================================================================== */


/**
  * @brief Pulse Density Modulation (Digital Microphone) Interface (PDM)
  */

typedef struct {                                /*!< (@ 0x4001D000) PDM Structure                                              */
  __OM  uint32_t  TASKS_START;                  /*!< (@ 0x00000000) Starts continuous PDM transfer                             */
  __OM  uint32_t  TASKS_STOP;                   /*!< (@ 0x00000004) Stops PDM transfer                                         */
  __IM  uint32_t  RESERVED[62];
  __IOM uint32_t  EVENTS_STARTED;               /*!< (@ 0x00000100) PDM transfer has started                                   */
  __IOM uint32_t  EVENTS_STOPPED;               /*!< (@ 0x00000104) PDM transfer has finished                                  */
  __IOM uint32_t  EVENTS_END;                   /*!< (@ 0x00000108) The PDM has written the last sample specified
                                                                    by SAMPLE.MAXCNT (or the last sample after
                                                                    a STOP task has been received) to Data RAM                 */
  __IM  uint32_t  RESERVED1[125];
  __IOM uint32_t  INTEN;                        /*!< (@ 0x00000300) Enable or disable interrupt                                */
  __IOM uint32_t  INTENSET;                     /*!< (@ 0x00000304) Enable interrupt                                           */
  __IOM uint32_t  INTENCLR;                     /*!< (@ 0x00000308) Disable interrupt                                          */
  __IM  uint32_t  RESERVED2[125];
  __IOM uint32_t  ENABLE;                       /*!< (@ 0x00000500) PDM module enable register                                 */
  __IOM uint32_t  PDMCLKCTRL;                   /*!< (@ 0x00000504) PDM clock generator control                                */
  __IOM uint32_t  MODE;                         /*!< (@ 0x00000508) Defines the routing of the connected PDM microphones'
                                                                    signals                                                    */
  __IM  uint32_t  RESERVED3[3];
  __IOM uint32_t  GAINL;                        /*!< (@ 0x00000518) Left output gain adjustment                                */
  __IOM uint32_t  GAINR;                        /*!< (@ 0x0000051C) Right output gain adjustment                               */
  __IOM uint32_t  RATIO;                        /*!< (@ 0x00000520) Selects the ratio between PDM_CLK and output
                                                                    sample rate. Change PDMCLKCTRL accordingly.                */
  __IM  uint32_t  RESERVED4[7];
  __IOM PDM_PSEL_Type PSEL;                     /*!< (@ 0x00000540) Unspecified                                                */
  __IM  uint32_t  RESERVED5[6];
  __IOM PDM_SAMPLE_Type SAMPLE;                 /*!< (@ 0x00000560) Unspecified                                                */
} NRF_PDM_Type;                                 /*!< Size = 1384 (0x568)                                                       */



/* =========================================================================================================================== */
/* ================                                            ACL                                            ================ */
/* =========================================================================================================================== */


/**
  * @brief Access control lists (ACL)
  */

typedef struct {                                /*!< (@ 0x4001E000) ACL Structure                                              */
  __IM  uint32_t  RESERVED[512];
  __IOM ACL_ACL_Type ACL[8];                    /*!< (@ 0x00000800) Unspecified                                                */
} NRF_ACL_Type;                                 /*!< Size = 2176 (0x880)                                                       */



/* =========================================================================================================================== */
/* ================                                           NVMC                                            ================ */
/* =========================================================================================================================== */


/**
  * @brief Non Volatile Memory Controller (NVMC)
  */

typedef struct {                                /*!< (@ 0x4001E000) NVMC Structure                                             */
  __IM  uint32_t  RESERVED[256];
  __IM  uint32_t  READY;                        /*!< (@ 0x00000400) Ready flag                                                 */
  __IM  uint32_t  RESERVED1;
  __IM  uint32_t  READYNEXT;                    /*!< (@ 0x00000408) Ready flag                                                 */
  __IM  uint32_t  RESERVED2[62];
  __IOM uint32_t  CONFIG;                       /*!< (@ 0x00000504) Configuration register                                     */
  
  union {
    __IOM uint32_t ERASEPAGE;                   /*!< (@ 0x00000508) Register for erasing a page in code area                   */
    __IOM uint32_t ERASEPCR1;                   /*!< (@ 0x00000508) Deprecated register - Register for erasing a
                                                                    page in code area. Equivalent to ERASEPAGE.                */
  };
  __IOM uint32_t  ERASEALL;                     /*!< (@ 0x0000050C) Register for erasing all non-volatile user memory          */
  __IOM uint32_t  ERASEPCR0;                    /*!< (@ 0x00000510) Deprecated register - Register for erasing a
                                                                    page in code area. Equivalent to ERASEPAGE.                */
  __IOM uint32_t  ERASEUICR;                    /*!< (@ 0x00000514) Register for erasing user information configuration
                                                                    registers                                                  */
  __IOM uint32_t  ERASEPAGEPARTIAL;             /*!< (@ 0x00000518) Register for partial erase of a page in code
                                                                    area                                                       */
  __IOM uint32_t  ERASEPAGEPARTIALCFG;          /*!< (@ 0x0000051C) Register for partial erase configuration                   */
  __IM  uint32_t  RESERVED3[8];
  __IOM uint32_t  ICACHECNF;                    /*!< (@ 0x00000540) I-code cache configuration register.                       */
  __IM  uint32_t  RESERVED4;
  __IOM uint32_t  IHIT;                         /*!< (@ 0x00000548) I-code cache hit counter.                                  */
  __IOM uint32_t  IMISS;                        /*!< (@ 0x0000054C) I-code cache miss counter.                                 */
} NRF_NVMC_Type;                                /*!< Size = 1360 (0x550)                                                       */



/* =========================================================================================================================== */
/* ================                                            PPI                                            ================ */
/* =========================================================================================================================== */


/**
  * @brief Programmable Peripheral Interconnect (PPI)
  */

typedef struct {                                /*!< (@ 0x4001F000) PPI Structure                                              */
  __OM  PPI_TASKS_CHG_Type TASKS_CHG[6];        /*!< (@ 0x00000000) Channel group tasks                                        */
  __IM  uint32_t  RESERVED[308];
  __IOM uint32_t  CHEN;                         /*!< (@ 0x00000500) Channel enable register                                    */
  __IOM uint32_t  CHENSET;                      /*!< (@ 0x00000504) Channel enable set register                                */
  __IOM uint32_t  CHENCLR;                      /*!< (@ 0x00000508) Channel enable clear register                              */
  __IM  uint32_t  RESERVED1;
  __IOM PPI_CH_Type CH[20];                     /*!< (@ 0x00000510) PPI Channel                                                */
  __IM  uint32_t  RESERVED2[148];
  __IOM uint32_t  CHG[6];                       /*!< (@ 0x00000800) Description collection: Channel group n                    */
  __IM  uint32_t  RESERVED3[62];
  __IOM PPI_FORK_Type FORK[32];                 /*!< (@ 0x00000910) Fork                                                       */
} NRF_PPI_Type;                                 /*!< Size = 2448 (0x990)                                                       */



/* =========================================================================================================================== */
/* ================                                            MWU                                            ================ */
/* =========================================================================================================================== */


/**
  * @brief Memory Watch Unit (MWU)
  */

typedef struct {                                /*!< (@ 0x40020000) MWU Structure                                              */
  __IM  uint32_t  RESERVED[64];
  __IOM MWU_EVENTS_REGION_Type EVENTS_REGION[4];/*!< (@ 0x00000100) Peripheral events.                                         */
  __IM  uint32_t  RESERVED1[16];
  __IOM MWU_EVENTS_PREGION_Type EVENTS_PREGION[2];/*!< (@ 0x00000160) Peripheral events.                                       */
  __IM  uint32_t  RESERVED2[100];
  __IOM uint32_t  INTEN;                        /*!< (@ 0x00000300) Enable or disable interrupt                                */
  __IOM uint32_t  INTENSET;                     /*!< (@ 0x00000304) Enable interrupt                                           */
  __IOM uint32_t  INTENCLR;                     /*!< (@ 0x00000308) Disable interrupt                                          */
  __IM  uint32_t  RESERVED3[5];
  __IOM uint32_t  NMIEN;                        /*!< (@ 0x00000320) Enable or disable interrupt                                */
  __IOM uint32_t  NMIENSET;                     /*!< (@ 0x00000324) Enable interrupt                                           */
  __IOM uint32_t  NMIENCLR;                     /*!< (@ 0x00000328) Disable interrupt                                          */
  __IM  uint32_t  RESERVED4[53];
  __IOM MWU_PERREGION_Type PERREGION[2];        /*!< (@ 0x00000400) Unspecified                                                */
  __IM  uint32_t  RESERVED5[64];
  __IOM uint32_t  REGIONEN;                     /*!< (@ 0x00000510) Enable/disable regions watch                               */
  __IOM uint32_t  REGIONENSET;                  /*!< (@ 0x00000514) Enable regions watch                                       */
  __IOM uint32_t  REGIONENCLR;                  /*!< (@ 0x00000518) Disable regions watch                                      */
  __IM  uint32_t  RESERVED6[57];
  __IOM MWU_REGION_Type REGION[4];              /*!< (@ 0x00000600) Unspecified                                                */
  __IM  uint32_t  RESERVED7[32];
  __IOM MWU_PREGION_Type PREGION[2];            /*!< (@ 0x000006C0) Unspecified                                                */
} NRF_MWU_Type;                                 /*!< Size = 1760 (0x6e0)                                                       */



/* =========================================================================================================================== */
/* ================                                            I2S                                            ================ */
/* =========================================================================================================================== */


/**
  * @brief Inter-IC Sound (I2S)
  */

typedef struct {                                /*!< (@ 0x40025000) I2S Structure                                              */
  __OM  uint32_t  TASKS_START;                  /*!< (@ 0x00000000) Starts continuous I2S transfer. Also starts MCK
                                                                    generator when this is enabled.                            */
  __OM  uint32_t  TASKS_STOP;                   /*!< (@ 0x00000004) Stops I2S transfer. Also stops MCK generator.
                                                                    Triggering this task will cause the STOPPED
                                                                    event to be generated.                                     */
  __IM  uint32_t  RESERVED[63];
  __IOM uint32_t  EVENTS_RXPTRUPD;              /*!< (@ 0x00000104) The RXD.PTR register has been copied to internal
                                                                    double-buffers. When the I2S module is started
                                                                    and RX is enabled, this event will be generated
                                                                    for every RXTXD.MAXCNT words that are received
                                                                    on the SDIN pin.                                           */
  __IOM uint32_t  EVENTS_STOPPED;               /*!< (@ 0x00000108) I2S transfer stopped.                                      */
  __IM  uint32_t  RESERVED1[2];
  __IOM uint32_t  EVENTS_TXPTRUPD;              /*!< (@ 0x00000114) The TDX.PTR register has been copied to internal
                                                                    double-buffers. When the I2S module is started
                                                                    and TX is enabled, this event will be generated
                                                                    for every RXTXD.MAXCNT words that are sent
                                                                    on the SDOUT pin.                                          */
  __IM  uint32_t  RESERVED2[122];
  __IOM uint32_t  INTEN;                        /*!< (@ 0x00000300) Enable or disable interrupt                                */
  __IOM uint32_t  INTENSET;                     /*!< (@ 0x00000304) Enable interrupt                                           */
  __IOM uint32_t  INTENCLR;                     /*!< (@ 0x00000308) Disable interrupt                                          */
  __IM  uint32_t  RESERVED3[125];
  __IOM uint32_t  ENABLE;                       /*!< (@ 0x00000500) Enable I2S module.                                         */
  __IOM I2S_CONFIG_Type CONFIG;                 /*!< (@ 0x00000504) Unspecified                                                */
  __IM  uint32_t  RESERVED4[3];
  __IOM I2S_RXD_Type RXD;                       /*!< (@ 0x00000538) Unspecified                                                */
  __IM  uint32_t  RESERVED5;
  __IOM I2S_TXD_Type TXD;                       /*!< (@ 0x00000540) Unspecified                                                */
  __IM  uint32_t  RESERVED6[3];
  __IOM I2S_RXTXD_Type RXTXD;                   /*!< (@ 0x00000550) Unspecified                                                */
  __IM  uint32_t  RESERVED7[3];
  __IOM I2S_PSEL_Type PSEL;                     /*!< (@ 0x00000560) Unspecified                                                */
} NRF_I2S_Type;                                 /*!< Size = 1396 (0x574)                                                       */



/* =========================================================================================================================== */
/* ================                                            FPU                                            ================ */
/* =========================================================================================================================== */


/**
  * @brief FPU (FPU)
  */

typedef struct {                                /*!< (@ 0x40026000) FPU Structure                                              */
  __IM  uint32_t  UNUSED;                       /*!< (@ 0x00000000) Unused.                                                    */
} NRF_FPU_Type;                                 /*!< Size = 4 (0x4)                                                            */



/* =========================================================================================================================== */
/* ================                                           USBD                                            ================ */
/* =========================================================================================================================== */


/**
  * @brief Universal serial bus device (USBD)
  */

typedef struct {                                /*!< (@ 0x40027000) USBD Structure                                             */
  __IM  uint32_t  RESERVED;
  __OM  uint32_t  TASKS_STARTEPIN[8];           /*!< (@ 0x00000004) Description collection: Captures the EPIN[n].PTR
                                                                    and EPIN[n].MAXCNT registers values, and
                                                                    enables endpoint IN n to respond to traffic
                                                                    from host                                                  */
  __OM  uint32_t  TASKS_STARTISOIN;             /*!< (@ 0x00000024) Captures the ISOIN.PTR and ISOIN.MAXCNT registers
                                                                    values, and enables sending data on ISO
                                                                    endpoint                                                   */
  __OM  uint32_t  TASKS_STARTEPOUT[8];          /*!< (@ 0x00000028) Description collection: Captures the EPOUT[n].PTR
                                                                    and EPOUT[n].MAXCNT registers values, and
                                                                    enables endpoint n to respond to traffic
                                                                    from host                                                  */
  __OM  uint32_t  TASKS_STARTISOOUT;            /*!< (@ 0x00000048) Captures the ISOOUT.PTR and ISOOUT.MAXCNT registers
                                                                    values, and enables receiving of data on
                                                                    ISO endpoint                                               */
  __OM  uint32_t  TASKS_EP0RCVOUT;              /*!< (@ 0x0000004C) Allows OUT data stage on control endpoint 0                */
  __OM  uint32_t  TASKS_EP0STATUS;              /*!< (@ 0x00000050) Allows status stage on control endpoint 0                  */
  __OM  uint32_t  TASKS_EP0STALL;               /*!< (@ 0x00000054) Stalls data and status stage on control endpoint
                                                                    0                                                          */
  __OM  uint32_t  TASKS_DPDMDRIVE;              /*!< (@ 0x00000058) Forces D+ and D- lines into the state defined
                                                                    in the DPDMVALUE register                                  */
  __OM  uint32_t  TASKS_DPDMNODRIVE;            /*!< (@ 0x0000005C) Stops forcing D+ and D- lines into any state
                                                                    (USB engine takes control)                                 */
  __IM  uint32_t  RESERVED1[40];
  __IOM uint32_t  EVENTS_USBRESET;              /*!< (@ 0x00000100) Signals that a USB reset condition has been detected
                                                                    on USB lines                                               */
  __IOM uint32_t  EVENTS_STARTED;               /*!< (@ 0x00000104) Confirms that the EPIN[n].PTR and EPIN[n].MAXCNT,
                                                                    or EPOUT[n].PTR and EPOUT[n].MAXCNT registers
                                                                    have been captured on all endpoints reported
                                                                    in the EPSTATUS register                                   */
  __IOM uint32_t  EVENTS_ENDEPIN[8];            /*!< (@ 0x00000108) Description collection: The whole EPIN[n] buffer
                                                                    has been consumed. The buffer can be accessed
                                                                    safely by software.                                        */
  __IOM uint32_t  EVENTS_EP0DATADONE;           /*!< (@ 0x00000128) An acknowledged data transfer has taken place
                                                                    on the control endpoint                                    */
  __IOM uint32_t  EVENTS_ENDISOIN;              /*!< (@ 0x0000012C) The whole ISOIN buffer has been consumed. The
                                                                    buffer can be accessed safely by software.                 */
  __IOM uint32_t  EVENTS_ENDEPOUT[8];           /*!< (@ 0x00000130) Description collection: The whole EPOUT[n] buffer
                                                                    has been consumed. The buffer can be accessed
                                                                    safely by software.                                        */
  __IOM uint32_t  EVENTS_ENDISOOUT;             /*!< (@ 0x00000150) The whole ISOOUT buffer has been consumed. The
                                                                    buffer can be accessed safely by software.                 */
  __IOM uint32_t  EVENTS_SOF;                   /*!< (@ 0x00000154) Signals that a SOF (start of frame) condition
                                                                    has been detected on USB lines                             */
  __IOM uint32_t  EVENTS_USBEVENT;              /*!< (@ 0x00000158) An event or an error not covered by specific
                                                                    events has occurred. Check EVENTCAUSE register
                                                                    to find the cause.                                         */
  __IOM uint32_t  EVENTS_EP0SETUP;              /*!< (@ 0x0000015C) A valid SETUP token has been received (and acknowledged)
                                                                    on the control endpoint                                    */
  __IOM uint32_t  EVENTS_EPDATA;                /*!< (@ 0x00000160) A data transfer has occurred on a data endpoint,
                                                                    indicated by the EPDATASTATUS register                     */
  __IM  uint32_t  RESERVED2[39];
  __IOM uint32_t  SHORTS;                       /*!< (@ 0x00000200) Shortcuts between local events and tasks                   */
  __IM  uint32_t  RESERVED3[63];
  __IOM uint32_t  INTEN;                        /*!< (@ 0x00000300) Enable or disable interrupt                                */
  __IOM uint32_t  INTENSET;                     /*!< (@ 0x00000304) Enable interrupt                                           */
  __IOM uint32_t  INTENCLR;                     /*!< (@ 0x00000308) Disable interrupt                                          */
  __IM  uint32_t  RESERVED4[61];
  __IOM uint32_t  EVENTCAUSE;                   /*!< (@ 0x00000400) Details on what caused the USBEVENT event                  */
  __IM  uint32_t  RESERVED5[7];
  __IOM USBD_HALTED_Type HALTED;                /*!< (@ 0x00000420) Unspecified                                                */
  __IM  uint32_t  RESERVED6;
  __IOM uint32_t  EPSTATUS;                     /*!< (@ 0x00000468) Provides information on which endpoint's EasyDMA
                                                                    registers have been captured                               */
  __IOM uint32_t  EPDATASTATUS;                 /*!< (@ 0x0000046C) Provides information on which endpoint(s) an
                                                                    acknowledged data transfer has occurred
                                                                    (EPDATA event)                                             */
  __IM  uint32_t  USBADDR;                      /*!< (@ 0x00000470) Device USB address                                         */
  __IM  uint32_t  RESERVED7[3];
  __IM  uint32_t  BMREQUESTTYPE;                /*!< (@ 0x00000480) SETUP data, byte 0, bmRequestType                          */
  __IM  uint32_t  BREQUEST;                     /*!< (@ 0x00000484) SETUP data, byte 1, bRequest                               */
  __IM  uint32_t  WVALUEL;                      /*!< (@ 0x00000488) SETUP data, byte 2, LSB of wValue                          */
  __IM  uint32_t  WVALUEH;                      /*!< (@ 0x0000048C) SETUP data, byte 3, MSB of wValue                          */
  __IM  uint32_t  WINDEXL;                      /*!< (@ 0x00000490) SETUP data, byte 4, LSB of wIndex                          */
  __IM  uint32_t  WINDEXH;                      /*!< (@ 0x00000494) SETUP data, byte 5, MSB of wIndex                          */
  __IM  uint32_t  WLENGTHL;                     /*!< (@ 0x00000498) SETUP data, byte 6, LSB of wLength                         */
  __IM  uint32_t  WLENGTHH;                     /*!< (@ 0x0000049C) SETUP data, byte 7, MSB of wLength                         */
  __IOM USBD_SIZE_Type SIZE;                    /*!< (@ 0x000004A0) Unspecified                                                */
  __IM  uint32_t  RESERVED8[15];
  __IOM uint32_t  ENABLE;                       /*!< (@ 0x00000500) Enable USB                                                 */
  __IOM uint32_t  USBPULLUP;                    /*!< (@ 0x00000504) Control of the USB pull-up                                 */
  __IOM uint32_t  DPDMVALUE;                    /*!< (@ 0x00000508) State D+ and D- lines will be forced into by
                                                                    the DPDMDRIVE task. The DPDMNODRIVE task
                                                                    reverts the control of the lines to MAC
                                                                    IP (no forcing).                                           */
  __IOM uint32_t  DTOGGLE;                      /*!< (@ 0x0000050C) Data toggle control and status                             */
  __IOM uint32_t  EPINEN;                       /*!< (@ 0x00000510) Endpoint IN enable                                         */
  __IOM uint32_t  EPOUTEN;                      /*!< (@ 0x00000514) Endpoint OUT enable                                        */
  __OM  uint32_t  EPSTALL;                      /*!< (@ 0x00000518) STALL endpoints                                            */
  __IOM uint32_t  ISOSPLIT;                     /*!< (@ 0x0000051C) Controls the split of ISO buffers                          */
  __IM  uint32_t  FRAMECNTR;                    /*!< (@ 0x00000520) Returns the current value of the start of frame
                                                                    counter                                                    */
  __IM  uint32_t  RESERVED9[2];
  __IOM uint32_t  LOWPOWER;                     /*!< (@ 0x0000052C) Controls USBD peripheral low power mode during
                                                                    USB suspend                                                */
  __IOM uint32_t  ISOINCONFIG;                  /*!< (@ 0x00000530) Controls the response of the ISO IN endpoint
                                                                    to an IN token when no data is ready to
                                                                    be sent                                                    */
  __IM  uint32_t  RESERVED10[51];
  __IOM USBD_EPIN_Type EPIN[8];                 /*!< (@ 0x00000600) Unspecified                                                */
  __IOM USBD_ISOIN_Type ISOIN;                  /*!< (@ 0x000006A0) Unspecified                                                */
  __IM  uint32_t  RESERVED11[21];
  __IOM USBD_EPOUT_Type EPOUT[8];               /*!< (@ 0x00000700) Unspecified                                                */
  __IOM USBD_ISOOUT_Type ISOOUT;                /*!< (@ 0x000007A0) Unspecified                                                */
} NRF_USBD_Type;                                /*!< Size = 1964 (0x7ac)                                                       */



/* =========================================================================================================================== */
/* ================                                           QSPI                                            ================ */
/* =========================================================================================================================== */


/**
  * @brief External flash interface (QSPI)
  */

typedef struct {                                /*!< (@ 0x40029000) QSPI Structure                                             */
  __OM  uint32_t  TASKS_ACTIVATE;               /*!< (@ 0x00000000) Activate QSPI interface                                    */
  __OM  uint32_t  TASKS_READSTART;              /*!< (@ 0x00000004) Start transfer from external flash memory to
                                                                    internal RAM                                               */
  __OM  uint32_t  TASKS_WRITESTART;             /*!< (@ 0x00000008) Start transfer from internal RAM to external
                                                                    flash memory                                               */
  __OM  uint32_t  TASKS_ERASESTART;             /*!< (@ 0x0000000C) Start external flash memory erase operation                */
  __OM  uint32_t  TASKS_DEACTIVATE;             /*!< (@ 0x00000010) Deactivate QSPI interface                                  */
  __IM  uint32_t  RESERVED[59];
  __IOM uint32_t  EVENTS_READY;                 /*!< (@ 0x00000100) QSPI peripheral is ready. This event will be
                                                                    generated as a response to any QSPI task.                  */
  __IM  uint32_t  RESERVED1[127];
  __IOM uint32_t  INTEN;                        /*!< (@ 0x00000300) Enable or disable interrupt                                */
  __IOM uint32_t  INTENSET;                     /*!< (@ 0x00000304) Enable interrupt                                           */
  __IOM uint32_t  INTENCLR;                     /*!< (@ 0x00000308) Disable interrupt                                          */
  __IM  uint32_t  RESERVED2[125];
  __IOM uint32_t  ENABLE;                       /*!< (@ 0x00000500) Enable QSPI peripheral and acquire the pins selected
                                                                    in PSELn registers                                         */
  __IOM QSPI_READ_Type READ;                    /*!< (@ 0x00000504) Unspecified                                                */
  __IOM QSPI_WRITE_Type WRITE;                  /*!< (@ 0x00000510) Unspecified                                                */
  __IOM QSPI_ERASE_Type ERASE;                  /*!< (@ 0x0000051C) Unspecified                                                */
  __IOM QSPI_PSEL_Type PSEL;                    /*!< (@ 0x00000524) Unspecified                                                */
  __IOM uint32_t  XIPOFFSET;                    /*!< (@ 0x00000540) Address offset into the external memory for Execute
                                                                    in Place operation.                                        */
  __IOM uint32_t  IFCONFIG0;                    /*!< (@ 0x00000544) Interface configuration.                                   */
  __IM  uint32_t  RESERVED3[46];
  __IOM uint32_t  IFCONFIG1;                    /*!< (@ 0x00000600) Interface configuration.                                   */
  __IM  uint32_t  STATUS;                       /*!< (@ 0x00000604) Status register.                                           */
  __IM  uint32_t  RESERVED4[3];
  __IOM uint32_t  DPMDUR;                       /*!< (@ 0x00000614) Set the duration required to enter/exit deep
                                                                    power-down mode (DPM).                                     */
  __IM  uint32_t  RESERVED5[3];
  __IOM uint32_t  ADDRCONF;                     /*!< (@ 0x00000624) Extended address configuration.                            */
  __IM  uint32_t  RESERVED6[3];
  __IOM uint32_t  CINSTRCONF;                   /*!< (@ 0x00000634) Custom instruction configuration register.                 */
  __IOM uint32_t  CINSTRDAT0;                   /*!< (@ 0x00000638) Custom instruction data register 0.                        */
  __IOM uint32_t  CINSTRDAT1;                   /*!< (@ 0x0000063C) Custom instruction data register 1.                        */
  __IOM uint32_t  IFTIMING;                     /*!< (@ 0x00000640) SPI interface timing.                                      */
} NRF_QSPI_Type;                                /*!< Size = 1604 (0x644)                                                       */



/* =========================================================================================================================== */
/* ================                                        CC_HOST_RGF                                        ================ */
/* =========================================================================================================================== */


/**
  * @brief CRYPTOCELL HOST_RGF interface (CC_HOST_RGF)
  */

typedef struct {                                /*!< (@ 0x5002A000) CC_HOST_RGF Structure                                      */
  __IM  uint32_t  RESERVED[1678];
  __IOM uint32_t  HOST_CRYPTOKEY_SEL;           /*!< (@ 0x00001A38) AES hardware key select                                    */
  __IM  uint32_t  RESERVED1[4];
  __IOM uint32_t  HOST_IOT_KPRTL_LOCK;          /*!< (@ 0x00001A4C) This write-once register is the K_PRTL lock register.
                                                                    When this register is set, K_PRTL can not
                                                                    be used and a zeroed key will be used instead.
                                                                    The value of this register is saved in the
                                                                    CRYPTOCELL AO power domain.                                */
  __IOM uint32_t  HOST_IOT_KDR0;                /*!< (@ 0x00001A50) This register holds bits 31:0 of K_DR. The value
                                                                    of this register is saved in the CRYPTOCELL
                                                                    AO power domain. Reading from this address
                                                                    returns the K_DR valid status indicating
                                                                    if K_DR is successfully retained.                          */
  __OM  uint32_t  HOST_IOT_KDR1;                /*!< (@ 0x00001A54) This register holds bits 63:32 of K_DR. The value
                                                                    of this register is saved in the CRYPTOCELL
                                                                    AO power domain.                                           */
  __OM  uint32_t  HOST_IOT_KDR2;                /*!< (@ 0x00001A58) This register holds bits 95:64 of K_DR. The value
                                                                    of this register is saved in the CRYPTOCELL
                                                                    AO power domain.                                           */
  __OM  uint32_t  HOST_IOT_KDR3;                /*!< (@ 0x00001A5C) This register holds bits 127:96 of K_DR. The
                                                                    value of this register is saved in the CRYPTOCELL
                                                                    AO power domain.                                           */
  __IOM uint32_t  HOST_IOT_LCS;                 /*!< (@ 0x00001A60) Controls lifecycle state (LCS) for CRYPTOCELL
                                                                    subsystem                                                  */
} NRF_CC_HOST_RGF_Type;                         /*!< Size = 6756 (0x1a64)                                                      */



/* =========================================================================================================================== */
/* ================                                        CRYPTOCELL                                         ================ */
/* =========================================================================================================================== */


/**
  * @brief ARM TrustZone CryptoCell register interface (CRYPTOCELL)
  */

typedef struct {                                /*!< (@ 0x5002A000) CRYPTOCELL Structure                                       */
  __IM  uint32_t  RESERVED[320];
  __IOM uint32_t  ENABLE;                       /*!< (@ 0x00000500) Enable CRYPTOCELL subsystem                                */
} NRF_CRYPTOCELL_Type;                          /*!< Size = 1284 (0x504)                                                       */


/** @} */ /* End of group Device_Peripheral_peripherals */


/* =========================================================================================================================== */
/* ================                          Device Specific Peripheral Address Map                           ================ */
/* =========================================================================================================================== */


/** @addtogroup Device_Peripheral_peripheralAddr
  * @{
  */

#define NRF_FICR_BASE               0x10000000UL
#define NRF_UICR_BASE               0x10001000UL
#define NRF_CLOCK_BASE              0x40000000UL
#define NRF_POWER_BASE              0x40000000UL
#define NRF_P0_BASE                 0x50000000UL
#define NRF_P1_BASE                 0x50000300UL
#define NRF_RADIO_BASE              0x40001000UL
#define NRF_UART0_BASE              0x40002000UL
#define NRF_UARTE0_BASE             0x40002000UL
#define NRF_SPI0_BASE               0x40003000UL
#define NRF_SPIM0_BASE              0x40003000UL
#define NRF_SPIS0_BASE              0x40003000UL
#define NRF_TWI0_BASE               0x40003000UL
#define NRF_TWIM0_BASE              0x40003000UL
#define NRF_TWIS0_BASE              0x40003000UL
#define NRF_SPI1_BASE               0x40004000UL
#define NRF_SPIM1_BASE              0x40004000UL
#define NRF_SPIS1_BASE              0x40004000UL
#define NRF_TWI1_BASE               0x40004000UL
#define NRF_TWIM1_BASE              0x40004000UL
#define NRF_TWIS1_BASE              0x40004000UL
#define NRF_NFCT_BASE               0x40005000UL
#define NRF_GPIOTE_BASE             0x40006000UL
#define NRF_SAADC_BASE              0x40007000UL
#define NRF_TIMER0_BASE             0x40008000UL
#define NRF_TIMER1_BASE             0x40009000UL
#define NRF_TIMER2_BASE             0x4000A000UL
#define NRF_RTC0_BASE               0x4000B000UL
#define NRF_TEMP_BASE               0x4000C000UL
#define NRF_RNG_BASE                0x4000D000UL
#define NRF_ECB_BASE                0x4000E000UL
#define NRF_AAR_BASE                0x4000F000UL
#define NRF_CCM_BASE                0x4000F000UL
#define NRF_WDT_BASE                0x40010000UL
#define NRF_RTC1_BASE               0x40011000UL
#define NRF_QDEC_BASE               0x40012000UL
#define NRF_COMP_BASE               0x40013000UL
#define NRF_LPCOMP_BASE             0x40013000UL
#define NRF_EGU0_BASE               0x40014000UL
#define NRF_SWI0_BASE               0x40014000UL
#define NRF_EGU1_BASE               0x40015000UL
#define NRF_SWI1_BASE               0x40015000UL
#define NRF_EGU2_BASE               0x40016000UL
#define NRF_SWI2_BASE               0x40016000UL
#define NRF_EGU3_BASE               0x40017000UL
#define NRF_SWI3_BASE               0x40017000UL
#define NRF_EGU4_BASE               0x40018000UL
#define NRF_SWI4_BASE               0x40018000UL
#define NRF_EGU5_BASE               0x40019000UL
#define NRF_SWI5_BASE               0x40019000UL
#define NRF_TIMER3_BASE             0x4001A000UL
#define NRF_TIMER4_BASE             0x4001B000UL
#define NRF_PWM0_BASE               0x4001C000UL
#define NRF_PDM_BASE                0x4001D000UL
#define NRF_ACL_BASE                0x4001E000UL
#define NRF_NVMC_BASE               0x4001E000UL
#define NRF_PPI_BASE                0x4001F000UL
#define NRF_MWU_BASE                0x40020000UL
#define NRF_PWM1_BASE               0x40021000UL
#define NRF_PWM2_BASE               0x40022000UL
#define NRF_SPI2_BASE               0x40023000UL
#define NRF_SPIM2_BASE              0x40023000UL
#define NRF_SPIS2_BASE              0x40023000UL
#define NRF_RTC2_BASE               0x40024000UL
#define NRF_I2S_BASE                0x40025000UL
#define NRF_FPU_BASE                0x40026000UL
#define NRF_USBD_BASE               0x40027000UL
#define NRF_UARTE1_BASE             0x40028000UL
#define NRF_QSPI_BASE               0x40029000UL
#define NRF_CC_HOST_RGF_BASE        0x5002A000UL
#define NRF_CRYPTOCELL_BASE         0x5002A000UL
#define NRF_PWM3_BASE               0x4002D000UL
#define NRF_SPIM3_BASE              0x4002F000UL

/** @} */ /* End of group Device_Peripheral_peripheralAddr */


/* =========================================================================================================================== */
/* ================                                  Peripheral declaration                                   ================ */
/* =========================================================================================================================== */


/** @addtogroup Device_Peripheral_declaration
  * @{
  */

#define NRF_FICR                    ((NRF_FICR_Type*)          NRF_FICR_BASE)
#define NRF_UICR                    ((NRF_UICR_Type*)          NRF_UICR_BASE)
#define NRF_CLOCK                   ((NRF_CLOCK_Type*)         NRF_CLOCK_BASE)
#define NRF_POWER                   ((NRF_POWER_Type*)         NRF_POWER_BASE)
#define NRF_P0                      ((NRF_GPIO_Type*)          NRF_P0_BASE)
#define NRF_P1                      ((NRF_GPIO_Type*)          NRF_P1_BASE)
#define NRF_RADIO                   ((NRF_RADIO_Type*)         NRF_RADIO_BASE)
#define NRF_UART0                   ((NRF_UART_Type*)          NRF_UART0_BASE)
#define NRF_UARTE0                  ((NRF_UARTE_Type*)         NRF_UARTE0_BASE)
#define NRF_SPI0                    ((NRF_SPI_Type*)           NRF_SPI0_BASE)
#define NRF_SPIM0                   ((NRF_SPIM_Type*)          NRF_SPIM0_BASE)
#define NRF_SPIS0                   ((NRF_SPIS_Type*)          NRF_SPIS0_BASE)
#define NRF_TWI0                    ((NRF_TWI_Type*)           NRF_TWI0_BASE)
#define NRF_TWIM0                   ((NRF_TWIM_Type*)          NRF_TWIM0_BASE)
#define NRF_TWIS0                   ((NRF_TWIS_Type*)          NRF_TWIS0_BASE)
#define NRF_SPI1                    ((NRF_SPI_Type*)           NRF_SPI1_BASE)
#define NRF_SPIM1                   ((NRF_SPIM_Type*)          NRF_SPIM1_BASE)
#define NRF_SPIS1                   ((NRF_SPIS_Type*)          NRF_SPIS1_BASE)
#define NRF_TWI1                    ((NRF_TWI_Type*)           NRF_TWI1_BASE)
#define NRF_TWIM1                   ((NRF_TWIM_Type*)          NRF_TWIM1_BASE)
#define NRF_TWIS1                   ((NRF_TWIS_Type*)          NRF_TWIS1_BASE)
#define NRF_NFCT                    ((NRF_NFCT_Type*)          NRF_NFCT_BASE)
#define NRF_GPIOTE                  ((NRF_GPIOTE_Type*)        NRF_GPIOTE_BASE)
#define NRF_SAADC                   ((NRF_SAADC_Type*)         NRF_SAADC_BASE)
#define NRF_TIMER0                  ((NRF_TIMER_Type*)         NRF_TIMER0_BASE)
#define NRF_TIMER1                  ((NRF_TIMER_Type*)         NRF_TIMER1_BASE)
#define NRF_TIMER2                  ((NRF_TIMER_Type*)         NRF_TIMER2_BASE)
#define NRF_RTC0                    ((NRF_RTC_Type*)           NRF_RTC0_BASE)
#define NRF_TEMP                    ((NRF_TEMP_Type*)          NRF_TEMP_BASE)
#define NRF_RNG                     ((NRF_RNG_Type*)           NRF_RNG_BASE)
#define NRF_ECB                     ((NRF_ECB_Type*)           NRF_ECB_BASE)
#define NRF_AAR                     ((NRF_AAR_Type*)           NRF_AAR_BASE)
#define NRF_CCM                     ((NRF_CCM_Type*)           NRF_CCM_BASE)
#define NRF_WDT                     ((NRF_WDT_Type*)           NRF_WDT_BASE)
#define NRF_RTC1                    ((NRF_RTC_Type*)           NRF_RTC1_BASE)
#define NRF_QDEC                    ((NRF_QDEC_Type*)          NRF_QDEC_BASE)
#define NRF_COMP                    ((NRF_COMP_Type*)          NRF_COMP_BASE)
#define NRF_LPCOMP                  ((NRF_LPCOMP_Type*)        NRF_LPCOMP_BASE)
#define NRF_EGU0                    ((NRF_EGU_Type*)           NRF_EGU0_BASE)
#define NRF_SWI0                    ((NRF_SWI_Type*)           NRF_SWI0_BASE)
#define NRF_EGU1                    ((NRF_EGU_Type*)           NRF_EGU1_BASE)
#define NRF_SWI1                    ((NRF_SWI_Type*)           NRF_SWI1_BASE)
#define NRF_EGU2                    ((NRF_EGU_Type*)           NRF_EGU2_BASE)
#define NRF_SWI2                    ((NRF_SWI_Type*)           NRF_SWI2_BASE)
#define NRF_EGU3                    ((NRF_EGU_Type*)           NRF_EGU3_BASE)
#define NRF_SWI3                    ((NRF_SWI_Type*)           NRF_SWI3_BASE)
#define NRF_EGU4                    ((NRF_EGU_Type*)           NRF_EGU4_BASE)
#define NRF_SWI4                    ((NRF_SWI_Type*)           NRF_SWI4_BASE)
#define NRF_EGU5                    ((NRF_EGU_Type*)           NRF_EGU5_BASE)
#define NRF_SWI5                    ((NRF_SWI_Type*)           NRF_SWI5_BASE)
#define NRF_TIMER3                  ((NRF_TIMER_Type*)         NRF_TIMER3_BASE)
#define NRF_TIMER4                  ((NRF_TIMER_Type*)         NRF_TIMER4_BASE)
#define NRF_PWM0                    ((NRF_PWM_Type*)           NRF_PWM0_BASE)
#define NRF_PDM                     ((NRF_PDM_Type*)           NRF_PDM_BASE)
#define NRF_ACL                     ((NRF_ACL_Type*)           NRF_ACL_BASE)
#define NRF_NVMC                    ((NRF_NVMC_Type*)          NRF_NVMC_BASE)
#define NRF_PPI                     ((NRF_PPI_Type*)           NRF_PPI_BASE)
#define NRF_MWU                     ((NRF_MWU_Type*)           NRF_MWU_BASE)
#define NRF_PWM1                    ((NRF_PWM_Type*)           NRF_PWM1_BASE)
#define NRF_PWM2                    ((NRF_PWM_Type*)           NRF_PWM2_BASE)
#define NRF_SPI2                    ((NRF_SPI_Type*)           NRF_SPI2_BASE)
#define NRF_SPIM2                   ((NRF_SPIM_Type*)          NRF_SPIM2_BASE)
#define NRF_SPIS2                   ((NRF_SPIS_Type*)          NRF_SPIS2_BASE)
#define NRF_RTC2                    ((NRF_RTC_Type*)           NRF_RTC2_BASE)
#define NRF_I2S                     ((NRF_I2S_Type*)           NRF_I2S_BASE)
#define NRF_FPU                     ((NRF_FPU_Type*)           NRF_FPU_BASE)
#define NRF_USBD                    ((NRF_USBD_Type*)          NRF_USBD_BASE)
#define NRF_UARTE1                  ((NRF_UARTE_Type*)         NRF_UARTE1_BASE)
#define NRF_QSPI                    ((NRF_QSPI_Type*)          NRF_QSPI_BASE)
#define NRF_CC_HOST_RGF             ((NRF_CC_HOST_RGF_Type*)   NRF_CC_HOST_RGF_BASE)
#define NRF_CRYPTOCELL              ((NRF_CRYPTOCELL_Type*)    NRF_CRYPTOCELL_BASE)
#define NRF_PWM3                    ((NRF_PWM_Type*)           NRF_PWM3_BASE)
#define NRF_SPIM3                   ((NRF_SPIM_Type*)          NRF_SPIM3_BASE)

/** @} */ /* End of group Device_Peripheral_declaration */


/* =========================================  End of section using anonymous unions  ========================================= */
#if defined (__CC_ARM)
  #pragma pop
#elif defined (__ICCARM__)
  /* leave anonymous unions enabled */
#elif defined(__ARMCC_VERSION) && (__ARMCC_VERSION >= 6010050)
  #pragma clang diagnostic pop
#elif defined (__GNUC__)
  /* anonymous unions are enabled by default */
#elif defined (__TMS470__)
  /* anonymous unions are enabled by default */
#elif defined (__TASKING__)
  #pragma warning restore
#elif defined (__CSMC__)
  /* anonymous unions are enabled by default */
#endif


#ifdef __cplusplus
}
#endif

#endif /* NRF52840_H */


/** @} */ /* End of group nrf52840 */

/** @} */ /* End of group Nordic Semiconductor */
