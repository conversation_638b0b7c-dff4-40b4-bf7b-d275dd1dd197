<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html lang="en">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<meta http-equiv="Content-Style-Type" content="text/css">
<link rel="up" title="FatFs" href="../00index_e.html">
<link rel="alternate" hreflang="ja" title="Japanese" href="../ja/forward.html">
<link rel="stylesheet" href="../css_e.css" type="text/css" media="screen" title="ELM Default">
<title>FatFs - f_forward</title>
</head>

<body>

<div class="para func">
<h2>f_forward</h2>
<p>The f_forward function reads the file data and forward it to the data streaming device.</p>
<pre>
FRESULT f_forward (
  FIL* <span class="arg">fp</span>,                        <span class="c">/* [IN] File object */</span>
  UINT (*<span class="arg">func</span>)(const BYTE*,UINT), <span class="c">/* [IN] Data streaming function */</span>
  UINT <span class="arg">btf</span>,                       <span class="c">/* [IN] Number of bytes to forward */</span>
  UINT* <span class="arg">bf</span>                        <span class="c">/* [OUT] Number of bytes forwarded */</span>
);
</pre>
</div>

<div class="para arg">
<h4>Parameters</h4>
<dl class="par">
<dt>fp</dt>
<dd>Pointer to the open file object.</dd>
<dt>func</dt>
<dd>Pointer to the user-defined data streaming function. For details, refer to the sample code.</dd>
<dt>btf</dt>
<dd>Number of bytes to forward in range of <tt>UINT</tt>.</dd>
<dt>bf</dt>
<dd>Pointer to the <tt>UINT</tt> variable to return number of bytes forwarded.</dd>
</dl>
</div>


<div class="para ret">
<h4>Return Values</h4>
<p>
<a href="rc.html#ok">FR_OK</a>,
<a href="rc.html#de">FR_DISK_ERR</a>,
<a href="rc.html#ie">FR_INT_ERR</a>,
<a href="rc.html#io">FR_INVALID_OBJECT</a>,
<a href="rc.html#tm">FR_TIMEOUT</a>
</p>
</div>


<div class="para desc">
<h4>Description</h4>
<p>The <tt>f_forward</tt> function reads the data from the file and forward it to the outgoing stream without data buffer. This is suitable for small memory system because it does not require any data buffer at application module. The file pointer of the file object increases in number of bytes forwarded. In case of <tt class="arg">*bf</tt> is less than <tt class="arg">btf</tt> without error, it means the requested bytes could not be transferred due to end of file or stream goes busy during data transfer.</p>
</div>


<div class="para comp">
<h4>QuickInfo</h4>
<p>Available when <tt>_USE_FORWARD == 1</tt>.</p>
</div>


<div class="para use">
<h4>Example (Audio playback)</h4>
<pre>
<span class="c">/*------------------------------------------------------------------------*/</span>
<span class="c">/* Sample code of data transfer function to be called back from f_forward */</span>
<span class="c">/*------------------------------------------------------------------------*/</span>

UINT out_stream (   <span class="c">/* Returns number of bytes sent or stream status */</span>
    const BYTE *p,  <span class="c">/* Pointer to the data block to be sent */</span>
    UINT btf        <span class="c">/* &gt;0: Transfer call (Number of bytes to be sent). 0: Sense call */</span>
)
{
    UINT cnt = 0;


    if (btf == 0) {     <span class="c">/* Sense call */</span>
        <span class="c">/* Return stream status (0: Busy, 1: Ready) */</span>
        <span class="c">/* When once it returned ready to sense call, it must accept a byte at least */</span>
        <span class="c">/* at subsequent transfer call, or f_forward will fail with FR_INT_ERR. */</span>
        if (FIFO_READY) cnt = 1;
    }
    else {              <span class="c">/* Transfer call */</span>
        do {    <span class="c">/* Repeat while there is any data to be sent and the stream is ready */</span>
            FIFO_PORT = *p++;
            cnt++;
        } while (cnt &lt; btf &amp;&amp; FIFO_READY);
    }

    return cnt;
}


<span class="c">/*------------------------------------------------------------------------*/</span>
<span class="c">/* Sample code using f_forward function                                   */</span>
<span class="c">/*------------------------------------------------------------------------*/</span>

FRESULT play_file (
    char *fn        <span class="c">/* Pointer to the audio file name to be played */</span>
)
{
    FRESULT rc;
    FIL fil;
    UINT dmy;

    <span class="c">/* Open the audio file in read only mode */</span>
    rc = f_open(&amp;fil, fn, FA_READ);
    if (rc) return rc;

    <span class="c">/* Repeat until the file pointer reaches end of the file */</span>
    while (rc == FR_OK &amp;&amp; !f_eof(&amp;fil)) {

        <span class="c">/* any other processes... */</span>

        <span class="c">/* Fill output stream periodicaly or on-demand */</span>
        rc = f_forward(&amp;fil, out_stream, 1000, &amp;dmy);
    }

    <span class="c">/* Close the file and return */</span>
    f_close(&amp;fil);
    return rc;
}
</pre>
</div>


<div class="para ref">
<h4>See Also</h4>
<p><tt><a href="open.html">f_open</a>, <a href="gets.html">fgets</a>, <a href="write.html">f_write</a>, <a href="close.html">f_close</a>, <a href="sfile.html">FIL</a></tt></p>
</div>

<p class="foot"><a href="../00index_e.html">Return</a></p>
</body>
</html>
