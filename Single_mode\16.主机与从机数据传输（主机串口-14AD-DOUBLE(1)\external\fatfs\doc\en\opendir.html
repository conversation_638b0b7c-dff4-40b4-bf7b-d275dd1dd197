<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html lang="en">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<meta http-equiv="Content-Style-Type" content="text/css">
<link rel="up" title="FatFs" href="../00index_e.html">
<link rel="alternate" hreflang="ja" title="Japanese" href="../ja/opendir.html">
<link rel="stylesheet" href="../css_e.css" type="text/css" media="screen" title="ELM Default">
<title>FatFs - f_opendir</title>
</head>

<body>

<div class="para func">
<h2>f_opendir</h2>
<p>The f_opendir function opens a directory.</p>
<pre>
FRESULT f_opendir (
  DIR* <span class="arg">dp</span>,           <span class="c">/* [OUT] Pointer to the directory object structure */</span>
  const TCHAR* <span class="arg">path</span>  <span class="c">/* [IN] Directory name */</span>
);
</pre>
</div>

<div class="para arg">
<h4>Parameters</h4>
<dl class="par">
<dt>dp</dt>
<dd>Pointer to the blank directory object to create a new one.</dd>
<dt>path</dt>
<dd>Pointer to the null-terminated string that specifies the <a href="filename.html">directory name</a> to be opened.</dd>
</dl>
</div>


<div class="para ret">
<h4>Return Values</h4>
<p>
<a href="rc.html#ok">FR_OK</a>,
<a href="rc.html#de">FR_DISK_ERR</a>,
<a href="rc.html#ie">FR_INT_ERR</a>,
<a href="rc.html#nr">FR_NOT_READY</a>,
<a href="rc.html#np">FR_NO_PATH</a>,
<a href="rc.html#in">FR_INVALID_NAME</a>,
<a href="rc.html#io">FR_INVALID_OBJECT</a>,
<a href="rc.html#id">FR_INVALID_DRIVE</a>,
<a href="rc.html#ne">FR_NOT_ENABLED</a>,
<a href="rc.html#ns">FR_NO_FILESYSTEM</a>,
<a href="rc.html#tm">FR_TIMEOUT</a>,
<a href="rc.html#nc">FR_NOT_ENOUGH_CORE</a>,
<a href="rc.html#tf">FR_TOO_MANY_OPEN_FILES</a>
</p>
</div>


<div class="para desc">
<h4>Description</h4>
<p>The <tt>f_opendir</tt> function opens an exsisting directory and creates a directory object for subsequent <tt>f_readdir</tt> function.</p>
</div>


<div class="para comp">
<h4>QuickInfo</h4>
<p>Available when <tt>_FS_MINIMIZE &lt;= 1</tt>.</p>
</div>


<div class="para ref">
<h4>See Also</h4>
<p><tt><a href="readdir.html">f_readdir</a>, <a href="closedir.html">f_closedir</a>, <a href="sdir.html">DIR</a></tt></p>
</div>

<p class="foot"><a href="../00index_e.html">Return</a></p>
</body>
</html>
