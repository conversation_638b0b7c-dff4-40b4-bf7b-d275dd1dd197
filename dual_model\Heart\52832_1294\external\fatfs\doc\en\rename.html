<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html lang="en">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=ISO-8859-1">
<meta http-equiv="Content-Style-Type" content="text/css">
<link rel="up" title="FatFs" href="../00index_e.html">
<link rel="alternate" hreflang="ja" title="Japanese" href="../ja/rename.html">
<link rel="stylesheet" href="../css_e.css" type="text/css" media="screen" title="ELM Default">
<title>FatFs - f_rename</title>
</head>

<body>

<div class="para func">
<h2>f_rename</h2>
<p>The f_rename function renames and/or moves a file or sub-directory.</p>
<pre>
FRESULT f_rename (
  const TCHAR* <span class="arg">old_name</span>, <span class="c">/* [IN] Old object name */</span>
  const TCHAR* <span class="arg">new_name</span>  <span class="c">/* [IN] New object name */</span>
);
</pre>
</div>

<div class="para arg">
<h4>Parameters</h4>
<dl class="par">
<dt>old_name</dt>
<dd>Pointer to a null-terminated string that specifies the existing <a href="filename.html">file or sub-directory</a> to be renamed.</dd>
<dt>new_name</dt>
<dd>Pointer to a null-terminated string that specifies the new object name. A drive number may be specified in this string but it is ignored and assumed as the same drive of the <tt class="arg">old_name</tt>. Any object with this path name except <tt class="arg">old_name</tt> must not be exist, or the function fails with <tt>FR_EXIST</tt>.</dd>
</dl>
</div>


<div class="para ret">
<h4>Return Values</h4>
<p>
<a href="rc.html#ok">FR_OK</a>,
<a href="rc.html#de">FR_DISK_ERR</a>,
<a href="rc.html#ie">FR_INT_ERR</a>,
<a href="rc.html#nr">FR_NOT_READY</a>,
<a href="rc.html#ok">FR_NO_FILE</a>,
<a href="rc.html#np">FR_NO_PATH</a>,
<a href="rc.html#in">FR_INVALID_NAME</a>,
<a href="rc.html#de">FR_DENIED</a>,
<a href="rc.html#ex">FR_EXIST</a>,
<a href="rc.html#wp">FR_WRITE_PROTECTED</a>,
<a href="rc.html#id">FR_INVALID_DRIVE</a>,
<a href="rc.html#ne">FR_NOT_ENABLED</a>,
<a href="rc.html#ns">FR_NO_FILESYSTEM</a>,
<a href="rc.html#tm">FR_TIMEOUT</a>,
<a href="rc.html#lo">FR_LOCKED</a>,
<a href="rc.html#nc">FR_NOT_ENOUGH_CORE</a>
</p>
</div>


<div class="para desc">
<h4>Description</h4>
<p>Renames a file or sub-directory and can also move it to other directory in the same volume. The object to be renamed must not be an open object, or <em>the FAT volume can be collapsed</em>. Such the wrong operation is rejected safely when <a href="appnote.html#dup">file lock function</a> is enabled.</p>
</div>


<div class="para comp">
<h4>QuickInfo</h4>
<p>Available when <tt>_FS_READONLY == 0</tt> and <tt>_FS_MINIMIZE == 0</tt>.</p>
</div>


<div class="para use">
<h4>Example</h4>
<pre>
    <span class="c">/* Rename an object in the default drive */</span>
    f_rename("oldname.txt", "newname.txt");

    <span class="c">/* Rename an object in the drive 2 */</span>
    f_rename("2:oldname.txt", "newname.txt");

    <span class="c">/* Rename an object and move it to other directory in the volume */</span>
    f_rename("log.txt", "old/log0001.txt");
</pre>
</div>


<p class="foot"><a href="../00index_e.html">Return</a></p>
</body>
</html>
