Component: ARM Compiler 5.06 update 7 (build 960) Tool: armlink [4d3601]

==============================================================================

Section Cross References

    main.o(i.assert_nrf_callback) refers to app_error_handler_keil.o(.emb_text) for app_error_handler
    main.o(i.ble_evt_handler) refers to nrf_log_frontend.o(i.nrf_log_frontend_std_0) for nrf_log_frontend_std_0
    main.o(i.ble_evt_handler) refers to bsp.o(i.bsp_indication_set) for bsp_indication_set
    main.o(i.ble_evt_handler) refers to app_error.o(i.app_error_handler_bare) for app_error_handler_bare
    main.o(i.ble_evt_handler) refers to nrf_ble_qwr.o(i.nrf_ble_qwr_conn_handle_assign) for nrf_ble_qwr_conn_handle_assign
    main.o(i.ble_evt_handler) refers to main.o(.data) for .data
    main.o(i.ble_evt_handler) refers to nrf_log_frontend.o(log_const_data) for m_nrf_log_app_logs_data_const
    main.o(i.ble_evt_handler) refers to main.o(.bss) for .bss
    main.o(i.bsp_event_handler) refers to main.o(i.sleep_mode_enter) for sleep_mode_enter
    main.o(i.bsp_event_handler) refers to app_error.o(i.app_error_handler_bare) for app_error_handler_bare
    main.o(i.bsp_event_handler) refers to ble_advertising.o(i.ble_advertising_restart_without_whitelist) for ble_advertising_restart_without_whitelist
    main.o(i.bsp_event_handler) refers to main.o(.data) for .data
    main.o(i.bsp_event_handler) refers to main.o(.bss) for .bss
    main.o(i.conn_params_error_handler) refers to app_error.o(i.app_error_handler_bare) for app_error_handler_bare
    main.o(i.datacjfunc) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    main.o(i.datacjfunc) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    main.o(i.datacjfunc) refers to ble_nus.o(i.ble_nus_data_send) for ble_nus_data_send
    main.o(i.datacjfunc) refers to main.o(.bss) for .bss
    main.o(i.datacjfunc) refers to main.o(.data) for .data
    main.o(i.gatt_evt_handler) refers to nrf_log_frontend.o(i.nrf_log_frontend_std_2) for nrf_log_frontend_std_2
    main.o(i.gatt_evt_handler) refers to main.o(.data) for .data
    main.o(i.gatt_evt_handler) refers to nrf_log_frontend.o(log_const_data) for m_nrf_log_app_logs_data_const
    main.o(i.gatt_init) refers to nrf_ble_gatt.o(i.nrf_ble_gatt_init) for nrf_ble_gatt_init
    main.o(i.gatt_init) refers to app_error.o(i.app_error_handler_bare) for app_error_handler_bare
    main.o(i.gatt_init) refers to nrf_ble_gatt.o(i.nrf_ble_gatt_att_mtu_periph_set) for nrf_ble_gatt_att_mtu_periph_set
    main.o(i.gatt_init) refers to main.o(i.gatt_evt_handler) for gatt_evt_handler
    main.o(i.gatt_init) refers to main.o(.bss) for .bss
    main.o(i.gpio_init) refers to nrfx_gpiote.o(i.nrfx_gpiote_init) for nrfx_gpiote_init
    main.o(i.gpio_init) refers to app_error.o(i.app_error_handler_bare) for app_error_handler_bare
    main.o(i.gpio_init) refers to nrfx_gpiote.o(i.nrfx_gpiote_in_init) for nrfx_gpiote_in_init
    main.o(i.gpio_init) refers to nrfx_gpiote.o(i.nrfx_gpiote_in_event_enable) for nrfx_gpiote_in_event_enable
    main.o(i.gpio_init) refers to nrf_log_frontend.o(i.nrf_log_frontend_std_0) for nrf_log_frontend_std_0
    main.o(i.gpio_init) refers to nrf_log_frontend.o(i.nrf_log_frontend_dequeue) for nrf_log_frontend_dequeue
    main.o(i.gpio_init) refers to main.o(.constdata) for .constdata
    main.o(i.gpio_init) refers to main.o(i.in_pin_handler) for in_pin_handler
    main.o(i.gpio_init) refers to nrf_log_frontend.o(log_const_data) for m_nrf_log_app_logs_data_const
    main.o(i.in_pin_handler) refers to ads1292.o(i.ADS_READDATA) for ADS_READDATA
    main.o(i.in_pin_handler) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    main.o(i.in_pin_handler) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    main.o(i.in_pin_handler) refers to main.o(.data) for .data
    main.o(i.in_pin_handler) refers to main.o(.bss) for .bss
    main.o(i.main) refers to led.o(i.LED_Init) for LED_Init
    main.o(i.main) refers to nrf_log_frontend.o(i.nrf_log_init) for nrf_log_init
    main.o(i.main) refers to app_error.o(i.app_error_handler_bare) for app_error_handler_bare
    main.o(i.main) refers to nrf_log_default_backends.o(i.nrf_log_default_backends_init) for nrf_log_default_backends_init
    main.o(i.main) refers to main.o(i.timers_init) for timers_init
    main.o(i.main) refers to nrf_pwr_mgmt.o(i.nrf_pwr_mgmt_init) for nrf_pwr_mgmt_init
    main.o(i.main) refers to nrf_sdh.o(i.nrf_sdh_enable_request) for nrf_sdh_enable_request
    main.o(i.main) refers to nrf_sdh_ble.o(i.nrf_sdh_ble_default_cfg_set) for nrf_sdh_ble_default_cfg_set
    main.o(i.main) refers to nrf_sdh_ble.o(i.nrf_sdh_ble_enable) for nrf_sdh_ble_enable
    main.o(i.main) refers to main.o(i.gatt_init) for gatt_init
    main.o(i.main) refers to nrf_ble_qwr.o(i.nrf_ble_qwr_init) for nrf_ble_qwr_init
    main.o(i.main) refers to ble_nus.o(i.ble_nus_init) for ble_nus_init
    main.o(i.main) refers to memseta.o(.text) for __aeabi_memclr4
    main.o(i.main) refers to ble_advertising.o(i.ble_advertising_init) for ble_advertising_init
    main.o(i.main) refers to ble_advertising.o(i.ble_advertising_conn_cfg_tag_set) for ble_advertising_conn_cfg_tag_set
    main.o(i.main) refers to ble_conn_params.o(i.ble_conn_params_init) for ble_conn_params_init
    main.o(i.main) refers to ble_advertising.o(i.ble_advertising_start) for ble_advertising_start
    main.o(i.main) refers to ads1292.o(i.ads1299_spi_init) for ads1299_spi_init
    main.o(i.main) refers to main.o(i.gpio_init) for gpio_init
    main.o(i.main) refers to led.o(i.LED1_Close) for LED1_Close
    main.o(i.main) refers to led.o(i.LED2_Close) for LED2_Close
    main.o(i.main) refers to main.o(i.datacjfunc) for datacjfunc
    main.o(i.main) refers to nrf_log_frontend.o(i.nrf_log_frontend_dequeue) for nrf_log_frontend_dequeue
    main.o(i.main) refers to nrf_pwr_mgmt.o(i.nrf_pwr_mgmt_run) for nrf_pwr_mgmt_run
    main.o(i.main) refers to main.o(i.nrf_qwr_error_handler) for nrf_qwr_error_handler
    main.o(i.main) refers to main.o(.bss) for .bss
    main.o(i.main) refers to main.o(i.nus_data_handler) for nus_data_handler
    main.o(i.main) refers to main.o(.data) for .data
    main.o(i.main) refers to main.o(i.on_adv_evt) for on_adv_evt
    main.o(i.main) refers to main.o(i.on_conn_params_evt) for on_conn_params_evt
    main.o(i.main) refers to main.o(i.conn_params_error_handler) for conn_params_error_handler
    main.o(i.nrf_delay_ms) refers to main.o(.constdata) for .constdata
    main.o(i.nrf_qwr_error_handler) refers to app_error.o(i.app_error_handler_bare) for app_error_handler_bare
    main.o(i.nus_data_handler) refers to memseta.o(.text) for __aeabi_memclr4
    main.o(i.nus_data_handler) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    main.o(i.nus_data_handler) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    main.o(i.nus_data_handler) refers to memcpya.o(.text) for __aeabi_memcpy
    main.o(i.nus_data_handler) refers to ble_nus.o(i.ble_nus_data_send) for ble_nus_data_send
    main.o(i.nus_data_handler) refers to led.o(i.LED1_Open) for LED1_Open
    main.o(i.nus_data_handler) refers to main.o(i.nrf_delay_ms) for nrf_delay_ms
    main.o(i.nus_data_handler) refers to led.o(i.LED2_Close) for LED2_Close
    main.o(i.nus_data_handler) refers to led.o(i.LED1_Close) for LED1_Close
    main.o(i.nus_data_handler) refers to led.o(i.LED2_Open) for LED2_Open
    main.o(i.nus_data_handler) refers to main.o(i.nrf_gpio_pin_toggle) for nrf_gpio_pin_toggle
    main.o(i.nus_data_handler) refers to main.o(i.sleep_mode_enter) for sleep_mode_enter
    main.o(i.nus_data_handler) refers to app_uart_fifo.o(i.app_uart_put) for app_uart_put
    main.o(i.nus_data_handler) refers to main.o(.data) for .data
    main.o(i.nus_data_handler) refers to main.o(.bss) for .bss
    main.o(i.on_adv_evt) refers to bsp.o(i.bsp_indication_set) for bsp_indication_set
    main.o(i.on_adv_evt) refers to app_error.o(i.app_error_handler_bare) for app_error_handler_bare
    main.o(i.on_adv_evt) refers to main.o(i.sleep_mode_enter) for sleep_mode_enter
    main.o(i.on_conn_params_evt) refers to app_error.o(i.app_error_handler_bare) for app_error_handler_bare
    main.o(i.on_conn_params_evt) refers to main.o(.data) for .data
    main.o(i.saadc_sampling_event_enable) refers to nrfx_ppi.o(i.nrfx_ppi_channel_enable) for nrfx_ppi_channel_enable
    main.o(i.saadc_sampling_event_enable) refers to app_error.o(i.app_error_handler_bare) for app_error_handler_bare
    main.o(i.saadc_sampling_event_enable) refers to main.o(.data) for .data
    main.o(i.sleep_mode_enter) refers to bsp.o(i.bsp_indication_set) for bsp_indication_set
    main.o(i.sleep_mode_enter) refers to app_error.o(i.app_error_handler_bare) for app_error_handler_bare
    main.o(i.sleep_mode_enter) refers to bsp_btn_ble.o(i.bsp_btn_ble_sleep_mode_prepare) for bsp_btn_ble_sleep_mode_prepare
    main.o(i.timers_init) refers to app_timer2.o(i.app_timer_init) for app_timer_init
    main.o(i.timers_init) refers to app_error.o(i.app_error_handler_bare) for app_error_handler_bare
    main.o(i.timers_init) refers to nrfx_timer.o(i.nrfx_timer_init) for nrfx_timer_init
    main.o(i.timers_init) refers to llushr.o(.text) for __aeabi_llsr
    main.o(i.timers_init) refers to nrf_log_frontend.o(i.nrf_log_frontend_std_1) for nrf_log_frontend_std_1
    main.o(i.timers_init) refers to nrfx_timer.o(i.nrfx_timer_extended_compare) for nrfx_timer_extended_compare
    main.o(i.timers_init) refers to main.o(.constdata) for .constdata
    main.o(i.timers_init) refers to main.o(i.timer_data_event_handler) for timer_data_event_handler
    main.o(i.timers_init) refers to main.o(.data) for .data
    main.o(i.timers_init) refers to nrf_log_frontend.o(log_const_data) for m_nrf_log_app_logs_data_const
    main.o(i.uart_event_handle) refers to app_error.o(i.app_error_handler_bare) for app_error_handler_bare
    main.o(i.uart_event_handle) refers to app_uart_fifo.o(i.app_uart_get) for app_uart_get
    main.o(i.uart_event_handle) refers to ble_nus.o(i.ble_nus_data_send) for ble_nus_data_send
    main.o(i.uart_event_handle) refers to main.o(.bss) for .bss
    main.o(i.uart_event_handle) refers to main.o(.data) for .data
    main.o(.data) refers to main.o(.data) for m_nus_link_ctx_storage_ctx_data_pool
    main.o(.data) refers to main.o(.data) for m_nus_link_ctx_storage
    main.o(sdh_ble_observers1) refers to nrf_ble_gatt.o(i.nrf_ble_gatt_on_ble_evt) for nrf_ble_gatt_on_ble_evt
    main.o(sdh_ble_observers1) refers to main.o(.bss) for m_gatt
    main.o(sdh_ble_observers1) refers to ble_advertising.o(i.ble_advertising_on_ble_evt) for ble_advertising_on_ble_evt
    main.o(sdh_ble_observers2) refers to ble_nus.o(i.ble_nus_on_ble_evt) for ble_nus_on_ble_evt
    main.o(sdh_ble_observers2) refers to main.o(.data) for m_nus
    main.o(sdh_ble_observers2) refers to nrf_ble_qwr.o(i.nrf_ble_qwr_on_ble_evt) for nrf_ble_qwr_on_ble_evt
    main.o(sdh_ble_observers2) refers to main.o(.bss) for m_qwr
    main.o(sdh_ble_observers3) refers to main.o(i.ble_evt_handler) for ble_evt_handler
    dht11.o(i.dht_Check) refers to dht11.o(i.nrfx_coredep_delay_us) for nrfx_coredep_delay_us
    dht11.o(i.dht_Check) refers to printfb.o(i.__0printf$bare) for __2printf
    dht11.o(i.dht_Init) refers to dht11.o(i.dht_Rst) for dht_Rst
    dht11.o(i.dht_Init) refers to dht11.o(i.dht_Check) for dht_Check
    dht11.o(i.dht_Read_Bit) refers to dht11.o(i.nrfx_coredep_delay_us) for nrfx_coredep_delay_us
    dht11.o(i.dht_Read_Byte) refers to dht11.o(i.dht_Read_Bit) for dht_Read_Bit
    dht11.o(i.dht_Read_Data) refers to dht11.o(i.dht_Rst) for dht_Rst
    dht11.o(i.dht_Read_Data) refers to dht11.o(i.dht_Check) for dht_Check
    dht11.o(i.dht_Read_Data) refers to dht11.o(i.dht_Read_Byte) for dht_Read_Byte
    dht11.o(i.dht_Rst) refers to dht11.o(i.nrfx_coredep_delay_us) for nrfx_coredep_delay_us
    dht11.o(i.nrfx_coredep_delay_us) refers to dht11.o(.constdata) for .constdata
    ads1292.o(i.ADS1299_CMD) refers to ads1292.o(i.nrf_drv_spi_transfer) for nrf_drv_spi_transfer
    ads1292.o(i.ADS1299_CMD) refers to app_error.o(i.app_error_handler_bare) for app_error_handler_bare
    ads1292.o(i.ADS1299_CMD) refers to ads1292.o(.bss) for .bss
    ads1292.o(i.ADS1299_CMD) refers to ads1292.o(.data) for .data
    ads1292.o(i.ADS1299_CMD) refers to ads1292.o(.constdata) for .constdata
    ads1292.o(i.ADS1299_READREG) refers to ads1292.o(i.nrf_drv_spi_transfer) for nrf_drv_spi_transfer
    ads1292.o(i.ADS1299_READREG) refers to ads1292.o(.bss) for .bss
    ads1292.o(i.ADS1299_READREG) refers to ads1292.o(.data) for .data
    ads1292.o(i.ADS1299_READREG) refers to ads1292.o(.constdata) for .constdata
    ads1292.o(i.ADS1299_WRITEREG) refers to ads1292.o(i.nrf_drv_spi_transfer) for nrf_drv_spi_transfer
    ads1292.o(i.ADS1299_WRITEREG) refers to ads1292.o(.bss) for .bss
    ads1292.o(i.ADS1299_WRITEREG) refers to ads1292.o(.data) for .data
    ads1292.o(i.ADS1299_WRITEREG) refers to ads1292.o(.constdata) for .constdata
    ads1292.o(i.ADS_READDATA) refers to memseta.o(.text) for __aeabi_memclr
    ads1292.o(i.ADS_READDATA) refers to ads1292.o(i.nrf_drv_spi_transfer) for nrf_drv_spi_transfer
    ads1292.o(i.ADS_READDATA) refers to ads1292.o(.bss) for .bss
    ads1292.o(i.ADS_READDATA) refers to ads1292.o(.data) for .data
    ads1292.o(i.ADS_READDATA) refers to ads1292.o(.constdata) for .constdata
    ads1292.o(i.ads1299_spi_init) refers to ads1292.o(i.nrf_gpio_cfg) for nrf_gpio_cfg
    ads1292.o(i.ads1299_spi_init) refers to nrf_drv_spi.o(i.nrf_drv_spi_init) for nrf_drv_spi_init
    ads1292.o(i.ads1299_spi_init) refers to app_error.o(i.app_error_handler_bare) for app_error_handler_bare
    ads1292.o(i.ads1299_spi_init) refers to ads1292.o(i.nrf_gpio_cfg_output) for nrf_gpio_cfg_output
    ads1292.o(i.ads1299_spi_init) refers to ads1292.o(i.ADS1299_CMD) for ADS1299_CMD
    ads1292.o(i.ads1299_spi_init) refers to ads1292.o(i.nrf_delay_ms) for nrf_delay_ms
    ads1292.o(i.ads1299_spi_init) refers to ads1292.o(i.nrfx_coredep_delay_us) for nrfx_coredep_delay_us
    ads1292.o(i.ads1299_spi_init) refers to ads1292.o(i.ADS1299_READREG) for ADS1299_READREG
    ads1292.o(i.ads1299_spi_init) refers to nrf_log_frontend.o(i.nrf_log_frontend_std_1) for nrf_log_frontend_std_1
    ads1292.o(i.ads1299_spi_init) refers to nrf_log_frontend.o(i.nrf_log_frontend_dequeue) for nrf_log_frontend_dequeue
    ads1292.o(i.ads1299_spi_init) refers to ads1292.o(i.ADS1299_WRITEREG) for ADS1299_WRITEREG
    ads1292.o(i.ads1299_spi_init) refers to nrf_log_frontend.o(i.nrf_log_frontend_std_0) for nrf_log_frontend_std_0
    ads1292.o(i.ads1299_spi_init) refers to ads1292.o(.constdata) for .constdata
    ads1292.o(i.ads1299_spi_init) refers to ads1292.o(i.spi_event_handler) for spi_event_handler
    ads1292.o(i.ads1299_spi_init) refers to nrf_log_frontend.o(log_const_data) for m_nrf_log_app_logs_data_const
    ads1292.o(i.nrf_delay_ms) refers to ads1292.o(i.nrfx_coredep_delay_us) for nrfx_coredep_delay_us
    ads1292.o(i.nrf_drv_spi_transfer) refers to nrfx_spim.o(i.nrfx_spim_xfer) for nrfx_spim_xfer
    ads1292.o(i.nrf_gpio_cfg_output) refers to ads1292.o(i.nrf_gpio_cfg) for nrf_gpio_cfg
    ads1292.o(i.nrfx_coredep_delay_us) refers to ads1292.o(.constdata) for .constdata
    ads1292.o(i.spi_event_handler) refers to ads1292.o(.data) for .data
    led.o(i.LED1_Toggle) refers to led.o(i.nrf_gpio_pin_toggle) for nrf_gpio_pin_toggle
    led.o(i.LED2_Toggle) refers to led.o(i.nrf_gpio_pin_toggle) for nrf_gpio_pin_toggle
    led.o(i.LED3_Toggle) refers to led.o(i.nrf_gpio_pin_toggle) for nrf_gpio_pin_toggle
    led.o(i.LED_Init) refers to led.o(i.nrf_gpio_cfg_output) for nrf_gpio_cfg_output
    boards.o(i.bsp_board_button_idx_to_pin) refers to boards.o(.constdata) for .constdata
    boards.o(i.bsp_board_button_state_get) refers to boards.o(.constdata) for .constdata
    boards.o(i.bsp_board_init) refers to boards.o(i.nrf_gpio_cfg) for nrf_gpio_cfg
    boards.o(i.bsp_board_init) refers to boards.o(i.bsp_board_leds_off) for bsp_board_leds_off
    boards.o(i.bsp_board_init) refers to boards.o(.constdata) for .constdata
    boards.o(i.bsp_board_led_idx_to_pin) refers to boards.o(.constdata) for .constdata
    boards.o(i.bsp_board_led_invert) refers to boards.o(.constdata) for .constdata
    boards.o(i.bsp_board_led_off) refers to boards.o(i.nrf_gpio_pin_write) for nrf_gpio_pin_write
    boards.o(i.bsp_board_led_off) refers to boards.o(.constdata) for .constdata
    boards.o(i.bsp_board_led_on) refers to boards.o(i.nrf_gpio_pin_write) for nrf_gpio_pin_write
    boards.o(i.bsp_board_led_on) refers to boards.o(.constdata) for .constdata
    boards.o(i.bsp_board_led_state_get) refers to boards.o(.constdata) for .constdata
    boards.o(i.bsp_board_leds_off) refers to boards.o(i.bsp_board_led_off) for bsp_board_led_off
    boards.o(i.bsp_board_leds_on) refers to boards.o(i.bsp_board_led_on) for bsp_board_led_on
    boards.o(i.bsp_board_pin_to_button_idx) refers to boards.o(.constdata) for .constdata
    boards.o(i.bsp_board_pin_to_led_idx) refers to boards.o(.constdata) for .constdata
    bsp.o(i.alert_timer_handler) refers to boards.o(i.bsp_board_led_invert) for bsp_board_led_invert
    bsp.o(i.bsp_button_event_handler) refers to boards.o(i.bsp_board_pin_to_button_idx) for bsp_board_pin_to_button_idx
    bsp.o(i.bsp_button_event_handler) refers to app_timer2.o(i.app_timer_start) for app_timer_start
    bsp.o(i.bsp_button_event_handler) refers to app_timer2.o(i.app_timer_stop) for app_timer_stop
    bsp.o(i.bsp_button_event_handler) refers to bsp.o(.constdata) for .constdata
    bsp.o(i.bsp_button_event_handler) refers to bsp.o(.data) for .data
    bsp.o(i.bsp_button_event_handler) refers to bsp.o(.bss) for .bss
    bsp.o(i.bsp_button_is_pressed) refers to boards.o(i.bsp_board_button_state_get) for bsp_board_button_state_get
    bsp.o(i.bsp_buttons_disable) refers to app_button.o(i.app_button_disable) for app_button_disable
    bsp.o(i.bsp_buttons_enable) refers to app_button.o(i.app_button_enable) for app_button_enable
    bsp.o(i.bsp_event_to_button_action_assign) refers to bsp.o(.bss) for .bss
    bsp.o(i.bsp_indication_set) refers to bsp.o(i.bsp_led_indication) for bsp_led_indication
    bsp.o(i.bsp_indication_set) refers to bsp.o(.data) for .data
    bsp.o(i.bsp_init) refers to bsp.o(i.bsp_event_to_button_action_assign) for bsp_event_to_button_action_assign
    bsp.o(i.bsp_init) refers to app_button.o(i.app_button_init) for app_button_init
    bsp.o(i.bsp_init) refers to app_button.o(i.app_button_enable) for app_button_enable
    bsp.o(i.bsp_init) refers to app_timer2.o(i.app_timer_create) for app_timer_create
    bsp.o(i.bsp_init) refers to boards.o(i.bsp_board_init) for bsp_board_init
    bsp.o(i.bsp_init) refers to bsp.o(.data) for .data
    bsp.o(i.bsp_init) refers to bsp.o(.constdata) for .constdata
    bsp.o(i.bsp_init) refers to bsp.o(i.button_timer_handler) for button_timer_handler
    bsp.o(i.bsp_init) refers to bsp.o(i.leds_timer_handler) for leds_timer_handler
    bsp.o(i.bsp_init) refers to bsp.o(i.alert_timer_handler) for alert_timer_handler
    bsp.o(i.bsp_led_indication) refers to bsp.o(i.leds_off) for leds_off
    bsp.o(i.bsp_led_indication) refers to app_timer2.o(i.app_timer_stop) for app_timer_stop
    bsp.o(i.bsp_led_indication) refers to boards.o(i.bsp_board_led_state_get) for bsp_board_led_state_get
    bsp.o(i.bsp_led_indication) refers to boards.o(i.bsp_board_led_off) for bsp_board_led_off
    bsp.o(i.bsp_led_indication) refers to boards.o(i.bsp_board_led_on) for bsp_board_led_on
    bsp.o(i.bsp_led_indication) refers to uldiv.o(.text) for __aeabi_uldivmod
    bsp.o(i.bsp_led_indication) refers to app_timer2.o(i.app_timer_start) for app_timer_start
    bsp.o(i.bsp_led_indication) refers to boards.o(i.bsp_board_led_invert) for bsp_board_led_invert
    bsp.o(i.bsp_led_indication) refers to boards.o(i.bsp_board_leds_on) for bsp_board_leds_on
    bsp.o(i.bsp_led_indication) refers to bsp.o(.data) for .data
    bsp.o(i.bsp_led_indication) refers to bsp.o(.constdata) for .constdata
    bsp.o(i.bsp_wakeup_button_disable) refers to bsp.o(i.wakeup_button_cfg) for wakeup_button_cfg
    bsp.o(i.bsp_wakeup_button_enable) refers to bsp.o(i.wakeup_button_cfg) for wakeup_button_cfg
    bsp.o(i.button_timer_handler) refers to bsp.o(i.bsp_button_event_handler) for bsp_button_event_handler
    bsp.o(i.leds_off) refers to boards.o(i.bsp_board_led_off) for bsp_board_led_off
    bsp.o(i.leds_off) refers to boards.o(i.bsp_board_leds_off) for bsp_board_leds_off
    bsp.o(i.leds_off) refers to bsp.o(.data) for .data
    bsp.o(i.leds_timer_handler) refers to bsp.o(i.bsp_led_indication) for bsp_led_indication
    bsp.o(i.leds_timer_handler) refers to bsp.o(.data) for .data
    bsp.o(i.wakeup_button_cfg) refers to boards.o(i.bsp_board_button_idx_to_pin) for bsp_board_button_idx_to_pin
    bsp.o(.constdata) refers to bsp.o(.bss) for m_bsp_leds_tmr_data
    bsp.o(.constdata) refers to bsp.o(.bss) for m_bsp_alert_tmr_data
    bsp.o(.constdata) refers to bsp.o(.bss) for m_bsp_button_tmr_data
    bsp.o(.constdata) refers to bsp.o(i.bsp_button_event_handler) for bsp_button_event_handler
    bsp_btn_ble.o(i.advertising_buttons_configure) refers to bsp.o(i.bsp_event_to_button_action_assign) for bsp_event_to_button_action_assign
    bsp_btn_ble.o(i.ble_evt_handler) refers to bsp_btn_ble.o(i.advertising_buttons_configure) for advertising_buttons_configure
    bsp_btn_ble.o(i.ble_evt_handler) refers to bsp.o(i.bsp_event_to_button_action_assign) for bsp_event_to_button_action_assign
    bsp_btn_ble.o(i.ble_evt_handler) refers to bsp_btn_ble.o(.data) for .data
    bsp_btn_ble.o(i.bsp_btn_ble_init) refers to bsp.o(i.bsp_button_is_pressed) for bsp_button_is_pressed
    bsp_btn_ble.o(i.bsp_btn_ble_init) refers to bsp_btn_ble.o(i.advertising_buttons_configure) for advertising_buttons_configure
    bsp_btn_ble.o(i.bsp_btn_ble_init) refers to bsp_btn_ble.o(.data) for .data
    bsp_btn_ble.o(i.bsp_btn_ble_sleep_mode_prepare) refers to bsp.o(i.bsp_wakeup_button_enable) for bsp_wakeup_button_enable
    bsp_btn_ble.o(sdh_ble_observers1) refers to bsp_btn_ble.o(i.ble_evt_handler) for ble_evt_handler
    utf.o(i.utf16RuneCount) refers to utf.o(i.utf16DecodeRune) for utf16DecodeRune
    utf.o(i.utf16UTF8Count) refers to utf.o(i.utf16DecodeRune) for utf16DecodeRune
    utf.o(i.utf16UTF8Count) refers to utf.o(i.utf8EncodeRune) for utf8EncodeRune
    utf.o(i.utf8RuneCount) refers to utf.o(i.utf8DecodeRune) for utf8DecodeRune
    utf.o(i.utf8UTF16Count) refers to utf.o(i.utf8DecodeRune) for utf8DecodeRune
    utf.o(i.utf8UTF16Count) refers to utf.o(i.utf16EncodeRune) for utf16EncodeRune
    ble_advdata.o(i.ble_advdata_appearance_find) refers to ble_advdata.o(i.ble_advdata_search) for ble_advdata_search
    ble_advdata.o(i.ble_advdata_encode) refers to ble_advdata.o(i.ble_device_addr_encode) for ble_device_addr_encode
    ble_advdata.o(i.ble_advdata_encode) refers to ble_advdata.o(i.uint16_encode) for uint16_encode
    ble_advdata.o(i.ble_advdata_encode) refers to ble_advdata.o(i.uuid_list_encode) for uuid_list_encode
    ble_advdata.o(i.ble_advdata_encode) refers to ble_advdata.o(i.conn_int_encode) for conn_int_encode
    ble_advdata.o(i.ble_advdata_encode) refers to ble_advdata.o(i.manuf_specific_data_encode) for manuf_specific_data_encode
    ble_advdata.o(i.ble_advdata_encode) refers to ble_advdata.o(i.service_data_encode) for service_data_encode
    ble_advdata.o(i.ble_advdata_encode) refers to ble_advdata.o(i.name_encode) for name_encode
    ble_advdata.o(i.ble_advdata_name_find) refers to ble_advdata.o(i.ble_advdata_search) for ble_advdata_search
    ble_advdata.o(i.ble_advdata_name_find) refers to strlen.o(.text) for strlen
    ble_advdata.o(i.ble_advdata_name_find) refers to memcmp.o(.text) for memcmp
    ble_advdata.o(i.ble_advdata_parse) refers to ble_advdata.o(i.ble_advdata_search) for ble_advdata_search
    ble_advdata.o(i.ble_advdata_short_name_find) refers to ble_advdata.o(i.ble_advdata_search) for ble_advdata_search
    ble_advdata.o(i.ble_advdata_short_name_find) refers to strlen.o(.text) for strlen
    ble_advdata.o(i.ble_advdata_short_name_find) refers to memcmp.o(.text) for memcmp
    ble_advdata.o(i.ble_advdata_uuid_find) refers to ble_advdata.o(i.ble_advdata_search) for ble_advdata_search
    ble_advdata.o(i.ble_advdata_uuid_find) refers to memcmp.o(.text) for memcmp
    ble_advdata.o(i.conn_int_encode) refers to ble_advdata.o(i.uint16_encode) for uint16_encode
    ble_advdata.o(i.manuf_specific_data_encode) refers to ble_advdata.o(i.uint16_encode) for uint16_encode
    ble_advdata.o(i.manuf_specific_data_encode) refers to memcpya.o(.text) for __aeabi_memcpy
    ble_advdata.o(i.service_data_encode) refers to ble_advdata.o(i.uint16_encode) for uint16_encode
    ble_advdata.o(i.service_data_encode) refers to memcpya.o(.text) for __aeabi_memcpy
    ble_advdata.o(i.uuid_list_encode) refers to ble_advdata.o(i.uuid_list_sized_encode) for uuid_list_sized_encode
    ble_advertising.o(i.ble_advertising_advdata_update) refers to ble_advertising.o(i.adv_set_data_size_max_get) for adv_set_data_size_max_get
    ble_advertising.o(i.ble_advertising_advdata_update) refers to ble_advdata.o(i.ble_advdata_encode) for ble_advdata_encode
    ble_advertising.o(i.ble_advertising_init) refers to memcpya.o(.text) for __aeabi_memcpy4
    ble_advertising.o(i.ble_advertising_init) refers to ble_advertising.o(i.adv_set_data_size_max_get) for adv_set_data_size_max_get
    ble_advertising.o(i.ble_advertising_init) refers to ble_advdata.o(i.ble_advdata_encode) for ble_advdata_encode
    ble_advertising.o(i.ble_advertising_modes_config_set) refers to memcpya.o(.text) for __aeabi_memcpy4
    ble_advertising.o(i.ble_advertising_on_ble_evt) refers to ble_advertising.o(i.ble_advertising_start) for ble_advertising_start
    ble_advertising.o(i.ble_advertising_restart_without_whitelist) refers to ble_advertising.o(i.flags_set) for flags_set
    ble_advertising.o(i.ble_advertising_restart_without_whitelist) refers to ble_advertising.o(i.ble_advertising_start) for ble_advertising_start
    ble_advertising.o(i.ble_advertising_start) refers to memseta.o(.text) for __aeabi_memclr4
    ble_advertising.o(i.ble_advertising_start) refers to ble_advertising.o(i.phy_is_valid) for phy_is_valid
    ble_advertising.o(i.ble_advertising_start) refers to ble_advertising.o(i.use_whitelist) for use_whitelist
    ble_advertising.o(i.ble_advertising_start) refers to ble_advertising.o(i.flags_set) for flags_set
    ble_advertising.o(i.flags_set) refers to ble_advdata.o(i.ble_advdata_parse) for ble_advdata_parse
    ble_conn_params.o(i.ble_conn_params_change_conn_params) refers to ble_conn_params.o(i.instance_get) for instance_get
    ble_conn_params.o(i.ble_conn_params_change_conn_params) refers to ble_conn_params.o(.data) for .data
    ble_conn_params.o(i.ble_conn_params_init) refers to memcpya.o(.text) for __aeabi_memcpy4
    ble_conn_params.o(i.ble_conn_params_init) refers to app_timer2.o(i.app_timer_create) for app_timer_create
    ble_conn_params.o(i.ble_conn_params_init) refers to ble_conn_params.o(.bss) for .bss
    ble_conn_params.o(i.ble_conn_params_init) refers to ble_conn_params.o(.data) for .data
    ble_conn_params.o(i.ble_conn_params_init) refers to ble_conn_params.o(i.update_timeout_handler) for update_timeout_handler
    ble_conn_params.o(i.ble_conn_params_stop) refers to app_timer2.o(i.app_timer_stop) for app_timer_stop
    ble_conn_params.o(i.ble_conn_params_stop) refers to ble_conn_params.o(.bss) for .bss
    ble_conn_params.o(i.ble_evt_handler) refers to ble_conn_params.o(i.instance_get) for instance_get
    ble_conn_params.o(i.ble_evt_handler) refers to ble_srv_common.o(i.ble_srv_is_notification_enabled) for ble_srv_is_notification_enabled
    ble_conn_params.o(i.ble_evt_handler) refers to ble_conn_params.o(i.is_conn_params_ok) for is_conn_params_ok
    ble_conn_params.o(i.ble_evt_handler) refers to app_timer2.o(i.app_timer_stop) for app_timer_stop
    ble_conn_params.o(i.ble_evt_handler) refers to ble_conn_params.o(i.send_error_evt) for send_error_evt
    ble_conn_params.o(i.ble_evt_handler) refers to ble_conn_params.o(i.conn_params_negotiation) for conn_params_negotiation
    ble_conn_params.o(i.ble_evt_handler) refers to ble_conn_params.o(.bss) for .bss
    ble_conn_params.o(i.ble_evt_handler) refers to ble_conn_params.o(.data) for .data
    ble_conn_params.o(i.conn_params_negotiation) refers to app_timer2.o(i.app_timer_start) for app_timer_start
    ble_conn_params.o(i.conn_params_negotiation) refers to ble_conn_params.o(i.send_error_evt) for send_error_evt
    ble_conn_params.o(i.conn_params_negotiation) refers to ble_conn_params.o(.bss) for .bss
    ble_conn_params.o(i.instance_get) refers to ble_conn_params.o(.bss) for .bss
    ble_conn_params.o(i.send_error_evt) refers to ble_conn_params.o(.bss) for .bss
    ble_conn_params.o(i.update_timeout_handler) refers to ble_conn_params.o(i.instance_get) for instance_get
    ble_conn_params.o(i.update_timeout_handler) refers to ble_conn_params.o(i.send_error_evt) for send_error_evt
    ble_conn_params.o(i.update_timeout_handler) refers to ble_conn_params.o(.bss) for .bss
    ble_conn_params.o(sdh_ble_observers1) refers to ble_conn_params.o(i.ble_evt_handler) for ble_evt_handler
    ble_conn_state.o(i.active_flag_count) refers to nrf_atflags.o(i.nrf_atflags_get) for nrf_atflags_get
    ble_conn_state.o(i.bcs_internal_state_reset) refers to memseta.o(.text) for __aeabi_memclr4
    ble_conn_state.o(i.bcs_internal_state_reset) refers to ble_conn_state.o(.bss) for .bss
    ble_conn_state.o(i.ble_conn_state_central_conn_count) refers to nrf_atomic.o(i.nrf_atomic_u32_and) for nrf_atomic_u32_and
    ble_conn_state.o(i.ble_conn_state_central_conn_count) refers to ble_conn_state.o(i.active_flag_count) for active_flag_count
    ble_conn_state.o(i.ble_conn_state_central_conn_count) refers to ble_conn_state.o(.bss) for .bss
    ble_conn_state.o(i.ble_conn_state_central_handles) refers to nrf_atomic.o(i.nrf_atomic_u32_and) for nrf_atomic_u32_and
    ble_conn_state.o(i.ble_conn_state_central_handles) refers to ble_conn_state.o(i.conn_handle_list_get) for conn_handle_list_get
    ble_conn_state.o(i.ble_conn_state_central_handles) refers to ble_conn_state.o(.bss) for .bss
    ble_conn_state.o(i.ble_conn_state_conn_count) refers to ble_conn_state.o(i.active_flag_count) for active_flag_count
    ble_conn_state.o(i.ble_conn_state_conn_count) refers to ble_conn_state.o(.bss) for .bss
    ble_conn_state.o(i.ble_conn_state_conn_handles) refers to ble_conn_state.o(i.conn_handle_list_get) for conn_handle_list_get
    ble_conn_state.o(i.ble_conn_state_conn_handles) refers to ble_conn_state.o(.bss) for .bss
    ble_conn_state.o(i.ble_conn_state_conn_idx) refers to ble_conn_state.o(i.ble_conn_state_valid) for ble_conn_state_valid
    ble_conn_state.o(i.ble_conn_state_encrypted) refers to ble_conn_state.o(i.ble_conn_state_valid) for ble_conn_state_valid
    ble_conn_state.o(i.ble_conn_state_encrypted) refers to nrf_atflags.o(i.nrf_atflags_get) for nrf_atflags_get
    ble_conn_state.o(i.ble_conn_state_encrypted) refers to ble_conn_state.o(.bss) for .bss
    ble_conn_state.o(i.ble_conn_state_for_each_connected) refers to ble_conn_state.o(i.for_each_set_flag) for for_each_set_flag
    ble_conn_state.o(i.ble_conn_state_for_each_connected) refers to ble_conn_state.o(.bss) for .bss
    ble_conn_state.o(i.ble_conn_state_for_each_set_user_flag) refers to ble_conn_state.o(i.user_flag_is_acquired) for user_flag_is_acquired
    ble_conn_state.o(i.ble_conn_state_for_each_set_user_flag) refers to ble_conn_state.o(i.for_each_set_flag) for for_each_set_flag
    ble_conn_state.o(i.ble_conn_state_for_each_set_user_flag) refers to ble_conn_state.o(.bss) for .bss
    ble_conn_state.o(i.ble_conn_state_init) refers to ble_conn_state.o(i.bcs_internal_state_reset) for bcs_internal_state_reset
    ble_conn_state.o(i.ble_conn_state_lesc) refers to ble_conn_state.o(i.ble_conn_state_valid) for ble_conn_state_valid
    ble_conn_state.o(i.ble_conn_state_lesc) refers to nrf_atflags.o(i.nrf_atflags_get) for nrf_atflags_get
    ble_conn_state.o(i.ble_conn_state_lesc) refers to ble_conn_state.o(.bss) for .bss
    ble_conn_state.o(i.ble_conn_state_mitm_protected) refers to ble_conn_state.o(i.ble_conn_state_valid) for ble_conn_state_valid
    ble_conn_state.o(i.ble_conn_state_mitm_protected) refers to nrf_atflags.o(i.nrf_atflags_get) for nrf_atflags_get
    ble_conn_state.o(i.ble_conn_state_mitm_protected) refers to ble_conn_state.o(.bss) for .bss
    ble_conn_state.o(i.ble_conn_state_periph_handles) refers to nrf_atomic.o(i.nrf_atomic_u32_and) for nrf_atomic_u32_and
    ble_conn_state.o(i.ble_conn_state_periph_handles) refers to ble_conn_state.o(i.conn_handle_list_get) for conn_handle_list_get
    ble_conn_state.o(i.ble_conn_state_periph_handles) refers to ble_conn_state.o(.bss) for .bss
    ble_conn_state.o(i.ble_conn_state_peripheral_conn_count) refers to nrf_atomic.o(i.nrf_atomic_u32_and) for nrf_atomic_u32_and
    ble_conn_state.o(i.ble_conn_state_peripheral_conn_count) refers to ble_conn_state.o(i.active_flag_count) for active_flag_count
    ble_conn_state.o(i.ble_conn_state_peripheral_conn_count) refers to ble_conn_state.o(.bss) for .bss
    ble_conn_state.o(i.ble_conn_state_role) refers to ble_conn_state.o(i.ble_conn_state_valid) for ble_conn_state_valid
    ble_conn_state.o(i.ble_conn_state_role) refers to nrf_atflags.o(i.nrf_atflags_get) for nrf_atflags_get
    ble_conn_state.o(i.ble_conn_state_role) refers to ble_conn_state.o(.bss) for .bss
    ble_conn_state.o(i.ble_conn_state_status) refers to ble_conn_state.o(i.ble_conn_state_valid) for ble_conn_state_valid
    ble_conn_state.o(i.ble_conn_state_status) refers to nrf_atflags.o(i.nrf_atflags_get) for nrf_atflags_get
    ble_conn_state.o(i.ble_conn_state_status) refers to ble_conn_state.o(.bss) for .bss
    ble_conn_state.o(i.ble_conn_state_user_flag_acquire) refers to nrf_atflags.o(i.nrf_atflags_find_and_set_flag) for nrf_atflags_find_and_set_flag
    ble_conn_state.o(i.ble_conn_state_user_flag_acquire) refers to ble_conn_state.o(.bss) for .bss
    ble_conn_state.o(i.ble_conn_state_user_flag_get) refers to ble_conn_state.o(i.user_flag_is_acquired) for user_flag_is_acquired
    ble_conn_state.o(i.ble_conn_state_user_flag_get) refers to ble_conn_state.o(i.ble_conn_state_valid) for ble_conn_state_valid
    ble_conn_state.o(i.ble_conn_state_user_flag_get) refers to nrf_atflags.o(i.nrf_atflags_get) for nrf_atflags_get
    ble_conn_state.o(i.ble_conn_state_user_flag_get) refers to ble_conn_state.o(.bss) for .bss
    ble_conn_state.o(i.ble_conn_state_user_flag_set) refers to ble_conn_state.o(i.user_flag_is_acquired) for user_flag_is_acquired
    ble_conn_state.o(i.ble_conn_state_user_flag_set) refers to ble_conn_state.o(i.ble_conn_state_valid) for ble_conn_state_valid
    ble_conn_state.o(i.ble_conn_state_user_flag_set) refers to ble_conn_state.o(i.flag_toggle) for flag_toggle
    ble_conn_state.o(i.ble_conn_state_user_flag_set) refers to ble_conn_state.o(.bss) for .bss
    ble_conn_state.o(i.ble_conn_state_valid) refers to nrf_atflags.o(i.nrf_atflags_get) for nrf_atflags_get
    ble_conn_state.o(i.ble_conn_state_valid) refers to ble_conn_state.o(.bss) for .bss
    ble_conn_state.o(i.ble_evt_handler) refers to nrf_atomic.o(i.nrf_atomic_u32_and) for nrf_atomic_u32_and
    ble_conn_state.o(i.ble_evt_handler) refers to ble_conn_state.o(i.conn_handle_list_get) for conn_handle_list_get
    ble_conn_state.o(i.ble_evt_handler) refers to memcpya.o(.text) for __aeabi_memcpy4
    ble_conn_state.o(i.ble_evt_handler) refers to nrf_atflags.o(i.nrf_atflags_clear) for nrf_atflags_clear
    ble_conn_state.o(i.ble_evt_handler) refers to app_error.o(i.app_error_handler_bare) for app_error_handler_bare
    ble_conn_state.o(i.ble_evt_handler) refers to nrf_atflags.o(i.nrf_atflags_set) for nrf_atflags_set
    ble_conn_state.o(i.ble_evt_handler) refers to ble_conn_state.o(i.flag_toggle) for flag_toggle
    ble_conn_state.o(i.ble_evt_handler) refers to ble_conn_state.o(.bss) for .bss
    ble_conn_state.o(i.conn_handle_list_get) refers to nrf_atflags.o(i.nrf_atflags_get) for nrf_atflags_get
    ble_conn_state.o(i.conn_handle_list_get) refers to memcpya.o(.text) for __aeabi_memcpy4
    ble_conn_state.o(i.flag_toggle) refers to nrf_atflags.o(i.nrf_atflags_set) for nrf_atflags_set
    ble_conn_state.o(i.flag_toggle) refers to nrf_atflags.o(i.nrf_atflags_clear) for nrf_atflags_clear
    ble_conn_state.o(i.for_each_set_flag) refers to nrf_atflags.o(i.nrf_atflags_get) for nrf_atflags_get
    ble_conn_state.o(i.user_flag_is_acquired) refers to nrf_atflags.o(i.nrf_atflags_get) for nrf_atflags_get
    ble_conn_state.o(i.user_flag_is_acquired) refers to ble_conn_state.o(.bss) for .bss
    ble_conn_state.o(sdh_ble_observers0) refers to ble_conn_state.o(i.ble_evt_handler) for ble_evt_handler
    ble_link_ctx_manager.o(i.blcm_link_ctx_get) refers to ble_conn_state.o(i.ble_conn_state_conn_idx) for ble_conn_state_conn_idx
    ble_srv_common.o(i.ble_srv_ascii_to_utf8) refers to strlen.o(.text) for strlen
    ble_srv_common.o(i.characteristic_add) refers to ble_srv_common.o(i.set_security_req) for set_security_req
    ble_srv_common.o(i.characteristic_add) refers to memseta.o(.text) for __aeabi_memclr4
    ble_srv_common.o(i.descriptor_add) refers to memseta.o(.text) for __aeabi_memclr4
    ble_srv_common.o(i.descriptor_add) refers to ble_srv_common.o(i.set_security_req) for set_security_req
    nrf_ble_gatt.o(i.data_length_update) refers to nrf_strerror.o(i.nrf_strerror_get) for nrf_strerror_get
    nrf_ble_gatt.o(i.data_length_update) refers to nrf_log_frontend.o(i.nrf_log_frontend_std_2) for nrf_log_frontend_std_2
    nrf_ble_gatt.o(i.data_length_update) refers to nrf_log_frontend.o(i.nrf_log_frontend_std_1) for nrf_log_frontend_std_1
    nrf_ble_gatt.o(i.data_length_update) refers to nrf_ble_gatt.o(log_const_data) for log_const_data
    nrf_ble_gatt.o(i.data_length_update) refers to nrf_ble_gatt.o(.conststring) for .conststring
    nrf_ble_gatt.o(i.nrf_ble_gatt_data_length_set) refers to nrf_ble_gatt.o(i.data_length_update) for data_length_update
    nrf_ble_gatt.o(i.nrf_ble_gatt_init) refers to nrf_ble_gatt.o(i.link_init) for link_init
    nrf_ble_gatt.o(i.nrf_ble_gatt_on_ble_evt) refers to nrf_ble_gatt.o(i.on_connected_evt) for on_connected_evt
    nrf_ble_gatt.o(i.nrf_ble_gatt_on_ble_evt) refers to nrf_ble_gatt.o(i.link_init) for link_init
    nrf_ble_gatt.o(i.nrf_ble_gatt_on_ble_evt) refers to nrf_ble_gatt.o(i.on_exchange_mtu_request_evt) for on_exchange_mtu_request_evt
    nrf_ble_gatt.o(i.nrf_ble_gatt_on_ble_evt) refers to memcpya.o(.text) for __aeabi_memcpy4
    nrf_ble_gatt.o(i.nrf_ble_gatt_on_ble_evt) refers to nrf_ble_gatt.o(i.data_length_update) for data_length_update
    nrf_ble_gatt.o(i.nrf_ble_gatt_on_ble_evt) refers to nrf_strerror.o(i.nrf_strerror_get) for nrf_strerror_get
    nrf_ble_gatt.o(i.nrf_ble_gatt_on_ble_evt) refers to nrf_log_frontend.o(i.nrf_log_frontend_std_1) for nrf_log_frontend_std_1
    nrf_ble_gatt.o(i.nrf_ble_gatt_on_ble_evt) refers to nrf_ble_gatt.o(.constdata) for .constdata
    nrf_ble_gatt.o(i.nrf_ble_gatt_on_ble_evt) refers to nrf_ble_gatt.o(log_const_data) for log_const_data
    nrf_ble_gatt.o(i.on_connected_evt) refers to nrf_strerror.o(i.nrf_strerror_get) for nrf_strerror_get
    nrf_ble_gatt.o(i.on_connected_evt) refers to nrf_log_frontend.o(i.nrf_log_frontend_std_1) for nrf_log_frontend_std_1
    nrf_ble_gatt.o(i.on_connected_evt) refers to nrf_ble_gatt.o(i.data_length_update) for data_length_update
    nrf_ble_gatt.o(i.on_connected_evt) refers to nrf_ble_gatt.o(log_const_data) for log_const_data
    nrf_ble_gatt.o(i.on_exchange_mtu_request_evt) refers to nrf_strerror.o(i.nrf_strerror_get) for nrf_strerror_get
    nrf_ble_gatt.o(i.on_exchange_mtu_request_evt) refers to nrf_log_frontend.o(i.nrf_log_frontend_std_1) for nrf_log_frontend_std_1
    nrf_ble_gatt.o(i.on_exchange_mtu_request_evt) refers to nrf_ble_gatt.o(log_const_data) for log_const_data
    nrf_ble_gatt.o(i.on_exchange_mtu_request_evt) refers to nrf_ble_gatt.o(.constdata) for .constdata
    nrf_ble_gatt.o(log_const_data) refers to nrf_ble_gatt.o(.conststrlit) for .conststrlit
    nrf_ble_qwr.o(i.nrf_ble_qwr_on_ble_evt) refers to nrf_ble_qwr.o(i.user_mem_reply) for user_mem_reply
    ble_nus.o(i.ble_nus_data_send) refers to ble_link_ctx_manager.o(i.blcm_link_ctx_get) for blcm_link_ctx_get
    ble_nus.o(i.ble_nus_init) refers to memseta.o(.text) for __aeabi_memclr4
    ble_nus.o(i.ble_nus_init) refers to ble_srv_common.o(i.characteristic_add) for characteristic_add
    ble_nus.o(i.ble_nus_init) refers to ble_nus.o(.constdata) for .constdata
    ble_nus.o(i.ble_nus_on_ble_evt) refers to ble_link_ctx_manager.o(i.blcm_link_ctx_get) for blcm_link_ctx_get
    ble_nus.o(i.ble_nus_on_ble_evt) refers to memseta.o(.text) for __aeabi_memclr4
    ble_nus.o(i.ble_nus_on_ble_evt) refers to ble_nus.o(i.on_connect) for on_connect
    ble_nus.o(i.ble_nus_on_ble_evt) refers to ble_nus.o(i.on_write) for on_write
    ble_nus.o(i.on_connect) refers to ble_link_ctx_manager.o(i.blcm_link_ctx_get) for blcm_link_ctx_get
    ble_nus.o(i.on_connect) refers to ble_srv_common.o(i.ble_srv_is_notification_enabled) for ble_srv_is_notification_enabled
    ble_nus.o(i.on_connect) refers to memseta.o(.text) for __aeabi_memclr4
    ble_nus.o(i.on_write) refers to ble_link_ctx_manager.o(i.blcm_link_ctx_get) for blcm_link_ctx_get
    ble_nus.o(i.on_write) refers to memseta.o(.text) for __aeabi_memclr4
    ble_nus.o(i.on_write) refers to ble_srv_common.o(i.ble_srv_is_notification_enabled) for ble_srv_is_notification_enabled
    ble_nus.o(log_const_data) refers to ble_nus.o(.conststrlit) for .conststrlit
    nrf_drv_clock.o(i.clock_clk_started_notify) refers to nrf_drv_clock.o(.bss) for .bss
    nrf_drv_clock.o(i.clock_irq_handler) refers to nrf_drv_clock.o(i.clock_clk_started_notify) for clock_clk_started_notify
    nrf_drv_clock.o(i.clock_irq_handler) refers to nrf_drv_clock.o(.bss) for .bss
    nrf_drv_clock.o(i.nrf_drv_clock_hfclk_is_running) refers to nrf_sdh.o(i.nrf_sdh_is_enabled) for nrf_sdh_is_enabled
    nrf_drv_clock.o(i.nrf_drv_clock_hfclk_release) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    nrf_drv_clock.o(i.nrf_drv_clock_hfclk_release) refers to nrf_sdh.o(i.nrf_sdh_is_enabled) for nrf_sdh_is_enabled
    nrf_drv_clock.o(i.nrf_drv_clock_hfclk_release) refers to nrfx_clock.o(i.nrfx_clock_hfclk_stop) for nrfx_clock_hfclk_stop
    nrf_drv_clock.o(i.nrf_drv_clock_hfclk_release) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    nrf_drv_clock.o(i.nrf_drv_clock_hfclk_release) refers to nrf_drv_clock.o(.bss) for .bss
    nrf_drv_clock.o(i.nrf_drv_clock_hfclk_request) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    nrf_drv_clock.o(i.nrf_drv_clock_hfclk_request) refers to nrf_drv_clock.o(i.item_enqueue) for item_enqueue
    nrf_drv_clock.o(i.nrf_drv_clock_hfclk_request) refers to nrf_sdh.o(i.nrf_sdh_is_enabled) for nrf_sdh_is_enabled
    nrf_drv_clock.o(i.nrf_drv_clock_hfclk_request) refers to nrfx_clock.o(i.nrfx_clock_hfclk_start) for nrfx_clock_hfclk_start
    nrf_drv_clock.o(i.nrf_drv_clock_hfclk_request) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    nrf_drv_clock.o(i.nrf_drv_clock_hfclk_request) refers to nrf_drv_clock.o(.bss) for .bss
    nrf_drv_clock.o(i.nrf_drv_clock_init) refers to nrfx_clock.o(i.nrfx_clock_init) for nrfx_clock_init
    nrf_drv_clock.o(i.nrf_drv_clock_init) refers to nrf_sdh.o(i.nrf_sdh_is_enabled) for nrf_sdh_is_enabled
    nrf_drv_clock.o(i.nrf_drv_clock_init) refers to nrfx_clock.o(i.nrfx_clock_enable) for nrfx_clock_enable
    nrf_drv_clock.o(i.nrf_drv_clock_init) refers to nrf_drv_clock.o(i.nrf_wdt_started) for nrf_wdt_started
    nrf_drv_clock.o(i.nrf_drv_clock_init) refers to nrf_drv_clock.o(.bss) for .bss
    nrf_drv_clock.o(i.nrf_drv_clock_init) refers to nrf_drv_clock.o(i.clock_irq_handler) for clock_irq_handler
    nrf_drv_clock.o(i.nrf_drv_clock_init_check) refers to nrf_drv_clock.o(.bss) for .bss
    nrf_drv_clock.o(i.nrf_drv_clock_lfclk_is_running) refers to nrf_sdh.o(i.nrf_sdh_is_enabled) for nrf_sdh_is_enabled
    nrf_drv_clock.o(i.nrf_drv_clock_lfclk_release) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    nrf_drv_clock.o(i.nrf_drv_clock_lfclk_release) refers to nrf_drv_clock.o(i.nrf_wdt_started) for nrf_wdt_started
    nrf_drv_clock.o(i.nrf_drv_clock_lfclk_release) refers to nrfx_clock.o(i.nrfx_clock_lfclk_stop) for nrfx_clock_lfclk_stop
    nrf_drv_clock.o(i.nrf_drv_clock_lfclk_release) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    nrf_drv_clock.o(i.nrf_drv_clock_lfclk_release) refers to nrf_drv_clock.o(.bss) for .bss
    nrf_drv_clock.o(i.nrf_drv_clock_lfclk_request) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    nrf_drv_clock.o(i.nrf_drv_clock_lfclk_request) refers to nrf_drv_clock.o(i.item_enqueue) for item_enqueue
    nrf_drv_clock.o(i.nrf_drv_clock_lfclk_request) refers to nrfx_clock.o(i.nrfx_clock_lfclk_start) for nrfx_clock_lfclk_start
    nrf_drv_clock.o(i.nrf_drv_clock_lfclk_request) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    nrf_drv_clock.o(i.nrf_drv_clock_lfclk_request) refers to nrf_drv_clock.o(.bss) for .bss
    nrf_drv_clock.o(i.nrf_drv_clock_uninit) refers to nrfx_clock.o(i.nrfx_clock_disable) for nrfx_clock_disable
    nrf_drv_clock.o(i.nrf_drv_clock_uninit) refers to nrfx_clock.o(i.nrfx_clock_uninit) for nrfx_clock_uninit
    nrf_drv_clock.o(i.nrf_drv_clock_uninit) refers to nrf_drv_clock.o(.bss) for .bss
    nrf_drv_clock.o(i.sd_state_evt_handler) refers to nrfx_clock.o(i.nrfx_clock_enable) for nrfx_clock_enable
    nrf_drv_clock.o(i.sd_state_evt_handler) refers to nrf_drv_clock.o(i.nrf_drv_clock_lfclk_release) for nrf_drv_clock_lfclk_release
    nrf_drv_clock.o(i.sd_state_evt_handler) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    nrf_drv_clock.o(i.sd_state_evt_handler) refers to nrf_drv_clock.o(i.nrf_drv_clock_init) for nrf_drv_clock_init
    nrf_drv_clock.o(i.sd_state_evt_handler) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    nrf_drv_clock.o(i.sd_state_evt_handler) refers to nrf_drv_clock.o(.bss) for .bss
    nrf_drv_clock.o(i.soc_evt_handler) refers to nrf_drv_clock.o(i.clock_clk_started_notify) for clock_clk_started_notify
    nrf_drv_clock.o(i.soc_evt_handler) refers to nrf_drv_clock.o(.bss) for .bss
    nrf_drv_clock.o(log_const_data) refers to nrf_drv_clock.o(.conststrlit) for .conststrlit
    nrf_drv_clock.o(sdh_soc_observers0) refers to nrf_drv_clock.o(i.soc_evt_handler) for soc_evt_handler
    nrf_drv_clock.o(sdh_state_observers0) refers to nrf_drv_clock.o(i.sd_state_evt_handler) for sd_state_evt_handler
    nrf_drv_uart.o(i.nrf_drv_uart_init) refers to memcpya.o(.text) for __aeabi_memcpy4
    nrf_drv_uart.o(i.nrf_drv_uart_init) refers to nrfx_uarte.o(i.nrfx_uarte_init) for nrfx_uarte_init
    nrf_drv_uart.o(i.nrf_drv_uart_init) refers to nrfx_uart.o(i.nrfx_uart_init) for nrfx_uart_init
    nrf_drv_uart.o(i.nrf_drv_uart_init) refers to nrf_drv_uart.o(.data) for .data
    nrf_drv_uart.o(i.nrf_drv_uart_init) refers to nrf_drv_uart.o(i.uarte_evt_handler) for uarte_evt_handler
    nrf_drv_uart.o(i.nrf_drv_uart_init) refers to nrf_drv_uart.o(i.uart_evt_handler) for uart_evt_handler
    nrf_drv_uart.o(i.uart_evt_handler) refers to nrf_drv_uart.o(.data) for .data
    nrf_drv_uart.o(i.uarte_evt_handler) refers to nrf_drv_uart.o(.data) for .data
    nrfx_atomic.o(i.nrfx_atomic_flag_clear) refers to nrfx_atomic.o(i.nrfx_atomic_u32_and) for nrfx_atomic_u32_and
    nrfx_atomic.o(i.nrfx_atomic_flag_clear_fetch) refers to nrfx_atomic.o(i.nrfx_atomic_u32_fetch_and) for nrfx_atomic_u32_fetch_and
    nrfx_atomic.o(i.nrfx_atomic_flag_set) refers to nrfx_atomic.o(i.nrfx_atomic_u32_or) for nrfx_atomic_u32_or
    nrfx_atomic.o(i.nrfx_atomic_flag_set_fetch) refers to nrfx_atomic.o(i.nrfx_atomic_u32_fetch_or) for nrfx_atomic_u32_fetch_or
    nrfx_atomic.o(i.nrfx_atomic_u32_add) refers to nrfx_atomic.o(.emb_text) for __asm___13_nrfx_atomic_c_3bd32246__nrfx_atomic_internal_add
    nrfx_atomic.o(i.nrfx_atomic_u32_and) refers to nrfx_atomic.o(.emb_text) for __asm___13_nrfx_atomic_c_3bd32246__nrfx_atomic_internal_and
    nrfx_atomic.o(i.nrfx_atomic_u32_cmp_exch) refers to nrfx_atomic.o(.emb_text) for __asm___13_nrfx_atomic_c_3bd32246__nrfx_atomic_internal_cmp_exch
    nrfx_atomic.o(i.nrfx_atomic_u32_fetch_add) refers to nrfx_atomic.o(.emb_text) for __asm___13_nrfx_atomic_c_3bd32246__nrfx_atomic_internal_add
    nrfx_atomic.o(i.nrfx_atomic_u32_fetch_and) refers to nrfx_atomic.o(.emb_text) for __asm___13_nrfx_atomic_c_3bd32246__nrfx_atomic_internal_and
    nrfx_atomic.o(i.nrfx_atomic_u32_fetch_or) refers to nrfx_atomic.o(.emb_text) for __asm___13_nrfx_atomic_c_3bd32246__nrfx_atomic_internal_orr
    nrfx_atomic.o(i.nrfx_atomic_u32_fetch_store) refers to nrfx_atomic.o(.emb_text) for __asm___13_nrfx_atomic_c_3bd32246__nrfx_atomic_internal_mov
    nrfx_atomic.o(i.nrfx_atomic_u32_fetch_sub) refers to nrfx_atomic.o(.emb_text) for __asm___13_nrfx_atomic_c_3bd32246__nrfx_atomic_internal_sub
    nrfx_atomic.o(i.nrfx_atomic_u32_fetch_sub_hs) refers to nrfx_atomic.o(.emb_text) for __asm___13_nrfx_atomic_c_3bd32246__nrfx_atomic_internal_sub_hs
    nrfx_atomic.o(i.nrfx_atomic_u32_fetch_xor) refers to nrfx_atomic.o(.emb_text) for __asm___13_nrfx_atomic_c_3bd32246__nrfx_atomic_internal_eor
    nrfx_atomic.o(i.nrfx_atomic_u32_or) refers to nrfx_atomic.o(.emb_text) for __asm___13_nrfx_atomic_c_3bd32246__nrfx_atomic_internal_orr
    nrfx_atomic.o(i.nrfx_atomic_u32_store) refers to nrfx_atomic.o(.emb_text) for __asm___13_nrfx_atomic_c_3bd32246__nrfx_atomic_internal_mov
    nrfx_atomic.o(i.nrfx_atomic_u32_sub) refers to nrfx_atomic.o(.emb_text) for __asm___13_nrfx_atomic_c_3bd32246__nrfx_atomic_internal_sub
    nrfx_atomic.o(i.nrfx_atomic_u32_sub_hs) refers to nrfx_atomic.o(.emb_text) for __asm___13_nrfx_atomic_c_3bd32246__nrfx_atomic_internal_sub_hs
    nrfx_atomic.o(i.nrfx_atomic_u32_xor) refers to nrfx_atomic.o(.emb_text) for __asm___13_nrfx_atomic_c_3bd32246__nrfx_atomic_internal_eor
    nrfx_clock.o(i.POWER_CLOCK_IRQHandler) refers to nrfx_clock.o(i.nrf_clock_event_check) for nrf_clock_event_check
    nrfx_clock.o(i.POWER_CLOCK_IRQHandler) refers to nrfx_clock.o(i.nrf_clock_event_clear) for nrf_clock_event_clear
    nrfx_clock.o(i.POWER_CLOCK_IRQHandler) refers to nrfx_clock.o(.data) for .data
    nrfx_clock.o(i.nrfx_clock_hfclk_start) refers to nrfx_clock.o(i.nrf_clock_event_clear) for nrf_clock_event_clear
    nrfx_clock.o(i.nrfx_clock_hfclk_stop) refers to nrfx_clock.o(i.nrf_clock_event_clear) for nrf_clock_event_clear
    nrfx_clock.o(i.nrfx_clock_hfclk_stop) refers to nrfx_clock.o(.data) for .data
    nrfx_clock.o(i.nrfx_clock_init) refers to nrfx_clock.o(.data) for .data
    nrfx_clock.o(i.nrfx_clock_lfclk_start) refers to nrfx_clock.o(i.nrf_clock_event_clear) for nrf_clock_event_clear
    nrfx_clock.o(i.nrfx_clock_lfclk_stop) refers to nrfx_clock.o(i.nrf_clock_event_clear) for nrf_clock_event_clear
    nrfx_clock.o(i.nrfx_clock_uninit) refers to nrfx_clock.o(i.nrfx_clock_lfclk_stop) for nrfx_clock_lfclk_stop
    nrfx_clock.o(i.nrfx_clock_uninit) refers to nrfx_clock.o(i.nrfx_clock_hfclk_stop) for nrfx_clock_hfclk_stop
    nrfx_clock.o(i.nrfx_clock_uninit) refers to nrfx_clock.o(.data) for .data
    nrfx_clock.o(log_const_data) refers to nrfx_clock.o(.conststrlit) for .conststrlit
    nrfx_gpiote.o(i.GPIOTE_IRQHandler) refers to nrfx_gpiote.o(i.nrf_gpiote_event_is_set) for nrf_gpiote_event_is_set
    nrfx_gpiote.o(i.GPIOTE_IRQHandler) refers to nrfx_gpiote.o(i.nrf_gpiote_event_clear) for nrf_gpiote_event_clear
    nrfx_gpiote.o(i.GPIOTE_IRQHandler) refers to nrfx_gpiote.o(i.nrf_gpio_latches_read_and_clear) for nrf_gpio_latches_read_and_clear
    nrfx_gpiote.o(i.GPIOTE_IRQHandler) refers to nrfx_gpiote.o(i.port_event_handle) for port_event_handle
    nrfx_gpiote.o(i.GPIOTE_IRQHandler) refers to nrfx_gpiote.o(.bss) for .bss
    nrfx_gpiote.o(i.channel_free) refers to nrfx_gpiote.o(.bss) for .bss
    nrfx_gpiote.o(i.channel_port_alloc) refers to nrfx_gpiote.o(.bss) for .bss
    nrfx_gpiote.o(i.channel_port_get) refers to nrfx_gpiote.o(.bss) for .bss
    nrfx_gpiote.o(i.nrf_gpio_cfg_default) refers to nrfx_gpiote.o(i.nrf_gpio_cfg) for nrf_gpio_cfg
    nrfx_gpiote.o(i.nrf_gpio_latches_read_and_clear) refers to nrfx_gpiote.o(.constdata) for .constdata
    nrfx_gpiote.o(i.nrfx_gpiote_clr_task_addr_get) refers to nrfx_gpiote.o(i.nrfx_gpiote_clr_task_get) for nrfx_gpiote_clr_task_get
    nrfx_gpiote.o(i.nrfx_gpiote_clr_task_get) refers to nrfx_gpiote.o(i.channel_port_get) for channel_port_get
    nrfx_gpiote.o(i.nrfx_gpiote_clr_task_trigger) refers to nrfx_gpiote.o(i.channel_port_get) for channel_port_get
    nrfx_gpiote.o(i.nrfx_gpiote_in_event_addr_get) refers to nrfx_gpiote.o(i.nrfx_gpiote_in_event_get) for nrfx_gpiote_in_event_get
    nrfx_gpiote.o(i.nrfx_gpiote_in_event_disable) refers to nrfx_gpiote.o(i.pin_in_use_by_port) for pin_in_use_by_port
    nrfx_gpiote.o(i.nrfx_gpiote_in_event_disable) refers to nrfx_gpiote.o(i.nrf_gpio_cfg_sense_set) for nrf_gpio_cfg_sense_set
    nrfx_gpiote.o(i.nrfx_gpiote_in_event_disable) refers to nrfx_gpiote.o(i.pin_in_use_by_te) for pin_in_use_by_te
    nrfx_gpiote.o(i.nrfx_gpiote_in_event_disable) refers to nrfx_gpiote.o(i.channel_port_get) for channel_port_get
    nrfx_gpiote.o(i.nrfx_gpiote_in_event_enable) refers to nrfx_gpiote.o(i.pin_in_use_by_port) for pin_in_use_by_port
    nrfx_gpiote.o(i.nrfx_gpiote_in_event_enable) refers to nrfx_gpiote.o(i.channel_port_get) for channel_port_get
    nrfx_gpiote.o(i.nrfx_gpiote_in_event_enable) refers to nrfx_gpiote.o(i.port_handler_polarity_get) for port_handler_polarity_get
    nrfx_gpiote.o(i.nrfx_gpiote_in_event_enable) refers to nrfx_gpiote.o(i.nrf_gpio_cfg_sense_set) for nrf_gpio_cfg_sense_set
    nrfx_gpiote.o(i.nrfx_gpiote_in_event_enable) refers to nrfx_gpiote.o(i.pin_in_use_by_te) for pin_in_use_by_te
    nrfx_gpiote.o(i.nrfx_gpiote_in_event_enable) refers to nrfx_gpiote.o(i.nrf_gpiote_event_clear) for nrf_gpiote_event_clear
    nrfx_gpiote.o(i.nrfx_gpiote_in_event_enable) refers to nrfx_gpiote.o(.bss) for .bss
    nrfx_gpiote.o(i.nrfx_gpiote_in_event_get) refers to nrfx_gpiote.o(i.pin_in_use_by_te) for pin_in_use_by_te
    nrfx_gpiote.o(i.nrfx_gpiote_in_event_get) refers to nrfx_gpiote.o(i.channel_port_get) for channel_port_get
    nrfx_gpiote.o(i.nrfx_gpiote_in_init) refers to nrfx_gpiote.o(i.channel_port_alloc) for channel_port_alloc
    nrfx_gpiote.o(i.nrfx_gpiote_in_init) refers to nrfx_gpiote.o(i.nrf_gpio_cfg) for nrf_gpio_cfg
    nrfx_gpiote.o(i.nrfx_gpiote_in_init) refers to nrfx_gpiote.o(i.pin_configured_set) for pin_configured_set
    nrfx_gpiote.o(i.nrfx_gpiote_in_init) refers to nrfx_gpiote.o(.bss) for .bss
    nrfx_gpiote.o(i.nrfx_gpiote_in_uninit) refers to nrfx_gpiote.o(i.nrfx_gpiote_in_event_disable) for nrfx_gpiote_in_event_disable
    nrfx_gpiote.o(i.nrfx_gpiote_in_uninit) refers to nrfx_gpiote.o(i.pin_in_use_by_te) for pin_in_use_by_te
    nrfx_gpiote.o(i.nrfx_gpiote_in_uninit) refers to nrfx_gpiote.o(i.channel_port_get) for channel_port_get
    nrfx_gpiote.o(i.nrfx_gpiote_in_uninit) refers to nrfx_gpiote.o(i.pin_configured_check) for pin_configured_check
    nrfx_gpiote.o(i.nrfx_gpiote_in_uninit) refers to nrfx_gpiote.o(i.nrf_gpio_cfg_default) for nrf_gpio_cfg_default
    nrfx_gpiote.o(i.nrfx_gpiote_in_uninit) refers to nrfx_gpiote.o(i.pin_configured_clear) for pin_configured_clear
    nrfx_gpiote.o(i.nrfx_gpiote_in_uninit) refers to nrfx_gpiote.o(i.channel_free) for channel_free
    nrfx_gpiote.o(i.nrfx_gpiote_in_uninit) refers to nrfx_gpiote.o(.bss) for .bss
    nrfx_gpiote.o(i.nrfx_gpiote_init) refers to nrfx_gpiote.o(i.nrf_gpio_pin_present_check) for nrf_gpio_pin_present_check
    nrfx_gpiote.o(i.nrfx_gpiote_init) refers to nrfx_gpiote.o(i.channel_free) for channel_free
    nrfx_gpiote.o(i.nrfx_gpiote_init) refers to nrfx_gpiote.o(i.nrf_gpiote_event_clear) for nrf_gpiote_event_clear
    nrfx_gpiote.o(i.nrfx_gpiote_init) refers to nrfx_gpiote.o(.bss) for .bss
    nrfx_gpiote.o(i.nrfx_gpiote_is_init) refers to nrfx_gpiote.o(.bss) for .bss
    nrfx_gpiote.o(i.nrfx_gpiote_out_init) refers to nrfx_gpiote.o(i.channel_port_alloc) for channel_port_alloc
    nrfx_gpiote.o(i.nrfx_gpiote_out_init) refers to nrfx_gpiote.o(i.nrf_gpio_cfg) for nrf_gpio_cfg
    nrfx_gpiote.o(i.nrfx_gpiote_out_init) refers to nrfx_gpiote.o(i.pin_configured_set) for pin_configured_set
    nrfx_gpiote.o(i.nrfx_gpiote_out_init) refers to nrfx_gpiote.o(.bss) for .bss
    nrfx_gpiote.o(i.nrfx_gpiote_out_task_addr_get) refers to nrfx_gpiote.o(i.nrfx_gpiote_out_task_get) for nrfx_gpiote_out_task_get
    nrfx_gpiote.o(i.nrfx_gpiote_out_task_disable) refers to nrfx_gpiote.o(.bss) for .bss
    nrfx_gpiote.o(i.nrfx_gpiote_out_task_enable) refers to nrfx_gpiote.o(.bss) for .bss
    nrfx_gpiote.o(i.nrfx_gpiote_out_task_force) refers to nrfx_gpiote.o(.bss) for .bss
    nrfx_gpiote.o(i.nrfx_gpiote_out_task_get) refers to nrfx_gpiote.o(i.channel_port_get) for channel_port_get
    nrfx_gpiote.o(i.nrfx_gpiote_out_task_trigger) refers to nrfx_gpiote.o(i.channel_port_get) for channel_port_get
    nrfx_gpiote.o(i.nrfx_gpiote_out_uninit) refers to nrfx_gpiote.o(i.pin_in_use_by_te) for pin_in_use_by_te
    nrfx_gpiote.o(i.nrfx_gpiote_out_uninit) refers to nrfx_gpiote.o(i.channel_port_get) for channel_port_get
    nrfx_gpiote.o(i.nrfx_gpiote_out_uninit) refers to nrfx_gpiote.o(i.channel_free) for channel_free
    nrfx_gpiote.o(i.nrfx_gpiote_out_uninit) refers to nrfx_gpiote.o(i.pin_configured_check) for pin_configured_check
    nrfx_gpiote.o(i.nrfx_gpiote_out_uninit) refers to nrfx_gpiote.o(i.nrf_gpio_cfg_default) for nrf_gpio_cfg_default
    nrfx_gpiote.o(i.nrfx_gpiote_out_uninit) refers to nrfx_gpiote.o(i.pin_configured_clear) for pin_configured_clear
    nrfx_gpiote.o(i.nrfx_gpiote_out_uninit) refers to nrfx_gpiote.o(.bss) for .bss
    nrfx_gpiote.o(i.nrfx_gpiote_set_task_addr_get) refers to nrfx_gpiote.o(i.nrfx_gpiote_set_task_get) for nrfx_gpiote_set_task_get
    nrfx_gpiote.o(i.nrfx_gpiote_set_task_get) refers to nrfx_gpiote.o(i.channel_port_get) for channel_port_get
    nrfx_gpiote.o(i.nrfx_gpiote_set_task_trigger) refers to nrfx_gpiote.o(i.channel_port_get) for channel_port_get
    nrfx_gpiote.o(i.nrfx_gpiote_uninit) refers to nrfx_gpiote.o(i.nrf_gpio_pin_present_check) for nrf_gpio_pin_present_check
    nrfx_gpiote.o(i.nrfx_gpiote_uninit) refers to nrfx_gpiote.o(i.nrfx_gpiote_in_uninit) for nrfx_gpiote_in_uninit
    nrfx_gpiote.o(i.nrfx_gpiote_uninit) refers to nrfx_gpiote.o(i.nrfx_gpiote_out_uninit) for nrfx_gpiote_out_uninit
    nrfx_gpiote.o(i.nrfx_gpiote_uninit) refers to nrfx_gpiote.o(.bss) for .bss
    nrfx_gpiote.o(i.pin_configured_check) refers to nrfx_gpiote.o(i.nrf_bitmask_bit_is_set) for nrf_bitmask_bit_is_set
    nrfx_gpiote.o(i.pin_configured_check) refers to nrfx_gpiote.o(.bss) for .bss
    nrfx_gpiote.o(i.pin_configured_clear) refers to nrfx_gpiote.o(.bss) for .bss
    nrfx_gpiote.o(i.pin_configured_set) refers to nrfx_gpiote.o(.bss) for .bss
    nrfx_gpiote.o(i.pin_in_use_by_port) refers to nrfx_gpiote.o(.bss) for .bss
    nrfx_gpiote.o(i.pin_in_use_by_te) refers to nrfx_gpiote.o(.bss) for .bss
    nrfx_gpiote.o(i.port_event_handle) refers to nrfx_gpiote.o(i.nrf_bitmask_bit_is_set) for nrf_bitmask_bit_is_set
    nrfx_gpiote.o(i.port_event_handle) refers to nrfx_gpiote.o(i.port_handler_polarity_get) for port_handler_polarity_get
    nrfx_gpiote.o(i.port_event_handle) refers to nrfx_gpiote.o(i.nrf_gpio_cfg_sense_set) for nrf_gpio_cfg_sense_set
    nrfx_gpiote.o(i.port_event_handle) refers to nrfx_gpiote.o(i.channel_port_get) for channel_port_get
    nrfx_gpiote.o(i.port_event_handle) refers to nrfx_gpiote.o(i.nrf_gpio_latches_read_and_clear) for nrf_gpio_latches_read_and_clear
    nrfx_gpiote.o(i.port_event_handle) refers to nrfx_gpiote.o(.bss) for .bss
    nrfx_gpiote.o(i.port_handler_polarity_get) refers to nrfx_gpiote.o(.bss) for .bss
    nrfx_gpiote.o(log_const_data) refers to nrfx_gpiote.o(.conststrlit) for .conststrlit
    nrfx_prs.o(i.UARTE0_UART0_IRQHandler) refers to nrfx_prs.o(.data) for .data
    nrfx_prs.o(i.nrfx_prs_acquire) refers to nrfx_prs.o(i.prs_box_get) for prs_box_get
    nrfx_prs.o(i.nrfx_prs_acquire) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    nrfx_prs.o(i.nrfx_prs_acquire) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    nrfx_prs.o(i.nrfx_prs_release) refers to nrfx_prs.o(i.prs_box_get) for prs_box_get
    nrfx_prs.o(i.prs_box_get) refers to nrfx_prs.o(.data) for .data
    nrfx_prs.o(log_const_data) refers to nrfx_prs.o(.conststrlit) for .conststrlit
    nrfx_uart.o(i.apply_config) refers to nrfx_uart.o(i.nrf_gpio_cfg_output) for nrf_gpio_cfg_output
    nrfx_uart.o(i.apply_config) refers to nrfx_uart.o(i.nrf_gpio_cfg_input) for nrf_gpio_cfg_input
    nrfx_uart.o(i.nrf_gpio_cfg_default) refers to nrfx_uart.o(i.nrf_gpio_cfg) for nrf_gpio_cfg
    nrfx_uart.o(i.nrf_gpio_cfg_input) refers to nrfx_uart.o(i.nrf_gpio_cfg) for nrf_gpio_cfg
    nrfx_uart.o(i.nrf_gpio_cfg_output) refers to nrfx_uart.o(i.nrf_gpio_cfg) for nrf_gpio_cfg
    nrfx_uart.o(i.nrfx_uart_0_irq_handler) refers to nrfx_uart.o(i.uart_irq_handler) for uart_irq_handler
    nrfx_uart.o(i.nrfx_uart_0_irq_handler) refers to nrfx_uart.o(.bss) for .bss
    nrfx_uart.o(i.nrfx_uart_errorsrc_get) refers to nrfx_uart.o(i.nrf_uart_event_clear) for nrf_uart_event_clear
    nrfx_uart.o(i.nrfx_uart_init) refers to nrfx_prs.o(i.nrfx_prs_acquire) for nrfx_prs_acquire
    nrfx_uart.o(i.nrfx_uart_init) refers to nrfx_uart.o(i.apply_config) for apply_config
    nrfx_uart.o(i.nrfx_uart_init) refers to nrfx_uart.o(i.nrf_uart_event_clear) for nrf_uart_event_clear
    nrfx_uart.o(i.nrfx_uart_init) refers to nrfx_uart.o(.bss) for .bss
    nrfx_uart.o(i.nrfx_uart_init) refers to nrfx_uart.o(.constdata) for .constdata
    nrfx_uart.o(i.nrfx_uart_rx) refers to nrfx_uart.o(i.rx_enable) for rx_enable
    nrfx_uart.o(i.nrfx_uart_rx) refers to nrfx_uart.o(i.nrf_uart_event_clear) for nrf_uart_event_clear
    nrfx_uart.o(i.nrfx_uart_rx) refers to nrfx_uart.o(i.nrf_uart_event_check) for nrf_uart_event_check
    nrfx_uart.o(i.nrfx_uart_rx) refers to nrfx_uart.o(i.rx_byte) for rx_byte
    nrfx_uart.o(i.nrfx_uart_rx) refers to nrfx_uart.o(.bss) for .bss
    nrfx_uart.o(i.nrfx_uart_rx_disable) refers to nrfx_uart.o(.bss) for .bss
    nrfx_uart.o(i.nrfx_uart_rx_enable) refers to nrfx_uart.o(i.rx_enable) for rx_enable
    nrfx_uart.o(i.nrfx_uart_rx_enable) refers to nrfx_uart.o(.bss) for .bss
    nrfx_uart.o(i.nrfx_uart_tx) refers to nrfx_uart.o(i.nrfx_uart_tx_in_progress) for nrfx_uart_tx_in_progress
    nrfx_uart.o(i.nrfx_uart_tx) refers to nrfx_uart.o(i.nrf_uart_event_clear) for nrf_uart_event_clear
    nrfx_uart.o(i.nrfx_uart_tx) refers to nrfx_uart.o(i.tx_byte) for tx_byte
    nrfx_uart.o(i.nrfx_uart_tx) refers to nrfx_uart.o(i.nrf_uart_event_check) for nrf_uart_event_check
    nrfx_uart.o(i.nrfx_uart_tx) refers to nrfx_uart.o(.bss) for .bss
    nrfx_uart.o(i.nrfx_uart_tx_abort) refers to nrfx_uart.o(i.tx_done_event) for tx_done_event
    nrfx_uart.o(i.nrfx_uart_tx_abort) refers to nrfx_uart.o(.bss) for .bss
    nrfx_uart.o(i.nrfx_uart_tx_in_progress) refers to nrfx_uart.o(.bss) for .bss
    nrfx_uart.o(i.nrfx_uart_uninit) refers to nrfx_uart.o(i.nrf_gpio_cfg_default) for nrf_gpio_cfg_default
    nrfx_uart.o(i.nrfx_uart_uninit) refers to nrfx_prs.o(i.nrfx_prs_release) for nrfx_prs_release
    nrfx_uart.o(i.nrfx_uart_uninit) refers to nrfx_uart.o(.bss) for .bss
    nrfx_uart.o(i.rx_byte) refers to nrfx_uart.o(i.nrf_uart_event_clear) for nrf_uart_event_clear
    nrfx_uart.o(i.rx_enable) refers to nrfx_uart.o(i.nrf_uart_event_clear) for nrf_uart_event_clear
    nrfx_uart.o(i.tx_byte) refers to nrfx_uart.o(i.nrf_uart_event_clear) for nrf_uart_event_clear
    nrfx_uart.o(i.uart_irq_handler) refers to nrfx_uart.o(i.nrf_uart_int_enable_check) for nrf_uart_int_enable_check
    nrfx_uart.o(i.uart_irq_handler) refers to nrfx_uart.o(i.nrf_uart_event_check) for nrf_uart_event_check
    nrfx_uart.o(i.uart_irq_handler) refers to nrfx_uart.o(i.nrf_uart_event_clear) for nrf_uart_event_clear
    nrfx_uart.o(i.uart_irq_handler) refers to nrfx_uart.o(i.rx_byte) for rx_byte
    nrfx_uart.o(i.uart_irq_handler) refers to nrfx_uart.o(i.rx_done_event) for rx_done_event
    nrfx_uart.o(i.uart_irq_handler) refers to nrfx_uart.o(i.tx_done_event) for tx_done_event
    nrfx_uart.o(i.uart_irq_handler) refers to nrfx_uart.o(i.tx_byte) for tx_byte
    nrfx_uart.o(.constdata) refers to nrfx_uart.o(i.nrfx_uart_0_irq_handler) for nrfx_uart_0_irq_handler
    nrfx_uart.o(log_const_data) refers to nrfx_uart.o(.conststrlit) for .conststrlit
    nrfx_uarte.o(i.apply_config) refers to nrfx_uarte.o(i.nrf_gpio_cfg_output) for nrf_gpio_cfg_output
    nrfx_uarte.o(i.apply_config) refers to nrfx_uarte.o(i.nrf_gpio_cfg_input) for nrf_gpio_cfg_input
    nrfx_uarte.o(i.interrupts_enable) refers to nrfx_uarte.o(i.nrf_uarte_event_clear) for nrf_uarte_event_clear
    nrfx_uarte.o(i.nrf_gpio_cfg_default) refers to nrfx_uarte.o(i.nrf_gpio_cfg) for nrf_gpio_cfg
    nrfx_uarte.o(i.nrf_gpio_cfg_input) refers to nrfx_uarte.o(i.nrf_gpio_cfg) for nrf_gpio_cfg
    nrfx_uarte.o(i.nrf_gpio_cfg_output) refers to nrfx_uarte.o(i.nrf_gpio_cfg) for nrf_gpio_cfg
    nrfx_uarte.o(i.nrfx_uarte_0_irq_handler) refers to nrfx_uarte.o(i.uarte_irq_handler) for uarte_irq_handler
    nrfx_uarte.o(i.nrfx_uarte_0_irq_handler) refers to nrfx_uarte.o(.bss) for .bss
    nrfx_uarte.o(i.nrfx_uarte_errorsrc_get) refers to nrfx_uarte.o(i.nrf_uarte_event_clear) for nrf_uarte_event_clear
    nrfx_uarte.o(i.nrfx_uarte_init) refers to nrfx_prs.o(i.nrfx_prs_acquire) for nrfx_prs_acquire
    nrfx_uarte.o(i.nrfx_uarte_init) refers to nrfx_uarte.o(i.apply_config) for apply_config
    nrfx_uarte.o(i.nrfx_uarte_init) refers to nrfx_uarte.o(i.interrupts_enable) for interrupts_enable
    nrfx_uarte.o(i.nrfx_uarte_init) refers to nrfx_uarte.o(.bss) for .bss
    nrfx_uarte.o(i.nrfx_uarte_init) refers to nrfx_uarte.o(.constdata) for .constdata
    nrfx_uarte.o(i.nrfx_uarte_rx) refers to nrfx_uarte.o(i.nrfx_is_in_ram) for nrfx_is_in_ram
    nrfx_uarte.o(i.nrfx_uarte_rx) refers to nrfx_uarte.o(i.nrf_uarte_event_clear) for nrf_uarte_event_clear
    nrfx_uarte.o(i.nrfx_uarte_rx) refers to nrfx_uarte.o(i.nrf_uarte_event_check) for nrf_uarte_event_check
    nrfx_uarte.o(i.nrfx_uarte_rx) refers to nrfx_uarte.o(.bss) for .bss
    nrfx_uarte.o(i.nrfx_uarte_rx_abort) refers to nrfx_uarte.o(.bss) for .bss
    nrfx_uarte.o(i.nrfx_uarte_tx) refers to nrfx_uarte.o(i.nrfx_is_in_ram) for nrfx_is_in_ram
    nrfx_uarte.o(i.nrfx_uarte_tx) refers to nrfx_uarte.o(i.nrfx_uarte_tx_in_progress) for nrfx_uarte_tx_in_progress
    nrfx_uarte.o(i.nrfx_uarte_tx) refers to nrfx_uarte.o(i.nrf_uarte_event_clear) for nrf_uarte_event_clear
    nrfx_uarte.o(i.nrfx_uarte_tx) refers to nrfx_uarte.o(i.nrf_uarte_event_check) for nrf_uarte_event_check
    nrfx_uarte.o(i.nrfx_uarte_tx) refers to nrfx_uarte.o(.bss) for .bss
    nrfx_uarte.o(i.nrfx_uarte_tx_abort) refers to nrfx_uarte.o(i.nrf_uarte_event_clear) for nrf_uarte_event_clear
    nrfx_uarte.o(i.nrfx_uarte_tx_abort) refers to nrfx_uarte.o(i.nrf_uarte_event_check) for nrf_uarte_event_check
    nrfx_uarte.o(i.nrfx_uarte_tx_abort) refers to nrfx_uarte.o(.bss) for .bss
    nrfx_uarte.o(i.nrfx_uarte_tx_in_progress) refers to nrfx_uarte.o(.bss) for .bss
    nrfx_uarte.o(i.nrfx_uarte_uninit) refers to nrfx_uarte.o(i.nrf_uarte_event_clear) for nrf_uarte_event_clear
    nrfx_uarte.o(i.nrfx_uarte_uninit) refers to nrfx_uarte.o(i.nrf_uarte_event_check) for nrf_uarte_event_check
    nrfx_uarte.o(i.nrfx_uarte_uninit) refers to nrfx_uarte.o(i.nrf_gpio_cfg_default) for nrf_gpio_cfg_default
    nrfx_uarte.o(i.nrfx_uarte_uninit) refers to nrfx_prs.o(i.nrfx_prs_release) for nrfx_prs_release
    nrfx_uarte.o(i.nrfx_uarte_uninit) refers to nrfx_uarte.o(.bss) for .bss
    nrfx_uarte.o(i.uarte_irq_handler) refers to nrfx_uarte.o(i.nrf_uarte_event_check) for nrf_uarte_event_check
    nrfx_uarte.o(i.uarte_irq_handler) refers to nrfx_uarte.o(i.nrf_uarte_event_clear) for nrf_uarte_event_clear
    nrfx_uarte.o(i.uarte_irq_handler) refers to nrfx_uarte.o(i.rx_done_event) for rx_done_event
    nrfx_uarte.o(i.uarte_irq_handler) refers to nrfx_uarte.o(i.tx_done_event) for tx_done_event
    nrfx_uarte.o(.constdata) refers to nrfx_uarte.o(i.nrfx_uarte_0_irq_handler) for nrfx_uarte_0_irq_handler
    nrfx_uarte.o(log_const_data) refers to nrfx_uarte.o(.conststrlit) for .conststrlit
    nrfx_ppi.o(i.is_allocated_channel) refers to nrfx_ppi.o(.data) for .data
    nrfx_ppi.o(i.is_allocated_group) refers to nrfx_ppi.o(.data) for .data
    nrfx_ppi.o(i.is_app_channel) refers to nrfx_ppi.o(i.are_app_channels) for are_app_channels
    nrfx_ppi.o(i.nrfx_ppi_channel_alloc) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    nrfx_ppi.o(i.nrfx_ppi_channel_alloc) refers to nrfx_ppi.o(i.is_allocated_channel) for is_allocated_channel
    nrfx_ppi.o(i.nrfx_ppi_channel_alloc) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    nrfx_ppi.o(i.nrfx_ppi_channel_alloc) refers to nrfx_ppi.o(.data) for .data
    nrfx_ppi.o(i.nrfx_ppi_channel_assign) refers to nrfx_ppi.o(i.is_programmable_app_channel) for is_programmable_app_channel
    nrfx_ppi.o(i.nrfx_ppi_channel_assign) refers to nrfx_ppi.o(i.is_allocated_channel) for is_allocated_channel
    nrfx_ppi.o(i.nrfx_ppi_channel_disable) refers to nrfx_ppi.o(i.is_app_channel) for is_app_channel
    nrfx_ppi.o(i.nrfx_ppi_channel_disable) refers to nrfx_ppi.o(i.is_programmable_app_channel) for is_programmable_app_channel
    nrfx_ppi.o(i.nrfx_ppi_channel_disable) refers to nrfx_ppi.o(i.is_allocated_channel) for is_allocated_channel
    nrfx_ppi.o(i.nrfx_ppi_channel_enable) refers to nrfx_ppi.o(i.is_app_channel) for is_app_channel
    nrfx_ppi.o(i.nrfx_ppi_channel_enable) refers to nrfx_ppi.o(i.is_programmable_app_channel) for is_programmable_app_channel
    nrfx_ppi.o(i.nrfx_ppi_channel_enable) refers to nrfx_ppi.o(i.is_allocated_channel) for is_allocated_channel
    nrfx_ppi.o(i.nrfx_ppi_channel_fork_assign) refers to nrfx_ppi.o(i.is_allocated_channel) for is_allocated_channel
    nrfx_ppi.o(i.nrfx_ppi_channel_free) refers to nrfx_ppi.o(i.is_programmable_app_channel) for is_programmable_app_channel
    nrfx_ppi.o(i.nrfx_ppi_channel_free) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    nrfx_ppi.o(i.nrfx_ppi_channel_free) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    nrfx_ppi.o(i.nrfx_ppi_channel_free) refers to nrfx_ppi.o(.data) for .data
    nrfx_ppi.o(i.nrfx_ppi_channels_include_in_group) refers to nrfx_ppi.o(i.is_app_group) for is_app_group
    nrfx_ppi.o(i.nrfx_ppi_channels_include_in_group) refers to nrfx_ppi.o(i.is_allocated_group) for is_allocated_group
    nrfx_ppi.o(i.nrfx_ppi_channels_include_in_group) refers to nrfx_ppi.o(i.are_app_channels) for are_app_channels
    nrfx_ppi.o(i.nrfx_ppi_channels_include_in_group) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    nrfx_ppi.o(i.nrfx_ppi_channels_include_in_group) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    nrfx_ppi.o(i.nrfx_ppi_channels_remove_from_group) refers to nrfx_ppi.o(i.is_app_group) for is_app_group
    nrfx_ppi.o(i.nrfx_ppi_channels_remove_from_group) refers to nrfx_ppi.o(i.is_allocated_group) for is_allocated_group
    nrfx_ppi.o(i.nrfx_ppi_channels_remove_from_group) refers to nrfx_ppi.o(i.are_app_channels) for are_app_channels
    nrfx_ppi.o(i.nrfx_ppi_channels_remove_from_group) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    nrfx_ppi.o(i.nrfx_ppi_channels_remove_from_group) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    nrfx_ppi.o(i.nrfx_ppi_free_all) refers to nrfx_ppi.o(.data) for .data
    nrfx_ppi.o(i.nrfx_ppi_group_alloc) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    nrfx_ppi.o(i.nrfx_ppi_group_alloc) refers to nrfx_ppi.o(i.is_allocated_group) for is_allocated_group
    nrfx_ppi.o(i.nrfx_ppi_group_alloc) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    nrfx_ppi.o(i.nrfx_ppi_group_alloc) refers to nrfx_ppi.o(.data) for .data
    nrfx_ppi.o(i.nrfx_ppi_group_disable) refers to nrfx_ppi.o(i.is_app_group) for is_app_group
    nrfx_ppi.o(i.nrfx_ppi_group_enable) refers to nrfx_ppi.o(i.is_app_group) for is_app_group
    nrfx_ppi.o(i.nrfx_ppi_group_enable) refers to nrfx_ppi.o(i.is_allocated_group) for is_allocated_group
    nrfx_ppi.o(i.nrfx_ppi_group_free) refers to nrfx_ppi.o(i.is_app_group) for is_app_group
    nrfx_ppi.o(i.nrfx_ppi_group_free) refers to nrfx_ppi.o(i.is_allocated_group) for is_allocated_group
    nrfx_ppi.o(i.nrfx_ppi_group_free) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    nrfx_ppi.o(i.nrfx_ppi_group_free) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    nrfx_ppi.o(i.nrfx_ppi_group_free) refers to nrfx_ppi.o(.data) for .data
    nrfx_ppi.o(log_const_data) refers to nrfx_ppi.o(.conststrlit) for .conststrlit
    nrf_drv_ppi.o(i.nrf_drv_ppi_init) refers to nrf_drv_ppi.o(.data) for .data
    nrf_drv_ppi.o(i.nrf_drv_ppi_uninit) refers to nrfx_ppi.o(i.nrfx_ppi_free_all) for nrfx_ppi_free_all
    nrf_drv_ppi.o(i.nrf_drv_ppi_uninit) refers to nrf_drv_ppi.o(.data) for .data
    nrfx_saadc.o(i.SAADC_IRQHandler) refers to nrfx_saadc.o(i.nrf_saadc_event_check) for nrf_saadc_event_check
    nrfx_saadc.o(i.SAADC_IRQHandler) refers to nrfx_saadc.o(i.nrf_saadc_event_clear) for nrf_saadc_event_clear
    nrfx_saadc.o(i.SAADC_IRQHandler) refers to nrfx_saadc.o(i.nrf_saadc_buffer_init) for nrf_saadc_buffer_init
    nrfx_saadc.o(i.SAADC_IRQHandler) refers to nrfx_saadc.o(.bss) for .bss
    nrfx_saadc.o(i.nrfx_coredep_delay_us) refers to nrfx_saadc.o(.constdata) for .constdata
    nrfx_saadc.o(i.nrfx_saadc_abort) refers to nrfx_saadc.o(i.nrfx_saadc_is_busy) for nrfx_saadc_is_busy
    nrfx_saadc.o(i.nrfx_saadc_abort) refers to nrfx_saadc.o(i.nrf_saadc_event_clear) for nrf_saadc_event_clear
    nrfx_saadc.o(i.nrfx_saadc_abort) refers to nrfx_saadc.o(i.nrfx_coredep_delay_us) for nrfx_coredep_delay_us
    nrfx_saadc.o(i.nrfx_saadc_abort) refers to nrfx_saadc.o(.bss) for .bss
    nrfx_saadc.o(i.nrfx_saadc_buffer_convert) refers to nrfx_saadc.o(i.nrf_saadc_buffer_init) for nrf_saadc_buffer_init
    nrfx_saadc.o(i.nrfx_saadc_buffer_convert) refers to nrfx_saadc.o(i.nrf_saadc_event_check) for nrf_saadc_event_check
    nrfx_saadc.o(i.nrfx_saadc_buffer_convert) refers to nrfx_saadc.o(i.nrf_saadc_event_clear) for nrf_saadc_event_clear
    nrfx_saadc.o(i.nrfx_saadc_buffer_convert) refers to nrfx_saadc.o(.bss) for .bss
    nrfx_saadc.o(i.nrfx_saadc_calibrate_offset) refers to nrfx_saadc.o(i.nrf_saadc_event_clear) for nrf_saadc_event_clear
    nrfx_saadc.o(i.nrfx_saadc_calibrate_offset) refers to nrfx_saadc.o(.bss) for .bss
    nrfx_saadc.o(i.nrfx_saadc_channel_init) refers to nrfx_saadc.o(i.nrf_saadc_channel_input_set) for nrf_saadc_channel_input_set
    nrfx_saadc.o(i.nrfx_saadc_channel_init) refers to nrfx_saadc.o(.bss) for .bss
    nrfx_saadc.o(i.nrfx_saadc_channel_uninit) refers to nrfx_saadc.o(i.nrf_saadc_channel_input_set) for nrf_saadc_channel_input_set
    nrfx_saadc.o(i.nrfx_saadc_channel_uninit) refers to nrfx_saadc.o(i.nrfx_saadc_limits_set) for nrfx_saadc_limits_set
    nrfx_saadc.o(i.nrfx_saadc_channel_uninit) refers to nrfx_saadc.o(.bss) for .bss
    nrfx_saadc.o(i.nrfx_saadc_init) refers to nrfx_saadc.o(i.nrf_saadc_event_clear) for nrf_saadc_event_clear
    nrfx_saadc.o(i.nrfx_saadc_init) refers to nrfx_saadc.o(.bss) for .bss
    nrfx_saadc.o(i.nrfx_saadc_is_busy) refers to nrfx_saadc.o(.bss) for .bss
    nrfx_saadc.o(i.nrfx_saadc_limits_set) refers to nrfx_saadc.o(i.nrf_saadc_limit_int_get) for nrf_saadc_limit_int_get
    nrfx_saadc.o(i.nrfx_saadc_limits_set) refers to nrfx_saadc.o(.bss) for .bss
    nrfx_saadc.o(i.nrfx_saadc_sample) refers to nrfx_saadc.o(.bss) for .bss
    nrfx_saadc.o(i.nrfx_saadc_sample_convert) refers to nrfx_saadc.o(i.nrf_saadc_buffer_init) for nrf_saadc_buffer_init
    nrfx_saadc.o(i.nrfx_saadc_sample_convert) refers to nrfx_saadc.o(i.nrf_saadc_channel_input_set) for nrf_saadc_channel_input_set
    nrfx_saadc.o(i.nrfx_saadc_sample_convert) refers to nrfx_saadc.o(i.nrf_saadc_event_check) for nrf_saadc_event_check
    nrfx_saadc.o(i.nrfx_saadc_sample_convert) refers to nrfx_saadc.o(i.nrfx_coredep_delay_us) for nrfx_coredep_delay_us
    nrfx_saadc.o(i.nrfx_saadc_sample_convert) refers to nrfx_saadc.o(i.nrf_saadc_event_clear) for nrf_saadc_event_clear
    nrfx_saadc.o(i.nrfx_saadc_sample_convert) refers to nrfx_saadc.o(.bss) for .bss
    nrfx_saadc.o(i.nrfx_saadc_sample_task_get) refers to nrfx_saadc.o(.bss) for .bss
    nrfx_saadc.o(i.nrfx_saadc_uninit) refers to nrfx_saadc.o(i.nrf_saadc_event_check) for nrf_saadc_event_check
    nrfx_saadc.o(i.nrfx_saadc_uninit) refers to nrfx_saadc.o(i.nrfx_coredep_delay_us) for nrfx_coredep_delay_us
    nrfx_saadc.o(i.nrfx_saadc_uninit) refers to nrfx_saadc.o(i.nrfx_saadc_channel_uninit) for nrfx_saadc_channel_uninit
    nrfx_saadc.o(i.nrfx_saadc_uninit) refers to nrfx_saadc.o(.bss) for .bss
    nrfx_saadc.o(log_const_data) refers to nrfx_saadc.o(.conststrlit) for .conststrlit
    nrfx_timer.o(i.TIMER1_IRQHandler) refers to nrfx_timer.o(i.nrf_timer_event_clear) for nrf_timer_event_clear
    nrfx_timer.o(i.TIMER1_IRQHandler) refers to nrfx_timer.o(.bss) for .bss
    nrfx_timer.o(i.nrfx_timer_compare) refers to nrfx_timer.o(i.nrf_timer_event_clear) for nrf_timer_event_clear
    nrfx_timer.o(i.nrfx_timer_compare_int_enable) refers to nrfx_timer.o(i.nrf_timer_event_clear) for nrf_timer_event_clear
    nrfx_timer.o(i.nrfx_timer_disable) refers to nrfx_timer.o(.bss) for .bss
    nrfx_timer.o(i.nrfx_timer_enable) refers to nrfx_timer.o(.bss) for .bss
    nrfx_timer.o(i.nrfx_timer_extended_compare) refers to nrfx_timer.o(i.nrfx_timer_compare) for nrfx_timer_compare
    nrfx_timer.o(i.nrfx_timer_init) refers to nrfx_timer.o(i.nrf_timer_event_clear) for nrf_timer_event_clear
    nrfx_timer.o(i.nrfx_timer_init) refers to nrfx_timer.o(.bss) for .bss
    nrfx_timer.o(i.nrfx_timer_is_enabled) refers to nrfx_timer.o(.bss) for .bss
    nrfx_timer.o(i.nrfx_timer_uninit) refers to nrfx_timer.o(i.nrfx_timer_disable) for nrfx_timer_disable
    nrfx_timer.o(i.nrfx_timer_uninit) refers to nrfx_timer.o(.bss) for .bss
    nrfx_timer.o(log_const_data) refers to nrfx_timer.o(.conststrlit) for .conststrlit
    nrfx_spim.o(i.SPIM0_SPIS0_TWIM0_TWIS0_SPI0_TWI0_IRQHandler) refers to nrfx_spim.o(i.nrf_spim_event_check) for nrf_spim_event_check
    nrfx_spim.o(i.SPIM0_SPIS0_TWIM0_TWIS0_SPI0_TWI0_IRQHandler) refers to nrfx_spim.o(i.nrf_gpio_pin_clear) for nrf_gpio_pin_clear
    nrfx_spim.o(i.SPIM0_SPIS0_TWIM0_TWIS0_SPI0_TWI0_IRQHandler) refers to nrfx_spim.o(i.nrf_gpio_pin_set) for nrf_gpio_pin_set
    nrfx_spim.o(i.SPIM0_SPIS0_TWIM0_TWIS0_SPI0_TWI0_IRQHandler) refers to nrfx_spim.o(.bss) for .bss
    nrfx_spim.o(i.nrf_gpio_cfg_output) refers to nrfx_spim.o(i.nrf_gpio_cfg) for nrf_gpio_cfg
    nrfx_spim.o(i.nrfx_spim_abort) refers to nrfx_spim.o(i.nrf_spim_event_check) for nrf_spim_event_check
    nrfx_spim.o(i.nrfx_spim_abort) refers to nrfx_spim.o(.bss) for .bss
    nrfx_spim.o(i.nrfx_spim_init) refers to nrfx_prs.o(i.nrfx_prs_acquire) for nrfx_prs_acquire
    nrfx_spim.o(i.nrfx_spim_init) refers to nrfx_spim.o(i.nrf_gpio_pin_clear) for nrf_gpio_pin_clear
    nrfx_spim.o(i.nrfx_spim_init) refers to nrfx_spim.o(i.nrf_gpio_pin_set) for nrf_gpio_pin_set
    nrfx_spim.o(i.nrfx_spim_init) refers to nrfx_spim.o(i.nrf_gpio_cfg) for nrf_gpio_cfg
    nrfx_spim.o(i.nrfx_spim_init) refers to nrfx_spim.o(i.nrf_gpio_cfg_output) for nrf_gpio_cfg_output
    nrfx_spim.o(i.nrfx_spim_init) refers to nrfx_spim.o(.bss) for .bss
    nrfx_spim.o(i.nrfx_spim_init) refers to nrfx_spim.o(.constdata) for .constdata
    nrfx_spim.o(i.nrfx_spim_uninit) refers to nrfx_spim.o(i.nrf_spim_event_check) for nrf_spim_event_check
    nrfx_spim.o(i.nrfx_spim_uninit) refers to nrfx_spim.o(i.nrf_gpio_cfg) for nrf_gpio_cfg
    nrfx_spim.o(i.nrfx_spim_uninit) refers to nrfx_prs.o(i.nrfx_prs_release) for nrfx_prs_release
    nrfx_spim.o(i.nrfx_spim_uninit) refers to nrfx_spim.o(.bss) for .bss
    nrfx_spim.o(i.nrfx_spim_xfer) refers to nrfx_spim.o(i.nrf_gpio_pin_set) for nrf_gpio_pin_set
    nrfx_spim.o(i.nrfx_spim_xfer) refers to nrfx_spim.o(i.nrf_gpio_pin_clear) for nrf_gpio_pin_clear
    nrfx_spim.o(i.nrfx_spim_xfer) refers to nrfx_spim.o(i.spim_xfer) for spim_xfer
    nrfx_spim.o(i.nrfx_spim_xfer) refers to nrfx_spim.o(.bss) for .bss
    nrfx_spim.o(i.spim_xfer) refers to nrfx_spim.o(i.nrfx_is_in_ram) for nrfx_is_in_ram
    nrfx_spim.o(i.spim_xfer) refers to nrfx_spim.o(i.nrf_spim_event_check) for nrf_spim_event_check
    nrfx_spim.o(i.spim_xfer) refers to nrfx_spim.o(i.nrf_gpio_pin_clear) for nrf_gpio_pin_clear
    nrfx_spim.o(i.spim_xfer) refers to nrfx_spim.o(i.nrf_gpio_pin_set) for nrf_gpio_pin_set
    nrfx_spim.o(.constdata) refers to nrfx_spim.o(i.SPIM0_SPIS0_TWIM0_TWIS0_SPI0_TWI0_IRQHandler) for SPIM0_SPIS0_TWIM0_TWIS0_SPI0_TWI0_IRQHandler
    nrfx_spim.o(log_const_data) refers to nrfx_spim.o(.conststrlit) for .conststrlit
    nrf_drv_spi.o(i.nrf_drv_spi_init) refers to nrfx_spim.o(i.nrfx_spim_init) for nrfx_spim_init
    nrf_drv_spi.o(i.nrf_drv_spi_init) refers to nrf_drv_spi.o(.bss) for .bss
    nrf_drv_spi.o(i.nrf_drv_spi_init) refers to nrf_drv_spi.o(.constdata) for .constdata
    nrf_drv_spi.o(i.nrf_drv_spi_init) refers to nrf_drv_spi.o(i.spim_evt_handler) for spim_evt_handler
    nrf_drv_spi.o(i.spim_evt_handler) refers to nrf_drv_spi.o(.bss) for .bss
    app_button.o(i.app_button_disable) refers to nrfx_gpiote.o(i.nrfx_gpiote_in_event_disable) for nrfx_gpiote_in_event_disable
    app_button.o(i.app_button_disable) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    app_button.o(i.app_button_disable) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    app_button.o(i.app_button_disable) refers to app_timer2.o(i.app_timer_stop) for app_timer_stop
    app_button.o(i.app_button_disable) refers to app_button.o(.data) for .data
    app_button.o(i.app_button_disable) refers to app_button.o(.constdata) for .constdata
    app_button.o(i.app_button_enable) refers to nrfx_gpiote.o(i.nrfx_gpiote_in_event_enable) for nrfx_gpiote_in_event_enable
    app_button.o(i.app_button_enable) refers to app_button.o(.data) for .data
    app_button.o(i.app_button_init) refers to nrfx_gpiote.o(i.nrfx_gpiote_is_init) for nrfx_gpiote_is_init
    app_button.o(i.app_button_init) refers to nrfx_gpiote.o(i.nrfx_gpiote_init) for nrfx_gpiote_init
    app_button.o(i.app_button_init) refers to memseta.o(.text) for __aeabi_memclr
    app_button.o(i.app_button_init) refers to nrfx_gpiote.o(i.nrfx_gpiote_in_init) for nrfx_gpiote_in_init
    app_button.o(i.app_button_init) refers to app_timer2.o(i.app_timer_create) for app_timer_create
    app_button.o(i.app_button_init) refers to app_button.o(.data) for .data
    app_button.o(i.app_button_init) refers to app_button.o(.bss) for .bss
    app_button.o(i.app_button_init) refers to app_button.o(.constdata) for .constdata
    app_button.o(i.app_button_init) refers to app_button.o(i.gpiote_event_handler) for gpiote_event_handler
    app_button.o(i.app_button_init) refers to app_button.o(i.detection_delay_timeout_handler) for detection_delay_timeout_handler
    app_button.o(i.app_button_is_pushed) refers to nrfx_gpiote.o(i.nrfx_gpiote_in_is_set) for nrfx_gpiote_in_is_set
    app_button.o(i.app_button_is_pushed) refers to app_button.o(.data) for .data
    app_button.o(i.button_get) refers to app_button.o(.data) for .data
    app_button.o(i.detection_delay_timeout_handler) refers to nrfx_gpiote.o(i.nrfx_gpiote_in_is_set) for nrfx_gpiote_in_is_set
    app_button.o(i.detection_delay_timeout_handler) refers to app_button.o(i.evt_handle) for evt_handle
    app_button.o(i.detection_delay_timeout_handler) refers to app_button.o(i.timer_start) for timer_start
    app_button.o(i.detection_delay_timeout_handler) refers to app_button.o(.data) for .data
    app_button.o(i.evt_handle) refers to llshl.o(.text) for __aeabi_llsl
    app_button.o(i.evt_handle) refers to app_button.o(i.state_set) for state_set
    app_button.o(i.evt_handle) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    app_button.o(i.evt_handle) refers to app_button.o(i.usr_event) for usr_event
    app_button.o(i.evt_handle) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    app_button.o(i.evt_handle) refers to app_button.o(.bss) for .bss
    app_button.o(i.evt_handle) refers to app_button.o(.data) for .data
    app_button.o(i.gpiote_event_handler) refers to app_button.o(i.button_get) for button_get
    app_button.o(i.gpiote_event_handler) refers to nrfx_gpiote.o(i.nrfx_gpiote_in_is_set) for nrfx_gpiote_in_is_set
    app_button.o(i.gpiote_event_handler) refers to app_button.o(i.timer_start) for timer_start
    app_button.o(i.gpiote_event_handler) refers to app_button.o(.data) for .data
    app_button.o(i.state_set) refers to app_button.o(.bss) for .bss
    app_button.o(i.timer_start) refers to app_timer2.o(i.app_timer_start) for app_timer_start
    app_button.o(i.timer_start) refers to app_button.o(.data) for .data
    app_button.o(i.timer_start) refers to app_button.o(.constdata) for .constdata
    app_button.o(i.usr_event) refers to app_button.o(i.button_get) for button_get
    app_button.o(.constdata) refers to app_button.o(.bss) for m_detection_delay_timer_id_data
    app_button.o(log_const_data) refers to app_button.o(.conststrlit) for .conststrlit
    app_error.o(i.app_error_handler_bare) refers to app_error_weak.o(i.app_error_fault_handler) for app_error_fault_handler
    app_error.o(i.app_error_save_and_stop) refers to app_error.o(.bss) for .bss
    app_error_handler_keil.o(.emb_text) refers to app_error_weak.o(i.app_error_fault_handler) for app_error_fault_handler
    app_error_weak.o(i.app_error_fault_handler) refers to nrf_log_frontend.o(i.nrf_log_panic) for nrf_log_panic
    app_error_weak.o(i.app_error_fault_handler) refers to nrf_log_frontend.o(i.nrf_log_frontend_dequeue) for nrf_log_frontend_dequeue
    app_error_weak.o(i.app_error_fault_handler) refers to nrf_log_frontend.o(i.nrf_log_frontend_std_0) for nrf_log_frontend_std_0
    app_error_weak.o(i.app_error_fault_handler) refers to nrf_log_frontend.o(log_const_data) for m_nrf_log_app_logs_data_const
    app_fifo.o(i.app_fifo_get) refers to app_fifo.o(i.fifo_get) for fifo_get
    app_fifo.o(i.app_fifo_put) refers to app_fifo.o(i.fifo_put) for fifo_put
    app_fifo.o(i.app_fifo_read) refers to app_fifo.o(i.fifo_get) for fifo_get
    app_fifo.o(i.app_fifo_write) refers to app_fifo.o(i.fifo_put) for fifo_put
    app_scheduler.o(i.app_sched_event_put) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    app_scheduler.o(i.app_sched_event_put) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    app_scheduler.o(i.app_sched_event_put) refers to memcpya.o(.text) for __aeabi_memcpy
    app_scheduler.o(i.app_sched_event_put) refers to app_scheduler.o(.data) for .data
    app_scheduler.o(i.app_sched_execute) refers to app_scheduler.o(.data) for .data
    app_scheduler.o(i.app_sched_init) refers to app_scheduler.o(.data) for .data
    app_scheduler.o(i.app_sched_queue_space_get) refers to app_scheduler.o(.data) for .data
    app_timer2.o(i.app_timer_cnt_get) refers to drv_rtc.o(i.drv_rtc_counter_get) for drv_rtc_counter_get
    app_timer2.o(i.app_timer_cnt_get) refers to app_timer2.o(.data) for .data
    app_timer2.o(i.app_timer_init) refers to nrf_atfifo.o(i.nrf_atfifo_init) for nrf_atfifo_init
    app_timer2.o(i.app_timer_init) refers to drv_rtc.o(i.drv_rtc_init) for drv_rtc_init
    app_timer2.o(i.app_timer_init) refers to drv_rtc.o(i.drv_rtc_overflow_enable) for drv_rtc_overflow_enable
    app_timer2.o(i.app_timer_init) refers to drv_rtc.o(i.drv_rtc_compare_set) for drv_rtc_compare_set
    app_timer2.o(i.app_timer_init) refers to app_timer2.o(.constdata) for .constdata
    app_timer2.o(i.app_timer_init) refers to app_timer2.o(.bss) for .bss
    app_timer2.o(i.app_timer_init) refers to app_timer2.o(i.rtc_irq) for rtc_irq
    app_timer2.o(i.app_timer_init) refers to app_timer2.o(.data) for .data
    app_timer2.o(i.app_timer_pause) refers to drv_rtc.o(i.drv_rtc_stop) for drv_rtc_stop
    app_timer2.o(i.app_timer_pause) refers to app_timer2.o(.data) for .data
    app_timer2.o(i.app_timer_resume) refers to drv_rtc.o(i.drv_rtc_start) for drv_rtc_start
    app_timer2.o(i.app_timer_resume) refers to app_timer2.o(.data) for .data
    app_timer2.o(i.app_timer_start) refers to app_timer2.o(i.get_now) for get_now
    app_timer2.o(i.app_timer_start) refers to app_timer2.o(i.timer_req_schedule) for timer_req_schedule
    app_timer2.o(i.app_timer_stop) refers to app_timer2.o(i.timer_req_schedule) for timer_req_schedule
    app_timer2.o(i.app_timer_stop_all) refers to app_timer2.o(i.timer_req_schedule) for timer_req_schedule
    app_timer2.o(i.app_timer_stop_all) refers to app_timer2.o(.data) for .data
    app_timer2.o(i.get_now) refers to drv_rtc.o(i.drv_rtc_counter_get) for drv_rtc_counter_get
    app_timer2.o(i.get_now) refers to app_timer2.o(.data) for .data
    app_timer2.o(i.rtc_irq) refers to drv_rtc.o(i.drv_rtc_overflow_pending) for drv_rtc_overflow_pending
    app_timer2.o(i.rtc_irq) refers to drv_rtc.o(i.drv_rtc_compare_pending) for drv_rtc_compare_pending
    app_timer2.o(i.rtc_irq) refers to app_timer2.o(i.timer_expire) for timer_expire
    app_timer2.o(i.rtc_irq) refers to app_timer2.o(i.get_now) for get_now
    app_timer2.o(i.rtc_irq) refers to app_timer2.o(i.timer_req_process) for timer_req_process
    app_timer2.o(i.rtc_irq) refers to app_timer2.o(i.rtc_update) for rtc_update
    app_timer2.o(i.rtc_irq) refers to app_timer2.o(.data) for .data
    app_timer2.o(i.rtc_schedule) refers to app_timer2.o(i.get_now) for get_now
    app_timer2.o(i.rtc_schedule) refers to app_timer2.o(i.app_timer_cnt_get) for app_timer_cnt_get
    app_timer2.o(i.rtc_schedule) refers to drv_rtc.o(i.drv_rtc_windowed_compare_set) for drv_rtc_windowed_compare_set
    app_timer2.o(i.rtc_schedule) refers to drv_rtc.o(i.drv_rtc_compare_disable) for drv_rtc_compare_disable
    app_timer2.o(i.rtc_schedule) refers to app_timer2.o(i.timer_expire) for timer_expire
    app_timer2.o(i.rtc_schedule) refers to app_timer2.o(.data) for .data
    app_timer2.o(i.rtc_update) refers to nrf_sortlist.o(i.nrf_sortlist_peek) for nrf_sortlist_peek
    app_timer2.o(i.rtc_update) refers to nrf_sortlist.o(i.nrf_sortlist_add) for nrf_sortlist_add
    app_timer2.o(i.rtc_update) refers to app_timer2.o(i.sortlist_pop) for sortlist_pop
    app_timer2.o(i.rtc_update) refers to app_timer2.o(i.rtc_schedule) for rtc_schedule
    app_timer2.o(i.rtc_update) refers to drv_rtc.o(i.drv_rtc_stop) for drv_rtc_stop
    app_timer2.o(i.rtc_update) refers to drv_rtc.o(i.drv_rtc_start) for drv_rtc_start
    app_timer2.o(i.rtc_update) refers to app_timer2.o(.data) for .data
    app_timer2.o(i.rtc_update) refers to app_timer2.o(.constdata) for .constdata
    app_timer2.o(i.sortlist_pop) refers to nrf_sortlist.o(i.nrf_sortlist_pop) for nrf_sortlist_pop
    app_timer2.o(i.sortlist_pop) refers to app_timer2.o(.constdata) for .constdata
    app_timer2.o(i.timer_expire) refers to app_timer2.o(i.get_now) for get_now
    app_timer2.o(i.timer_expire) refers to nrf_sortlist.o(i.nrf_sortlist_add) for nrf_sortlist_add
    app_timer2.o(i.timer_expire) refers to app_timer2.o(.data) for .data
    app_timer2.o(i.timer_expire) refers to app_timer2.o(.constdata) for .constdata
    app_timer2.o(i.timer_req_process) refers to nrf_atfifo.o(i.nrf_atfifo_item_get) for nrf_atfifo_item_get
    app_timer2.o(i.timer_req_process) refers to nrf_sortlist.o(i.nrf_sortlist_add) for nrf_sortlist_add
    app_timer2.o(i.timer_req_process) refers to nrf_sortlist.o(i.nrf_sortlist_remove) for nrf_sortlist_remove
    app_timer2.o(i.timer_req_process) refers to app_timer2.o(i.sortlist_pop) for sortlist_pop
    app_timer2.o(i.timer_req_process) refers to nrf_atfifo.o(i.nrf_atfifo_item_free) for nrf_atfifo_item_free
    app_timer2.o(i.timer_req_process) refers to app_timer2.o(.constdata) for .constdata
    app_timer2.o(i.timer_req_process) refers to app_timer2.o(.data) for .data
    app_timer2.o(i.timer_req_schedule) refers to nrf_atfifo.o(i.nrf_atfifo_item_alloc) for nrf_atfifo_item_alloc
    app_timer2.o(i.timer_req_schedule) refers to nrf_atfifo.o(i.nrf_atfifo_item_put) for nrf_atfifo_item_put
    app_timer2.o(i.timer_req_schedule) refers to drv_rtc.o(i.drv_rtc_irq_trigger) for drv_rtc_irq_trigger
    app_timer2.o(i.timer_req_schedule) refers to app_timer2.o(.constdata) for .constdata
    app_timer2.o(i.timer_req_schedule) refers to app_timer2.o(.data) for .data
    app_timer2.o(.constdata) refers to app_timer2.o(.bss) for m_req_fifo_inst
    app_timer2.o(.constdata) refers to app_timer2.o(.data) for m_app_timer_sortlist_sortlist_cb
    app_timer2.o(.constdata) refers to app_timer2.o(i.compare_func) for compare_func
    app_timer2.o(log_const_data) refers to app_timer2.o(.conststrlit) for .conststrlit
    app_uart_fifo.o(i.app_uart_close) refers to nrfx_uarte.o(i.nrfx_uarte_uninit) for nrfx_uarte_uninit
    app_uart_fifo.o(i.app_uart_close) refers to nrfx_uart.o(i.nrfx_uart_uninit) for nrfx_uart_uninit
    app_uart_fifo.o(i.app_uart_close) refers to app_uart_fifo.o(.data) for .data
    app_uart_fifo.o(i.app_uart_close) refers to nrf_drv_uart.o(.data) for nrf_drv_uart_use_easy_dma
    app_uart_fifo.o(i.app_uart_flush) refers to app_fifo.o(i.app_fifo_flush) for app_fifo_flush
    app_uart_fifo.o(i.app_uart_flush) refers to app_uart_fifo.o(.bss) for .bss
    app_uart_fifo.o(i.app_uart_get) refers to app_fifo.o(i.app_fifo_get) for app_fifo_get
    app_uart_fifo.o(i.app_uart_get) refers to app_uart_fifo.o(i.nrf_drv_uart_rx) for nrf_drv_uart_rx
    app_uart_fifo.o(i.app_uart_get) refers to app_error.o(i.app_error_handler_bare) for app_error_handler_bare
    app_uart_fifo.o(i.app_uart_get) refers to app_uart_fifo.o(.data) for .data
    app_uart_fifo.o(i.app_uart_get) refers to app_uart_fifo.o(.bss) for .bss
    app_uart_fifo.o(i.app_uart_init) refers to app_fifo.o(i.app_fifo_init) for app_fifo_init
    app_uart_fifo.o(i.app_uart_init) refers to memcpya.o(.text) for __aeabi_memcpy4
    app_uart_fifo.o(i.app_uart_init) refers to nrf_drv_uart.o(i.nrf_drv_uart_init) for nrf_drv_uart_init
    app_uart_fifo.o(i.app_uart_init) refers to app_uart_fifo.o(i.nrf_drv_uart_rx) for nrf_drv_uart_rx
    app_uart_fifo.o(i.app_uart_init) refers to app_uart_fifo.o(.data) for .data
    app_uart_fifo.o(i.app_uart_init) refers to app_uart_fifo.o(.bss) for .bss
    app_uart_fifo.o(i.app_uart_init) refers to app_uart_fifo.o(.constdata) for .constdata
    app_uart_fifo.o(i.app_uart_init) refers to app_uart_fifo.o(i.uart_event_handler) for uart_event_handler
    app_uart_fifo.o(i.app_uart_put) refers to app_fifo.o(i.app_fifo_put) for app_fifo_put
    app_uart_fifo.o(i.app_uart_put) refers to nrfx_uarte.o(i.nrfx_uarte_tx_in_progress) for nrfx_uarte_tx_in_progress
    app_uart_fifo.o(i.app_uart_put) refers to nrfx_uart.o(i.nrfx_uart_tx_in_progress) for nrfx_uart_tx_in_progress
    app_uart_fifo.o(i.app_uart_put) refers to app_fifo.o(i.app_fifo_get) for app_fifo_get
    app_uart_fifo.o(i.app_uart_put) refers to app_uart_fifo.o(i.nrf_drv_uart_tx) for nrf_drv_uart_tx
    app_uart_fifo.o(i.app_uart_put) refers to app_uart_fifo.o(.bss) for .bss
    app_uart_fifo.o(i.app_uart_put) refers to app_uart_fifo.o(.data) for .data
    app_uart_fifo.o(i.app_uart_put) refers to nrf_drv_uart.o(.data) for nrf_drv_uart_use_easy_dma
    app_uart_fifo.o(i.nrf_drv_uart_rx) refers to nrfx_uarte.o(i.nrfx_uarte_rx) for nrfx_uarte_rx
    app_uart_fifo.o(i.nrf_drv_uart_rx) refers to nrfx_uart.o(i.nrfx_uart_rx) for nrfx_uart_rx
    app_uart_fifo.o(i.nrf_drv_uart_rx) refers to nrf_drv_uart.o(.data) for nrf_drv_uart_use_easy_dma
    app_uart_fifo.o(i.nrf_drv_uart_tx) refers to nrfx_uarte.o(i.nrfx_uarte_tx) for nrfx_uarte_tx
    app_uart_fifo.o(i.nrf_drv_uart_tx) refers to nrfx_uart.o(i.nrfx_uart_tx) for nrfx_uart_tx
    app_uart_fifo.o(i.nrf_drv_uart_tx) refers to nrf_drv_uart.o(.data) for nrf_drv_uart_use_easy_dma
    app_uart_fifo.o(i.uart_event_handler) refers to app_uart_fifo.o(i.nrf_drv_uart_rx) for nrf_drv_uart_rx
    app_uart_fifo.o(i.uart_event_handler) refers to app_fifo.o(i.app_fifo_put) for app_fifo_put
    app_uart_fifo.o(i.uart_event_handler) refers to app_fifo.o(i.app_fifo_get) for app_fifo_get
    app_uart_fifo.o(i.uart_event_handler) refers to app_uart_fifo.o(i.nrf_drv_uart_tx) for nrf_drv_uart_tx
    app_uart_fifo.o(i.uart_event_handler) refers to app_uart_fifo.o(.data) for .data
    app_uart_fifo.o(i.uart_event_handler) refers to app_uart_fifo.o(.bss) for .bss
    app_util_platform.o(i.app_util_critical_region_enter) refers to app_util_platform.o(.bss) for .bss
    app_util_platform.o(i.app_util_critical_region_exit) refers to app_util_platform.o(.bss) for .bss
    app_util_platform.o(i.app_util_disable_irq) refers to app_util_platform.o(.data) for .data
    app_util_platform.o(i.app_util_enable_irq) refers to app_util_platform.o(.data) for .data
    drv_rtc.o(i.RTC1_IRQHandler) refers to drv_rtc.o(.data) for .data
    drv_rtc.o(i.drv_rtc_compare_enable) refers to drv_rtc.o(i.evt_enable) for evt_enable
    drv_rtc.o(i.drv_rtc_compare_pending) refers to drv_rtc.o(i.evt_pending) for evt_pending
    drv_rtc.o(i.drv_rtc_compare_set) refers to drv_rtc.o(i.nrf_rtc_event_clear) for nrf_rtc_event_clear
    drv_rtc.o(i.drv_rtc_init) refers to nrf_log_frontend.o(i.nrf_log_frontend_std_0) for nrf_log_frontend_std_0
    drv_rtc.o(i.drv_rtc_init) refers to drv_rtc.o(.data) for .data
    drv_rtc.o(i.drv_rtc_init) refers to app_timer2.o(log_const_data) for m_nrf_log_app_timer_logs_data_const
    drv_rtc.o(i.drv_rtc_overflow_enable) refers to drv_rtc.o(i.evt_enable) for evt_enable
    drv_rtc.o(i.drv_rtc_overflow_pending) refers to drv_rtc.o(i.evt_pending) for evt_pending
    drv_rtc.o(i.drv_rtc_tick_enable) refers to drv_rtc.o(i.evt_enable) for evt_enable
    drv_rtc.o(i.drv_rtc_tick_pending) refers to drv_rtc.o(i.evt_pending) for evt_pending
    drv_rtc.o(i.drv_rtc_uninit) refers to nrf_log_frontend.o(i.nrf_log_frontend_std_0) for nrf_log_frontend_std_0
    drv_rtc.o(i.drv_rtc_uninit) refers to drv_rtc.o(.data) for .data
    drv_rtc.o(i.drv_rtc_uninit) refers to app_timer2.o(log_const_data) for m_nrf_log_app_timer_logs_data_const
    drv_rtc.o(i.drv_rtc_windowed_compare_set) refers to drv_rtc.o(i.nrf_rtc_event_clear) for nrf_rtc_event_clear
    drv_rtc.o(i.drv_rtc_windowed_compare_set) refers to drv_rtc.o(i.nrfx_coredep_delay_us) for nrfx_coredep_delay_us
    drv_rtc.o(i.drv_rtc_windowed_compare_set) refers to drv_rtc.o(i.evt_enable) for evt_enable
    drv_rtc.o(i.evt_pending) refers to drv_rtc.o(i.nrf_rtc_event_clear) for nrf_rtc_event_clear
    drv_rtc.o(i.nrfx_coredep_delay_us) refers to drv_rtc.o(.constdata) for .constdata
    nrf_assert.o(i.assert_nrf_callback) refers to app_error_weak.o(i.app_error_fault_handler) for app_error_fault_handler
    nrf_atfifo.o(i.nrf_atfifo_alloc_put) refers to nrf_atfifo.o(i.nrf_atfifo_item_alloc) for nrf_atfifo_item_alloc
    nrf_atfifo.o(i.nrf_atfifo_alloc_put) refers to memcpya.o(.text) for __aeabi_memcpy
    nrf_atfifo.o(i.nrf_atfifo_alloc_put) refers to nrf_atfifo.o(i.nrf_atfifo_item_put) for nrf_atfifo_item_put
    nrf_atfifo.o(i.nrf_atfifo_clear) refers to nrf_atfifo.o(.emb_text) for __asm___12_nrf_atfifo_c_51f461e1__nrf_atfifo_space_clear
    nrf_atfifo.o(i.nrf_atfifo_get_free) refers to nrf_atfifo.o(i.nrf_atfifo_item_get) for nrf_atfifo_item_get
    nrf_atfifo.o(i.nrf_atfifo_get_free) refers to memcpya.o(.text) for __aeabi_memcpy
    nrf_atfifo.o(i.nrf_atfifo_get_free) refers to nrf_atfifo.o(i.nrf_atfifo_item_free) for nrf_atfifo_item_free
    nrf_atfifo.o(i.nrf_atfifo_item_alloc) refers to nrf_atfifo.o(.emb_text) for __asm___12_nrf_atfifo_c_51f461e1__nrf_atfifo_wspace_req
    nrf_atfifo.o(i.nrf_atfifo_item_free) refers to nrf_atfifo.o(.emb_text) for __asm___12_nrf_atfifo_c_51f461e1__nrf_atfifo_rspace_close
    nrf_atfifo.o(i.nrf_atfifo_item_get) refers to nrf_atfifo.o(.emb_text) for __asm___12_nrf_atfifo_c_51f461e1__nrf_atfifo_rspace_req
    nrf_atfifo.o(i.nrf_atfifo_item_put) refers to nrf_atfifo.o(.emb_text) for __asm___12_nrf_atfifo_c_51f461e1__nrf_atfifo_wspace_close
    nrf_atflags.o(i.nrf_atflags_clear) refers to nrf_atomic.o(i.nrf_atomic_u32_and) for nrf_atomic_u32_and
    nrf_atflags.o(i.nrf_atflags_fetch_clear) refers to nrf_atomic.o(i.nrf_atomic_u32_fetch_and) for nrf_atomic_u32_fetch_and
    nrf_atflags.o(i.nrf_atflags_fetch_set) refers to nrf_atomic.o(i.nrf_atomic_u32_fetch_or) for nrf_atomic_u32_fetch_or
    nrf_atflags.o(i.nrf_atflags_find_and_clear_flag) refers to nrf_atflags.o(i.nrf_atflags_fetch_clear) for nrf_atflags_fetch_clear
    nrf_atflags.o(i.nrf_atflags_find_and_set_flag) refers to nrf_atflags.o(i.nrf_atflags_fetch_set) for nrf_atflags_fetch_set
    nrf_atflags.o(i.nrf_atflags_set) refers to nrf_atomic.o(i.nrf_atomic_u32_or) for nrf_atomic_u32_or
    nrf_atomic.o(i.nrf_atomic_flag_clear) refers to nrf_atomic.o(i.nrf_atomic_u32_and) for nrf_atomic_u32_and
    nrf_atomic.o(i.nrf_atomic_flag_clear_fetch) refers to nrf_atomic.o(i.nrf_atomic_u32_fetch_and) for nrf_atomic_u32_fetch_and
    nrf_atomic.o(i.nrf_atomic_flag_set) refers to nrf_atomic.o(i.nrf_atomic_u32_or) for nrf_atomic_u32_or
    nrf_atomic.o(i.nrf_atomic_flag_set_fetch) refers to nrf_atomic.o(i.nrf_atomic_u32_fetch_or) for nrf_atomic_u32_fetch_or
    nrf_atomic.o(i.nrf_atomic_u32_add) refers to nrf_atomic.o(.emb_text) for __asm___12_nrf_atomic_c_85ca2469__nrf_atomic_internal_add
    nrf_atomic.o(i.nrf_atomic_u32_and) refers to nrf_atomic.o(.emb_text) for __asm___12_nrf_atomic_c_85ca2469__nrf_atomic_internal_and
    nrf_atomic.o(i.nrf_atomic_u32_cmp_exch) refers to nrf_atomic.o(.emb_text) for __asm___12_nrf_atomic_c_85ca2469__nrf_atomic_internal_cmp_exch
    nrf_atomic.o(i.nrf_atomic_u32_fetch_add) refers to nrf_atomic.o(.emb_text) for __asm___12_nrf_atomic_c_85ca2469__nrf_atomic_internal_add
    nrf_atomic.o(i.nrf_atomic_u32_fetch_and) refers to nrf_atomic.o(.emb_text) for __asm___12_nrf_atomic_c_85ca2469__nrf_atomic_internal_and
    nrf_atomic.o(i.nrf_atomic_u32_fetch_or) refers to nrf_atomic.o(.emb_text) for __asm___12_nrf_atomic_c_85ca2469__nrf_atomic_internal_orr
    nrf_atomic.o(i.nrf_atomic_u32_fetch_store) refers to nrf_atomic.o(.emb_text) for __asm___12_nrf_atomic_c_85ca2469__nrf_atomic_internal_mov
    nrf_atomic.o(i.nrf_atomic_u32_fetch_sub) refers to nrf_atomic.o(.emb_text) for __asm___12_nrf_atomic_c_85ca2469__nrf_atomic_internal_sub
    nrf_atomic.o(i.nrf_atomic_u32_fetch_sub_hs) refers to nrf_atomic.o(.emb_text) for __asm___12_nrf_atomic_c_85ca2469__nrf_atomic_internal_sub_hs
    nrf_atomic.o(i.nrf_atomic_u32_fetch_xor) refers to nrf_atomic.o(.emb_text) for __asm___12_nrf_atomic_c_85ca2469__nrf_atomic_internal_eor
    nrf_atomic.o(i.nrf_atomic_u32_or) refers to nrf_atomic.o(.emb_text) for __asm___12_nrf_atomic_c_85ca2469__nrf_atomic_internal_orr
    nrf_atomic.o(i.nrf_atomic_u32_store) refers to nrf_atomic.o(.emb_text) for __asm___12_nrf_atomic_c_85ca2469__nrf_atomic_internal_mov
    nrf_atomic.o(i.nrf_atomic_u32_sub) refers to nrf_atomic.o(.emb_text) for __asm___12_nrf_atomic_c_85ca2469__nrf_atomic_internal_sub
    nrf_atomic.o(i.nrf_atomic_u32_sub_hs) refers to nrf_atomic.o(.emb_text) for __asm___12_nrf_atomic_c_85ca2469__nrf_atomic_internal_sub_hs
    nrf_atomic.o(i.nrf_atomic_u32_xor) refers to nrf_atomic.o(.emb_text) for __asm___12_nrf_atomic_c_85ca2469__nrf_atomic_internal_eor
    nrf_balloc.o(i.nrf_balloc_alloc) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    nrf_balloc.o(i.nrf_balloc_alloc) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    nrf_balloc.o(i.nrf_balloc_free) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    nrf_balloc.o(i.nrf_balloc_free) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    nrf_fprintf.o(i.nrf_fprintf) refers to nrf_fprintf_format.o(i.nrf_fprintf_fmt) for nrf_fprintf_fmt
    nrf_fprintf_format.o(i.buffer_add) refers to nrf_fprintf.o(i.nrf_fprintf_buffer_flush) for nrf_fprintf_buffer_flush
    nrf_fprintf_format.o(i.int_print) refers to nrf_fprintf_format.o(i.buffer_add) for buffer_add
    nrf_fprintf_format.o(i.int_print) refers to nrf_fprintf_format.o(i.unsigned_print) for unsigned_print
    nrf_fprintf_format.o(i.nrf_fprintf_fmt) refers to nrf_fprintf_format.o(i.buffer_add) for buffer_add
    nrf_fprintf_format.o(i.nrf_fprintf_fmt) refers to nrf_fprintf.o(i.nrf_fprintf_buffer_flush) for nrf_fprintf_buffer_flush
    nrf_fprintf_format.o(i.nrf_fprintf_fmt) refers to nrf_fprintf_format.o(i.int_print) for int_print
    nrf_fprintf_format.o(i.nrf_fprintf_fmt) refers to nrf_fprintf_format.o(i.unsigned_print) for unsigned_print
    nrf_fprintf_format.o(i.nrf_fprintf_fmt) refers to strlen.o(.text) for strlen
    nrf_fprintf_format.o(i.unsigned_print) refers to nrf_fprintf_format.o(i.buffer_add) for buffer_add
    nrf_fprintf_format.o(i.unsigned_print) refers to nrf_fprintf_format.o(.constdata) for .constdata
    nrf_memobj.o(i.memobj_op) refers to memcpya.o(.text) for __aeabi_memcpy
    nrf_memobj.o(i.nrf_memobj_alloc) refers to nrf_balloc.o(i.nrf_balloc_alloc) for nrf_balloc_alloc
    nrf_memobj.o(i.nrf_memobj_alloc) refers to nrf_memobj.o(i.nrf_memobj_free) for nrf_memobj_free
    nrf_memobj.o(i.nrf_memobj_free) refers to nrf_balloc.o(i.nrf_balloc_free) for nrf_balloc_free
    nrf_memobj.o(i.nrf_memobj_get) refers to nrf_atomic.o(i.nrf_atomic_u32_add) for nrf_atomic_u32_add
    nrf_memobj.o(i.nrf_memobj_pool_init) refers to nrf_balloc.o(i.nrf_balloc_init) for nrf_balloc_init
    nrf_memobj.o(i.nrf_memobj_put) refers to nrf_atomic.o(i.nrf_atomic_u32_sub) for nrf_atomic_u32_sub
    nrf_memobj.o(i.nrf_memobj_put) refers to nrf_memobj.o(i.nrf_memobj_free) for nrf_memobj_free
    nrf_memobj.o(i.nrf_memobj_read) refers to nrf_memobj.o(i.memobj_op) for memobj_op
    nrf_memobj.o(i.nrf_memobj_write) refers to nrf_memobj.o(i.memobj_op) for memobj_op
    nrf_pwr_mgmt.o(i.nrf_pwr_mgmt_init) refers to nrf_section_iter.o(i.nrf_section_iter_init) for nrf_section_iter_init
    nrf_pwr_mgmt.o(i.nrf_pwr_mgmt_init) refers to nrf_pwr_mgmt.o(.data) for .data
    nrf_pwr_mgmt.o(i.nrf_pwr_mgmt_init) refers to nrf_pwr_mgmt.o(.constdata) for .constdata
    nrf_pwr_mgmt.o(i.nrf_pwr_mgmt_init) refers to nrf_pwr_mgmt.o(.bss) for .bss
    nrf_pwr_mgmt.o(i.nrf_pwr_mgmt_run) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    nrf_pwr_mgmt.o(i.nrf_pwr_mgmt_run) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    nrf_pwr_mgmt.o(i.nrf_pwr_mgmt_run) refers to nrf_sdh.o(i.nrf_sdh_is_enabled) for nrf_sdh_is_enabled
    nrf_pwr_mgmt.o(i.nrf_pwr_mgmt_shutdown) refers to nrf_atomic.o(i.nrf_atomic_u32_fetch_store) for nrf_atomic_u32_fetch_store
    nrf_pwr_mgmt.o(i.nrf_pwr_mgmt_shutdown) refers to nrf_pwr_mgmt.o(i.shutdown_process) for shutdown_process
    nrf_pwr_mgmt.o(i.nrf_pwr_mgmt_shutdown) refers to nrf_pwr_mgmt.o(.data) for .data
    nrf_pwr_mgmt.o(i.shutdown_process) refers to nrf_section_iter.o(i.nrf_section_iter_next) for nrf_section_iter_next
    nrf_pwr_mgmt.o(i.shutdown_process) refers to nrf_log_frontend.o(i.nrf_log_panic) for nrf_log_panic
    nrf_pwr_mgmt.o(i.shutdown_process) refers to nrf_log_frontend.o(i.nrf_log_frontend_dequeue) for nrf_log_frontend_dequeue
    nrf_pwr_mgmt.o(i.shutdown_process) refers to nrf_sdh.o(i.nrf_sdh_is_enabled) for nrf_sdh_is_enabled
    nrf_pwr_mgmt.o(i.shutdown_process) refers to nrf_pwr_mgmt.o(.bss) for .bss
    nrf_pwr_mgmt.o(i.shutdown_process) refers to nrf_pwr_mgmt.o(.data) for .data
    nrf_pwr_mgmt.o(.constdata) refers to nrf_pwr_mgmt.o(.constdata) for pwr_mgmt_data_array
    nrf_pwr_mgmt.o(log_const_data) refers to nrf_pwr_mgmt.o(.conststrlit) for .conststrlit
    nrf_ringbuf.o(i.nrf_ringbuf_alloc) refers to nrf_atomic.o(i.nrf_atomic_flag_set_fetch) for nrf_atomic_flag_set_fetch
    nrf_ringbuf.o(i.nrf_ringbuf_alloc) refers to nrf_atomic.o(i.nrf_atomic_flag_clear) for nrf_atomic_flag_clear
    nrf_ringbuf.o(i.nrf_ringbuf_cpy_get) refers to nrf_atomic.o(i.nrf_atomic_flag_set_fetch) for nrf_atomic_flag_set_fetch
    nrf_ringbuf.o(i.nrf_ringbuf_cpy_get) refers to memcpya.o(.text) for __aeabi_memcpy
    nrf_ringbuf.o(i.nrf_ringbuf_cpy_get) refers to nrf_atomic.o(i.nrf_atomic_flag_clear) for nrf_atomic_flag_clear
    nrf_ringbuf.o(i.nrf_ringbuf_cpy_put) refers to nrf_atomic.o(i.nrf_atomic_flag_set_fetch) for nrf_atomic_flag_set_fetch
    nrf_ringbuf.o(i.nrf_ringbuf_cpy_put) refers to memcpya.o(.text) for __aeabi_memcpy
    nrf_ringbuf.o(i.nrf_ringbuf_cpy_put) refers to nrf_atomic.o(i.nrf_atomic_flag_clear) for nrf_atomic_flag_clear
    nrf_ringbuf.o(i.nrf_ringbuf_free) refers to nrf_atomic.o(i.nrf_atomic_flag_clear) for nrf_atomic_flag_clear
    nrf_ringbuf.o(i.nrf_ringbuf_get) refers to nrf_atomic.o(i.nrf_atomic_flag_set_fetch) for nrf_atomic_flag_set_fetch
    nrf_ringbuf.o(i.nrf_ringbuf_get) refers to nrf_atomic.o(i.nrf_atomic_flag_clear) for nrf_atomic_flag_clear
    nrf_ringbuf.o(i.nrf_ringbuf_put) refers to nrf_atomic.o(i.nrf_atomic_flag_clear_fetch) for nrf_atomic_flag_clear_fetch
    nrf_section_iter.o(i.nrf_section_iter_init) refers to nrf_section_iter.o(i.nrf_section_iter_item_set) for nrf_section_iter_item_set
    nrf_section_iter.o(i.nrf_section_iter_next) refers to nrf_section_iter.o(i.nrf_section_iter_item_set) for nrf_section_iter_item_set
    nrf_sortlist.o(log_const_data) refers to nrf_sortlist.o(.conststrlit) for .conststrlit
    nrf_strerror.o(i.nrf_strerror_find) refers to nrf_strerror.o(.constdata) for .constdata
    nrf_strerror.o(i.nrf_strerror_get) refers to nrf_strerror.o(i.nrf_strerror_find) for nrf_strerror_find
    nrf_strerror.o(i.nrf_strerror_get) refers to nrf_strerror.o(.constdata) for .constdata
    nrf_strerror.o(.constdata) refers to nrf_strerror.o(.conststring) for .conststring
    retarget.o(i.fgetc) refers to app_uart_fifo.o(i.app_uart_get) for app_uart_get
    retarget.o(i.fputc) refers to app_uart_fifo.o(i.app_uart_put) for app_uart_put
    nrf_log_backend_rtt.o(i.nrf_log_backend_rtt_init) refers to segger_rtt.o(i.SEGGER_RTT_Init) for SEGGER_RTT_Init
    nrf_log_backend_rtt.o(i.nrf_log_backend_rtt_put) refers to nrf_log_backend_serial.o(i.nrf_log_backend_serial_put) for nrf_log_backend_serial_put
    nrf_log_backend_rtt.o(i.nrf_log_backend_rtt_put) refers to nrf_log_backend_rtt.o(i.serial_tx) for serial_tx
    nrf_log_backend_rtt.o(i.nrf_log_backend_rtt_put) refers to nrf_log_backend_rtt.o(.bss) for .bss
    nrf_log_backend_rtt.o(i.serial_tx) refers to segger_rtt.o(i.SEGGER_RTT_WriteNoLock) for SEGGER_RTT_WriteNoLock
    nrf_log_backend_rtt.o(i.serial_tx) refers to nrf_log_backend_rtt.o(.data) for .data
    nrf_log_backend_rtt.o(i.serial_tx) refers to nrf_log_backend_rtt.o(.constdata) for .constdata
    nrf_log_backend_rtt.o(.constdata) refers to nrf_log_backend_rtt.o(i.nrf_log_backend_rtt_put) for nrf_log_backend_rtt_put
    nrf_log_backend_rtt.o(.constdata) refers to nrf_log_backend_rtt.o(i.nrf_log_backend_rtt_panic_set) for nrf_log_backend_rtt_panic_set
    nrf_log_backend_rtt.o(.constdata) refers to nrf_log_backend_rtt.o(i.nrf_log_backend_rtt_flush) for nrf_log_backend_rtt_flush
    nrf_log_backend_serial.o(i.nrf_log_backend_serial_put) refers to nrf_memobj.o(i.nrf_memobj_get) for nrf_memobj_get
    nrf_log_backend_serial.o(i.nrf_log_backend_serial_put) refers to memseta.o(.text) for __aeabi_memclr4
    nrf_log_backend_serial.o(i.nrf_log_backend_serial_put) refers to nrf_memobj.o(i.nrf_memobj_read) for nrf_memobj_read
    nrf_log_backend_serial.o(i.nrf_log_backend_serial_put) refers to nrf_log_str_formatter.o(i.nrf_log_std_entry_process) for nrf_log_std_entry_process
    nrf_log_backend_serial.o(i.nrf_log_backend_serial_put) refers to nrf_log_str_formatter.o(i.nrf_log_hexdump_entry_process) for nrf_log_hexdump_entry_process
    nrf_log_backend_serial.o(i.nrf_log_backend_serial_put) refers to nrf_memobj.o(i.nrf_memobj_put) for nrf_memobj_put
    nrf_log_default_backends.o(i.nrf_log_default_backends_init) refers to nrf_log_backend_rtt.o(i.nrf_log_backend_rtt_init) for nrf_log_backend_rtt_init
    nrf_log_default_backends.o(i.nrf_log_default_backends_init) refers to nrf_log_frontend.o(i.nrf_log_backend_add) for nrf_log_backend_add
    nrf_log_default_backends.o(i.nrf_log_default_backends_init) refers to nrf_log_default_backends.o(log_backends) for log_backends
    nrf_log_default_backends.o(log_backends) refers to nrf_log_backend_rtt.o(.constdata) for nrf_log_backend_rtt_api
    nrf_log_default_backends.o(log_backends) refers to nrf_log_default_backends.o(.conststrlit) for .conststrlit
    nrf_log_default_backends.o(log_backends) refers to nrf_log_default_backends.o(.data) for log_backend_cb_rtt_log_backend
    nrf_log_frontend.o(i.buf_prealloc) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    nrf_log_frontend.o(i.buf_prealloc) refers to nrf_atomic.o(i.nrf_atomic_u32_add) for nrf_atomic_u32_add
    nrf_log_frontend.o(i.buf_prealloc) refers to nrf_log_frontend.o(i.log_skip) for log_skip
    nrf_log_frontend.o(i.buf_prealloc) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    nrf_log_frontend.o(i.buf_prealloc) refers to nrf_log_frontend.o(.bss) for .bss
    nrf_log_frontend.o(i.buf_prealloc) refers to nrf_log_frontend.o(.data) for .data
    nrf_log_frontend.o(i.buffer_is_empty) refers to nrf_log_frontend.o(.bss) for .bss
    nrf_log_frontend.o(i.dropped_sat16_get) refers to nrf_atomic.o(i.nrf_atomic_u32_fetch_store) for nrf_atomic_u32_fetch_store
    nrf_log_frontend.o(i.dropped_sat16_get) refers to nrf_log_frontend.o(.bss) for .bss
    nrf_log_frontend.o(i.log_skip) refers to nrf_atomic.o(i.nrf_atomic_flag_set) for nrf_atomic_flag_set
    nrf_log_frontend.o(i.log_skip) refers to nrf_log_frontend.o(i.invalid_packets_omit) for invalid_packets_omit
    nrf_log_frontend.o(i.log_skip) refers to nrf_atomic.o(i.nrf_atomic_flag_clear_fetch) for nrf_atomic_flag_clear_fetch
    nrf_log_frontend.o(i.log_skip) refers to nrf_log_frontend.o(.bss) for .bss
    nrf_log_frontend.o(i.log_skip) refers to nrf_log_frontend.o(.data) for .data
    nrf_log_frontend.o(i.module_idx_get) refers to nrf_log_frontend.o(i.nrf_log_module_cnt_get) for nrf_log_module_cnt_get
    nrf_log_frontend.o(i.nrf_log_backend_add) refers to nrf_log_frontend.o(.bss) for .bss
    nrf_log_frontend.o(i.nrf_log_backend_remove) refers to nrf_log_frontend.o(.bss) for .bss
    nrf_log_frontend.o(i.nrf_log_frontend_dequeue) refers to nrf_log_frontend.o(i.buffer_is_empty) for buffer_is_empty
    nrf_log_frontend.o(i.nrf_log_frontend_dequeue) refers to nrf_log_frontend.o(i.invalid_packets_omit) for invalid_packets_omit
    nrf_log_frontend.o(i.nrf_log_frontend_dequeue) refers to nrf_memobj.o(i.nrf_memobj_alloc) for nrf_memobj_alloc
    nrf_log_frontend.o(i.nrf_log_frontend_dequeue) refers to nrf_memobj.o(i.nrf_memobj_get) for nrf_memobj_get
    nrf_log_frontend.o(i.nrf_log_frontend_dequeue) refers to nrf_memobj.o(i.nrf_memobj_write) for nrf_memobj_write
    nrf_log_frontend.o(i.nrf_log_frontend_dequeue) refers to nrf_memobj.o(i.nrf_memobj_put) for nrf_memobj_put
    nrf_log_frontend.o(i.nrf_log_frontend_dequeue) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    nrf_log_frontend.o(i.nrf_log_frontend_dequeue) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    nrf_log_frontend.o(i.nrf_log_frontend_dequeue) refers to nrf_log_frontend.o(i.nrf_log_frontend_std_0) for nrf_log_frontend_std_0
    nrf_log_frontend.o(i.nrf_log_frontend_dequeue) refers to nrf_log_frontend.o(.bss) for .bss
    nrf_log_frontend.o(i.nrf_log_frontend_dequeue) refers to nrf_log_frontend.o(.data) for .data
    nrf_log_frontend.o(i.nrf_log_frontend_dequeue) refers to nrf_log_frontend.o(nrf_balloc) for nrf_balloc
    nrf_log_frontend.o(i.nrf_log_frontend_dequeue) refers to nrf_log_frontend.o(log_const_data) for log_const_data
    nrf_log_frontend.o(i.nrf_log_frontend_hexdump) refers to nrf_log_frontend.o(i.buf_prealloc) for buf_prealloc
    nrf_log_frontend.o(i.nrf_log_frontend_hexdump) refers to memcpya.o(.text) for __aeabi_memcpy
    nrf_log_frontend.o(i.nrf_log_frontend_hexdump) refers to nrf_log_frontend.o(i.dropped_sat16_get) for dropped_sat16_get
    nrf_log_frontend.o(i.nrf_log_frontend_hexdump) refers to nrf_log_frontend.o(i.nrf_log_frontend_dequeue) for nrf_log_frontend_dequeue
    nrf_log_frontend.o(i.nrf_log_frontend_hexdump) refers to nrf_log_frontend.o(.data) for .data
    nrf_log_frontend.o(i.nrf_log_frontend_hexdump) refers to nrf_log_frontend.o(.bss) for .bss
    nrf_log_frontend.o(i.nrf_log_frontend_std_0) refers to nrf_log_frontend.o(i.std_n) for std_n
    nrf_log_frontend.o(i.nrf_log_frontend_std_1) refers to nrf_log_frontend.o(i.std_n) for std_n
    nrf_log_frontend.o(i.nrf_log_frontend_std_2) refers to nrf_log_frontend.o(i.std_n) for std_n
    nrf_log_frontend.o(i.nrf_log_frontend_std_3) refers to nrf_log_frontend.o(i.std_n) for std_n
    nrf_log_frontend.o(i.nrf_log_frontend_std_4) refers to nrf_log_frontend.o(i.std_n) for std_n
    nrf_log_frontend.o(i.nrf_log_frontend_std_5) refers to nrf_log_frontend.o(i.std_n) for std_n
    nrf_log_frontend.o(i.nrf_log_frontend_std_6) refers to nrf_log_frontend.o(i.std_n) for std_n
    nrf_log_frontend.o(i.nrf_log_init) refers to nrf_memobj.o(i.nrf_memobj_pool_init) for nrf_memobj_pool_init
    nrf_log_frontend.o(i.nrf_log_init) refers to nrf_ringbuf.o(i.nrf_ringbuf_init) for nrf_ringbuf_init
    nrf_log_frontend.o(i.nrf_log_init) refers to nrf_log_frontend.o(.bss) for .bss
    nrf_log_frontend.o(i.nrf_log_init) refers to nrf_log_frontend.o(nrf_balloc) for nrf_balloc
    nrf_log_frontend.o(i.nrf_log_init) refers to nrf_log_frontend.o(.constdata) for .constdata
    nrf_log_frontend.o(i.nrf_log_module_filter_get) refers to nrf_log_frontend.o(i.module_idx_get) for module_idx_get
    nrf_log_frontend.o(i.nrf_log_module_name_get) refers to nrf_log_frontend.o(i.module_idx_get) for module_idx_get
    nrf_log_frontend.o(i.nrf_log_panic) refers to nrf_log_frontend.o(.bss) for .bss
    nrf_log_frontend.o(i.nrf_log_push) refers to strlen.o(.text) for strlen
    nrf_log_frontend.o(i.nrf_log_push) refers to nrf_ringbuf.o(i.nrf_ringbuf_alloc) for nrf_ringbuf_alloc
    nrf_log_frontend.o(i.nrf_log_push) refers to memcpya.o(.text) for __aeabi_memcpy
    nrf_log_frontend.o(i.nrf_log_push) refers to nrf_ringbuf.o(i.nrf_ringbuf_put) for nrf_ringbuf_put
    nrf_log_frontend.o(i.nrf_log_push) refers to nrf_ringbuf.o(i.nrf_ringbuf_free) for nrf_ringbuf_free
    nrf_log_frontend.o(i.nrf_log_push) refers to nrf_log_frontend.o(.bss) for .bss
    nrf_log_frontend.o(i.nrf_log_push) refers to nrf_log_frontend.o(.constdata) for .constdata
    nrf_log_frontend.o(i.std_n) refers to nrf_log_frontend.o(i.buf_prealloc) for buf_prealloc
    nrf_log_frontend.o(i.std_n) refers to nrf_log_frontend.o(i.dropped_sat16_get) for dropped_sat16_get
    nrf_log_frontend.o(i.std_n) refers to nrf_log_frontend.o(i.nrf_log_frontend_dequeue) for nrf_log_frontend_dequeue
    nrf_log_frontend.o(i.std_n) refers to nrf_log_frontend.o(.data) for .data
    nrf_log_frontend.o(i.std_n) refers to nrf_log_frontend.o(.bss) for .bss
    nrf_log_frontend.o(.constdata) refers to nrf_log_frontend.o(.bss) for m_log_push_ringbuf_buf
    nrf_log_frontend.o(.constdata) refers to nrf_log_frontend.o(.bss) for m_log_push_ringbuf_cb
    nrf_log_frontend.o(log_const_data) refers to nrf_log_frontend.o(.conststrlit) for .conststrlit
    nrf_log_frontend.o(nrf_balloc) refers to nrf_log_frontend.o(.data) for log_mempool_nrf_balloc_cb
    nrf_log_frontend.o(nrf_balloc) refers to nrf_log_frontend.o(.data) for log_mempool_nrf_balloc_pool_stack
    nrf_log_frontend.o(nrf_balloc) refers to nrf_log_frontend.o(.bss) for log_mempool_nrf_balloc_pool_mem
    nrf_log_str_formatter.o(i.nrf_log_hexdump_entry_process) refers to nrf_log_str_formatter.o(i.prefix_process) for prefix_process
    nrf_log_str_formatter.o(i.nrf_log_hexdump_entry_process) refers to nrf_fprintf.o(i.nrf_fprintf) for nrf_fprintf
    nrf_log_str_formatter.o(i.nrf_log_hexdump_entry_process) refers to ctype_o.o(.text) for __rt_ctype_table
    nrf_log_str_formatter.o(i.nrf_log_hexdump_entry_process) refers to nrf_log_str_formatter.o(i.postfix_process) for postfix_process
    nrf_log_str_formatter.o(i.nrf_log_std_entry_process) refers to nrf_log_str_formatter.o(i.prefix_process) for prefix_process
    nrf_log_str_formatter.o(i.nrf_log_std_entry_process) refers to nrf_fprintf.o(i.nrf_fprintf) for nrf_fprintf
    nrf_log_str_formatter.o(i.nrf_log_std_entry_process) refers to nrf_log_str_formatter.o(i.postfix_process) for postfix_process
    nrf_log_str_formatter.o(i.nrf_log_str_formatter_timestamp_freq_set) refers to nrf_log_str_formatter.o(.data) for .data
    nrf_log_str_formatter.o(i.postfix_process) refers to nrf_fprintf.o(i.nrf_fprintf) for nrf_fprintf
    nrf_log_str_formatter.o(i.postfix_process) refers to nrf_fprintf.o(i.nrf_fprintf_buffer_flush) for nrf_fprintf_buffer_flush
    nrf_log_str_formatter.o(i.postfix_process) refers to nrf_log_str_formatter.o(.data) for .data
    nrf_log_str_formatter.o(i.prefix_process) refers to nrf_fprintf.o(i.nrf_fprintf) for nrf_fprintf
    nrf_log_str_formatter.o(i.prefix_process) refers to nrf_log_frontend.o(i.nrf_log_color_id_get) for nrf_log_color_id_get
    nrf_log_str_formatter.o(i.prefix_process) refers to nrf_log_frontend.o(i.nrf_log_module_name_get) for nrf_log_module_name_get
    nrf_log_str_formatter.o(i.prefix_process) refers to nrf_log_str_formatter.o(.data) for .data
    nrf_log_str_formatter.o(.data) refers to nrf_log_str_formatter.o(.conststring) for .conststring
    segger_rtt.o(i.SEGGER_RTT_AllocDownBuffer) refers to segger_rtt.o(i._DoInit) for _DoInit
    segger_rtt.o(i.SEGGER_RTT_AllocDownBuffer) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    segger_rtt.o(i.SEGGER_RTT_AllocDownBuffer) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    segger_rtt.o(i.SEGGER_RTT_AllocDownBuffer) refers to segger_rtt.o(.bss) for .bss
    segger_rtt.o(i.SEGGER_RTT_AllocUpBuffer) refers to segger_rtt.o(i._DoInit) for _DoInit
    segger_rtt.o(i.SEGGER_RTT_AllocUpBuffer) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    segger_rtt.o(i.SEGGER_RTT_AllocUpBuffer) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    segger_rtt.o(i.SEGGER_RTT_AllocUpBuffer) refers to segger_rtt.o(.bss) for .bss
    segger_rtt.o(i.SEGGER_RTT_ConfigDownBuffer) refers to segger_rtt.o(i._DoInit) for _DoInit
    segger_rtt.o(i.SEGGER_RTT_ConfigDownBuffer) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    segger_rtt.o(i.SEGGER_RTT_ConfigDownBuffer) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    segger_rtt.o(i.SEGGER_RTT_ConfigDownBuffer) refers to segger_rtt.o(.bss) for .bss
    segger_rtt.o(i.SEGGER_RTT_ConfigUpBuffer) refers to segger_rtt.o(i._DoInit) for _DoInit
    segger_rtt.o(i.SEGGER_RTT_ConfigUpBuffer) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    segger_rtt.o(i.SEGGER_RTT_ConfigUpBuffer) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    segger_rtt.o(i.SEGGER_RTT_ConfigUpBuffer) refers to segger_rtt.o(.bss) for .bss
    segger_rtt.o(i.SEGGER_RTT_GetKey) refers to segger_rtt.o(i.SEGGER_RTT_Read) for SEGGER_RTT_Read
    segger_rtt.o(i.SEGGER_RTT_HasData) refers to segger_rtt.o(.bss) for .bss
    segger_rtt.o(i.SEGGER_RTT_HasKey) refers to segger_rtt.o(i._DoInit) for _DoInit
    segger_rtt.o(i.SEGGER_RTT_HasKey) refers to segger_rtt.o(.bss) for .bss
    segger_rtt.o(i.SEGGER_RTT_Init) refers to segger_rtt.o(i._DoInit) for _DoInit
    segger_rtt.o(i.SEGGER_RTT_PutChar) refers to segger_rtt.o(i._DoInit) for _DoInit
    segger_rtt.o(i.SEGGER_RTT_PutChar) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    segger_rtt.o(i.SEGGER_RTT_PutChar) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    segger_rtt.o(i.SEGGER_RTT_PutChar) refers to segger_rtt.o(.bss) for .bss
    segger_rtt.o(i.SEGGER_RTT_PutCharSkip) refers to segger_rtt.o(i._DoInit) for _DoInit
    segger_rtt.o(i.SEGGER_RTT_PutCharSkip) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    segger_rtt.o(i.SEGGER_RTT_PutCharSkip) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    segger_rtt.o(i.SEGGER_RTT_PutCharSkip) refers to segger_rtt.o(.bss) for .bss
    segger_rtt.o(i.SEGGER_RTT_PutCharSkipNoLock) refers to segger_rtt.o(.bss) for .bss
    segger_rtt.o(i.SEGGER_RTT_Read) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    segger_rtt.o(i.SEGGER_RTT_Read) refers to segger_rtt.o(i.SEGGER_RTT_ReadNoLock) for SEGGER_RTT_ReadNoLock
    segger_rtt.o(i.SEGGER_RTT_Read) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    segger_rtt.o(i.SEGGER_RTT_ReadNoLock) refers to segger_rtt.o(i._DoInit) for _DoInit
    segger_rtt.o(i.SEGGER_RTT_ReadNoLock) refers to memcpya.o(.text) for __aeabi_memcpy
    segger_rtt.o(i.SEGGER_RTT_ReadNoLock) refers to segger_rtt.o(.bss) for .bss
    segger_rtt.o(i.SEGGER_RTT_SetFlagsDownBuffer) refers to segger_rtt.o(i._DoInit) for _DoInit
    segger_rtt.o(i.SEGGER_RTT_SetFlagsDownBuffer) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    segger_rtt.o(i.SEGGER_RTT_SetFlagsDownBuffer) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    segger_rtt.o(i.SEGGER_RTT_SetFlagsDownBuffer) refers to segger_rtt.o(.bss) for .bss
    segger_rtt.o(i.SEGGER_RTT_SetFlagsUpBuffer) refers to segger_rtt.o(i._DoInit) for _DoInit
    segger_rtt.o(i.SEGGER_RTT_SetFlagsUpBuffer) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    segger_rtt.o(i.SEGGER_RTT_SetFlagsUpBuffer) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    segger_rtt.o(i.SEGGER_RTT_SetFlagsUpBuffer) refers to segger_rtt.o(.bss) for .bss
    segger_rtt.o(i.SEGGER_RTT_SetNameDownBuffer) refers to segger_rtt.o(i._DoInit) for _DoInit
    segger_rtt.o(i.SEGGER_RTT_SetNameDownBuffer) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    segger_rtt.o(i.SEGGER_RTT_SetNameDownBuffer) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    segger_rtt.o(i.SEGGER_RTT_SetNameDownBuffer) refers to segger_rtt.o(.bss) for .bss
    segger_rtt.o(i.SEGGER_RTT_SetNameUpBuffer) refers to segger_rtt.o(i._DoInit) for _DoInit
    segger_rtt.o(i.SEGGER_RTT_SetNameUpBuffer) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    segger_rtt.o(i.SEGGER_RTT_SetNameUpBuffer) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    segger_rtt.o(i.SEGGER_RTT_SetNameUpBuffer) refers to segger_rtt.o(.bss) for .bss
    segger_rtt.o(i.SEGGER_RTT_SetTerminal) refers to segger_rtt.o(i._DoInit) for _DoInit
    segger_rtt.o(i.SEGGER_RTT_SetTerminal) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    segger_rtt.o(i.SEGGER_RTT_SetTerminal) refers to segger_rtt.o(i._GetAvailWriteSpace) for _GetAvailWriteSpace
    segger_rtt.o(i.SEGGER_RTT_SetTerminal) refers to segger_rtt.o(i._WriteNoCheck) for _WriteNoCheck
    segger_rtt.o(i.SEGGER_RTT_SetTerminal) refers to segger_rtt.o(i._WriteBlocking) for _WriteBlocking
    segger_rtt.o(i.SEGGER_RTT_SetTerminal) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    segger_rtt.o(i.SEGGER_RTT_SetTerminal) refers to segger_rtt.o(.bss) for .bss
    segger_rtt.o(i.SEGGER_RTT_SetTerminal) refers to segger_rtt.o(.data) for .data
    segger_rtt.o(i.SEGGER_RTT_TerminalOut) refers to segger_rtt.o(i._DoInit) for _DoInit
    segger_rtt.o(i.SEGGER_RTT_TerminalOut) refers to strlen.o(.text) for strlen
    segger_rtt.o(i.SEGGER_RTT_TerminalOut) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    segger_rtt.o(i.SEGGER_RTT_TerminalOut) refers to segger_rtt.o(i._GetAvailWriteSpace) for _GetAvailWriteSpace
    segger_rtt.o(i.SEGGER_RTT_TerminalOut) refers to segger_rtt.o(i._PostTerminalSwitch) for _PostTerminalSwitch
    segger_rtt.o(i.SEGGER_RTT_TerminalOut) refers to segger_rtt.o(i._WriteBlocking) for _WriteBlocking
    segger_rtt.o(i.SEGGER_RTT_TerminalOut) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    segger_rtt.o(i.SEGGER_RTT_TerminalOut) refers to segger_rtt.o(.bss) for .bss
    segger_rtt.o(i.SEGGER_RTT_TerminalOut) refers to segger_rtt.o(.data) for .data
    segger_rtt.o(i.SEGGER_RTT_WaitKey) refers to segger_rtt.o(i.SEGGER_RTT_GetKey) for SEGGER_RTT_GetKey
    segger_rtt.o(i.SEGGER_RTT_Write) refers to segger_rtt.o(i._DoInit) for _DoInit
    segger_rtt.o(i.SEGGER_RTT_Write) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    segger_rtt.o(i.SEGGER_RTT_Write) refers to segger_rtt.o(i.SEGGER_RTT_WriteNoLock) for SEGGER_RTT_WriteNoLock
    segger_rtt.o(i.SEGGER_RTT_Write) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    segger_rtt.o(i.SEGGER_RTT_Write) refers to segger_rtt.o(.bss) for .bss
    segger_rtt.o(i.SEGGER_RTT_WriteNoLock) refers to segger_rtt.o(i._GetAvailWriteSpace) for _GetAvailWriteSpace
    segger_rtt.o(i.SEGGER_RTT_WriteNoLock) refers to segger_rtt.o(i._WriteNoCheck) for _WriteNoCheck
    segger_rtt.o(i.SEGGER_RTT_WriteNoLock) refers to segger_rtt.o(i._WriteBlocking) for _WriteBlocking
    segger_rtt.o(i.SEGGER_RTT_WriteNoLock) refers to segger_rtt.o(.bss) for .bss
    segger_rtt.o(i.SEGGER_RTT_WriteSkipNoLock) refers to memcpya.o(.text) for __aeabi_memcpy
    segger_rtt.o(i.SEGGER_RTT_WriteSkipNoLock) refers to segger_rtt.o(.bss) for .bss
    segger_rtt.o(i.SEGGER_RTT_WriteString) refers to strlen.o(.text) for strlen
    segger_rtt.o(i.SEGGER_RTT_WriteString) refers to segger_rtt.o(i.SEGGER_RTT_Write) for SEGGER_RTT_Write
    segger_rtt.o(i.SEGGER_RTT_WriteWithOverwriteNoLock) refers to memcpya.o(.text) for __aeabi_memcpy
    segger_rtt.o(i.SEGGER_RTT_WriteWithOverwriteNoLock) refers to segger_rtt.o(.bss) for .bss
    segger_rtt.o(i._DoInit) refers to strcpy.o(.text) for strcpy
    segger_rtt.o(i._DoInit) refers to segger_rtt.o(.bss) for .bss
    segger_rtt.o(i._PostTerminalSwitch) refers to segger_rtt.o(i._WriteBlocking) for _WriteBlocking
    segger_rtt.o(i._PostTerminalSwitch) refers to segger_rtt.o(.data) for .data
    segger_rtt.o(i._WriteBlocking) refers to memcpya.o(.text) for __aeabi_memcpy
    segger_rtt.o(i._WriteNoCheck) refers to memcpya.o(.text) for __aeabi_memcpy
    segger_rtt_printf.o(i.SEGGER_RTT_printf) refers to segger_rtt_printf.o(i.SEGGER_RTT_vprintf) for SEGGER_RTT_vprintf
    segger_rtt_printf.o(i.SEGGER_RTT_vprintf) refers to segger_rtt_printf.o(i._StoreChar) for _StoreChar
    segger_rtt_printf.o(i.SEGGER_RTT_vprintf) refers to segger_rtt_printf.o(i._PrintInt) for _PrintInt
    segger_rtt_printf.o(i.SEGGER_RTT_vprintf) refers to segger_rtt_printf.o(i._PrintUnsigned) for _PrintUnsigned
    segger_rtt_printf.o(i.SEGGER_RTT_vprintf) refers to segger_rtt.o(i.SEGGER_RTT_Write) for SEGGER_RTT_Write
    segger_rtt_printf.o(i._PrintInt) refers to segger_rtt_printf.o(i._StoreChar) for _StoreChar
    segger_rtt_printf.o(i._PrintInt) refers to segger_rtt_printf.o(i._PrintUnsigned) for _PrintUnsigned
    segger_rtt_printf.o(i._PrintUnsigned) refers to segger_rtt_printf.o(i._StoreChar) for _StoreChar
    segger_rtt_printf.o(i._PrintUnsigned) refers to segger_rtt_printf.o(.constdata) for .constdata
    segger_rtt_printf.o(i._StoreChar) refers to segger_rtt.o(i.SEGGER_RTT_Write) for SEGGER_RTT_Write
    nrf_sdh.o(i.SWI2_EGU2_IRQHandler) refers to nrf_sdh.o(i.nrf_sdh_evts_poll) for nrf_sdh_evts_poll
    nrf_sdh.o(i.nrf_sdh_disable_request) refers to nrf_sdh.o(i.sdh_request_observer_notify) for sdh_request_observer_notify
    nrf_sdh.o(i.nrf_sdh_disable_request) refers to nrf_sdh.o(i.sdh_state_observer_notify) for sdh_state_observer_notify
    nrf_sdh.o(i.nrf_sdh_disable_request) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    nrf_sdh.o(i.nrf_sdh_disable_request) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    nrf_sdh.o(i.nrf_sdh_disable_request) refers to nrf_sdh.o(i.softdevice_evt_irq_disable) for softdevice_evt_irq_disable
    nrf_sdh.o(i.nrf_sdh_disable_request) refers to nrf_sdh.o(.data) for .data
    nrf_sdh.o(i.nrf_sdh_enable_request) refers to nrf_sdh.o(i.sdh_request_observer_notify) for sdh_request_observer_notify
    nrf_sdh.o(i.nrf_sdh_enable_request) refers to nrf_sdh.o(i.sdh_state_observer_notify) for sdh_state_observer_notify
    nrf_sdh.o(i.nrf_sdh_enable_request) refers to app_util_platform.o(i.app_util_critical_region_enter) for app_util_critical_region_enter
    nrf_sdh.o(i.nrf_sdh_enable_request) refers to app_util_platform.o(i.app_util_critical_region_exit) for app_util_critical_region_exit
    nrf_sdh.o(i.nrf_sdh_enable_request) refers to nrf_sdh.o(i.softdevices_evt_irq_enable) for softdevices_evt_irq_enable
    nrf_sdh.o(i.nrf_sdh_enable_request) refers to nrf_sdh.o(.data) for .data
    nrf_sdh.o(i.nrf_sdh_enable_request) refers to nrf_sdh.o(.constdata) for .constdata
    nrf_sdh.o(i.nrf_sdh_enable_request) refers to app_error_weak.o(i.app_error_fault_handler) for app_error_fault_handler
    nrf_sdh.o(i.nrf_sdh_evts_poll) refers to nrf_section_iter.o(i.nrf_section_iter_init) for nrf_section_iter_init
    nrf_sdh.o(i.nrf_sdh_evts_poll) refers to nrf_section_iter.o(i.nrf_section_iter_next) for nrf_section_iter_next
    nrf_sdh.o(i.nrf_sdh_evts_poll) refers to nrf_sdh.o(.constdata) for .constdata
    nrf_sdh.o(i.nrf_sdh_is_enabled) refers to nrf_sdh.o(.data) for .data
    nrf_sdh.o(i.nrf_sdh_is_suspended) refers to nrf_sdh.o(.data) for .data
    nrf_sdh.o(i.nrf_sdh_request_continue) refers to nrf_sdh.o(i.nrf_sdh_disable_request) for nrf_sdh_disable_request
    nrf_sdh.o(i.nrf_sdh_request_continue) refers to nrf_sdh.o(i.nrf_sdh_enable_request) for nrf_sdh_enable_request
    nrf_sdh.o(i.nrf_sdh_request_continue) refers to nrf_sdh.o(.data) for .data
    nrf_sdh.o(i.nrf_sdh_resume) refers to nrf_sdh.o(i.__sd_nvic_app_accessible_irq) for __sd_nvic_app_accessible_irq
    nrf_sdh.o(i.nrf_sdh_resume) refers to app_error.o(i.app_error_handler_bare) for app_error_handler_bare
    nrf_sdh.o(i.nrf_sdh_resume) refers to nrf_sdh.o(i.softdevices_evt_irq_enable) for softdevices_evt_irq_enable
    nrf_sdh.o(i.nrf_sdh_resume) refers to nrf_sdh.o(.data) for .data
    nrf_sdh.o(i.nrf_sdh_suspend) refers to nrf_sdh.o(i.softdevice_evt_irq_disable) for softdevice_evt_irq_disable
    nrf_sdh.o(i.nrf_sdh_suspend) refers to nrf_sdh.o(.data) for .data
    nrf_sdh.o(i.sdh_request_observer_notify) refers to nrf_section_iter.o(i.nrf_section_iter_init) for nrf_section_iter_init
    nrf_sdh.o(i.sdh_request_observer_notify) refers to nrf_section_iter.o(i.nrf_section_iter_next) for nrf_section_iter_next
    nrf_sdh.o(i.sdh_request_observer_notify) refers to nrf_sdh.o(.constdata) for .constdata
    nrf_sdh.o(i.sdh_state_observer_notify) refers to nrf_section_iter.o(i.nrf_section_iter_init) for nrf_section_iter_init
    nrf_sdh.o(i.sdh_state_observer_notify) refers to nrf_section_iter.o(i.nrf_section_iter_next) for nrf_section_iter_next
    nrf_sdh.o(i.sdh_state_observer_notify) refers to nrf_sdh.o(.constdata) for .constdata
    nrf_sdh.o(i.softdevice_evt_irq_disable) refers to nrf_sdh.o(i.__sd_nvic_app_accessible_irq) for __sd_nvic_app_accessible_irq
    nrf_sdh.o(i.softdevice_evt_irq_disable) refers to app_error.o(i.app_error_handler_bare) for app_error_handler_bare
    nrf_sdh.o(i.softdevice_evt_irq_disable) refers to app_util_platform.o(.bss) for nrf_nvic_state
    nrf_sdh.o(i.softdevices_evt_irq_enable) refers to nrf_sdh.o(i.__sd_nvic_app_accessible_irq) for __sd_nvic_app_accessible_irq
    nrf_sdh.o(i.softdevices_evt_irq_enable) refers to app_error.o(i.app_error_handler_bare) for app_error_handler_bare
    nrf_sdh.o(i.softdevices_evt_irq_enable) refers to app_util_platform.o(.bss) for nrf_nvic_state
    nrf_sdh.o(.constdata) refers to nrf_sdh.o(.constdata) for sdh_req_observers_array
    nrf_sdh.o(.constdata) refers to nrf_sdh.o(.constdata) for sdh_state_observers_array
    nrf_sdh.o(.constdata) refers to nrf_sdh.o(.constdata) for sdh_stack_observers_array
    nrf_sdh.o(log_const_data) refers to nrf_sdh.o(.conststrlit) for .conststrlit
    nrf_sdh_ble.o(i.nrf_sdh_ble_app_ram_start_get) refers to nrf_sdh_ble.o(.constdata) for .constdata
    nrf_sdh_ble.o(i.nrf_sdh_ble_default_cfg_set) refers to nrf_sdh_ble.o(i.nrf_sdh_ble_app_ram_start_get) for nrf_sdh_ble_app_ram_start_get
    nrf_sdh_ble.o(i.nrf_sdh_ble_default_cfg_set) refers to nrf_strerror.o(i.nrf_strerror_get) for nrf_strerror_get
    nrf_sdh_ble.o(i.nrf_sdh_ble_default_cfg_set) refers to nrf_log_frontend.o(i.nrf_log_frontend_std_1) for nrf_log_frontend_std_1
    nrf_sdh_ble.o(i.nrf_sdh_ble_default_cfg_set) refers to nrf_sdh_ble.o(log_const_data) for log_const_data
    nrf_sdh_ble.o(i.nrf_sdh_ble_default_cfg_set) refers to nrf_sdh_ble.o(.conststring) for .conststring
    nrf_sdh_ble.o(i.nrf_sdh_ble_enable) refers to nrf_log_frontend.o(i.nrf_log_frontend_std_0) for nrf_log_frontend_std_0
    nrf_sdh_ble.o(i.nrf_sdh_ble_enable) refers to nrf_log_frontend.o(i.nrf_log_frontend_std_2) for nrf_log_frontend_std_2
    nrf_sdh_ble.o(i.nrf_sdh_ble_enable) refers to nrf_log_frontend.o(i.nrf_log_frontend_std_1) for nrf_log_frontend_std_1
    nrf_sdh_ble.o(i.nrf_sdh_ble_enable) refers to nrf_strerror.o(i.nrf_strerror_get) for nrf_strerror_get
    nrf_sdh_ble.o(i.nrf_sdh_ble_enable) refers to nrf_sdh_ble.o(log_const_data) for log_const_data
    nrf_sdh_ble.o(i.nrf_sdh_ble_enable) refers to nrf_sdh_ble.o(.data) for .data
    nrf_sdh_ble.o(i.nrf_sdh_ble_evts_poll) refers to app_error.o(i.app_error_handler_bare) for app_error_handler_bare
    nrf_sdh_ble.o(i.nrf_sdh_ble_evts_poll) refers to nrf_section_iter.o(i.nrf_section_iter_init) for nrf_section_iter_init
    nrf_sdh_ble.o(i.nrf_sdh_ble_evts_poll) refers to nrf_section_iter.o(i.nrf_section_iter_next) for nrf_section_iter_next
    nrf_sdh_ble.o(i.nrf_sdh_ble_evts_poll) refers to nrf_sdh_ble.o(.data) for .data
    nrf_sdh_ble.o(i.nrf_sdh_ble_evts_poll) refers to nrf_sdh_ble.o(.constdata) for .constdata
    nrf_sdh_ble.o(.constdata) refers to nrf_sdh_ble.o(.constdata) for sdh_ble_observers_array
    nrf_sdh_ble.o(log_const_data) refers to nrf_sdh_ble.o(.conststrlit) for .conststrlit
    nrf_sdh_ble.o(sdh_stack_observers0) refers to nrf_sdh_ble.o(i.nrf_sdh_ble_evts_poll) for nrf_sdh_ble_evts_poll
    nrf_sdh_soc.o(i.nrf_sdh_soc_evts_poll) refers to app_error.o(i.app_error_handler_bare) for app_error_handler_bare
    nrf_sdh_soc.o(i.nrf_sdh_soc_evts_poll) refers to nrf_section_iter.o(i.nrf_section_iter_init) for nrf_section_iter_init
    nrf_sdh_soc.o(i.nrf_sdh_soc_evts_poll) refers to nrf_section_iter.o(i.nrf_section_iter_next) for nrf_section_iter_next
    nrf_sdh_soc.o(i.nrf_sdh_soc_evts_poll) refers to nrf_sdh_soc.o(.constdata) for .constdata
    nrf_sdh_soc.o(.constdata) refers to nrf_sdh_soc.o(.constdata) for sdh_soc_observers_array
    nrf_sdh_soc.o(log_const_data) refers to nrf_sdh_soc.o(.conststrlit) for .conststrlit
    nrf_sdh_soc.o(sdh_stack_observers0) refers to nrf_sdh_soc.o(i.nrf_sdh_soc_evts_poll) for nrf_sdh_soc_evts_poll
    arm_startup_nrf52.o(RESET) refers to arm_startup_nrf52.o(STACK) for __initial_sp
    arm_startup_nrf52.o(RESET) refers to arm_startup_nrf52.o(.text) for Reset_Handler
    arm_startup_nrf52.o(RESET) refers to nrfx_clock.o(i.POWER_CLOCK_IRQHandler) for POWER_CLOCK_IRQHandler
    arm_startup_nrf52.o(RESET) refers to nrfx_prs.o(i.UARTE0_UART0_IRQHandler) for UARTE0_UART0_IRQHandler
    arm_startup_nrf52.o(RESET) refers to nrfx_spim.o(i.SPIM0_SPIS0_TWIM0_TWIS0_SPI0_TWI0_IRQHandler) for SPIM0_SPIS0_TWIM0_TWIS0_SPI0_TWI0_IRQHandler
    arm_startup_nrf52.o(RESET) refers to nrfx_gpiote.o(i.GPIOTE_IRQHandler) for GPIOTE_IRQHandler
    arm_startup_nrf52.o(RESET) refers to nrfx_saadc.o(i.SAADC_IRQHandler) for SAADC_IRQHandler
    arm_startup_nrf52.o(RESET) refers to nrfx_timer.o(i.TIMER1_IRQHandler) for TIMER1_IRQHandler
    arm_startup_nrf52.o(RESET) refers to drv_rtc.o(i.RTC1_IRQHandler) for RTC1_IRQHandler
    arm_startup_nrf52.o(RESET) refers to nrf_sdh.o(i.SWI2_EGU2_IRQHandler) for SWI2_EGU2_IRQHandler
    arm_startup_nrf52.o(.text) refers to system_nrf52.o(i.SystemInit) for SystemInit
    arm_startup_nrf52.o(.text) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    system_nrf52.o(i.SystemCoreClockUpdate) refers to system_nrf52.o(.data) for .data
    system_nrf52.o(i.SystemInit) refers to system_nrf52.o(.data) for .data
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry10a.o(.ARM.Collect$$$$0000000F) for __rt_final_cpp
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry11a.o(.ARM.Collect$$$$00000011) for __rt_final_exit
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry12b.o(.ARM.Collect$$$$0000000E) for __rt_lib_shutdown_fini
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry7b.o(.ARM.Collect$$$$00000008) for _main_clock
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry8b.o(.ARM.Collect$$$$0000000A) for _main_cpp_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry9a.o(.ARM.Collect$$$$0000000B) for _main_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry5.o(.ARM.Collect$$$$00000004) for _main_scatterload
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry2.o(.ARM.Collect$$$$00000001) for _main_stk
    uldiv.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    uldiv.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    ctype_o.o(.text) refers to ctype_o.o(.constdata) for .constdata
    ctype_o.o(.constdata) refers to ctype_o.o(.constdata) for __ctype_table
    isalnum_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isalpha_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isblank_o.o(.text) refers to ctype_o.o(.constdata) for __ctype_table
    iscntrl_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isdigit_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isgraph_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    islower_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isprint_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    ispunct_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isspace_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isupper_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isxdigit_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    printfb.o(i.__0fprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0fprintf$bare) refers to retarget.o(i.fputc) for fputc
    printfb.o(i.__0printf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0printf$bare) refers to retarget.o(i.fputc) for fputc
    printfb.o(i.__0printf$bare) refers to retarget.o(.data) for __stdout
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printfb.o(i.__0vfprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vfprintf$bare) refers to retarget.o(i.fputc) for fputc
    printfb.o(i.__0vprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vprintf$bare) refers to retarget.o(i.fputc) for fputc
    printfb.o(i.__0vprintf$bare) refers to retarget.o(.data) for __stdout
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printf0.o(i.__0fprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0fprintf$0) refers to retarget.o(i.fputc) for fputc
    printf0.o(i.__0printf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0printf$0) refers to retarget.o(i.fputc) for fputc
    printf0.o(i.__0printf$0) refers to retarget.o(.data) for __stdout
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf0.o(i.__0vfprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vfprintf$0) refers to retarget.o(i.fputc) for fputc
    printf0.o(i.__0vprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vprintf$0) refers to retarget.o(i.fputc) for fputc
    printf0.o(i.__0vprintf$0) refers to retarget.o(.data) for __stdout
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf1.o(i.__0fprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0fprintf$1) refers to retarget.o(i.fputc) for fputc
    printf1.o(i.__0printf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0printf$1) refers to retarget.o(i.fputc) for fputc
    printf1.o(i.__0printf$1) refers to retarget.o(.data) for __stdout
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i.__0vfprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vfprintf$1) refers to retarget.o(i.fputc) for fputc
    printf1.o(i.__0vprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vprintf$1) refers to retarget.o(i.fputc) for fputc
    printf1.o(i.__0vprintf$1) refers to retarget.o(.data) for __stdout
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf2.o(i.__0fprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0fprintf$2) refers to retarget.o(i.fputc) for fputc
    printf2.o(i.__0printf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0printf$2) refers to retarget.o(i.fputc) for fputc
    printf2.o(i.__0printf$2) refers to retarget.o(.data) for __stdout
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf2.o(i.__0vfprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vfprintf$2) refers to retarget.o(i.fputc) for fputc
    printf2.o(i.__0vprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vprintf$2) refers to retarget.o(i.fputc) for fputc
    printf2.o(i.__0vprintf$2) refers to retarget.o(.data) for __stdout
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf3.o(i.__0fprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0fprintf$3) refers to retarget.o(i.fputc) for fputc
    printf3.o(i.__0printf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0printf$3) refers to retarget.o(i.fputc) for fputc
    printf3.o(i.__0printf$3) refers to retarget.o(.data) for __stdout
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i.__0vfprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vfprintf$3) refers to retarget.o(i.fputc) for fputc
    printf3.o(i.__0vprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vprintf$3) refers to retarget.o(i.fputc) for fputc
    printf3.o(i.__0vprintf$3) refers to retarget.o(.data) for __stdout
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf4.o(i.__0fprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0fprintf$4) refers to retarget.o(i.fputc) for fputc
    printf4.o(i.__0printf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0printf$4) refers to retarget.o(i.fputc) for fputc
    printf4.o(i.__0printf$4) refers to retarget.o(.data) for __stdout
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i.__0vfprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vfprintf$4) refers to retarget.o(i.fputc) for fputc
    printf4.o(i.__0vprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vprintf$4) refers to retarget.o(i.fputc) for fputc
    printf4.o(i.__0vprintf$4) refers to retarget.o(.data) for __stdout
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf5.o(i.__0fprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0fprintf$5) refers to retarget.o(i.fputc) for fputc
    printf5.o(i.__0printf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0printf$5) refers to retarget.o(i.fputc) for fputc
    printf5.o(i.__0printf$5) refers to retarget.o(.data) for __stdout
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i.__0vfprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vfprintf$5) refers to retarget.o(i.fputc) for fputc
    printf5.o(i.__0vprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vprintf$5) refers to retarget.o(i.fputc) for fputc
    printf5.o(i.__0vprintf$5) refers to retarget.o(.data) for __stdout
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf6.o(i.__0fprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0fprintf$6) refers to retarget.o(i.fputc) for fputc
    printf6.o(i.__0printf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0printf$6) refers to retarget.o(i.fputc) for fputc
    printf6.o(i.__0printf$6) refers to retarget.o(.data) for __stdout
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i.__0vfprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vfprintf$6) refers to retarget.o(i.fputc) for fputc
    printf6.o(i.__0vprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vprintf$6) refers to retarget.o(i.fputc) for fputc
    printf6.o(i.__0vprintf$6) refers to retarget.o(.data) for __stdout
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i._printf_core) refers to printf6.o(i._printf_pre_padding) for _printf_pre_padding
    printf6.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf6.o(i._printf_core) refers to printf6.o(i._printf_post_padding) for _printf_post_padding
    printf7.o(i.__0fprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0fprintf$7) refers to retarget.o(i.fputc) for fputc
    printf7.o(i.__0printf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0printf$7) refers to retarget.o(i.fputc) for fputc
    printf7.o(i.__0printf$7) refers to retarget.o(.data) for __stdout
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i.__0vfprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vfprintf$7) refers to retarget.o(i.fputc) for fputc
    printf7.o(i.__0vprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vprintf$7) refers to retarget.o(i.fputc) for fputc
    printf7.o(i.__0vprintf$7) refers to retarget.o(.data) for __stdout
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i._printf_core) refers to printf7.o(i._printf_pre_padding) for _printf_pre_padding
    printf7.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf7.o(i._printf_core) refers to printf7.o(i._printf_post_padding) for _printf_post_padding
    printf8.o(i.__0fprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0fprintf$8) refers to retarget.o(i.fputc) for fputc
    printf8.o(i.__0printf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0printf$8) refers to retarget.o(i.fputc) for fputc
    printf8.o(i.__0printf$8) refers to retarget.o(.data) for __stdout
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i.__0vfprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vfprintf$8) refers to retarget.o(i.fputc) for fputc
    printf8.o(i.__0vprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vprintf$8) refers to retarget.o(i.fputc) for fputc
    printf8.o(i.__0vprintf$8) refers to retarget.o(.data) for __stdout
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i._printf_core) refers to printf8.o(i._printf_pre_padding) for _printf_pre_padding
    printf8.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf8.o(i._printf_core) refers to printf8.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i.__0fprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0fprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0fprintf) refers to retarget.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0printf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0printf) refers to retarget.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers to retarget.o(.data) for __stdout
    printfa.o(i.__0snprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0snprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0snprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0sprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0sprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0sprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i.__0vfprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vfprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vfprintf) refers to retarget.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vprintf) refers to retarget.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers to retarget.o(.data) for __stdout
    printfa.o(i.__0vsnprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0vsprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i._fp_digits) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._fp_digits) refers to dmul.o(.text) for __aeabi_dmul
    printfa.o(i._fp_digits) refers to ddiv.o(.text) for __aeabi_ddiv
    printfa.o(i._fp_digits) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    printfa.o(i._fp_digits) refers to dadd.o(.text) for __aeabi_dadd
    printfa.o(i._fp_digits) refers to dfixul.o(.text) for __aeabi_d2ulz
    printfa.o(i._fp_digits) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_core) refers to printfa.o(i._printf_pre_padding) for _printf_pre_padding
    printfa.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers to printfa.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i._printf_core) refers to printfa.o(i._fp_digits) for _fp_digits
    printfa.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printfa.o(i._printf_post_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_pre_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._snputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._sputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    entry2.o(.ARM.Collect$$$$00000001) refers to entry2.o(.ARM.Collect$$$$00002712) for __lit__00000000
    entry2.o(.ARM.Collect$$$$00002712) refers to arm_startup_nrf52.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to arm_startup_nrf52.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry5.o(.ARM.Collect$$$$00000004) refers to init.o(.text) for __scatterload
    entry9a.o(.ARM.Collect$$$$0000000B) refers to main.o(i.main) for main
    entry9b.o(.ARM.Collect$$$$0000000C) refers to main.o(i.main) for main
    dadd.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    dadd.o(.text) refers to llsshr.o(.text) for __aeabi_lasr
    dadd.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dmul.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ddiv.o(.text) refers to depilogue.o(.text) for _double_round
    dfixul.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dfixul.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    init.o(.text) refers to entry5.o(.ARM.Collect$$$$00000004) for __main_after_scatterload
    depilogue.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    depilogue.o(.text) refers to llushr.o(.text) for __aeabi_llsr


==============================================================================

Removing Unused input sections from the image.

    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing main.o(.rrx_text), (6 bytes).
    Removing main.o(i.assert_nrf_callback), (16 bytes).
    Removing main.o(i.bsp_event_handler), (76 bytes).
    Removing main.o(i.peripheral_init), (2 bytes).
    Removing main.o(i.saadc_sampling_event_enable), (28 bytes).
    Removing main.o(i.timer_handler), (2 bytes).
    Removing main.o(i.uart_event_handle), (120 bytes).
    Removing main.o(.bss), (24 bytes).
    Removing main.o(.constdata), (1 bytes).
    Removing main.o(.data), (2 bytes).
    Removing main.o(.data), (2 bytes).
    Removing main.o(.data), (1 bytes).
    Removing dht11.o(.rev16_text), (4 bytes).
    Removing dht11.o(.revsh_text), (4 bytes).
    Removing dht11.o(.rrx_text), (6 bytes).
    Removing dht11.o(i.dht_Check), (124 bytes).
    Removing dht11.o(i.dht_Init), (14 bytes).
    Removing dht11.o(i.dht_Read_Bit), (80 bytes).
    Removing dht11.o(i.dht_Read_Byte), (26 bytes).
    Removing dht11.o(i.dht_Read_Data), (80 bytes).
    Removing dht11.o(i.dht_Rst), (60 bytes).
    Removing dht11.o(i.nrfx_coredep_delay_us), (16 bytes).
    Removing dht11.o(.constdata), (6 bytes).
    Removing ads1292.o(.rev16_text), (4 bytes).
    Removing ads1292.o(.revsh_text), (4 bytes).
    Removing ads1292.o(.rrx_text), (6 bytes).
    Removing led.o(.rev16_text), (4 bytes).
    Removing led.o(.revsh_text), (4 bytes).
    Removing led.o(.rrx_text), (6 bytes).
    Removing led.o(i.LED1_Toggle), (6 bytes).
    Removing led.o(i.LED2_Toggle), (6 bytes).
    Removing led.o(i.LED3_Close), (14 bytes).
    Removing led.o(i.LED3_Open), (14 bytes).
    Removing led.o(i.LED3_Toggle), (6 bytes).
    Removing led.o(i.nrf_gpio_pin_toggle), (28 bytes).
    Removing boards.o(.rev16_text), (4 bytes).
    Removing boards.o(.revsh_text), (4 bytes).
    Removing boards.o(.rrx_text), (6 bytes).
    Removing boards.o(i.bsp_board_button_state_get), (32 bytes).
    Removing boards.o(i.bsp_board_init), (84 bytes).
    Removing boards.o(i.bsp_board_led_idx_to_pin), (12 bytes).
    Removing boards.o(i.bsp_board_pin_to_led_idx), (36 bytes).
    Removing boards.o(i.nrf_gpio_cfg), (36 bytes).
    Removing bsp.o(.rev16_text), (4 bytes).
    Removing bsp.o(.revsh_text), (4 bytes).
    Removing bsp.o(.rrx_text), (6 bytes).
    Removing bsp.o(i.alert_timer_handler), (6 bytes).
    Removing bsp.o(i.bsp_button_is_pressed), (12 bytes).
    Removing bsp.o(i.bsp_buttons_disable), (4 bytes).
    Removing bsp.o(i.bsp_buttons_enable), (4 bytes).
    Removing bsp.o(i.bsp_init), (148 bytes).
    Removing bsp.o(i.bsp_wakeup_button_disable), (6 bytes).
    Removing bsp.o(i.button_timer_handler), (8 bytes).
    Removing bsp.o(i.leds_timer_handler), (20 bytes).
    Removing bsp_btn_ble.o(.rev16_text), (4 bytes).
    Removing bsp_btn_ble.o(.revsh_text), (4 bytes).
    Removing bsp_btn_ble.o(.rrx_text), (6 bytes).
    Removing bsp_btn_ble.o(i.bsp_btn_ble_init), (56 bytes).
    Removing utf.o(i.utf16DecodeRune), (74 bytes).
    Removing utf.o(i.utf16EncodeRune), (64 bytes).
    Removing utf.o(i.utf16RuneCount), (60 bytes).
    Removing utf.o(i.utf16UTF8Count), (78 bytes).
    Removing utf.o(i.utf8DecodeRune), (174 bytes).
    Removing utf.o(i.utf8EncodeRune), (160 bytes).
    Removing utf.o(i.utf8RuneCount), (58 bytes).
    Removing utf.o(i.utf8UTF16Count), (76 bytes).
    Removing ble_advdata.o(.rev16_text), (4 bytes).
    Removing ble_advdata.o(.revsh_text), (4 bytes).
    Removing ble_advdata.o(.rrx_text), (6 bytes).
    Removing ble_advdata.o(i.ble_advdata_appearance_find), (52 bytes).
    Removing ble_advdata.o(i.ble_advdata_name_find), (68 bytes).
    Removing ble_advdata.o(i.ble_advdata_short_name_find), (74 bytes).
    Removing ble_advdata.o(i.ble_advdata_uuid_find), (182 bytes).
    Removing ble_advertising.o(.rev16_text), (4 bytes).
    Removing ble_advertising.o(.revsh_text), (4 bytes).
    Removing ble_advertising.o(.rrx_text), (6 bytes).
    Removing ble_advertising.o(i.ble_advertising_advdata_update), (160 bytes).
    Removing ble_advertising.o(i.ble_advertising_modes_config_set), (8 bytes).
    Removing ble_advertising.o(i.ble_advertising_peer_addr_reply), (38 bytes).
    Removing ble_advertising.o(i.ble_advertising_restart_without_whitelist), (58 bytes).
    Removing ble_advertising.o(i.ble_advertising_whitelist_reply), (36 bytes).
    Removing ble_conn_params.o(.rev16_text), (4 bytes).
    Removing ble_conn_params.o(.revsh_text), (4 bytes).
    Removing ble_conn_params.o(.rrx_text), (6 bytes).
    Removing ble_conn_params.o(i.ble_conn_params_change_conn_params), (64 bytes).
    Removing ble_conn_params.o(i.ble_conn_params_stop), (36 bytes).
    Removing ble_conn_state.o(.rev16_text), (4 bytes).
    Removing ble_conn_state.o(.revsh_text), (4 bytes).
    Removing ble_conn_state.o(.rrx_text), (6 bytes).
    Removing ble_conn_state.o(i.active_flag_count), (28 bytes).
    Removing ble_conn_state.o(i.bcs_internal_state_reset), (12 bytes).
    Removing ble_conn_state.o(i.ble_conn_state_central_conn_count), (28 bytes).
    Removing ble_conn_state.o(i.ble_conn_state_central_handles), (32 bytes).
    Removing ble_conn_state.o(i.ble_conn_state_conn_count), (12 bytes).
    Removing ble_conn_state.o(i.ble_conn_state_conn_handles), (12 bytes).
    Removing ble_conn_state.o(i.ble_conn_state_encrypted), (32 bytes).
    Removing ble_conn_state.o(i.ble_conn_state_for_each_connected), (20 bytes).
    Removing ble_conn_state.o(i.ble_conn_state_for_each_set_user_flag), (44 bytes).
    Removing ble_conn_state.o(i.ble_conn_state_init), (4 bytes).
    Removing ble_conn_state.o(i.ble_conn_state_lesc), (32 bytes).
    Removing ble_conn_state.o(i.ble_conn_state_mitm_protected), (32 bytes).
    Removing ble_conn_state.o(i.ble_conn_state_periph_handles), (36 bytes).
    Removing ble_conn_state.o(i.ble_conn_state_peripheral_conn_count), (32 bytes).
    Removing ble_conn_state.o(i.ble_conn_state_role), (36 bytes).
    Removing ble_conn_state.o(i.ble_conn_state_status), (36 bytes).
    Removing ble_conn_state.o(i.ble_conn_state_user_flag_acquire), (24 bytes).
    Removing ble_conn_state.o(i.ble_conn_state_user_flag_get), (48 bytes).
    Removing ble_conn_state.o(i.ble_conn_state_user_flag_set), (56 bytes).
    Removing ble_conn_state.o(i.for_each_set_flag), (48 bytes).
    Removing ble_conn_state.o(i.user_flag_is_acquired), (12 bytes).
    Removing ble_link_ctx_manager.o(.rev16_text), (4 bytes).
    Removing ble_link_ctx_manager.o(.revsh_text), (4 bytes).
    Removing ble_link_ctx_manager.o(.rrx_text), (6 bytes).
    Removing ble_srv_common.o(.rev16_text), (4 bytes).
    Removing ble_srv_common.o(.revsh_text), (4 bytes).
    Removing ble_srv_common.o(.rrx_text), (6 bytes).
    Removing ble_srv_common.o(i.ble_srv_ascii_to_utf8), (18 bytes).
    Removing ble_srv_common.o(i.ble_srv_is_indication_enabled), (8 bytes).
    Removing ble_srv_common.o(i.ble_srv_report_ref_encode), (12 bytes).
    Removing ble_srv_common.o(i.descriptor_add), (170 bytes).
    Removing nrf_ble_gatt.o(.rev16_text), (4 bytes).
    Removing nrf_ble_gatt.o(.revsh_text), (4 bytes).
    Removing nrf_ble_gatt.o(.rrx_text), (6 bytes).
    Removing nrf_ble_gatt.o(i.nrf_ble_gatt_att_mtu_central_set), (24 bytes).
    Removing nrf_ble_gatt.o(i.nrf_ble_gatt_data_length_get), (34 bytes).
    Removing nrf_ble_gatt.o(i.nrf_ble_gatt_data_length_set), (46 bytes).
    Removing nrf_ble_gatt.o(i.nrf_ble_gatt_eff_mtu_get), (12 bytes).
    Removing nrf_ble_qwr.o(.rev16_text), (4 bytes).
    Removing nrf_ble_qwr.o(.revsh_text), (4 bytes).
    Removing nrf_ble_qwr.o(.rrx_text), (6 bytes).
    Removing ble_nus.o(.rev16_text), (4 bytes).
    Removing ble_nus.o(.revsh_text), (4 bytes).
    Removing ble_nus.o(.rrx_text), (6 bytes).
    Removing nrf_drv_clock.o(.rev16_text), (4 bytes).
    Removing nrf_drv_clock.o(.revsh_text), (4 bytes).
    Removing nrf_drv_clock.o(.rrx_text), (6 bytes).
    Removing nrf_drv_clock.o(i.item_enqueue), (22 bytes).
    Removing nrf_drv_clock.o(i.nrf_drv_clock_calibration_abort), (4 bytes).
    Removing nrf_drv_clock.o(i.nrf_drv_clock_calibration_start), (4 bytes).
    Removing nrf_drv_clock.o(i.nrf_drv_clock_hfclk_is_running), (44 bytes).
    Removing nrf_drv_clock.o(i.nrf_drv_clock_hfclk_release), (56 bytes).
    Removing nrf_drv_clock.o(i.nrf_drv_clock_hfclk_request), (92 bytes).
    Removing nrf_drv_clock.o(i.nrf_drv_clock_init_check), (12 bytes).
    Removing nrf_drv_clock.o(i.nrf_drv_clock_is_calibrating), (4 bytes).
    Removing nrf_drv_clock.o(i.nrf_drv_clock_lfclk_is_running), (28 bytes).
    Removing nrf_drv_clock.o(i.nrf_drv_clock_lfclk_request), (84 bytes).
    Removing nrf_drv_clock.o(i.nrf_drv_clock_uninit), (24 bytes).
    Removing nrf_drv_clock.o(.constdata), (19 bytes).
    Removing nrf_drv_clock.o(.constdata), (32 bytes).
    Removing nrf_drv_clock.o(.constdata), (32 bytes).
    Removing nrf_drv_clock.o(.constdata), (29 bytes).
    Removing nrf_drv_uart.o(.rev16_text), (4 bytes).
    Removing nrf_drv_uart.o(.revsh_text), (4 bytes).
    Removing nrf_drv_uart.o(.rrx_text), (6 bytes).
    Removing nrf_drv_uart.o(i.nrf_drv_uart_init), (104 bytes).
    Removing nrf_drv_uart.o(i.uart_evt_handler), (48 bytes).
    Removing nrf_drv_uart.o(i.uarte_evt_handler), (48 bytes).
    Removing nrfx_atomic.o(.rev16_text), (4 bytes).
    Removing nrfx_atomic.o(.revsh_text), (4 bytes).
    Removing nrfx_atomic.o(.rrx_text), (6 bytes).
    Removing nrfx_atomic.o(.emb_text), (226 bytes).
    Removing nrfx_atomic.o(i.nrfx_atomic_flag_clear), (6 bytes).
    Removing nrfx_atomic.o(i.nrfx_atomic_flag_clear_fetch), (6 bytes).
    Removing nrfx_atomic.o(i.nrfx_atomic_flag_set), (6 bytes).
    Removing nrfx_atomic.o(i.nrfx_atomic_flag_set_fetch), (6 bytes).
    Removing nrfx_atomic.o(i.nrfx_atomic_u32_add), (12 bytes).
    Removing nrfx_atomic.o(i.nrfx_atomic_u32_and), (12 bytes).
    Removing nrfx_atomic.o(i.nrfx_atomic_u32_cmp_exch), (4 bytes).
    Removing nrfx_atomic.o(i.nrfx_atomic_u32_fetch_add), (10 bytes).
    Removing nrfx_atomic.o(i.nrfx_atomic_u32_fetch_and), (10 bytes).
    Removing nrfx_atomic.o(i.nrfx_atomic_u32_fetch_or), (10 bytes).
    Removing nrfx_atomic.o(i.nrfx_atomic_u32_fetch_store), (10 bytes).
    Removing nrfx_atomic.o(i.nrfx_atomic_u32_fetch_sub), (10 bytes).
    Removing nrfx_atomic.o(i.nrfx_atomic_u32_fetch_sub_hs), (10 bytes).
    Removing nrfx_atomic.o(i.nrfx_atomic_u32_fetch_xor), (10 bytes).
    Removing nrfx_atomic.o(i.nrfx_atomic_u32_or), (12 bytes).
    Removing nrfx_atomic.o(i.nrfx_atomic_u32_store), (12 bytes).
    Removing nrfx_atomic.o(i.nrfx_atomic_u32_sub), (12 bytes).
    Removing nrfx_atomic.o(i.nrfx_atomic_u32_sub_hs), (12 bytes).
    Removing nrfx_atomic.o(i.nrfx_atomic_u32_xor), (12 bytes).
    Removing nrfx_clock.o(.rev16_text), (4 bytes).
    Removing nrfx_clock.o(.revsh_text), (4 bytes).
    Removing nrfx_clock.o(.rrx_text), (6 bytes).
    Removing nrfx_clock.o(i.nrfx_clock_calibration_start), (4 bytes).
    Removing nrfx_clock.o(i.nrfx_clock_calibration_timer_start), (2 bytes).
    Removing nrfx_clock.o(i.nrfx_clock_calibration_timer_stop), (2 bytes).
    Removing nrfx_clock.o(i.nrfx_clock_disable), (30 bytes).
    Removing nrfx_clock.o(i.nrfx_clock_hfclk_start), (22 bytes).
    Removing nrfx_clock.o(i.nrfx_clock_hfclk_stop), (60 bytes).
    Removing nrfx_clock.o(i.nrfx_clock_is_calibrating), (4 bytes).
    Removing nrfx_clock.o(i.nrfx_clock_lfclk_start), (76 bytes).
    Removing nrfx_clock.o(i.nrfx_clock_uninit), (24 bytes).
    Removing nrfx_clock.o(.constdata), (16 bytes).
    Removing nrfx_clock.o(.constdata), (29 bytes).
    Removing nrfx_gpiote.o(.rev16_text), (4 bytes).
    Removing nrfx_gpiote.o(.revsh_text), (4 bytes).
    Removing nrfx_gpiote.o(.rrx_text), (6 bytes).
    Removing nrfx_gpiote.o(i.nrf_gpio_cfg_default), (18 bytes).
    Removing nrfx_gpiote.o(i.nrfx_gpiote_clr_task_addr_get), (16 bytes).
    Removing nrfx_gpiote.o(i.nrfx_gpiote_clr_task_get), (16 bytes).
    Removing nrfx_gpiote.o(i.nrfx_gpiote_clr_task_trigger), (28 bytes).
    Removing nrfx_gpiote.o(i.nrfx_gpiote_in_event_addr_get), (16 bytes).
    Removing nrfx_gpiote.o(i.nrfx_gpiote_in_event_disable), (72 bytes).
    Removing nrfx_gpiote.o(i.nrfx_gpiote_in_event_get), (34 bytes).
    Removing nrfx_gpiote.o(i.nrfx_gpiote_in_is_set), (20 bytes).
    Removing nrfx_gpiote.o(i.nrfx_gpiote_in_uninit), (88 bytes).
    Removing nrfx_gpiote.o(i.nrfx_gpiote_is_init), (20 bytes).
    Removing nrfx_gpiote.o(i.nrfx_gpiote_out_clear), (14 bytes).
    Removing nrfx_gpiote.o(i.nrfx_gpiote_out_init), (188 bytes).
    Removing nrfx_gpiote.o(i.nrfx_gpiote_out_set), (14 bytes).
    Removing nrfx_gpiote.o(i.nrfx_gpiote_out_task_addr_get), (16 bytes).
    Removing nrfx_gpiote.o(i.nrfx_gpiote_out_task_disable), (36 bytes).
    Removing nrfx_gpiote.o(i.nrfx_gpiote_out_task_enable), (36 bytes).
    Removing nrfx_gpiote.o(i.nrfx_gpiote_out_task_force), (40 bytes).
    Removing nrfx_gpiote.o(i.nrfx_gpiote_out_task_get), (12 bytes).
    Removing nrfx_gpiote.o(i.nrfx_gpiote_out_task_trigger), (24 bytes).
    Removing nrfx_gpiote.o(i.nrfx_gpiote_out_toggle), (28 bytes).
    Removing nrfx_gpiote.o(i.nrfx_gpiote_out_uninit), (88 bytes).
    Removing nrfx_gpiote.o(i.nrfx_gpiote_set_task_addr_get), (16 bytes).
    Removing nrfx_gpiote.o(i.nrfx_gpiote_set_task_get), (16 bytes).
    Removing nrfx_gpiote.o(i.nrfx_gpiote_set_task_trigger), (28 bytes).
    Removing nrfx_gpiote.o(i.nrfx_gpiote_uninit), (64 bytes).
    Removing nrfx_gpiote.o(i.pin_configured_check), (20 bytes).
    Removing nrfx_gpiote.o(i.pin_configured_clear), (28 bytes).
    Removing nrfx_gpiote.o(.constdata), (17 bytes).
    Removing nrfx_gpiote.o(.constdata), (21 bytes).
    Removing nrfx_gpiote.o(.constdata), (20 bytes).
    Removing nrfx_prs.o(.rev16_text), (4 bytes).
    Removing nrfx_prs.o(.revsh_text), (4 bytes).
    Removing nrfx_prs.o(.rrx_text), (6 bytes).
    Removing nrfx_prs.o(i.nrfx_prs_release), (18 bytes).
    Removing nrfx_prs.o(.constdata), (17 bytes).
    Removing nrfx_uart.o(.rev16_text), (4 bytes).
    Removing nrfx_uart.o(.revsh_text), (4 bytes).
    Removing nrfx_uart.o(.rrx_text), (6 bytes).
    Removing nrfx_uart.o(i.apply_config), (136 bytes).
    Removing nrfx_uart.o(i.nrf_gpio_cfg), (36 bytes).
    Removing nrfx_uart.o(i.nrf_gpio_cfg_default), (18 bytes).
    Removing nrfx_uart.o(i.nrf_gpio_cfg_input), (18 bytes).
    Removing nrfx_uart.o(i.nrf_gpio_cfg_output), (20 bytes).
    Removing nrfx_uart.o(i.nrf_uart_int_enable_check), (12 bytes).
    Removing nrfx_uart.o(i.nrfx_uart_0_irq_handler), (16 bytes).
    Removing nrfx_uart.o(i.nrfx_uart_errorsrc_get), (26 bytes).
    Removing nrfx_uart.o(i.nrfx_uart_init), (212 bytes).
    Removing nrfx_uart.o(i.nrfx_uart_rx), (236 bytes).
    Removing nrfx_uart.o(i.nrfx_uart_rx_abort), (18 bytes).
    Removing nrfx_uart.o(i.nrfx_uart_rx_disable), (36 bytes).
    Removing nrfx_uart.o(i.nrfx_uart_rx_enable), (60 bytes).
    Removing nrfx_uart.o(i.nrfx_uart_rx_ready), (14 bytes).
    Removing nrfx_uart.o(i.nrfx_uart_tx_abort), (48 bytes).
    Removing nrfx_uart.o(i.nrfx_uart_uninit), (192 bytes).
    Removing nrfx_uart.o(i.rx_byte), (50 bytes).
    Removing nrfx_uart.o(i.rx_done_event), (22 bytes).
    Removing nrfx_uart.o(i.rx_enable), (32 bytes).
    Removing nrfx_uart.o(i.tx_done_event), (26 bytes).
    Removing nrfx_uart.o(i.uart_irq_handler), (298 bytes).
    Removing nrfx_uart.o(.constdata), (15 bytes).
    Removing nrfx_uart.o(.constdata), (4 bytes).
    Removing nrfx_uart.o(.constdata), (13 bytes).
    Removing nrfx_uart.o(.constdata), (13 bytes).
    Removing nrfx_uarte.o(.rev16_text), (4 bytes).
    Removing nrfx_uarte.o(.revsh_text), (4 bytes).
    Removing nrfx_uarte.o(.rrx_text), (6 bytes).
    Removing nrfx_uarte.o(i.apply_config), (136 bytes).
    Removing nrfx_uarte.o(i.interrupts_enable), (136 bytes).
    Removing nrfx_uarte.o(i.nrf_gpio_cfg), (36 bytes).
    Removing nrfx_uarte.o(i.nrf_gpio_cfg_default), (18 bytes).
    Removing nrfx_uarte.o(i.nrf_gpio_cfg_input), (18 bytes).
    Removing nrfx_uarte.o(i.nrf_gpio_cfg_output), (20 bytes).
    Removing nrfx_uarte.o(i.nrfx_uarte_0_irq_handler), (16 bytes).
    Removing nrfx_uarte.o(i.nrfx_uarte_errorsrc_get), (26 bytes).
    Removing nrfx_uarte.o(i.nrfx_uarte_init), (108 bytes).
    Removing nrfx_uarte.o(i.nrfx_uarte_rx), (260 bytes).
    Removing nrfx_uarte.o(i.nrfx_uarte_rx_abort), (44 bytes).
    Removing nrfx_uarte.o(i.nrfx_uarte_rx_ready), (14 bytes).
    Removing nrfx_uarte.o(i.nrfx_uarte_tx_abort), (56 bytes).
    Removing nrfx_uarte.o(i.nrfx_uarte_uninit), (264 bytes).
    Removing nrfx_uarte.o(i.rx_done_event), (22 bytes).
    Removing nrfx_uarte.o(i.tx_done_event), (26 bytes).
    Removing nrfx_uarte.o(i.uarte_irq_handler), (274 bytes).
    Removing nrfx_uarte.o(.constdata), (16 bytes).
    Removing nrfx_uarte.o(.constdata), (4 bytes).
    Removing nrfx_uarte.o(.constdata), (14 bytes).
    Removing nrfx_uarte.o(.constdata), (14 bytes).
    Removing nrfx_ppi.o(.rev16_text), (4 bytes).
    Removing nrfx_ppi.o(.revsh_text), (4 bytes).
    Removing nrfx_ppi.o(.rrx_text), (6 bytes).
    Removing nrfx_ppi.o(i.are_app_channels), (12 bytes).
    Removing nrfx_ppi.o(i.is_allocated_channel), (24 bytes).
    Removing nrfx_ppi.o(i.is_allocated_group), (24 bytes).
    Removing nrfx_ppi.o(i.is_app_channel), (10 bytes).
    Removing nrfx_ppi.o(i.is_app_group), (12 bytes).
    Removing nrfx_ppi.o(i.is_programmable_app_channel), (12 bytes).
    Removing nrfx_ppi.o(i.nrfx_ppi_channel_alloc), (108 bytes).
    Removing nrfx_ppi.o(i.nrfx_ppi_channel_assign), (68 bytes).
    Removing nrfx_ppi.o(i.nrfx_ppi_channel_disable), (52 bytes).
    Removing nrfx_ppi.o(i.nrfx_ppi_channel_enable), (52 bytes).
    Removing nrfx_ppi.o(i.nrfx_ppi_channel_fork_assign), (36 bytes).
    Removing nrfx_ppi.o(i.nrfx_ppi_channel_free), (64 bytes).
    Removing nrfx_ppi.o(i.nrfx_ppi_channels_include_in_group), (84 bytes).
    Removing nrfx_ppi.o(i.nrfx_ppi_channels_remove_from_group), (84 bytes).
    Removing nrfx_ppi.o(i.nrfx_ppi_free_all), (84 bytes).
    Removing nrfx_ppi.o(i.nrfx_ppi_group_alloc), (104 bytes).
    Removing nrfx_ppi.o(i.nrfx_ppi_group_disable), (36 bytes).
    Removing nrfx_ppi.o(i.nrfx_ppi_group_enable), (44 bytes).
    Removing nrfx_ppi.o(i.nrfx_ppi_group_free), (80 bytes).
    Removing nrfx_ppi.o(.constdata), (23 bytes).
    Removing nrfx_ppi.o(.constdata), (22 bytes).
    Removing nrfx_ppi.o(.constdata), (24 bytes).
    Removing nrfx_ppi.o(.constdata), (29 bytes).
    Removing nrfx_ppi.o(.constdata), (24 bytes).
    Removing nrfx_ppi.o(.constdata), (25 bytes).
    Removing nrfx_ppi.o(.constdata), (21 bytes).
    Removing nrfx_ppi.o(.constdata), (20 bytes).
    Removing nrfx_ppi.o(.constdata), (22 bytes).
    Removing nrfx_ppi.o(.constdata), (23 bytes).
    Removing nrfx_ppi.o(.constdata), (36 bytes).
    Removing nrfx_ppi.o(.constdata), (35 bytes).
    Removing nrfx_ppi.o(.data), (8 bytes).
    Removing nrf_drv_ppi.o(.rev16_text), (4 bytes).
    Removing nrf_drv_ppi.o(.revsh_text), (4 bytes).
    Removing nrf_drv_ppi.o(.rrx_text), (6 bytes).
    Removing nrf_drv_ppi.o(i.nrf_drv_ppi_init), (24 bytes).
    Removing nrf_drv_ppi.o(i.nrf_drv_ppi_uninit), (28 bytes).
    Removing nrf_drv_ppi.o(.data), (1 bytes).
    Removing nrfx_saadc.o(.rev16_text), (4 bytes).
    Removing nrfx_saadc.o(.revsh_text), (4 bytes).
    Removing nrfx_saadc.o(.rrx_text), (6 bytes).
    Removing nrfx_saadc.o(i.nrf_saadc_channel_input_set), (20 bytes).
    Removing nrfx_saadc.o(i.nrf_saadc_limit_int_get), (16 bytes).
    Removing nrfx_saadc.o(i.nrfx_coredep_delay_us), (16 bytes).
    Removing nrfx_saadc.o(i.nrfx_saadc_abort), (92 bytes).
    Removing nrfx_saadc.o(i.nrfx_saadc_buffer_convert), (164 bytes).
    Removing nrfx_saadc.o(i.nrfx_saadc_calibrate_offset), (52 bytes).
    Removing nrfx_saadc.o(i.nrfx_saadc_channel_init), (172 bytes).
    Removing nrfx_saadc.o(i.nrfx_saadc_channel_uninit), (68 bytes).
    Removing nrfx_saadc.o(i.nrfx_saadc_init), (148 bytes).
    Removing nrfx_saadc.o(i.nrfx_saadc_is_busy), (16 bytes).
    Removing nrfx_saadc.o(i.nrfx_saadc_limits_set), (128 bytes).
    Removing nrfx_saadc.o(i.nrfx_saadc_sample), (44 bytes).
    Removing nrfx_saadc.o(i.nrfx_saadc_sample_convert), (192 bytes).
    Removing nrfx_saadc.o(i.nrfx_saadc_sample_task_get), (28 bytes).
    Removing nrfx_saadc.o(i.nrfx_saadc_uninit), (116 bytes).
    Removing nrfx_saadc.o(.constdata), (6 bytes).
    Removing nrfx_saadc.o(.constdata), (16 bytes).
    Removing nrfx_saadc.o(.constdata), (24 bytes).
    Removing nrfx_saadc.o(.constdata), (26 bytes).
    Removing nrfx_saadc.o(.constdata), (26 bytes).
    Removing nrfx_saadc.o(.constdata), (26 bytes).
    Removing nrfx_saadc.o(.constdata), (18 bytes).
    Removing nrfx_saadc.o(.constdata), (28 bytes).
    Removing nrfx_timer.o(.rev16_text), (4 bytes).
    Removing nrfx_timer.o(.revsh_text), (4 bytes).
    Removing nrfx_timer.o(.rrx_text), (6 bytes).
    Removing nrfx_timer.o(i.nrfx_timer_capture), (30 bytes).
    Removing nrfx_timer.o(i.nrfx_timer_clear), (8 bytes).
    Removing nrfx_timer.o(i.nrfx_timer_compare_int_disable), (14 bytes).
    Removing nrfx_timer.o(i.nrfx_timer_compare_int_enable), (36 bytes).
    Removing nrfx_timer.o(i.nrfx_timer_disable), (28 bytes).
    Removing nrfx_timer.o(i.nrfx_timer_enable), (28 bytes).
    Removing nrfx_timer.o(i.nrfx_timer_increment), (8 bytes).
    Removing nrfx_timer.o(i.nrfx_timer_is_enabled), (32 bytes).
    Removing nrfx_timer.o(i.nrfx_timer_pause), (8 bytes).
    Removing nrfx_timer.o(i.nrfx_timer_resume), (8 bytes).
    Removing nrfx_timer.o(i.nrfx_timer_uninit), (92 bytes).
    Removing nrfx_timer.o(.constdata), (16 bytes).
    Removing nrfx_spi.o(.rev16_text), (4 bytes).
    Removing nrfx_spi.o(.revsh_text), (4 bytes).
    Removing nrfx_spi.o(.rrx_text), (6 bytes).
    Removing nrfx_spim.o(.rev16_text), (4 bytes).
    Removing nrfx_spim.o(.revsh_text), (4 bytes).
    Removing nrfx_spim.o(.rrx_text), (6 bytes).
    Removing nrfx_spim.o(i.nrfx_spim_abort), (48 bytes).
    Removing nrfx_spim.o(i.nrfx_spim_end_event_get), (8 bytes).
    Removing nrfx_spim.o(i.nrfx_spim_start_task_get), (6 bytes).
    Removing nrfx_spim.o(i.nrfx_spim_uninit), (148 bytes).
    Removing nrfx_spim.o(.constdata), (15 bytes).
    Removing nrfx_spim.o(.constdata), (10 bytes).
    Removing nrfx_spim.o(.constdata), (15 bytes).
    Removing nrf_drv_spi.o(.rev16_text), (4 bytes).
    Removing nrf_drv_spi.o(.revsh_text), (4 bytes).
    Removing nrf_drv_spi.o(.rrx_text), (6 bytes).
    Removing app_button.o(.rev16_text), (4 bytes).
    Removing app_button.o(.revsh_text), (4 bytes).
    Removing app_button.o(.rrx_text), (6 bytes).
    Removing app_button.o(i.app_button_disable), (72 bytes).
    Removing app_button.o(i.app_button_enable), (36 bytes).
    Removing app_button.o(i.app_button_init), (128 bytes).
    Removing app_button.o(i.app_button_is_pushed), (36 bytes).
    Removing app_button.o(i.button_get), (40 bytes).
    Removing app_button.o(i.detection_delay_timeout_handler), (72 bytes).
    Removing app_button.o(i.evt_handle), (224 bytes).
    Removing app_button.o(i.gpiote_event_handler), (60 bytes).
    Removing app_button.o(i.state_set), (48 bytes).
    Removing app_button.o(i.timer_start), (24 bytes).
    Removing app_button.o(i.usr_event), (32 bytes).
    Removing app_button.o(.bss), (32 bytes).
    Removing app_button.o(.bss), (16 bytes).
    Removing app_button.o(.constdata), (8 bytes).
    Removing app_button.o(.data), (24 bytes).
    Removing app_error.o(.rev16_text), (4 bytes).
    Removing app_error.o(.revsh_text), (4 bytes).
    Removing app_error.o(.rrx_text), (6 bytes).
    Removing app_error.o(i.app_error_save_and_stop), (100 bytes).
    Removing app_error.o(.bss), (32 bytes).
    Removing app_error_handler_keil.o(.rev16_text), (4 bytes).
    Removing app_error_handler_keil.o(.revsh_text), (4 bytes).
    Removing app_error_handler_keil.o(.rrx_text), (6 bytes).
    Removing app_error_handler_keil.o(.emb_text), (26 bytes).
    Removing app_error_weak.o(.rev16_text), (4 bytes).
    Removing app_error_weak.o(.revsh_text), (4 bytes).
    Removing app_error_weak.o(.rrx_text), (6 bytes).
    Removing app_fifo.o(.rev16_text), (4 bytes).
    Removing app_fifo.o(.revsh_text), (4 bytes).
    Removing app_fifo.o(.rrx_text), (6 bytes).
    Removing app_fifo.o(i.app_fifo_flush), (8 bytes).
    Removing app_fifo.o(i.app_fifo_init), (32 bytes).
    Removing app_fifo.o(i.app_fifo_peek), (34 bytes).
    Removing app_fifo.o(i.app_fifo_read), (74 bytes).
    Removing app_fifo.o(i.app_fifo_write), (82 bytes).
    Removing app_scheduler.o(.rev16_text), (4 bytes).
    Removing app_scheduler.o(.revsh_text), (4 bytes).
    Removing app_scheduler.o(.rrx_text), (6 bytes).
    Removing app_scheduler.o(i.app_sched_event_put), (160 bytes).
    Removing app_scheduler.o(i.app_sched_execute), (64 bytes).
    Removing app_scheduler.o(i.app_sched_init), (44 bytes).
    Removing app_scheduler.o(i.app_sched_queue_space_get), (36 bytes).
    Removing app_scheduler.o(.data), (16 bytes).
    Removing app_timer2.o(.rev16_text), (4 bytes).
    Removing app_timer2.o(.revsh_text), (4 bytes).
    Removing app_timer2.o(.rrx_text), (6 bytes).
    Removing app_timer2.o(i.app_timer_cnt_diff_compute), (8 bytes).
    Removing app_timer2.o(i.app_timer_pause), (12 bytes).
    Removing app_timer2.o(i.app_timer_resume), (12 bytes).
    Removing app_timer2.o(i.app_timer_stop_all), (20 bytes).
    Removing app_uart_fifo.o(.rev16_text), (4 bytes).
    Removing app_uart_fifo.o(.revsh_text), (4 bytes).
    Removing app_uart_fifo.o(.rrx_text), (6 bytes).
    Removing app_uart_fifo.o(i.app_uart_close), (40 bytes).
    Removing app_uart_fifo.o(i.app_uart_flush), (32 bytes).
    Removing app_uart_fifo.o(i.app_uart_get), (52 bytes).
    Removing app_uart_fifo.o(i.app_uart_init), (164 bytes).
    Removing app_uart_fifo.o(i.nrf_drv_uart_rx), (32 bytes).
    Removing app_uart_fifo.o(i.uart_event_handler), (164 bytes).
    Removing app_uart_fifo.o(.constdata), (32 bytes).
    Removing app_util_platform.o(.rev16_text), (4 bytes).
    Removing app_util_platform.o(.revsh_text), (4 bytes).
    Removing app_util_platform.o(.rrx_text), (6 bytes).
    Removing app_util_platform.o(i.app_util_disable_irq), (16 bytes).
    Removing app_util_platform.o(i.app_util_enable_irq), (20 bytes).
    Removing app_util_platform.o(i.current_int_priority_get), (48 bytes).
    Removing app_util_platform.o(i.privilege_level_get), (26 bytes).
    Removing app_util_platform.o(.data), (4 bytes).
    Removing drv_rtc.o(.rev16_text), (4 bytes).
    Removing drv_rtc.o(.revsh_text), (4 bytes).
    Removing drv_rtc.o(.rrx_text), (6 bytes).
    Removing drv_rtc.o(i.drv_rtc_compare_enable), (12 bytes).
    Removing drv_rtc.o(i.drv_rtc_compare_get), (14 bytes).
    Removing drv_rtc.o(i.drv_rtc_overflow_disable), (16 bytes).
    Removing drv_rtc.o(i.drv_rtc_tick_disable), (16 bytes).
    Removing drv_rtc.o(i.drv_rtc_tick_enable), (8 bytes).
    Removing drv_rtc.o(i.drv_rtc_tick_pending), (8 bytes).
    Removing drv_rtc.o(i.drv_rtc_uninit), (128 bytes).
    Removing hardfault_implementation.o(.rev16_text), (4 bytes).
    Removing hardfault_implementation.o(.revsh_text), (4 bytes).
    Removing hardfault_implementation.o(.rrx_text), (6 bytes).
    Removing nrf_assert.o(.rev16_text), (4 bytes).
    Removing nrf_assert.o(.revsh_text), (4 bytes).
    Removing nrf_assert.o(.rrx_text), (6 bytes).
    Removing nrf_assert.o(i.assert_nrf_callback), (20 bytes).
    Removing nrf_atfifo.o(.rev16_text), (4 bytes).
    Removing nrf_atfifo.o(.revsh_text), (4 bytes).
    Removing nrf_atfifo.o(.rrx_text), (6 bytes).
    Removing nrf_atfifo.o(i.nrf_atfifo_alloc_put), (46 bytes).
    Removing nrf_atfifo.o(i.nrf_atfifo_clear), (16 bytes).
    Removing nrf_atfifo.o(i.nrf_atfifo_get_free), (48 bytes).
    Removing nrf_atflags.o(.rev16_text), (4 bytes).
    Removing nrf_atflags.o(.revsh_text), (4 bytes).
    Removing nrf_atflags.o(.rrx_text), (6 bytes).
    Removing nrf_atflags.o(i.nrf_atflags_fetch_clear), (32 bytes).
    Removing nrf_atflags.o(i.nrf_atflags_fetch_set), (32 bytes).
    Removing nrf_atflags.o(i.nrf_atflags_find_and_clear_flag), (72 bytes).
    Removing nrf_atflags.o(i.nrf_atflags_find_and_set_flag), (74 bytes).
    Removing nrf_atflags.o(i.nrf_atflags_init), (36 bytes).
    Removing nrf_atomic.o(.rev16_text), (4 bytes).
    Removing nrf_atomic.o(.revsh_text), (4 bytes).
    Removing nrf_atomic.o(.rrx_text), (6 bytes).
    Removing nrf_atomic.o(i.nrf_atomic_flag_clear), (6 bytes).
    Removing nrf_atomic.o(i.nrf_atomic_flag_set_fetch), (6 bytes).
    Removing nrf_atomic.o(i.nrf_atomic_u32_cmp_exch), (4 bytes).
    Removing nrf_atomic.o(i.nrf_atomic_u32_fetch_add), (10 bytes).
    Removing nrf_atomic.o(i.nrf_atomic_u32_fetch_or), (10 bytes).
    Removing nrf_atomic.o(i.nrf_atomic_u32_fetch_sub), (10 bytes).
    Removing nrf_atomic.o(i.nrf_atomic_u32_fetch_sub_hs), (10 bytes).
    Removing nrf_atomic.o(i.nrf_atomic_u32_fetch_xor), (10 bytes).
    Removing nrf_atomic.o(i.nrf_atomic_u32_store), (12 bytes).
    Removing nrf_atomic.o(i.nrf_atomic_u32_sub_hs), (12 bytes).
    Removing nrf_atomic.o(i.nrf_atomic_u32_xor), (12 bytes).
    Removing nrf_balloc.o(.rev16_text), (4 bytes).
    Removing nrf_balloc.o(.revsh_text), (4 bytes).
    Removing nrf_balloc.o(.rrx_text), (6 bytes).
    Removing nrf_fprintf.o(.rev16_text), (4 bytes).
    Removing nrf_fprintf.o(.revsh_text), (4 bytes).
    Removing nrf_fprintf.o(.rrx_text), (6 bytes).
    Removing nrf_fprintf_format.o(.rev16_text), (4 bytes).
    Removing nrf_fprintf_format.o(.revsh_text), (4 bytes).
    Removing nrf_fprintf_format.o(.rrx_text), (6 bytes).
    Removing nrf_memobj.o(.rev16_text), (4 bytes).
    Removing nrf_memobj.o(.revsh_text), (4 bytes).
    Removing nrf_memobj.o(.rrx_text), (6 bytes).
    Removing nrf_pwr_mgmt.o(.rev16_text), (4 bytes).
    Removing nrf_pwr_mgmt.o(.revsh_text), (4 bytes).
    Removing nrf_pwr_mgmt.o(.rrx_text), (6 bytes).
    Removing nrf_pwr_mgmt.o(i.nrf_pwr_mgmt_feed), (2 bytes).
    Removing nrf_pwr_mgmt.o(i.nrf_pwr_mgmt_shutdown), (60 bytes).
    Removing nrf_pwr_mgmt.o(i.shutdown_process), (124 bytes).
    Removing nrf_ringbuf.o(.rev16_text), (4 bytes).
    Removing nrf_ringbuf.o(.revsh_text), (4 bytes).
    Removing nrf_ringbuf.o(.rrx_text), (6 bytes).
    Removing nrf_ringbuf.o(i.nrf_ringbuf_alloc), (112 bytes).
    Removing nrf_ringbuf.o(i.nrf_ringbuf_cpy_get), (124 bytes).
    Removing nrf_ringbuf.o(i.nrf_ringbuf_cpy_put), (114 bytes).
    Removing nrf_ringbuf.o(i.nrf_ringbuf_free), (38 bytes).
    Removing nrf_ringbuf.o(i.nrf_ringbuf_get), (112 bytes).
    Removing nrf_ringbuf.o(i.nrf_ringbuf_put), (48 bytes).
    Removing nrf_section_iter.o(.rev16_text), (4 bytes).
    Removing nrf_section_iter.o(.revsh_text), (4 bytes).
    Removing nrf_section_iter.o(.rrx_text), (6 bytes).
    Removing nrf_sortlist.o(.rev16_text), (4 bytes).
    Removing nrf_sortlist.o(.revsh_text), (4 bytes).
    Removing nrf_sortlist.o(.rrx_text), (6 bytes).
    Removing nrf_sortlist.o(i.nrf_sortlist_next), (4 bytes).
    Removing nrf_strerror.o(.rev16_text), (4 bytes).
    Removing nrf_strerror.o(.revsh_text), (4 bytes).
    Removing nrf_strerror.o(.rrx_text), (6 bytes).
    Removing retarget.o(.rev16_text), (4 bytes).
    Removing retarget.o(.revsh_text), (4 bytes).
    Removing retarget.o(.rrx_text), (6 bytes).
    Removing retarget.o(i.fgetc), (18 bytes).
    Removing retarget.o(i.fputc), (14 bytes).
    Removing retarget.o(.data), (4 bytes).
    Removing retarget.o(.data), (4 bytes).
    Removing nrf_log_backend_rtt.o(.rev16_text), (4 bytes).
    Removing nrf_log_backend_rtt.o(.revsh_text), (4 bytes).
    Removing nrf_log_backend_rtt.o(.rrx_text), (6 bytes).
    Removing nrf_log_backend_serial.o(.rev16_text), (4 bytes).
    Removing nrf_log_backend_serial.o(.revsh_text), (4 bytes).
    Removing nrf_log_backend_serial.o(.rrx_text), (6 bytes).
    Removing nrf_log_default_backends.o(.rev16_text), (4 bytes).
    Removing nrf_log_default_backends.o(.revsh_text), (4 bytes).
    Removing nrf_log_default_backends.o(.rrx_text), (6 bytes).
    Removing nrf_log_frontend.o(.rev16_text), (4 bytes).
    Removing nrf_log_frontend.o(.revsh_text), (4 bytes).
    Removing nrf_log_frontend.o(.rrx_text), (6 bytes).
    Removing nrf_log_frontend.o(i.nrf_log_backend_remove), (52 bytes).
    Removing nrf_log_frontend.o(i.nrf_log_frontend_hexdump), (168 bytes).
    Removing nrf_log_frontend.o(i.nrf_log_frontend_std_3), (18 bytes).
    Removing nrf_log_frontend.o(i.nrf_log_frontend_std_4), (20 bytes).
    Removing nrf_log_frontend.o(i.nrf_log_frontend_std_5), (28 bytes).
    Removing nrf_log_frontend.o(i.nrf_log_frontend_std_6), (28 bytes).
    Removing nrf_log_frontend.o(i.nrf_log_module_filter_get), (36 bytes).
    Removing nrf_log_frontend.o(i.nrf_log_module_filter_set), (2 bytes).
    Removing nrf_log_frontend.o(i.nrf_log_push), (96 bytes).
    Removing nrf_log_str_formatter.o(.rev16_text), (4 bytes).
    Removing nrf_log_str_formatter.o(.revsh_text), (4 bytes).
    Removing nrf_log_str_formatter.o(.rrx_text), (6 bytes).
    Removing nrf_log_str_formatter.o(i.nrf_log_str_formatter_timestamp_freq_set), (32 bytes).
    Removing segger_rtt.o(.rev16_text), (4 bytes).
    Removing segger_rtt.o(.revsh_text), (4 bytes).
    Removing segger_rtt.o(.rrx_text), (6 bytes).
    Removing segger_rtt.o(i.SEGGER_RTT_AllocDownBuffer), (112 bytes).
    Removing segger_rtt.o(i.SEGGER_RTT_AllocUpBuffer), (112 bytes).
    Removing segger_rtt.o(i.SEGGER_RTT_ConfigDownBuffer), (96 bytes).
    Removing segger_rtt.o(i.SEGGER_RTT_ConfigUpBuffer), (96 bytes).
    Removing segger_rtt.o(i.SEGGER_RTT_GetKey), (28 bytes).
    Removing segger_rtt.o(i.SEGGER_RTT_HasData), (24 bytes).
    Removing segger_rtt.o(i.SEGGER_RTT_HasKey), (32 bytes).
    Removing segger_rtt.o(i.SEGGER_RTT_PutChar), (96 bytes).
    Removing segger_rtt.o(i.SEGGER_RTT_PutCharSkip), (84 bytes).
    Removing segger_rtt.o(i.SEGGER_RTT_PutCharSkipNoLock), (52 bytes).
    Removing segger_rtt.o(i.SEGGER_RTT_Read), (44 bytes).
    Removing segger_rtt.o(i.SEGGER_RTT_ReadNoLock), (120 bytes).
    Removing segger_rtt.o(i.SEGGER_RTT_SetFlagsDownBuffer), (68 bytes).
    Removing segger_rtt.o(i.SEGGER_RTT_SetFlagsUpBuffer), (68 bytes).
    Removing segger_rtt.o(i.SEGGER_RTT_SetNameDownBuffer), (68 bytes).
    Removing segger_rtt.o(i.SEGGER_RTT_SetNameUpBuffer), (68 bytes).
    Removing segger_rtt.o(i.SEGGER_RTT_SetTerminal), (136 bytes).
    Removing segger_rtt.o(i.SEGGER_RTT_TerminalOut), (176 bytes).
    Removing segger_rtt.o(i.SEGGER_RTT_WaitKey), (14 bytes).
    Removing segger_rtt.o(i.SEGGER_RTT_Write), (60 bytes).
    Removing segger_rtt.o(i.SEGGER_RTT_WriteSkipNoLock), (116 bytes).
    Removing segger_rtt.o(i.SEGGER_RTT_WriteString), (26 bytes).
    Removing segger_rtt.o(i.SEGGER_RTT_WriteWithOverwriteNoLock), (148 bytes).
    Removing segger_rtt.o(i._PostTerminalSwitch), (32 bytes).
    Removing segger_rtt.o(.data), (17 bytes).
    Removing segger_rtt_printf.o(.rev16_text), (4 bytes).
    Removing segger_rtt_printf.o(.revsh_text), (4 bytes).
    Removing segger_rtt_printf.o(.rrx_text), (6 bytes).
    Removing segger_rtt_printf.o(i.SEGGER_RTT_printf), (22 bytes).
    Removing segger_rtt_printf.o(i.SEGGER_RTT_vprintf), (406 bytes).
    Removing segger_rtt_printf.o(i._PrintInt), (198 bytes).
    Removing segger_rtt_printf.o(i._PrintUnsigned), (212 bytes).
    Removing segger_rtt_printf.o(i._StoreChar), (62 bytes).
    Removing segger_rtt_printf.o(.constdata), (16 bytes).
    Removing nrf_sdh.o(.rev16_text), (4 bytes).
    Removing nrf_sdh.o(.revsh_text), (4 bytes).
    Removing nrf_sdh.o(.rrx_text), (6 bytes).
    Removing nrf_sdh.o(i.nrf_sdh_disable_request), (84 bytes).
    Removing nrf_sdh.o(i.nrf_sdh_is_suspended), (20 bytes).
    Removing nrf_sdh.o(i.nrf_sdh_request_continue), (28 bytes).
    Removing nrf_sdh.o(i.nrf_sdh_resume), (56 bytes).
    Removing nrf_sdh.o(i.nrf_sdh_suspend), (24 bytes).
    Removing nrf_sdh.o(i.softdevice_evt_irq_disable), (64 bytes).
    Removing nrf_sdh_ble.o(.rev16_text), (4 bytes).
    Removing nrf_sdh_ble.o(.revsh_text), (4 bytes).
    Removing nrf_sdh_ble.o(.rrx_text), (6 bytes).
    Removing nrf_sdh_soc.o(.rev16_text), (4 bytes).
    Removing nrf_sdh_soc.o(.revsh_text), (4 bytes).
    Removing nrf_sdh_soc.o(.rrx_text), (6 bytes).
    Removing arm_startup_nrf52.o(HEAP), (8192 bytes).
    Removing system_nrf52.o(.rev16_text), (4 bytes).
    Removing system_nrf52.o(.revsh_text), (4 bytes).
    Removing system_nrf52.o(.rrx_text), (6 bytes).
    Removing system_nrf52.o(i.SystemCoreClockUpdate), (16 bytes).
    Removing dadd.o(.text), (334 bytes).
    Removing dmul.o(.text), (228 bytes).
    Removing ddiv.o(.text), (222 bytes).
    Removing dfixul.o(.text), (48 bytes).
    Removing cdrcmple.o(.text), (48 bytes).
    Removing depilogue.o(.text), (186 bytes).

632 unused section(s) (total 30055 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/../cmprslib/zerorunl2.c          0x00000000   Number         0  __dczerorl2.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  iscntrl_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  ctype_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isgraph_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isdigit_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isprint_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  islower_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  ispunct_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isalnum_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isblank_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isalpha_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isspace_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isupper_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isxdigit_o.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uidiv.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uldiv.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry12a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry2.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry5.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry12b.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llushr.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llshl.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llsshr.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf5.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfb.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf4.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf0.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf8.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfa.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf1.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf2.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf3.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf6.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf7.o ABSOLUTE
    ../clib/microlib/string/memcmp.c         0x00000000   Number         0  memcmp.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpyb.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpya.o ABSOLUTE
    ../clib/microlib/string/memset.c         0x00000000   Number         0  memseta.o ABSOLUTE
    ../clib/microlib/string/strcpy.c         0x00000000   Number         0  strcpy.o ABSOLUTE
    ../clib/microlib/string/strlen.c         0x00000000   Number         0  strlen.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  iusefp.o ABSOLUTE
    ../fplib/microlib/fpadd.c                0x00000000   Number         0  dadd.o ABSOLUTE
    ../fplib/microlib/fpdiv.c                0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  depilogue.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixul.o ABSOLUTE
    ../fplib/microlib/fpmul.c                0x00000000   Number         0  dmul.o ABSOLUTE
    ..\..\..\..\..\..\components\ble\ble_advertising\ble_advertising.c 0x00000000   Number         0  ble_advertising.o ABSOLUTE
    ..\..\..\..\..\..\components\ble\ble_link_ctx_manager\ble_link_ctx_manager.c 0x00000000   Number         0  ble_link_ctx_manager.o ABSOLUTE
    ..\..\..\..\..\..\components\ble\ble_services\ble_nus\ble_nus.c 0x00000000   Number         0  ble_nus.o ABSOLUTE
    ..\..\..\..\..\..\components\ble\common\ble_advdata.c 0x00000000   Number         0  ble_advdata.o ABSOLUTE
    ..\..\..\..\..\..\components\ble\common\ble_conn_params.c 0x00000000   Number         0  ble_conn_params.o ABSOLUTE
    ..\..\..\..\..\..\components\ble\common\ble_conn_state.c 0x00000000   Number         0  ble_conn_state.o ABSOLUTE
    ..\..\..\..\..\..\components\ble\common\ble_srv_common.c 0x00000000   Number         0  ble_srv_common.o ABSOLUTE
    ..\..\..\..\..\..\components\ble\nrf_ble_gatt\nrf_ble_gatt.c 0x00000000   Number         0  nrf_ble_gatt.o ABSOLUTE
    ..\..\..\..\..\..\components\ble\nrf_ble_qwr\nrf_ble_qwr.c 0x00000000   Number         0  nrf_ble_qwr.o ABSOLUTE
    ..\..\..\..\..\..\components\boards\boards.c 0x00000000   Number         0  boards.o ABSOLUTE
    ..\..\..\..\..\..\components\libraries\atomic\nrf_atomic.c 0x00000000   Number         0  nrf_atomic.o ABSOLUTE
    ..\..\..\..\..\..\components\libraries\atomic_fifo\nrf_atfifo.c 0x00000000   Number         0  nrf_atfifo.o ABSOLUTE
    ..\..\..\..\..\..\components\libraries\atomic_flags\nrf_atflags.c 0x00000000   Number         0  nrf_atflags.o ABSOLUTE
    ..\..\..\..\..\..\components\libraries\balloc\nrf_balloc.c 0x00000000   Number         0  nrf_balloc.o ABSOLUTE
    ..\..\..\..\..\..\components\libraries\bsp\bsp.c 0x00000000   Number         0  bsp.o ABSOLUTE
    ..\..\..\..\..\..\components\libraries\bsp\bsp_btn_ble.c 0x00000000   Number         0  bsp_btn_ble.o ABSOLUTE
    ..\..\..\..\..\..\components\libraries\button\app_button.c 0x00000000   Number         0  app_button.o ABSOLUTE
    ..\..\..\..\..\..\components\libraries\experimental_section_vars\nrf_section_iter.c 0x00000000   Number         0  nrf_section_iter.o ABSOLUTE
    ..\..\..\..\..\..\components\libraries\fifo\app_fifo.c 0x00000000   Number         0  app_fifo.o ABSOLUTE
    ..\..\..\..\..\..\components\libraries\hardfault\hardfault_implementation.c 0x00000000   Number         0  hardfault_implementation.o ABSOLUTE
    ..\..\..\..\..\..\components\libraries\log\src\nrf_log_backend_rtt.c 0x00000000   Number         0  nrf_log_backend_rtt.o ABSOLUTE
    ..\..\..\..\..\..\components\libraries\log\src\nrf_log_backend_serial.c 0x00000000   Number         0  nrf_log_backend_serial.o ABSOLUTE
    ..\..\..\..\..\..\components\libraries\log\src\nrf_log_default_backends.c 0x00000000   Number         0  nrf_log_default_backends.o ABSOLUTE
    ..\..\..\..\..\..\components\libraries\log\src\nrf_log_frontend.c 0x00000000   Number         0  nrf_log_frontend.o ABSOLUTE
    ..\..\..\..\..\..\components\libraries\log\src\nrf_log_str_formatter.c 0x00000000   Number         0  nrf_log_str_formatter.o ABSOLUTE
    ..\..\..\..\..\..\components\libraries\memobj\nrf_memobj.c 0x00000000   Number         0  nrf_memobj.o ABSOLUTE
    ..\..\..\..\..\..\components\libraries\pwr_mgmt\nrf_pwr_mgmt.c 0x00000000   Number         0  nrf_pwr_mgmt.o ABSOLUTE
    ..\..\..\..\..\..\components\libraries\ringbuf\nrf_ringbuf.c 0x00000000   Number         0  nrf_ringbuf.o ABSOLUTE
    ..\..\..\..\..\..\components\libraries\scheduler\app_scheduler.c 0x00000000   Number         0  app_scheduler.o ABSOLUTE
    ..\..\..\..\..\..\components\libraries\sortlist\nrf_sortlist.c 0x00000000   Number         0  nrf_sortlist.o ABSOLUTE
    ..\..\..\..\..\..\components\libraries\strerror\nrf_strerror.c 0x00000000   Number         0  nrf_strerror.o ABSOLUTE
    ..\..\..\..\..\..\components\libraries\timer\app_timer2.c 0x00000000   Number         0  app_timer2.o ABSOLUTE
    ..\..\..\..\..\..\components\libraries\timer\drv_rtc.c 0x00000000   Number         0  drv_rtc.o ABSOLUTE
    ..\..\..\..\..\..\components\libraries\uart\app_uart_fifo.c 0x00000000   Number         0  app_uart_fifo.o ABSOLUTE
    ..\..\..\..\..\..\components\libraries\uart\retarget.c 0x00000000   Number         0  retarget.o ABSOLUTE
    ..\..\..\..\..\..\components\libraries\util\app_error.c 0x00000000   Number         0  app_error.o ABSOLUTE
    ..\..\..\..\..\..\components\libraries\util\app_error_handler_keil.c 0x00000000   Number         0  app_error_handler_keil.o ABSOLUTE
    ..\..\..\..\..\..\components\libraries\util\app_error_weak.c 0x00000000   Number         0  app_error_weak.o ABSOLUTE
    ..\..\..\..\..\..\components\libraries\util\app_util_platform.c 0x00000000   Number         0  app_util_platform.o ABSOLUTE
    ..\..\..\..\..\..\components\libraries\util\nrf_assert.c 0x00000000   Number         0  nrf_assert.o ABSOLUTE
    ..\..\..\..\..\..\components\softdevice\common\nrf_sdh.c 0x00000000   Number         0  nrf_sdh.o ABSOLUTE
    ..\..\..\..\..\..\components\softdevice\common\nrf_sdh_ble.c 0x00000000   Number         0  nrf_sdh_ble.o ABSOLUTE
    ..\..\..\..\..\..\components\softdevice\common\nrf_sdh_soc.c 0x00000000   Number         0  nrf_sdh_soc.o ABSOLUTE
    ..\..\..\..\..\..\drive\ads1292.c        0x00000000   Number         0  ads1292.o ABSOLUTE
    ..\..\..\..\..\..\drive\dht11.c          0x00000000   Number         0  dht11.o ABSOLUTE
    ..\..\..\..\..\..\drive\led.c            0x00000000   Number         0  led.o ABSOLUTE
    ..\..\..\..\..\..\external\fprintf\nrf_fprintf.c 0x00000000   Number         0  nrf_fprintf.o ABSOLUTE
    ..\..\..\..\..\..\external\fprintf\nrf_fprintf_format.c 0x00000000   Number         0  nrf_fprintf_format.o ABSOLUTE
    ..\..\..\..\..\..\external\segger_rtt\SEGGER_RTT.c 0x00000000   Number         0  segger_rtt.o ABSOLUTE
    ..\..\..\..\..\..\external\segger_rtt\SEGGER_RTT_Syscalls_KEIL.c 0x00000000   Number         0  segger_rtt_syscalls_keil.o ABSOLUTE
    ..\..\..\..\..\..\external\segger_rtt\SEGGER_RTT_printf.c 0x00000000   Number         0  segger_rtt_printf.o ABSOLUTE
    ..\..\..\..\..\..\external\utf_converter\utf.c 0x00000000   Number         0  utf.o ABSOLUTE
    ..\..\..\..\..\..\integration\nrfx\legacy\nrf_drv_clock.c 0x00000000   Number         0  nrf_drv_clock.o ABSOLUTE
    ..\..\..\..\..\..\integration\nrfx\legacy\nrf_drv_ppi.c 0x00000000   Number         0  nrf_drv_ppi.o ABSOLUTE
    ..\..\..\..\..\..\integration\nrfx\legacy\nrf_drv_spi.c 0x00000000   Number         0  nrf_drv_spi.o ABSOLUTE
    ..\..\..\..\..\..\integration\nrfx\legacy\nrf_drv_uart.c 0x00000000   Number         0  nrf_drv_uart.o ABSOLUTE
    ..\..\..\..\..\..\modules\nrfx\drivers\src\nrfx_clock.c 0x00000000   Number         0  nrfx_clock.o ABSOLUTE
    ..\..\..\..\..\..\modules\nrfx\drivers\src\nrfx_gpiote.c 0x00000000   Number         0  nrfx_gpiote.o ABSOLUTE
    ..\..\..\..\..\..\modules\nrfx\drivers\src\nrfx_ppi.c 0x00000000   Number         0  nrfx_ppi.o ABSOLUTE
    ..\..\..\..\..\..\modules\nrfx\drivers\src\nrfx_saadc.c 0x00000000   Number         0  nrfx_saadc.o ABSOLUTE
    ..\..\..\..\..\..\modules\nrfx\drivers\src\nrfx_spi.c 0x00000000   Number         0  nrfx_spi.o ABSOLUTE
    ..\..\..\..\..\..\modules\nrfx\drivers\src\nrfx_spim.c 0x00000000   Number         0  nrfx_spim.o ABSOLUTE
    ..\..\..\..\..\..\modules\nrfx\drivers\src\nrfx_timer.c 0x00000000   Number         0  nrfx_timer.o ABSOLUTE
    ..\..\..\..\..\..\modules\nrfx\drivers\src\nrfx_uart.c 0x00000000   Number         0  nrfx_uart.o ABSOLUTE
    ..\..\..\..\..\..\modules\nrfx\drivers\src\nrfx_uarte.c 0x00000000   Number         0  nrfx_uarte.o ABSOLUTE
    ..\..\..\..\..\..\modules\nrfx\drivers\src\prs\nrfx_prs.c 0x00000000   Number         0  nrfx_prs.o ABSOLUTE
    ..\..\..\..\..\..\modules\nrfx\soc\nrfx_atomic.c 0x00000000   Number         0  nrfx_atomic.o ABSOLUTE
    ..\..\..\main.c                          0x00000000   Number         0  main.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\ble\\ble_advertising\\ble_advertising.c 0x00000000   Number         0  ble_advertising.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\ble\\ble_link_ctx_manager\\ble_link_ctx_manager.c 0x00000000   Number         0  ble_link_ctx_manager.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\ble\\ble_services\\ble_nus\\ble_nus.c 0x00000000   Number         0  ble_nus.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\ble\\common\\ble_advdata.c 0x00000000   Number         0  ble_advdata.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\ble\\common\\ble_conn_params.c 0x00000000   Number         0  ble_conn_params.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\ble\\common\\ble_conn_state.c 0x00000000   Number         0  ble_conn_state.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\ble\\common\\ble_srv_common.c 0x00000000   Number         0  ble_srv_common.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\ble\\nrf_ble_gatt\\nrf_ble_gatt.c 0x00000000   Number         0  nrf_ble_gatt.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\ble\\nrf_ble_qwr\\nrf_ble_qwr.c 0x00000000   Number         0  nrf_ble_qwr.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\boards\\boards.c 0x00000000   Number         0  boards.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\libraries\\atomic\\nrf_atomic.c 0x00000000   Number         0  nrf_atomic.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\libraries\\atomic_fifo\\nrf_atfifo.c 0x00000000   Number         0  nrf_atfifo.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\libraries\\atomic_flags\\nrf_atflags.c 0x00000000   Number         0  nrf_atflags.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\libraries\\balloc\\nrf_balloc.c 0x00000000   Number         0  nrf_balloc.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\libraries\\bsp\\bsp.c 0x00000000   Number         0  bsp.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\libraries\\bsp\\bsp_btn_ble.c 0x00000000   Number         0  bsp_btn_ble.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\libraries\\button\\app_button.c 0x00000000   Number         0  app_button.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\libraries\\experimental_section_vars\\nrf_section_iter.c 0x00000000   Number         0  nrf_section_iter.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\libraries\\fifo\\app_fifo.c 0x00000000   Number         0  app_fifo.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\libraries\\hardfault\\hardfault_implementation.c 0x00000000   Number         0  hardfault_implementation.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\libraries\\log\\src\\nrf_log_backend_rtt.c 0x00000000   Number         0  nrf_log_backend_rtt.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\libraries\\log\\src\\nrf_log_backend_serial.c 0x00000000   Number         0  nrf_log_backend_serial.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\libraries\\log\\src\\nrf_log_default_backends.c 0x00000000   Number         0  nrf_log_default_backends.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\libraries\\log\\src\\nrf_log_frontend.c 0x00000000   Number         0  nrf_log_frontend.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\libraries\\log\\src\\nrf_log_str_formatter.c 0x00000000   Number         0  nrf_log_str_formatter.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\libraries\\memobj\\nrf_memobj.c 0x00000000   Number         0  nrf_memobj.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\libraries\\pwr_mgmt\\nrf_pwr_mgmt.c 0x00000000   Number         0  nrf_pwr_mgmt.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\libraries\\ringbuf\\nrf_ringbuf.c 0x00000000   Number         0  nrf_ringbuf.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\libraries\\scheduler\\app_scheduler.c 0x00000000   Number         0  app_scheduler.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\libraries\\sortlist\\nrf_sortlist.c 0x00000000   Number         0  nrf_sortlist.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\libraries\\strerror\\nrf_strerror.c 0x00000000   Number         0  nrf_strerror.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\libraries\\timer\\app_timer2.c 0x00000000   Number         0  app_timer2.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\libraries\\timer\\drv_rtc.c 0x00000000   Number         0  drv_rtc.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\libraries\\uart\\app_uart_fifo.c 0x00000000   Number         0  app_uart_fifo.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\libraries\\uart\\retarget.c 0x00000000   Number         0  retarget.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\libraries\\util\\app_error.c 0x00000000   Number         0  app_error.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\libraries\\util\\app_error_handler_keil.c 0x00000000   Number         0  app_error_handler_keil.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\libraries\\util\\app_error_weak.c 0x00000000   Number         0  app_error_weak.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\libraries\\util\\app_util_platform.c 0x00000000   Number         0  app_util_platform.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\libraries\\util\\nrf_assert.c 0x00000000   Number         0  nrf_assert.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\softdevice\\common\\nrf_sdh.c 0x00000000   Number         0  nrf_sdh.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\softdevice\\common\\nrf_sdh_ble.c 0x00000000   Number         0  nrf_sdh_ble.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\components\\softdevice\\common\\nrf_sdh_soc.c 0x00000000   Number         0  nrf_sdh_soc.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\drive\\ads1292.c 0x00000000   Number         0  ads1292.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\drive\\dht11.c   0x00000000   Number         0  dht11.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\drive\\led.c     0x00000000   Number         0  led.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\external\\fprintf\\nrf_fprintf.c 0x00000000   Number         0  nrf_fprintf.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\external\\fprintf\\nrf_fprintf_format.c 0x00000000   Number         0  nrf_fprintf_format.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\external\\segger_rtt\\SEGGER_RTT.c 0x00000000   Number         0  segger_rtt.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\external\\segger_rtt\\SEGGER_RTT_printf.c 0x00000000   Number         0  segger_rtt_printf.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\integration\\nrfx\\legacy\\nrf_drv_clock.c 0x00000000   Number         0  nrf_drv_clock.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\integration\\nrfx\\legacy\\nrf_drv_ppi.c 0x00000000   Number         0  nrf_drv_ppi.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\integration\\nrfx\\legacy\\nrf_drv_spi.c 0x00000000   Number         0  nrf_drv_spi.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\integration\\nrfx\\legacy\\nrf_drv_uart.c 0x00000000   Number         0  nrf_drv_uart.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\modules\\nrfx\\drivers\\src\\nrfx_clock.c 0x00000000   Number         0  nrfx_clock.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\modules\\nrfx\\drivers\\src\\nrfx_gpiote.c 0x00000000   Number         0  nrfx_gpiote.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\modules\\nrfx\\drivers\\src\\nrfx_ppi.c 0x00000000   Number         0  nrfx_ppi.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\modules\\nrfx\\drivers\\src\\nrfx_saadc.c 0x00000000   Number         0  nrfx_saadc.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\modules\\nrfx\\drivers\\src\\nrfx_spi.c 0x00000000   Number         0  nrfx_spi.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\modules\\nrfx\\drivers\\src\\nrfx_spim.c 0x00000000   Number         0  nrfx_spim.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\modules\\nrfx\\drivers\\src\\nrfx_timer.c 0x00000000   Number         0  nrfx_timer.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\modules\\nrfx\\drivers\\src\\nrfx_uart.c 0x00000000   Number         0  nrfx_uart.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\modules\\nrfx\\drivers\\src\\nrfx_uarte.c 0x00000000   Number         0  nrfx_uarte.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\modules\\nrfx\\drivers\\src\\prs\\nrfx_prs.c 0x00000000   Number         0  nrfx_prs.o ABSOLUTE
    ..\\..\\..\\..\\..\\..\\modules\\nrfx\\soc\\nrfx_atomic.c 0x00000000   Number         0  nrfx_atomic.o ABSOLUTE
    ..\\..\\..\\main.c                       0x00000000   Number         0  main.o ABSOLUTE
    RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s 0x00000000   Number         0  arm_startup_nrf52.o ABSOLUTE
    RTE\Device\nRF52832_xxAA\system_nrf52.c  0x00000000   Number         0  system_nrf52.o ABSOLUTE
    RTE\\Device\\nRF52832_xxAA\\system_nrf52.c 0x00000000   Number         0  system_nrf52.o ABSOLUTE
    cdrcmple.s                               0x00000000   Number         0  cdrcmple.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    handlers.s                               0x00000000   Number         0  handlers.o ABSOLUTE
    init.s                                   0x00000000   Number         0  init.o ABSOLUTE
    RESET                                    0x00026000   Section      512  arm_startup_nrf52.o(RESET)
    .ARM.Collect$$$$00000000                 0x00026200   Section        0  entry.o(.ARM.Collect$$$$00000000)
    .ARM.Collect$$$$00000001                 0x00026200   Section        4  entry2.o(.ARM.Collect$$$$00000001)
    .ARM.Collect$$$$00000004                 0x00026204   Section        4  entry5.o(.ARM.Collect$$$$00000004)
    .ARM.Collect$$$$00000008                 0x00026208   Section        0  entry7b.o(.ARM.Collect$$$$00000008)
    .ARM.Collect$$$$0000000A                 0x00026208   Section        0  entry8b.o(.ARM.Collect$$$$0000000A)
    .ARM.Collect$$$$0000000B                 0x00026208   Section        8  entry9a.o(.ARM.Collect$$$$0000000B)
    .ARM.Collect$$$$0000000E                 0x00026210   Section        4  entry12b.o(.ARM.Collect$$$$0000000E)
    .ARM.Collect$$$$0000000F                 0x00026214   Section        0  entry10a.o(.ARM.Collect$$$$0000000F)
    .ARM.Collect$$$$00000011                 0x00026214   Section        0  entry11a.o(.ARM.Collect$$$$00000011)
    .ARM.Collect$$$$00002712                 0x00026214   Section        4  entry2.o(.ARM.Collect$$$$00002712)
    __lit__00000000                          0x00026214   Data           4  entry2.o(.ARM.Collect$$$$00002712)
    .emb_text                                0x00026218   Section      200  nrf_atfifo.o(.emb_text)
    $v0                                      0x00026218   Number         0  nrf_atfifo.o(.emb_text)
    .emb_text                                0x000262e0   Section      226  nrf_atomic.o(.emb_text)
    $v0                                      0x000262e0   Number         0  nrf_atomic.o(.emb_text)
    .text                                    0x000263c4   Section       36  arm_startup_nrf52.o(.text)
    $v0                                      0x000263c4   Number         0  arm_startup_nrf52.o(.text)
    .text                                    0x000263e8   Section        0  uldiv.o(.text)
    .text                                    0x0002644a   Section        0  llshl.o(.text)
    .text                                    0x00026468   Section        0  llushr.o(.text)
    .text                                    0x00026488   Section        0  ctype_o.o(.text)
    .text                                    0x00026490   Section        0  memcpya.o(.text)
    .text                                    0x000264b4   Section        0  memseta.o(.text)
    .text                                    0x000264d8   Section        0  strlen.o(.text)
    .text                                    0x000264e6   Section        0  strcpy.o(.text)
    .text                                    0x000264f8   Section       36  init.o(.text)
    .text                                    0x0002651c   Section        0  __dczerorl2.o(.text)
    i.ADS1299_CMD                            0x00026574   Section        0  ads1292.o(i.ADS1299_CMD)
    i.ADS1299_READREG                        0x000265ac   Section        0  ads1292.o(i.ADS1299_READREG)
    i.ADS1299_WRITEREG                       0x000265ec   Section        0  ads1292.o(i.ADS1299_WRITEREG)
    i.ADS_READDATA                           0x00026628   Section        0  ads1292.o(i.ADS_READDATA)
    i.GPIOTE_IRQHandler                      0x000266c8   Section        0  nrfx_gpiote.o(i.GPIOTE_IRQHandler)
    i.LED1_Close                             0x0002676c   Section        0  led.o(i.LED1_Close)
    i.LED1_Open                              0x0002677a   Section        0  led.o(i.LED1_Open)
    i.LED2_Close                             0x00026788   Section        0  led.o(i.LED2_Close)
    i.LED2_Open                              0x00026796   Section        0  led.o(i.LED2_Open)
    i.LED_Init                               0x000267a4   Section        0  led.o(i.LED_Init)
    i.POWER_CLOCK_IRQHandler                 0x000267bc   Section        0  nrfx_clock.o(i.POWER_CLOCK_IRQHandler)
    i.RTC1_IRQHandler                        0x00026814   Section        0  drv_rtc.o(i.RTC1_IRQHandler)
    i.SAADC_IRQHandler                       0x00026824   Section        0  nrfx_saadc.o(i.SAADC_IRQHandler)
    i.SEGGER_RTT_Init                        0x0002696c   Section        0  segger_rtt.o(i.SEGGER_RTT_Init)
    i.SEGGER_RTT_WriteNoLock                 0x00026970   Section        0  segger_rtt.o(i.SEGGER_RTT_WriteNoLock)
    i.SPIM0_SPIS0_TWIM0_TWIS0_SPI0_TWI0_IRQHandler 0x000269cc   Section        0  nrfx_spim.o(i.SPIM0_SPIS0_TWIM0_TWIS0_SPI0_TWI0_IRQHandler)
    i.SWI2_EGU2_IRQHandler                   0x00026a20   Section        0  nrf_sdh.o(i.SWI2_EGU2_IRQHandler)
    i.SystemInit                             0x00026a24   Section        0  system_nrf52.o(i.SystemInit)
    i.TIMER1_IRQHandler                      0x00026dbc   Section        0  nrfx_timer.o(i.TIMER1_IRQHandler)
    i.UARTE0_UART0_IRQHandler                0x00026e0c   Section        0  nrfx_prs.o(i.UARTE0_UART0_IRQHandler)
    i._DoInit                                0x00026e18   Section        0  segger_rtt.o(i._DoInit)
    _DoInit                                  0x00026e19   Thumb Code    76  segger_rtt.o(i._DoInit)
    i._GetAvailWriteSpace                    0x00026e80   Section        0  segger_rtt.o(i._GetAvailWriteSpace)
    _GetAvailWriteSpace                      0x00026e81   Thumb Code    22  segger_rtt.o(i._GetAvailWriteSpace)
    i._WriteBlocking                         0x00026e96   Section        0  segger_rtt.o(i._WriteBlocking)
    _WriteBlocking                           0x00026e97   Thumb Code    90  segger_rtt.o(i._WriteBlocking)
    i._WriteNoCheck                          0x00026ef0   Section        0  segger_rtt.o(i._WriteNoCheck)
    _WriteNoCheck                            0x00026ef1   Thumb Code    66  segger_rtt.o(i._WriteNoCheck)
    i.__scatterload_copy                     0x00026f32   Section       14  handlers.o(i.__scatterload_copy)
    i.__scatterload_null                     0x00026f40   Section        2  handlers.o(i.__scatterload_null)
    i.__scatterload_zeroinit                 0x00026f42   Section       14  handlers.o(i.__scatterload_zeroinit)
    i.__sd_nvic_app_accessible_irq           0x00026f50   Section        0  nrf_sdh.o(i.__sd_nvic_app_accessible_irq)
    __sd_nvic_app_accessible_irq             0x00026f51   Thumb Code    32  nrf_sdh.o(i.__sd_nvic_app_accessible_irq)
    i.ads1299_spi_init                       0x00026f74   Section        0  ads1292.o(i.ads1299_spi_init)
    i.adv_set_data_size_max_get              0x00027114   Section        0  ble_advertising.o(i.adv_set_data_size_max_get)
    adv_set_data_size_max_get                0x00027115   Thumb Code    16  ble_advertising.o(i.adv_set_data_size_max_get)
    i.advertising_buttons_configure          0x00027124   Section        0  bsp_btn_ble.o(i.advertising_buttons_configure)
    advertising_buttons_configure            0x00027125   Thumb Code    54  bsp_btn_ble.o(i.advertising_buttons_configure)
    i.app_error_fault_handler                0x0002715c   Section        0  app_error_weak.o(i.app_error_fault_handler)
    i.app_error_handler_bare                 0x000271e0   Section        0  app_error.o(i.app_error_handler_bare)
    i.app_fifo_get                           0x000271f6   Section        0  app_fifo.o(i.app_fifo_get)
    i.app_fifo_put                           0x0002720c   Section        0  app_fifo.o(i.app_fifo_put)
    i.app_timer_cnt_get                      0x00027228   Section        0  app_timer2.o(i.app_timer_cnt_get)
    i.app_timer_create                       0x00027234   Section        0  app_timer2.o(i.app_timer_create)
    i.app_timer_init                         0x0002724c   Section        0  app_timer2.o(i.app_timer_init)
    i.app_timer_start                        0x000272a8   Section        0  app_timer2.o(i.app_timer_start)
    i.app_timer_stop                         0x000272d8   Section        0  app_timer2.o(i.app_timer_stop)
    i.app_uart_put                           0x000272e4   Section        0  app_uart_fifo.o(i.app_uart_put)
    i.app_util_critical_region_enter         0x00027338   Section        0  app_util_platform.o(i.app_util_critical_region_enter)
    i.app_util_critical_region_exit          0x00027380   Section        0  app_util_platform.o(i.app_util_critical_region_exit)
    i.blcm_link_ctx_get                      0x000273b4   Section        0  ble_link_ctx_manager.o(i.blcm_link_ctx_get)
    i.ble_advdata_encode                     0x000273fc   Section        0  ble_advdata.o(i.ble_advdata_encode)
    i.ble_advdata_parse                      0x0002757e   Section        0  ble_advdata.o(i.ble_advdata_parse)
    i.ble_advdata_search                     0x0002759e   Section        0  ble_advdata.o(i.ble_advdata_search)
    i.ble_advertising_conn_cfg_tag_set       0x000275e4   Section        0  ble_advertising.o(i.ble_advertising_conn_cfg_tag_set)
    i.ble_advertising_init                   0x000275ea   Section        0  ble_advertising.o(i.ble_advertising_init)
    i.ble_advertising_on_ble_evt             0x000276e2   Section        0  ble_advertising.o(i.ble_advertising_on_ble_evt)
    i.ble_advertising_start                  0x0002774c   Section        0  ble_advertising.o(i.ble_advertising_start)
    i.ble_conn_params_init                   0x0002797c   Section        0  ble_conn_params.o(i.ble_conn_params_init)
    i.ble_conn_state_conn_idx                0x000279e4   Section        0  ble_conn_state.o(i.ble_conn_state_conn_idx)
    i.ble_conn_state_valid                   0x000279f8   Section        0  ble_conn_state.o(i.ble_conn_state_valid)
    i.ble_device_addr_encode                 0x00027a0c   Section        0  ble_advdata.o(i.ble_device_addr_encode)
    ble_device_addr_encode                   0x00027a0d   Thumb Code    96  ble_advdata.o(i.ble_device_addr_encode)
    i.ble_evt_handler                        0x00027a6c   Section        0  main.o(i.ble_evt_handler)
    ble_evt_handler                          0x00027a6d   Thumb Code   174  main.o(i.ble_evt_handler)
    i.ble_evt_handler                        0x00027b48   Section        0  bsp_btn_ble.o(i.ble_evt_handler)
    ble_evt_handler                          0x00027b49   Thumb Code   106  bsp_btn_ble.o(i.ble_evt_handler)
    i.ble_evt_handler                        0x00027bb8   Section        0  ble_conn_params.o(i.ble_evt_handler)
    ble_evt_handler                          0x00027bb9   Thumb Code   242  ble_conn_params.o(i.ble_evt_handler)
    i.ble_evt_handler                        0x00027cb4   Section        0  ble_conn_state.o(i.ble_evt_handler)
    ble_evt_handler                          0x00027cb5   Thumb Code   274  ble_conn_state.o(i.ble_evt_handler)
    i.ble_nus_data_send                      0x00027dcc   Section        0  ble_nus.o(i.ble_nus_data_send)
    i.ble_nus_init                           0x00027e34   Section        0  ble_nus.o(i.ble_nus_init)
    i.ble_nus_on_ble_evt                     0x00027f08   Section        0  ble_nus.o(i.ble_nus_on_ble_evt)
    i.ble_srv_is_notification_enabled        0x00027f80   Section        0  ble_srv_common.o(i.ble_srv_is_notification_enabled)
    i.bsp_board_button_idx_to_pin            0x00027f88   Section        0  boards.o(i.bsp_board_button_idx_to_pin)
    i.bsp_board_led_invert                   0x00027f94   Section        0  boards.o(i.bsp_board_led_invert)
    i.bsp_board_led_off                      0x00027fb8   Section        0  boards.o(i.bsp_board_led_off)
    i.bsp_board_led_on                       0x00027fc8   Section        0  boards.o(i.bsp_board_led_on)
    i.bsp_board_led_state_get                0x00027fd8   Section        0  boards.o(i.bsp_board_led_state_get)
    i.bsp_board_leds_off                     0x00027ff8   Section        0  boards.o(i.bsp_board_leds_off)
    i.bsp_board_leds_on                      0x0002800a   Section        0  boards.o(i.bsp_board_leds_on)
    i.bsp_board_pin_to_button_idx            0x0002801c   Section        0  boards.o(i.bsp_board_pin_to_button_idx)
    i.bsp_btn_ble_sleep_mode_prepare         0x00028040   Section        0  bsp_btn_ble.o(i.bsp_btn_ble_sleep_mode_prepare)
    i.bsp_button_event_handler               0x00028060   Section        0  bsp.o(i.bsp_button_event_handler)
    bsp_button_event_handler                 0x00028061   Thumb Code   132  bsp.o(i.bsp_button_event_handler)
    i.bsp_event_to_button_action_assign      0x000280f0   Section        0  bsp.o(i.bsp_event_to_button_action_assign)
    i.bsp_indication_set                     0x00028134   Section        0  bsp.o(i.bsp_indication_set)
    i.bsp_led_indication                     0x0002814c   Section        0  bsp.o(i.bsp_led_indication)
    bsp_led_indication                       0x0002814d   Thumb Code   446  bsp.o(i.bsp_led_indication)
    i.bsp_wakeup_button_enable               0x00028314   Section        0  bsp.o(i.bsp_wakeup_button_enable)
    i.buf_prealloc                           0x0002831c   Section        0  nrf_log_frontend.o(i.buf_prealloc)
    buf_prealloc                             0x0002831d   Thumb Code   140  nrf_log_frontend.o(i.buf_prealloc)
    i.buffer_add                             0x000283b0   Section        0  nrf_fprintf_format.o(i.buffer_add)
    buffer_add                               0x000283b1   Thumb Code    46  nrf_fprintf_format.o(i.buffer_add)
    i.buffer_is_empty                        0x000283e0   Section        0  nrf_log_frontend.o(i.buffer_is_empty)
    i.channel_free                           0x000283f8   Section        0  nrfx_gpiote.o(i.channel_free)
    channel_free                             0x000283f9   Thumb Code    22  nrfx_gpiote.o(i.channel_free)
    i.channel_port_alloc                     0x00028414   Section        0  nrfx_gpiote.o(i.channel_port_alloc)
    channel_port_alloc                       0x00028415   Thumb Code    66  nrfx_gpiote.o(i.channel_port_alloc)
    i.channel_port_get                       0x0002845c   Section        0  nrfx_gpiote.o(i.channel_port_get)
    channel_port_get                         0x0002845d   Thumb Code    10  nrfx_gpiote.o(i.channel_port_get)
    i.characteristic_add                     0x0002846c   Section        0  ble_srv_common.o(i.characteristic_add)
    i.clock_clk_started_notify               0x000285fc   Section        0  nrf_drv_clock.o(i.clock_clk_started_notify)
    clock_clk_started_notify                 0x000285fd   Thumb Code    34  nrf_drv_clock.o(i.clock_clk_started_notify)
    i.clock_irq_handler                      0x00028624   Section        0  nrf_drv_clock.o(i.clock_irq_handler)
    clock_irq_handler                        0x00028625   Thumb Code    24  nrf_drv_clock.o(i.clock_irq_handler)
    i.compare_func                           0x00028640   Section        0  app_timer2.o(i.compare_func)
    compare_func                             0x00028641   Thumb Code    24  app_timer2.o(i.compare_func)
    i.conn_handle_list_get                   0x00028658   Section        0  ble_conn_state.o(i.conn_handle_list_get)
    i.conn_int_encode                        0x00028696   Section        0  ble_advdata.o(i.conn_int_encode)
    conn_int_encode                          0x00028697   Thumb Code   136  ble_advdata.o(i.conn_int_encode)
    i.conn_params_error_handler              0x0002871e   Section        0  main.o(i.conn_params_error_handler)
    conn_params_error_handler                0x0002871f   Thumb Code     4  main.o(i.conn_params_error_handler)
    i.conn_params_negotiation                0x00028724   Section        0  ble_conn_params.o(i.conn_params_negotiation)
    conn_params_negotiation                  0x00028725   Thumb Code    68  ble_conn_params.o(i.conn_params_negotiation)
    i.data_length_update                     0x0002876c   Section        0  nrf_ble_gatt.o(i.data_length_update)
    data_length_update                       0x0002876d   Thumb Code   110  nrf_ble_gatt.o(i.data_length_update)
    i.datacjfunc                             0x0002882c   Section        0  main.o(i.datacjfunc)
    i.dropped_sat16_get                      0x00028928   Section        0  nrf_log_frontend.o(i.dropped_sat16_get)
    dropped_sat16_get                        0x00028929   Thumb Code    16  nrf_log_frontend.o(i.dropped_sat16_get)
    i.drv_rtc_compare_disable                0x0002893c   Section        0  drv_rtc.o(i.drv_rtc_compare_disable)
    i.drv_rtc_compare_pending                0x00028950   Section        0  drv_rtc.o(i.drv_rtc_compare_pending)
    i.drv_rtc_compare_set                    0x0002895e   Section        0  drv_rtc.o(i.drv_rtc_compare_set)
    i.drv_rtc_counter_get                    0x000289ae   Section        0  drv_rtc.o(i.drv_rtc_counter_get)
    i.drv_rtc_init                           0x000289b8   Section        0  drv_rtc.o(i.drv_rtc_init)
    i.drv_rtc_irq_trigger                    0x00028a90   Section        0  drv_rtc.o(i.drv_rtc_irq_trigger)
    i.drv_rtc_overflow_enable                0x00028aae   Section        0  drv_rtc.o(i.drv_rtc_overflow_enable)
    i.drv_rtc_overflow_pending               0x00028ab6   Section        0  drv_rtc.o(i.drv_rtc_overflow_pending)
    i.drv_rtc_start                          0x00028abe   Section        0  drv_rtc.o(i.drv_rtc_start)
    i.drv_rtc_stop                           0x00028ac6   Section        0  drv_rtc.o(i.drv_rtc_stop)
    i.drv_rtc_windowed_compare_set           0x00028ace   Section        0  drv_rtc.o(i.drv_rtc_windowed_compare_set)
    i.evt_enable                             0x00028bac   Section        0  drv_rtc.o(i.evt_enable)
    evt_enable                               0x00028bad   Thumb Code    18  drv_rtc.o(i.evt_enable)
    i.evt_pending                            0x00028bbe   Section        0  drv_rtc.o(i.evt_pending)
    evt_pending                              0x00028bbf   Thumb Code    20  drv_rtc.o(i.evt_pending)
    i.fifo_get                               0x00028bd2   Section        0  app_fifo.o(i.fifo_get)
    fifo_get                                 0x00028bd3   Thumb Code    20  app_fifo.o(i.fifo_get)
    i.fifo_put                               0x00028be6   Section        0  app_fifo.o(i.fifo_put)
    fifo_put                                 0x00028be7   Thumb Code    18  app_fifo.o(i.fifo_put)
    i.flag_toggle                            0x00028bf8   Section        0  ble_conn_state.o(i.flag_toggle)
    flag_toggle                              0x00028bf9   Thumb Code    10  ble_conn_state.o(i.flag_toggle)
    i.flags_set                              0x00028c02   Section        0  ble_advertising.o(i.flags_set)
    flags_set                                0x00028c03   Thumb Code    40  ble_advertising.o(i.flags_set)
    i.gatt_evt_handler                       0x00028c2c   Section        0  main.o(i.gatt_evt_handler)
    i.gatt_init                              0x00028c88   Section        0  main.o(i.gatt_init)
    i.get_now                                0x00028cb8   Section        0  app_timer2.o(i.get_now)
    get_now                                  0x00028cb9   Thumb Code    46  app_timer2.o(i.get_now)
    i.gpio_init                              0x00028cec   Section        0  main.o(i.gpio_init)
    gpio_init                                0x00028ced   Thumb Code    78  main.o(i.gpio_init)
    i.in_pin_handler                         0x00028d60   Section        0  main.o(i.in_pin_handler)
    i.instance_get                           0x00028e58   Section        0  ble_conn_params.o(i.instance_get)
    instance_get                             0x00028e59   Thumb Code    14  ble_conn_params.o(i.instance_get)
    i.int_print                              0x00028e6c   Section        0  nrf_fprintf_format.o(i.int_print)
    int_print                                0x00028e6d   Thumb Code   166  nrf_fprintf_format.o(i.int_print)
    i.invalid_packets_omit                   0x00028f12   Section        0  nrf_log_frontend.o(i.invalid_packets_omit)
    invalid_packets_omit                     0x00028f13   Thumb Code    52  nrf_log_frontend.o(i.invalid_packets_omit)
    i.is_conn_params_ok                      0x00028f46   Section        0  ble_conn_params.o(i.is_conn_params_ok)
    is_conn_params_ok                        0x00028f47   Thumb Code    68  ble_conn_params.o(i.is_conn_params_ok)
    i.leds_off                               0x00028f8c   Section        0  bsp.o(i.leds_off)
    leds_off                                 0x00028f8d   Thumb Code    42  bsp.o(i.leds_off)
    i.link_init                              0x00028fbc   Section        0  nrf_ble_gatt.o(i.link_init)
    link_init                                0x00028fbd   Thumb Code    24  nrf_ble_gatt.o(i.link_init)
    i.log_skip                               0x00028fd4   Section        0  nrf_log_frontend.o(i.log_skip)
    log_skip                                 0x00028fd5   Thumb Code   150  nrf_log_frontend.o(i.log_skip)
    i.main                                   0x00029078   Section        0  main.o(i.main)
    i.manuf_specific_data_encode             0x0002923c   Section        0  ble_advdata.o(i.manuf_specific_data_encode)
    manuf_specific_data_encode               0x0002923d   Thumb Code   100  ble_advdata.o(i.manuf_specific_data_encode)
    i.memobj_op                              0x000292a0   Section        0  nrf_memobj.o(i.memobj_op)
    memobj_op                                0x000292a1   Thumb Code   126  nrf_memobj.o(i.memobj_op)
    i.module_idx_get                         0x00029320   Section        0  nrf_log_frontend.o(i.module_idx_get)
    module_idx_get                           0x00029321   Thumb Code    42  nrf_log_frontend.o(i.module_idx_get)
    i.name_encode                            0x00029350   Section        0  ble_advdata.o(i.name_encode)
    name_encode                              0x00029351   Thumb Code   166  ble_advdata.o(i.name_encode)
    i.nrf_atfifo_init                        0x000293f6   Section        0  nrf_atfifo.o(i.nrf_atfifo_init)
    i.nrf_atfifo_item_alloc                  0x0002941c   Section        0  nrf_atfifo.o(i.nrf_atfifo_item_alloc)
    i.nrf_atfifo_item_free                   0x00029432   Section        0  nrf_atfifo.o(i.nrf_atfifo_item_free)
    i.nrf_atfifo_item_get                    0x00029448   Section        0  nrf_atfifo.o(i.nrf_atfifo_item_get)
    i.nrf_atfifo_item_put                    0x0002945e   Section        0  nrf_atfifo.o(i.nrf_atfifo_item_put)
    i.nrf_atflags_clear                      0x00029474   Section        0  nrf_atflags.o(i.nrf_atflags_clear)
    i.nrf_atflags_get                        0x0002948a   Section        0  nrf_atflags.o(i.nrf_atflags_get)
    i.nrf_atflags_set                        0x000294a0   Section        0  nrf_atflags.o(i.nrf_atflags_set)
    i.nrf_atomic_flag_clear_fetch            0x000294b4   Section        0  nrf_atomic.o(i.nrf_atomic_flag_clear_fetch)
    i.nrf_atomic_flag_set                    0x000294ba   Section        0  nrf_atomic.o(i.nrf_atomic_flag_set)
    i.nrf_atomic_u32_add                     0x000294c0   Section        0  nrf_atomic.o(i.nrf_atomic_u32_add)
    i.nrf_atomic_u32_and                     0x000294cc   Section        0  nrf_atomic.o(i.nrf_atomic_u32_and)
    i.nrf_atomic_u32_fetch_and               0x000294d8   Section        0  nrf_atomic.o(i.nrf_atomic_u32_fetch_and)
    i.nrf_atomic_u32_fetch_store             0x000294e2   Section        0  nrf_atomic.o(i.nrf_atomic_u32_fetch_store)
    i.nrf_atomic_u32_or                      0x000294ec   Section        0  nrf_atomic.o(i.nrf_atomic_u32_or)
    i.nrf_atomic_u32_sub                     0x000294f8   Section        0  nrf_atomic.o(i.nrf_atomic_u32_sub)
    i.nrf_balloc_alloc                       0x00029504   Section        0  nrf_balloc.o(i.nrf_balloc_alloc)
    i.nrf_balloc_free                        0x00029548   Section        0  nrf_balloc.o(i.nrf_balloc_free)
    i.nrf_balloc_init                        0x00029578   Section        0  nrf_balloc.o(i.nrf_balloc_init)
    i.nrf_bitmask_bit_is_set                 0x000295a8   Section        0  nrfx_gpiote.o(i.nrf_bitmask_bit_is_set)
    nrf_bitmask_bit_is_set                   0x000295a9   Thumb Code    16  nrfx_gpiote.o(i.nrf_bitmask_bit_is_set)
    i.nrf_ble_gatt_att_mtu_periph_set        0x000295b8   Section        0  nrf_ble_gatt.o(i.nrf_ble_gatt_att_mtu_periph_set)
    i.nrf_ble_gatt_init                      0x000295d0   Section        0  nrf_ble_gatt.o(i.nrf_ble_gatt_init)
    i.nrf_ble_gatt_on_ble_evt                0x000295f4   Section        0  nrf_ble_gatt.o(i.nrf_ble_gatt_on_ble_evt)
    i.nrf_ble_qwr_conn_handle_assign         0x00029744   Section        0  nrf_ble_qwr.o(i.nrf_ble_qwr_conn_handle_assign)
    i.nrf_ble_qwr_init                       0x0002975a   Section        0  nrf_ble_qwr.o(i.nrf_ble_qwr_init)
    i.nrf_ble_qwr_on_ble_evt                 0x0002977e   Section        0  nrf_ble_qwr.o(i.nrf_ble_qwr_on_ble_evt)
    i.nrf_clock_event_check                  0x00029834   Section        0  nrfx_clock.o(i.nrf_clock_event_check)
    nrf_clock_event_check                    0x00029835   Thumb Code    14  nrfx_clock.o(i.nrf_clock_event_check)
    i.nrf_clock_event_clear                  0x00029842   Section        0  nrfx_clock.o(i.nrf_clock_event_clear)
    nrf_clock_event_clear                    0x00029843   Thumb Code    16  nrfx_clock.o(i.nrf_clock_event_clear)
    i.nrf_delay_ms                           0x00029854   Section        0  main.o(i.nrf_delay_ms)
    nrf_delay_ms                             0x00029855   Thumb Code    28  main.o(i.nrf_delay_ms)
    i.nrf_delay_ms                           0x00029874   Section        0  ads1292.o(i.nrf_delay_ms)
    nrf_delay_ms                             0x00029875   Thumb Code    22  ads1292.o(i.nrf_delay_ms)
    i.nrf_drv_clock_init                     0x0002988c   Section        0  nrf_drv_clock.o(i.nrf_drv_clock_init)
    i.nrf_drv_clock_lfclk_release            0x000298cc   Section        0  nrf_drv_clock.o(i.nrf_drv_clock_lfclk_release)
    i.nrf_drv_spi_init                       0x00029900   Section        0  nrf_drv_spi.o(i.nrf_drv_spi_init)
    i.nrf_drv_spi_transfer                   0x00029970   Section        0  ads1292.o(i.nrf_drv_spi_transfer)
    nrf_drv_spi_transfer                     0x00029971   Thumb Code    22  ads1292.o(i.nrf_drv_spi_transfer)
    i.nrf_drv_uart_tx                        0x00029988   Section        0  app_uart_fifo.o(i.nrf_drv_uart_tx)
    nrf_drv_uart_tx                          0x00029989   Thumb Code    26  app_uart_fifo.o(i.nrf_drv_uart_tx)
    i.nrf_fprintf                            0x000299a8   Section        0  nrf_fprintf.o(i.nrf_fprintf)
    i.nrf_fprintf_buffer_flush               0x000299c2   Section        0  nrf_fprintf.o(i.nrf_fprintf_buffer_flush)
    i.nrf_fprintf_fmt                        0x000299da   Section        0  nrf_fprintf_format.o(i.nrf_fprintf_fmt)
    i.nrf_gpio_cfg                           0x00029bb4   Section        0  ads1292.o(i.nrf_gpio_cfg)
    nrf_gpio_cfg                             0x00029bb5   Thumb Code    36  ads1292.o(i.nrf_gpio_cfg)
    i.nrf_gpio_cfg                           0x00029bd8   Section        0  nrfx_gpiote.o(i.nrf_gpio_cfg)
    nrf_gpio_cfg                             0x00029bd9   Thumb Code    36  nrfx_gpiote.o(i.nrf_gpio_cfg)
    i.nrf_gpio_cfg                           0x00029bfc   Section        0  nrfx_spim.o(i.nrf_gpio_cfg)
    nrf_gpio_cfg                             0x00029bfd   Thumb Code    36  nrfx_spim.o(i.nrf_gpio_cfg)
    i.nrf_gpio_cfg_output                    0x00029c20   Section        0  ads1292.o(i.nrf_gpio_cfg_output)
    nrf_gpio_cfg_output                      0x00029c21   Thumb Code    20  ads1292.o(i.nrf_gpio_cfg_output)
    i.nrf_gpio_cfg_output                    0x00029c34   Section        0  led.o(i.nrf_gpio_cfg_output)
    nrf_gpio_cfg_output                      0x00029c35   Thumb Code    16  led.o(i.nrf_gpio_cfg_output)
    i.nrf_gpio_cfg_output                    0x00029c44   Section        0  nrfx_spim.o(i.nrf_gpio_cfg_output)
    nrf_gpio_cfg_output                      0x00029c45   Thumb Code    20  nrfx_spim.o(i.nrf_gpio_cfg_output)
    i.nrf_gpio_cfg_sense_set                 0x00029c58   Section        0  nrfx_gpiote.o(i.nrf_gpio_cfg_sense_set)
    nrf_gpio_cfg_sense_set                   0x00029c59   Thumb Code    34  nrfx_gpiote.o(i.nrf_gpio_cfg_sense_set)
    i.nrf_gpio_latches_read_and_clear        0x00029c7c   Section        0  nrfx_gpiote.o(i.nrf_gpio_latches_read_and_clear)
    nrf_gpio_latches_read_and_clear          0x00029c7d   Thumb Code    38  nrfx_gpiote.o(i.nrf_gpio_latches_read_and_clear)
    i.nrf_gpio_pin_clear                     0x00029ca8   Section        0  nrfx_spim.o(i.nrf_gpio_pin_clear)
    nrf_gpio_pin_clear                       0x00029ca9   Thumb Code    14  nrfx_spim.o(i.nrf_gpio_pin_clear)
    i.nrf_gpio_pin_present_check             0x00029cb6   Section        0  nrfx_gpiote.o(i.nrf_gpio_pin_present_check)
    nrf_gpio_pin_present_check               0x00029cb7   Thumb Code    30  nrfx_gpiote.o(i.nrf_gpio_pin_present_check)
    i.nrf_gpio_pin_set                       0x00029cd4   Section        0  nrfx_spim.o(i.nrf_gpio_pin_set)
    nrf_gpio_pin_set                         0x00029cd5   Thumb Code    14  nrfx_spim.o(i.nrf_gpio_pin_set)
    i.nrf_gpio_pin_toggle                    0x00029ce2   Section        0  main.o(i.nrf_gpio_pin_toggle)
    nrf_gpio_pin_toggle                      0x00029ce3   Thumb Code    28  main.o(i.nrf_gpio_pin_toggle)
    i.nrf_gpio_pin_write                     0x00029cfe   Section        0  boards.o(i.nrf_gpio_pin_write)
    nrf_gpio_pin_write                       0x00029cff   Thumb Code    22  boards.o(i.nrf_gpio_pin_write)
    i.nrf_gpiote_event_clear                 0x00029d14   Section        0  nrfx_gpiote.o(i.nrf_gpiote_event_clear)
    nrf_gpiote_event_clear                   0x00029d15   Thumb Code    16  nrfx_gpiote.o(i.nrf_gpiote_event_clear)
    i.nrf_gpiote_event_is_set                0x00029d28   Section        0  nrfx_gpiote.o(i.nrf_gpiote_event_is_set)
    nrf_gpiote_event_is_set                  0x00029d29   Thumb Code    12  nrfx_gpiote.o(i.nrf_gpiote_event_is_set)
    i.nrf_log_backend_add                    0x00029d38   Section        0  nrf_log_frontend.o(i.nrf_log_backend_add)
    i.nrf_log_backend_rtt_flush              0x00029d94   Section        0  nrf_log_backend_rtt.o(i.nrf_log_backend_rtt_flush)
    nrf_log_backend_rtt_flush                0x00029d95   Thumb Code     2  nrf_log_backend_rtt.o(i.nrf_log_backend_rtt_flush)
    i.nrf_log_backend_rtt_init               0x00029d96   Section        0  nrf_log_backend_rtt.o(i.nrf_log_backend_rtt_init)
    i.nrf_log_backend_rtt_panic_set          0x00029d9a   Section        0  nrf_log_backend_rtt.o(i.nrf_log_backend_rtt_panic_set)
    nrf_log_backend_rtt_panic_set            0x00029d9b   Thumb Code     2  nrf_log_backend_rtt.o(i.nrf_log_backend_rtt_panic_set)
    i.nrf_log_backend_rtt_put                0x00029d9c   Section        0  nrf_log_backend_rtt.o(i.nrf_log_backend_rtt_put)
    nrf_log_backend_rtt_put                  0x00029d9d   Thumb Code    16  nrf_log_backend_rtt.o(i.nrf_log_backend_rtt_put)
    i.nrf_log_backend_serial_put             0x00029db4   Section        0  nrf_log_backend_serial.o(i.nrf_log_backend_serial_put)
    i.nrf_log_color_id_get                   0x00029e78   Section        0  nrf_log_frontend.o(i.nrf_log_color_id_get)
    i.nrf_log_default_backends_init          0x00029ea8   Section        0  nrf_log_default_backends.o(i.nrf_log_default_backends_init)
    i.nrf_log_frontend_dequeue               0x00029ec4   Section        0  nrf_log_frontend.o(i.nrf_log_frontend_dequeue)
    i.nrf_log_frontend_std_0                 0x0002a0f8   Section        0  nrf_log_frontend.o(i.nrf_log_frontend_std_0)
    i.nrf_log_frontend_std_1                 0x0002a100   Section        0  nrf_log_frontend.o(i.nrf_log_frontend_std_1)
    i.nrf_log_frontend_std_2                 0x0002a10e   Section        0  nrf_log_frontend.o(i.nrf_log_frontend_std_2)
    i.nrf_log_hexdump_entry_process          0x0002a120   Section        0  nrf_log_str_formatter.o(i.nrf_log_hexdump_entry_process)
    i.nrf_log_init                           0x0002a1d0   Section        0  nrf_log_frontend.o(i.nrf_log_init)
    i.nrf_log_module_cnt_get                 0x0002a1f8   Section        0  nrf_log_frontend.o(i.nrf_log_module_cnt_get)
    i.nrf_log_module_name_get                0x0002a20c   Section        0  nrf_log_frontend.o(i.nrf_log_module_name_get)
    i.nrf_log_panic                          0x0002a228   Section        0  nrf_log_frontend.o(i.nrf_log_panic)
    i.nrf_log_std_entry_process              0x0002a250   Section        0  nrf_log_str_formatter.o(i.nrf_log_std_entry_process)
    i.nrf_memobj_alloc                       0x0002a306   Section        0  nrf_memobj.o(i.nrf_memobj_alloc)
    i.nrf_memobj_free                        0x0002a366   Section        0  nrf_memobj.o(i.nrf_memobj_free)
    i.nrf_memobj_get                         0x0002a398   Section        0  nrf_memobj.o(i.nrf_memobj_get)
    i.nrf_memobj_pool_init                   0x0002a3a0   Section        0  nrf_memobj.o(i.nrf_memobj_pool_init)
    i.nrf_memobj_put                         0x0002a3a4   Section        0  nrf_memobj.o(i.nrf_memobj_put)
    i.nrf_memobj_read                        0x0002a3c2   Section        0  nrf_memobj.o(i.nrf_memobj_read)
    i.nrf_memobj_write                       0x0002a3d2   Section        0  nrf_memobj.o(i.nrf_memobj_write)
    i.nrf_pwr_mgmt_init                      0x0002a3e4   Section        0  nrf_pwr_mgmt.o(i.nrf_pwr_mgmt_init)
    i.nrf_pwr_mgmt_run                       0x0002a40c   Section        0  nrf_pwr_mgmt.o(i.nrf_pwr_mgmt_run)
    i.nrf_qwr_error_handler                  0x0002a450   Section        0  main.o(i.nrf_qwr_error_handler)
    nrf_qwr_error_handler                    0x0002a451   Thumb Code     4  main.o(i.nrf_qwr_error_handler)
    i.nrf_ringbuf_init                       0x0002a454   Section        0  nrf_ringbuf.o(i.nrf_ringbuf_init)
    i.nrf_rtc_event_clear                    0x0002a470   Section        0  drv_rtc.o(i.nrf_rtc_event_clear)
    nrf_rtc_event_clear                      0x0002a471   Thumb Code    12  drv_rtc.o(i.nrf_rtc_event_clear)
    i.nrf_saadc_buffer_init                  0x0002a47c   Section        0  nrfx_saadc.o(i.nrf_saadc_buffer_init)
    nrf_saadc_buffer_init                    0x0002a47d   Thumb Code    10  nrfx_saadc.o(i.nrf_saadc_buffer_init)
    i.nrf_saadc_event_check                  0x0002a48c   Section        0  nrfx_saadc.o(i.nrf_saadc_event_check)
    nrf_saadc_event_check                    0x0002a48d   Thumb Code    12  nrfx_saadc.o(i.nrf_saadc_event_check)
    i.nrf_saadc_event_clear                  0x0002a49c   Section        0  nrfx_saadc.o(i.nrf_saadc_event_clear)
    nrf_saadc_event_clear                    0x0002a49d   Thumb Code    16  nrfx_saadc.o(i.nrf_saadc_event_clear)
    i.nrf_sdh_ble_app_ram_start_get          0x0002a4b0   Section        0  nrf_sdh_ble.o(i.nrf_sdh_ble_app_ram_start_get)
    i.nrf_sdh_ble_default_cfg_set            0x0002a4c4   Section        0  nrf_sdh_ble.o(i.nrf_sdh_ble_default_cfg_set)
    i.nrf_sdh_ble_enable                     0x0002a5f4   Section        0  nrf_sdh_ble.o(i.nrf_sdh_ble_enable)
    i.nrf_sdh_ble_evts_poll                  0x0002a724   Section        0  nrf_sdh_ble.o(i.nrf_sdh_ble_evts_poll)
    nrf_sdh_ble_evts_poll                    0x0002a725   Thumb Code    88  nrf_sdh_ble.o(i.nrf_sdh_ble_evts_poll)
    i.nrf_sdh_enable_request                 0x0002a784   Section        0  nrf_sdh.o(i.nrf_sdh_enable_request)
    i.nrf_sdh_evts_poll                      0x0002a7f8   Section        0  nrf_sdh.o(i.nrf_sdh_evts_poll)
    i.nrf_sdh_is_enabled                     0x0002a81c   Section        0  nrf_sdh.o(i.nrf_sdh_is_enabled)
    i.nrf_sdh_soc_evts_poll                  0x0002a828   Section        0  nrf_sdh_soc.o(i.nrf_sdh_soc_evts_poll)
    nrf_sdh_soc_evts_poll                    0x0002a829   Thumb Code    56  nrf_sdh_soc.o(i.nrf_sdh_soc_evts_poll)
    i.nrf_section_iter_init                  0x0002a864   Section        0  nrf_section_iter.o(i.nrf_section_iter_init)
    i.nrf_section_iter_item_set              0x0002a86e   Section        0  nrf_section_iter.o(i.nrf_section_iter_item_set)
    nrf_section_iter_item_set                0x0002a86f   Thumb Code    36  nrf_section_iter.o(i.nrf_section_iter_item_set)
    i.nrf_section_iter_next                  0x0002a892   Section        0  nrf_section_iter.o(i.nrf_section_iter_next)
    i.nrf_sortlist_add                       0x0002a8b2   Section        0  nrf_sortlist.o(i.nrf_sortlist_add)
    i.nrf_sortlist_peek                      0x0002a8d4   Section        0  nrf_sortlist.o(i.nrf_sortlist_peek)
    i.nrf_sortlist_pop                       0x0002a8da   Section        0  nrf_sortlist.o(i.nrf_sortlist_pop)
    i.nrf_sortlist_remove                    0x0002a8e8   Section        0  nrf_sortlist.o(i.nrf_sortlist_remove)
    i.nrf_spim_event_check                   0x0002a906   Section        0  nrfx_spim.o(i.nrf_spim_event_check)
    nrf_spim_event_check                     0x0002a907   Thumb Code    10  nrfx_spim.o(i.nrf_spim_event_check)
    i.nrf_strerror_find                      0x0002a910   Section        0  nrf_strerror.o(i.nrf_strerror_find)
    i.nrf_strerror_get                       0x0002a948   Section        0  nrf_strerror.o(i.nrf_strerror_get)
    i.nrf_timer_event_clear                  0x0002a95c   Section        0  nrfx_timer.o(i.nrf_timer_event_clear)
    nrf_timer_event_clear                    0x0002a95d   Thumb Code    12  nrfx_timer.o(i.nrf_timer_event_clear)
    i.nrf_uart_event_check                   0x0002a968   Section        0  nrfx_uart.o(i.nrf_uart_event_check)
    nrf_uart_event_check                     0x0002a969   Thumb Code    10  nrfx_uart.o(i.nrf_uart_event_check)
    i.nrf_uart_event_clear                   0x0002a972   Section        0  nrfx_uart.o(i.nrf_uart_event_clear)
    nrf_uart_event_clear                     0x0002a973   Thumb Code    12  nrfx_uart.o(i.nrf_uart_event_clear)
    i.nrf_uarte_event_check                  0x0002a97e   Section        0  nrfx_uarte.o(i.nrf_uarte_event_check)
    nrf_uarte_event_check                    0x0002a97f   Thumb Code    10  nrfx_uarte.o(i.nrf_uarte_event_check)
    i.nrf_uarte_event_clear                  0x0002a988   Section        0  nrfx_uarte.o(i.nrf_uarte_event_clear)
    nrf_uarte_event_clear                    0x0002a989   Thumb Code    12  nrfx_uarte.o(i.nrf_uarte_event_clear)
    i.nrf_wdt_started                        0x0002a994   Section        0  nrf_drv_clock.o(i.nrf_wdt_started)
    nrf_wdt_started                          0x0002a995   Thumb Code    12  nrf_drv_clock.o(i.nrf_wdt_started)
    i.nrfx_clock_enable                      0x0002a9a4   Section        0  nrfx_clock.o(i.nrfx_clock_enable)
    i.nrfx_clock_init                        0x0002a9d0   Section        0  nrfx_clock.o(i.nrfx_clock_init)
    i.nrfx_clock_lfclk_stop                  0x0002a9f0   Section        0  nrfx_clock.o(i.nrfx_clock_lfclk_stop)
    i.nrfx_coredep_delay_us                  0x0002aa20   Section        0  ads1292.o(i.nrfx_coredep_delay_us)
    nrfx_coredep_delay_us                    0x0002aa21   Thumb Code    12  ads1292.o(i.nrfx_coredep_delay_us)
    i.nrfx_coredep_delay_us                  0x0002aa30   Section        0  drv_rtc.o(i.nrfx_coredep_delay_us)
    nrfx_coredep_delay_us                    0x0002aa31   Thumb Code    12  drv_rtc.o(i.nrfx_coredep_delay_us)
    i.nrfx_gpiote_in_event_enable            0x0002aa40   Section        0  nrfx_gpiote.o(i.nrfx_gpiote_in_event_enable)
    i.nrfx_gpiote_in_init                    0x0002aae0   Section        0  nrfx_gpiote.o(i.nrfx_gpiote_in_init)
    i.nrfx_gpiote_init                       0x0002abac   Section        0  nrfx_gpiote.o(i.nrfx_gpiote_init)
    i.nrfx_is_in_ram                         0x0002ac20   Section        0  nrfx_uarte.o(i.nrfx_is_in_ram)
    nrfx_is_in_ram                           0x0002ac21   Thumb Code    16  nrfx_uarte.o(i.nrfx_is_in_ram)
    i.nrfx_is_in_ram                         0x0002ac30   Section        0  nrfx_spim.o(i.nrfx_is_in_ram)
    nrfx_is_in_ram                           0x0002ac31   Thumb Code    16  nrfx_spim.o(i.nrfx_is_in_ram)
    i.nrfx_prs_acquire                       0x0002ac40   Section        0  nrfx_prs.o(i.nrfx_prs_acquire)
    i.nrfx_spim_init                         0x0002ac7c   Section        0  nrfx_spim.o(i.nrfx_spim_init)
    i.nrfx_spim_xfer                         0x0002ade8   Section        0  nrfx_spim.o(i.nrfx_spim_xfer)
    i.nrfx_timer_compare                     0x0002ae4c   Section        0  nrfx_timer.o(i.nrfx_timer_compare)
    i.nrfx_timer_extended_compare            0x0002ae86   Section        0  nrfx_timer.o(i.nrfx_timer_extended_compare)
    i.nrfx_timer_init                        0x0002aeb0   Section        0  nrfx_timer.o(i.nrfx_timer_init)
    i.nrfx_uart_tx                           0x0002af70   Section        0  nrfx_uart.o(i.nrfx_uart_tx)
    i.nrfx_uart_tx_in_progress               0x0002b018   Section        0  nrfx_uart.o(i.nrfx_uart_tx_in_progress)
    i.nrfx_uarte_tx                          0x0002b038   Section        0  nrfx_uarte.o(i.nrfx_uarte_tx)
    i.nrfx_uarte_tx_in_progress              0x0002b0e4   Section        0  nrfx_uarte.o(i.nrfx_uarte_tx_in_progress)
    i.nus_data_handler                       0x0002b100   Section        0  main.o(i.nus_data_handler)
    nus_data_handler                         0x0002b101   Thumb Code   312  main.o(i.nus_data_handler)
    i.on_adv_evt                             0x0002b24c   Section        0  main.o(i.on_adv_evt)
    on_adv_evt                               0x0002b24d   Thumb Code    36  main.o(i.on_adv_evt)
    i.on_conn_params_evt                     0x0002b270   Section        0  main.o(i.on_conn_params_evt)
    on_conn_params_evt                       0x0002b271   Thumb Code    24  main.o(i.on_conn_params_evt)
    i.on_connect                             0x0002b28c   Section        0  ble_nus.o(i.on_connect)
    on_connect                               0x0002b28d   Thumb Code   106  ble_nus.o(i.on_connect)
    i.on_connected_evt                       0x0002b2f8   Section        0  nrf_ble_gatt.o(i.on_connected_evt)
    on_connected_evt                         0x0002b2f9   Thumb Code   106  nrf_ble_gatt.o(i.on_connected_evt)
    i.on_exchange_mtu_request_evt            0x0002b3a0   Section        0  nrf_ble_gatt.o(i.on_exchange_mtu_request_evt)
    on_exchange_mtu_request_evt              0x0002b3a1   Thumb Code   110  nrf_ble_gatt.o(i.on_exchange_mtu_request_evt)
    i.on_write                               0x0002b44c   Section        0  ble_nus.o(i.on_write)
    on_write                                 0x0002b44d   Thumb Code   132  ble_nus.o(i.on_write)
    i.phy_is_valid                           0x0002b4d0   Section        0  ble_advertising.o(i.phy_is_valid)
    phy_is_valid                             0x0002b4d1   Thumb Code    18  ble_advertising.o(i.phy_is_valid)
    i.pin_configured_set                     0x0002b4e4   Section        0  nrfx_gpiote.o(i.pin_configured_set)
    pin_configured_set                       0x0002b4e5   Thumb Code    22  nrfx_gpiote.o(i.pin_configured_set)
    i.pin_in_use_by_port                     0x0002b500   Section        0  nrfx_gpiote.o(i.pin_in_use_by_port)
    pin_in_use_by_port                       0x0002b501   Thumb Code    20  nrfx_gpiote.o(i.pin_in_use_by_port)
    i.pin_in_use_by_te                       0x0002b518   Section        0  nrfx_gpiote.o(i.pin_in_use_by_te)
    pin_in_use_by_te                         0x0002b519   Thumb Code    20  nrfx_gpiote.o(i.pin_in_use_by_te)
    i.port_event_handle                      0x0002b530   Section        0  nrfx_gpiote.o(i.port_event_handle)
    port_event_handle                        0x0002b531   Thumb Code   176  nrfx_gpiote.o(i.port_event_handle)
    i.port_handler_polarity_get              0x0002b5e4   Section        0  nrfx_gpiote.o(i.port_handler_polarity_get)
    port_handler_polarity_get                0x0002b5e5   Thumb Code    12  nrfx_gpiote.o(i.port_handler_polarity_get)
    i.postfix_process                        0x0002b5f4   Section        0  nrf_log_str_formatter.o(i.postfix_process)
    postfix_process                          0x0002b5f5   Thumb Code    48  nrf_log_str_formatter.o(i.postfix_process)
    i.prefix_process                         0x0002b630   Section        0  nrf_log_str_formatter.o(i.prefix_process)
    prefix_process                           0x0002b631   Thumb Code    90  nrf_log_str_formatter.o(i.prefix_process)
    i.prs_box_get                            0x0002b6c8   Section        0  nrfx_prs.o(i.prs_box_get)
    prs_box_get                              0x0002b6c9   Thumb Code    14  nrfx_prs.o(i.prs_box_get)
    i.rtc_irq                                0x0002b6e0   Section        0  app_timer2.o(i.rtc_irq)
    rtc_irq                                  0x0002b6e1   Thumb Code    88  app_timer2.o(i.rtc_irq)
    i.rtc_schedule                           0x0002b73c   Section        0  app_timer2.o(i.rtc_schedule)
    rtc_schedule                             0x0002b73d   Thumb Code    92  app_timer2.o(i.rtc_schedule)
    i.rtc_update                             0x0002b7a0   Section        0  app_timer2.o(i.rtc_update)
    rtc_update                               0x0002b7a1   Thumb Code   108  app_timer2.o(i.rtc_update)
    i.sd_state_evt_handler                   0x0002b814   Section        0  nrf_drv_clock.o(i.sd_state_evt_handler)
    sd_state_evt_handler                     0x0002b815   Thumb Code    84  nrf_drv_clock.o(i.sd_state_evt_handler)
    i.sdh_request_observer_notify            0x0002b86c   Section        0  nrf_sdh.o(i.sdh_request_observer_notify)
    sdh_request_observer_notify              0x0002b86d   Thumb Code    44  nrf_sdh.o(i.sdh_request_observer_notify)
    i.sdh_state_observer_notify              0x0002b89c   Section        0  nrf_sdh.o(i.sdh_state_observer_notify)
    sdh_state_observer_notify                0x0002b89d   Thumb Code    38  nrf_sdh.o(i.sdh_state_observer_notify)
    i.send_error_evt                         0x0002b8c8   Section        0  ble_conn_params.o(i.send_error_evt)
    send_error_evt                           0x0002b8c9   Thumb Code    12  ble_conn_params.o(i.send_error_evt)
    i.serial_tx                              0x0002b8d8   Section        0  nrf_log_backend_rtt.o(i.serial_tx)
    serial_tx                                0x0002b8d9   Thumb Code    70  nrf_log_backend_rtt.o(i.serial_tx)
    i.service_data_encode                    0x0002b928   Section        0  ble_advdata.o(i.service_data_encode)
    service_data_encode                      0x0002b929   Thumb Code   136  ble_advdata.o(i.service_data_encode)
    i.set_security_req                       0x0002b9b0   Section        0  ble_srv_common.o(i.set_security_req)
    set_security_req                         0x0002b9b1   Thumb Code    48  ble_srv_common.o(i.set_security_req)
    i.sleep_mode_enter                       0x0002b9e0   Section        0  main.o(i.sleep_mode_enter)
    sleep_mode_enter                         0x0002b9e1   Thumb Code    40  main.o(i.sleep_mode_enter)
    i.soc_evt_handler                        0x0002ba08   Section        0  nrf_drv_clock.o(i.soc_evt_handler)
    soc_evt_handler                          0x0002ba09   Thumb Code    18  nrf_drv_clock.o(i.soc_evt_handler)
    i.softdevices_evt_irq_enable             0x0002ba20   Section        0  nrf_sdh.o(i.softdevices_evt_irq_enable)
    softdevices_evt_irq_enable               0x0002ba21   Thumb Code    80  nrf_sdh.o(i.softdevices_evt_irq_enable)
    i.sortlist_pop                           0x0002ba78   Section        0  app_timer2.o(i.sortlist_pop)
    sortlist_pop                             0x0002ba79   Thumb Code     6  app_timer2.o(i.sortlist_pop)
    i.spi_event_handler                      0x0002ba84   Section        0  ads1292.o(i.spi_event_handler)
    spi_event_handler                        0x0002ba85   Thumb Code     8  ads1292.o(i.spi_event_handler)
    i.spim_evt_handler                       0x0002ba90   Section        0  nrf_drv_spi.o(i.spim_evt_handler)
    spim_evt_handler                         0x0002ba91   Thumb Code    52  nrf_drv_spi.o(i.spim_evt_handler)
    i.spim_xfer                              0x0002bac8   Section        0  nrfx_spim.o(i.spim_xfer)
    spim_xfer                                0x0002bac9   Thumb Code   188  nrfx_spim.o(i.spim_xfer)
    i.std_n                                  0x0002bb84   Section        0  nrf_log_frontend.o(i.std_n)
    std_n                                    0x0002bb85   Thumb Code   140  nrf_log_frontend.o(i.std_n)
    i.timer_data_event_handler               0x0002bc18   Section        0  main.o(i.timer_data_event_handler)
    i.timer_expire                           0x0002bc1c   Section        0  app_timer2.o(i.timer_expire)
    timer_expire                             0x0002bc1d   Thumb Code    80  app_timer2.o(i.timer_expire)
    i.timer_req_process                      0x0002bc74   Section        0  app_timer2.o(i.timer_req_process)
    timer_req_process                        0x0002bc75   Thumb Code   110  app_timer2.o(i.timer_req_process)
    i.timer_req_schedule                     0x0002bcec   Section        0  app_timer2.o(i.timer_req_schedule)
    timer_req_schedule                       0x0002bced   Thumb Code    46  app_timer2.o(i.timer_req_schedule)
    i.timers_init                            0x0002bd24   Section        0  main.o(i.timers_init)
    timers_init                              0x0002bd25   Thumb Code   112  main.o(i.timers_init)
    i.tx_byte                                0x0002bdb8   Section        0  nrfx_uart.o(i.tx_byte)
    tx_byte                                  0x0002bdb9   Thumb Code    32  nrfx_uart.o(i.tx_byte)
    i.uint16_encode                          0x0002bdd8   Section        0  ble_advdata.o(i.uint16_encode)
    uint16_encode                            0x0002bdd9   Thumb Code    10  ble_advdata.o(i.uint16_encode)
    i.unsigned_print                         0x0002bde4   Section        0  nrf_fprintf_format.o(i.unsigned_print)
    unsigned_print                           0x0002bde5   Thumb Code   176  nrf_fprintf_format.o(i.unsigned_print)
    i.update_timeout_handler                 0x0002be98   Section        0  ble_conn_params.o(i.update_timeout_handler)
    update_timeout_handler                   0x0002be99   Thumb Code    98  ble_conn_params.o(i.update_timeout_handler)
    i.use_whitelist                          0x0002bf00   Section        0  ble_advertising.o(i.use_whitelist)
    use_whitelist                            0x0002bf01   Thumb Code    24  ble_advertising.o(i.use_whitelist)
    i.user_mem_reply                         0x0002bf18   Section        0  nrf_ble_qwr.o(i.user_mem_reply)
    user_mem_reply                           0x0002bf19   Thumb Code    36  nrf_ble_qwr.o(i.user_mem_reply)
    i.uuid_list_encode                       0x0002bf3c   Section        0  ble_advdata.o(i.uuid_list_encode)
    uuid_list_encode                         0x0002bf3d   Thumb Code    48  ble_advdata.o(i.uuid_list_encode)
    i.uuid_list_sized_encode                 0x0002bf6c   Section        0  ble_advdata.o(i.uuid_list_sized_encode)
    uuid_list_sized_encode                   0x0002bf6d   Thumb Code   158  ble_advdata.o(i.uuid_list_sized_encode)
    i.wakeup_button_cfg                      0x0002c00a   Section        0  bsp.o(i.wakeup_button_cfg)
    wakeup_button_cfg                        0x0002c00b   Thumb Code    58  bsp.o(i.wakeup_button_cfg)
    .constdata                               0x0002c050   Section       40  main.o(.constdata)
    delay_machine_code                       0x0002c060   Data           6  main.o(.constdata)
    m_timer                                  0x0002c068   Data           8  main.o(.constdata)
    .constdata                               0x0002c080   Section       40  ads1292.o(.constdata)
    delay_machine_code                       0x0002c080   Data           6  ads1292.o(.constdata)
    spi                                      0x0002c088   Data          16  ads1292.o(.constdata)
    .constdata                               0x0002c0a8   Section        8  boards.o(.constdata)
    m_board_led_list                         0x0002c0a8   Data           4  boards.o(.constdata)
    m_board_btn_list                         0x0002c0ac   Data           4  boards.o(.constdata)
    .constdata                               0x0002c0b0   Section       44  bsp.o(.constdata)
    m_bsp_leds_tmr                           0x0002c0b0   Data           4  bsp.o(.constdata)
    m_bsp_alert_tmr                          0x0002c0b4   Data           4  bsp.o(.constdata)
    m_bsp_button_tmr                         0x0002c0b8   Data           4  bsp.o(.constdata)
    app_buttons                              0x0002c0bc   Data          32  bsp.o(.constdata)
    .constdata                               0x0002c0dc   Section       22  nrf_ble_gatt.o(.constdata)
    .constdata                               0x0002c0f4   Section       16  ble_nus.o(.constdata)
    .constdata                               0x0002c104   Section        4  nrfx_gpiote.o(.constdata)
    .constdata                               0x0002c108   Section        4  nrfx_spim.o(.constdata)
    irq_handlers                             0x0002c108   Data           4  nrfx_spim.o(.constdata)
    .constdata                               0x0002c10c   Section       16  nrf_drv_spi.o(.constdata)
    .constdata                               0x0002c11c   Section       20  app_timer2.o(.constdata)
    m_req_fifo                               0x0002c11c   Data           4  app_timer2.o(.constdata)
    m_app_timer_sortlist                     0x0002c124   Data          12  app_timer2.o(.constdata)
    .constdata                               0x0002c130   Section        6  drv_rtc.o(.constdata)
    delay_machine_code                       0x0002c130   Data           6  drv_rtc.o(.constdata)
    .constdata                               0x0002c136   Section       16  nrf_fprintf_format.o(.constdata)
    _aV2C                                    0x0002c136   Data          16  nrf_fprintf_format.o(.constdata)
    .constdata                               0x0002c148   Section       24  nrf_pwr_mgmt.o(.constdata)
    pwr_mgmt_data_array                      0x0002c148   Data          24  nrf_pwr_mgmt.o(.constdata)
    .constdata                               0x0002c160   Section       12  nrf_pwr_mgmt.o(.constdata)
    pwr_mgmt_data                            0x0002c160   Data          12  nrf_pwr_mgmt.o(.constdata)
    .constdata                               0x0002c16c   Section      316  nrf_strerror.o(.constdata)
    m_unknown_str                            0x0002c16c   Data          19  nrf_strerror.o(.constdata)
    nrf_strerror_array                       0x0002c180   Data         296  nrf_strerror.o(.constdata)
    .constdata                               0x0002c2a8   Section       12  nrf_log_backend_rtt.o(.constdata)
    .constdata                               0x0002c2c0   Section        6  nrf_log_backend_rtt.o(.constdata)
    delay_machine_code                       0x0002c2c0   Data           6  nrf_log_backend_rtt.o(.constdata)
    .constdata                               0x0002c2c8   Section       12  nrf_log_frontend.o(.constdata)
    m_log_push_ringbuf                       0x0002c2c8   Data          12  nrf_log_frontend.o(.constdata)
    .constdata                               0x0002c2d4   Section       16  nrf_sdh.o(.constdata)
    sdh_req_observers_array                  0x0002c2d4   Data          16  nrf_sdh.o(.constdata)
    .constdata                               0x0002c2e4   Section       40  nrf_sdh.o(.constdata)
    sdh_req_observers                        0x0002c2e8   Data          12  nrf_sdh.o(.constdata)
    sdh_state_observers                      0x0002c2f4   Data          12  nrf_sdh.o(.constdata)
    sdh_stack_observers                      0x0002c300   Data          12  nrf_sdh.o(.constdata)
    .constdata                               0x0002c30c   Section       16  nrf_sdh.o(.constdata)
    sdh_state_observers_array                0x0002c30c   Data          16  nrf_sdh.o(.constdata)
    .constdata                               0x0002c31c   Section       16  nrf_sdh.o(.constdata)
    sdh_stack_observers_array                0x0002c31c   Data          16  nrf_sdh.o(.constdata)
    .constdata                               0x0002c32c   Section       32  nrf_sdh_ble.o(.constdata)
    sdh_ble_observers_array                  0x0002c32c   Data          32  nrf_sdh_ble.o(.constdata)
    .constdata                               0x0002c34c   Section       16  nrf_sdh_ble.o(.constdata)
    sdh_ble_observers                        0x0002c350   Data          12  nrf_sdh_ble.o(.constdata)
    .constdata                               0x0002c35c   Section       16  nrf_sdh_soc.o(.constdata)
    sdh_soc_observers_array                  0x0002c35c   Data          16  nrf_sdh_soc.o(.constdata)
    .constdata                               0x0002c36c   Section       12  nrf_sdh_soc.o(.constdata)
    sdh_soc_observers                        0x0002c36c   Data          12  nrf_sdh_soc.o(.constdata)
    .constdata                               0x0002c378   Section      129  ctype_o.o(.constdata)
    .constdata                               0x0002c3fc   Section        4  ctype_o.o(.constdata)
    table                                    0x0002c3fc   Data           4  ctype_o.o(.constdata)
    .conststring                             0x0002c400   Section      162  nrf_ble_gatt.o(.conststring)
    .conststring                             0x0002c4a4   Section     1003  nrf_strerror.o(.conststring)
    .conststring                             0x0002c890   Section      102  nrf_log_str_formatter.o(.conststring)
    .conststring                             0x0002c8f8   Section      463  nrf_sdh_ble.o(.conststring)
    .conststrlit                             0x0002cac8   Section       13  nrf_ble_gatt.o(.conststrlit)
    .conststrlit                             0x0002cad8   Section        8  ble_nus.o(.conststrlit)
    .conststrlit                             0x0002cae0   Section        6  nrf_drv_clock.o(.conststrlit)
    .conststrlit                             0x0002cae8   Section        6  nrfx_clock.o(.conststrlit)
    .conststrlit                             0x0002caf0   Section        7  nrfx_gpiote.o(.conststrlit)
    .conststrlit                             0x0002caf8   Section        4  nrfx_prs.o(.conststrlit)
    .conststrlit                             0x0002cafc   Section        5  nrfx_uart.o(.conststrlit)
    .conststrlit                             0x0002cb04   Section        6  nrfx_uarte.o(.conststrlit)
    .conststrlit                             0x0002cb0c   Section        4  nrfx_ppi.o(.conststrlit)
    .conststrlit                             0x0002cb10   Section        6  nrfx_saadc.o(.conststrlit)
    .conststrlit                             0x0002cb18   Section        6  nrfx_timer.o(.conststrlit)
    .conststrlit                             0x0002cb20   Section        5  nrfx_spim.o(.conststrlit)
    .conststrlit                             0x0002cb28   Section       11  app_button.o(.conststrlit)
    .conststrlit                             0x0002cb34   Section       10  app_timer2.o(.conststrlit)
    .conststrlit                             0x0002cb40   Section        9  nrf_pwr_mgmt.o(.conststrlit)
    .conststrlit                             0x0002cb4c   Section        9  nrf_sortlist.o(.conststrlit)
    .conststrlit                             0x0002cb58   Section       16  nrf_log_default_backends.o(.conststrlit)
    .conststrlit                             0x0002cb68   Section        4  nrf_log_frontend.o(.conststrlit)
    .conststrlit                             0x0002cb6c   Section        8  nrf_sdh.o(.conststrlit)
    .conststrlit                             0x0002cb74   Section       12  nrf_sdh_ble.o(.conststrlit)
    .conststrlit                             0x0002cb80   Section       12  nrf_sdh_soc.o(.conststrlit)
    log_backends                             0x0002cbac   Section       16  nrf_log_default_backends.o(log_backends)
    __tagsym$$used                           0x0002cbac   Number         0  nrf_log_default_backends.o(log_backends)
    rtt_log_backend                          0x0002cbac   Data          16  nrf_log_default_backends.o(log_backends)
    log_const_data                           0x0002cbbc   Section        8  nrf_ble_gatt.o(log_const_data)
    __tagsym$$used                           0x0002cbbc   Number         0  nrf_ble_gatt.o(log_const_data)
    log_const_data                           0x0002cbc4   Section        8  ble_nus.o(log_const_data)
    __tagsym$$used                           0x0002cbc4   Number         0  ble_nus.o(log_const_data)
    log_const_data                           0x0002cbcc   Section        8  nrf_drv_clock.o(log_const_data)
    __tagsym$$used                           0x0002cbcc   Number         0  nrf_drv_clock.o(log_const_data)
    log_const_data                           0x0002cbd4   Section        8  nrfx_clock.o(log_const_data)
    __tagsym$$used                           0x0002cbd4   Number         0  nrfx_clock.o(log_const_data)
    log_const_data                           0x0002cbdc   Section        8  nrfx_gpiote.o(log_const_data)
    __tagsym$$used                           0x0002cbdc   Number         0  nrfx_gpiote.o(log_const_data)
    log_const_data                           0x0002cbe4   Section        8  nrfx_prs.o(log_const_data)
    __tagsym$$used                           0x0002cbe4   Number         0  nrfx_prs.o(log_const_data)
    log_const_data                           0x0002cbec   Section        8  nrfx_uart.o(log_const_data)
    __tagsym$$used                           0x0002cbec   Number         0  nrfx_uart.o(log_const_data)
    log_const_data                           0x0002cbf4   Section        8  nrfx_uarte.o(log_const_data)
    __tagsym$$used                           0x0002cbf4   Number         0  nrfx_uarte.o(log_const_data)
    log_const_data                           0x0002cbfc   Section        8  nrfx_ppi.o(log_const_data)
    __tagsym$$used                           0x0002cbfc   Number         0  nrfx_ppi.o(log_const_data)
    log_const_data                           0x0002cc04   Section        8  nrfx_saadc.o(log_const_data)
    __tagsym$$used                           0x0002cc04   Number         0  nrfx_saadc.o(log_const_data)
    log_const_data                           0x0002cc0c   Section        8  nrfx_timer.o(log_const_data)
    __tagsym$$used                           0x0002cc0c   Number         0  nrfx_timer.o(log_const_data)
    log_const_data                           0x0002cc14   Section        8  nrfx_spim.o(log_const_data)
    __tagsym$$used                           0x0002cc14   Number         0  nrfx_spim.o(log_const_data)
    log_const_data                           0x0002cc1c   Section        8  app_button.o(log_const_data)
    __tagsym$$used                           0x0002cc1c   Number         0  app_button.o(log_const_data)
    log_const_data                           0x0002cc24   Section        8  app_timer2.o(log_const_data)
    __tagsym$$used                           0x0002cc24   Number         0  app_timer2.o(log_const_data)
    log_const_data                           0x0002cc2c   Section        8  nrf_pwr_mgmt.o(log_const_data)
    __tagsym$$used                           0x0002cc2c   Number         0  nrf_pwr_mgmt.o(log_const_data)
    log_const_data                           0x0002cc34   Section        8  nrf_sortlist.o(log_const_data)
    __tagsym$$used                           0x0002cc34   Number         0  nrf_sortlist.o(log_const_data)
    log_const_data                           0x0002cc3c   Section        8  nrf_log_frontend.o(log_const_data)
    __tagsym$$used                           0x0002cc3c   Number         0  nrf_log_frontend.o(log_const_data)
    log_const_data                           0x0002cc44   Section        8  nrf_sdh.o(log_const_data)
    __tagsym$$used                           0x0002cc44   Number         0  nrf_sdh.o(log_const_data)
    log_const_data                           0x0002cc4c   Section        8  nrf_sdh_ble.o(log_const_data)
    __tagsym$$used                           0x0002cc4c   Number         0  nrf_sdh_ble.o(log_const_data)
    log_const_data                           0x0002cc54   Section        8  nrf_sdh_soc.o(log_const_data)
    __tagsym$$used                           0x0002cc54   Number         0  nrf_sdh_soc.o(log_const_data)
    nrf_balloc                               0x0002cc5c   Section       20  nrf_log_frontend.o(nrf_balloc)
    __tagsym$$used                           0x0002cc5c   Number         0  nrf_log_frontend.o(nrf_balloc)
    sdh_ble_observers0                       0x0002cc70   Section        8  ble_conn_state.o(sdh_ble_observers0)
    __tagsym$$used                           0x0002cc70   Number         0  ble_conn_state.o(sdh_ble_observers0)
    m_ble_evt_observer                       0x0002cc70   Data           8  ble_conn_state.o(sdh_ble_observers0)
    sdh_ble_observers1                       0x0002cc78   Section       16  main.o(sdh_ble_observers1)
    __tagsym$$used                           0x0002cc78   Number         0  main.o(sdh_ble_observers1)
    m_gatt_obs                               0x0002cc78   Data           8  main.o(sdh_ble_observers1)
    __tagsym$$used                           0x0002cc80   Number         0  main.o(sdh_ble_observers1)
    m_advertising_ble_obs                    0x0002cc80   Data           8  main.o(sdh_ble_observers1)
    sdh_ble_observers1                       0x0002cc88   Section        8  bsp_btn_ble.o(sdh_ble_observers1)
    __tagsym$$used                           0x0002cc88   Number         0  bsp_btn_ble.o(sdh_ble_observers1)
    m_ble_observer                           0x0002cc88   Data           8  bsp_btn_ble.o(sdh_ble_observers1)
    sdh_ble_observers1                       0x0002cc90   Section        8  ble_conn_params.o(sdh_ble_observers1)
    __tagsym$$used                           0x0002cc90   Number         0  ble_conn_params.o(sdh_ble_observers1)
    m_ble_observer                           0x0002cc90   Data           8  ble_conn_params.o(sdh_ble_observers1)
    sdh_ble_observers2                       0x0002cc98   Section       16  main.o(sdh_ble_observers2)
    __tagsym$$used                           0x0002cc98   Number         0  main.o(sdh_ble_observers2)
    m_nus_obs                                0x0002cc98   Data           8  main.o(sdh_ble_observers2)
    __tagsym$$used                           0x0002cca0   Number         0  main.o(sdh_ble_observers2)
    m_qwr_obs                                0x0002cca0   Data           8  main.o(sdh_ble_observers2)
    sdh_ble_observers3                       0x0002cca8   Section        8  main.o(sdh_ble_observers3)
    __tagsym$$used                           0x0002cca8   Number         0  main.o(sdh_ble_observers3)
    m_ble_observer                           0x0002cca8   Data           8  main.o(sdh_ble_observers3)
    sdh_soc_observers0                       0x0002ccb0   Section        8  nrf_drv_clock.o(sdh_soc_observers0)
    __tagsym$$used                           0x0002ccb0   Number         0  nrf_drv_clock.o(sdh_soc_observers0)
    m_soc_evt_observer                       0x0002ccb0   Data           8  nrf_drv_clock.o(sdh_soc_observers0)
    sdh_stack_observers0                     0x0002ccb8   Section        8  nrf_sdh_ble.o(sdh_stack_observers0)
    __tagsym$$used                           0x0002ccb8   Number         0  nrf_sdh_ble.o(sdh_stack_observers0)
    m_nrf_sdh_ble_evts_poll                  0x0002ccb8   Data           8  nrf_sdh_ble.o(sdh_stack_observers0)
    sdh_stack_observers0                     0x0002ccc0   Section        8  nrf_sdh_soc.o(sdh_stack_observers0)
    __tagsym$$used                           0x0002ccc0   Number         0  nrf_sdh_soc.o(sdh_stack_observers0)
    m_nrf_sdh_soc_evts_poll                  0x0002ccc0   Data           8  nrf_sdh_soc.o(sdh_stack_observers0)
    sdh_state_observers0                     0x0002ccc8   Section        8  nrf_drv_clock.o(sdh_state_observers0)
    __tagsym$$used                           0x0002ccc8   Number         0  nrf_drv_clock.o(sdh_state_observers0)
    m_sd_state_observer                      0x0002ccc8   Data           8  nrf_drv_clock.o(sdh_state_observers0)
    .data                                    0x20002ad8   Section        8  main.o(.data)
    m_nus_link_ctx_storage                   0x20002ad8   Data           8  main.o(.data)
    .data                                    0x20002ae0   Section       68  main.o(.data)
    g_data_transfer_enabled                  0x20002ae0   Data           1  main.o(.data)
    ledflag                                  0x20002ae2   Data           1  main.o(.data)
    index                                    0x20002ae3   Data           1  main.o(.data)
    m_ppi_channel                            0x20002ae4   Data           1  main.o(.data)
    m_conn_handle                            0x20002ae6   Data           2  main.o(.data)
    m_ble_nus_max_data_len                   0x20002ae8   Data           2  main.o(.data)
    m_adv_uuids                              0x20002aec   Data           4  main.o(.data)
    interrupt_count                          0x20002af4   Data           4  main.o(.data)
    timestamp                                0x20002af8   Data           4  main.o(.data)
    timenumber                               0x20002b00   Data           8  main.o(.data)
    m_nus                                    0x20002b08   Data          28  main.o(.data)
    .data                                    0x20002b24   Section        4  main.o(.data)
    m_nus_link_ctx_storage_ctx_data_pool     0x20002b24   Data           4  main.o(.data)
    .data                                    0x20002b28   Section        1  ads1292.o(.data)
    spi_xfer_done                            0x20002b28   Data           1  ads1292.o(.data)
    .data                                    0x20002b2c   Section       16  bsp.o(.data)
    m_stable_state                           0x20002b2c   Data           1  bsp.o(.data)
    m_leds_clear                             0x20002b2d   Data           1  bsp.o(.data)
    m_alert_on                               0x20002b2e   Data           1  bsp.o(.data)
    current_long_push_pin_no                 0x20002b2f   Data           1  bsp.o(.data)
    m_indication_type                        0x20002b30   Data           4  bsp.o(.data)
    m_registered_callback                    0x20002b34   Data           4  bsp.o(.data)
    release_event_at_push                    0x20002b38   Data           4  bsp.o(.data)
    .data                                    0x20002b3c   Section        8  bsp_btn_ble.o(.data)
    m_error_handler                          0x20002b3c   Data           4  bsp_btn_ble.o(.data)
    m_num_connections                        0x20002b40   Data           4  bsp_btn_ble.o(.data)
    .data                                    0x20002b44   Section        8  ble_conn_params.o(.data)
    m_preferred_conn_params                  0x20002b44   Data           8  ble_conn_params.o(.data)
    .data                                    0x20002b4c   Section       12  nrf_drv_uart.o(.data)
    m_handlers                               0x20002b50   Data           4  nrf_drv_uart.o(.data)
    m_contexts                               0x20002b54   Data           4  nrf_drv_uart.o(.data)
    .data                                    0x20002b58   Section        8  nrfx_clock.o(.data)
    m_clock_cb                               0x20002b58   Data           8  nrfx_clock.o(.data)
    .data                                    0x20002b60   Section        8  nrfx_prs.o(.data)
    m_prs_box_4                              0x20002b60   Data           8  nrfx_prs.o(.data)
    .data                                    0x20002b68   Section       32  app_timer2.o(.data)
    m_global_active                          0x20002b68   Data           1  app_timer2.o(.data)
    mp_active_timer                          0x20002b6c   Data           4  app_timer2.o(.data)
    m_rtc_inst                               0x20002b70   Data           8  app_timer2.o(.data)
    m_base_counter                           0x20002b78   Data           8  app_timer2.o(.data)
    m_stamp64                                0x20002b80   Data           8  app_timer2.o(.data)
    .data                                    0x20002b88   Section        4  app_timer2.o(.data)
    m_app_timer_sortlist_sortlist_cb         0x20002b88   Data           4  app_timer2.o(.data)
    .data                                    0x20002b8c   Section       28  app_uart_fifo.o(.data)
    tx_buffer                                0x20002b8c   Data           1  app_uart_fifo.o(.data)
    rx_buffer                                0x20002b8d   Data           1  app_uart_fifo.o(.data)
    m_rx_ovf                                 0x20002b8e   Data           1  app_uart_fifo.o(.data)
    m_event_handler                          0x20002b90   Data           4  app_uart_fifo.o(.data)
    app_uart_inst                            0x20002b94   Data          20  app_uart_fifo.o(.data)
    .data                                    0x20002ba8   Section       12  drv_rtc.o(.data)
    m_handlers                               0x20002ba8   Data           4  drv_rtc.o(.data)
    m_cb                                     0x20002bac   Data           8  drv_rtc.o(.data)
    .data                                    0x20002bb4   Section        8  nrf_pwr_mgmt.o(.data)
    m_pwr_mgmt_evt                           0x20002bb4   Data           1  nrf_pwr_mgmt.o(.data)
    m_shutdown_started                       0x20002bb5   Data           1  nrf_pwr_mgmt.o(.data)
    m_sysoff_mtx                             0x20002bb8   Data           4  nrf_pwr_mgmt.o(.data)
    .data                                    0x20002bbc   Section        1  nrf_log_backend_rtt.o(.data)
    m_host_present                           0x20002bbc   Data           1  nrf_log_backend_rtt.o(.data)
    .data                                    0x20002bc0   Section        8  nrf_log_default_backends.o(.data)
    log_backend_cb_rtt_log_backend           0x20002bc0   Data           8  nrf_log_default_backends.o(.data)
    .data                                    0x20002bc8   Section        4  nrf_log_frontend.o(.data)
    m_buffer_mask                            0x20002bc8   Data           4  nrf_log_frontend.o(.data)
    .data                                    0x20002bcc   Section        8  nrf_log_frontend.o(.data)
    log_mempool_nrf_balloc_pool_stack        0x20002bcc   Data           8  nrf_log_frontend.o(.data)
    .data                                    0x20002bd4   Section        8  nrf_log_frontend.o(.data)
    log_mempool_nrf_balloc_cb                0x20002bd4   Data           8  nrf_log_frontend.o(.data)
    .data                                    0x20002bdc   Section       64  nrf_log_str_formatter.o(.data)
    m_freq                                   0x20002bdc   Data           4  nrf_log_str_formatter.o(.data)
    m_timestamp_div                          0x20002be0   Data           4  nrf_log_str_formatter.o(.data)
    severity_names                           0x20002be4   Data          20  nrf_log_str_formatter.o(.data)
    m_colors                                 0x20002bf8   Data          36  nrf_log_str_formatter.o(.data)
    .data                                    0x20002c1c   Section        3  nrf_sdh.o(.data)
    m_nrf_sdh_enabled                        0x20002c1c   Data           1  nrf_sdh.o(.data)
    m_nrf_sdh_suspended                      0x20002c1d   Data           1  nrf_sdh.o(.data)
    m_nrf_sdh_continue                       0x20002c1e   Data           1  nrf_sdh.o(.data)
    .data                                    0x20002c1f   Section        1  nrf_sdh_ble.o(.data)
    m_stack_is_enabled                       0x20002c1f   Data           1  nrf_sdh_ble.o(.data)
    .data                                    0x20002c20   Section        4  system_nrf52.o(.data)
    __tagsym$$used                           0x20002c20   Number         0  system_nrf52.o(.data)
    .bss                                     0x20002c24   Section     2154  main.o(.bss)
    data_array                               0x20002f3a   Data         244  main.o(.bss)
    m_gatt                                   0x20003030   Data          20  main.o(.bss)
    m_qwr                                    0x20003044   Data          12  main.o(.bss)
    m_advertising                            0x20003050   Data        1076  main.o(.bss)
    config_file_data                         0x20003484   Data          10  main.o(.bss)
    .bss                                     0x2000348e   Section       32  ads1292.o(.bss)
    spi_tx_buf                               0x2000348e   Data          16  ads1292.o(.bss)
    spi_rx_buf                               0x2000349e   Data          16  ads1292.o(.bss)
    .bss                                     0x200034b0   Section       32  bsp.o(.bss)
    m_bsp_leds_tmr_data                      0x200034b0   Data          32  bsp.o(.bss)
    .bss                                     0x200034d0   Section       32  bsp.o(.bss)
    m_bsp_alert_tmr_data                     0x200034d0   Data          32  bsp.o(.bss)
    .bss                                     0x200034f0   Section       12  bsp.o(.bss)
    m_events_list                            0x200034f0   Data          12  bsp.o(.bss)
    .bss                                     0x20003500   Section       32  bsp.o(.bss)
    m_bsp_button_tmr_data                    0x20003500   Data          32  bsp.o(.bss)
    .bss                                     0x20003520   Section       80  ble_conn_params.o(.bss)
    m_timer_data                             0x20003520   Data          32  ble_conn_params.o(.bss)
    m_conn_params_instances                  0x20003540   Data          20  ble_conn_params.o(.bss)
    m_conn_params_config                     0x20003554   Data          28  ble_conn_params.o(.bss)
    .bss                                     0x20003570   Section      124  ble_conn_state.o(.bss)
    m_bcs                                    0x20003570   Data         124  ble_conn_state.o(.bss)
    .bss                                     0x200035ec   Section       20  nrf_drv_clock.o(.bss)
    m_clock_cb                               0x200035ec   Data          20  nrf_drv_clock.o(.bss)
    .bss                                     0x20003600   Section       92  nrfx_gpiote.o(.bss)
    m_cb                                     0x20003600   Data          92  nrfx_gpiote.o(.bss)
    .bss                                     0x2000365c   Section       44  nrfx_uart.o(.bss)
    m_cb                                     0x2000365c   Data          44  nrfx_uart.o(.bss)
    .bss                                     0x20003688   Section       36  nrfx_uarte.o(.bss)
    m_cb                                     0x20003688   Data          36  nrfx_uarte.o(.bss)
    .bss                                     0x200036ac   Section       48  nrfx_saadc.o(.bss)
    m_cb                                     0x200036ac   Data          48  nrfx_saadc.o(.bss)
    .bss                                     0x200036dc   Section       12  nrfx_timer.o(.bss)
    m_cb                                     0x200036dc   Data          12  nrfx_timer.o(.bss)
    .bss                                     0x200036e8   Section       36  nrfx_spim.o(.bss)
    m_cb                                     0x200036e8   Data          36  nrfx_spim.o(.bss)
    .bss                                     0x2000370c   Section       24  nrf_drv_spi.o(.bss)
    m_handlers                               0x2000370c   Data          12  nrf_drv_spi.o(.bss)
    m_contexts                               0x20003718   Data          12  nrf_drv_spi.o(.bss)
    .bss                                     0x20003724   Section       16  app_timer2.o(.bss)
    m_req_fifo_inst                          0x20003724   Data          16  app_timer2.o(.bss)
    .bss                                     0x20003734   Section       88  app_timer2.o(.bss)
    m_req_fifo_data                          0x20003734   Data          88  app_timer2.o(.bss)
    .bss                                     0x2000378c   Section       32  app_uart_fifo.o(.bss)
    m_rx_fifo                                0x2000378c   Data          16  app_uart_fifo.o(.bss)
    m_tx_fifo                                0x2000379c   Data          16  app_uart_fifo.o(.bss)
    .bss                                     0x200037ac   Section       12  app_util_platform.o(.bss)
    .bss                                     0x200037b8   Section       12  nrf_pwr_mgmt.o(.bss)
    m_handlers_iter                          0x200037b8   Data          12  nrf_pwr_mgmt.o(.bss)
    .bss                                     0x200037c4   Section       64  nrf_log_backend_rtt.o(.bss)
    m_string_buff                            0x200037c4   Data          64  nrf_log_backend_rtt.o(.bss)
    .bss                                     0x20003804   Section      192  nrf_log_frontend.o(.bss)
    log_mempool_nrf_balloc_pool_mem          0x20003804   Data         192  nrf_log_frontend.o(.bss)
    .bss                                     0x200038c4   Section      128  nrf_log_frontend.o(.bss)
    m_log_push_ringbuf_buf                   0x200038c4   Data         128  nrf_log_frontend.o(.bss)
    .bss                                     0x20003944   Section       24  nrf_log_frontend.o(.bss)
    m_log_push_ringbuf_cb                    0x20003944   Data          24  nrf_log_frontend.o(.bss)
    .bss                                     0x2000395c   Section     1056  nrf_log_frontend.o(.bss)
    m_log_data                               0x2000395c   Data        1056  nrf_log_frontend.o(.bss)
    .bss                                     0x20003d7c   Section      648  segger_rtt.o(.bss)
    _acUpBuffer                              0x20003df4   Data         512  segger_rtt.o(.bss)
    _acDownBuffer                            0x20003ff4   Data          16  segger_rtt.o(.bss)
    STACK                                    0x20004008   Section     8192  arm_startup_nrf52.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$E$P$D$K$B$S$7EM$VFPi3$EXTD16$VFPS$VFMA$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OSPACE$ROPI$EBA8$MICROLIB$REQ8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    __arm_fini_                               - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _microlib_exit                            - Undefined Weak Reference
    log_dynamic_data$$Base                    - Undefined Reference
    pwr_mgmt_data0$$Base                      - Undefined Reference
    pwr_mgmt_data0$$Limit                     - Undefined Reference
    pwr_mgmt_data1$$Base                      - Undefined Reference
    pwr_mgmt_data1$$Limit                     - Undefined Reference
    pwr_mgmt_data2$$Base                      - Undefined Reference
    pwr_mgmt_data2$$Limit                     - Undefined Reference
    sdh_req_observers0$$Base                  - Undefined Reference
    sdh_req_observers0$$Limit                 - Undefined Reference
    sdh_req_observers1$$Base                  - Undefined Reference
    sdh_req_observers1$$Limit                 - Undefined Reference
    sdh_soc_observers1$$Base                  - Undefined Reference
    sdh_soc_observers1$$Limit                 - Undefined Reference
    sdh_stack_observers1$$Base                - Undefined Reference
    sdh_stack_observers1$$Limit               - Undefined Reference
    sdh_state_observers1$$Base                - Undefined Reference
    sdh_state_observers1$$Limit               - Undefined Reference
    __Vectors_Size                           0x00000200   Number         0  arm_startup_nrf52.o ABSOLUTE
    __Vectors                                0x00026000   Data           4  arm_startup_nrf52.o(RESET)
    __Vectors_End                            0x00026200   Data           0  arm_startup_nrf52.o(RESET)
    __main                                   0x00026201   Thumb Code     0  entry.o(.ARM.Collect$$$$00000000)
    _main_stk                                0x00026201   Thumb Code     0  entry2.o(.ARM.Collect$$$$00000001)
    _main_scatterload                        0x00026205   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    __main_after_scatterload                 0x00026209   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    _main_clock                              0x00026209   Thumb Code     0  entry7b.o(.ARM.Collect$$$$00000008)
    _main_cpp_init                           0x00026209   Thumb Code     0  entry8b.o(.ARM.Collect$$$$0000000A)
    _main_init                               0x00026209   Thumb Code     0  entry9a.o(.ARM.Collect$$$$0000000B)
    __rt_lib_shutdown_fini                   0x00026211   Thumb Code     0  entry12b.o(.ARM.Collect$$$$0000000E)
    __rt_final_cpp                           0x00026215   Thumb Code     0  entry10a.o(.ARM.Collect$$$$0000000F)
    __rt_final_exit                          0x00026215   Thumb Code     0  entry11a.o(.ARM.Collect$$$$00000011)
    __asm___12_nrf_atfifo_c_51f461e1__nrf_atfifo_wspace_req 0x00026219   Thumb Code    56  nrf_atfifo.o(.emb_text)
    __asm___12_nrf_atfifo_c_51f461e1__nrf_atfifo_wspace_close 0x00026251   Thumb Code    18  nrf_atfifo.o(.emb_text)
    __asm___12_nrf_atfifo_c_51f461e1__nrf_atfifo_rspace_req 0x00026263   Thumb Code    58  nrf_atfifo.o(.emb_text)
    __asm___12_nrf_atfifo_c_51f461e1__nrf_atfifo_rspace_close 0x0002629d   Thumb Code    18  nrf_atfifo.o(.emb_text)
    __asm___12_nrf_atfifo_c_51f461e1__nrf_atfifo_space_clear 0x000262af   Thumb Code    50  nrf_atfifo.o(.emb_text)
    __asm___12_nrf_atomic_c_85ca2469__nrf_atomic_internal_mov 0x000262e1   Thumb Code    24  nrf_atomic.o(.emb_text)
    __asm___12_nrf_atomic_c_85ca2469__nrf_atomic_internal_orr 0x000262f9   Thumb Code    26  nrf_atomic.o(.emb_text)
    __asm___12_nrf_atomic_c_85ca2469__nrf_atomic_internal_and 0x00026313   Thumb Code    26  nrf_atomic.o(.emb_text)
    __asm___12_nrf_atomic_c_85ca2469__nrf_atomic_internal_eor 0x0002632d   Thumb Code    26  nrf_atomic.o(.emb_text)
    __asm___12_nrf_atomic_c_85ca2469__nrf_atomic_internal_add 0x00026347   Thumb Code    26  nrf_atomic.o(.emb_text)
    __asm___12_nrf_atomic_c_85ca2469__nrf_atomic_internal_sub 0x00026361   Thumb Code    26  nrf_atomic.o(.emb_text)
    __asm___12_nrf_atomic_c_85ca2469__nrf_atomic_internal_cmp_exch 0x0002637b   Thumb Code    42  nrf_atomic.o(.emb_text)
    __asm___12_nrf_atomic_c_85ca2469__nrf_atomic_internal_sub_hs 0x000263a5   Thumb Code    30  nrf_atomic.o(.emb_text)
    Reset_Handler                            0x000263c5   Thumb Code     8  arm_startup_nrf52.o(.text)
    NMI_Handler                              0x000263cd   Thumb Code     2  arm_startup_nrf52.o(.text)
    HardFault_Handler                        0x000263cf   Thumb Code     2  arm_startup_nrf52.o(.text)
    MemoryManagement_Handler                 0x000263d1   Thumb Code     2  arm_startup_nrf52.o(.text)
    BusFault_Handler                         0x000263d3   Thumb Code     2  arm_startup_nrf52.o(.text)
    UsageFault_Handler                       0x000263d5   Thumb Code     2  arm_startup_nrf52.o(.text)
    SVC_Handler                              0x000263d7   Thumb Code     2  arm_startup_nrf52.o(.text)
    DebugMon_Handler                         0x000263d9   Thumb Code     2  arm_startup_nrf52.o(.text)
    PendSV_Handler                           0x000263db   Thumb Code     2  arm_startup_nrf52.o(.text)
    SysTick_Handler                          0x000263dd   Thumb Code     2  arm_startup_nrf52.o(.text)
    CCM_AAR_IRQHandler                       0x000263df   Thumb Code     0  arm_startup_nrf52.o(.text)
    COMP_LPCOMP_IRQHandler                   0x000263df   Thumb Code     0  arm_startup_nrf52.o(.text)
    ECB_IRQHandler                           0x000263df   Thumb Code     0  arm_startup_nrf52.o(.text)
    FPU_IRQHandler                           0x000263df   Thumb Code     0  arm_startup_nrf52.o(.text)
    I2S_IRQHandler                           0x000263df   Thumb Code     0  arm_startup_nrf52.o(.text)
    MWU_IRQHandler                           0x000263df   Thumb Code     0  arm_startup_nrf52.o(.text)
    NFCT_IRQHandler                          0x000263df   Thumb Code     0  arm_startup_nrf52.o(.text)
    PDM_IRQHandler                           0x000263df   Thumb Code     0  arm_startup_nrf52.o(.text)
    PWM0_IRQHandler                          0x000263df   Thumb Code     0  arm_startup_nrf52.o(.text)
    PWM1_IRQHandler                          0x000263df   Thumb Code     0  arm_startup_nrf52.o(.text)
    PWM2_IRQHandler                          0x000263df   Thumb Code     0  arm_startup_nrf52.o(.text)
    QDEC_IRQHandler                          0x000263df   Thumb Code     0  arm_startup_nrf52.o(.text)
    RADIO_IRQHandler                         0x000263df   Thumb Code     0  arm_startup_nrf52.o(.text)
    RNG_IRQHandler                           0x000263df   Thumb Code     0  arm_startup_nrf52.o(.text)
    RTC0_IRQHandler                          0x000263df   Thumb Code     0  arm_startup_nrf52.o(.text)
    RTC2_IRQHandler                          0x000263df   Thumb Code     0  arm_startup_nrf52.o(.text)
    SPIM1_SPIS1_TWIM1_TWIS1_SPI1_TWI1_IRQHandler 0x000263df   Thumb Code     0  arm_startup_nrf52.o(.text)
    SPIM2_SPIS2_SPI2_IRQHandler              0x000263df   Thumb Code     0  arm_startup_nrf52.o(.text)
    SWI0_EGU0_IRQHandler                     0x000263df   Thumb Code     0  arm_startup_nrf52.o(.text)
    SWI1_EGU1_IRQHandler                     0x000263df   Thumb Code     0  arm_startup_nrf52.o(.text)
    SWI3_EGU3_IRQHandler                     0x000263df   Thumb Code     0  arm_startup_nrf52.o(.text)
    SWI4_EGU4_IRQHandler                     0x000263df   Thumb Code     0  arm_startup_nrf52.o(.text)
    SWI5_EGU5_IRQHandler                     0x000263df   Thumb Code     0  arm_startup_nrf52.o(.text)
    TEMP_IRQHandler                          0x000263df   Thumb Code     0  arm_startup_nrf52.o(.text)
    TIMER0_IRQHandler                        0x000263df   Thumb Code     0  arm_startup_nrf52.o(.text)
    TIMER2_IRQHandler                        0x000263df   Thumb Code     0  arm_startup_nrf52.o(.text)
    TIMER3_IRQHandler                        0x000263df   Thumb Code     0  arm_startup_nrf52.o(.text)
    TIMER4_IRQHandler                        0x000263df   Thumb Code     0  arm_startup_nrf52.o(.text)
    WDT_IRQHandler                           0x000263df   Thumb Code     0  arm_startup_nrf52.o(.text)
    __aeabi_uldivmod                         0x000263e9   Thumb Code    98  uldiv.o(.text)
    __aeabi_llsl                             0x0002644b   Thumb Code    30  llshl.o(.text)
    _ll_shift_l                              0x0002644b   Thumb Code     0  llshl.o(.text)
    __aeabi_llsr                             0x00026469   Thumb Code    32  llushr.o(.text)
    _ll_ushift_r                             0x00026469   Thumb Code     0  llushr.o(.text)
    __rt_ctype_table                         0x00026489   Thumb Code     4  ctype_o.o(.text)
    __aeabi_memcpy                           0x00026491   Thumb Code    36  memcpya.o(.text)
    __aeabi_memcpy4                          0x00026491   Thumb Code     0  memcpya.o(.text)
    __aeabi_memcpy8                          0x00026491   Thumb Code     0  memcpya.o(.text)
    __aeabi_memset                           0x000264b5   Thumb Code    14  memseta.o(.text)
    __aeabi_memset4                          0x000264b5   Thumb Code     0  memseta.o(.text)
    __aeabi_memset8                          0x000264b5   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr                           0x000264c3   Thumb Code     4  memseta.o(.text)
    __aeabi_memclr4                          0x000264c3   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr8                          0x000264c3   Thumb Code     0  memseta.o(.text)
    _memset$wrapper                          0x000264c7   Thumb Code    18  memseta.o(.text)
    strlen                                   0x000264d9   Thumb Code    14  strlen.o(.text)
    strcpy                                   0x000264e7   Thumb Code    18  strcpy.o(.text)
    __scatterload                            0x000264f9   Thumb Code    28  init.o(.text)
    __scatterload_rt2                        0x000264f9   Thumb Code     0  init.o(.text)
    __decompress                             0x0002651d   Thumb Code     0  __dczerorl2.o(.text)
    __decompress1                            0x0002651d   Thumb Code    86  __dczerorl2.o(.text)
    ADS1299_CMD                              0x00026575   Thumb Code    42  ads1292.o(i.ADS1299_CMD)
    ADS1299_READREG                          0x000265ad   Thumb Code    50  ads1292.o(i.ADS1299_READREG)
    ADS1299_WRITEREG                         0x000265ed   Thumb Code    46  ads1292.o(i.ADS1299_WRITEREG)
    ADS_READDATA                             0x00026629   Thumb Code   148  ads1292.o(i.ADS_READDATA)
    GPIOTE_IRQHandler                        0x000266c9   Thumb Code   154  nrfx_gpiote.o(i.GPIOTE_IRQHandler)
    LED1_Close                               0x0002676d   Thumb Code    14  led.o(i.LED1_Close)
    LED1_Open                                0x0002677b   Thumb Code    14  led.o(i.LED1_Open)
    LED2_Close                               0x00026789   Thumb Code    14  led.o(i.LED2_Close)
    LED2_Open                                0x00026797   Thumb Code    14  led.o(i.LED2_Open)
    LED_Init                                 0x000267a5   Thumb Code    24  led.o(i.LED_Init)
    POWER_CLOCK_IRQHandler                   0x000267bd   Thumb Code    82  nrfx_clock.o(i.POWER_CLOCK_IRQHandler)
    RTC1_IRQHandler                          0x00026815   Thumb Code    12  drv_rtc.o(i.RTC1_IRQHandler)
    SAADC_IRQHandler                         0x00026825   Thumb Code   320  nrfx_saadc.o(i.SAADC_IRQHandler)
    SEGGER_RTT_Init                          0x0002696d   Thumb Code     4  segger_rtt.o(i.SEGGER_RTT_Init)
    SEGGER_RTT_WriteNoLock                   0x00026971   Thumb Code    86  segger_rtt.o(i.SEGGER_RTT_WriteNoLock)
    SPIM0_SPIS0_TWIM0_TWIS0_SPI0_TWI0_IRQHandler 0x000269cd   Thumb Code    76  nrfx_spim.o(i.SPIM0_SPIS0_TWIM0_TWIS0_SPI0_TWI0_IRQHandler)
    SWI2_EGU2_IRQHandler                     0x00026a21   Thumb Code     4  nrf_sdh.o(i.SWI2_EGU2_IRQHandler)
    SystemInit                               0x00026a25   Thumb Code   848  system_nrf52.o(i.SystemInit)
    TIMER1_IRQHandler                        0x00026dbd   Thumb Code    70  nrfx_timer.o(i.TIMER1_IRQHandler)
    UARTE0_UART0_IRQHandler                  0x00026e0d   Thumb Code     6  nrfx_prs.o(i.UARTE0_UART0_IRQHandler)
    __scatterload_copy                       0x00026f33   Thumb Code    14  handlers.o(i.__scatterload_copy)
    __scatterload_null                       0x00026f41   Thumb Code     2  handlers.o(i.__scatterload_null)
    __scatterload_zeroinit                   0x00026f43   Thumb Code    14  handlers.o(i.__scatterload_zeroinit)
    ads1299_spi_init                         0x00026f75   Thumb Code   364  ads1292.o(i.ads1299_spi_init)
    app_error_fault_handler                  0x0002715d   Thumb Code    86  app_error_weak.o(i.app_error_fault_handler)
    app_error_handler_bare                   0x000271e1   Thumb Code    22  app_error.o(i.app_error_handler_bare)
    app_fifo_get                             0x000271f7   Thumb Code    22  app_fifo.o(i.app_fifo_get)
    app_fifo_put                             0x0002720d   Thumb Code    26  app_fifo.o(i.app_fifo_put)
    app_timer_cnt_get                        0x00027229   Thumb Code     6  app_timer2.o(i.app_timer_cnt_get)
    app_timer_create                         0x00027235   Thumb Code    22  app_timer2.o(i.app_timer_create)
    app_timer_init                           0x0002724d   Thumb Code    70  app_timer2.o(i.app_timer_init)
    app_timer_start                          0x000272a9   Thumb Code    48  app_timer2.o(i.app_timer_start)
    app_timer_stop                           0x000272d9   Thumb Code    12  app_timer2.o(i.app_timer_stop)
    app_uart_put                             0x000272e5   Thumb Code    70  app_uart_fifo.o(i.app_uart_put)
    app_util_critical_region_enter           0x00027339   Thumb Code    64  app_util_platform.o(i.app_util_critical_region_enter)
    app_util_critical_region_exit            0x00027381   Thumb Code    46  app_util_platform.o(i.app_util_critical_region_exit)
    blcm_link_ctx_get                        0x000273b5   Thumb Code    72  ble_link_ctx_manager.o(i.blcm_link_ctx_get)
    ble_advdata_encode                       0x000273fd   Thumb Code   386  ble_advdata.o(i.ble_advdata_encode)
    ble_advdata_parse                        0x0002757f   Thumb Code    32  ble_advdata.o(i.ble_advdata_parse)
    ble_advdata_search                       0x0002759f   Thumb Code    70  ble_advdata.o(i.ble_advdata_search)
    ble_advertising_conn_cfg_tag_set         0x000275e5   Thumb Code     6  ble_advertising.o(i.ble_advertising_conn_cfg_tag_set)
    ble_advertising_init                     0x000275eb   Thumb Code   248  ble_advertising.o(i.ble_advertising_init)
    ble_advertising_on_ble_evt               0x000276e3   Thumb Code   106  ble_advertising.o(i.ble_advertising_on_ble_evt)
    ble_advertising_start                    0x0002774d   Thumb Code   560  ble_advertising.o(i.ble_advertising_start)
    ble_conn_params_init                     0x0002797d   Thumb Code    90  ble_conn_params.o(i.ble_conn_params_init)
    ble_conn_state_conn_idx                  0x000279e5   Thumb Code    18  ble_conn_state.o(i.ble_conn_state_conn_idx)
    ble_conn_state_valid                     0x000279f9   Thumb Code    16  ble_conn_state.o(i.ble_conn_state_valid)
    ble_nus_data_send                        0x00027dcd   Thumb Code   102  ble_nus.o(i.ble_nus_data_send)
    ble_nus_init                             0x00027e35   Thumb Code   206  ble_nus.o(i.ble_nus_init)
    ble_nus_on_ble_evt                       0x00027f09   Thumb Code   120  ble_nus.o(i.ble_nus_on_ble_evt)
    ble_srv_is_notification_enabled          0x00027f81   Thumb Code     8  ble_srv_common.o(i.ble_srv_is_notification_enabled)
    bsp_board_button_idx_to_pin              0x00027f89   Thumb Code     6  boards.o(i.bsp_board_button_idx_to_pin)
    bsp_board_led_invert                     0x00027f95   Thumb Code    32  boards.o(i.bsp_board_led_invert)
    bsp_board_led_off                        0x00027fb9   Thumb Code    10  boards.o(i.bsp_board_led_off)
    bsp_board_led_on                         0x00027fc9   Thumb Code    10  boards.o(i.bsp_board_led_on)
    bsp_board_led_state_get                  0x00027fd9   Thumb Code    28  boards.o(i.bsp_board_led_state_get)
    bsp_board_leds_off                       0x00027ff9   Thumb Code    18  boards.o(i.bsp_board_leds_off)
    bsp_board_leds_on                        0x0002800b   Thumb Code    18  boards.o(i.bsp_board_leds_on)
    bsp_board_pin_to_button_idx              0x0002801d   Thumb Code    30  boards.o(i.bsp_board_pin_to_button_idx)
    bsp_btn_ble_sleep_mode_prepare           0x00028041   Thumb Code    30  bsp_btn_ble.o(i.bsp_btn_ble_sleep_mode_prepare)
    bsp_event_to_button_action_assign        0x000280f1   Thumb Code    64  bsp.o(i.bsp_event_to_button_action_assign)
    bsp_indication_set                       0x00028135   Thumb Code    20  bsp.o(i.bsp_indication_set)
    bsp_wakeup_button_enable                 0x00028315   Thumb Code     6  bsp.o(i.bsp_wakeup_button_enable)
    buffer_is_empty                          0x000283e1   Thumb Code    18  nrf_log_frontend.o(i.buffer_is_empty)
    characteristic_add                       0x0002846d   Thumb Code   400  ble_srv_common.o(i.characteristic_add)
    conn_handle_list_get                     0x00028659   Thumb Code    62  ble_conn_state.o(i.conn_handle_list_get)
    datacjfunc                               0x0002882d   Thumb Code   244  main.o(i.datacjfunc)
    drv_rtc_compare_disable                  0x0002893d   Thumb Code    20  drv_rtc.o(i.drv_rtc_compare_disable)
    drv_rtc_compare_pending                  0x00028951   Thumb Code    14  drv_rtc.o(i.drv_rtc_compare_pending)
    drv_rtc_compare_set                      0x0002895f   Thumb Code    80  drv_rtc.o(i.drv_rtc_compare_set)
    drv_rtc_counter_get                      0x000289af   Thumb Code     8  drv_rtc.o(i.drv_rtc_counter_get)
    drv_rtc_init                             0x000289b9   Thumb Code   148  drv_rtc.o(i.drv_rtc_init)
    drv_rtc_irq_trigger                      0x00028a91   Thumb Code    30  drv_rtc.o(i.drv_rtc_irq_trigger)
    drv_rtc_overflow_enable                  0x00028aaf   Thumb Code     8  drv_rtc.o(i.drv_rtc_overflow_enable)
    drv_rtc_overflow_pending                 0x00028ab7   Thumb Code     8  drv_rtc.o(i.drv_rtc_overflow_pending)
    drv_rtc_start                            0x00028abf   Thumb Code     8  drv_rtc.o(i.drv_rtc_start)
    drv_rtc_stop                             0x00028ac7   Thumb Code     8  drv_rtc.o(i.drv_rtc_stop)
    drv_rtc_windowed_compare_set             0x00028acf   Thumb Code   222  drv_rtc.o(i.drv_rtc_windowed_compare_set)
    gatt_evt_handler                         0x00028c2d   Thumb Code    52  main.o(i.gatt_evt_handler)
    gatt_init                                0x00028c89   Thumb Code    38  main.o(i.gatt_init)
    in_pin_handler                           0x00028d61   Thumb Code   238  main.o(i.in_pin_handler)
    main                                     0x00029079   Thumb Code   406  main.o(i.main)
    nrf_atfifo_init                          0x000293f7   Thumb Code    38  nrf_atfifo.o(i.nrf_atfifo_init)
    nrf_atfifo_item_alloc                    0x0002941d   Thumb Code    22  nrf_atfifo.o(i.nrf_atfifo_item_alloc)
    nrf_atfifo_item_free                     0x00029433   Thumb Code    22  nrf_atfifo.o(i.nrf_atfifo_item_free)
    nrf_atfifo_item_get                      0x00029449   Thumb Code    22  nrf_atfifo.o(i.nrf_atfifo_item_get)
    nrf_atfifo_item_put                      0x0002945f   Thumb Code    22  nrf_atfifo.o(i.nrf_atfifo_item_put)
    nrf_atflags_clear                        0x00029475   Thumb Code    22  nrf_atflags.o(i.nrf_atflags_clear)
    nrf_atflags_get                          0x0002948b   Thumb Code    22  nrf_atflags.o(i.nrf_atflags_get)
    nrf_atflags_set                          0x000294a1   Thumb Code    20  nrf_atflags.o(i.nrf_atflags_set)
    nrf_atomic_flag_clear_fetch              0x000294b5   Thumb Code     6  nrf_atomic.o(i.nrf_atomic_flag_clear_fetch)
    nrf_atomic_flag_set                      0x000294bb   Thumb Code     6  nrf_atomic.o(i.nrf_atomic_flag_set)
    nrf_atomic_u32_add                       0x000294c1   Thumb Code    12  nrf_atomic.o(i.nrf_atomic_u32_add)
    nrf_atomic_u32_and                       0x000294cd   Thumb Code    12  nrf_atomic.o(i.nrf_atomic_u32_and)
    nrf_atomic_u32_fetch_and                 0x000294d9   Thumb Code    10  nrf_atomic.o(i.nrf_atomic_u32_fetch_and)
    nrf_atomic_u32_fetch_store               0x000294e3   Thumb Code    10  nrf_atomic.o(i.nrf_atomic_u32_fetch_store)
    nrf_atomic_u32_or                        0x000294ed   Thumb Code    12  nrf_atomic.o(i.nrf_atomic_u32_or)
    nrf_atomic_u32_sub                       0x000294f9   Thumb Code    12  nrf_atomic.o(i.nrf_atomic_u32_sub)
    nrf_balloc_alloc                         0x00029505   Thumb Code    68  nrf_balloc.o(i.nrf_balloc_alloc)
    nrf_balloc_free                          0x00029549   Thumb Code    48  nrf_balloc.o(i.nrf_balloc_free)
    nrf_balloc_init                          0x00029579   Thumb Code    48  nrf_balloc.o(i.nrf_balloc_init)
    nrf_ble_gatt_att_mtu_periph_set          0x000295b9   Thumb Code    24  nrf_ble_gatt.o(i.nrf_ble_gatt_att_mtu_periph_set)
    nrf_ble_gatt_init                        0x000295d1   Thumb Code    34  nrf_ble_gatt.o(i.nrf_ble_gatt_init)
    nrf_ble_gatt_on_ble_evt                  0x000295f5   Thumb Code   270  nrf_ble_gatt.o(i.nrf_ble_gatt_on_ble_evt)
    nrf_ble_qwr_conn_handle_assign           0x00029745   Thumb Code    22  nrf_ble_qwr.o(i.nrf_ble_qwr_conn_handle_assign)
    nrf_ble_qwr_init                         0x0002975b   Thumb Code    36  nrf_ble_qwr.o(i.nrf_ble_qwr_init)
    nrf_ble_qwr_on_ble_evt                   0x0002977f   Thumb Code   182  nrf_ble_qwr.o(i.nrf_ble_qwr_on_ble_evt)
    nrf_drv_clock_init                       0x0002988d   Thumb Code    56  nrf_drv_clock.o(i.nrf_drv_clock_init)
    nrf_drv_clock_lfclk_release              0x000298cd   Thumb Code    48  nrf_drv_clock.o(i.nrf_drv_clock_lfclk_release)
    nrf_drv_spi_init                         0x00029901   Thumb Code    98  nrf_drv_spi.o(i.nrf_drv_spi_init)
    nrf_fprintf                              0x000299a9   Thumb Code    26  nrf_fprintf.o(i.nrf_fprintf)
    nrf_fprintf_buffer_flush                 0x000299c3   Thumb Code    24  nrf_fprintf.o(i.nrf_fprintf_buffer_flush)
    nrf_fprintf_fmt                          0x000299db   Thumb Code   474  nrf_fprintf_format.o(i.nrf_fprintf_fmt)
    nrf_log_backend_add                      0x00029d39   Thumb Code    86  nrf_log_frontend.o(i.nrf_log_backend_add)
    nrf_log_backend_rtt_init                 0x00029d97   Thumb Code     4  nrf_log_backend_rtt.o(i.nrf_log_backend_rtt_init)
    nrf_log_backend_serial_put               0x00029db5   Thumb Code   194  nrf_log_backend_serial.o(i.nrf_log_backend_serial_put)
    nrf_log_color_id_get                     0x00029e79   Thumb Code    42  nrf_log_frontend.o(i.nrf_log_color_id_get)
    nrf_log_default_backends_init            0x00029ea9   Thumb Code    24  nrf_log_default_backends.o(i.nrf_log_default_backends_init)
    nrf_log_frontend_dequeue                 0x00029ec5   Thumb Code   524  nrf_log_frontend.o(i.nrf_log_frontend_dequeue)
    nrf_log_frontend_std_0                   0x0002a0f9   Thumb Code     8  nrf_log_frontend.o(i.nrf_log_frontend_std_0)
    nrf_log_frontend_std_1                   0x0002a101   Thumb Code    14  nrf_log_frontend.o(i.nrf_log_frontend_std_1)
    nrf_log_frontend_std_2                   0x0002a10f   Thumb Code    16  nrf_log_frontend.o(i.nrf_log_frontend_std_2)
    nrf_log_hexdump_entry_process            0x0002a121   Thumb Code   150  nrf_log_str_formatter.o(i.nrf_log_hexdump_entry_process)
    nrf_log_init                             0x0002a1d1   Thumb Code    28  nrf_log_frontend.o(i.nrf_log_init)
    nrf_log_module_cnt_get                   0x0002a1f9   Thumb Code    10  nrf_log_frontend.o(i.nrf_log_module_cnt_get)
    nrf_log_module_name_get                  0x0002a20d   Thumb Code    24  nrf_log_frontend.o(i.nrf_log_module_name_get)
    nrf_log_panic                            0x0002a229   Thumb Code    36  nrf_log_frontend.o(i.nrf_log_panic)
    nrf_log_std_entry_process                0x0002a251   Thumb Code   182  nrf_log_str_formatter.o(i.nrf_log_std_entry_process)
    nrf_memobj_alloc                         0x0002a307   Thumb Code    96  nrf_memobj.o(i.nrf_memobj_alloc)
    nrf_memobj_free                          0x0002a367   Thumb Code    50  nrf_memobj.o(i.nrf_memobj_free)
    nrf_memobj_get                           0x0002a399   Thumb Code     8  nrf_memobj.o(i.nrf_memobj_get)
    nrf_memobj_pool_init                     0x0002a3a1   Thumb Code     4  nrf_memobj.o(i.nrf_memobj_pool_init)
    nrf_memobj_put                           0x0002a3a5   Thumb Code    30  nrf_memobj.o(i.nrf_memobj_put)
    nrf_memobj_read                          0x0002a3c3   Thumb Code    16  nrf_memobj.o(i.nrf_memobj_read)
    nrf_memobj_write                         0x0002a3d3   Thumb Code    16  nrf_memobj.o(i.nrf_memobj_write)
    nrf_pwr_mgmt_init                        0x0002a3e5   Thumb Code    28  nrf_pwr_mgmt.o(i.nrf_pwr_mgmt_init)
    nrf_pwr_mgmt_run                         0x0002a40d   Thumb Code    64  nrf_pwr_mgmt.o(i.nrf_pwr_mgmt_run)
    nrf_ringbuf_init                         0x0002a455   Thumb Code    28  nrf_ringbuf.o(i.nrf_ringbuf_init)
    nrf_sdh_ble_app_ram_start_get            0x0002a4b1   Thumb Code    16  nrf_sdh_ble.o(i.nrf_sdh_ble_app_ram_start_get)
    nrf_sdh_ble_default_cfg_set              0x0002a4c5   Thumb Code   272  nrf_sdh_ble.o(i.nrf_sdh_ble_default_cfg_set)
    nrf_sdh_ble_enable                       0x0002a5f5   Thumb Code   116  nrf_sdh_ble.o(i.nrf_sdh_ble_enable)
    nrf_sdh_enable_request                   0x0002a785   Thumb Code   102  nrf_sdh.o(i.nrf_sdh_enable_request)
    nrf_sdh_evts_poll                        0x0002a7f9   Thumb Code    32  nrf_sdh.o(i.nrf_sdh_evts_poll)
    nrf_sdh_is_enabled                       0x0002a81d   Thumb Code     6  nrf_sdh.o(i.nrf_sdh_is_enabled)
    nrf_section_iter_init                    0x0002a865   Thumb Code    10  nrf_section_iter.o(i.nrf_section_iter_init)
    nrf_section_iter_next                    0x0002a893   Thumb Code    32  nrf_section_iter.o(i.nrf_section_iter_next)
    nrf_sortlist_add                         0x0002a8b3   Thumb Code    34  nrf_sortlist.o(i.nrf_sortlist_add)
    nrf_sortlist_peek                        0x0002a8d5   Thumb Code     6  nrf_sortlist.o(i.nrf_sortlist_peek)
    nrf_sortlist_pop                         0x0002a8db   Thumb Code    14  nrf_sortlist.o(i.nrf_sortlist_pop)
    nrf_sortlist_remove                      0x0002a8e9   Thumb Code    30  nrf_sortlist.o(i.nrf_sortlist_remove)
    nrf_strerror_find                        0x0002a911   Thumb Code    52  nrf_strerror.o(i.nrf_strerror_find)
    nrf_strerror_get                         0x0002a949   Thumb Code    14  nrf_strerror.o(i.nrf_strerror_get)
    nrfx_clock_enable                        0x0002a9a5   Thumb Code    34  nrfx_clock.o(i.nrfx_clock_enable)
    nrfx_clock_init                          0x0002a9d1   Thumb Code    26  nrfx_clock.o(i.nrfx_clock_init)
    nrfx_clock_lfclk_stop                    0x0002a9f1   Thumb Code    38  nrfx_clock.o(i.nrfx_clock_lfclk_stop)
    nrfx_gpiote_in_event_enable              0x0002aa41   Thumb Code   152  nrfx_gpiote.o(i.nrfx_gpiote_in_event_enable)
    nrfx_gpiote_in_init                      0x0002aae1   Thumb Code   190  nrfx_gpiote.o(i.nrfx_gpiote_in_init)
    nrfx_gpiote_init                         0x0002abad   Thumb Code   102  nrfx_gpiote.o(i.nrfx_gpiote_init)
    nrfx_prs_acquire                         0x0002ac41   Thumb Code    58  nrfx_prs.o(i.nrfx_prs_acquire)
    nrfx_spim_init                           0x0002ac7d   Thumb Code   356  nrfx_spim.o(i.nrfx_spim_init)
    nrfx_spim_xfer                           0x0002ade9   Thumb Code    94  nrfx_spim.o(i.nrfx_spim_xfer)
    nrfx_timer_compare                       0x0002ae4d   Thumb Code    58  nrfx_timer.o(i.nrfx_timer_compare)
    nrfx_timer_extended_compare              0x0002ae87   Thumb Code    42  nrfx_timer.o(i.nrfx_timer_extended_compare)
    nrfx_timer_init                          0x0002aeb1   Thumb Code   188  nrfx_timer.o(i.nrfx_timer_init)
    nrfx_uart_tx                             0x0002af71   Thumb Code   162  nrfx_uart.o(i.nrfx_uart_tx)
    nrfx_uart_tx_in_progress                 0x0002b019   Thumb Code    26  nrfx_uart.o(i.nrfx_uart_tx_in_progress)
    nrfx_uarte_tx                            0x0002b039   Thumb Code   166  nrfx_uarte.o(i.nrfx_uarte_tx)
    nrfx_uarte_tx_in_progress                0x0002b0e5   Thumb Code    22  nrfx_uarte.o(i.nrfx_uarte_tx_in_progress)
    timer_data_event_handler                 0x0002bc19   Thumb Code     2  main.o(i.timer_data_event_handler)
    nrf_log_backend_rtt_api                  0x0002c2a8   Data          12  nrf_log_backend_rtt.o(.constdata)
    m_ram_start                              0x0002c34c   Data           4  nrf_sdh_ble.o(.constdata)
    __ctype_table                            0x0002c378   Data         129  ctype_o.o(.constdata)
    Region$$Table$$Base                      0x0002cb8c   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x0002cbac   Number         0  anon$$obj.o(Region$$Table)
    log_const_data$$Base                     0x0002cbbc   Number         0  nrf_ble_gatt.o(log_const_data)
    m_nrf_log_nrf_ble_gatt_logs_data_const   0x0002cbbc   Data           8  nrf_ble_gatt.o(log_const_data)
    m_nrf_log_ble_nus_logs_data_const        0x0002cbc4   Data           8  ble_nus.o(log_const_data)
    m_nrf_log_clock_logs_data_const          0x0002cbcc   Data           8  nrf_drv_clock.o(log_const_data)
    m_nrf_log_CLOCK_logs_data_const          0x0002cbd4   Data           8  nrfx_clock.o(log_const_data)
    m_nrf_log_GPIOTE_logs_data_const         0x0002cbdc   Data           8  nrfx_gpiote.o(log_const_data)
    m_nrf_log_PRS_logs_data_const            0x0002cbe4   Data           8  nrfx_prs.o(log_const_data)
    m_nrf_log_UART_logs_data_const           0x0002cbec   Data           8  nrfx_uart.o(log_const_data)
    m_nrf_log_UARTE_logs_data_const          0x0002cbf4   Data           8  nrfx_uarte.o(log_const_data)
    m_nrf_log_PPI_logs_data_const            0x0002cbfc   Data           8  nrfx_ppi.o(log_const_data)
    m_nrf_log_SAADC_logs_data_const          0x0002cc04   Data           8  nrfx_saadc.o(log_const_data)
    m_nrf_log_TIMER_logs_data_const          0x0002cc0c   Data           8  nrfx_timer.o(log_const_data)
    m_nrf_log_SPIM_logs_data_const           0x0002cc14   Data           8  nrfx_spim.o(log_const_data)
    m_nrf_log_app_button_logs_data_const     0x0002cc1c   Data           8  app_button.o(log_const_data)
    m_nrf_log_app_timer_logs_data_const      0x0002cc24   Data           8  app_timer2.o(log_const_data)
    m_nrf_log_pwr_mgmt_logs_data_const       0x0002cc2c   Data           8  nrf_pwr_mgmt.o(log_const_data)
    m_nrf_log_sortlist_logs_data_const       0x0002cc34   Data           8  nrf_sortlist.o(log_const_data)
    m_nrf_log_app_logs_data_const            0x0002cc3c   Data           8  nrf_log_frontend.o(log_const_data)
    m_nrf_log_nrf_sdh_logs_data_const        0x0002cc44   Data           8  nrf_sdh.o(log_const_data)
    m_nrf_log_nrf_sdh_ble_logs_data_const    0x0002cc4c   Data           8  nrf_sdh_ble.o(log_const_data)
    m_nrf_log_nrf_sdh_soc_logs_data_const    0x0002cc54   Data           8  nrf_sdh_soc.o(log_const_data)
    log_const_data$$Limit                    0x0002cc5c   Number         0  nrf_sdh_soc.o(log_const_data)
    log_mempool                              0x0002cc5c   Data          20  nrf_log_frontend.o(nrf_balloc)
    sdh_ble_observers0$$Base                 0x0002cc70   Number         0  ble_conn_state.o(sdh_ble_observers0)
    sdh_ble_observers0$$Limit                0x0002cc78   Number         0  ble_conn_state.o(sdh_ble_observers0)
    sdh_ble_observers1$$Base                 0x0002cc78   Number         0  main.o(sdh_ble_observers1)
    sdh_ble_observers1$$Limit                0x0002cc98   Number         0  ble_conn_params.o(sdh_ble_observers1)
    sdh_ble_observers2$$Base                 0x0002cc98   Number         0  main.o(sdh_ble_observers2)
    sdh_ble_observers2$$Limit                0x0002cca8   Number         0  main.o(sdh_ble_observers2)
    sdh_ble_observers3$$Base                 0x0002cca8   Number         0  main.o(sdh_ble_observers3)
    sdh_ble_observers3$$Limit                0x0002ccb0   Number         0  main.o(sdh_ble_observers3)
    sdh_soc_observers0$$Base                 0x0002ccb0   Number         0  nrf_drv_clock.o(sdh_soc_observers0)
    sdh_soc_observers0$$Limit                0x0002ccb8   Number         0  nrf_drv_clock.o(sdh_soc_observers0)
    sdh_stack_observers0$$Base               0x0002ccb8   Number         0  nrf_sdh_ble.o(sdh_stack_observers0)
    sdh_stack_observers0$$Limit              0x0002ccc8   Number         0  nrf_sdh_soc.o(sdh_stack_observers0)
    sdh_state_observers0$$Base               0x0002ccc8   Number         0  nrf_drv_clock.o(sdh_state_observers0)
    sdh_state_observers0$$Limit              0x0002ccd0   Number         0  nrf_drv_clock.o(sdh_state_observers0)
    Image$$RW_IRAM1$$Base                    0x20002ad8   Number         0  anon$$obj.o ABSOLUTE
    ble_send_data_flag                       0x20002ae1   Data           1  main.o(.data)
    ble_send_data_length                     0x20002aea   Data           2  main.o(.data)
    time_ms                                  0x20002af0   Data           4  main.o(.data)
    time_ticks                               0x20002afc   Data           4  main.o(.data)
    nrf_drv_uart_use_easy_dma                0x20002b4c   Data           1  nrf_drv_uart.o(.data)
    SystemCoreClock                          0x20002c20   Data           4  system_nrf52.o(.data)
    ble_send_data                            0x20002c24   Data         184  main.o(.bss)
    data_buffer                              0x20002cdc   Data         606  main.o(.bss)
    nrf_nvic_state                           0x200037ac   Data          12  app_util_platform.o(.bss)
    _SEGGER_RTT                              0x20003d7c   Data         120  segger_rtt.o(.bss)
    __initial_sp                             0x20006008   Data           0  arm_startup_nrf52.o(STACK)



==============================================================================

Memory Map of the image

  Image Entry point : 0x00026201

  Load Region LR_IROM1 (Base: 0x00026000, Size: 0x00006e1c, Max: 0x0005a000, ABSOLUTE, COMPRESSED[0x00006d2c])

    Execution Region ER_IROM1 (Exec base: 0x00026000, Load base: 0x00026000, Size: 0x00006cd0, Max: 0x0005a000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x00026000   0x00026000   0x00000200   Data   RO         6367    RESET               arm_startup_nrf52.o
    0x00026200   0x00026200   0x00000000   Code   RO         6423  * .ARM.Collect$$$$00000000  mc_w.l(entry.o)
    0x00026200   0x00026200   0x00000004   Code   RO         6732    .ARM.Collect$$$$00000001  mc_w.l(entry2.o)
    0x00026204   0x00026204   0x00000004   Code   RO         6735    .ARM.Collect$$$$00000004  mc_w.l(entry5.o)
    0x00026208   0x00026208   0x00000000   Code   RO         6737    .ARM.Collect$$$$00000008  mc_w.l(entry7b.o)
    0x00026208   0x00026208   0x00000000   Code   RO         6739    .ARM.Collect$$$$0000000A  mc_w.l(entry8b.o)
    0x00026208   0x00026208   0x00000008   Code   RO         6740    .ARM.Collect$$$$0000000B  mc_w.l(entry9a.o)
    0x00026210   0x00026210   0x00000004   Code   RO         6747    .ARM.Collect$$$$0000000E  mc_w.l(entry12b.o)
    0x00026214   0x00026214   0x00000000   Code   RO         6742    .ARM.Collect$$$$0000000F  mc_w.l(entry10a.o)
    0x00026214   0x00026214   0x00000000   Code   RO         6744    .ARM.Collect$$$$00000011  mc_w.l(entry11a.o)
    0x00026214   0x00026214   0x00000004   Code   RO         6733    .ARM.Collect$$$$00002712  mc_w.l(entry2.o)
    0x00026218   0x00026218   0x000000c8   Code   RO         4755    .emb_text           nrf_atfifo.o
    0x000262e0   0x000262e0   0x000000e2   Code   RO         4891    .emb_text           nrf_atomic.o
    0x000263c2   0x000263c2   0x00000002   PAD
    0x000263c4   0x000263c4   0x00000024   Code   RO         6368    .text               arm_startup_nrf52.o
    0x000263e8   0x000263e8   0x00000062   Code   RO         6426    .text               mc_w.l(uldiv.o)
    0x0002644a   0x0002644a   0x0000001e   Code   RO         6428    .text               mc_w.l(llshl.o)
    0x00026468   0x00026468   0x00000020   Code   RO         6430    .text               mc_w.l(llushr.o)
    0x00026488   0x00026488   0x00000008   Code   RO         6432    .text               mc_w.l(ctype_o.o)
    0x00026490   0x00026490   0x00000024   Code   RO         6460    .text               mc_w.l(memcpya.o)
    0x000264b4   0x000264b4   0x00000024   Code   RO         6462    .text               mc_w.l(memseta.o)
    0x000264d8   0x000264d8   0x0000000e   Code   RO         6464    .text               mc_w.l(strlen.o)
    0x000264e6   0x000264e6   0x00000012   Code   RO         6468    .text               mc_w.l(strcpy.o)
    0x000264f8   0x000264f8   0x00000024   Code   RO         6761    .text               mc_w.l(init.o)
    0x0002651c   0x0002651c   0x00000056   Code   RO         6775    .text               mc_w.l(__dczerorl2.o)
    0x00026572   0x00026572   0x00000002   PAD
    0x00026574   0x00026574   0x00000038   Code   RO          673    i.ADS1299_CMD       ads1292.o
    0x000265ac   0x000265ac   0x00000040   Code   RO          674    i.ADS1299_READREG   ads1292.o
    0x000265ec   0x000265ec   0x0000003c   Code   RO          675    i.ADS1299_WRITEREG  ads1292.o
    0x00026628   0x00026628   0x000000a0   Code   RO          676    i.ADS_READDATA      ads1292.o
    0x000266c8   0x000266c8   0x000000a4   Code   RO         2554    i.GPIOTE_IRQHandler  nrfx_gpiote.o
    0x0002676c   0x0002676c   0x0000000e   Code   RO          782    i.LED1_Close        led.o
    0x0002677a   0x0002677a   0x0000000e   Code   RO          783    i.LED1_Open         led.o
    0x00026788   0x00026788   0x0000000e   Code   RO          785    i.LED2_Close        led.o
    0x00026796   0x00026796   0x0000000e   Code   RO          786    i.LED2_Open         led.o
    0x000267a4   0x000267a4   0x00000018   Code   RO          791    i.LED_Init          led.o
    0x000267bc   0x000267bc   0x00000058   Code   RO         2415    i.POWER_CLOCK_IRQHandler  nrfx_clock.o
    0x00026814   0x00026814   0x00000010   Code   RO         4544    i.RTC1_IRQHandler   drv_rtc.o
    0x00026824   0x00026824   0x00000148   Code   RO         3455    i.SAADC_IRQHandler  nrfx_saadc.o
    0x0002696c   0x0002696c   0x00000004   Code   RO         5907    i.SEGGER_RTT_Init   segger_rtt.o
    0x00026970   0x00026970   0x0000005c   Code   RO         5921    i.SEGGER_RTT_WriteNoLock  segger_rtt.o
    0x000269cc   0x000269cc   0x00000054   Code   RO         3751    i.SPIM0_SPIS0_TWIM0_TWIS0_SPI0_TWI0_IRQHandler  nrfx_spim.o
    0x00026a20   0x00026a20   0x00000004   Code   RO         6158    i.SWI2_EGU2_IRQHandler  nrf_sdh.o
    0x00026a24   0x00026a24   0x00000398   Code   RO         6376    i.SystemInit        system_nrf52.o
    0x00026dbc   0x00026dbc   0x00000050   Code   RO         3604    i.TIMER1_IRQHandler  nrfx_timer.o
    0x00026e0c   0x00026e0c   0x0000000c   Code   RO         2859    i.UARTE0_UART0_IRQHandler  nrfx_prs.o
    0x00026e18   0x00026e18   0x00000068   Code   RO         5925    i._DoInit           segger_rtt.o
    0x00026e80   0x00026e80   0x00000016   Code   RO         5926    i._GetAvailWriteSpace  segger_rtt.o
    0x00026e96   0x00026e96   0x0000005a   Code   RO         5928    i._WriteBlocking    segger_rtt.o
    0x00026ef0   0x00026ef0   0x00000042   Code   RO         5929    i._WriteNoCheck     segger_rtt.o
    0x00026f32   0x00026f32   0x0000000e   Code   RO         6769    i.__scatterload_copy  mc_w.l(handlers.o)
    0x00026f40   0x00026f40   0x00000002   Code   RO         6770    i.__scatterload_null  mc_w.l(handlers.o)
    0x00026f42   0x00026f42   0x0000000e   Code   RO         6771    i.__scatterload_zeroinit  mc_w.l(handlers.o)
    0x00026f50   0x00026f50   0x00000024   Code   RO         6159    i.__sd_nvic_app_accessible_irq  nrf_sdh.o
    0x00026f74   0x00026f74   0x000001a0   Code   RO          677    i.ads1299_spi_init  ads1292.o
    0x00027114   0x00027114   0x00000010   Code   RO         1398    i.adv_set_data_size_max_get  ble_advertising.o
    0x00027124   0x00027124   0x00000036   Code   RO         1131    i.advertising_buttons_configure  bsp_btn_ble.o
    0x0002715a   0x0002715a   0x00000002   PAD
    0x0002715c   0x0002715c   0x00000084   Code   RO         4074    i.app_error_fault_handler  app_error_weak.o
    0x000271e0   0x000271e0   0x00000016   Code   RO         4014    i.app_error_handler_bare  app_error.o
    0x000271f6   0x000271f6   0x00000016   Code   RO         4131    i.app_fifo_get      app_fifo.o
    0x0002720c   0x0002720c   0x0000001a   Code   RO         4134    i.app_fifo_put      app_fifo.o
    0x00027226   0x00027226   0x00000002   PAD
    0x00027228   0x00027228   0x0000000c   Code   RO         4255    i.app_timer_cnt_get  app_timer2.o
    0x00027234   0x00027234   0x00000016   Code   RO         4256    i.app_timer_create  app_timer2.o
    0x0002724a   0x0002724a   0x00000002   PAD
    0x0002724c   0x0002724c   0x0000005c   Code   RO         4257    i.app_timer_init    app_timer2.o
    0x000272a8   0x000272a8   0x00000030   Code   RO         4260    i.app_timer_start   app_timer2.o
    0x000272d8   0x000272d8   0x0000000c   Code   RO         4261    i.app_timer_stop    app_timer2.o
    0x000272e4   0x000272e4   0x00000054   Code   RO         4400    i.app_uart_put      app_uart_fifo.o
    0x00027338   0x00027338   0x00000048   Code   RO         4476    i.app_util_critical_region_enter  app_util_platform.o
    0x00027380   0x00027380   0x00000034   Code   RO         4477    i.app_util_critical_region_exit  app_util_platform.o
    0x000273b4   0x000273b4   0x00000048   Code   RO         1768    i.blcm_link_ctx_get  ble_link_ctx_manager.o
    0x000273fc   0x000273fc   0x00000182   Code   RO         1280    i.ble_advdata_encode  ble_advdata.o
    0x0002757e   0x0002757e   0x00000020   Code   RO         1282    i.ble_advdata_parse  ble_advdata.o
    0x0002759e   0x0002759e   0x00000046   Code   RO         1283    i.ble_advdata_search  ble_advdata.o
    0x000275e4   0x000275e4   0x00000006   Code   RO         1400    i.ble_advertising_conn_cfg_tag_set  ble_advertising.o
    0x000275ea   0x000275ea   0x000000f8   Code   RO         1401    i.ble_advertising_init  ble_advertising.o
    0x000276e2   0x000276e2   0x0000006a   Code   RO         1403    i.ble_advertising_on_ble_evt  ble_advertising.o
    0x0002774c   0x0002774c   0x00000230   Code   RO         1406    i.ble_advertising_start  ble_advertising.o
    0x0002797c   0x0002797c   0x00000068   Code   RO         1516    i.ble_conn_params_init  ble_conn_params.o
    0x000279e4   0x000279e4   0x00000012   Code   RO         1600    i.ble_conn_state_conn_idx  ble_conn_state.o
    0x000279f6   0x000279f6   0x00000002   PAD
    0x000279f8   0x000279f8   0x00000014   Code   RO         1614    i.ble_conn_state_valid  ble_conn_state.o
    0x00027a0c   0x00027a0c   0x00000060   Code   RO         1286    i.ble_device_addr_encode  ble_advdata.o
    0x00027a6c   0x00027a6c   0x000000dc   Code   RO            5    i.ble_evt_handler   main.o
    0x00027b48   0x00027b48   0x00000070   Code   RO         1132    i.ble_evt_handler   bsp_btn_ble.o
    0x00027bb8   0x00027bb8   0x000000fc   Code   RO         1518    i.ble_evt_handler   ble_conn_params.o
    0x00027cb4   0x00027cb4   0x00000118   Code   RO         1615    i.ble_evt_handler   ble_conn_state.o
    0x00027dcc   0x00027dcc   0x00000066   Code   RO         2003    i.ble_nus_data_send  ble_nus.o
    0x00027e32   0x00027e32   0x00000002   PAD
    0x00027e34   0x00027e34   0x000000d4   Code   RO         2004    i.ble_nus_init      ble_nus.o
    0x00027f08   0x00027f08   0x00000078   Code   RO         2005    i.ble_nus_on_ble_evt  ble_nus.o
    0x00027f80   0x00027f80   0x00000008   Code   RO         1797    i.ble_srv_is_notification_enabled  ble_srv_common.o
    0x00027f88   0x00027f88   0x0000000c   Code   RO          886    i.bsp_board_button_idx_to_pin  boards.o
    0x00027f94   0x00027f94   0x00000024   Code   RO          890    i.bsp_board_led_invert  boards.o
    0x00027fb8   0x00027fb8   0x00000010   Code   RO          891    i.bsp_board_led_off  boards.o
    0x00027fc8   0x00027fc8   0x00000010   Code   RO          892    i.bsp_board_led_on  boards.o
    0x00027fd8   0x00027fd8   0x00000020   Code   RO          893    i.bsp_board_led_state_get  boards.o
    0x00027ff8   0x00027ff8   0x00000012   Code   RO          894    i.bsp_board_leds_off  boards.o
    0x0002800a   0x0002800a   0x00000012   Code   RO          895    i.bsp_board_leds_on  boards.o
    0x0002801c   0x0002801c   0x00000024   Code   RO          896    i.bsp_board_pin_to_button_idx  boards.o
    0x00028040   0x00028040   0x0000001e   Code   RO         1134    i.bsp_btn_ble_sleep_mode_prepare  bsp_btn_ble.o
    0x0002805e   0x0002805e   0x00000002   PAD
    0x00028060   0x00028060   0x00000090   Code   RO          996    i.bsp_button_event_handler  bsp.o
    0x000280f0   0x000280f0   0x00000044   Code   RO         1000    i.bsp_event_to_button_action_assign  bsp.o
    0x00028134   0x00028134   0x00000018   Code   RO         1001    i.bsp_indication_set  bsp.o
    0x0002814c   0x0002814c   0x000001c8   Code   RO         1003    i.bsp_led_indication  bsp.o
    0x00028314   0x00028314   0x00000006   Code   RO         1005    i.bsp_wakeup_button_enable  bsp.o
    0x0002831a   0x0002831a   0x00000002   PAD
    0x0002831c   0x0002831c   0x00000094   Code   RO         5661    i.buf_prealloc      nrf_log_frontend.o
    0x000283b0   0x000283b0   0x0000002e   Code   RO         5103    i.buffer_add        nrf_fprintf_format.o
    0x000283de   0x000283de   0x00000002   PAD
    0x000283e0   0x000283e0   0x00000018   Code   RO         5662    i.buffer_is_empty   nrf_log_frontend.o
    0x000283f8   0x000283f8   0x0000001c   Code   RO         2555    i.channel_free      nrfx_gpiote.o
    0x00028414   0x00028414   0x00000048   Code   RO         2556    i.channel_port_alloc  nrfx_gpiote.o
    0x0002845c   0x0002845c   0x00000010   Code   RO         2557    i.channel_port_get  nrfx_gpiote.o
    0x0002846c   0x0002846c   0x00000190   Code   RO         1799    i.characteristic_add  ble_srv_common.o
    0x000285fc   0x000285fc   0x00000028   Code   RO         2064    i.clock_clk_started_notify  nrf_drv_clock.o
    0x00028624   0x00028624   0x0000001c   Code   RO         2065    i.clock_irq_handler  nrf_drv_clock.o
    0x00028640   0x00028640   0x00000018   Code   RO         4263    i.compare_func      app_timer2.o
    0x00028658   0x00028658   0x0000003e   Code   RO         1616    i.conn_handle_list_get  ble_conn_state.o
    0x00028696   0x00028696   0x00000088   Code   RO         1287    i.conn_int_encode   ble_advdata.o
    0x0002871e   0x0002871e   0x00000004   Code   RO            7    i.conn_params_error_handler  main.o
    0x00028722   0x00028722   0x00000002   PAD
    0x00028724   0x00028724   0x00000048   Code   RO         1519    i.conn_params_negotiation  ble_conn_params.o
    0x0002876c   0x0002876c   0x000000c0   Code   RO         1857    i.data_length_update  nrf_ble_gatt.o
    0x0002882c   0x0002882c   0x000000fc   Code   RO            8    i.datacjfunc        main.o
    0x00028928   0x00028928   0x00000014   Code   RO         5663    i.dropped_sat16_get  nrf_log_frontend.o
    0x0002893c   0x0002893c   0x00000014   Code   RO         4545    i.drv_rtc_compare_disable  drv_rtc.o
    0x00028950   0x00028950   0x0000000e   Code   RO         4548    i.drv_rtc_compare_pending  drv_rtc.o
    0x0002895e   0x0002895e   0x00000050   Code   RO         4549    i.drv_rtc_compare_set  drv_rtc.o
    0x000289ae   0x000289ae   0x00000008   Code   RO         4550    i.drv_rtc_counter_get  drv_rtc.o
    0x000289b6   0x000289b6   0x00000002   PAD
    0x000289b8   0x000289b8   0x000000d8   Code   RO         4551    i.drv_rtc_init      drv_rtc.o
    0x00028a90   0x00028a90   0x0000001e   Code   RO         4552    i.drv_rtc_irq_trigger  drv_rtc.o
    0x00028aae   0x00028aae   0x00000008   Code   RO         4554    i.drv_rtc_overflow_enable  drv_rtc.o
    0x00028ab6   0x00028ab6   0x00000008   Code   RO         4555    i.drv_rtc_overflow_pending  drv_rtc.o
    0x00028abe   0x00028abe   0x00000008   Code   RO         4556    i.drv_rtc_start     drv_rtc.o
    0x00028ac6   0x00028ac6   0x00000008   Code   RO         4557    i.drv_rtc_stop      drv_rtc.o
    0x00028ace   0x00028ace   0x000000de   Code   RO         4562    i.drv_rtc_windowed_compare_set  drv_rtc.o
    0x00028bac   0x00028bac   0x00000012   Code   RO         4563    i.evt_enable        drv_rtc.o
    0x00028bbe   0x00028bbe   0x00000014   Code   RO         4564    i.evt_pending       drv_rtc.o
    0x00028bd2   0x00028bd2   0x00000014   Code   RO         4137    i.fifo_get          app_fifo.o
    0x00028be6   0x00028be6   0x00000012   Code   RO         4138    i.fifo_put          app_fifo.o
    0x00028bf8   0x00028bf8   0x0000000a   Code   RO         1617    i.flag_toggle       ble_conn_state.o
    0x00028c02   0x00028c02   0x00000028   Code   RO         1408    i.flags_set         ble_advertising.o
    0x00028c2a   0x00028c2a   0x00000002   PAD
    0x00028c2c   0x00028c2c   0x0000005c   Code   RO            9    i.gatt_evt_handler  main.o
    0x00028c88   0x00028c88   0x00000030   Code   RO           10    i.gatt_init         main.o
    0x00028cb8   0x00028cb8   0x00000034   Code   RO         4264    i.get_now           app_timer2.o
    0x00028cec   0x00028cec   0x00000074   Code   RO           11    i.gpio_init         main.o
    0x00028d60   0x00028d60   0x000000f8   Code   RO           12    i.in_pin_handler    main.o
    0x00028e58   0x00028e58   0x00000014   Code   RO         1520    i.instance_get      ble_conn_params.o
    0x00028e6c   0x00028e6c   0x000000a6   Code   RO         5104    i.int_print         nrf_fprintf_format.o
    0x00028f12   0x00028f12   0x00000034   Code   RO         5664    i.invalid_packets_omit  nrf_log_frontend.o
    0x00028f46   0x00028f46   0x00000044   Code   RO         1521    i.is_conn_params_ok  ble_conn_params.o
    0x00028f8a   0x00028f8a   0x00000002   PAD
    0x00028f8c   0x00028f8c   0x00000030   Code   RO         1007    i.leds_off          bsp.o
    0x00028fbc   0x00028fbc   0x00000018   Code   RO         1858    i.link_init         nrf_ble_gatt.o
    0x00028fd4   0x00028fd4   0x000000a4   Code   RO         5665    i.log_skip          nrf_log_frontend.o
    0x00029078   0x00029078   0x000001c4   Code   RO           13    i.main              main.o
    0x0002923c   0x0002923c   0x00000064   Code   RO         1288    i.manuf_specific_data_encode  ble_advdata.o
    0x000292a0   0x000292a0   0x0000007e   Code   RO         5143    i.memobj_op         nrf_memobj.o
    0x0002931e   0x0002931e   0x00000002   PAD
    0x00029320   0x00029320   0x00000030   Code   RO         5666    i.module_idx_get    nrf_log_frontend.o
    0x00029350   0x00029350   0x000000a6   Code   RO         1289    i.name_encode       ble_advdata.o
    0x000293f6   0x000293f6   0x00000026   Code   RO         4759    i.nrf_atfifo_init   nrf_atfifo.o
    0x0002941c   0x0002941c   0x00000016   Code   RO         4760    i.nrf_atfifo_item_alloc  nrf_atfifo.o
    0x00029432   0x00029432   0x00000016   Code   RO         4761    i.nrf_atfifo_item_free  nrf_atfifo.o
    0x00029448   0x00029448   0x00000016   Code   RO         4762    i.nrf_atfifo_item_get  nrf_atfifo.o
    0x0002945e   0x0002945e   0x00000016   Code   RO         4763    i.nrf_atfifo_item_put  nrf_atfifo.o
    0x00029474   0x00029474   0x00000016   Code   RO         4825    i.nrf_atflags_clear  nrf_atflags.o
    0x0002948a   0x0002948a   0x00000016   Code   RO         4830    i.nrf_atflags_get   nrf_atflags.o
    0x000294a0   0x000294a0   0x00000014   Code   RO         4832    i.nrf_atflags_set   nrf_atflags.o
    0x000294b4   0x000294b4   0x00000006   Code   RO         4893    i.nrf_atomic_flag_clear_fetch  nrf_atomic.o
    0x000294ba   0x000294ba   0x00000006   Code   RO         4894    i.nrf_atomic_flag_set  nrf_atomic.o
    0x000294c0   0x000294c0   0x0000000c   Code   RO         4896    i.nrf_atomic_u32_add  nrf_atomic.o
    0x000294cc   0x000294cc   0x0000000c   Code   RO         4897    i.nrf_atomic_u32_and  nrf_atomic.o
    0x000294d8   0x000294d8   0x0000000a   Code   RO         4900    i.nrf_atomic_u32_fetch_and  nrf_atomic.o
    0x000294e2   0x000294e2   0x0000000a   Code   RO         4902    i.nrf_atomic_u32_fetch_store  nrf_atomic.o
    0x000294ec   0x000294ec   0x0000000c   Code   RO         4906    i.nrf_atomic_u32_or  nrf_atomic.o
    0x000294f8   0x000294f8   0x0000000c   Code   RO         4908    i.nrf_atomic_u32_sub  nrf_atomic.o
    0x00029504   0x00029504   0x00000044   Code   RO         5027    i.nrf_balloc_alloc  nrf_balloc.o
    0x00029548   0x00029548   0x00000030   Code   RO         5028    i.nrf_balloc_free   nrf_balloc.o
    0x00029578   0x00029578   0x00000030   Code   RO         5029    i.nrf_balloc_init   nrf_balloc.o
    0x000295a8   0x000295a8   0x00000010   Code   RO         2558    i.nrf_bitmask_bit_is_set  nrfx_gpiote.o
    0x000295b8   0x000295b8   0x00000018   Code   RO         1860    i.nrf_ble_gatt_att_mtu_periph_set  nrf_ble_gatt.o
    0x000295d0   0x000295d0   0x00000022   Code   RO         1864    i.nrf_ble_gatt_init  nrf_ble_gatt.o
    0x000295f2   0x000295f2   0x00000002   PAD
    0x000295f4   0x000295f4   0x00000150   Code   RO         1865    i.nrf_ble_gatt_on_ble_evt  nrf_ble_gatt.o
    0x00029744   0x00029744   0x00000016   Code   RO         1959    i.nrf_ble_qwr_conn_handle_assign  nrf_ble_qwr.o
    0x0002975a   0x0002975a   0x00000024   Code   RO         1960    i.nrf_ble_qwr_init  nrf_ble_qwr.o
    0x0002977e   0x0002977e   0x000000b6   Code   RO         1961    i.nrf_ble_qwr_on_ble_evt  nrf_ble_qwr.o
    0x00029834   0x00029834   0x0000000e   Code   RO         2416    i.nrf_clock_event_check  nrfx_clock.o
    0x00029842   0x00029842   0x00000010   Code   RO         2417    i.nrf_clock_event_clear  nrfx_clock.o
    0x00029852   0x00029852   0x00000002   PAD
    0x00029854   0x00029854   0x00000020   Code   RO           14    i.nrf_delay_ms      main.o
    0x00029874   0x00029874   0x00000016   Code   RO          678    i.nrf_delay_ms      ads1292.o
    0x0002988a   0x0002988a   0x00000002   PAD
    0x0002988c   0x0002988c   0x00000040   Code   RO         2072    i.nrf_drv_clock_init  nrf_drv_clock.o
    0x000298cc   0x000298cc   0x00000034   Code   RO         2076    i.nrf_drv_clock_lfclk_release  nrf_drv_clock.o
    0x00029900   0x00029900   0x00000070   Code   RO         3868    i.nrf_drv_spi_init  nrf_drv_spi.o
    0x00029970   0x00029970   0x00000016   Code   RO          679    i.nrf_drv_spi_transfer  ads1292.o
    0x00029986   0x00029986   0x00000002   PAD
    0x00029988   0x00029988   0x00000020   Code   RO         4402    i.nrf_drv_uart_tx   app_uart_fifo.o
    0x000299a8   0x000299a8   0x0000001a   Code   RO         5064    i.nrf_fprintf       nrf_fprintf.o
    0x000299c2   0x000299c2   0x00000018   Code   RO         5065    i.nrf_fprintf_buffer_flush  nrf_fprintf.o
    0x000299da   0x000299da   0x000001da   Code   RO         5105    i.nrf_fprintf_fmt   nrf_fprintf_format.o
    0x00029bb4   0x00029bb4   0x00000024   Code   RO          680    i.nrf_gpio_cfg      ads1292.o
    0x00029bd8   0x00029bd8   0x00000024   Code   RO         2559    i.nrf_gpio_cfg      nrfx_gpiote.o
    0x00029bfc   0x00029bfc   0x00000024   Code   RO         3752    i.nrf_gpio_cfg      nrfx_spim.o
    0x00029c20   0x00029c20   0x00000014   Code   RO          681    i.nrf_gpio_cfg_output  ads1292.o
    0x00029c34   0x00029c34   0x00000010   Code   RO          792    i.nrf_gpio_cfg_output  led.o
    0x00029c44   0x00029c44   0x00000014   Code   RO         3753    i.nrf_gpio_cfg_output  nrfx_spim.o
    0x00029c58   0x00029c58   0x00000022   Code   RO         2561    i.nrf_gpio_cfg_sense_set  nrfx_gpiote.o
    0x00029c7a   0x00029c7a   0x00000002   PAD
    0x00029c7c   0x00029c7c   0x0000002c   Code   RO         2562    i.nrf_gpio_latches_read_and_clear  nrfx_gpiote.o
    0x00029ca8   0x00029ca8   0x0000000e   Code   RO         3754    i.nrf_gpio_pin_clear  nrfx_spim.o
    0x00029cb6   0x00029cb6   0x0000001e   Code   RO         2563    i.nrf_gpio_pin_present_check  nrfx_gpiote.o
    0x00029cd4   0x00029cd4   0x0000000e   Code   RO         3755    i.nrf_gpio_pin_set  nrfx_spim.o
    0x00029ce2   0x00029ce2   0x0000001c   Code   RO           15    i.nrf_gpio_pin_toggle  main.o
    0x00029cfe   0x00029cfe   0x00000016   Code   RO          899    i.nrf_gpio_pin_write  boards.o
    0x00029d14   0x00029d14   0x00000014   Code   RO         2564    i.nrf_gpiote_event_clear  nrfx_gpiote.o
    0x00029d28   0x00029d28   0x00000010   Code   RO         2565    i.nrf_gpiote_event_is_set  nrfx_gpiote.o
    0x00029d38   0x00029d38   0x0000005c   Code   RO         5667    i.nrf_log_backend_add  nrf_log_frontend.o
    0x00029d94   0x00029d94   0x00000002   Code   RO         5522    i.nrf_log_backend_rtt_flush  nrf_log_backend_rtt.o
    0x00029d96   0x00029d96   0x00000004   Code   RO         5523    i.nrf_log_backend_rtt_init  nrf_log_backend_rtt.o
    0x00029d9a   0x00029d9a   0x00000002   Code   RO         5524    i.nrf_log_backend_rtt_panic_set  nrf_log_backend_rtt.o
    0x00029d9c   0x00029d9c   0x00000018   Code   RO         5525    i.nrf_log_backend_rtt_put  nrf_log_backend_rtt.o
    0x00029db4   0x00029db4   0x000000c2   Code   RO         5588    i.nrf_log_backend_serial_put  nrf_log_backend_serial.o
    0x00029e76   0x00029e76   0x00000002   PAD
    0x00029e78   0x00029e78   0x00000030   Code   RO         5669    i.nrf_log_color_id_get  nrf_log_frontend.o
    0x00029ea8   0x00029ea8   0x0000001c   Code   RO         5622    i.nrf_log_default_backends_init  nrf_log_default_backends.o
    0x00029ec4   0x00029ec4   0x00000234   Code   RO         5670    i.nrf_log_frontend_dequeue  nrf_log_frontend.o
    0x0002a0f8   0x0002a0f8   0x00000008   Code   RO         5672    i.nrf_log_frontend_std_0  nrf_log_frontend.o
    0x0002a100   0x0002a100   0x0000000e   Code   RO         5673    i.nrf_log_frontend_std_1  nrf_log_frontend.o
    0x0002a10e   0x0002a10e   0x00000010   Code   RO         5674    i.nrf_log_frontend_std_2  nrf_log_frontend.o
    0x0002a11e   0x0002a11e   0x00000002   PAD
    0x0002a120   0x0002a120   0x000000b0   Code   RO         5848    i.nrf_log_hexdump_entry_process  nrf_log_str_formatter.o
    0x0002a1d0   0x0002a1d0   0x00000028   Code   RO         5679    i.nrf_log_init      nrf_log_frontend.o
    0x0002a1f8   0x0002a1f8   0x00000014   Code   RO         5680    i.nrf_log_module_cnt_get  nrf_log_frontend.o
    0x0002a20c   0x0002a20c   0x0000001c   Code   RO         5683    i.nrf_log_module_name_get  nrf_log_frontend.o
    0x0002a228   0x0002a228   0x00000028   Code   RO         5684    i.nrf_log_panic     nrf_log_frontend.o
    0x0002a250   0x0002a250   0x000000b6   Code   RO         5849    i.nrf_log_std_entry_process  nrf_log_str_formatter.o
    0x0002a306   0x0002a306   0x00000060   Code   RO         5144    i.nrf_memobj_alloc  nrf_memobj.o
    0x0002a366   0x0002a366   0x00000032   Code   RO         5145    i.nrf_memobj_free   nrf_memobj.o
    0x0002a398   0x0002a398   0x00000008   Code   RO         5146    i.nrf_memobj_get    nrf_memobj.o
    0x0002a3a0   0x0002a3a0   0x00000004   Code   RO         5147    i.nrf_memobj_pool_init  nrf_memobj.o
    0x0002a3a4   0x0002a3a4   0x0000001e   Code   RO         5148    i.nrf_memobj_put    nrf_memobj.o
    0x0002a3c2   0x0002a3c2   0x00000010   Code   RO         5149    i.nrf_memobj_read   nrf_memobj.o
    0x0002a3d2   0x0002a3d2   0x00000010   Code   RO         5150    i.nrf_memobj_write  nrf_memobj.o
    0x0002a3e2   0x0002a3e2   0x00000002   PAD
    0x0002a3e4   0x0002a3e4   0x00000028   Code   RO         5222    i.nrf_pwr_mgmt_init  nrf_pwr_mgmt.o
    0x0002a40c   0x0002a40c   0x00000044   Code   RO         5223    i.nrf_pwr_mgmt_run  nrf_pwr_mgmt.o
    0x0002a450   0x0002a450   0x00000004   Code   RO           16    i.nrf_qwr_error_handler  main.o
    0x0002a454   0x0002a454   0x0000001c   Code   RO         5302    i.nrf_ringbuf_init  nrf_ringbuf.o
    0x0002a470   0x0002a470   0x0000000c   Code   RO         4565    i.nrf_rtc_event_clear  drv_rtc.o
    0x0002a47c   0x0002a47c   0x00000010   Code   RO         3456    i.nrf_saadc_buffer_init  nrfx_saadc.o
    0x0002a48c   0x0002a48c   0x00000010   Code   RO         3458    i.nrf_saadc_event_check  nrfx_saadc.o
    0x0002a49c   0x0002a49c   0x00000014   Code   RO         3459    i.nrf_saadc_event_clear  nrfx_saadc.o
    0x0002a4b0   0x0002a4b0   0x00000014   Code   RO         6279    i.nrf_sdh_ble_app_ram_start_get  nrf_sdh_ble.o
    0x0002a4c4   0x0002a4c4   0x00000130   Code   RO         6280    i.nrf_sdh_ble_default_cfg_set  nrf_sdh_ble.o
    0x0002a5f4   0x0002a5f4   0x00000130   Code   RO         6281    i.nrf_sdh_ble_enable  nrf_sdh_ble.o
    0x0002a724   0x0002a724   0x00000060   Code   RO         6282    i.nrf_sdh_ble_evts_poll  nrf_sdh_ble.o
    0x0002a784   0x0002a784   0x00000074   Code   RO         6161    i.nrf_sdh_enable_request  nrf_sdh.o
    0x0002a7f8   0x0002a7f8   0x00000024   Code   RO         6162    i.nrf_sdh_evts_poll  nrf_sdh.o
    0x0002a81c   0x0002a81c   0x0000000c   Code   RO         6163    i.nrf_sdh_is_enabled  nrf_sdh.o
    0x0002a828   0x0002a828   0x0000003c   Code   RO         6333    i.nrf_sdh_soc_evts_poll  nrf_sdh_soc.o
    0x0002a864   0x0002a864   0x0000000a   Code   RO         5360    i.nrf_section_iter_init  nrf_section_iter.o
    0x0002a86e   0x0002a86e   0x00000024   Code   RO         5361    i.nrf_section_iter_item_set  nrf_section_iter.o
    0x0002a892   0x0002a892   0x00000020   Code   RO         5362    i.nrf_section_iter_next  nrf_section_iter.o
    0x0002a8b2   0x0002a8b2   0x00000022   Code   RO         5395    i.nrf_sortlist_add  nrf_sortlist.o
    0x0002a8d4   0x0002a8d4   0x00000006   Code   RO         5397    i.nrf_sortlist_peek  nrf_sortlist.o
    0x0002a8da   0x0002a8da   0x0000000e   Code   RO         5398    i.nrf_sortlist_pop  nrf_sortlist.o
    0x0002a8e8   0x0002a8e8   0x0000001e   Code   RO         5399    i.nrf_sortlist_remove  nrf_sortlist.o
    0x0002a906   0x0002a906   0x0000000a   Code   RO         3756    i.nrf_spim_event_check  nrfx_spim.o
    0x0002a910   0x0002a910   0x00000038   Code   RO         5451    i.nrf_strerror_find  nrf_strerror.o
    0x0002a948   0x0002a948   0x00000014   Code   RO         5452    i.nrf_strerror_get  nrf_strerror.o
    0x0002a95c   0x0002a95c   0x0000000c   Code   RO         3605    i.nrf_timer_event_clear  nrfx_timer.o
    0x0002a968   0x0002a968   0x0000000a   Code   RO         2918    i.nrf_uart_event_check  nrfx_uart.o
    0x0002a972   0x0002a972   0x0000000c   Code   RO         2919    i.nrf_uart_event_clear  nrfx_uart.o
    0x0002a97e   0x0002a97e   0x0000000a   Code   RO         3107    i.nrf_uarte_event_check  nrfx_uarte.o
    0x0002a988   0x0002a988   0x0000000c   Code   RO         3108    i.nrf_uarte_event_clear  nrfx_uarte.o
    0x0002a994   0x0002a994   0x00000010   Code   RO         2079    i.nrf_wdt_started   nrf_drv_clock.o
    0x0002a9a4   0x0002a9a4   0x0000002c   Code   RO         2422    i.nrfx_clock_enable  nrfx_clock.o
    0x0002a9d0   0x0002a9d0   0x00000020   Code   RO         2425    i.nrfx_clock_init   nrfx_clock.o
    0x0002a9f0   0x0002a9f0   0x00000030   Code   RO         2428    i.nrfx_clock_lfclk_stop  nrfx_clock.o
    0x0002aa20   0x0002aa20   0x00000010   Code   RO          682    i.nrfx_coredep_delay_us  ads1292.o
    0x0002aa30   0x0002aa30   0x00000010   Code   RO         4566    i.nrfx_coredep_delay_us  drv_rtc.o
    0x0002aa40   0x0002aa40   0x000000a0   Code   RO         2571    i.nrfx_gpiote_in_event_enable  nrfx_gpiote.o
    0x0002aae0   0x0002aae0   0x000000cc   Code   RO         2573    i.nrfx_gpiote_in_init  nrfx_gpiote.o
    0x0002abac   0x0002abac   0x00000074   Code   RO         2576    i.nrfx_gpiote_init  nrfx_gpiote.o
    0x0002ac20   0x0002ac20   0x00000010   Code   RO         3109    i.nrfx_is_in_ram    nrfx_uarte.o
    0x0002ac30   0x0002ac30   0x00000010   Code   RO         3757    i.nrfx_is_in_ram    nrfx_spim.o
    0x0002ac40   0x0002ac40   0x0000003a   Code   RO         2860    i.nrfx_prs_acquire  nrfx_prs.o
    0x0002ac7a   0x0002ac7a   0x00000002   PAD
    0x0002ac7c   0x0002ac7c   0x0000016c   Code   RO         3760    i.nrfx_spim_init    nrfx_spim.o
    0x0002ade8   0x0002ade8   0x00000064   Code   RO         3763    i.nrfx_spim_xfer    nrfx_spim.o
    0x0002ae4c   0x0002ae4c   0x0000003a   Code   RO         3608    i.nrfx_timer_compare  nrfx_timer.o
    0x0002ae86   0x0002ae86   0x0000002a   Code   RO         3613    i.nrfx_timer_extended_compare  nrfx_timer.o
    0x0002aeb0   0x0002aeb0   0x000000c0   Code   RO         3615    i.nrfx_timer_init   nrfx_timer.o
    0x0002af70   0x0002af70   0x000000a8   Code   RO         2929    i.nrfx_uart_tx      nrfx_uart.o
    0x0002b018   0x0002b018   0x00000020   Code   RO         2931    i.nrfx_uart_tx_in_progress  nrfx_uart.o
    0x0002b038   0x0002b038   0x000000ac   Code   RO         3116    i.nrfx_uarte_tx     nrfx_uarte.o
    0x0002b0e4   0x0002b0e4   0x0000001c   Code   RO         3118    i.nrfx_uarte_tx_in_progress  nrfx_uarte.o
    0x0002b100   0x0002b100   0x0000014c   Code   RO           17    i.nus_data_handler  main.o
    0x0002b24c   0x0002b24c   0x00000024   Code   RO           18    i.on_adv_evt        main.o
    0x0002b270   0x0002b270   0x0000001c   Code   RO           19    i.on_conn_params_evt  main.o
    0x0002b28c   0x0002b28c   0x0000006a   Code   RO         2006    i.on_connect        ble_nus.o
    0x0002b2f6   0x0002b2f6   0x00000002   PAD
    0x0002b2f8   0x0002b2f8   0x000000a8   Code   RO         1866    i.on_connected_evt  nrf_ble_gatt.o
    0x0002b3a0   0x0002b3a0   0x000000ac   Code   RO         1867    i.on_exchange_mtu_request_evt  nrf_ble_gatt.o
    0x0002b44c   0x0002b44c   0x00000084   Code   RO         2007    i.on_write          ble_nus.o
    0x0002b4d0   0x0002b4d0   0x00000012   Code   RO         1409    i.phy_is_valid      ble_advertising.o
    0x0002b4e2   0x0002b4e2   0x00000002   PAD
    0x0002b4e4   0x0002b4e4   0x0000001c   Code   RO         2595    i.pin_configured_set  nrfx_gpiote.o
    0x0002b500   0x0002b500   0x00000018   Code   RO         2596    i.pin_in_use_by_port  nrfx_gpiote.o
    0x0002b518   0x0002b518   0x00000018   Code   RO         2597    i.pin_in_use_by_te  nrfx_gpiote.o
    0x0002b530   0x0002b530   0x000000b4   Code   RO         2598    i.port_event_handle  nrfx_gpiote.o
    0x0002b5e4   0x0002b5e4   0x00000010   Code   RO         2599    i.port_handler_polarity_get  nrfx_gpiote.o
    0x0002b5f4   0x0002b5f4   0x0000003c   Code   RO         5851    i.postfix_process   nrf_log_str_formatter.o
    0x0002b630   0x0002b630   0x00000098   Code   RO         5852    i.prefix_process    nrf_log_str_formatter.o
    0x0002b6c8   0x0002b6c8   0x00000018   Code   RO         2862    i.prs_box_get       nrfx_prs.o
    0x0002b6e0   0x0002b6e0   0x0000005c   Code   RO         4265    i.rtc_irq           app_timer2.o
    0x0002b73c   0x0002b73c   0x00000064   Code   RO         4266    i.rtc_schedule      app_timer2.o
    0x0002b7a0   0x0002b7a0   0x00000074   Code   RO         4267    i.rtc_update        app_timer2.o
    0x0002b814   0x0002b814   0x00000058   Code   RO         2080    i.sd_state_evt_handler  nrf_drv_clock.o
    0x0002b86c   0x0002b86c   0x00000030   Code   RO         6168    i.sdh_request_observer_notify  nrf_sdh.o
    0x0002b89c   0x0002b89c   0x0000002c   Code   RO         6169    i.sdh_state_observer_notify  nrf_sdh.o
    0x0002b8c8   0x0002b8c8   0x00000010   Code   RO         1522    i.send_error_evt    ble_conn_params.o
    0x0002b8d8   0x0002b8d8   0x00000050   Code   RO         5526    i.serial_tx         nrf_log_backend_rtt.o
    0x0002b928   0x0002b928   0x00000088   Code   RO         1290    i.service_data_encode  ble_advdata.o
    0x0002b9b0   0x0002b9b0   0x00000030   Code   RO         1801    i.set_security_req  ble_srv_common.o
    0x0002b9e0   0x0002b9e0   0x00000028   Code   RO           22    i.sleep_mode_enter  main.o
    0x0002ba08   0x0002ba08   0x00000018   Code   RO         2081    i.soc_evt_handler   nrf_drv_clock.o
    0x0002ba20   0x0002ba20   0x00000058   Code   RO         6171    i.softdevices_evt_irq_enable  nrf_sdh.o
    0x0002ba78   0x0002ba78   0x0000000c   Code   RO         4268    i.sortlist_pop      app_timer2.o
    0x0002ba84   0x0002ba84   0x0000000c   Code   RO          683    i.spi_event_handler  ads1292.o
    0x0002ba90   0x0002ba90   0x00000038   Code   RO         3869    i.spim_evt_handler  nrf_drv_spi.o
    0x0002bac8   0x0002bac8   0x000000bc   Code   RO         3764    i.spim_xfer         nrfx_spim.o
    0x0002bb84   0x0002bb84   0x00000094   Code   RO         5686    i.std_n             nrf_log_frontend.o
    0x0002bc18   0x0002bc18   0x00000002   Code   RO           23    i.timer_data_event_handler  main.o
    0x0002bc1a   0x0002bc1a   0x00000002   PAD
    0x0002bc1c   0x0002bc1c   0x00000058   Code   RO         4269    i.timer_expire      app_timer2.o
    0x0002bc74   0x0002bc74   0x00000078   Code   RO         4270    i.timer_req_process  app_timer2.o
    0x0002bcec   0x0002bcec   0x00000038   Code   RO         4271    i.timer_req_schedule  app_timer2.o
    0x0002bd24   0x0002bd24   0x00000094   Code   RO           25    i.timers_init       main.o
    0x0002bdb8   0x0002bdb8   0x00000020   Code   RO         2936    i.tx_byte           nrfx_uart.o
    0x0002bdd8   0x0002bdd8   0x0000000a   Code   RO         1291    i.uint16_encode     ble_advdata.o
    0x0002bde2   0x0002bde2   0x00000002   PAD
    0x0002bde4   0x0002bde4   0x000000b4   Code   RO         5106    i.unsigned_print    nrf_fprintf_format.o
    0x0002be98   0x0002be98   0x00000068   Code   RO         1523    i.update_timeout_handler  ble_conn_params.o
    0x0002bf00   0x0002bf00   0x00000018   Code   RO         1410    i.use_whitelist     ble_advertising.o
    0x0002bf18   0x0002bf18   0x00000024   Code   RO         1962    i.user_mem_reply    nrf_ble_qwr.o
    0x0002bf3c   0x0002bf3c   0x00000030   Code   RO         1292    i.uuid_list_encode  ble_advdata.o
    0x0002bf6c   0x0002bf6c   0x0000009e   Code   RO         1293    i.uuid_list_sized_encode  ble_advdata.o
    0x0002c00a   0x0002c00a   0x0000003a   Code   RO         1009    i.wakeup_button_cfg  bsp.o
    0x0002c044   0x0002c044   0x0000000c   PAD
    0x0002c050   0x0002c050   0x00000028   Data   RO           29    .constdata          main.o
    0x0002c078   0x0002c078   0x00000008   PAD
    0x0002c080   0x0002c080   0x00000028   Data   RO          685    .constdata          ads1292.o
    0x0002c0a8   0x0002c0a8   0x00000008   Data   RO          900    .constdata          boards.o
    0x0002c0b0   0x0002c0b0   0x0000002c   Data   RO         1014    .constdata          bsp.o
    0x0002c0dc   0x0002c0dc   0x00000016   Data   RO         1868    .constdata          nrf_ble_gatt.o
    0x0002c0f2   0x0002c0f2   0x00000002   PAD
    0x0002c0f4   0x0002c0f4   0x00000010   Data   RO         2008    .constdata          ble_nus.o
    0x0002c104   0x0002c104   0x00000004   Data   RO         2601    .constdata          nrfx_gpiote.o
    0x0002c108   0x0002c108   0x00000004   Data   RO         3767    .constdata          nrfx_spim.o
    0x0002c10c   0x0002c10c   0x00000010   Data   RO         3871    .constdata          nrf_drv_spi.o
    0x0002c11c   0x0002c11c   0x00000014   Data   RO         4274    .constdata          app_timer2.o
    0x0002c130   0x0002c130   0x00000006   Data   RO         4567    .constdata          drv_rtc.o
    0x0002c136   0x0002c136   0x00000010   Data   RO         5107    .constdata          nrf_fprintf_format.o
    0x0002c146   0x0002c146   0x00000002   PAD
    0x0002c148   0x0002c148   0x00000018   Data   RO         5227    .constdata          nrf_pwr_mgmt.o
    0x0002c160   0x0002c160   0x0000000c   Data   RO         5228    .constdata          nrf_pwr_mgmt.o
    0x0002c16c   0x0002c16c   0x0000013c   Data   RO         5453    .constdata          nrf_strerror.o
    0x0002c2a8   0x0002c2a8   0x0000000c   Data   RO         5528    .constdata          nrf_log_backend_rtt.o
    0x0002c2b4   0x0002c2b4   0x0000000c   PAD
    0x0002c2c0   0x0002c2c0   0x00000006   Data   RO         5529    .constdata          nrf_log_backend_rtt.o
    0x0002c2c6   0x0002c2c6   0x00000002   PAD
    0x0002c2c8   0x0002c2c8   0x0000000c   Data   RO         5691    .constdata          nrf_log_frontend.o
    0x0002c2d4   0x0002c2d4   0x00000010   Data   RO         6172    .constdata          nrf_sdh.o
    0x0002c2e4   0x0002c2e4   0x00000028   Data   RO         6173    .constdata          nrf_sdh.o
    0x0002c30c   0x0002c30c   0x00000010   Data   RO         6174    .constdata          nrf_sdh.o
    0x0002c31c   0x0002c31c   0x00000010   Data   RO         6175    .constdata          nrf_sdh.o
    0x0002c32c   0x0002c32c   0x00000020   Data   RO         6283    .constdata          nrf_sdh_ble.o
    0x0002c34c   0x0002c34c   0x00000010   Data   RO         6284    .constdata          nrf_sdh_ble.o
    0x0002c35c   0x0002c35c   0x00000010   Data   RO         6334    .constdata          nrf_sdh_soc.o
    0x0002c36c   0x0002c36c   0x0000000c   Data   RO         6335    .constdata          nrf_sdh_soc.o
    0x0002c378   0x0002c378   0x00000081   Data   RO         6433    .constdata          mc_w.l(ctype_o.o)
    0x0002c3f9   0x0002c3f9   0x00000003   PAD
    0x0002c3fc   0x0002c3fc   0x00000004   Data   RO         6434    .constdata          mc_w.l(ctype_o.o)
    0x0002c400   0x0002c400   0x000000a2   Data   RO         1869    .conststring        nrf_ble_gatt.o
    0x0002c4a2   0x0002c4a2   0x00000002   PAD
    0x0002c4a4   0x0002c4a4   0x000003eb   Data   RO         5454    .conststring        nrf_strerror.o
    0x0002c88f   0x0002c88f   0x00000001   PAD
    0x0002c890   0x0002c890   0x00000066   Data   RO         5853    .conststring        nrf_log_str_formatter.o
    0x0002c8f6   0x0002c8f6   0x00000002   PAD
    0x0002c8f8   0x0002c8f8   0x000001cf   Data   RO         6285    .conststring        nrf_sdh_ble.o
    0x0002cac7   0x0002cac7   0x00000001   PAD
    0x0002cac8   0x0002cac8   0x0000000d   Data   RO         1870    .conststrlit        nrf_ble_gatt.o
    0x0002cad5   0x0002cad5   0x00000003   PAD
    0x0002cad8   0x0002cad8   0x00000008   Data   RO         2009    .conststrlit        ble_nus.o
    0x0002cae0   0x0002cae0   0x00000006   Data   RO         2087    .conststrlit        nrf_drv_clock.o
    0x0002cae6   0x0002cae6   0x00000002   PAD
    0x0002cae8   0x0002cae8   0x00000006   Data   RO         2432    .conststrlit        nrfx_clock.o
    0x0002caee   0x0002caee   0x00000002   PAD
    0x0002caf0   0x0002caf0   0x00000007   Data   RO         2605    .conststrlit        nrfx_gpiote.o
    0x0002caf7   0x0002caf7   0x00000001   PAD
    0x0002caf8   0x0002caf8   0x00000004   Data   RO         2864    .conststrlit        nrfx_prs.o
    0x0002cafc   0x0002cafc   0x00000005   Data   RO         2944    .conststrlit        nrfx_uart.o
    0x0002cb01   0x0002cb01   0x00000003   PAD
    0x0002cb04   0x0002cb04   0x00000006   Data   RO         3128    .conststrlit        nrfx_uarte.o
    0x0002cb0a   0x0002cb0a   0x00000002   PAD
    0x0002cb0c   0x0002cb0c   0x00000004   Data   RO         3292    .conststrlit        nrfx_ppi.o
    0x0002cb10   0x0002cb10   0x00000006   Data   RO         3483    .conststrlit        nrfx_saadc.o
    0x0002cb16   0x0002cb16   0x00000002   PAD
    0x0002cb18   0x0002cb18   0x00000006   Data   RO         3622    .conststrlit        nrfx_timer.o
    0x0002cb1e   0x0002cb1e   0x00000002   PAD
    0x0002cb20   0x0002cb20   0x00000005   Data   RO         3770    .conststrlit        nrfx_spim.o
    0x0002cb25   0x0002cb25   0x00000003   PAD
    0x0002cb28   0x0002cb28   0x0000000b   Data   RO         3929    .conststrlit        app_button.o
    0x0002cb33   0x0002cb33   0x00000001   PAD
    0x0002cb34   0x0002cb34   0x0000000a   Data   RO         4275    .conststrlit        app_timer2.o
    0x0002cb3e   0x0002cb3e   0x00000002   PAD
    0x0002cb40   0x0002cb40   0x00000009   Data   RO         5229    .conststrlit        nrf_pwr_mgmt.o
    0x0002cb49   0x0002cb49   0x00000003   PAD
    0x0002cb4c   0x0002cb4c   0x00000009   Data   RO         5400    .conststrlit        nrf_sortlist.o
    0x0002cb55   0x0002cb55   0x00000003   PAD
    0x0002cb58   0x0002cb58   0x00000010   Data   RO         5623    .conststrlit        nrf_log_default_backends.o
    0x0002cb68   0x0002cb68   0x00000004   Data   RO         5692    .conststrlit        nrf_log_frontend.o
    0x0002cb6c   0x0002cb6c   0x00000008   Data   RO         6176    .conststrlit        nrf_sdh.o
    0x0002cb74   0x0002cb74   0x0000000c   Data   RO         6286    .conststrlit        nrf_sdh_ble.o
    0x0002cb80   0x0002cb80   0x0000000c   Data   RO         6336    .conststrlit        nrf_sdh_soc.o
    0x0002cb8c   0x0002cb8c   0x00000020   Data   RO         6767    Region$$Table       anon$$obj.o
    0x0002cbac   0x0002cbac   0x00000010   Data   RO         5625    log_backends        nrf_log_default_backends.o
    0x0002cbbc   0x0002cbbc   0x00000008   Data   RO         1871    log_const_data      nrf_ble_gatt.o
    0x0002cbc4   0x0002cbc4   0x00000008   Data   RO         2010    log_const_data      ble_nus.o
    0x0002cbcc   0x0002cbcc   0x00000008   Data   RO         2088    log_const_data      nrf_drv_clock.o
    0x0002cbd4   0x0002cbd4   0x00000008   Data   RO         2434    log_const_data      nrfx_clock.o
    0x0002cbdc   0x0002cbdc   0x00000008   Data   RO         2606    log_const_data      nrfx_gpiote.o
    0x0002cbe4   0x0002cbe4   0x00000008   Data   RO         2866    log_const_data      nrfx_prs.o
    0x0002cbec   0x0002cbec   0x00000008   Data   RO         2945    log_const_data      nrfx_uart.o
    0x0002cbf4   0x0002cbf4   0x00000008   Data   RO         3129    log_const_data      nrfx_uarte.o
    0x0002cbfc   0x0002cbfc   0x00000008   Data   RO         3294    log_const_data      nrfx_ppi.o
    0x0002cc04   0x0002cc04   0x00000008   Data   RO         3484    log_const_data      nrfx_saadc.o
    0x0002cc0c   0x0002cc0c   0x00000008   Data   RO         3623    log_const_data      nrfx_timer.o
    0x0002cc14   0x0002cc14   0x00000008   Data   RO         3771    log_const_data      nrfx_spim.o
    0x0002cc1c   0x0002cc1c   0x00000008   Data   RO         3931    log_const_data      app_button.o
    0x0002cc24   0x0002cc24   0x00000008   Data   RO         4278    log_const_data      app_timer2.o
    0x0002cc2c   0x0002cc2c   0x00000008   Data   RO         5231    log_const_data      nrf_pwr_mgmt.o
    0x0002cc34   0x0002cc34   0x00000008   Data   RO         5401    log_const_data      nrf_sortlist.o
    0x0002cc3c   0x0002cc3c   0x00000008   Data   RO         5696    log_const_data      nrf_log_frontend.o
    0x0002cc44   0x0002cc44   0x00000008   Data   RO         6178    log_const_data      nrf_sdh.o
    0x0002cc4c   0x0002cc4c   0x00000008   Data   RO         6288    log_const_data      nrf_sdh_ble.o
    0x0002cc54   0x0002cc54   0x00000008   Data   RO         6337    log_const_data      nrf_sdh_soc.o
    0x0002cc5c   0x0002cc5c   0x00000014   Data   RO         5697    nrf_balloc          nrf_log_frontend.o
    0x0002cc70   0x0002cc70   0x00000008   Data   RO         1621    sdh_ble_observers0  ble_conn_state.o
    0x0002cc78   0x0002cc78   0x00000010   Data   RO           37    sdh_ble_observers1  main.o
    0x0002cc88   0x0002cc88   0x00000008   Data   RO         1136    sdh_ble_observers1  bsp_btn_ble.o
    0x0002cc90   0x0002cc90   0x00000008   Data   RO         1526    sdh_ble_observers1  ble_conn_params.o
    0x0002cc98   0x0002cc98   0x00000010   Data   RO           38    sdh_ble_observers2  main.o
    0x0002cca8   0x0002cca8   0x00000008   Data   RO           39    sdh_ble_observers3  main.o
    0x0002ccb0   0x0002ccb0   0x00000008   Data   RO         2089    sdh_soc_observers0  nrf_drv_clock.o
    0x0002ccb8   0x0002ccb8   0x00000008   Data   RO         6289    sdh_stack_observers0  nrf_sdh_ble.o
    0x0002ccc0   0x0002ccc0   0x00000008   Data   RO         6338    sdh_stack_observers0  nrf_sdh_soc.o
    0x0002ccc8   0x0002ccc8   0x00000008   Data   RO         2090    sdh_state_observers0  nrf_drv_clock.o


    Execution Region RW_IRAM1 (Exec base: 0x20002ad8, Load base: 0x0002ccd0, Size: 0x00003530, Max: 0x0000d528, ABSOLUTE, COMPRESSED[0x0000005c])

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20002ad8   COMPRESSED   0x00000008   Data   RW           31    .data               main.o
    0x20002ae0   COMPRESSED   0x00000044   Data   RW           32    .data               main.o
    0x20002b24   COMPRESSED   0x00000004   Data   RW           35    .data               main.o
    0x20002b28   COMPRESSED   0x00000001   Data   RW          686    .data               ads1292.o
    0x20002b29   COMPRESSED   0x00000003   PAD
    0x20002b2c   COMPRESSED   0x00000010   Data   RW         1015    .data               bsp.o
    0x20002b3c   COMPRESSED   0x00000008   Data   RW         1135    .data               bsp_btn_ble.o
    0x20002b44   COMPRESSED   0x00000008   Data   RW         1525    .data               ble_conn_params.o
    0x20002b4c   COMPRESSED   0x0000000c   Data   RW         2227    .data               nrf_drv_uart.o
    0x20002b58   COMPRESSED   0x00000008   Data   RW         2433    .data               nrfx_clock.o
    0x20002b60   COMPRESSED   0x00000008   Data   RW         2865    .data               nrfx_prs.o
    0x20002b68   COMPRESSED   0x00000020   Data   RW         4276    .data               app_timer2.o
    0x20002b88   COMPRESSED   0x00000004   Data   RW         4277    .data               app_timer2.o
    0x20002b8c   COMPRESSED   0x0000001c   Data   RW         4406    .data               app_uart_fifo.o
    0x20002ba8   COMPRESSED   0x0000000c   Data   RW         4568    .data               drv_rtc.o
    0x20002bb4   COMPRESSED   0x00000008   Data   RW         5230    .data               nrf_pwr_mgmt.o
    0x20002bbc   COMPRESSED   0x00000001   Data   RW         5530    .data               nrf_log_backend_rtt.o
    0x20002bbd   COMPRESSED   0x00000003   PAD
    0x20002bc0   COMPRESSED   0x00000008   Data   RW         5624    .data               nrf_log_default_backends.o
    0x20002bc8   COMPRESSED   0x00000004   Data   RW         5693    .data               nrf_log_frontend.o
    0x20002bcc   COMPRESSED   0x00000008   Data   RW         5694    .data               nrf_log_frontend.o
    0x20002bd4   COMPRESSED   0x00000008   Data   RW         5695    .data               nrf_log_frontend.o
    0x20002bdc   COMPRESSED   0x00000040   Data   RW         5854    .data               nrf_log_str_formatter.o
    0x20002c1c   COMPRESSED   0x00000003   Data   RW         6177    .data               nrf_sdh.o
    0x20002c1f   COMPRESSED   0x00000001   Data   RW         6287    .data               nrf_sdh_ble.o
    0x20002c20   COMPRESSED   0x00000004   Data   RW         6377    .data               system_nrf52.o
    0x20002c24        -       0x0000086a   Zero   RW           27    .bss                main.o
    0x2000348e        -       0x00000020   Zero   RW          684    .bss                ads1292.o
    0x200034ae   COMPRESSED   0x00000002   PAD
    0x200034b0        -       0x00000020   Zero   RW         1010    .bss                bsp.o
    0x200034d0        -       0x00000020   Zero   RW         1011    .bss                bsp.o
    0x200034f0        -       0x0000000c   Zero   RW         1012    .bss                bsp.o
    0x200034fc   COMPRESSED   0x00000004   PAD
    0x20003500        -       0x00000020   Zero   RW         1013    .bss                bsp.o
    0x20003520        -       0x00000050   Zero   RW         1524    .bss                ble_conn_params.o
    0x20003570        -       0x0000007c   Zero   RW         1620    .bss                ble_conn_state.o
    0x200035ec        -       0x00000014   Zero   RW         2082    .bss                nrf_drv_clock.o
    0x20003600        -       0x0000005c   Zero   RW         2600    .bss                nrfx_gpiote.o
    0x2000365c        -       0x0000002c   Zero   RW         2939    .bss                nrfx_uart.o
    0x20003688        -       0x00000024   Zero   RW         3123    .bss                nrfx_uarte.o
    0x200036ac        -       0x00000030   Zero   RW         3474    .bss                nrfx_saadc.o
    0x200036dc        -       0x0000000c   Zero   RW         3620    .bss                nrfx_timer.o
    0x200036e8        -       0x00000024   Zero   RW         3765    .bss                nrfx_spim.o
    0x2000370c        -       0x00000018   Zero   RW         3870    .bss                nrf_drv_spi.o
    0x20003724        -       0x00000010   Zero   RW         4272    .bss                app_timer2.o
    0x20003734        -       0x00000058   Zero   RW         4273    .bss                app_timer2.o
    0x2000378c        -       0x00000020   Zero   RW         4404    .bss                app_uart_fifo.o
    0x200037ac        -       0x0000000c   Zero   RW         4482    .bss                app_util_platform.o
    0x200037b8        -       0x0000000c   Zero   RW         5226    .bss                nrf_pwr_mgmt.o
    0x200037c4        -       0x00000040   Zero   RW         5527    .bss                nrf_log_backend_rtt.o
    0x20003804        -       0x000000c0   Zero   RW         5687    .bss                nrf_log_frontend.o
    0x200038c4        -       0x00000080   Zero   RW         5688    .bss                nrf_log_frontend.o
    0x20003944        -       0x00000018   Zero   RW         5689    .bss                nrf_log_frontend.o
    0x2000395c        -       0x00000420   Zero   RW         5690    .bss                nrf_log_frontend.o
    0x20003d7c        -       0x00000288   Zero   RW         5930    .bss                segger_rtt.o
    0x20004004   COMPRESSED   0x00000004   PAD
    0x20004008        -       0x00002000   Zero   RW         6365    STACK               arm_startup_nrf52.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       884        114         40          1         32      26641   ads1292.o
         0          0         19          0          0       1815   app_button.o
        22          0          0          0          0       2497   app_error.o
       132         46          0          0          0      42490   app_error_weak.o
        86          0          0          0          0       4138   app_fifo.o
       846         88         38         36        104      16449   app_timer2.o
       116         20          0         28         32      13867   app_uart_fifo.o
       124         14          0          0         12      10630   app_util_platform.o
        36          8        512          0       8192        916   arm_startup_nrf52.o
      1338          0          0          0          0      60388   ble_advdata.o
      1018          6          0          0          0      16991   ble_advertising.o
       636         44          8          8         80      12603   ble_conn_params.o
       390         10          8          0        124       7615   ble_conn_state.o
        72          0          0          0          0       2784   ble_link_ctx_manager.o
       672          6         32          0          0       8828   ble_nus.o
       456          6          0          0          0       3331   ble_srv_common.o
       206         32          8          0          0      11085   boards.o
       804         60         44         16        108      19544   bsp.o
       196          6          8          8          0      54029   bsp_btn_ble.o
         0          0          0          0          0      46134   dht11.o
       704         76          6         12          0      47821   drv_rtc.o
        96          0          0          0          0       8269   led.o
      2082        262         80         80       2154     190694   main.o
       326          0          0          0          0       5875   nrf_atfifo.o
        64          0          0          0          0       2992   nrf_atflags.o
       306          0          0          0          0       7915   nrf_atomic.o
       164          0          0          0          0       3592   nrf_balloc.o
       950        272        205          0          0      37885   nrf_ble_gatt.o
       276          0          0          0          0       6149   nrf_ble_qwr.o
       312         36         30          0         20      47376   nrf_drv_clock.o
       168         18         16          0         24      15515   nrf_drv_spi.o
         0          0          0         12          0      15296   nrf_drv_uart.o
        50          0          0          0          0       3493   nrf_fprintf.o
       866          4         16          0          0       6502   nrf_fprintf_format.o
       112         18         18          1         64       5541   nrf_log_backend_rtt.o
       194          0          0          0          0      10881   nrf_log_backend_serial.o
        28          4         32          8          0       3313   nrf_log_default_backends.o
      1474        128         44         20       1400      29626   nrf_log_frontend.o
       570        108        102         64          0       5596   nrf_log_str_formatter.o
       346          0          0          0          0       9479   nrf_memobj.o
       108         16         53          8         12      42254   nrf_pwr_mgmt.o
        28          0          0          0          0       2339   nrf_ringbuf.o
       384         46        104          3          0      42659   nrf_sdh.o
       724        232        539          1          0       5865   nrf_sdh_ble.o
        60          4         56          0          0       2440   nrf_sdh_soc.o
        78          0          0          0          0       2740   nrf_section_iter.o
        84          0         17          0          0       4049   nrf_sortlist.o
        76         10       1319          0          0       2703   nrf_strerror.o
       242         32         14          8          0      46960   nrfx_clock.o
      1228        100         19          0         92      64091   nrfx_gpiote.o
         0          0         12          0          0        408   nrfx_ppi.o
        94         16         12          8          0       3717   nrfx_prs.o
       380         22         14          0         48      13430   nrfx_saadc.o
       846         22         17          0         36      21226   nrfx_spim.o
       384         14         14          0         12      13636   nrfx_timer.o
       254         12         13          0         44      14615   nrfx_uart.o
       238         12         14          0         36      14398   nrfx_uarte.o
       378         34          0          0        648       8909   segger_rtt.o
       920         72          0          4          0      13407   system_nrf52.o

    ----------------------------------------------------------------------
     23694       <USER>       <GROUP>        332      13284    1146431   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        66          0         61          6         10          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

        86          0          0          0          0          0   __dczerorl2.o
         8          4        133          0          0         68   ctype_o.o
         0          0          0          0          0          0   entry.o
         0          0          0          0          0          0   entry10a.o
         0          0          0          0          0          0   entry11a.o
         4          0          0          0          0          0   entry12b.o
         8          4          0          0          0          0   entry2.o
         4          0          0          0          0          0   entry5.o
         0          0          0          0          0          0   entry7b.o
         0          0          0          0          0          0   entry8b.o
         8          4          0          0          0          0   entry9a.o
        30          0          0          0          0          0   handlers.o
        36          8          0          0          0         68   init.o
        30          0          0          0          0         68   llshl.o
        32          0          0          0          0         68   llushr.o
        36          0          0          0          0         68   memcpya.o
        36          0          0          0          0        108   memseta.o
        18          0          0          0          0         68   strcpy.o
        14          0          0          0          0         68   strlen.o
        98          0          0          0          0         92   uldiv.o

    ----------------------------------------------------------------------
       450         <USER>        <GROUP>          0          0        676   Library Totals
         2          0          3          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

       448         20        133          0          0        676   mc_w.l

    ----------------------------------------------------------------------
       450         <USER>        <GROUP>          0          0        676   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     24144       2050       3712        332      13284    1117283   Grand Totals
     24144       2050       3712         92      13284    1117283   ELF Image Totals (compressed)
     24144       2050       3712         92          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                27856 (  27.20kB)
    Total RW  Size (RW Data + ZI Data)             13616 (  13.30kB)
    Total ROM Size (Code + RO Data + RW Data)      27948 (  27.29kB)

==============================================================================

