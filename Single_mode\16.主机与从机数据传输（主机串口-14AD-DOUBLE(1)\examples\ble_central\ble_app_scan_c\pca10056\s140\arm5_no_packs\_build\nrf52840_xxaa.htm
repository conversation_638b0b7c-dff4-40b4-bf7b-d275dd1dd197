<!doctype html public "-//w3c//dtd html 4.0 transitional//en">
<html><head>
<title>Static Call Graph - [.\_build\nrf52840_xxaa.axf]</title></head>
<body><HR>
<H1>Static Call Graph for image .\_build\nrf52840_xxaa.axf</H1><HR>
<BR><P>#&#060CALLGRAPH&#062# ARM Linker, 5060061: Last Updated: Tue Sep 08 19:54:49 2020
<BR><P>
<H3>Maximum Stack Usage =        748 bytes + Unknown(Cycles, Untraceable Function Pointers)</H3><H3>
Call chain for Maximum Stack Depth:</H3>
nrf_sdh_ble_evts_poll &rArr; app_error_handler_bare &rArr; app_error_fault_handler &rArr; nrf_log_frontend_std_0 &rArr; std_n &rArr; nrf_log_frontend_dequeue &rArr;  nrf_log_frontend_std_0 (Cycle)
<P>
<H3>
Mutually Recursive functions
</H3> <LI><a href="#[1]">NMI_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[1]">NMI_Handler</a><BR>
 <LI><a href="#[2]">HardFault_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[2]">HardFault_Handler</a><BR>
 <LI><a href="#[3]">MemoryManagement_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[3]">MemoryManagement_Handler</a><BR>
 <LI><a href="#[4]">BusFault_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[4]">BusFault_Handler</a><BR>
 <LI><a href="#[5]">UsageFault_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[5]">UsageFault_Handler</a><BR>
 <LI><a href="#[6]">SVC_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[6]">SVC_Handler</a><BR>
 <LI><a href="#[7]">DebugMon_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[7]">DebugMon_Handler</a><BR>
 <LI><a href="#[8]">PendSV_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[8]">PendSV_Handler</a><BR>
 <LI><a href="#[9]">SysTick_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[9]">SysTick_Handler</a><BR>
 <LI><a href="#[19]">CCM_AAR_IRQHandler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[19]">CCM_AAR_IRQHandler</a><BR>
 <LI><a href="#[77]">nrf_log_frontend_dequeue</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[78]">nrf_log_frontend_std_0</a><BR>
 <LI><a href="#[54]">__asm___12_nrf_atfifo_c_51f461e1__nrf_atfifo_wspace_close</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[54]">__asm___12_nrf_atfifo_c_51f461e1__nrf_atfifo_wspace_close</a><BR>
 <LI><a href="#[55]">__asm___12_nrf_atfifo_c_51f461e1__nrf_atfifo_rspace_close</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[55]">__asm___12_nrf_atfifo_c_51f461e1__nrf_atfifo_rspace_close</a><BR>
 <LI><a href="#[a3]">buffer_add</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[a3]">buffer_add</a><BR>
</UL>
<P>
<H3>
Function Pointers
</H3><UL>
 <LI><a href="#[4]">BusFault_Handler</a> from arm_startup_nrf52840.o(.text) referenced from arm_startup_nrf52840.o(RESET)
 <LI><a href="#[19]">CCM_AAR_IRQHandler</a> from arm_startup_nrf52840.o(.text) referenced from arm_startup_nrf52840.o(RESET)
 <LI><a href="#[1d]">COMP_LPCOMP_IRQHandler</a> from arm_startup_nrf52840.o(.text) referenced from arm_startup_nrf52840.o(RESET)
 <LI><a href="#[32]">CRYPTOCELL_IRQHandler</a> from arm_startup_nrf52840.o(.text) referenced from arm_startup_nrf52840.o(RESET)
 <LI><a href="#[7]">DebugMon_Handler</a> from arm_startup_nrf52840.o(.text) referenced from arm_startup_nrf52840.o(RESET)
 <LI><a href="#[18]">ECB_IRQHandler</a> from arm_startup_nrf52840.o(.text) referenced from arm_startup_nrf52840.o(RESET)
 <LI><a href="#[2e]">FPU_IRQHandler</a> from arm_startup_nrf52840.o(.text) referenced from arm_startup_nrf52840.o(RESET)
 <LI><a href="#[10]">GPIOTE_IRQHandler</a> from nrfx_gpiote.o(i.GPIOTE_IRQHandler) referenced from arm_startup_nrf52840.o(RESET)
 <LI><a href="#[2]">HardFault_Handler</a> from arm_startup_nrf52840.o(.text) referenced from arm_startup_nrf52840.o(RESET)
 <LI><a href="#[2d]">I2S_IRQHandler</a> from arm_startup_nrf52840.o(.text) referenced from arm_startup_nrf52840.o(RESET)
 <LI><a href="#[28]">MWU_IRQHandler</a> from arm_startup_nrf52840.o(.text) referenced from arm_startup_nrf52840.o(RESET)
 <LI><a href="#[3]">MemoryManagement_Handler</a> from arm_startup_nrf52840.o(.text) referenced from arm_startup_nrf52840.o(RESET)
 <LI><a href="#[f]">NFCT_IRQHandler</a> from arm_startup_nrf52840.o(.text) referenced from arm_startup_nrf52840.o(RESET)
 <LI><a href="#[1]">NMI_Handler</a> from arm_startup_nrf52840.o(.text) referenced from arm_startup_nrf52840.o(RESET)
 <LI><a href="#[27]">PDM_IRQHandler</a> from arm_startup_nrf52840.o(.text) referenced from arm_startup_nrf52840.o(RESET)
 <LI><a href="#[a]">POWER_CLOCK_IRQHandler</a> from nrfx_clock.o(i.POWER_CLOCK_IRQHandler) referenced from arm_startup_nrf52840.o(RESET)
 <LI><a href="#[26]">PWM0_IRQHandler</a> from arm_startup_nrf52840.o(.text) referenced from arm_startup_nrf52840.o(RESET)
 <LI><a href="#[29]">PWM1_IRQHandler</a> from arm_startup_nrf52840.o(.text) referenced from arm_startup_nrf52840.o(RESET)
 <LI><a href="#[2a]">PWM2_IRQHandler</a> from arm_startup_nrf52840.o(.text) referenced from arm_startup_nrf52840.o(RESET)
 <LI><a href="#[33]">PWM3_IRQHandler</a> from arm_startup_nrf52840.o(.text) referenced from arm_startup_nrf52840.o(RESET)
 <LI><a href="#[8]">PendSV_Handler</a> from arm_startup_nrf52840.o(.text) referenced from arm_startup_nrf52840.o(RESET)
 <LI><a href="#[1c]">QDEC_IRQHandler</a> from arm_startup_nrf52840.o(.text) referenced from arm_startup_nrf52840.o(RESET)
 <LI><a href="#[31]">QSPI_IRQHandler</a> from arm_startup_nrf52840.o(.text) referenced from arm_startup_nrf52840.o(RESET)
 <LI><a href="#[b]">RADIO_IRQHandler</a> from arm_startup_nrf52840.o(.text) referenced from arm_startup_nrf52840.o(RESET)
 <LI><a href="#[17]">RNG_IRQHandler</a> from arm_startup_nrf52840.o(.text) referenced from arm_startup_nrf52840.o(RESET)
 <LI><a href="#[15]">RTC0_IRQHandler</a> from arm_startup_nrf52840.o(.text) referenced from arm_startup_nrf52840.o(RESET)
 <LI><a href="#[1b]">RTC1_IRQHandler</a> from drv_rtc.o(i.RTC1_IRQHandler) referenced from arm_startup_nrf52840.o(RESET)
 <LI><a href="#[2c]">RTC2_IRQHandler</a> from arm_startup_nrf52840.o(.text) referenced from arm_startup_nrf52840.o(RESET)
 <LI><a href="#[0]">Reset_Handler</a> from arm_startup_nrf52840.o(.text) referenced from arm_startup_nrf52840.o(RESET)
 <LI><a href="#[11]">SAADC_IRQHandler</a> from arm_startup_nrf52840.o(.text) referenced from arm_startup_nrf52840.o(RESET)
 <LI><a href="#[d]">SPIM0_SPIS0_TWIM0_TWIS0_SPI0_TWI0_IRQHandler</a> from arm_startup_nrf52840.o(.text) referenced from arm_startup_nrf52840.o(RESET)
 <LI><a href="#[e]">SPIM1_SPIS1_TWIM1_TWIS1_SPI1_TWI1_IRQHandler</a> from arm_startup_nrf52840.o(.text) referenced from arm_startup_nrf52840.o(RESET)
 <LI><a href="#[2b]">SPIM2_SPIS2_SPI2_IRQHandler</a> from arm_startup_nrf52840.o(.text) referenced from arm_startup_nrf52840.o(RESET)
 <LI><a href="#[34]">SPIM3_IRQHandler</a> from arm_startup_nrf52840.o(.text) referenced from arm_startup_nrf52840.o(RESET)
 <LI><a href="#[6]">SVC_Handler</a> from arm_startup_nrf52840.o(.text) referenced from arm_startup_nrf52840.o(RESET)
 <LI><a href="#[1e]">SWI0_EGU0_IRQHandler</a> from arm_startup_nrf52840.o(.text) referenced from arm_startup_nrf52840.o(RESET)
 <LI><a href="#[1f]">SWI1_EGU1_IRQHandler</a> from arm_startup_nrf52840.o(.text) referenced from arm_startup_nrf52840.o(RESET)
 <LI><a href="#[20]">SWI2_EGU2_IRQHandler</a> from nrf_sdh.o(i.SWI2_EGU2_IRQHandler) referenced from arm_startup_nrf52840.o(RESET)
 <LI><a href="#[21]">SWI3_EGU3_IRQHandler</a> from arm_startup_nrf52840.o(.text) referenced from arm_startup_nrf52840.o(RESET)
 <LI><a href="#[22]">SWI4_EGU4_IRQHandler</a> from arm_startup_nrf52840.o(.text) referenced from arm_startup_nrf52840.o(RESET)
 <LI><a href="#[23]">SWI5_EGU5_IRQHandler</a> from arm_startup_nrf52840.o(.text) referenced from arm_startup_nrf52840.o(RESET)
 <LI><a href="#[9]">SysTick_Handler</a> from arm_startup_nrf52840.o(.text) referenced from arm_startup_nrf52840.o(RESET)
 <LI><a href="#[36]">SystemInit</a> from system_nrf52840.o(i.SystemInit) referenced from arm_startup_nrf52840.o(.text)
 <LI><a href="#[16]">TEMP_IRQHandler</a> from arm_startup_nrf52840.o(.text) referenced from arm_startup_nrf52840.o(RESET)
 <LI><a href="#[12]">TIMER0_IRQHandler</a> from arm_startup_nrf52840.o(.text) referenced from arm_startup_nrf52840.o(RESET)
 <LI><a href="#[13]">TIMER1_IRQHandler</a> from arm_startup_nrf52840.o(.text) referenced from arm_startup_nrf52840.o(RESET)
 <LI><a href="#[14]">TIMER2_IRQHandler</a> from arm_startup_nrf52840.o(.text) referenced from arm_startup_nrf52840.o(RESET)
 <LI><a href="#[24]">TIMER3_IRQHandler</a> from arm_startup_nrf52840.o(.text) referenced from arm_startup_nrf52840.o(RESET)
 <LI><a href="#[25]">TIMER4_IRQHandler</a> from arm_startup_nrf52840.o(.text) referenced from arm_startup_nrf52840.o(RESET)
 <LI><a href="#[c]">UARTE0_UART0_IRQHandler</a> from nrfx_prs.o(i.UARTE0_UART0_IRQHandler) referenced from arm_startup_nrf52840.o(RESET)
 <LI><a href="#[30]">UARTE1_IRQHandler</a> from arm_startup_nrf52840.o(.text) referenced from arm_startup_nrf52840.o(RESET)
 <LI><a href="#[2f]">USBD_IRQHandler</a> from arm_startup_nrf52840.o(.text) referenced from arm_startup_nrf52840.o(RESET)
 <LI><a href="#[5]">UsageFault_Handler</a> from arm_startup_nrf52840.o(.text) referenced from arm_startup_nrf52840.o(RESET)
 <LI><a href="#[1a]">WDT_IRQHandler</a> from arm_startup_nrf52840.o(.text) referenced from arm_startup_nrf52840.o(RESET)
 <LI><a href="#[37]">__main</a> from entry.o(.ARM.Collect$$$$00000000) referenced from arm_startup_nrf52840.o(.text)
 <LI><a href="#[3d]">alert_timer_handler</a> from bsp.o(i.alert_timer_handler) referenced from bsp.o(i.bsp_init)
 <LI><a href="#[42]">app_error_fault_handler</a> from app_error_weak.o(i.app_error_fault_handler) referenced from nrf_sdh.o(i.nrf_sdh_enable_request)
 <LI><a href="#[4c]">ble_evt_handler</a> from bsp_btn_ble.o(i.ble_evt_handler) referenced from bsp_btn_ble.o(sdh_ble_observers1)
 <LI><a href="#[4d]">ble_evt_handler</a> from main.o(i.ble_evt_handler) referenced from main.o(sdh_ble_observers3)
 <LI><a href="#[44]">bsp_button_event_handler</a> from bsp.o(i.bsp_button_event_handler) referenced 4 times from bsp.o(.constdata)
 <LI><a href="#[3f]">bsp_event_handler</a> from main.o(i.bsp_event_handler) referenced from main.o(i.main)
 <LI><a href="#[3b]">button_timer_handler</a> from bsp.o(i.button_timer_handler) referenced from bsp.o(i.bsp_init)
 <LI><a href="#[40]">clock_irq_handler</a> from nrf_drv_clock.o(i.clock_irq_handler) referenced from nrf_drv_clock.o(i.nrf_drv_clock_init)
 <LI><a href="#[45]">compare_func</a> from app_timer2.o(i.compare_func) referenced from app_timer2.o(.constdata)
 <LI><a href="#[39]">detection_delay_timeout_handler</a> from app_button.o(i.detection_delay_timeout_handler) referenced from app_button.o(i.app_button_init)
 <LI><a href="#[3e]">gatt_evt_handler</a> from main.o(i.gatt_evt_handler) referenced from main.o(i.gatt_init)
 <LI><a href="#[38]">gpiote_event_handler</a> from app_button.o(i.gpiote_event_handler) referenced from app_button.o(i.app_button_init)
 <LI><a href="#[3c]">leds_timer_handler</a> from bsp.o(i.leds_timer_handler) referenced from bsp.o(i.bsp_init)
 <LI><a href="#[35]">main</a> from main.o(i.main) referenced from entry9a.o(.ARM.Collect$$$$0000000B)
 <LI><a href="#[4a]">nrf_ble_gatt_on_ble_evt</a> from nrf_ble_gatt.o(i.nrf_ble_gatt_on_ble_evt) referenced from main.o(sdh_ble_observers1)
 <LI><a href="#[4b]">nrf_ble_scan_on_ble_evt</a> from nrf_ble_scan.o(i.nrf_ble_scan_on_ble_evt) referenced from main.o(sdh_ble_observers1)
 <LI><a href="#[48]">nrf_log_backend_rtt_flush</a> from nrf_log_backend_rtt.o(i.nrf_log_backend_rtt_flush) referenced from nrf_log_backend_rtt.o(.constdata)
 <LI><a href="#[47]">nrf_log_backend_rtt_panic_set</a> from nrf_log_backend_rtt.o(i.nrf_log_backend_rtt_panic_set) referenced from nrf_log_backend_rtt.o(.constdata)
 <LI><a href="#[46]">nrf_log_backend_rtt_put</a> from nrf_log_backend_rtt.o(i.nrf_log_backend_rtt_put) referenced from nrf_log_backend_rtt.o(.constdata)
 <LI><a href="#[4f]">nrf_sdh_ble_evts_poll</a> from nrf_sdh_ble.o(i.nrf_sdh_ble_evts_poll) referenced from nrf_sdh_ble.o(sdh_stack_observers0)
 <LI><a href="#[50]">nrf_sdh_soc_evts_poll</a> from nrf_sdh_soc.o(i.nrf_sdh_soc_evts_poll) referenced from nrf_sdh_soc.o(sdh_stack_observers0)
 <LI><a href="#[3a]">rtc_irq</a> from app_timer2.o(i.rtc_irq) referenced from app_timer2.o(i.app_timer_init)
 <LI><a href="#[43]">scan_evt_handler</a> from main.o(i.scan_evt_handler) referenced from main.o(i.scan_init)
 <LI><a href="#[51]">sd_state_evt_handler</a> from nrf_drv_clock.o(i.sd_state_evt_handler) referenced from nrf_drv_clock.o(sdh_state_observers0)
 <LI><a href="#[41]">serial_tx</a> from nrf_log_backend_rtt.o(i.serial_tx) referenced from nrf_log_backend_rtt.o(i.nrf_log_backend_rtt_put)
 <LI><a href="#[49]">shutdown_handler</a> from main.o(i.shutdown_handler) referenced from main.o(pwr_mgmt_data1)
 <LI><a href="#[4e]">soc_evt_handler</a> from nrf_drv_clock.o(i.soc_evt_handler) referenced from nrf_drv_clock.o(sdh_soc_observers0)
</UL>
<P>
<H3>
Global Symbols
</H3>
<P><STRONG><a name="[37]"></a>__main</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry.o(.ARM.Collect$$$$00000000))
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52840.o(.text)
</UL>
<P><STRONG><a name="[12f]"></a>_main_stk</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry2.o(.ARM.Collect$$$$00000001))

<P><STRONG><a name="[52]"></a>_main_scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry5.o(.ARM.Collect$$$$00000004))
<BR><BR>[Calls]<UL><LI><a href="#[53]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
</UL>

<P><STRONG><a name="[5c]"></a>__main_after_scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry5.o(.ARM.Collect$$$$00000004))
<BR><BR>[Called By]<UL><LI><a href="#[53]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
</UL>

<P><STRONG><a name="[130]"></a>_main_clock</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry7b.o(.ARM.Collect$$$$00000008))

<P><STRONG><a name="[131]"></a>_main_cpp_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry8b.o(.ARM.Collect$$$$0000000A))

<P><STRONG><a name="[132]"></a>_main_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry9a.o(.ARM.Collect$$$$0000000B))

<P><STRONG><a name="[133]"></a>__rt_final_cpp</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry10a.o(.ARM.Collect$$$$0000000D))

<P><STRONG><a name="[134]"></a>__rt_final_exit</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry11a.o(.ARM.Collect$$$$0000000F))

<P><STRONG><a name="[cc]"></a>__asm___12_nrf_atfifo_c_51f461e1__nrf_atfifo_wspace_req</STRONG> (Thumb, 56 bytes, Stack size 0 bytes, nrf_atfifo.o(.emb_text))
<BR><BR>[Called By]<UL><LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_atfifo_item_alloc
</UL>

<P><STRONG><a name="[54]"></a>__asm___12_nrf_atfifo_c_51f461e1__nrf_atfifo_wspace_close</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, nrf_atfifo.o(.emb_text))
<BR><BR>[Calls]<UL><LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__asm___12_nrf_atfifo_c_51f461e1__nrf_atfifo_wspace_close
</UL>
<BR>[Called By]<UL><LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_atfifo_item_put
<LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__asm___12_nrf_atfifo_c_51f461e1__nrf_atfifo_wspace_close
</UL>

<P><STRONG><a name="[cf]"></a>__asm___12_nrf_atfifo_c_51f461e1__nrf_atfifo_rspace_req</STRONG> (Thumb, 58 bytes, Stack size 0 bytes, nrf_atfifo.o(.emb_text))
<BR><BR>[Called By]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_atfifo_item_get
</UL>

<P><STRONG><a name="[55]"></a>__asm___12_nrf_atfifo_c_51f461e1__nrf_atfifo_rspace_close</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, nrf_atfifo.o(.emb_text))
<BR><BR>[Calls]<UL><LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__asm___12_nrf_atfifo_c_51f461e1__nrf_atfifo_rspace_close
</UL>
<BR>[Called By]<UL><LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_atfifo_item_free
<LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__asm___12_nrf_atfifo_c_51f461e1__nrf_atfifo_rspace_close
</UL>

<P><STRONG><a name="[135]"></a>__asm___12_nrf_atfifo_c_51f461e1__nrf_atfifo_space_clear</STRONG> (Thumb, 50 bytes, Stack size 0 bytes, nrf_atfifo.o(.emb_text), UNUSED)

<P><STRONG><a name="[d5]"></a>__asm___12_nrf_atomic_c_85ca2469__nrf_atomic_internal_mov</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, nrf_atomic.o(.emb_text))
<BR><BR>[Called By]<UL><LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_atomic_u32_fetch_store
</UL>

<P><STRONG><a name="[d6]"></a>__asm___12_nrf_atomic_c_85ca2469__nrf_atomic_internal_orr</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, nrf_atomic.o(.emb_text))
<BR><BR>[Called By]<UL><LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_atomic_u32_or
</UL>

<P><STRONG><a name="[d4]"></a>__asm___12_nrf_atomic_c_85ca2469__nrf_atomic_internal_and</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, nrf_atomic.o(.emb_text))
<BR><BR>[Called By]<UL><LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_atomic_u32_fetch_and
</UL>

<P><STRONG><a name="[136]"></a>__asm___12_nrf_atomic_c_85ca2469__nrf_atomic_internal_eor</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, nrf_atomic.o(.emb_text), UNUSED)

<P><STRONG><a name="[d3]"></a>__asm___12_nrf_atomic_c_85ca2469__nrf_atomic_internal_add</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, nrf_atomic.o(.emb_text))
<BR><BR>[Called By]<UL><LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_atomic_u32_add
</UL>

<P><STRONG><a name="[d8]"></a>__asm___12_nrf_atomic_c_85ca2469__nrf_atomic_internal_sub</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, nrf_atomic.o(.emb_text))
<BR><BR>[Called By]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_atomic_u32_sub
</UL>

<P><STRONG><a name="[137]"></a>__asm___12_nrf_atomic_c_85ca2469__nrf_atomic_internal_cmp_exch</STRONG> (Thumb, 42 bytes, Stack size 0 bytes, nrf_atomic.o(.emb_text), UNUSED)

<P><STRONG><a name="[138]"></a>__asm___12_nrf_atomic_c_85ca2469__nrf_atomic_internal_sub_hs</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, nrf_atomic.o(.emb_text), UNUSED)

<P><STRONG><a name="[0]"></a>Reset_Handler</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, arm_startup_nrf52840.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52840.o(RESET)
</UL>
<P><STRONG><a name="[1]"></a>NMI_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, arm_startup_nrf52840.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NMI_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NMI_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52840.o(RESET)
</UL>
<P><STRONG><a name="[2]"></a>HardFault_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, arm_startup_nrf52840.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HardFault_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HardFault_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52840.o(RESET)
</UL>
<P><STRONG><a name="[3]"></a>MemoryManagement_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, arm_startup_nrf52840.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MemoryManagement_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MemoryManagement_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52840.o(RESET)
</UL>
<P><STRONG><a name="[4]"></a>BusFault_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, arm_startup_nrf52840.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BusFault_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BusFault_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52840.o(RESET)
</UL>
<P><STRONG><a name="[5]"></a>UsageFault_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, arm_startup_nrf52840.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsageFault_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsageFault_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52840.o(RESET)
</UL>
<P><STRONG><a name="[6]"></a>SVC_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, arm_startup_nrf52840.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SVC_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SVC_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52840.o(RESET)
</UL>
<P><STRONG><a name="[7]"></a>DebugMon_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, arm_startup_nrf52840.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DebugMon_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DebugMon_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52840.o(RESET)
</UL>
<P><STRONG><a name="[8]"></a>PendSV_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, arm_startup_nrf52840.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PendSV_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PendSV_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52840.o(RESET)
</UL>
<P><STRONG><a name="[9]"></a>SysTick_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, arm_startup_nrf52840.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52840.o(RESET)
</UL>
<P><STRONG><a name="[19]"></a>CCM_AAR_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, arm_startup_nrf52840.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[19]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CCM_AAR_IRQHandler
</UL>
<BR>[Called By]<UL><LI><a href="#[19]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CCM_AAR_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52840.o(RESET)
</UL>
<P><STRONG><a name="[1d]"></a>COMP_LPCOMP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, arm_startup_nrf52840.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52840.o(RESET)
</UL>
<P><STRONG><a name="[32]"></a>CRYPTOCELL_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, arm_startup_nrf52840.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52840.o(RESET)
</UL>
<P><STRONG><a name="[18]"></a>ECB_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, arm_startup_nrf52840.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52840.o(RESET)
</UL>
<P><STRONG><a name="[2e]"></a>FPU_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, arm_startup_nrf52840.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52840.o(RESET)
</UL>
<P><STRONG><a name="[2d]"></a>I2S_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, arm_startup_nrf52840.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52840.o(RESET)
</UL>
<P><STRONG><a name="[28]"></a>MWU_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, arm_startup_nrf52840.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52840.o(RESET)
</UL>
<P><STRONG><a name="[f]"></a>NFCT_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, arm_startup_nrf52840.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52840.o(RESET)
</UL>
<P><STRONG><a name="[27]"></a>PDM_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, arm_startup_nrf52840.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52840.o(RESET)
</UL>
<P><STRONG><a name="[26]"></a>PWM0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, arm_startup_nrf52840.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52840.o(RESET)
</UL>
<P><STRONG><a name="[29]"></a>PWM1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, arm_startup_nrf52840.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52840.o(RESET)
</UL>
<P><STRONG><a name="[2a]"></a>PWM2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, arm_startup_nrf52840.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52840.o(RESET)
</UL>
<P><STRONG><a name="[33]"></a>PWM3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, arm_startup_nrf52840.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52840.o(RESET)
</UL>
<P><STRONG><a name="[1c]"></a>QDEC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, arm_startup_nrf52840.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52840.o(RESET)
</UL>
<P><STRONG><a name="[31]"></a>QSPI_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, arm_startup_nrf52840.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52840.o(RESET)
</UL>
<P><STRONG><a name="[b]"></a>RADIO_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, arm_startup_nrf52840.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52840.o(RESET)
</UL>
<P><STRONG><a name="[17]"></a>RNG_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, arm_startup_nrf52840.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52840.o(RESET)
</UL>
<P><STRONG><a name="[15]"></a>RTC0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, arm_startup_nrf52840.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52840.o(RESET)
</UL>
<P><STRONG><a name="[2c]"></a>RTC2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, arm_startup_nrf52840.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52840.o(RESET)
</UL>
<P><STRONG><a name="[11]"></a>SAADC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, arm_startup_nrf52840.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52840.o(RESET)
</UL>
<P><STRONG><a name="[d]"></a>SPIM0_SPIS0_TWIM0_TWIS0_SPI0_TWI0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, arm_startup_nrf52840.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52840.o(RESET)
</UL>
<P><STRONG><a name="[e]"></a>SPIM1_SPIS1_TWIM1_TWIS1_SPI1_TWI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, arm_startup_nrf52840.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52840.o(RESET)
</UL>
<P><STRONG><a name="[2b]"></a>SPIM2_SPIS2_SPI2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, arm_startup_nrf52840.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52840.o(RESET)
</UL>
<P><STRONG><a name="[34]"></a>SPIM3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, arm_startup_nrf52840.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52840.o(RESET)
</UL>
<P><STRONG><a name="[1e]"></a>SWI0_EGU0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, arm_startup_nrf52840.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52840.o(RESET)
</UL>
<P><STRONG><a name="[1f]"></a>SWI1_EGU1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, arm_startup_nrf52840.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52840.o(RESET)
</UL>
<P><STRONG><a name="[21]"></a>SWI3_EGU3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, arm_startup_nrf52840.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52840.o(RESET)
</UL>
<P><STRONG><a name="[22]"></a>SWI4_EGU4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, arm_startup_nrf52840.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52840.o(RESET)
</UL>
<P><STRONG><a name="[23]"></a>SWI5_EGU5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, arm_startup_nrf52840.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52840.o(RESET)
</UL>
<P><STRONG><a name="[16]"></a>TEMP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, arm_startup_nrf52840.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52840.o(RESET)
</UL>
<P><STRONG><a name="[12]"></a>TIMER0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, arm_startup_nrf52840.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52840.o(RESET)
</UL>
<P><STRONG><a name="[13]"></a>TIMER1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, arm_startup_nrf52840.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52840.o(RESET)
</UL>
<P><STRONG><a name="[14]"></a>TIMER2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, arm_startup_nrf52840.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52840.o(RESET)
</UL>
<P><STRONG><a name="[24]"></a>TIMER3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, arm_startup_nrf52840.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52840.o(RESET)
</UL>
<P><STRONG><a name="[25]"></a>TIMER4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, arm_startup_nrf52840.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52840.o(RESET)
</UL>
<P><STRONG><a name="[30]"></a>UARTE1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, arm_startup_nrf52840.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52840.o(RESET)
</UL>
<P><STRONG><a name="[2f]"></a>USBD_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, arm_startup_nrf52840.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52840.o(RESET)
</UL>
<P><STRONG><a name="[1a]"></a>WDT_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, arm_startup_nrf52840.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52840.o(RESET)
</UL>
<P><STRONG><a name="[56]"></a>__aeabi_uldivmod</STRONG> (Thumb, 98 bytes, Stack size 40 bytes, uldiv.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = __aeabi_uldivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
<LI><a href="#[58]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
</UL>
<BR>[Called By]<UL><LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_led_indication
</UL>

<P><STRONG><a name="[58]"></a>__aeabi_llsl</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, llshl.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;evt_handle
<LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
</UL>

<P><STRONG><a name="[139]"></a>_ll_shift_l</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, llshl.o(.text), UNUSED)

<P><STRONG><a name="[105]"></a>__rt_ctype_table</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, ctype_o.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_hexdump_entry_process
</UL>

<P><STRONG><a name="[6b]"></a>__aeabi_memcpy</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, memcpya.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_ble_scan_init
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;memobj_op
<LI><a href="#[67]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_WriteNoCheck
<LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_WriteBlocking
</UL>

<P><STRONG><a name="[de]"></a>__aeabi_memcpy4</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memcpya.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[4a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_ble_gatt_on_ble_evt
</UL>

<P><STRONG><a name="[13a]"></a>__aeabi_memcpy8</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memcpya.o(.text), UNUSED)

<P><STRONG><a name="[5a]"></a>__aeabi_memset</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, memseta.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_memset$wrapper
<LI><a href="#[59]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
</UL>

<P><STRONG><a name="[13b]"></a>__aeabi_memset4</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)

<P><STRONG><a name="[13c]"></a>__aeabi_memset8</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)

<P><STRONG><a name="[59]"></a>__aeabi_memclr</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, memseta.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memset
</UL>
<BR>[Called By]<UL><LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_button_init
</UL>

<P><STRONG><a name="[e0]"></a>__aeabi_memclr4</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_ble_scan_start
<LI><a href="#[4b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_ble_scan_on_ble_evt
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_ble_scan_on_adv_report
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_ble_scan_connect_with_target
<LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_backend_serial_put
</UL>

<P><STRONG><a name="[13d]"></a>__aeabi_memclr8</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)

<P><STRONG><a name="[5b]"></a>_memset$wrapper</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, memseta.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memset
</UL>

<P><STRONG><a name="[f2]"></a>strlen</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, strlen.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_fprintf_fmt
</UL>

<P><STRONG><a name="[87]"></a>memcmp</STRONG> (Thumb, 26 bytes, Stack size 12 bytes, memcmp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = memcmp
</UL>
<BR>[Called By]<UL><LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_advdata_uuid_find
</UL>

<P><STRONG><a name="[6a]"></a>strcpy</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, strcpy.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_DoInit
</UL>

<P><STRONG><a name="[57]"></a>__aeabi_llsr</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, llushr.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
</UL>

<P><STRONG><a name="[13e]"></a>_ll_ushift_r</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, llushr.o(.text), UNUSED)

<P><STRONG><a name="[53]"></a>__scatterload</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, init.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__main_after_scatterload
</UL>
<BR>[Called By]<UL><LI><a href="#[52]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_main_scatterload
</UL>

<P><STRONG><a name="[13f]"></a>__scatterload_rt2</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, init.o(.text), UNUSED)

<P><STRONG><a name="[10]"></a>GPIOTE_IRQHandler</STRONG> (Thumb, 156 bytes, Stack size 32 bytes, nrfx_gpiote.o(i.GPIOTE_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 92<LI>Call Chain = GPIOTE_IRQHandler &rArr; port_event_handle &rArr; nrf_gpio_latches_read_and_clear
</UL>
<BR>[Calls]<UL><LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;port_event_handle
<LI><a href="#[5d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_gpiote_event_is_set
<LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_gpiote_event_clear
<LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_gpio_latches_read_and_clear
</UL>
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52840.o(RESET)
</UL>
<P><STRONG><a name="[a]"></a>POWER_CLOCK_IRQHandler</STRONG> (Thumb, 82 bytes, Stack size 16 bytes, nrfx_clock.o(i.POWER_CLOCK_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = POWER_CLOCK_IRQHandler &rArr; nrf_clock_event_clear
</UL>
<BR>[Calls]<UL><LI><a href="#[62]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_clock_event_clear
<LI><a href="#[61]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_clock_event_check
</UL>
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52840.o(RESET)
</UL>
<P><STRONG><a name="[1b]"></a>RTC1_IRQHandler</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, drv_rtc.o(i.RTC1_IRQHandler))
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52840.o(RESET)
</UL>
<P><STRONG><a name="[63]"></a>SEGGER_RTT_Init</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, segger_rtt.o(i.SEGGER_RTT_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = SEGGER_RTT_Init &rArr; _DoInit
</UL>
<BR>[Calls]<UL><LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_DoInit
</UL>
<BR>[Called By]<UL><LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_backend_rtt_init
</UL>

<P><STRONG><a name="[65]"></a>SEGGER_RTT_WriteNoLock</STRONG> (Thumb, 86 bytes, Stack size 16 bytes, segger_rtt.o(i.SEGGER_RTT_WriteNoLock))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = SEGGER_RTT_WriteNoLock &rArr; _WriteBlocking
</UL>
<BR>[Calls]<UL><LI><a href="#[67]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_WriteNoCheck
<LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_WriteBlocking
<LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_GetAvailWriteSpace
</UL>
<BR>[Called By]<UL><LI><a href="#[41]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;serial_tx
</UL>

<P><STRONG><a name="[20]"></a>SWI2_EGU2_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, nrf_sdh.o(i.SWI2_EGU2_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = SWI2_EGU2_IRQHandler &rArr; nrf_sdh_evts_poll &rArr; nrf_section_iter_next &rArr; nrf_section_iter_item_set
</UL>
<BR>[Calls]<UL><LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_sdh_evts_poll
</UL>
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52840.o(RESET)
</UL>
<P><STRONG><a name="[36]"></a>SystemInit</STRONG> (Thumb, 516 bytes, Stack size 20 bytes, system_nrf52840.o(i.SystemInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = SystemInit
</UL>
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52840.o(.text)
</UL>
<P><STRONG><a name="[c]"></a>UARTE0_UART0_IRQHandler</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, nrfx_prs.o(i.UARTE0_UART0_IRQHandler))
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52840.o(RESET)
</UL>
<P><STRONG><a name="[140]"></a>__scatterload_copy</STRONG> (Thumb, 14 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_copy), UNUSED)

<P><STRONG><a name="[141]"></a>__scatterload_null</STRONG> (Thumb, 2 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_null), UNUSED)

<P><STRONG><a name="[142]"></a>__scatterload_zeroinit</STRONG> (Thumb, 14 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_zeroinit), UNUSED)

<P><STRONG><a name="[6f]"></a>app_button_enable</STRONG> (Thumb, 32 bytes, Stack size 16 bytes, app_button.o(i.app_button_enable))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = app_button_enable &rArr; nrfx_gpiote_in_event_enable &rArr; nrf_gpio_cfg_sense_set
</UL>
<BR>[Calls]<UL><LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrfx_gpiote_in_event_enable
</UL>
<BR>[Called By]<UL><LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_init
</UL>

<P><STRONG><a name="[71]"></a>app_button_init</STRONG> (Thumb, 106 bytes, Stack size 24 bytes, app_button.o(i.app_button_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = app_button_init &rArr; nrfx_gpiote_in_init &rArr; nrf_gpio_cfg
</UL>
<BR>[Calls]<UL><LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_timer_create
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrfx_gpiote_is_init
<LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrfx_gpiote_init
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrfx_gpiote_in_init
<LI><a href="#[59]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
</UL>
<BR>[Called By]<UL><LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_init
</UL>

<P><STRONG><a name="[42]"></a>app_error_fault_handler</STRONG> (Thumb, 86 bytes, Stack size 0 bytes, app_error_weak.o(i.app_error_fault_handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 204<LI>Call Chain = app_error_fault_handler &rArr; nrf_log_frontend_std_0 &rArr; std_n &rArr; nrf_log_frontend_dequeue &rArr;  nrf_log_frontend_std_0 (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_frontend_std_0
<LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_frontend_dequeue
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_panic
</UL>
<BR>[Called By]<UL><LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_error_handler_bare
</UL>
<BR>[Address Reference Count : 1]<UL><LI> nrf_sdh.o(i.nrf_sdh_enable_request)
</UL>
<P><STRONG><a name="[79]"></a>app_error_handler_bare</STRONG> (Thumb, 22 bytes, Stack size 16 bytes, app_error.o(i.app_error_handler_bare))
<BR><BR>[Stack]<UL><LI>Max Depth = 220<LI>Call Chain = app_error_handler_bare &rArr; app_error_fault_handler &rArr; nrf_log_frontend_std_0 &rArr; std_n &rArr; nrf_log_frontend_dequeue &rArr;  nrf_log_frontend_std_0 (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[42]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_error_fault_handler
</UL>
<BR>[Called By]<UL><LI><a href="#[35]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gatt_init
<LI><a href="#[3f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_event_handler
<LI><a href="#[49]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;shutdown_handler
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;scan_start
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;scan_init
<LI><a href="#[43]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;scan_evt_handler
<LI><a href="#[4d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_evt_handler
<LI><a href="#[50]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_sdh_soc_evts_poll
<LI><a href="#[4f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_sdh_ble_evts_poll
<LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;softdevices_evt_irq_enable
</UL>

<P><STRONG><a name="[7a]"></a>app_timer_cnt_get</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, app_timer2.o(i.app_timer_cnt_get))
<BR><BR>[Calls]<UL><LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;drv_rtc_counter_get
</UL>
<BR>[Called By]<UL><LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_schedule
</UL>

<P><STRONG><a name="[75]"></a>app_timer_create</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, app_timer2.o(i.app_timer_create))
<BR><BR>[Called By]<UL><LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_button_init
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_init
</UL>

<P><STRONG><a name="[7c]"></a>app_timer_init</STRONG> (Thumb, 70 bytes, Stack size 16 bytes, app_timer2.o(i.app_timer_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 236<LI>Call Chain = app_timer_init &rArr; drv_rtc_init &rArr; nrf_log_frontend_std_0 &rArr; std_n &rArr; nrf_log_frontend_dequeue &rArr;  nrf_log_frontend_std_0 (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_atfifo_init
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;drv_rtc_overflow_enable
<LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;drv_rtc_init
<LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;drv_rtc_compare_set
</UL>
<BR>[Called By]<UL><LI><a href="#[35]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[81]"></a>app_timer_start</STRONG> (Thumb, 48 bytes, Stack size 16 bytes, app_timer2.o(i.app_timer_start))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = app_timer_start &rArr; timer_req_schedule &rArr; nrf_atfifo_item_alloc
</UL>
<BR>[Calls]<UL><LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_req_schedule
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_now
</UL>
<BR>[Called By]<UL><LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_led_indication
<LI><a href="#[44]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_button_event_handler
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_start
</UL>

<P><STRONG><a name="[84]"></a>app_timer_stop</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, app_timer2.o(i.app_timer_stop))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = app_timer_stop &rArr; timer_req_schedule &rArr; nrf_atfifo_item_alloc
</UL>
<BR>[Calls]<UL><LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_req_schedule
</UL>
<BR>[Called By]<UL><LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_led_indication
<LI><a href="#[44]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_button_event_handler
</UL>

<P><STRONG><a name="[9f]"></a>app_util_critical_region_enter</STRONG> (Thumb, 64 bytes, Stack size 12 bytes, app_util_platform.o(i.app_util_critical_region_enter))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = app_util_critical_region_enter
</UL>
<BR>[Called By]<UL><LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_sdh_enable_request
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_pwr_mgmt_run
<LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_frontend_dequeue
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_drv_clock_lfclk_release
<LI><a href="#[51]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_state_evt_handler
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;evt_handle
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_balloc_free
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_balloc_alloc
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;buf_prealloc
</UL>

<P><STRONG><a name="[a2]"></a>app_util_critical_region_exit</STRONG> (Thumb, 46 bytes, Stack size 0 bytes, app_util_platform.o(i.app_util_critical_region_exit))
<BR><BR>[Called By]<UL><LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_sdh_enable_request
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_pwr_mgmt_run
<LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_frontend_dequeue
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_drv_clock_lfclk_release
<LI><a href="#[51]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_state_evt_handler
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;evt_handle
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_balloc_free
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_balloc_alloc
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;buf_prealloc
</UL>

<P><STRONG><a name="[86]"></a>ble_advdata_search</STRONG> (Thumb, 70 bytes, Stack size 16 bytes, ble_advdata.o(i.ble_advdata_search))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = ble_advdata_search
</UL>
<BR>[Called By]<UL><LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_advdata_uuid_find
</UL>

<P><STRONG><a name="[85]"></a>ble_advdata_uuid_find</STRONG> (Thumb, 182 bytes, Stack size 64 bytes, ble_advdata.o(i.ble_advdata_uuid_find))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = ble_advdata_uuid_find &rArr; ble_advdata_search
</UL>
<BR>[Calls]<UL><LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_advdata_search
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;memcmp
</UL>
<BR>[Called By]<UL><LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_ble_scan_on_adv_report
</UL>

<P><STRONG><a name="[12e]"></a>bsp_board_button_idx_to_pin</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, boards.o(i.bsp_board_button_idx_to_pin))
<BR><BR>[Called By]<UL><LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wakeup_button_cfg
</UL>

<P><STRONG><a name="[8a]"></a>bsp_board_button_state_get</STRONG> (Thumb, 36 bytes, Stack size 8 bytes, boards.o(i.bsp_board_button_state_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = bsp_board_button_state_get
</UL>
<BR>[Calls]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_gpio_pin_port_decode
</UL>
<BR>[Called By]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_button_is_pressed
</UL>

<P><STRONG><a name="[8c]"></a>bsp_board_init</STRONG> (Thumb, 80 bytes, Stack size 32 bytes, boards.o(i.bsp_board_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = bsp_board_init &rArr; nrf_gpio_cfg
</UL>
<BR>[Calls]<UL><LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_board_leds_off
<LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_gpio_cfg
</UL>
<BR>[Called By]<UL><LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_init
</UL>

<P><STRONG><a name="[6e]"></a>bsp_board_led_invert</STRONG> (Thumb, 40 bytes, Stack size 8 bytes, boards.o(i.bsp_board_led_invert))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = bsp_board_led_invert
</UL>
<BR>[Calls]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_gpio_pin_port_decode
</UL>
<BR>[Called By]<UL><LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_led_indication
<LI><a href="#[3d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;alert_timer_handler
</UL>

<P><STRONG><a name="[8f]"></a>bsp_board_led_off</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, boards.o(i.bsp_board_led_off))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = bsp_board_led_off &rArr; nrf_gpio_pin_write
</UL>
<BR>[Calls]<UL><LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_gpio_pin_write
</UL>
<BR>[Called By]<UL><LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;leds_off
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_led_indication
<LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_board_leds_off
</UL>

<P><STRONG><a name="[91]"></a>bsp_board_led_on</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, boards.o(i.bsp_board_led_on))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = bsp_board_led_on &rArr; nrf_gpio_pin_write
</UL>
<BR>[Calls]<UL><LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_gpio_pin_write
</UL>
<BR>[Called By]<UL><LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_led_indication
<LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_board_leds_on
</UL>

<P><STRONG><a name="[92]"></a>bsp_board_led_state_get</STRONG> (Thumb, 36 bytes, Stack size 8 bytes, boards.o(i.bsp_board_led_state_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = bsp_board_led_state_get
</UL>
<BR>[Calls]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_gpio_pin_port_decode
</UL>
<BR>[Called By]<UL><LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_led_indication
</UL>

<P><STRONG><a name="[8e]"></a>bsp_board_leds_off</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, boards.o(i.bsp_board_leds_off))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = bsp_board_leds_off &rArr; bsp_board_led_off &rArr; nrf_gpio_pin_write
</UL>
<BR>[Calls]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_board_led_off
</UL>
<BR>[Called By]<UL><LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;leds_off
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_board_init
</UL>

<P><STRONG><a name="[93]"></a>bsp_board_leds_on</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, boards.o(i.bsp_board_leds_on))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = bsp_board_leds_on &rArr; bsp_board_led_on &rArr; nrf_gpio_pin_write
</UL>
<BR>[Calls]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_board_led_on
</UL>
<BR>[Called By]<UL><LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_led_indication
</UL>

<P><STRONG><a name="[98]"></a>bsp_board_pin_to_button_idx</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, boards.o(i.bsp_board_pin_to_button_idx))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = bsp_board_pin_to_button_idx
</UL>
<BR>[Called By]<UL><LI><a href="#[44]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_button_event_handler
</UL>

<P><STRONG><a name="[94]"></a>bsp_btn_ble_init</STRONG> (Thumb, 52 bytes, Stack size 16 bytes, bsp_btn_ble.o(i.bsp_btn_ble_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = bsp_btn_ble_init &rArr; advertising_buttons_configure &rArr; bsp_event_to_button_action_assign
</UL>
<BR>[Calls]<UL><LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;advertising_buttons_configure
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_button_is_pressed
</UL>
<BR>[Called By]<UL><LI><a href="#[35]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[96]"></a>bsp_btn_ble_sleep_mode_prepare</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, bsp_btn_ble.o(i.bsp_btn_ble_sleep_mode_prepare))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = bsp_btn_ble_sleep_mode_prepare &rArr; bsp_wakeup_button_enable &rArr; wakeup_button_cfg
</UL>
<BR>[Calls]<UL><LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_wakeup_button_enable
</UL>
<BR>[Called By]<UL><LI><a href="#[49]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;shutdown_handler
</UL>

<P><STRONG><a name="[95]"></a>bsp_button_is_pressed</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, bsp.o(i.bsp_button_is_pressed))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = bsp_button_is_pressed &rArr; bsp_board_button_state_get
</UL>
<BR>[Calls]<UL><LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_board_button_state_get
</UL>
<BR>[Called By]<UL><LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_btn_ble_init
</UL>

<P><STRONG><a name="[3f]"></a>bsp_event_handler</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, main.o(i.bsp_event_handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 220<LI>Call Chain = bsp_event_handler &rArr; app_error_handler_bare &rArr; app_error_fault_handler &rArr; nrf_log_frontend_std_0 &rArr; std_n &rArr; nrf_log_frontend_dequeue &rArr;  nrf_log_frontend_std_0 (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_pwr_mgmt_shutdown
<LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_error_handler_bare
</UL>
<BR>[Address Reference Count : 1]<UL><LI> main.o(i.main)
</UL>
<P><STRONG><a name="[6d]"></a>bsp_event_to_button_action_assign</STRONG> (Thumb, 64 bytes, Stack size 16 bytes, bsp.o(i.bsp_event_to_button_action_assign))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = bsp_event_to_button_action_assign
</UL>
<BR>[Called By]<UL><LI><a href="#[4c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_evt_handler
<LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;advertising_buttons_configure
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_init
</UL>

<P><STRONG><a name="[88]"></a>bsp_indication_set</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, bsp.o(i.bsp_indication_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = bsp_indication_set &rArr; bsp_led_indication &rArr; app_timer_start &rArr; timer_req_schedule &rArr; nrf_atfifo_item_alloc
</UL>
<BR>[Calls]<UL><LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_led_indication
</UL>
<BR>[Called By]<UL><LI><a href="#[49]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;shutdown_handler
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;scan_start
<LI><a href="#[4d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_evt_handler
</UL>

<P><STRONG><a name="[9b]"></a>bsp_init</STRONG> (Thumb, 128 bytes, Stack size 16 bytes, bsp.o(i.bsp_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = bsp_init &rArr; app_button_init &rArr; nrfx_gpiote_in_init &rArr; nrf_gpio_cfg
</UL>
<BR>[Calls]<UL><LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_timer_create
<LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_button_init
<LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_button_enable
<LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_event_to_button_action_assign
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_board_init
</UL>
<BR>[Called By]<UL><LI><a href="#[35]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[97]"></a>bsp_wakeup_button_enable</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, bsp.o(i.bsp_wakeup_button_enable))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = bsp_wakeup_button_enable &rArr; wakeup_button_cfg
</UL>
<BR>[Calls]<UL><LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wakeup_button_cfg
</UL>
<BR>[Called By]<UL><LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_btn_ble_sleep_mode_prepare
</UL>

<P><STRONG><a name="[ff]"></a>buffer_is_empty</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, nrf_log_frontend.o(i.buffer_is_empty))
<BR><BR>[Called By]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_frontend_dequeue
</UL>

<P><STRONG><a name="[123]"></a>drv_rtc_compare_disable</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, drv_rtc.o(i.drv_rtc_compare_disable))
<BR><BR>[Called By]<UL><LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_schedule
</UL>

<P><STRONG><a name="[ae]"></a>drv_rtc_compare_pending</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, drv_rtc.o(i.drv_rtc_compare_pending))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = drv_rtc_compare_pending &rArr; evt_pending &rArr; nrf_rtc_event_clear
</UL>
<BR>[Calls]<UL><LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;evt_pending
</UL>
<BR>[Called By]<UL><LI><a href="#[3a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_irq
</UL>

<P><STRONG><a name="[80]"></a>drv_rtc_compare_set</STRONG> (Thumb, 80 bytes, Stack size 24 bytes, drv_rtc.o(i.drv_rtc_compare_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = drv_rtc_compare_set &rArr; nrf_rtc_event_clear
</UL>
<BR>[Calls]<UL><LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_rtc_event_clear
</UL>
<BR>[Called By]<UL><LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_timer_init
</UL>

<P><STRONG><a name="[7b]"></a>drv_rtc_counter_get</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, drv_rtc.o(i.drv_rtc_counter_get))
<BR><BR>[Called By]<UL><LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_timer_cnt_get
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_now
</UL>

<P><STRONG><a name="[7e]"></a>drv_rtc_init</STRONG> (Thumb, 148 bytes, Stack size 16 bytes, drv_rtc.o(i.drv_rtc_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 220<LI>Call Chain = drv_rtc_init &rArr; nrf_log_frontend_std_0 &rArr; std_n &rArr; nrf_log_frontend_dequeue &rArr;  nrf_log_frontend_std_0 (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_frontend_std_0
</UL>
<BR>[Called By]<UL><LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_timer_init
</UL>

<P><STRONG><a name="[12d]"></a>drv_rtc_irq_trigger</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, drv_rtc.o(i.drv_rtc_irq_trigger))
<BR><BR>[Called By]<UL><LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_req_schedule
</UL>

<P><STRONG><a name="[7f]"></a>drv_rtc_overflow_enable</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, drv_rtc.o(i.drv_rtc_overflow_enable))
<BR><BR>[Calls]<UL><LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;evt_enable
</UL>
<BR>[Called By]<UL><LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_timer_init
</UL>

<P><STRONG><a name="[b2]"></a>drv_rtc_overflow_pending</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, drv_rtc.o(i.drv_rtc_overflow_pending))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = drv_rtc_overflow_pending &rArr; evt_pending &rArr; nrf_rtc_event_clear
</UL>
<BR>[Calls]<UL><LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;evt_pending
</UL>
<BR>[Called By]<UL><LI><a href="#[3a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_irq
</UL>

<P><STRONG><a name="[128]"></a>drv_rtc_start</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, drv_rtc.o(i.drv_rtc_start))
<BR><BR>[Called By]<UL><LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_update
</UL>

<P><STRONG><a name="[127]"></a>drv_rtc_stop</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, drv_rtc.o(i.drv_rtc_stop))
<BR><BR>[Called By]<UL><LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_update
</UL>

<P><STRONG><a name="[b3]"></a>drv_rtc_windowed_compare_set</STRONG> (Thumb, 222 bytes, Stack size 40 bytes, drv_rtc.o(i.drv_rtc_windowed_compare_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = drv_rtc_windowed_compare_set &rArr; nrf_rtc_event_clear
</UL>
<BR>[Calls]<UL><LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrfx_coredep_delay_us
<LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_rtc_event_clear
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;evt_enable
</UL>
<BR>[Called By]<UL><LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_schedule
</UL>

<P><STRONG><a name="[aa]"></a>evt_handle</STRONG> (Thumb, 216 bytes, Stack size 40 bytes, app_button.o(i.evt_handle))
<BR><BR>[Stack]<UL><LI>Max Depth = 68<LI>Call Chain = evt_handle &rArr; usr_event &rArr; button_get
</UL>
<BR>[Calls]<UL><LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_util_critical_region_exit
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_util_critical_region_enter
<LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usr_event
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;state_set
<LI><a href="#[58]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
</UL>
<BR>[Called By]<UL><LI><a href="#[39]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;detection_delay_timeout_handler
</UL>

<P><STRONG><a name="[3e]"></a>gatt_evt_handler</STRONG> (Thumb, 62 bytes, Stack size 16 bytes, main.o(i.gatt_evt_handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 236<LI>Call Chain = gatt_evt_handler &rArr; nrf_log_frontend_std_2 &rArr; std_n &rArr; nrf_log_frontend_dequeue &rArr;  nrf_log_frontend_std_0 (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_frontend_std_2
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_frontend_std_0
</UL>
<BR>[Address Reference Count : 1]<UL><LI> main.o(i.gatt_init)
</UL>
<P><STRONG><a name="[b7]"></a>gatt_init</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, main.o(i.gatt_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 228<LI>Call Chain = gatt_init &rArr; app_error_handler_bare &rArr; app_error_fault_handler &rArr; nrf_log_frontend_std_0 &rArr; std_n &rArr; nrf_log_frontend_dequeue &rArr;  nrf_log_frontend_std_0 (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_ble_gatt_init
<LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_error_handler_bare
</UL>
<BR>[Called By]<UL><LI><a href="#[35]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[e7]"></a>is_whitelist_used</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, nrf_ble_scan.o(i.is_whitelist_used))
<BR><BR>[Called By]<UL><LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_ble_scan_start
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_ble_scan_on_adv_report
</UL>

<P><STRONG><a name="[35]"></a>main</STRONG> (Thumb, 158 bytes, Stack size 8 bytes, main.o(i.main))
<BR><BR>[Stack]<UL><LI>Max Depth = 260<LI>Call Chain = main &rArr; nrf_sdh_enable_request &rArr; softdevices_evt_irq_enable &rArr; app_error_handler_bare &rArr; app_error_fault_handler &rArr; nrf_log_frontend_std_0 &rArr; std_n &rArr; nrf_log_frontend_dequeue &rArr;  nrf_log_frontend_std_0 (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_sdh_enable_request
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_sdh_ble_enable
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_sdh_ble_default_cfg_set
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_pwr_mgmt_run
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_pwr_mgmt_init
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_init
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_frontend_std_0
<LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_frontend_dequeue
<LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_default_backends_init
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_init
<LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_btn_ble_init
<LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_timer_init
<LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_error_handler_bare
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gatt_init
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;scan_start
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;scan_init
</UL>
<BR>[Address Reference Count : 1]<UL><LI> entry9a.o(.ARM.Collect$$$$0000000B)
</UL>
<P><STRONG><a name="[7d]"></a>nrf_atfifo_init</STRONG> (Thumb, 38 bytes, Stack size 8 bytes, nrf_atfifo.o(i.nrf_atfifo_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = nrf_atfifo_init
</UL>
<BR>[Called By]<UL><LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_timer_init
</UL>

<P><STRONG><a name="[cb]"></a>nrf_atfifo_item_alloc</STRONG> (Thumb, 22 bytes, Stack size 16 bytes, nrf_atfifo.o(i.nrf_atfifo_item_alloc))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = nrf_atfifo_item_alloc
</UL>
<BR>[Calls]<UL><LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__asm___12_nrf_atfifo_c_51f461e1__nrf_atfifo_wspace_req
</UL>
<BR>[Called By]<UL><LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_req_schedule
</UL>

<P><STRONG><a name="[cd]"></a>nrf_atfifo_item_free</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, nrf_atfifo.o(i.nrf_atfifo_item_free))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = nrf_atfifo_item_free
</UL>
<BR>[Calls]<UL><LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__asm___12_nrf_atfifo_c_51f461e1__nrf_atfifo_rspace_close
</UL>
<BR>[Called By]<UL><LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_req_process
</UL>

<P><STRONG><a name="[ce]"></a>nrf_atfifo_item_get</STRONG> (Thumb, 22 bytes, Stack size 16 bytes, nrf_atfifo.o(i.nrf_atfifo_item_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = nrf_atfifo_item_get
</UL>
<BR>[Calls]<UL><LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__asm___12_nrf_atfifo_c_51f461e1__nrf_atfifo_rspace_req
</UL>
<BR>[Called By]<UL><LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_req_process
</UL>

<P><STRONG><a name="[d0]"></a>nrf_atfifo_item_put</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, nrf_atfifo.o(i.nrf_atfifo_item_put))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = nrf_atfifo_item_put
</UL>
<BR>[Calls]<UL><LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__asm___12_nrf_atfifo_c_51f461e1__nrf_atfifo_wspace_close
</UL>
<BR>[Called By]<UL><LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_req_schedule
</UL>

<P><STRONG><a name="[be]"></a>nrf_atomic_flag_clear_fetch</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, nrf_atomic.o(i.nrf_atomic_flag_clear_fetch))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = nrf_atomic_flag_clear_fetch &rArr; nrf_atomic_u32_fetch_and
</UL>
<BR>[Calls]<UL><LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_atomic_u32_fetch_and
</UL>
<BR>[Called By]<UL><LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;log_skip
</UL>

<P><STRONG><a name="[bc]"></a>nrf_atomic_flag_set</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, nrf_atomic.o(i.nrf_atomic_flag_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = nrf_atomic_flag_set &rArr; nrf_atomic_u32_or
</UL>
<BR>[Calls]<UL><LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_atomic_u32_or
</UL>
<BR>[Called By]<UL><LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;log_skip
</UL>

<P><STRONG><a name="[a0]"></a>nrf_atomic_u32_add</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, nrf_atomic.o(i.nrf_atomic_u32_add))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = nrf_atomic_u32_add
</UL>
<BR>[Calls]<UL><LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__asm___12_nrf_atomic_c_85ca2469__nrf_atomic_internal_add
</UL>
<BR>[Called By]<UL><LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_memobj_get
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;buf_prealloc
</UL>

<P><STRONG><a name="[d1]"></a>nrf_atomic_u32_fetch_and</STRONG> (Thumb, 10 bytes, Stack size 8 bytes, nrf_atomic.o(i.nrf_atomic_u32_fetch_and))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = nrf_atomic_u32_fetch_and
</UL>
<BR>[Calls]<UL><LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__asm___12_nrf_atomic_c_85ca2469__nrf_atomic_internal_and
</UL>
<BR>[Called By]<UL><LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_atomic_flag_clear_fetch
</UL>

<P><STRONG><a name="[ad]"></a>nrf_atomic_u32_fetch_store</STRONG> (Thumb, 10 bytes, Stack size 8 bytes, nrf_atomic.o(i.nrf_atomic_u32_fetch_store))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = nrf_atomic_u32_fetch_store
</UL>
<BR>[Calls]<UL><LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__asm___12_nrf_atomic_c_85ca2469__nrf_atomic_internal_mov
</UL>
<BR>[Called By]<UL><LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_pwr_mgmt_shutdown
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dropped_sat16_get
</UL>

<P><STRONG><a name="[d2]"></a>nrf_atomic_u32_or</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, nrf_atomic.o(i.nrf_atomic_u32_or))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = nrf_atomic_u32_or
</UL>
<BR>[Calls]<UL><LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__asm___12_nrf_atomic_c_85ca2469__nrf_atomic_internal_orr
</UL>
<BR>[Called By]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_atomic_flag_set
</UL>

<P><STRONG><a name="[d7]"></a>nrf_atomic_u32_sub</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, nrf_atomic.o(i.nrf_atomic_u32_sub))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = nrf_atomic_u32_sub
</UL>
<BR>[Calls]<UL><LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__asm___12_nrf_atomic_c_85ca2469__nrf_atomic_internal_sub
</UL>
<BR>[Called By]<UL><LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_memobj_put
</UL>

<P><STRONG><a name="[d9]"></a>nrf_balloc_alloc</STRONG> (Thumb, 68 bytes, Stack size 16 bytes, nrf_balloc.o(i.nrf_balloc_alloc))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = nrf_balloc_alloc &rArr; app_util_critical_region_enter
</UL>
<BR>[Calls]<UL><LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_util_critical_region_exit
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_util_critical_region_enter
</UL>
<BR>[Called By]<UL><LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_memobj_alloc
</UL>

<P><STRONG><a name="[da]"></a>nrf_balloc_free</STRONG> (Thumb, 48 bytes, Stack size 16 bytes, nrf_balloc.o(i.nrf_balloc_free))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = nrf_balloc_free &rArr; app_util_critical_region_enter
</UL>
<BR>[Calls]<UL><LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_util_critical_region_exit
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_util_critical_region_enter
</UL>
<BR>[Called By]<UL><LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_memobj_free
</UL>

<P><STRONG><a name="[10b]"></a>nrf_balloc_init</STRONG> (Thumb, 48 bytes, Stack size 8 bytes, nrf_balloc.o(i.nrf_balloc_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = nrf_balloc_init
</UL>
<BR>[Called By]<UL><LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_memobj_pool_init
</UL>

<P><STRONG><a name="[b8]"></a>nrf_ble_gatt_init</STRONG> (Thumb, 34 bytes, Stack size 8 bytes, nrf_ble_gatt.o(i.nrf_ble_gatt_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = nrf_ble_gatt_init
</UL>
<BR>[Calls]<UL><LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;link_init
</UL>
<BR>[Called By]<UL><LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gatt_init
</UL>

<P><STRONG><a name="[4a]"></a>nrf_ble_gatt_on_ble_evt</STRONG> (Thumb, 270 bytes, Stack size 96 bytes, nrf_ble_gatt.o(i.nrf_ble_gatt_on_ble_evt))
<BR><BR>[Stack]<UL><LI>Max Depth = 380<LI>Call Chain = nrf_ble_gatt_on_ble_evt &rArr; on_connected_evt &rArr; data_length_update &rArr; nrf_log_frontend_std_2 &rArr; std_n &rArr; nrf_log_frontend_dequeue &rArr;  nrf_log_frontend_std_0 (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_strerror_get
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;on_exchange_mtu_request_evt
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;on_connected_evt
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;link_init
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;data_length_update
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_frontend_std_1
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>
<BR>[Address Reference Count : 1]<UL><LI> main.o(sdh_ble_observers1)
</UL>
<P><STRONG><a name="[129]"></a>nrf_ble_scan_filter_set</STRONG> (Thumb, 66 bytes, Stack size 20 bytes, nrf_ble_scan.o(i.nrf_ble_scan_filter_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = nrf_ble_scan_filter_set
</UL>
<BR>[Called By]<UL><LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;scan_init
</UL>

<P><STRONG><a name="[e2]"></a>nrf_ble_scan_filters_disable</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, nrf_ble_scan.o(i.nrf_ble_scan_filters_disable))
<BR><BR>[Called By]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_ble_scan_filters_enable
</UL>

<P><STRONG><a name="[e1]"></a>nrf_ble_scan_filters_enable</STRONG> (Thumb, 54 bytes, Stack size 8 bytes, nrf_ble_scan.o(i.nrf_ble_scan_filters_enable))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = nrf_ble_scan_filters_enable
</UL>
<BR>[Calls]<UL><LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_ble_scan_filters_disable
</UL>
<BR>[Called By]<UL><LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;scan_init
</UL>

<P><STRONG><a name="[e3]"></a>nrf_ble_scan_init</STRONG> (Thumb, 106 bytes, Stack size 16 bytes, nrf_ble_scan.o(i.nrf_ble_scan_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = nrf_ble_scan_init
</UL>
<BR>[Calls]<UL><LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_ble_scan_default_param_set
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_ble_scan_default_conn_param_set
<LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;scan_init
</UL>

<P><STRONG><a name="[4b]"></a>nrf_ble_scan_on_ble_evt</STRONG> (Thumb, 128 bytes, Stack size 40 bytes, nrf_ble_scan.o(i.nrf_ble_scan_on_ble_evt))
<BR><BR>[Stack]<UL><LI>Max Depth = 192<LI>Call Chain = nrf_ble_scan_on_ble_evt &rArr; nrf_ble_scan_on_adv_report &rArr; ble_advdata_uuid_find &rArr; ble_advdata_search
</UL>
<BR>[Calls]<UL><LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_ble_scan_on_adv_report
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Address Reference Count : 1]<UL><LI> main.o(sdh_ble_observers1)
</UL>
<P><STRONG><a name="[e8]"></a>nrf_ble_scan_start</STRONG> (Thumb, 92 bytes, Stack size 32 bytes, nrf_ble_scan.o(i.nrf_ble_scan_start))
<BR><BR>[Stack]<UL><LI>Max Depth = 244<LI>Call Chain = nrf_ble_scan_start &rArr; nrf_log_frontend_std_1 &rArr; std_n &rArr; nrf_log_frontend_dequeue &rArr;  nrf_log_frontend_std_0 (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;is_whitelist_used
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_frontend_std_1
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;scan_start
</UL>

<P><STRONG><a name="[e9]"></a>nrf_drv_clock_init</STRONG> (Thumb, 56 bytes, Stack size 16 bytes, nrf_drv_clock.o(i.nrf_drv_clock_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = nrf_drv_clock_init
</UL>
<BR>[Calls]<UL><LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrfx_clock_init
<LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrfx_clock_enable
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_sdh_is_enabled
<LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_wdt_started
</UL>
<BR>[Called By]<UL><LI><a href="#[51]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_state_evt_handler
</UL>

<P><STRONG><a name="[ee]"></a>nrf_drv_clock_lfclk_release</STRONG> (Thumb, 48 bytes, Stack size 16 bytes, nrf_drv_clock.o(i.nrf_drv_clock_lfclk_release))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = nrf_drv_clock_lfclk_release &rArr; nrfx_clock_lfclk_stop &rArr; nrf_clock_event_clear
</UL>
<BR>[Calls]<UL><LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrfx_clock_lfclk_stop
<LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_util_critical_region_exit
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_util_critical_region_enter
<LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_wdt_started
</UL>
<BR>[Called By]<UL><LI><a href="#[51]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_state_evt_handler
</UL>

<P><STRONG><a name="[f0]"></a>nrf_fprintf</STRONG> (Thumb, 26 bytes, Stack size 24 bytes, nrf_fprintf.o(i.nrf_fprintf))
<BR><BR>[Stack]<UL><LI>Max Depth = 176<LI>Call Chain = nrf_fprintf &rArr; nrf_fprintf_fmt &rArr; int_print &rArr; unsigned_print &rArr; buffer_add &rArr;  buffer_add (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_fprintf_fmt
</UL>
<BR>[Called By]<UL><LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prefix_process
<LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;postfix_process
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_std_entry_process
<LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_hexdump_entry_process
</UL>

<P><STRONG><a name="[a4]"></a>nrf_fprintf_buffer_flush</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, nrf_fprintf.o(i.nrf_fprintf_buffer_flush))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = nrf_fprintf_buffer_flush
</UL>
<BR>[Called By]<UL><LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;buffer_add
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_fprintf_fmt
<LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;postfix_process
</UL>

<P><STRONG><a name="[f1]"></a>nrf_fprintf_fmt</STRONG> (Thumb, 474 bytes, Stack size 40 bytes, nrf_fprintf_format.o(i.nrf_fprintf_fmt))
<BR><BR>[Stack]<UL><LI>Max Depth = 152<LI>Call Chain = nrf_fprintf_fmt &rArr; int_print &rArr; unsigned_print &rArr; buffer_add &rArr;  buffer_add (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;unsigned_print
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;int_print
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;buffer_add
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_fprintf_buffer_flush
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
</UL>
<BR>[Called By]<UL><LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_fprintf
</UL>

<P><STRONG><a name="[fe]"></a>nrf_log_backend_add</STRONG> (Thumb, 86 bytes, Stack size 12 bytes, nrf_log_frontend.o(i.nrf_log_backend_add))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = nrf_log_backend_add
</UL>
<BR>[Called By]<UL><LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_default_backends_init
</UL>

<P><STRONG><a name="[f7]"></a>nrf_log_backend_rtt_init</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, nrf_log_backend_rtt.o(i.nrf_log_backend_rtt_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = nrf_log_backend_rtt_init &rArr; SEGGER_RTT_Init &rArr; _DoInit
</UL>
<BR>[Calls]<UL><LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SEGGER_RTT_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_default_backends_init
</UL>

<P><STRONG><a name="[f8]"></a>nrf_log_backend_serial_put</STRONG> (Thumb, 200 bytes, Stack size 104 bytes, nrf_log_backend_serial.o(i.nrf_log_backend_serial_put))
<BR><BR>[Stack]<UL><LI>Max Depth = 344<LI>Call Chain = nrf_log_backend_serial_put &rArr; nrf_log_std_entry_process &rArr; prefix_process &rArr; nrf_fprintf &rArr; nrf_fprintf_fmt &rArr; int_print &rArr; unsigned_print &rArr; buffer_add &rArr;  buffer_add (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_memobj_read
<LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_memobj_put
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_memobj_get
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_std_entry_process
<LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_hexdump_entry_process
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[46]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_backend_rtt_put
</UL>

<P><STRONG><a name="[11e]"></a>nrf_log_color_id_get</STRONG> (Thumb, 42 bytes, Stack size 0 bytes, nrf_log_frontend.o(i.nrf_log_color_id_get))
<BR><BR>[Called By]<UL><LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prefix_process
</UL>

<P><STRONG><a name="[c0]"></a>nrf_log_default_backends_init</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, nrf_log_default_backends.o(i.nrf_log_default_backends_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = nrf_log_default_backends_init &rArr; nrf_log_backend_add
</UL>
<BR>[Calls]<UL><LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_backend_add
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_backend_rtt_init
</UL>
<BR>[Called By]<UL><LI><a href="#[35]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[77]"></a>nrf_log_frontend_dequeue</STRONG> (Thumb, 528 bytes, Stack size 88 bytes, nrf_log_frontend.o(i.nrf_log_frontend_dequeue))
<BR><BR>[Stack]<UL><LI>Max Depth = 164 + In Cycle
<LI>Call Chain = nrf_log_frontend_dequeue &rArr;  nrf_log_frontend_std_0 (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_frontend_std_0
<LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_util_critical_region_exit
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_util_critical_region_enter
<LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_memobj_write
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_memobj_alloc
<LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_memobj_put
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_memobj_get
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;buffer_is_empty
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;invalid_packets_omit
</UL>
<BR>[Called By]<UL><LI><a href="#[35]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[42]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_error_fault_handler
<LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;shutdown_process
<LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;std_n
</UL>

<P><STRONG><a name="[78]"></a>nrf_log_frontend_std_0</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, nrf_log_frontend.o(i.nrf_log_frontend_std_0))
<BR><BR>[Stack]<UL><LI>Max Depth = 204<LI>Call Chain = nrf_log_frontend_std_0 &rArr; std_n &rArr; nrf_log_frontend_dequeue &rArr;  nrf_log_frontend_std_0 (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;std_n
</UL>
<BR>[Called By]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_sdh_ble_enable
<LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_frontend_dequeue
<LI><a href="#[35]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[3e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gatt_evt_handler
<LI><a href="#[43]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;scan_evt_handler
<LI><a href="#[4d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_evt_handler
<LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;drv_rtc_init
<LI><a href="#[42]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_error_fault_handler
</UL>

<P><STRONG><a name="[a8]"></a>nrf_log_frontend_std_1</STRONG> (Thumb, 14 bytes, Stack size 8 bytes, nrf_log_frontend.o(i.nrf_log_frontend_std_1))
<BR><BR>[Stack]<UL><LI>Max Depth = 212<LI>Call Chain = nrf_log_frontend_std_1 &rArr; std_n &rArr; nrf_log_frontend_dequeue &rArr;  nrf_log_frontend_std_0 (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;std_n
</UL>
<BR>[Called By]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_sdh_ble_enable
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_sdh_ble_default_cfg_set
<LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_ble_scan_start
<LI><a href="#[4a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_ble_gatt_on_ble_evt
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;on_exchange_mtu_request_evt
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;on_connected_evt
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;data_length_update
</UL>

<P><STRONG><a name="[89]"></a>nrf_log_frontend_std_2</STRONG> (Thumb, 16 bytes, Stack size 16 bytes, nrf_log_frontend.o(i.nrf_log_frontend_std_2))
<BR><BR>[Stack]<UL><LI>Max Depth = 220<LI>Call Chain = nrf_log_frontend_std_2 &rArr; std_n &rArr; nrf_log_frontend_dequeue &rArr;  nrf_log_frontend_std_0 (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;std_n
</UL>
<BR>[Called By]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_sdh_ble_enable
<LI><a href="#[3e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gatt_evt_handler
<LI><a href="#[4d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_evt_handler
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;data_length_update
</UL>

<P><STRONG><a name="[103]"></a>nrf_log_frontend_std_6</STRONG> (Thumb, 28 bytes, Stack size 48 bytes, nrf_log_frontend.o(i.nrf_log_frontend_std_6))
<BR><BR>[Stack]<UL><LI>Max Depth = 252<LI>Call Chain = nrf_log_frontend_std_6 &rArr; std_n &rArr; nrf_log_frontend_dequeue &rArr;  nrf_log_frontend_std_0 (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;std_n
</UL>
<BR>[Called By]<UL><LI><a href="#[43]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;scan_evt_handler
</UL>

<P><STRONG><a name="[fc]"></a>nrf_log_hexdump_entry_process</STRONG> (Thumb, 150 bytes, Stack size 32 bytes, nrf_log_str_formatter.o(i.nrf_log_hexdump_entry_process))
<BR><BR>[Stack]<UL><LI>Max Depth = 224<LI>Call Chain = nrf_log_hexdump_entry_process &rArr; prefix_process &rArr; nrf_fprintf &rArr; nrf_fprintf_fmt &rArr; int_print &rArr; unsigned_print &rArr; buffer_add &rArr;  buffer_add (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_fprintf
<LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prefix_process
<LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;postfix_process
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_ctype_table
</UL>
<BR>[Called By]<UL><LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_backend_serial_put
</UL>

<P><STRONG><a name="[bf]"></a>nrf_log_init</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, nrf_log_frontend.o(i.nrf_log_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = nrf_log_init &rArr; nrf_memobj_pool_init &rArr; nrf_balloc_init
</UL>
<BR>[Calls]<UL><LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_memobj_pool_init
<LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_ringbuf_init
</UL>
<BR>[Called By]<UL><LI><a href="#[35]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[ca]"></a>nrf_log_module_cnt_get</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, nrf_log_frontend.o(i.nrf_log_module_cnt_get))
<BR><BR>[Called By]<UL><LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;module_idx_get
</UL>

<P><STRONG><a name="[109]"></a>nrf_log_module_name_get</STRONG> (Thumb, 24 bytes, Stack size 16 bytes, nrf_log_frontend.o(i.nrf_log_module_name_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = nrf_log_module_name_get &rArr; module_idx_get
</UL>
<BR>[Calls]<UL><LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;module_idx_get
</UL>
<BR>[Called By]<UL><LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prefix_process
</UL>

<P><STRONG><a name="[76]"></a>nrf_log_panic</STRONG> (Thumb, 36 bytes, Stack size 16 bytes, nrf_log_frontend.o(i.nrf_log_panic))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = nrf_log_panic
</UL>
<BR>[Called By]<UL><LI><a href="#[42]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_error_fault_handler
<LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;shutdown_process
</UL>

<P><STRONG><a name="[fb]"></a>nrf_log_std_entry_process</STRONG> (Thumb, 182 bytes, Stack size 48 bytes, nrf_log_str_formatter.o(i.nrf_log_std_entry_process))
<BR><BR>[Stack]<UL><LI>Max Depth = 240<LI>Call Chain = nrf_log_std_entry_process &rArr; prefix_process &rArr; nrf_fprintf &rArr; nrf_fprintf_fmt &rArr; int_print &rArr; unsigned_print &rArr; buffer_add &rArr;  buffer_add (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_fprintf
<LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prefix_process
<LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;postfix_process
</UL>
<BR>[Called By]<UL><LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_backend_serial_put
</UL>

<P><STRONG><a name="[100]"></a>nrf_memobj_alloc</STRONG> (Thumb, 96 bytes, Stack size 24 bytes, nrf_memobj.o(i.nrf_memobj_alloc))
<BR><BR>[Stack]<UL><LI>Max Depth = 76<LI>Call Chain = nrf_memobj_alloc &rArr; nrf_memobj_free &rArr; nrf_balloc_free &rArr; app_util_critical_region_enter
</UL>
<BR>[Calls]<UL><LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_memobj_free
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_balloc_alloc
</UL>
<BR>[Called By]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_frontend_dequeue
</UL>

<P><STRONG><a name="[10a]"></a>nrf_memobj_free</STRONG> (Thumb, 50 bytes, Stack size 24 bytes, nrf_memobj.o(i.nrf_memobj_free))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = nrf_memobj_free &rArr; nrf_balloc_free &rArr; app_util_critical_region_enter
</UL>
<BR>[Calls]<UL><LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_balloc_free
</UL>
<BR>[Called By]<UL><LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_memobj_alloc
<LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_memobj_put
</UL>

<P><STRONG><a name="[f9]"></a>nrf_memobj_get</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, nrf_memobj.o(i.nrf_memobj_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = nrf_memobj_get &rArr; nrf_atomic_u32_add
</UL>
<BR>[Calls]<UL><LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_atomic_u32_add
</UL>
<BR>[Called By]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_frontend_dequeue
<LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_backend_serial_put
</UL>

<P><STRONG><a name="[107]"></a>nrf_memobj_pool_init</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, nrf_memobj.o(i.nrf_memobj_pool_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = nrf_memobj_pool_init &rArr; nrf_balloc_init
</UL>
<BR>[Calls]<UL><LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_balloc_init
</UL>
<BR>[Called By]<UL><LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_init
</UL>

<P><STRONG><a name="[fd]"></a>nrf_memobj_put</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, nrf_memobj.o(i.nrf_memobj_put))
<BR><BR>[Stack]<UL><LI>Max Depth = 60<LI>Call Chain = nrf_memobj_put &rArr; nrf_memobj_free &rArr; nrf_balloc_free &rArr; app_util_critical_region_enter
</UL>
<BR>[Calls]<UL><LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_memobj_free
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_atomic_u32_sub
</UL>
<BR>[Called By]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_frontend_dequeue
<LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_backend_serial_put
</UL>

<P><STRONG><a name="[fa]"></a>nrf_memobj_read</STRONG> (Thumb, 16 bytes, Stack size 16 bytes, nrf_memobj.o(i.nrf_memobj_read))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = nrf_memobj_read &rArr; memobj_op
</UL>
<BR>[Calls]<UL><LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;memobj_op
</UL>
<BR>[Called By]<UL><LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_backend_serial_put
</UL>

<P><STRONG><a name="[101]"></a>nrf_memobj_write</STRONG> (Thumb, 16 bytes, Stack size 16 bytes, nrf_memobj.o(i.nrf_memobj_write))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = nrf_memobj_write &rArr; memobj_op
</UL>
<BR>[Calls]<UL><LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;memobj_op
</UL>
<BR>[Called By]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_frontend_dequeue
</UL>

<P><STRONG><a name="[c1]"></a>nrf_pwr_mgmt_init</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, nrf_pwr_mgmt.o(i.nrf_pwr_mgmt_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = nrf_pwr_mgmt_init
</UL>
<BR>[Calls]<UL><LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_section_iter_init
</UL>
<BR>[Called By]<UL><LI><a href="#[35]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[c7]"></a>nrf_pwr_mgmt_run</STRONG> (Thumb, 64 bytes, Stack size 8 bytes, nrf_pwr_mgmt.o(i.nrf_pwr_mgmt_run))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = nrf_pwr_mgmt_run &rArr; app_util_critical_region_enter
</UL>
<BR>[Calls]<UL><LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_sdh_is_enabled
<LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_util_critical_region_exit
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_util_critical_region_enter
</UL>
<BR>[Called By]<UL><LI><a href="#[35]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[99]"></a>nrf_pwr_mgmt_shutdown</STRONG> (Thumb, 54 bytes, Stack size 16 bytes, nrf_pwr_mgmt.o(i.nrf_pwr_mgmt_shutdown))
<BR><BR>[Stack]<UL><LI>Max Depth = 196<LI>Call Chain = nrf_pwr_mgmt_shutdown &rArr; shutdown_process &rArr; nrf_log_frontend_dequeue &rArr;  nrf_log_frontend_std_0 (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;shutdown_process
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_atomic_u32_fetch_store
</UL>
<BR>[Called By]<UL><LI><a href="#[3f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_event_handler
</UL>

<P><STRONG><a name="[108]"></a>nrf_ringbuf_init</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, nrf_ringbuf.o(i.nrf_ringbuf_init))
<BR><BR>[Called By]<UL><LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_init
</UL>

<P><STRONG><a name="[10e]"></a>nrf_sdh_ble_app_ram_start_get</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, nrf_sdh_ble.o(i.nrf_sdh_ble_app_ram_start_get))
<BR><BR>[Called By]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_sdh_ble_default_cfg_set
</UL>

<P><STRONG><a name="[c3]"></a>nrf_sdh_ble_default_cfg_set</STRONG> (Thumb, 272 bytes, Stack size 40 bytes, nrf_sdh_ble.o(i.nrf_sdh_ble_default_cfg_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 252<LI>Call Chain = nrf_sdh_ble_default_cfg_set &rArr; nrf_log_frontend_std_1 &rArr; std_n &rArr; nrf_log_frontend_dequeue &rArr;  nrf_log_frontend_std_0 (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_strerror_get
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_frontend_std_1
<LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_sdh_ble_app_ram_start_get
</UL>
<BR>[Called By]<UL><LI><a href="#[35]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[c4]"></a>nrf_sdh_ble_enable</STRONG> (Thumb, 116 bytes, Stack size 24 bytes, nrf_sdh_ble.o(i.nrf_sdh_ble_enable))
<BR><BR>[Stack]<UL><LI>Max Depth = 244<LI>Call Chain = nrf_sdh_ble_enable &rArr; nrf_log_frontend_std_2 &rArr; std_n &rArr; nrf_log_frontend_dequeue &rArr;  nrf_log_frontend_std_0 (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_frontend_std_2
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_frontend_std_0
<LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_strerror_get
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_frontend_std_1
</UL>
<BR>[Called By]<UL><LI><a href="#[35]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[c2]"></a>nrf_sdh_enable_request</STRONG> (Thumb, 102 bytes, Stack size 24 bytes, nrf_sdh.o(i.nrf_sdh_enable_request))
<BR><BR>[Stack]<UL><LI>Max Depth = 252<LI>Call Chain = nrf_sdh_enable_request &rArr; softdevices_evt_irq_enable &rArr; app_error_handler_bare &rArr; app_error_fault_handler &rArr; nrf_log_frontend_std_0 &rArr; std_n &rArr; nrf_log_frontend_dequeue &rArr;  nrf_log_frontend_std_0 (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_util_critical_region_exit
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_util_critical_region_enter
<LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;softdevices_evt_irq_enable
<LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdh_state_observer_notify
<LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdh_request_observer_notify
</UL>
<BR>[Called By]<UL><LI><a href="#[35]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[69]"></a>nrf_sdh_evts_poll</STRONG> (Thumb, 32 bytes, Stack size 16 bytes, nrf_sdh.o(i.nrf_sdh_evts_poll))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = nrf_sdh_evts_poll &rArr; nrf_section_iter_next &rArr; nrf_section_iter_item_set
</UL>
<BR>[Calls]<UL><LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_section_iter_init
<LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_section_iter_next
</UL>
<BR>[Called By]<UL><LI><a href="#[20]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SWI2_EGU2_IRQHandler
</UL>

<P><STRONG><a name="[eb]"></a>nrf_sdh_is_enabled</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, nrf_sdh.o(i.nrf_sdh_is_enabled))
<BR><BR>[Called By]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_pwr_mgmt_run
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_drv_clock_init
<LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;shutdown_process
</UL>

<P><STRONG><a name="[10c]"></a>nrf_section_iter_init</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, nrf_section_iter.o(i.nrf_section_iter_init))
<BR><BR>[Called By]<UL><LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_pwr_mgmt_init
<LI><a href="#[50]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_sdh_soc_evts_poll
<LI><a href="#[4f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_sdh_ble_evts_poll
<LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_sdh_evts_poll
<LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdh_state_observer_notify
<LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdh_request_observer_notify
</UL>

<P><STRONG><a name="[10f]"></a>nrf_section_iter_next</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, nrf_section_iter.o(i.nrf_section_iter_next))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = nrf_section_iter_next &rArr; nrf_section_iter_item_set
</UL>
<BR>[Calls]<UL><LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_section_iter_item_set
</UL>
<BR>[Called By]<UL><LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;shutdown_process
<LI><a href="#[50]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_sdh_soc_evts_poll
<LI><a href="#[4f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_sdh_ble_evts_poll
<LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_sdh_evts_poll
<LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdh_state_observer_notify
<LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdh_request_observer_notify
</UL>

<P><STRONG><a name="[125]"></a>nrf_sortlist_add</STRONG> (Thumb, 34 bytes, Stack size 16 bytes, nrf_sortlist.o(i.nrf_sortlist_add))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = nrf_sortlist_add
</UL>
<BR>[Called By]<UL><LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_req_process
<LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_expire
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_update
</UL>

<P><STRONG><a name="[124]"></a>nrf_sortlist_peek</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, nrf_sortlist.o(i.nrf_sortlist_peek))
<BR><BR>[Called By]<UL><LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_update
</UL>

<P><STRONG><a name="[12b]"></a>nrf_sortlist_pop</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, nrf_sortlist.o(i.nrf_sortlist_pop))
<BR><BR>[Called By]<UL><LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sortlist_pop
</UL>

<P><STRONG><a name="[12c]"></a>nrf_sortlist_remove</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, nrf_sortlist.o(i.nrf_sortlist_remove))
<BR><BR>[Called By]<UL><LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_req_process
</UL>

<P><STRONG><a name="[114]"></a>nrf_strerror_find</STRONG> (Thumb, 52 bytes, Stack size 8 bytes, nrf_strerror.o(i.nrf_strerror_find))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = nrf_strerror_find
</UL>
<BR>[Called By]<UL><LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_strerror_get
</UL>

<P><STRONG><a name="[a7]"></a>nrf_strerror_get</STRONG> (Thumb, 14 bytes, Stack size 4 bytes, nrf_strerror.o(i.nrf_strerror_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = nrf_strerror_get &rArr; nrf_strerror_find
</UL>
<BR>[Calls]<UL><LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_strerror_find
</UL>
<BR>[Called By]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_sdh_ble_enable
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_sdh_ble_default_cfg_set
<LI><a href="#[4a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_ble_gatt_on_ble_evt
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;on_exchange_mtu_request_evt
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;on_connected_evt
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;data_length_update
</UL>

<P><STRONG><a name="[ec]"></a>nrfx_clock_enable</STRONG> (Thumb, 34 bytes, Stack size 0 bytes, nrfx_clock.o(i.nrfx_clock_enable))
<BR><BR>[Called By]<UL><LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_drv_clock_init
<LI><a href="#[51]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_state_evt_handler
</UL>

<P><STRONG><a name="[ea]"></a>nrfx_clock_init</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, nrfx_clock.o(i.nrfx_clock_init))
<BR><BR>[Called By]<UL><LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_drv_clock_init
</UL>

<P><STRONG><a name="[ef]"></a>nrfx_clock_lfclk_stop</STRONG> (Thumb, 38 bytes, Stack size 8 bytes, nrfx_clock.o(i.nrfx_clock_lfclk_stop))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = nrfx_clock_lfclk_stop &rArr; nrf_clock_event_clear
</UL>
<BR>[Calls]<UL><LI><a href="#[62]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_clock_event_clear
</UL>
<BR>[Called By]<UL><LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_drv_clock_lfclk_release
</UL>

<P><STRONG><a name="[70]"></a>nrfx_gpiote_in_event_enable</STRONG> (Thumb, 144 bytes, Stack size 24 bytes, nrfx_gpiote.o(i.nrfx_gpiote_in_event_enable))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = nrfx_gpiote_in_event_enable &rArr; nrf_gpio_cfg_sense_set
</UL>
<BR>[Calls]<UL><LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;port_handler_polarity_get
<LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pin_in_use_by_te
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pin_in_use_by_port
<LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_gpiote_event_clear
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_gpio_pin_read
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_gpio_cfg_sense_set
<LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;channel_port_get
</UL>
<BR>[Called By]<UL><LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_button_enable
</UL>

<P><STRONG><a name="[74]"></a>nrfx_gpiote_in_init</STRONG> (Thumb, 196 bytes, Stack size 32 bytes, nrfx_gpiote.o(i.nrfx_gpiote_in_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = nrfx_gpiote_in_init &rArr; nrf_gpio_cfg
</UL>
<BR>[Calls]<UL><LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pin_configured_set
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_gpio_pin_port_decode
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_gpio_cfg
<LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;channel_port_alloc
</UL>
<BR>[Called By]<UL><LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_button_init
</UL>

<P><STRONG><a name="[a9]"></a>nrfx_gpiote_in_is_set</STRONG> (Thumb, 14 bytes, Stack size 8 bytes, nrfx_gpiote.o(i.nrfx_gpiote_in_is_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = nrfx_gpiote_in_is_set &rArr; nrf_gpio_pin_read
</UL>
<BR>[Calls]<UL><LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_gpio_pin_read
</UL>
<BR>[Called By]<UL><LI><a href="#[38]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpiote_event_handler
<LI><a href="#[39]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;detection_delay_timeout_handler
</UL>

<P><STRONG><a name="[73]"></a>nrfx_gpiote_init</STRONG> (Thumb, 108 bytes, Stack size 16 bytes, nrfx_gpiote.o(i.nrfx_gpiote_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = nrfx_gpiote_init &rArr; nrf_gpiote_event_clear
</UL>
<BR>[Calls]<UL><LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_gpiote_event_clear
<LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_gpio_pin_present_check
<LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;channel_free
</UL>
<BR>[Called By]<UL><LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_button_init
</UL>

<P><STRONG><a name="[72]"></a>nrfx_gpiote_is_init</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, nrfx_gpiote.o(i.nrfx_gpiote_is_init))
<BR><BR>[Called By]<UL><LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_button_init
</UL>
<P>
<H3>
Local Symbols
</H3>
<P><STRONG><a name="[4d]"></a>ble_evt_handler</STRONG> (Thumb, 174 bytes, Stack size 16 bytes, main.o(i.ble_evt_handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 236<LI>Call Chain = ble_evt_handler &rArr; nrf_log_frontend_std_2 &rArr; std_n &rArr; nrf_log_frontend_dequeue &rArr;  nrf_log_frontend_std_0 (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_frontend_std_2
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_frontend_std_0
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_indication_set
<LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_error_handler_bare
</UL>
<BR>[Address Reference Count : 1]<UL><LI> main.o(sdh_ble_observers3)
</UL>
<P><STRONG><a name="[43]"></a>scan_evt_handler</STRONG> (Thumb, 88 bytes, Stack size 32 bytes, main.o(i.scan_evt_handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 284<LI>Call Chain = scan_evt_handler &rArr; nrf_log_frontend_std_6 &rArr; std_n &rArr; nrf_log_frontend_dequeue &rArr;  nrf_log_frontend_std_0 (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_frontend_std_6
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_frontend_std_0
<LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_error_handler_bare
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;scan_start
</UL>
<BR>[Address Reference Count : 1]<UL><LI> main.o(i.scan_init)
</UL>
<P><STRONG><a name="[c5]"></a>scan_init</STRONG> (Thumb, 74 bytes, Stack size 24 bytes, main.o(i.scan_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 244<LI>Call Chain = scan_init &rArr; app_error_handler_bare &rArr; app_error_fault_handler &rArr; nrf_log_frontend_std_0 &rArr; std_n &rArr; nrf_log_frontend_dequeue &rArr;  nrf_log_frontend_std_0 (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_ble_scan_init
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_ble_scan_filters_enable
<LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_ble_scan_filter_set
<LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_error_handler_bare
</UL>
<BR>[Called By]<UL><LI><a href="#[35]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[c6]"></a>scan_start</STRONG> (Thumb, 34 bytes, Stack size 8 bytes, main.o(i.scan_start))
<BR><BR>[Stack]<UL><LI>Max Depth = 252<LI>Call Chain = scan_start &rArr; nrf_ble_scan_start &rArr; nrf_log_frontend_std_1 &rArr; std_n &rArr; nrf_log_frontend_dequeue &rArr;  nrf_log_frontend_std_0 (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_ble_scan_start
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_indication_set
<LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_error_handler_bare
</UL>
<BR>[Called By]<UL><LI><a href="#[35]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[43]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;scan_evt_handler
</UL>

<P><STRONG><a name="[49]"></a>shutdown_handler</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, main.o(i.shutdown_handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 228<LI>Call Chain = shutdown_handler &rArr; app_error_handler_bare &rArr; app_error_fault_handler &rArr; nrf_log_frontend_std_0 &rArr; std_n &rArr; nrf_log_frontend_dequeue &rArr;  nrf_log_frontend_std_0 (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_indication_set
<LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_btn_ble_sleep_mode_prepare
<LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_error_handler_bare
</UL>
<BR>[Address Reference Count : 1]<UL><LI> main.o(pwr_mgmt_data1)
</UL>
<P><STRONG><a name="[8d]"></a>nrf_gpio_cfg</STRONG> (Thumb, 50 bytes, Stack size 40 bytes, boards.o(i.nrf_gpio_cfg))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = nrf_gpio_cfg
</UL>
<BR>[Calls]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_gpio_pin_port_decode
</UL>
<BR>[Called By]<UL><LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_board_init
</UL>

<P><STRONG><a name="[8b]"></a>nrf_gpio_pin_port_decode</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, boards.o(i.nrf_gpio_pin_port_decode))
<BR><BR>[Called By]<UL><LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_board_led_state_get
<LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_board_led_invert
<LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_board_button_state_get
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_gpio_pin_write
<LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_gpio_cfg
</UL>

<P><STRONG><a name="[90]"></a>nrf_gpio_pin_write</STRONG> (Thumb, 38 bytes, Stack size 16 bytes, boards.o(i.nrf_gpio_pin_write))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = nrf_gpio_pin_write
</UL>
<BR>[Calls]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_gpio_pin_port_decode
</UL>
<BR>[Called By]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_board_led_on
<LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_board_led_off
</UL>

<P><STRONG><a name="[3d]"></a>alert_timer_handler</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, bsp.o(i.alert_timer_handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = alert_timer_handler &rArr; bsp_board_led_invert
</UL>
<BR>[Calls]<UL><LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_board_led_invert
</UL>
<BR>[Address Reference Count : 1]<UL><LI> bsp.o(i.bsp_init)
</UL>
<P><STRONG><a name="[44]"></a>bsp_button_event_handler</STRONG> (Thumb, 132 bytes, Stack size 32 bytes, bsp.o(i.bsp_button_event_handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = bsp_button_event_handler &rArr; app_timer_start &rArr; timer_req_schedule &rArr; nrf_atfifo_item_alloc
</UL>
<BR>[Calls]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_timer_stop
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_timer_start
<LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_board_pin_to_button_idx
</UL>
<BR>[Called By]<UL><LI><a href="#[3b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;button_timer_handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> bsp.o(.constdata)
</UL>
<P><STRONG><a name="[9a]"></a>bsp_led_indication</STRONG> (Thumb, 446 bytes, Stack size 40 bytes, bsp.o(i.bsp_led_indication))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = bsp_led_indication &rArr; app_timer_start &rArr; timer_req_schedule &rArr; nrf_atfifo_item_alloc
</UL>
<BR>[Calls]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_timer_stop
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_timer_start
<LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;leds_off
<LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_board_leds_on
<LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_board_led_state_get
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_board_led_on
<LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_board_led_off
<LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_board_led_invert
<LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
</UL>
<BR>[Called By]<UL><LI><a href="#[3c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;leds_timer_handler
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_indication_set
</UL>

<P><STRONG><a name="[3b]"></a>button_timer_handler</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, bsp.o(i.button_timer_handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = button_timer_handler &rArr; bsp_button_event_handler &rArr; app_timer_start &rArr; timer_req_schedule &rArr; nrf_atfifo_item_alloc
</UL>
<BR>[Calls]<UL><LI><a href="#[44]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_button_event_handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> bsp.o(i.bsp_init)
</UL>
<P><STRONG><a name="[9c]"></a>leds_off</STRONG> (Thumb, 42 bytes, Stack size 8 bytes, bsp.o(i.leds_off))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = leds_off &rArr; bsp_board_leds_off &rArr; bsp_board_led_off &rArr; nrf_gpio_pin_write
</UL>
<BR>[Calls]<UL><LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_board_leds_off
<LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_board_led_off
</UL>
<BR>[Called By]<UL><LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_led_indication
</UL>

<P><STRONG><a name="[3c]"></a>leds_timer_handler</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, bsp.o(i.leds_timer_handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = leds_timer_handler &rArr; bsp_led_indication &rArr; app_timer_start &rArr; timer_req_schedule &rArr; nrf_atfifo_item_alloc
</UL>
<BR>[Calls]<UL><LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_led_indication
</UL>
<BR>[Address Reference Count : 1]<UL><LI> bsp.o(i.bsp_init)
</UL>
<P><STRONG><a name="[9d]"></a>wakeup_button_cfg</STRONG> (Thumb, 70 bytes, Stack size 8 bytes, bsp.o(i.wakeup_button_cfg))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = wakeup_button_cfg
</UL>
<BR>[Calls]<UL><LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_board_button_idx_to_pin
</UL>
<BR>[Called By]<UL><LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_wakeup_button_enable
</UL>

<P><STRONG><a name="[6c]"></a>advertising_buttons_configure</STRONG> (Thumb, 54 bytes, Stack size 8 bytes, bsp_btn_ble.o(i.advertising_buttons_configure))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = advertising_buttons_configure &rArr; bsp_event_to_button_action_assign
</UL>
<BR>[Calls]<UL><LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_event_to_button_action_assign
</UL>
<BR>[Called By]<UL><LI><a href="#[4c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_evt_handler
<LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_btn_ble_init
</UL>

<P><STRONG><a name="[4c]"></a>ble_evt_handler</STRONG> (Thumb, 106 bytes, Stack size 8 bytes, bsp_btn_ble.o(i.ble_evt_handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = ble_evt_handler &rArr; advertising_buttons_configure &rArr; bsp_event_to_button_action_assign
</UL>
<BR>[Calls]<UL><LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;advertising_buttons_configure
<LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_event_to_button_action_assign
</UL>
<BR>[Address Reference Count : 1]<UL><LI> bsp_btn_ble.o(sdh_ble_observers1)
</UL>
<P><STRONG><a name="[a6]"></a>data_length_update</STRONG> (Thumb, 110 bytes, Stack size 48 bytes, nrf_ble_gatt.o(i.data_length_update))
<BR><BR>[Stack]<UL><LI>Max Depth = 268<LI>Call Chain = data_length_update &rArr; nrf_log_frontend_std_2 &rArr; std_n &rArr; nrf_log_frontend_dequeue &rArr;  nrf_log_frontend_std_0 (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_frontend_std_2
<LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_strerror_get
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_frontend_std_1
</UL>
<BR>[Called By]<UL><LI><a href="#[4a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_ble_gatt_on_ble_evt
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;on_connected_evt
</UL>

<P><STRONG><a name="[db]"></a>link_init</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, nrf_ble_gatt.o(i.link_init))
<BR><BR>[Called By]<UL><LI><a href="#[4a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_ble_gatt_on_ble_evt
<LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_ble_gatt_init
</UL>

<P><STRONG><a name="[dc]"></a>on_connected_evt</STRONG> (Thumb, 106 bytes, Stack size 16 bytes, nrf_ble_gatt.o(i.on_connected_evt))
<BR><BR>[Stack]<UL><LI>Max Depth = 284<LI>Call Chain = on_connected_evt &rArr; data_length_update &rArr; nrf_log_frontend_std_2 &rArr; std_n &rArr; nrf_log_frontend_dequeue &rArr;  nrf_log_frontend_std_0 (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_strerror_get
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;data_length_update
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_frontend_std_1
</UL>
<BR>[Called By]<UL><LI><a href="#[4a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_ble_gatt_on_ble_evt
</UL>

<P><STRONG><a name="[dd]"></a>on_exchange_mtu_request_evt</STRONG> (Thumb, 110 bytes, Stack size 24 bytes, nrf_ble_gatt.o(i.on_exchange_mtu_request_evt))
<BR><BR>[Stack]<UL><LI>Max Depth = 236<LI>Call Chain = on_exchange_mtu_request_evt &rArr; nrf_log_frontend_std_1 &rArr; std_n &rArr; nrf_log_frontend_dequeue &rArr;  nrf_log_frontend_std_0 (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_strerror_get
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_frontend_std_1
</UL>
<BR>[Called By]<UL><LI><a href="#[4a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_ble_gatt_on_ble_evt
</UL>

<P><STRONG><a name="[df]"></a>nrf_ble_scan_connect_with_target</STRONG> (Thumb, 76 bytes, Stack size 48 bytes, nrf_ble_scan.o(i.nrf_ble_scan_connect_with_target))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = nrf_ble_scan_connect_with_target
</UL>
<BR>[Calls]<UL><LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_ble_scan_on_adv_report
</UL>

<P><STRONG><a name="[e5]"></a>nrf_ble_scan_default_conn_param_set</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, nrf_ble_scan.o(i.nrf_ble_scan_default_conn_param_set))
<BR><BR>[Called By]<UL><LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_ble_scan_init
</UL>

<P><STRONG><a name="[e4]"></a>nrf_ble_scan_default_param_set</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, nrf_ble_scan.o(i.nrf_ble_scan_default_param_set))
<BR><BR>[Called By]<UL><LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_ble_scan_init
</UL>

<P><STRONG><a name="[e6]"></a>nrf_ble_scan_on_adv_report</STRONG> (Thumb, 252 bytes, Stack size 72 bytes, nrf_ble_scan.o(i.nrf_ble_scan_on_adv_report))
<BR><BR>[Stack]<UL><LI>Max Depth = 152<LI>Call Chain = nrf_ble_scan_on_adv_report &rArr; ble_advdata_uuid_find &rArr; ble_advdata_search
</UL>
<BR>[Calls]<UL><LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;is_whitelist_used
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_ble_scan_connect_with_target
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_advdata_uuid_find
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[4b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_ble_scan_on_ble_evt
</UL>

<P><STRONG><a name="[a5]"></a>clock_clk_started_notify</STRONG> (Thumb, 34 bytes, Stack size 16 bytes, nrf_drv_clock.o(i.clock_clk_started_notify))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = clock_clk_started_notify
</UL>
<BR>[Called By]<UL><LI><a href="#[4e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;soc_evt_handler
<LI><a href="#[40]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;clock_irq_handler
</UL>

<P><STRONG><a name="[40]"></a>clock_irq_handler</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, nrf_drv_clock.o(i.clock_irq_handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = clock_irq_handler &rArr; clock_clk_started_notify
</UL>
<BR>[Calls]<UL><LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;clock_clk_started_notify
</UL>
<BR>[Address Reference Count : 1]<UL><LI> nrf_drv_clock.o(i.nrf_drv_clock_init)
</UL>
<P><STRONG><a name="[ed]"></a>nrf_wdt_started</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, nrf_drv_clock.o(i.nrf_wdt_started))
<BR><BR>[Called By]<UL><LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_drv_clock_lfclk_release
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_drv_clock_init
</UL>

<P><STRONG><a name="[51]"></a>sd_state_evt_handler</STRONG> (Thumb, 84 bytes, Stack size 16 bytes, nrf_drv_clock.o(i.sd_state_evt_handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = sd_state_evt_handler &rArr; nrf_drv_clock_lfclk_release &rArr; nrfx_clock_lfclk_stop &rArr; nrf_clock_event_clear
</UL>
<BR>[Calls]<UL><LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrfx_clock_enable
<LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_util_critical_region_exit
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_util_critical_region_enter
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_drv_clock_lfclk_release
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_drv_clock_init
</UL>
<BR>[Address Reference Count : 1]<UL><LI> nrf_drv_clock.o(sdh_state_observers0)
</UL>
<P><STRONG><a name="[4e]"></a>soc_evt_handler</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, nrf_drv_clock.o(i.soc_evt_handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = soc_evt_handler &rArr; clock_clk_started_notify
</UL>
<BR>[Calls]<UL><LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;clock_clk_started_notify
</UL>
<BR>[Address Reference Count : 1]<UL><LI> nrf_drv_clock.o(sdh_soc_observers0)
</UL>
<P><STRONG><a name="[61]"></a>nrf_clock_event_check</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, nrfx_clock.o(i.nrf_clock_event_check))
<BR><BR>[Called By]<UL><LI><a href="#[a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;POWER_CLOCK_IRQHandler
</UL>

<P><STRONG><a name="[62]"></a>nrf_clock_event_clear</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, nrfx_clock.o(i.nrf_clock_event_clear))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = nrf_clock_event_clear
</UL>
<BR>[Called By]<UL><LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrfx_clock_lfclk_stop
<LI><a href="#[a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;POWER_CLOCK_IRQHandler
</UL>

<P><STRONG><a name="[11c]"></a>channel_free</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, nrfx_gpiote.o(i.channel_free))
<BR><BR>[Called By]<UL><LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrfx_gpiote_init
</UL>

<P><STRONG><a name="[119]"></a>channel_port_alloc</STRONG> (Thumb, 66 bytes, Stack size 20 bytes, nrfx_gpiote.o(i.channel_port_alloc))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = channel_port_alloc
</UL>
<BR>[Called By]<UL><LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrfx_gpiote_in_init
</UL>

<P><STRONG><a name="[116]"></a>channel_port_get</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, nrfx_gpiote.o(i.channel_port_get))
<BR><BR>[Called By]<UL><LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrfx_gpiote_in_event_enable
<LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;port_event_handle
</UL>

<P><STRONG><a name="[11d]"></a>nrf_bitmask_bit_is_set</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, nrfx_gpiote.o(i.nrf_bitmask_bit_is_set))
<BR><BR>[Called By]<UL><LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;port_event_handle
</UL>

<P><STRONG><a name="[f3]"></a>nrf_gpio_cfg</STRONG> (Thumb, 50 bytes, Stack size 40 bytes, nrfx_gpiote.o(i.nrf_gpio_cfg))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = nrf_gpio_cfg
</UL>
<BR>[Calls]<UL><LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_gpio_pin_port_decode
</UL>
<BR>[Called By]<UL><LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrfx_gpiote_in_init
</UL>

<P><STRONG><a name="[f5]"></a>nrf_gpio_cfg_sense_set</STRONG> (Thumb, 48 bytes, Stack size 16 bytes, nrfx_gpiote.o(i.nrf_gpio_cfg_sense_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = nrf_gpio_cfg_sense_set
</UL>
<BR>[Calls]<UL><LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_gpio_pin_port_decode
</UL>
<BR>[Called By]<UL><LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrfx_gpiote_in_event_enable
<LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;port_event_handle
</UL>

<P><STRONG><a name="[5f]"></a>nrf_gpio_latches_read_and_clear</STRONG> (Thumb, 42 bytes, Stack size 20 bytes, nrfx_gpiote.o(i.nrf_gpio_latches_read_and_clear))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = nrf_gpio_latches_read_and_clear
</UL>
<BR>[Called By]<UL><LI><a href="#[10]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOTE_IRQHandler
<LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;port_event_handle
</UL>

<P><STRONG><a name="[f4]"></a>nrf_gpio_pin_port_decode</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, nrfx_gpiote.o(i.nrf_gpio_pin_port_decode))
<BR><BR>[Called By]<UL><LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrfx_gpiote_in_init
<LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;port_event_handle
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_gpio_pin_read
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_gpio_cfg_sense_set
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_gpio_cfg
</UL>

<P><STRONG><a name="[11b]"></a>nrf_gpio_pin_present_check</STRONG> (Thumb, 44 bytes, Stack size 0 bytes, nrfx_gpiote.o(i.nrf_gpio_pin_present_check))
<BR><BR>[Called By]<UL><LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrfx_gpiote_init
</UL>

<P><STRONG><a name="[f6]"></a>nrf_gpio_pin_read</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, nrfx_gpiote.o(i.nrf_gpio_pin_read))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = nrf_gpio_pin_read
</UL>
<BR>[Calls]<UL><LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_gpio_pin_port_decode
</UL>
<BR>[Called By]<UL><LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrfx_gpiote_in_is_set
<LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrfx_gpiote_in_event_enable
</UL>

<P><STRONG><a name="[5e]"></a>nrf_gpiote_event_clear</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, nrfx_gpiote.o(i.nrf_gpiote_event_clear))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = nrf_gpiote_event_clear
</UL>
<BR>[Called By]<UL><LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrfx_gpiote_init
<LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrfx_gpiote_in_event_enable
<LI><a href="#[10]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOTE_IRQHandler
</UL>

<P><STRONG><a name="[5d]"></a>nrf_gpiote_event_is_set</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, nrfx_gpiote.o(i.nrf_gpiote_event_is_set))
<BR><BR>[Called By]<UL><LI><a href="#[10]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOTE_IRQHandler
</UL>

<P><STRONG><a name="[11a]"></a>pin_configured_set</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, nrfx_gpiote.o(i.pin_configured_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = pin_configured_set
</UL>
<BR>[Called By]<UL><LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrfx_gpiote_in_init
</UL>

<P><STRONG><a name="[115]"></a>pin_in_use_by_port</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, nrfx_gpiote.o(i.pin_in_use_by_port))
<BR><BR>[Called By]<UL><LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrfx_gpiote_in_event_enable
</UL>

<P><STRONG><a name="[118]"></a>pin_in_use_by_te</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, nrfx_gpiote.o(i.pin_in_use_by_te))
<BR><BR>[Called By]<UL><LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrfx_gpiote_in_event_enable
</UL>

<P><STRONG><a name="[60]"></a>port_event_handle</STRONG> (Thumb, 202 bytes, Stack size 40 bytes, nrfx_gpiote.o(i.port_event_handle))
<BR><BR>[Stack]<UL><LI>Max Depth = 60<LI>Call Chain = port_event_handle &rArr; nrf_gpio_latches_read_and_clear
</UL>
<BR>[Calls]<UL><LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;port_handler_polarity_get
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_gpio_pin_port_decode
<LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_gpio_latches_read_and_clear
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_gpio_cfg_sense_set
<LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_bitmask_bit_is_set
<LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;channel_port_get
</UL>
<BR>[Called By]<UL><LI><a href="#[10]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOTE_IRQHandler
</UL>

<P><STRONG><a name="[117]"></a>port_handler_polarity_get</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, nrfx_gpiote.o(i.port_handler_polarity_get))
<BR><BR>[Called By]<UL><LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrfx_gpiote_in_event_enable
<LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;port_event_handle
</UL>

<P><STRONG><a name="[b9]"></a>button_get</STRONG> (Thumb, 36 bytes, Stack size 12 bytes, app_button.o(i.button_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = button_get
</UL>
<BR>[Called By]<UL><LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usr_event
<LI><a href="#[38]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpiote_event_handler
</UL>

<P><STRONG><a name="[39]"></a>detection_delay_timeout_handler</STRONG> (Thumb, 66 bytes, Stack size 16 bytes, app_button.o(i.detection_delay_timeout_handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 84<LI>Call Chain = detection_delay_timeout_handler &rArr; evt_handle &rArr; usr_event &rArr; button_get
</UL>
<BR>[Calls]<UL><LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrfx_gpiote_in_is_set
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;evt_handle
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_start
</UL>
<BR>[Address Reference Count : 1]<UL><LI> app_button.o(i.app_button_init)
</UL>
<P><STRONG><a name="[38]"></a>gpiote_event_handler</STRONG> (Thumb, 56 bytes, Stack size 8 bytes, app_button.o(i.gpiote_event_handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = gpiote_event_handler &rArr; timer_start &rArr; app_timer_start &rArr; timer_req_schedule &rArr; nrf_atfifo_item_alloc
</UL>
<BR>[Calls]<UL><LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrfx_gpiote_in_is_set
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_start
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;button_get
</UL>
<BR>[Address Reference Count : 1]<UL><LI> app_button.o(i.app_button_init)
</UL>
<P><STRONG><a name="[b5]"></a>state_set</STRONG> (Thumb, 42 bytes, Stack size 0 bytes, app_button.o(i.state_set))
<BR><BR>[Called By]<UL><LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;evt_handle
</UL>

<P><STRONG><a name="[ab]"></a>timer_start</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, app_button.o(i.timer_start))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = timer_start &rArr; app_timer_start &rArr; timer_req_schedule &rArr; nrf_atfifo_item_alloc
</UL>
<BR>[Calls]<UL><LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_timer_start
</UL>
<BR>[Called By]<UL><LI><a href="#[38]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpiote_event_handler
<LI><a href="#[39]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;detection_delay_timeout_handler
</UL>

<P><STRONG><a name="[b6]"></a>usr_event</STRONG> (Thumb, 32 bytes, Stack size 16 bytes, app_button.o(i.usr_event))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = usr_event &rArr; button_get
</UL>
<BR>[Calls]<UL><LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;button_get
</UL>
<BR>[Called By]<UL><LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;evt_handle
</UL>

<P><STRONG><a name="[45]"></a>compare_func</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, app_timer2.o(i.compare_func))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = compare_func
</UL>
<BR>[Address Reference Count : 1]<UL><LI> app_timer2.o(.constdata)
</UL>
<P><STRONG><a name="[82]"></a>get_now</STRONG> (Thumb, 46 bytes, Stack size 8 bytes, app_timer2.o(i.get_now))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = get_now
</UL>
<BR>[Calls]<UL><LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;drv_rtc_counter_get
</UL>
<BR>[Called By]<UL><LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_timer_start
<LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_expire
<LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_schedule
<LI><a href="#[3a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_irq
</UL>

<P><STRONG><a name="[3a]"></a>rtc_irq</STRONG> (Thumb, 88 bytes, Stack size 16 bytes, app_timer2.o(i.rtc_irq))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = rtc_irq &rArr; rtc_update &rArr; rtc_schedule &rArr; drv_rtc_windowed_compare_set &rArr; nrf_rtc_event_clear
</UL>
<BR>[Calls]<UL><LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;drv_rtc_overflow_pending
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;drv_rtc_compare_pending
<LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_req_process
<LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_expire
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_update
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_now
</UL>
<BR>[Address Reference Count : 1]<UL><LI> app_timer2.o(i.app_timer_init)
</UL>
<P><STRONG><a name="[122]"></a>rtc_schedule</STRONG> (Thumb, 92 bytes, Stack size 16 bytes, app_timer2.o(i.rtc_schedule))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = rtc_schedule &rArr; drv_rtc_windowed_compare_set &rArr; nrf_rtc_event_clear
</UL>
<BR>[Calls]<UL><LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;drv_rtc_windowed_compare_set
<LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;drv_rtc_compare_disable
<LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_timer_cnt_get
<LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_expire
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_now
</UL>
<BR>[Called By]<UL><LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_update
</UL>

<P><STRONG><a name="[121]"></a>rtc_update</STRONG> (Thumb, 108 bytes, Stack size 24 bytes, app_timer2.o(i.rtc_update))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = rtc_update &rArr; rtc_schedule &rArr; drv_rtc_windowed_compare_set &rArr; nrf_rtc_event_clear
</UL>
<BR>[Calls]<UL><LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_sortlist_peek
<LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_sortlist_add
<LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;drv_rtc_stop
<LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;drv_rtc_start
<LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sortlist_pop
<LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_schedule
</UL>
<BR>[Called By]<UL><LI><a href="#[3a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_irq
</UL>

<P><STRONG><a name="[126]"></a>sortlist_pop</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, app_timer2.o(i.sortlist_pop))
<BR><BR>[Calls]<UL><LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_sortlist_pop
</UL>
<BR>[Called By]<UL><LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_req_process
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_update
</UL>

<P><STRONG><a name="[11f]"></a>timer_expire</STRONG> (Thumb, 80 bytes, Stack size 16 bytes, app_timer2.o(i.timer_expire))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = timer_expire &rArr; nrf_sortlist_add
</UL>
<BR>[Calls]<UL><LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_sortlist_add
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_now
</UL>
<BR>[Called By]<UL><LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_schedule
<LI><a href="#[3a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_irq
</UL>

<P><STRONG><a name="[120]"></a>timer_req_process</STRONG> (Thumb, 110 bytes, Stack size 24 bytes, app_timer2.o(i.timer_req_process))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = timer_req_process &rArr; nrf_sortlist_add
</UL>
<BR>[Calls]<UL><LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_sortlist_remove
<LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_sortlist_add
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_atfifo_item_get
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_atfifo_item_free
<LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sortlist_pop
</UL>
<BR>[Called By]<UL><LI><a href="#[3a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_irq
</UL>

<P><STRONG><a name="[83]"></a>timer_req_schedule</STRONG> (Thumb, 46 bytes, Stack size 24 bytes, app_timer2.o(i.timer_req_schedule))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = timer_req_schedule &rArr; nrf_atfifo_item_alloc
</UL>
<BR>[Calls]<UL><LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_atfifo_item_put
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_atfifo_item_alloc
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;drv_rtc_irq_trigger
</UL>
<BR>[Called By]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_timer_stop
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_timer_start
</UL>

<P><STRONG><a name="[b1]"></a>evt_enable</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, drv_rtc.o(i.evt_enable))
<BR><BR>[Called By]<UL><LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;drv_rtc_windowed_compare_set
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;drv_rtc_overflow_enable
</UL>

<P><STRONG><a name="[af]"></a>evt_pending</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, drv_rtc.o(i.evt_pending))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = evt_pending &rArr; nrf_rtc_event_clear
</UL>
<BR>[Calls]<UL><LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_rtc_event_clear
</UL>
<BR>[Called By]<UL><LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;drv_rtc_overflow_pending
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;drv_rtc_compare_pending
</UL>

<P><STRONG><a name="[b0]"></a>nrf_rtc_event_clear</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, drv_rtc.o(i.nrf_rtc_event_clear))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = nrf_rtc_event_clear
</UL>
<BR>[Called By]<UL><LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;drv_rtc_windowed_compare_set
<LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;drv_rtc_compare_set
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;evt_pending
</UL>

<P><STRONG><a name="[b4]"></a>nrfx_coredep_delay_us</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, drv_rtc.o(i.nrfx_coredep_delay_us))
<BR><BR>[Called By]<UL><LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;drv_rtc_windowed_compare_set
</UL>

<P><STRONG><a name="[a3]"></a>buffer_add</STRONG> (Thumb, 46 bytes, Stack size 16 bytes, nrf_fprintf_format.o(i.buffer_add))
<BR><BR>[Stack]<UL><LI>Max Depth = 24 + In Cycle
<LI>Call Chain = buffer_add &rArr;  buffer_add (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;buffer_add
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_fprintf_buffer_flush
</UL>
<BR>[Called By]<UL><LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;unsigned_print
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;int_print
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;buffer_add
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_fprintf_fmt
</UL>

<P><STRONG><a name="[ba]"></a>int_print</STRONG> (Thumb, 166 bytes, Stack size 48 bytes, nrf_fprintf_format.o(i.int_print))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = int_print &rArr; unsigned_print &rArr; buffer_add &rArr;  buffer_add (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;unsigned_print
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;buffer_add
</UL>
<BR>[Called By]<UL><LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_fprintf_fmt
</UL>

<P><STRONG><a name="[bb]"></a>unsigned_print</STRONG> (Thumb, 176 bytes, Stack size 40 bytes, nrf_fprintf_format.o(i.unsigned_print))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = unsigned_print &rArr; buffer_add &rArr;  buffer_add (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;buffer_add
</UL>
<BR>[Called By]<UL><LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;int_print
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_fprintf_fmt
</UL>

<P><STRONG><a name="[c8]"></a>memobj_op</STRONG> (Thumb, 126 bytes, Stack size 32 bytes, nrf_memobj.o(i.memobj_op))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = memobj_op
</UL>
<BR>[Calls]<UL><LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_memobj_write
<LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_memobj_read
</UL>

<P><STRONG><a name="[10d]"></a>shutdown_process</STRONG> (Thumb, 104 bytes, Stack size 16 bytes, nrf_pwr_mgmt.o(i.shutdown_process))
<BR><BR>[Stack]<UL><LI>Max Depth = 180<LI>Call Chain = shutdown_process &rArr; nrf_log_frontend_dequeue &rArr;  nrf_log_frontend_std_0 (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_frontend_dequeue
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_sdh_is_enabled
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_panic
<LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_section_iter_next
</UL>
<BR>[Called By]<UL><LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_pwr_mgmt_shutdown
</UL>

<P><STRONG><a name="[113]"></a>nrf_section_iter_item_set</STRONG> (Thumb, 36 bytes, Stack size 8 bytes, nrf_section_iter.o(i.nrf_section_iter_item_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = nrf_section_iter_item_set
</UL>
<BR>[Called By]<UL><LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_section_iter_next
</UL>

<P><STRONG><a name="[48]"></a>nrf_log_backend_rtt_flush</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, nrf_log_backend_rtt.o(i.nrf_log_backend_rtt_flush))
<BR>[Address Reference Count : 1]<UL><LI> nrf_log_backend_rtt.o(.constdata)
</UL>
<P><STRONG><a name="[47]"></a>nrf_log_backend_rtt_panic_set</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, nrf_log_backend_rtt.o(i.nrf_log_backend_rtt_panic_set))
<BR>[Address Reference Count : 1]<UL><LI> nrf_log_backend_rtt.o(.constdata)
</UL>
<P><STRONG><a name="[46]"></a>nrf_log_backend_rtt_put</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, nrf_log_backend_rtt.o(i.nrf_log_backend_rtt_put))
<BR><BR>[Stack]<UL><LI>Max Depth = 352<LI>Call Chain = nrf_log_backend_rtt_put &rArr; nrf_log_backend_serial_put &rArr; nrf_log_std_entry_process &rArr; prefix_process &rArr; nrf_fprintf &rArr; nrf_fprintf_fmt &rArr; int_print &rArr; unsigned_print &rArr; buffer_add &rArr;  buffer_add (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_backend_serial_put
</UL>
<BR>[Address Reference Count : 1]<UL><LI> nrf_log_backend_rtt.o(.constdata)
</UL>
<P><STRONG><a name="[41]"></a>serial_tx</STRONG> (Thumb, 70 bytes, Stack size 24 bytes, nrf_log_backend_rtt.o(i.serial_tx))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = serial_tx &rArr; SEGGER_RTT_WriteNoLock &rArr; _WriteBlocking
</UL>
<BR>[Calls]<UL><LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SEGGER_RTT_WriteNoLock
</UL>
<BR>[Address Reference Count : 1]<UL><LI> nrf_log_backend_rtt.o(i.nrf_log_backend_rtt_put)
</UL>
<P><STRONG><a name="[9e]"></a>buf_prealloc</STRONG> (Thumb, 140 bytes, Stack size 40 bytes, nrf_log_frontend.o(i.buf_prealloc))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = buf_prealloc &rArr; log_skip &rArr; nrf_atomic_flag_set &rArr; nrf_atomic_u32_or
</UL>
<BR>[Calls]<UL><LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_util_critical_region_exit
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_util_critical_region_enter
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_atomic_u32_add
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;log_skip
</UL>
<BR>[Called By]<UL><LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;std_n
</UL>

<P><STRONG><a name="[ac]"></a>dropped_sat16_get</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, nrf_log_frontend.o(i.dropped_sat16_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = dropped_sat16_get &rArr; nrf_atomic_u32_fetch_store
</UL>
<BR>[Calls]<UL><LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_atomic_u32_fetch_store
</UL>
<BR>[Called By]<UL><LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;std_n
</UL>

<P><STRONG><a name="[bd]"></a>invalid_packets_omit</STRONG> (Thumb, 52 bytes, Stack size 0 bytes, nrf_log_frontend.o(i.invalid_packets_omit))
<BR><BR>[Called By]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_frontend_dequeue
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;log_skip
</UL>

<P><STRONG><a name="[a1]"></a>log_skip</STRONG> (Thumb, 150 bytes, Stack size 40 bytes, nrf_log_frontend.o(i.log_skip))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = log_skip &rArr; nrf_atomic_flag_set &rArr; nrf_atomic_u32_or
</UL>
<BR>[Calls]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_atomic_flag_set
<LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_atomic_flag_clear_fetch
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;invalid_packets_omit
</UL>
<BR>[Called By]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;buf_prealloc
</UL>

<P><STRONG><a name="[c9]"></a>module_idx_get</STRONG> (Thumb, 42 bytes, Stack size 12 bytes, nrf_log_frontend.o(i.module_idx_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = module_idx_get
</UL>
<BR>[Calls]<UL><LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_module_cnt_get
</UL>
<BR>[Called By]<UL><LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_module_name_get
</UL>

<P><STRONG><a name="[102]"></a>std_n</STRONG> (Thumb, 140 bytes, Stack size 40 bytes, nrf_log_frontend.o(i.std_n))
<BR><BR>[Stack]<UL><LI>Max Depth = 204<LI>Call Chain = std_n &rArr; nrf_log_frontend_dequeue &rArr;  nrf_log_frontend_std_0 (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_frontend_dequeue
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dropped_sat16_get
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;buf_prealloc
</UL>
<BR>[Called By]<UL><LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_frontend_std_6
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_frontend_std_2
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_frontend_std_0
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_frontend_std_1
</UL>

<P><STRONG><a name="[106]"></a>postfix_process</STRONG> (Thumb, 48 bytes, Stack size 8 bytes, nrf_log_str_formatter.o(i.postfix_process))
<BR><BR>[Stack]<UL><LI>Max Depth = 184<LI>Call Chain = postfix_process &rArr; nrf_fprintf &rArr; nrf_fprintf_fmt &rArr; int_print &rArr; unsigned_print &rArr; buffer_add &rArr;  buffer_add (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_fprintf_buffer_flush
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_fprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_std_entry_process
<LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_hexdump_entry_process
</UL>

<P><STRONG><a name="[104]"></a>prefix_process</STRONG> (Thumb, 90 bytes, Stack size 16 bytes, nrf_log_str_formatter.o(i.prefix_process))
<BR><BR>[Stack]<UL><LI>Max Depth = 192<LI>Call Chain = prefix_process &rArr; nrf_fprintf &rArr; nrf_fprintf_fmt &rArr; int_print &rArr; unsigned_print &rArr; buffer_add &rArr;  buffer_add (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_fprintf
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_module_name_get
<LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_color_id_get
</UL>
<BR>[Called By]<UL><LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_std_entry_process
<LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_hexdump_entry_process
</UL>

<P><STRONG><a name="[64]"></a>_DoInit</STRONG> (Thumb, 76 bytes, Stack size 8 bytes, segger_rtt.o(i._DoInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = _DoInit
</UL>
<BR>[Calls]<UL><LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SEGGER_RTT_Init
</UL>

<P><STRONG><a name="[66]"></a>_GetAvailWriteSpace</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, segger_rtt.o(i._GetAvailWriteSpace))
<BR><BR>[Called By]<UL><LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SEGGER_RTT_WriteNoLock
</UL>

<P><STRONG><a name="[68]"></a>_WriteBlocking</STRONG> (Thumb, 90 bytes, Stack size 32 bytes, segger_rtt.o(i._WriteBlocking))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = _WriteBlocking
</UL>
<BR>[Calls]<UL><LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SEGGER_RTT_WriteNoLock
</UL>

<P><STRONG><a name="[67]"></a>_WriteNoCheck</STRONG> (Thumb, 66 bytes, Stack size 24 bytes, segger_rtt.o(i._WriteNoCheck))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = _WriteNoCheck
</UL>
<BR>[Calls]<UL><LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SEGGER_RTT_WriteNoLock
</UL>

<P><STRONG><a name="[12a]"></a>__sd_nvic_app_accessible_irq</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, nrf_sdh.o(i.__sd_nvic_app_accessible_irq))
<BR><BR>[Called By]<UL><LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;softdevices_evt_irq_enable
</UL>

<P><STRONG><a name="[110]"></a>sdh_request_observer_notify</STRONG> (Thumb, 44 bytes, Stack size 24 bytes, nrf_sdh.o(i.sdh_request_observer_notify))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = sdh_request_observer_notify &rArr; nrf_section_iter_next &rArr; nrf_section_iter_item_set
</UL>
<BR>[Calls]<UL><LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_section_iter_init
<LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_section_iter_next
</UL>
<BR>[Called By]<UL><LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_sdh_enable_request
</UL>

<P><STRONG><a name="[111]"></a>sdh_state_observer_notify</STRONG> (Thumb, 38 bytes, Stack size 24 bytes, nrf_sdh.o(i.sdh_state_observer_notify))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = sdh_state_observer_notify &rArr; nrf_section_iter_next &rArr; nrf_section_iter_item_set
</UL>
<BR>[Calls]<UL><LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_section_iter_init
<LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_section_iter_next
</UL>
<BR>[Called By]<UL><LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_sdh_enable_request
</UL>

<P><STRONG><a name="[112]"></a>softdevices_evt_irq_enable</STRONG> (Thumb, 80 bytes, Stack size 8 bytes, nrf_sdh.o(i.softdevices_evt_irq_enable))
<BR><BR>[Stack]<UL><LI>Max Depth = 228<LI>Call Chain = softdevices_evt_irq_enable &rArr; app_error_handler_bare &rArr; app_error_fault_handler &rArr; nrf_log_frontend_std_0 &rArr; std_n &rArr; nrf_log_frontend_dequeue &rArr;  nrf_log_frontend_std_0 (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_error_handler_bare
<LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__sd_nvic_app_accessible_irq
</UL>
<BR>[Called By]<UL><LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_sdh_enable_request
</UL>

<P><STRONG><a name="[4f]"></a>nrf_sdh_ble_evts_poll</STRONG> (Thumb, 88 bytes, Stack size 528 bytes, nrf_sdh_ble.o(i.nrf_sdh_ble_evts_poll))
<BR><BR>[Stack]<UL><LI>Max Depth = 748<LI>Call Chain = nrf_sdh_ble_evts_poll &rArr; app_error_handler_bare &rArr; app_error_fault_handler &rArr; nrf_log_frontend_std_0 &rArr; std_n &rArr; nrf_log_frontend_dequeue &rArr;  nrf_log_frontend_std_0 (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_error_handler_bare
<LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_section_iter_init
<LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_section_iter_next
</UL>
<BR>[Address Reference Count : 1]<UL><LI> nrf_sdh_ble.o(sdh_stack_observers0)
</UL>
<P><STRONG><a name="[50]"></a>nrf_sdh_soc_evts_poll</STRONG> (Thumb, 56 bytes, Stack size 24 bytes, nrf_sdh_soc.o(i.nrf_sdh_soc_evts_poll))
<BR><BR>[Stack]<UL><LI>Max Depth = 244<LI>Call Chain = nrf_sdh_soc_evts_poll &rArr; app_error_handler_bare &rArr; app_error_fault_handler &rArr; nrf_log_frontend_std_0 &rArr; std_n &rArr; nrf_log_frontend_dequeue &rArr;  nrf_log_frontend_std_0 (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_error_handler_bare
<LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_section_iter_init
<LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_section_iter_next
</UL>
<BR>[Address Reference Count : 1]<UL><LI> nrf_sdh_soc.o(sdh_stack_observers0)
</UL><P>
<H3>
Undefined Global Symbols
</H3><HR></body></html>
