<!DOCTYPE CrossStudio_Project_File>
<solution Name="gzll_ack_payload_device_pca10040" target="8" version="2">
  <project Name="gzll_ack_payload_device_pca10040">
    <configuration
      Name="Common"
      arm_architecture="v7EM"
      arm_core_type="Cortex-M4"
      arm_endian="Little"
      arm_fp_abi="Hard"
      arm_fpu_type="FPv4-SP-D16"
      arm_linker_heap_size="8192"
      arm_linker_process_stack_size="0"
      arm_linker_stack_size="8192"
      arm_linker_treat_warnings_as_errors="No"
      arm_simulator_memory_simulation_parameter="RWX 00000000,00100000,FFFFFFFF;RWX 20000000,00010000,CDCDCDCD"
      arm_target_device_name="nRF52832_xxAA"
      arm_target_interface_type="SWD"
      c_user_include_directories="../../../config;../../../../../../../../components;../../../../../../../../components/boards;../../../../../../../../components/drivers_nrf/nrf_soc_nosd;../../../../../../../../components/libraries/atomic;../../../../../../../../components/libraries/atomic_fifo;../../../../../../../../components/libraries/balloc;../../../../../../../../components/libraries/bsp;../../../../../../../../components/libraries/button;../../../../../../../../components/libraries/delay;../../../../../../../../components/libraries/experimental_section_vars;../../../../../../../../components/libraries/log;../../../../../../../../components/libraries/log/src;../../../../../../../../components/libraries/memobj;../../../../../../../../components/libraries/ringbuf;../../../../../../../../components/libraries/scheduler;../../../../../../../../components/libraries/sortlist;../../../../../../../../components/libraries/strerror;../../../../../../../../components/libraries/timer;../../../../../../../../components/libraries/util;../../../../../../../../components/proprietary_rf/gzll;../../../../../../../../components/toolchain/cmsis/include;../../..;../../../../../../../../external/fprintf;../../../../../../../../external/segger_rtt;../../../../../../../../integration/nrfx;../../../../../../../../integration/nrfx/legacy;../../../../../../../../modules/nrfx;../../../../../../../../modules/nrfx/drivers/include;../../../../../../../../modules/nrfx/hal;../../../../../../../../modules/nrfx/mdk;../config;"
      c_preprocessor_definitions="APP_TIMER_V2;APP_TIMER_V2_RTC1_ENABLED;BOARD_PCA10040;CONFIG_GPIO_AS_PINRESET;FLOAT_ABI_HARD;GAZELL_ALTERNATIVE_RESOURCES;GAZELL_PRESENT;INITIALIZE_USER_SECTIONS;NO_VTOR_CONFIG;NRF52;NRF52832_XXAA;NRF52_PAN_74;USE_SD_HW_RESOURCES;"
      debug_target_connection="J-Link"
      gcc_entry_point="Reset_Handler"
      macros="CMSIS_CONFIG_TOOL=../../../../../../../../external_tools/cmsisconfig/CMSIS_Configuration_Wizard.jar"
      debug_register_definition_file="../../../../../../../../modules/nrfx/mdk/nrf52.svd"
      debug_start_from_entry_point_symbol="No"
      gcc_debugging_level="Level 3"      linker_output_format="hex"
      linker_printf_width_precision_supported="Yes"
      linker_printf_fmt_level="long"
      linker_scanf_fmt_level="long"
      linker_section_placement_file="flash_placement.xml"
      linker_section_placement_macros="FLASH_PH_START=0x0;FLASH_PH_SIZE=0x80000;RAM_PH_START=0x20000000;RAM_PH_SIZE=0x10000;FLASH_START=0x0;FLASH_SIZE=0x80000;RAM_START=0x20000000;RAM_SIZE=0x10000"
      
      linker_section_placements_segments="FLASH RX 0x0 0x80000;RAM RWX 0x20000000 0x10000"
      project_directory=""
      project_type="Executable" />
      <folder Name="Segger Startup Files">
        <file file_name="$(StudioDir)/source/thumb_crt0.s" />
      </folder>
    <folder Name="nRF_Log">
      <file file_name="../../../../../../../../components/libraries/log/src/nrf_log_backend_rtt.c" />
      <file file_name="../../../../../../../../components/libraries/log/src/nrf_log_backend_serial.c" />
      <file file_name="../../../../../../../../components/libraries/log/src/nrf_log_backend_uart.c" />
      <file file_name="../../../../../../../../components/libraries/log/src/nrf_log_default_backends.c" />
      <file file_name="../../../../../../../../components/libraries/log/src/nrf_log_frontend.c" />
      <file file_name="../../../../../../../../components/libraries/log/src/nrf_log_str_formatter.c" />
    </folder>
    <folder Name="Board Definition">
      <file file_name="../../../../../../../../components/boards/boards.c" />
    </folder>
    <folder Name="nRF_Libraries">
      <file file_name="../../../../../../../../components/libraries/button/app_button.c" />
      <file file_name="../../../../../../../../components/libraries/util/app_error.c" />
      <file file_name="../../../../../../../../components/libraries/util/app_error_handler_gcc.c" />
      <file file_name="../../../../../../../../components/libraries/util/app_error_weak.c" />
      <file file_name="../../../../../../../../components/libraries/scheduler/app_scheduler.c" />
      <file file_name="../../../../../../../../components/libraries/timer/app_timer2.c" />
      <file file_name="../../../../../../../../components/libraries/util/app_util_platform.c" />
      <file file_name="../../../../../../../../components/libraries/timer/drv_rtc.c" />
      <file file_name="../../../../../../../../components/libraries/util/nrf_assert.c" />
      <file file_name="../../../../../../../../components/libraries/atomic_fifo/nrf_atfifo.c" />
      <file file_name="../../../../../../../../components/libraries/atomic/nrf_atomic.c" />
      <file file_name="../../../../../../../../components/libraries/balloc/nrf_balloc.c" />
      <file file_name="../../../../../../../../external/fprintf/nrf_fprintf.c" />
      <file file_name="../../../../../../../../external/fprintf/nrf_fprintf_format.c" />
      <file file_name="../../../../../../../../components/libraries/memobj/nrf_memobj.c" />
      <file file_name="../../../../../../../../components/libraries/ringbuf/nrf_ringbuf.c" />
      <file file_name="../../../../../../../../components/libraries/sortlist/nrf_sortlist.c" />
      <file file_name="../../../../../../../../components/libraries/strerror/nrf_strerror.c" />
    </folder>
    <folder Name="nRF_Drivers">
      <file file_name="../../../../../../../../integration/nrfx/legacy/nrf_drv_uart.c" />
      <file file_name="../../../../../../../../modules/nrfx/soc/nrfx_atomic.c" />
      <file file_name="../../../../../../../../modules/nrfx/drivers/src/nrfx_gpiote.c" />
      <file file_name="../../../../../../../../modules/nrfx/drivers/src/prs/nrfx_prs.c" />
      <file file_name="../../../../../../../../modules/nrfx/drivers/src/nrfx_uart.c" />
      <file file_name="../../../../../../../../modules/nrfx/drivers/src/nrfx_uarte.c" />
    </folder>
    <folder Name="Board Support">
      <file file_name="../../../../../../../../components/libraries/bsp/bsp.c" />
    </folder>
    <folder Name="Application">
      <file file_name="../../../main.c" />
      <file file_name="../config/sdk_config.h" />
    </folder>
    <folder Name="nRF_Segger_RTT">
      <file file_name="../../../../../../../../external/segger_rtt/SEGGER_RTT.c" />
      <file file_name="../../../../../../../../external/segger_rtt/SEGGER_RTT_Syscalls_SES.c" />
      <file file_name="../../../../../../../../external/segger_rtt/SEGGER_RTT_printf.c" />
    </folder>
    <folder Name="None">
      <file file_name="../../../../../../../../modules/nrfx/mdk/ses_startup_nrf52.s" />
      <file file_name="../../../../../../../../modules/nrfx/mdk/ses_startup_nrf_common.s" />
      <file file_name="../../../../../../../../modules/nrfx/mdk/system_nrf52.c" />
    </folder>
    <folder Name="nRF_Properitary_RF">
      <file file_name="../../../../../../../../components/proprietary_rf/gzll/gcc/gzll_nrf52_sd_resources_gcc.a" />
    </folder>
  </project>
  <configuration Name="Release"
    c_preprocessor_definitions="NDEBUG"
    link_time_optimization="No"    gcc_optimization_level="Optimize For Size" />
  <configuration Name="Debug"
    c_preprocessor_definitions="DEBUG; DEBUG_NRF"
    gcc_optimization_level="None"/>

</solution>
