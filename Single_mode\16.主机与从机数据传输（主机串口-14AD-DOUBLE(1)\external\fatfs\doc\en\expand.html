<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html lang="en">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<meta http-equiv="Content-Style-Type" content="text/css">
<link rel="up" title="FatFs" href="../00index_e.html">
<link rel="alternate" hreflang="ja" title="Japanese" href="../ja/lseek.html">
<link rel="stylesheet" href="../css_e.css" type="text/css" media="screen" title="ELM Default">
<title>FatFs - f_expand</title>
</head>

<body>

<div class="para func">
<h2>f_expand</h2>
<p>The f_expand function prepares or allocates a contiguous data area to the file.</p>

<pre>
FRESULT f_expand (
  FIL*    <span class="arg">fp</span>,  <span class="c">/* [IN] File object */</span>
  FSIZE_t <span class="arg">fsz</span>, <span class="c">/* [IN] File size expanded to */</span>
  BYTE    <span class="arg">opt</span>  <span class="c">/* [IN] Operation mode */</span>
);
</pre>
</div>

<div class="para arg">
<h4>Parameters</h4>
<dl class="par">
<dt>fp</dt>
<dd>Pointer to the open file object.</dd>
<dt>fsz</dt>
<dd>Number of bytes in size to prepare or allocate for the file. The data type <tt>FSIZE_t</tt> is an alias of either <tt>DWORD</tt>(32-bit) or <tt>QWORD</tt>(64-bit) depends on the configuration option <tt>_FS_EXFAT</tt>.</dd>
<dt>opt</dt>
<dd>Operation mode. Prepare only (0) or Allocate now (1).</dd>
</dl>
</div>


<div class="para ret">
<h4>Return Values</h4>
<p>
<a href="rc.html#ok">FR_OK</a>,
<a href="rc.html#de">FR_DISK_ERR</a>,
<a href="rc.html#ie">FR_INT_ERR</a>,
<a href="rc.html#io">FR_INVALID_OBJECT</a>,
<a href="rc.html#dn">FR_DENIED</a>,
<a href="rc.html#tm">FR_TIMEOUT</a>
</p>
</div>


<div class="para desc">
<h4>Description</h4>
<p>The <tt>f_expand</tt> function prepares or allocates a contiguous data area to the file. When <tt class="arg">opt</tt> is 1, the function allocates a contiguous data area to the file. Unlike expansion of file by <tt>f_lseek</tt> function, the file must be truncated prior to use this function and read/write pointer of the file stays at top of the file after the function. The file content allocated with this function is <em>undefined</em> because no data is written to the file in this process. The function can fail with <tt>FR_DENIED</tt> due to some reasons below.</p>
<ul>
<li>No free contiguous space was found.</li>
<li>Size of the file was not zero.</li>
<li>The file has been opened in read-only mode.</li>
<li>Not allowable file size. (&gt;= 4GiB on FAT volume)</li>
</ul>
<p>When <tt class="arg">opt</tt> is 0, the function finds a contiguous data area and set it as suggested point for next allocation instead of allocating it to the file. The next cluster allocation is started at top of the contiguous area found by this function. Thus the write file is guaranteed be contiguous and no allocation delay until the size reaches that size at least unless any other changes to the volume is performed.</p>
<p>The contiguous file would have an advantage at time-critical read/write operations. It reduces some overheads in the file system and the storage media caused by random access due to fragmented file data. Especially, at the exFAT volume, any FAT access for the contiguous file is completely eliminated and storage media will be accessed sequentially.</p>
<p>Also the contiguous file data can be easily accessed directly via low-level disk functions but it is not recommended in consideration for future compatibility.</p>
</div>

<div class="para comp">
<h4>QuickInfo</h4>
<p>Available when <tt>_USE_EXPAND == 1</tt> and <tt>_FS_READONLY == 0</tt>.</p>
</div>


<div class="para use">
<h4>Example</h4>
<pre>
    <span class="c">/* Creating a contiguous file */</span>

    <span class="c">/* Create a new file */</span>
    res = f_open(fp = malloc(sizeof (FIL)), "file.dat", FA_WRITE|FA_CREATE_ALWAYS);
    if (res) { <span class="c">/* Check if the file has been opened */</span>
        free(fp);
        ...
    }

    <span class="c">/* Alloacte a 100 MiB of contiguous area to the file */</span>
    res = f_expand(fp, 104857600, 1);
    if (res) { <span class="c">/* Check if the file has been expanded */</span>
        ...
        free(fp);
        ...
    }
    <span class="c">/* Now you have a contiguous file accessible with fp */</span>

</pre>
<pre>
    <span class="c">/* Accessing the file data directly via low-level disk functions */</span>

    <span class="c">/* Get physical location of the file data */</span>
    drv = fp-&gt;obj.fs-&gt;drv;
    sect = fp-&gt;obj.fs-&gt;database + fp-&gt;obj.fs-&gt;csize * (fp-&gt;obj.sclust - 2);

    <span class="c">/* Write 2048 sectors from top of the file at a time */</span>
    res = disk_write(drv, buffer, sect, 2048);

</pre>
</div>


<div class="para ref">
<h4>See Also</h4>
<p><tt><a href="open.html">f_open</a>, <a href="lseek.html">f_lseek</a>, <a href="sfile.html">FIL</a></tt></p>
</div>

<p class="foot"><a href="../00index_e.html">Return</a></p>
</body>
</html>
