/*###ICF### Section handled by ICF editor, don't touch! ****/
/*-Editor annotation file-*/
/* IcfEditorFile="$TOOLKIT_DIR$\config\ide\IcfEditor\cortex_v1_0.xml" */
/*-Specials-*/
define symbol __ICFEDIT_intvec_start__ = 0xf1000;
/*-Memory Regions-*/
define symbol __ICFEDIT_region_ROM_start__   = 0xf1000;
define symbol __ICFEDIT_region_ROM_end__     = 0xfdfff;
define symbol __ICFEDIT_region_RAM_start__   = 0x20005968;
define symbol __ICFEDIT_region_RAM_end__     = 0x2003ffff;
export symbol __ICFEDIT_region_RAM_start__;
export symbol __ICFEDIT_region_RAM_end__;
/*-Sizes-*/
define symbol __ICFEDIT_size_cstack__   = 2048;
define symbol __ICFEDIT_size_heap__     = 0;
/**** End of ICF editor section. ###ICF###*/

define memory mem with size = 4G;
define region ROM_region   = mem:[from __ICFEDIT_region_ROM_start__   to __ICFEDIT_region_ROM_end__];
define region RAM_region   = mem:[from __ICFEDIT_region_RAM_start__   to __ICFEDIT_region_RAM_end__];

define symbol __region_uicr_bootloader_start_address_start = 0x10001014;
define symbol __region_uicr_bootloader_start_address_length = 0x4;
define symbol __start_uicr_bootloader_start_address = __region_uicr_bootloader_start_address_start;
define symbol __stop_uicr_bootloader_start_address = __region_uicr_bootloader_start_address_start + __region_uicr_bootloader_start_address_length;
export symbol __start_uicr_bootloader_start_address;
export symbol __stop_uicr_bootloader_start_address;
define symbol __region_mbr_params_page_start = 0x000FE000;
define symbol __region_mbr_params_page_length = 0x1000;
define symbol __start_mbr_params_page = __region_mbr_params_page_start;
define symbol __stop_mbr_params_page = __region_mbr_params_page_start + __region_mbr_params_page_length;
export symbol __start_mbr_params_page;
export symbol __stop_mbr_params_page;
define symbol __region_bootloader_settings_page_start = 0x000FF000;
define symbol __region_bootloader_settings_page_length = 0x1000;
define symbol __start_bootloader_settings_page = __region_bootloader_settings_page_start;
define symbol __stop_bootloader_settings_page = __region_bootloader_settings_page_start + __region_bootloader_settings_page_length;
export symbol __start_bootloader_settings_page;
export symbol __stop_bootloader_settings_page;
define symbol __region_uicr_mbr_params_page_start = 0x10001018;
define symbol __region_uicr_mbr_params_page_length = 0x4;
define symbol __start_uicr_mbr_params_page = __region_uicr_mbr_params_page_start;
define symbol __stop_uicr_mbr_params_page = __region_uicr_mbr_params_page_start + __region_uicr_mbr_params_page_length;
export symbol __start_uicr_mbr_params_page;
export symbol __stop_uicr_mbr_params_page;

define block CSTACK    with alignment = 8, size = __ICFEDIT_size_cstack__   { };
define block HEAP      with alignment = 8, size = __ICFEDIT_size_heap__     { };
define block RO_END    with alignment = 8, size = 0     { };

initialize by copy { readwrite };
do not initialize  { section .noinit };

keep { section .intvec };
place at address mem:__ICFEDIT_intvec_start__ { readonly section .intvec };
place in ROM_region   { readonly,
                        block RO_END };
place in RAM_region   { readwrite,
                        block CSTACK,
                        block HEAP };

