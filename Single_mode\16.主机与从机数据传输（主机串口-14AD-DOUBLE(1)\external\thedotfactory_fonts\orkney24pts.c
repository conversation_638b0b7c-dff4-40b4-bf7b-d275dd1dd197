/* Font copyrights:
 *
 * Copyright (c) 2015,
 * <PERSON> (https://behance.net/pradil),
 * <PERSON> (http://oakes.co/),
 * <PERSON><PERSON><PERSON> (https://www.behance.net/cssobral20f492),
 * with Reserved Font Name Orkney.
 *
 * Code was generated by The Dot Factory (https://github.com/pavius/the-dot-factory)
 *
 * This Font Software is licensed under the SIL Open Font License, Version 1.1. See SIL-License.txt
 * for more informations.
 *
 */

#include "nrf_font.h"
// Character bitmaps for Orkney 24pt
const uint8_t orkney_24ptBitmaps[] =
{
    // @0 '!' (4 pixels wide)
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0xF0, // ####
    0xF0, // ####
    0xF0, // ####
    0xF0, // ####
    0xF0, // ####
    0xF0, // ####
    0xF0, // ####
    0xF0, // ####
    0xF0, // ####
    0xF0, // ####
    0xF0, // ####
    0xF0, // ####
    0xE0, // ###
    0xE0, // ###
    0xE0, // ###
    0xE0, // ###
    0xE0, // ###
    0xE0, // ###
    0xE0, // ###
    0x60, //  ##
    0x60, //  ##
    0x60, //  ##
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x60, //  ##
    0xF0, // ####
    0xF0, // ####
    0xF0, // ####
    0x60, //  ##
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //

    // @47 '"' (10 pixels wide)
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0xE3, 0xC0, // ###   ####
    0xE1, 0xC0, // ###    ###
    0xE1, 0xC0, // ###    ###
    0xE1, 0xC0, // ###    ###
    0xE1, 0xC0, // ###    ###
    0xE1, 0xC0, // ###    ###
    0xE1, 0xC0, // ###    ###
    0xE1, 0xC0, // ###    ###
    0xE1, 0xC0, // ###    ###
    0xE1, 0xC0, // ###    ###
    0xE1, 0x80, // ###    ##
    0xE1, 0x80, // ###    ##
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //

    // @141 '#' (27 pixels wide)
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x70, 0x1C, 0x00, //          ###       ###
    0x00, 0x70, 0x1C, 0x00, //          ###       ###
    0x00, 0x60, 0x18, 0x00, //          ##        ##
    0x00, 0x60, 0x18, 0x00, //          ##        ##
    0x00, 0x60, 0x18, 0x00, //          ##        ##
    0x00, 0xE0, 0x38, 0x00, //         ###       ###
    0x00, 0xE0, 0x38, 0x00, //         ###       ###
    0x1F, 0xFF, 0xFF, 0xE0, //    ########################
    0x3F, 0xFF, 0xFF, 0xC0, //   ########################
    0x3F, 0xFF, 0xFF, 0xC0, //   ########################
    0x00, 0xC0, 0x30, 0x00, //         ##        ##
    0x00, 0xC0, 0x30, 0x00, //         ##        ##
    0x01, 0xC0, 0x30, 0x00, //        ###        ##
    0x01, 0xC0, 0x70, 0x00, //        ###       ###
    0x01, 0xC0, 0x70, 0x00, //        ###       ###
    0x01, 0xC0, 0x70, 0x00, //        ###       ###
    0x01, 0x80, 0x70, 0x00, //        ##        ###
    0x01, 0x80, 0x60, 0x00, //        ##        ##
    0x01, 0x80, 0x60, 0x00, //        ##        ##
    0x7F, 0xFF, 0xFF, 0x80, //  ########################
    0x7F, 0xFF, 0xFF, 0x80, //  ########################
    0xFF, 0xFF, 0xFF, 0x80, // #########################
    0x03, 0x80, 0xE0, 0x00, //       ###       ###
    0x03, 0x80, 0xE0, 0x00, //       ###       ###
    0x03, 0x00, 0xE0, 0x00, //       ##        ###
    0x03, 0x00, 0xC0, 0x00, //       ##        ##
    0x03, 0x00, 0xC0, 0x00, //       ##        ##
    0x07, 0x01, 0xC0, 0x00, //      ###       ###
    0x07, 0x01, 0xC0, 0x00, //      ###       ###
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //

    // @329 '$' (20 pixels wide)
    0x00, 0x00, 0x00, //
    0x00, 0x70, 0x00, //          ###
    0x00, 0x70, 0x00, //          ###
    0x00, 0x70, 0x00, //          ###
    0x00, 0xF0, 0x00, //         ####
    0x07, 0xFE, 0x00, //      ##########
    0x0F, 0xFF, 0x00, //     ############
    0x1F, 0x07, 0x80, //    #####     ####
    0x3C, 0x03, 0xC0, //   ####        ####
    0x38, 0x01, 0x00, //   ###          #
    0x78, 0x00, 0x00, //  ####
    0x70, 0x00, 0x00, //  ###
    0x70, 0x00, 0x00, //  ###
    0x78, 0x00, 0x00, //  ####
    0x78, 0x00, 0x00, //  ####
    0x3C, 0x00, 0x00, //   ####
    0x3E, 0x00, 0x00, //   #####
    0x1F, 0x80, 0x00, //    ######
    0x0F, 0xF0, 0x00, //     ########
    0x03, 0xFC, 0x00, //       ########
    0x00, 0xFF, 0x00, //         ########
    0x00, 0x3F, 0x80, //           #######
    0x00, 0x07, 0xC0, //              #####
    0x00, 0x01, 0xE0, //                ####
    0x00, 0x00, 0xE0, //                 ###
    0x00, 0x00, 0xF0, //                 ####
    0x00, 0x00, 0x70, //                  ###
    0x00, 0x00, 0x70, //                  ###
    0x20, 0x00, 0x70, //   #              ###
    0x60, 0x00, 0xF0, //  ##             ####
    0xF0, 0x00, 0xE0, // ####            ###
    0x78, 0x01, 0xE0, //  ####          ####
    0x3E, 0x07, 0xC0, //   #####      #####
    0x1F, 0xFF, 0x80, //    ##############
    0x0F, 0xFF, 0x00, //     ############
    0x01, 0xF8, 0x00, //        ######
    0x00, 0x70, 0x00, //          ###
    0x00, 0x70, 0x00, //          ###
    0x00, 0x70, 0x00, //          ###
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //

    // @470 '%' (26 pixels wide)
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x0F, 0x00, 0x07, 0x80, //     ####             ####
    0x3F, 0x80, 0x0F, 0x00, //   #######           ####
    0x7F, 0xC0, 0x0E, 0x00, //  #########          ###
    0x71, 0xE0, 0x1E, 0x00, //  ###   ####        ####
    0xE0, 0xE0, 0x3C, 0x00, // ###     ###       ####
    0xE0, 0xE0, 0x38, 0x00, // ###     ###       ###
    0xE0, 0x60, 0x78, 0x00, // ###      ##      ####
    0xE0, 0xE0, 0xF0, 0x00, // ###     ###     ####
    0xF0, 0xE0, 0xF0, 0x00, // ####    ###     ####
    0x71, 0xE1, 0xE0, 0x00, //  ###   ####    ####
    0x7F, 0xC3, 0xC0, 0x00, //  #########    ####
    0x3F, 0x83, 0xC0, 0x00, //   #######     ####
    0x0F, 0x07, 0x80, 0x00, //     ####     ####
    0x00, 0x07, 0x00, 0x00, //              ###
    0x00, 0x0F, 0x00, 0x00, //             ####
    0x00, 0x1E, 0x00, 0x00, //            ####
    0x00, 0x1C, 0x00, 0x00, //            ###
    0x00, 0x3C, 0x00, 0x00, //           ####
    0x00, 0x78, 0x3C, 0x00, //          ####     ####
    0x00, 0x70, 0x7F, 0x00, //          ###     #######
    0x00, 0xF0, 0xFF, 0x80, //         ####    #########
    0x01, 0xE1, 0xE3, 0x80, //        ####    ####   ###
    0x01, 0xC1, 0xC1, 0xC0, //        ###     ###     ###
    0x03, 0xC1, 0xC1, 0xC0, //       ####     ###     ###
    0x07, 0x81, 0xC1, 0xC0, //      ####      ###     ###
    0x07, 0x01, 0xC1, 0xC0, //      ###       ###     ###
    0x0F, 0x01, 0xC1, 0xC0, //     ####       ###     ###
    0x1E, 0x01, 0xE3, 0x80, //    ####        ####   ###
    0x1C, 0x00, 0xFF, 0x80, //    ###          #########
    0x3C, 0x00, 0x7F, 0x00, //   ####           #######
    0x78, 0x00, 0x3E, 0x00, //  ####             #####
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //

    // @658 '&' (24 pixels wide)
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x03, 0xE0, 0x00, //       #####
    0x0F, 0xF8, 0x00, //     #########
    0x1F, 0xFC, 0x00, //    ###########
    0x3E, 0x1E, 0x00, //   #####    ####
    0x38, 0x0E, 0x00, //   ###       ###
    0x78, 0x0F, 0x00, //  ####       ####
    0x70, 0x07, 0x00, //  ###         ###
    0x70, 0x00, 0x00, //  ###
    0x78, 0x00, 0x00, //  ####
    0x38, 0x00, 0x00, //   ###
    0x3C, 0x00, 0x00, //   ####
    0x3E, 0x00, 0x00, //   #####
    0x1F, 0x00, 0x00, //    #####
    0x0F, 0x80, 0x00, //     #####
    0x0F, 0xC0, 0x00, //     ######
    0x1F, 0xE0, 0x00, //    ########
    0x3D, 0xF0, 0x00, //   #### #####
    0x78, 0xF8, 0x00, //  ####   #####
    0x70, 0x7C, 0x04, //  ###     #####       #
    0xF0, 0x3E, 0x0E, // ####      #####     ###
    0xE0, 0x1F, 0x1F, // ###        #####   #####
    0xE0, 0x0F, 0xBE, // ###         ##### #####
    0xE0, 0x07, 0xFC, // ###          #########
    0xE0, 0x03, 0xF8, // ###           #######
    0xF0, 0x01, 0xF0, // ####           #####
    0x70, 0x03, 0xF8, //  ###          #######
    0x78, 0x07, 0xFC, //  ####        #########
    0x3E, 0x0F, 0xBE, //   #####     ##### #####
    0x1F, 0xFF, 0x1F, //    #############   #####
    0x0F, 0xFC, 0x0E, //     ##########      ###
    0x03, 0xF0, 0x04, //       ######         #
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //

    // @799 ''' (3 pixels wide)
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0xE0, // ###
    0xE0, // ###
    0xE0, // ###
    0xE0, // ###
    0xE0, // ###
    0xE0, // ###
    0xE0, // ###
    0xE0, // ###
    0xE0, // ###
    0xE0, // ###
    0xE0, // ###
    0xE0, // ###
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //

    // @846 '(' (11 pixels wide)
    0x00, 0x20, //           #
    0x01, 0xE0, //        ####
    0x03, 0xE0, //       #####
    0x07, 0xE0, //      ######
    0x0F, 0x00, //     ####
    0x0E, 0x00, //     ###
    0x1E, 0x00, //    ####
    0x1C, 0x00, //    ###
    0x3C, 0x00, //   ####
    0x38, 0x00, //   ###
    0x78, 0x00, //  ####
    0x70, 0x00, //  ###
    0x70, 0x00, //  ###
    0x70, 0x00, //  ###
    0x70, 0x00, //  ###
    0xF0, 0x00, // ####
    0xE0, 0x00, // ###
    0xE0, 0x00, // ###
    0xE0, 0x00, // ###
    0xE0, 0x00, // ###
    0xE0, 0x00, // ###
    0xE0, 0x00, // ###
    0xE0, 0x00, // ###
    0xE0, 0x00, // ###
    0xE0, 0x00, // ###
    0xE0, 0x00, // ###
    0xE0, 0x00, // ###
    0xE0, 0x00, // ###
    0xE0, 0x00, // ###
    0xE0, 0x00, // ###
    0xE0, 0x00, // ###
    0xE0, 0x00, // ###
    0xF0, 0x00, // ####
    0x70, 0x00, //  ###
    0x70, 0x00, //  ###
    0x70, 0x00, //  ###
    0x70, 0x00, //  ###
    0x38, 0x00, //   ###
    0x38, 0x00, //   ###
    0x38, 0x00, //   ###
    0x1C, 0x00, //    ###
    0x1C, 0x00, //    ###
    0x0E, 0x00, //     ###
    0x0F, 0x00, //     ####
    0x07, 0xC0, //      #####
    0x03, 0xE0, //       #####
    0x00, 0xE0, //         ###

    // @940 ')' (10 pixels wide)
    0x80, 0x00, // #
    0xE0, 0x00, // ###
    0xF0, 0x00, // ####
    0xF8, 0x00, // #####
    0x3C, 0x00, //   ####
    0x1E, 0x00, //    ####
    0x0E, 0x00, //     ###
    0x0F, 0x00, //     ####
    0x07, 0x00, //      ###
    0x07, 0x00, //      ###
    0x07, 0x80, //      ####
    0x03, 0x80, //       ###
    0x03, 0x80, //       ###
    0x03, 0x80, //       ###
    0x03, 0xC0, //       ####
    0x01, 0xC0, //        ###
    0x01, 0xC0, //        ###
    0x01, 0xC0, //        ###
    0x01, 0xC0, //        ###
    0x01, 0xC0, //        ###
    0x01, 0xC0, //        ###
    0x01, 0xC0, //        ###
    0x01, 0xC0, //        ###
    0x01, 0xC0, //        ###
    0x01, 0xC0, //        ###
    0x01, 0xC0, //        ###
    0x01, 0xC0, //        ###
    0x01, 0xC0, //        ###
    0x01, 0xC0, //        ###
    0x01, 0xC0, //        ###
    0x01, 0xC0, //        ###
    0x01, 0xC0, //        ###
    0x01, 0xC0, //        ###
    0x01, 0xC0, //        ###
    0x03, 0x80, //       ###
    0x03, 0x80, //       ###
    0x03, 0x80, //       ###
    0x03, 0x80, //       ###
    0x07, 0x00, //      ###
    0x07, 0x00, //      ###
    0x0E, 0x00, //     ###
    0x0E, 0x00, //     ###
    0x1C, 0x00, //    ###
    0x3C, 0x00, //   ####
    0xF8, 0x00, // #####
    0xF0, 0x00, // ####
    0xC0, 0x00, // ##

    // @1034 '*' (10 pixels wide)
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x12, 0x00, //    #  #
    0x33, 0x00, //   ##  ##
    0x36, 0x00, //   ## ##
    0x1E, 0x00, //    ####
    0xEF, 0xC0, // ### ######
    0xFF, 0xC0, // ##########
    0x1C, 0x00, //    ###
    0x16, 0x00, //    # ##
    0x33, 0x00, //   ##  ##
    0x32, 0x00, //   ##  #
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //

    // @1128 '+' (24 pixels wide)
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x18, 0x00, //            ##
    0x00, 0x18, 0x00, //            ##
    0x00, 0x18, 0x00, //            ##
    0x00, 0x18, 0x00, //            ##
    0x00, 0x18, 0x00, //            ##
    0x00, 0x18, 0x00, //            ##
    0x00, 0x18, 0x00, //            ##
    0x00, 0x18, 0x00, //            ##
    0x00, 0x18, 0x00, //            ##
    0x00, 0x18, 0x00, //            ##
    0x00, 0x18, 0x00, //            ##
    0x00, 0x18, 0x00, //            ##
    0xFF, 0xFF, 0xFF, // ########################
    0xFF, 0xFF, 0xFF, // ########################
    0xFF, 0xFF, 0xFF, // ########################
    0x00, 0x18, 0x00, //            ##
    0x00, 0x18, 0x00, //            ##
    0x00, 0x18, 0x00, //            ##
    0x00, 0x18, 0x00, //            ##
    0x00, 0x18, 0x00, //            ##
    0x00, 0x18, 0x00, //            ##
    0x00, 0x18, 0x00, //            ##
    0x00, 0x18, 0x00, //            ##
    0x00, 0x18, 0x00, //            ##
    0x00, 0x18, 0x00, //            ##
    0x00, 0x18, 0x00, //            ##
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //

    // @1269 ',' (5 pixels wide)
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x60, //  ##
    0xF0, // ####
    0xF8, // #####
    0xF8, // #####
    0x78, //  ####
    0x18, //    ##
    0x18, //    ##
    0x30, //   ##
    0x30, //   ##
    0x70, //  ###
    0xE0, // ###
    0xC0, // ##
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //

    // @1316 '-' (14 pixels wide)
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0xFF, 0xFC, // ##############
    0xFF, 0xFC, // ##############
    0xFF, 0xFC, // ##############
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //

    // @1410 '.' (5 pixels wide)
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x70, //  ###
    0x78, //  ####
    0xF8, // #####
    0x78, //  ####
    0x70, //  ###
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //

    // @1457 '/' (25 pixels wide)
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x07, 0x80, //                      ####
    0x00, 0x00, 0x07, 0x00, //                      ###
    0x00, 0x00, 0x0F, 0x00, //                     ####
    0x00, 0x00, 0x0E, 0x00, //                     ###
    0x00, 0x00, 0x1C, 0x00, //                    ###
    0x00, 0x00, 0x1C, 0x00, //                    ###
    0x00, 0x00, 0x38, 0x00, //                   ###
    0x00, 0x00, 0x38, 0x00, //                   ###
    0x00, 0x00, 0x70, 0x00, //                  ###
    0x00, 0x00, 0x70, 0x00, //                  ###
    0x00, 0x00, 0xE0, 0x00, //                 ###
    0x00, 0x00, 0xE0, 0x00, //                 ###
    0x00, 0x01, 0xC0, 0x00, //                ###
    0x00, 0x03, 0xC0, 0x00, //               ####
    0x00, 0x03, 0x80, 0x00, //               ###
    0x00, 0x07, 0x80, 0x00, //              ####
    0x00, 0x07, 0x00, 0x00, //              ###
    0x00, 0x0F, 0x00, 0x00, //             ####
    0x00, 0x0E, 0x00, 0x00, //             ###
    0x00, 0x1E, 0x00, 0x00, //            ####
    0x00, 0x1C, 0x00, 0x00, //            ###
    0x00, 0x3C, 0x00, 0x00, //           ####
    0x00, 0x38, 0x00, 0x00, //           ###
    0x00, 0x78, 0x00, 0x00, //          ####
    0x00, 0x70, 0x00, 0x00, //          ###
    0x00, 0xF0, 0x00, 0x00, //         ####
    0x00, 0xE0, 0x00, 0x00, //         ###
    0x01, 0xE0, 0x00, 0x00, //        ####
    0x01, 0xC0, 0x00, 0x00, //        ###
    0x03, 0xC0, 0x00, 0x00, //       ####
    0x03, 0x80, 0x00, 0x00, //       ###
    0x07, 0x80, 0x00, 0x00, //      ####
    0x07, 0x00, 0x00, 0x00, //      ###
    0x0F, 0x00, 0x00, 0x00, //     ####
    0x0E, 0x00, 0x00, 0x00, //     ###
    0x1E, 0x00, 0x00, 0x00, //    ####
    0x1C, 0x00, 0x00, 0x00, //    ###
    0x3C, 0x00, 0x00, 0x00, //   ####
    0x38, 0x00, 0x00, 0x00, //   ###
    0x70, 0x00, 0x00, 0x00, //  ###
    0xF0, 0x00, 0x00, 0x00, // ####
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //

    // @1645 '0' (21 pixels wide)
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0xFC, 0x00, //         ######
    0x03, 0xFF, 0x00, //       ##########
    0x07, 0xFF, 0x80, //      ############
    0x0F, 0x87, 0xC0, //     #####    #####
    0x1E, 0x03, 0xC0, //    ####       ####
    0x3C, 0x01, 0xE0, //   ####         ####
    0x3C, 0x00, 0xF0, //   ####          ####
    0x78, 0x00, 0xF0, //  ####           ####
    0x78, 0x00, 0x70, //  ####            ###
    0x70, 0x00, 0x78, //  ###             ####
    0xF0, 0x00, 0x78, // ####             ####
    0xF0, 0x00, 0x38, // ####              ###
    0xF0, 0x00, 0x38, // ####              ###
    0xE0, 0x00, 0x38, // ###               ###
    0xE0, 0x00, 0x38, // ###               ###
    0xE0, 0x00, 0x38, // ###               ###
    0xE0, 0x00, 0x38, // ###               ###
    0xE0, 0x00, 0x38, // ###               ###
    0xF0, 0x00, 0x38, // ####              ###
    0xF0, 0x00, 0x38, // ####              ###
    0xF0, 0x00, 0x78, // ####             ####
    0x70, 0x00, 0x78, //  ###             ####
    0x78, 0x00, 0x70, //  ####            ###
    0x78, 0x00, 0xF0, //  ####           ####
    0x3C, 0x00, 0xF0, //   ####          ####
    0x3C, 0x01, 0xE0, //   ####         ####
    0x1E, 0x03, 0xC0, //    ####       ####
    0x0F, 0x87, 0xC0, //     #####    #####
    0x07, 0xFF, 0x80, //      ############
    0x03, 0xFF, 0x00, //       ##########
    0x00, 0xFC, 0x00, //         ######
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //

    // @1786 '1' (9 pixels wide)
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x07, 0x80, //      ####
    0x0F, 0x80, //     #####
    0x1F, 0x80, //    ######
    0x7F, 0x80, //  ########
    0xFF, 0x80, // #########
    0xF3, 0x80, // ####  ###
    0xE3, 0x80, // ###   ###
    0xC3, 0x80, // ##    ###
    0x83, 0x80, // #     ###
    0x03, 0x80, //       ###
    0x03, 0x80, //       ###
    0x03, 0x80, //       ###
    0x03, 0x80, //       ###
    0x03, 0x80, //       ###
    0x03, 0x80, //       ###
    0x03, 0x80, //       ###
    0x03, 0x80, //       ###
    0x03, 0x80, //       ###
    0x03, 0x80, //       ###
    0x03, 0x80, //       ###
    0x03, 0x80, //       ###
    0x03, 0x80, //       ###
    0x03, 0x80, //       ###
    0x03, 0x80, //       ###
    0x03, 0x80, //       ###
    0x03, 0x80, //       ###
    0x03, 0x80, //       ###
    0x03, 0x80, //       ###
    0x03, 0x80, //       ###
    0x03, 0x80, //       ###
    0x03, 0x80, //       ###
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //

    // @1880 '2' (20 pixels wide)
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x01, 0xF8, 0x00, //        ######
    0x07, 0xFE, 0x00, //      ##########
    0x1F, 0xFF, 0x00, //    #############
    0x3E, 0x0F, 0x80, //   #####     #####
    0x3C, 0x03, 0xC0, //   ####        ####
    0x78, 0x01, 0xC0, //  ####          ###
    0x70, 0x01, 0xE0, //  ###           ####
    0x70, 0x01, 0xE0, //  ###           ####
    0x70, 0x01, 0xE0, //  ###           ####
    0x00, 0x01, 0xE0, //                ####
    0x00, 0x01, 0xE0, //                ####
    0x00, 0x01, 0xC0, //                ###
    0x00, 0x03, 0xC0, //               ####
    0x00, 0x03, 0xC0, //               ####
    0x00, 0x07, 0x80, //              ####
    0x00, 0x0F, 0x00, //             ####
    0x00, 0x1E, 0x00, //            ####
    0x00, 0x3C, 0x00, //           ####
    0x00, 0x7C, 0x00, //          #####
    0x00, 0xF8, 0x00, //         #####
    0x01, 0xF0, 0x00, //        #####
    0x01, 0xE0, 0x00, //        ####
    0x03, 0xC0, 0x00, //       ####
    0x07, 0x80, 0x00, //      ####
    0x0F, 0x00, 0x00, //     ####
    0x1E, 0x00, 0x00, //    ####
    0x3C, 0x00, 0x00, //   ####
    0x78, 0x00, 0x00, //  ####
    0xFF, 0xFF, 0xF0, // ####################
    0xFF, 0xFF, 0xF0, // ####################
    0xFF, 0xFF, 0xF0, // ####################
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //

    // @2021 '3' (19 pixels wide)
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x01, 0xF8, 0x00, //        ######
    0x07, 0xFC, 0x00, //      #########
    0x1F, 0xFF, 0x00, //    #############
    0x1E, 0x0F, 0x00, //    ####     ####
    0x3C, 0x07, 0x80, //   ####       ####
    0x38, 0x03, 0x80, //   ###         ###
    0x38, 0x03, 0x80, //   ###         ###
    0x00, 0x03, 0xC0, //               ####
    0x00, 0x03, 0x80, //               ###
    0x00, 0x03, 0x80, //               ###
    0x00, 0x07, 0x80, //              ####
    0x00, 0x0F, 0x00, //             ####
    0x01, 0xFE, 0x00, //        ########
    0x01, 0xFC, 0x00, //        #######
    0x01, 0xFE, 0x00, //        ########
    0x00, 0x0F, 0x80, //             #####
    0x00, 0x03, 0x80, //               ###
    0x00, 0x03, 0xC0, //               ####
    0x00, 0x01, 0xE0, //                ####
    0x00, 0x01, 0xE0, //                ####
    0x00, 0x00, 0xE0, //                 ###
    0x00, 0x00, 0xE0, //                 ###
    0xF0, 0x00, 0xE0, // ####            ###
    0x70, 0x01, 0xE0, //  ###           ####
    0x70, 0x01, 0xE0, //  ###           ####
    0x78, 0x03, 0xC0, //  ####         ####
    0x3C, 0x03, 0xC0, //   ####        ####
    0x3E, 0x0F, 0x80, //   #####     #####
    0x1F, 0xFF, 0x00, //    #############
    0x07, 0xFE, 0x00, //      ##########
    0x01, 0xF8, 0x00, //        ######
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //

    // @2162 '4' (21 pixels wide)
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x07, 0xC0, //              #####
    0x00, 0x0F, 0xC0, //             ######
    0x00, 0x0F, 0xC0, //             ######
    0x00, 0x1F, 0xC0, //            #######
    0x00, 0x1F, 0xC0, //            #######
    0x00, 0x3B, 0xC0, //           ### ####
    0x00, 0x7B, 0xC0, //          #### ####
    0x00, 0x73, 0xC0, //          ###  ####
    0x00, 0xE3, 0xC0, //         ###   ####
    0x01, 0xE3, 0xC0, //        ####   ####
    0x01, 0xC3, 0xC0, //        ###    ####
    0x03, 0xC3, 0xC0, //       ####    ####
    0x03, 0x83, 0xC0, //       ###     ####
    0x07, 0x03, 0xC0, //      ###      ####
    0x0F, 0x03, 0xC0, //     ####      ####
    0x0E, 0x03, 0xC0, //     ###       ####
    0x1E, 0x03, 0xC0, //    ####       ####
    0x3C, 0x03, 0xC0, //   ####        ####
    0x38, 0x03, 0xC0, //   ###         ####
    0x78, 0x03, 0xC0, //  ####         ####
    0x70, 0x03, 0xC0, //  ###          ####
    0xFF, 0xFF, 0xF8, // #####################
    0xFF, 0xFF, 0xF8, // #####################
    0xFF, 0xFF, 0xF8, // #####################
    0x00, 0x03, 0xC0, //               ####
    0x00, 0x03, 0xC0, //               ####
    0x00, 0x03, 0xC0, //               ####
    0x00, 0x03, 0xC0, //               ####
    0x00, 0x03, 0xC0, //               ####
    0x00, 0x03, 0xC0, //               ####
    0x00, 0x03, 0xC0, //               ####
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //

    // @2303 '5' (19 pixels wide)
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x3F, 0xFF, 0x80, //   ###############
    0x3F, 0xFF, 0x80, //   ###############
    0x3F, 0xFF, 0x80, //   ###############
    0x38, 0x00, 0x00, //   ###
    0x78, 0x00, 0x00, //  ####
    0x70, 0x00, 0x00, //  ###
    0x70, 0x00, 0x00, //  ###
    0x70, 0x00, 0x00, //  ###
    0x70, 0x00, 0x00, //  ###
    0x70, 0x00, 0x00, //  ###
    0x73, 0xF8, 0x00, //  ###  #######
    0x7F, 0xFE, 0x00, //  ##############
    0xFF, 0xFF, 0x00, // ################
    0xFC, 0x1F, 0x80, // ######     ######
    0x70, 0x07, 0xC0, //  ###         #####
    0x10, 0x03, 0xC0, //    #          ####
    0x00, 0x01, 0xE0, //                ####
    0x00, 0x01, 0xE0, //                ####
    0x00, 0x00, 0xE0, //                 ###
    0x00, 0x00, 0xE0, //                 ###
    0x00, 0x00, 0xE0, //                 ###
    0x00, 0x00, 0xE0, //                 ###
    0x00, 0x00, 0xE0, //                 ###
    0x00, 0x01, 0xE0, //                ####
    0x20, 0x01, 0xE0, //   #            ####
    0x60, 0x03, 0xC0, //  ##           ####
    0xF0, 0x07, 0xC0, // ####         #####
    0xFC, 0x1F, 0x80, // ######     ######
    0x3F, 0xFF, 0x00, //   ##############
    0x1F, 0xFE, 0x00, //    ############
    0x03, 0xF0, 0x00, //       ######
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //

    // @2444 '6' (22 pixels wide)
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x07, 0xC0, //              #####
    0x00, 0x0F, 0x00, //             ####
    0x00, 0x0F, 0x00, //             ####
    0x00, 0x1E, 0x00, //            ####
    0x00, 0x3C, 0x00, //           ####
    0x00, 0x78, 0x00, //          ####
    0x00, 0xF0, 0x00, //         ####
    0x00, 0xF0, 0x00, //         ####
    0x01, 0xE0, 0x00, //        ####
    0x03, 0xC0, 0x00, //       ####
    0x07, 0xFE, 0x00, //      ##########
    0x07, 0xFF, 0x80, //      ############
    0x0F, 0xFF, 0xC0, //     ##############
    0x1F, 0x07, 0xE0, //    #####     ######
    0x3C, 0x01, 0xF0, //   ####         #####
    0x3C, 0x00, 0xF0, //   ####          ####
    0x78, 0x00, 0x78, //  ####            ####
    0x70, 0x00, 0x78, //  ###             ####
    0x70, 0x00, 0x38, //  ###              ###
    0xF0, 0x00, 0x3C, // ####              ####
    0xF0, 0x00, 0x3C, // ####              ####
    0xF0, 0x00, 0x3C, // ####              ####
    0x70, 0x00, 0x38, //  ###              ###
    0x78, 0x00, 0x38, //  ####             ###
    0x78, 0x00, 0x78, //  ####            ####
    0x3C, 0x00, 0xF0, //   ####          ####
    0x3E, 0x00, 0xF0, //   #####         ####
    0x1F, 0x83, 0xE0, //    ######     #####
    0x0F, 0xFF, 0xC0, //     ##############
    0x03, 0xFF, 0x80, //       ###########
    0x00, 0xFE, 0x00, //         #######
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //

    // @2585 '7' (20 pixels wide)
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0xFF, 0xFF, 0xF0, // ####################
    0xFF, 0xFF, 0xF0, // ####################
    0xFF, 0xFF, 0xE0, // ###################
    0x00, 0x01, 0xE0, //                ####
    0x00, 0x01, 0xC0, //                ###
    0x00, 0x03, 0xC0, //               ####
    0x00, 0x03, 0x80, //               ###
    0x00, 0x07, 0x80, //              ####
    0x00, 0x0F, 0x00, //             ####
    0x00, 0x0F, 0x00, //             ####
    0x00, 0x1E, 0x00, //            ####
    0x00, 0x1E, 0x00, //            ####
    0x00, 0x3C, 0x00, //           ####
    0x00, 0x38, 0x00, //           ###
    0x00, 0x78, 0x00, //          ####
    0x00, 0x70, 0x00, //          ###
    0x00, 0xF0, 0x00, //         ####
    0x00, 0xE0, 0x00, //         ###
    0x01, 0xE0, 0x00, //        ####
    0x03, 0xC0, 0x00, //       ####
    0x03, 0xC0, 0x00, //       ####
    0x07, 0x80, 0x00, //      ####
    0x07, 0x80, 0x00, //      ####
    0x0F, 0x00, 0x00, //     ####
    0x0F, 0x00, 0x00, //     ####
    0x1E, 0x00, 0x00, //    ####
    0x1C, 0x00, 0x00, //    ###
    0x3C, 0x00, 0x00, //   ####
    0x78, 0x00, 0x00, //  ####
    0x78, 0x00, 0x00, //  ####
    0xF0, 0x00, 0x00, // ####
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //

    // @2726 '8' (19 pixels wide)
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x03, 0xF0, 0x00, //       ######
    0x07, 0xFC, 0x00, //      #########
    0x1F, 0xFE, 0x00, //    ############
    0x1E, 0x0F, 0x00, //    ####     ####
    0x3C, 0x07, 0x80, //   ####       ####
    0x38, 0x03, 0x80, //   ###         ###
    0x38, 0x03, 0x80, //   ###         ###
    0x38, 0x03, 0x80, //   ###         ###
    0x38, 0x03, 0x80, //   ###         ###
    0x38, 0x03, 0x80, //   ###         ###
    0x3C, 0x07, 0x80, //   ####       ####
    0x1E, 0x0F, 0x00, //    ####     ####
    0x0F, 0xFF, 0x00, //     ############
    0x0F, 0xFC, 0x00, //     ##########
    0x1F, 0xFE, 0x00, //    ############
    0x3E, 0x0F, 0x00, //   #####     ####
    0x78, 0x07, 0x80, //  ####        ####
    0x78, 0x03, 0xC0, //  ####         ####
    0xF0, 0x01, 0xC0, // ####           ###
    0xF0, 0x01, 0xE0, // ####           ####
    0xE0, 0x00, 0xE0, // ###             ###
    0xE0, 0x00, 0xE0, // ###             ###
    0xE0, 0x01, 0xE0, // ###            ####
    0xF0, 0x01, 0xE0, // ####           ####
    0xF0, 0x01, 0xE0, // ####           ####
    0x78, 0x03, 0xC0, //  ####         ####
    0x7C, 0x07, 0xC0, //  #####       #####
    0x3E, 0x0F, 0x80, //   #####     #####
    0x1F, 0xFF, 0x00, //    #############
    0x0F, 0xFE, 0x00, //     ###########
    0x03, 0xF8, 0x00, //       #######
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //

    // @2867 '9' (22 pixels wide)
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x01, 0xFC, 0x00, //        #######
    0x07, 0xFF, 0x00, //      ###########
    0x0F, 0xFF, 0xC0, //     ##############
    0x1F, 0x07, 0xE0, //    #####     ######
    0x3C, 0x01, 0xF0, //   ####         #####
    0x38, 0x00, 0xF0, //   ###           ####
    0x78, 0x00, 0x78, //  ####            ####
    0x70, 0x00, 0x78, //  ###             ####
    0x70, 0x00, 0x38, //  ###              ###
    0xF0, 0x00, 0x3C, // ####              ####
    0xF0, 0x00, 0x3C, // ####              ####
    0xF0, 0x00, 0x3C, // ####              ####
    0x70, 0x00, 0x38, //  ###              ###
    0x78, 0x00, 0x38, //  ####             ###
    0x78, 0x00, 0x78, //  ####            ####
    0x3C, 0x00, 0xF0, //   ####          ####
    0x3E, 0x00, 0xF0, //   #####         ####
    0x1F, 0x83, 0xE0, //    ######     #####
    0x0F, 0xFF, 0xC0, //     ##############
    0x07, 0xFF, 0x80, //      ############
    0x01, 0xFF, 0x80, //        ##########
    0x00, 0x0F, 0x00, //             ####
    0x00, 0x1E, 0x00, //            ####
    0x00, 0x3C, 0x00, //           ####
    0x00, 0x3C, 0x00, //           ####
    0x00, 0x78, 0x00, //          ####
    0x00, 0xF0, 0x00, //         ####
    0x01, 0xE0, 0x00, //        ####
    0x03, 0xC0, 0x00, //       ####
    0x03, 0xC0, 0x00, //       ####
    0x0F, 0x80, 0x00, //     #####
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //

    // @3008 ':' (4 pixels wide)
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x60, //  ##
    0xF0, // ####
    0xF0, // ####
    0xF0, // ####
    0x60, //  ##
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x60, //  ##
    0xF0, // ####
    0xF0, // ####
    0xF0, // ####
    0x60, //  ##
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //

    // @3055 ';' (5 pixels wide)
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x60, //  ##
    0xF0, // ####
    0xF0, // ####
    0xF0, // ####
    0x60, //  ##
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x60, //  ##
    0xF0, // ####
    0xF8, // #####
    0xF8, // #####
    0x78, //  ####
    0x38, //   ###
    0x18, //    ##
    0x30, //   ##
    0x30, //   ##
    0x60, //  ##
    0xE0, // ###
    0xC0, // ##
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //

    // @3102 '<' (22 pixels wide)
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x0C, //                     ##
    0x00, 0x00, 0x3C, //                   ####
    0x00, 0x00, 0xFC, //                 ######
    0x00, 0x03, 0xF0, //               ######
    0x00, 0x0F, 0xC0, //             ######
    0x00, 0x3F, 0x80, //           #######
    0x00, 0x7E, 0x00, //          ######
    0x01, 0xF8, 0x00, //        ######
    0x07, 0xE0, 0x00, //      ######
    0x1F, 0x80, 0x00, //    ######
    0x7E, 0x00, 0x00, //  ######
    0xF8, 0x00, 0x00, // #####
    0xE0, 0x00, 0x00, // ###
    0xF0, 0x00, 0x00, // ####
    0xFC, 0x00, 0x00, // ######
    0x3F, 0x00, 0x00, //   ######
    0x0F, 0xC0, 0x00, //     ######
    0x07, 0xE0, 0x00, //      ######
    0x01, 0xF8, 0x00, //        ######
    0x00, 0x7E, 0x00, //          ######
    0x00, 0x1F, 0x80, //            ######
    0x00, 0x07, 0xE0, //              ######
    0x00, 0x01, 0xF8, //                ######
    0x00, 0x00, 0x7C, //                  #####
    0x00, 0x00, 0x1C, //                    ###
    0x00, 0x00, 0x04, //                      #
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //

    // @3243 '=' (24 pixels wide)
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0xFF, 0xFF, 0xFF, // ########################
    0xFF, 0xFF, 0xFF, // ########################
    0xFF, 0xFF, 0xFF, // ########################
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0xFF, 0xFF, 0xFF, // ########################
    0xFF, 0xFF, 0xFF, // ########################
    0xFF, 0xFF, 0xFF, // ########################
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //

    // @3384 '>' (22 pixels wide)
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x80, 0x00, 0x00, // #
    0xE0, 0x00, 0x00, // ###
    0xF8, 0x00, 0x00, // #####
    0x7E, 0x00, 0x00, //  ######
    0x1F, 0x80, 0x00, //    ######
    0x07, 0xE0, 0x00, //      ######
    0x01, 0xF8, 0x00, //        ######
    0x00, 0x7E, 0x00, //          ######
    0x00, 0x1F, 0x80, //            ######
    0x00, 0x0F, 0xE0, //             #######
    0x00, 0x03, 0xF0, //               ######
    0x00, 0x00, 0xFC, //                 ######
    0x00, 0x00, 0x3C, //                   ####
    0x00, 0x00, 0x3C, //                   ####
    0x00, 0x00, 0xFC, //                 ######
    0x00, 0x03, 0xF0, //               ######
    0x00, 0x0F, 0xC0, //             ######
    0x00, 0x3F, 0x00, //           ######
    0x00, 0xFC, 0x00, //         ######
    0x03, 0xF0, 0x00, //       ######
    0x0F, 0xC0, 0x00, //     ######
    0x3F, 0x00, 0x00, //   ######
    0xFC, 0x00, 0x00, // ######
    0xF0, 0x00, 0x00, // ####
    0xC0, 0x00, 0x00, // ##
    0x80, 0x00, 0x00, // #
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //

    // @3525 '?' (18 pixels wide)
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x03, 0xF0, 0x00, //       ######
    0x0F, 0xFC, 0x00, //     ##########
    0x1F, 0xFF, 0x00, //    #############
    0x3E, 0x0F, 0x00, //   #####     ####
    0x78, 0x07, 0x80, //  ####        ####
    0x70, 0x03, 0x80, //  ###          ###
    0x70, 0x01, 0xC0, //  ###           ###
    0xF0, 0x01, 0xC0, // ####           ###
    0x00, 0x01, 0xC0, //                ###
    0x00, 0x01, 0xC0, //                ###
    0x00, 0x03, 0xC0, //               ####
    0x00, 0x03, 0x80, //               ###
    0x00, 0x07, 0x80, //              ####
    0x00, 0x1F, 0x00, //            #####
    0x00, 0x7E, 0x00, //          ######
    0x00, 0x78, 0x00, //          ####
    0x00, 0xF0, 0x00, //         ####
    0x00, 0xE0, 0x00, //         ###
    0x00, 0xE0, 0x00, //         ###
    0x00, 0xE0, 0x00, //         ###
    0x00, 0xE0, 0x00, //         ###
    0x00, 0xE0, 0x00, //         ###
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0xE0, 0x00, //         ###
    0x01, 0xE0, 0x00, //        ####
    0x01, 0xF0, 0x00, //        #####
    0x01, 0xE0, 0x00, //        ####
    0x00, 0xE0, 0x00, //         ###
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //

    // @3666 '@' (19 pixels wide)
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0xFC, 0x00, //         ######
    0x03, 0xFF, 0x00, //       ##########
    0x0F, 0xFF, 0x80, //     #############
    0x1F, 0x07, 0x80, //    #####     ####
    0x3C, 0x01, 0xC0, //   ####         ###
    0x38, 0x00, 0xC0, //   ###           ##
    0x70, 0x00, 0xE0, //  ###            ###
    0x70, 0x3E, 0xE0, //  ###      ##### ###
    0x60, 0x7F, 0xE0, //  ##      ##########
    0x60, 0xE1, 0xE0, //  ##     ###    ####
    0xE0, 0xC0, 0xE0, // ###     ##      ###
    0xE1, 0xC0, 0xE0, // ###    ###      ###
    0xE1, 0xC0, 0xE0, // ###    ###      ###
    0x60, 0xC0, 0xE0, //  ##     ##      ###
    0x60, 0xE1, 0xE0, //  ##     ###    ####
    0x70, 0x7F, 0xE0, //  ###     ##########
    0x70, 0x3E, 0xE0, //  ###      ##### ###
    0x38, 0x00, 0x00, //   ###
    0x1E, 0x00, 0x00, //    ####
    0x0F, 0x80, 0x00, //     #####
    0x07, 0xFF, 0xE0, //      ##############
    0x03, 0xFF, 0xE0, //       #############
    0x00, 0xFF, 0xE0, //         ###########
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //

    // @3807 'A' (28 pixels wide)
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x0F, 0x00, 0x00, //             ####
    0x00, 0x1F, 0x00, 0x00, //            #####
    0x00, 0x1F, 0x80, 0x00, //            ######
    0x00, 0x3F, 0x80, 0x00, //           #######
    0x00, 0x3F, 0xC0, 0x00, //           ########
    0x00, 0x3B, 0xC0, 0x00, //           ### ####
    0x00, 0x79, 0xC0, 0x00, //          ####  ###
    0x00, 0x71, 0xE0, 0x00, //          ###   ####
    0x00, 0xF0, 0xE0, 0x00, //         ####    ###
    0x00, 0xF0, 0xF0, 0x00, //         ####    ####
    0x00, 0xE0, 0xF0, 0x00, //         ###     ####
    0x01, 0xE0, 0x70, 0x00, //        ####      ###
    0x01, 0xE0, 0x78, 0x00, //        ####      ####
    0x01, 0xC0, 0x38, 0x00, //        ###        ###
    0x03, 0xC0, 0x3C, 0x00, //       ####        ####
    0x03, 0x80, 0x3C, 0x00, //       ###         ####
    0x07, 0x80, 0x1C, 0x00, //      ####          ###
    0x07, 0x80, 0x1E, 0x00, //      ####          ####
    0x07, 0x00, 0x1E, 0x00, //      ###           ####
    0x0F, 0x00, 0x0E, 0x00, //     ####            ###
    0x0F, 0x00, 0x0F, 0x00, //     ####            ####
    0x1F, 0xFF, 0xFF, 0x00, //    #####################
    0x1F, 0xFF, 0xFF, 0x80, //    ######################
    0x1F, 0xFF, 0xFF, 0x80, //    ######################
    0x3C, 0x00, 0x03, 0x80, //   ####                ###
    0x3C, 0x00, 0x03, 0xC0, //   ####                ####
    0x38, 0x00, 0x03, 0xC0, //   ###                 ####
    0x78, 0x00, 0x01, 0xE0, //  ####                  ####
    0x78, 0x00, 0x01, 0xE0, //  ####                  ####
    0xF0, 0x00, 0x01, 0xE0, // ####                   ####
    0xF0, 0x00, 0x00, 0xF0, // ####                    ####
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //

    // @3995 'B' (21 pixels wide)
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0xFF, 0xF8, 0x00, // #############
    0xFF, 0xFE, 0x00, // ###############
    0xFF, 0xFF, 0x00, // ################
    0xF0, 0x0F, 0x80, // ####        #####
    0xF0, 0x07, 0xC0, // ####         #####
    0xF0, 0x03, 0xC0, // ####          ####
    0xF0, 0x01, 0xC0, // ####           ###
    0xF0, 0x01, 0xE0, // ####           ####
    0xF0, 0x01, 0xE0, // ####           ####
    0xF0, 0x01, 0xE0, // ####           ####
    0xF0, 0x01, 0xC0, // ####           ###
    0xF0, 0x03, 0xC0, // ####          ####
    0xF0, 0x07, 0xC0, // ####         #####
    0xF0, 0x0F, 0x80, // ####        #####
    0xFF, 0xFF, 0x00, // ################
    0xFF, 0xFF, 0x00, // ################
    0xFF, 0xFF, 0xC0, // ##################
    0xF0, 0x03, 0xE0, // ####          #####
    0xF0, 0x00, 0xF0, // ####            ####
    0xF0, 0x00, 0xF0, // ####            ####
    0xF0, 0x00, 0x78, // ####             ####
    0xF0, 0x00, 0x78, // ####             ####
    0xF0, 0x00, 0x78, // ####             ####
    0xF0, 0x00, 0x78, // ####             ####
    0xF0, 0x00, 0x78, // ####             ####
    0xF0, 0x00, 0xF0, // ####            ####
    0xF0, 0x00, 0xF0, // ####            ####
    0xF0, 0x03, 0xF0, // ####          ######
    0xFF, 0xFF, 0xE0, // ###################
    0xFF, 0xFF, 0xC0, // ##################
    0xFF, 0xFF, 0x00, // ################
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //

    // @4136 'C' (23 pixels wide)
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x1F, 0xE0, //            ########
    0x00, 0xFF, 0xF8, //         #############
    0x03, 0xFF, 0xFE, //       #################
    0x07, 0xE0, 0x3E, //      ######       #####
    0x0F, 0x80, 0x0E, //     #####           ###
    0x1F, 0x00, 0x06, //    #####             ##
    0x1E, 0x00, 0x02, //    ####               #
    0x3C, 0x00, 0x00, //   ####
    0x78, 0x00, 0x00, //  ####
    0x78, 0x00, 0x00, //  ####
    0x70, 0x00, 0x00, //  ###
    0xF0, 0x00, 0x00, // ####
    0xF0, 0x00, 0x00, // ####
    0xF0, 0x00, 0x00, // ####
    0xF0, 0x00, 0x00, // ####
    0xE0, 0x00, 0x00, // ###
    0xF0, 0x00, 0x00, // ####
    0xF0, 0x00, 0x00, // ####
    0xF0, 0x00, 0x00, // ####
    0xF0, 0x00, 0x00, // ####
    0x70, 0x00, 0x00, //  ###
    0x78, 0x00, 0x00, //  ####
    0x38, 0x00, 0x00, //   ###
    0x3C, 0x00, 0x00, //   ####
    0x1E, 0x00, 0x02, //    ####               #
    0x1F, 0x00, 0x06, //    #####             ##
    0x0F, 0x80, 0x0E, //     #####           ###
    0x07, 0xE0, 0x3E, //      ######       #####
    0x03, 0xFF, 0xFE, //       #################
    0x00, 0xFF, 0xF8, //         #############
    0x00, 0x1F, 0xE0, //            ########
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //

    // @4277 'D' (23 pixels wide)
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0xFF, 0xF0, 0x00, // ############
    0xFF, 0xFE, 0x00, // ###############
    0xFF, 0xFF, 0x80, // #################
    0xF0, 0x0F, 0xC0, // ####        ######
    0xF0, 0x03, 0xE0, // ####          #####
    0xF0, 0x01, 0xF0, // ####           #####
    0xF0, 0x00, 0xF8, // ####            #####
    0xF0, 0x00, 0x78, // ####             ####
    0xF0, 0x00, 0x3C, // ####              ####
    0xF0, 0x00, 0x3C, // ####              ####
    0xF0, 0x00, 0x1E, // ####               ####
    0xF0, 0x00, 0x1E, // ####               ####
    0xF0, 0x00, 0x1E, // ####               ####
    0xF0, 0x00, 0x0E, // ####                ###
    0xF0, 0x00, 0x0E, // ####                ###
    0xF0, 0x00, 0x0E, // ####                ###
    0xF0, 0x00, 0x0E, // ####                ###
    0xF0, 0x00, 0x0E, // ####                ###
    0xF0, 0x00, 0x1E, // ####               ####
    0xF0, 0x00, 0x1E, // ####               ####
    0xF0, 0x00, 0x1E, // ####               ####
    0xF0, 0x00, 0x3C, // ####              ####
    0xF0, 0x00, 0x3C, // ####              ####
    0xF0, 0x00, 0x78, // ####             ####
    0xF0, 0x00, 0xF8, // ####            #####
    0xF0, 0x01, 0xF0, // ####           #####
    0xF0, 0x03, 0xE0, // ####          #####
    0xF0, 0x0F, 0xC0, // ####        ######
    0xFF, 0xFF, 0x80, // #################
    0xFF, 0xFE, 0x00, // ###############
    0xFF, 0xF0, 0x00, // ############
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //

    // @4418 'E' (17 pixels wide)
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0xFF, 0xFF, 0x80, // #################
    0xFF, 0xFF, 0x80, // #################
    0xFF, 0xFF, 0x80, // #################
    0xF0, 0x00, 0x00, // ####
    0xF0, 0x00, 0x00, // ####
    0xF0, 0x00, 0x00, // ####
    0xF0, 0x00, 0x00, // ####
    0xF0, 0x00, 0x00, // ####
    0xF0, 0x00, 0x00, // ####
    0xF0, 0x00, 0x00, // ####
    0xF0, 0x00, 0x00, // ####
    0xF0, 0x00, 0x00, // ####
    0xF0, 0x00, 0x00, // ####
    0xF0, 0x00, 0x00, // ####
    0xFF, 0xFE, 0x00, // ###############
    0xFF, 0xFE, 0x00, // ###############
    0xFF, 0xFE, 0x00, // ###############
    0xF0, 0x00, 0x00, // ####
    0xF0, 0x00, 0x00, // ####
    0xF0, 0x00, 0x00, // ####
    0xF0, 0x00, 0x00, // ####
    0xF0, 0x00, 0x00, // ####
    0xF0, 0x00, 0x00, // ####
    0xF0, 0x00, 0x00, // ####
    0xF0, 0x00, 0x00, // ####
    0xF0, 0x00, 0x00, // ####
    0xF0, 0x00, 0x00, // ####
    0xF0, 0x00, 0x00, // ####
    0xFF, 0xFF, 0x80, // #################
    0xFF, 0xFF, 0x80, // #################
    0xFF, 0xFF, 0x80, // #################
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //

    // @4559 'F' (17 pixels wide)
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0xFF, 0xFF, 0x80, // #################
    0xFF, 0xFF, 0x80, // #################
    0xFF, 0xFF, 0x80, // #################
    0xF0, 0x00, 0x00, // ####
    0xF0, 0x00, 0x00, // ####
    0xF0, 0x00, 0x00, // ####
    0xF0, 0x00, 0x00, // ####
    0xF0, 0x00, 0x00, // ####
    0xF0, 0x00, 0x00, // ####
    0xF0, 0x00, 0x00, // ####
    0xF0, 0x00, 0x00, // ####
    0xF0, 0x00, 0x00, // ####
    0xF0, 0x00, 0x00, // ####
    0xF0, 0x00, 0x00, // ####
    0xFF, 0xFE, 0x00, // ###############
    0xFF, 0xFE, 0x00, // ###############
    0xFF, 0xFE, 0x00, // ###############
    0xF0, 0x00, 0x00, // ####
    0xF0, 0x00, 0x00, // ####
    0xF0, 0x00, 0x00, // ####
    0xF0, 0x00, 0x00, // ####
    0xF0, 0x00, 0x00, // ####
    0xF0, 0x00, 0x00, // ####
    0xF0, 0x00, 0x00, // ####
    0xF0, 0x00, 0x00, // ####
    0xF0, 0x00, 0x00, // ####
    0xF0, 0x00, 0x00, // ####
    0xF0, 0x00, 0x00, // ####
    0xF0, 0x00, 0x00, // ####
    0xF0, 0x00, 0x00, // ####
    0xF0, 0x00, 0x00, // ####
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //

    // @4700 'G' (24 pixels wide)
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x1F, 0xE0, //            ########
    0x00, 0xFF, 0xFC, //         ##############
    0x03, 0xFF, 0xFF, //       ##################
    0x07, 0xE0, 0x3F, //      ######       ######
    0x0F, 0x80, 0x07, //     #####            ###
    0x1F, 0x00, 0x03, //    #####              ##
    0x3E, 0x00, 0x01, //   #####                #
    0x3C, 0x00, 0x00, //   ####
    0x78, 0x00, 0x00, //  ####
    0x70, 0x00, 0x00, //  ###
    0x70, 0x00, 0x00, //  ###
    0xF0, 0x00, 0x00, // ####
    0xF0, 0x00, 0x00, // ####
    0xE0, 0x00, 0x00, // ###
    0xE0, 0x07, 0xFF, // ###          ###########
    0xE0, 0x07, 0xFF, // ###          ###########
    0xE0, 0x07, 0xFF, // ###          ###########
    0xE0, 0x00, 0x0F, // ###                 ####
    0xF0, 0x00, 0x0F, // ####                ####
    0xF0, 0x00, 0x0F, // ####                ####
    0x70, 0x00, 0x0F, //  ###                ####
    0x70, 0x00, 0x0F, //  ###                ####
    0x78, 0x00, 0x0F, //  ####               ####
    0x3C, 0x00, 0x0F, //   ####              ####
    0x3E, 0x00, 0x0F, //   #####             ####
    0x1F, 0x00, 0x0F, //    #####            ####
    0x0F, 0x80, 0x0F, //     #####           ####
    0x07, 0xF0, 0x3F, //      #######      ######
    0x03, 0xFF, 0xFF, //       ##################
    0x00, 0xFF, 0xFC, //         ##############
    0x00, 0x1F, 0xE0, //            ########
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //

    // @4841 'H' (23 pixels wide)
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0xF0, 0x00, 0x0E, // ####                ###
    0xF0, 0x00, 0x0E, // ####                ###
    0xF0, 0x00, 0x0E, // ####                ###
    0xF0, 0x00, 0x0E, // ####                ###
    0xF0, 0x00, 0x0E, // ####                ###
    0xF0, 0x00, 0x0E, // ####                ###
    0xF0, 0x00, 0x0E, // ####                ###
    0xF0, 0x00, 0x0E, // ####                ###
    0xF0, 0x00, 0x0E, // ####                ###
    0xF0, 0x00, 0x0E, // ####                ###
    0xF0, 0x00, 0x0E, // ####                ###
    0xF0, 0x00, 0x0E, // ####                ###
    0xF0, 0x00, 0x0E, // ####                ###
    0xF0, 0x00, 0x0E, // ####                ###
    0xFF, 0xFF, 0xFE, // #######################
    0xFF, 0xFF, 0xFE, // #######################
    0xFF, 0xFF, 0xFE, // #######################
    0xF0, 0x00, 0x0E, // ####                ###
    0xF0, 0x00, 0x0E, // ####                ###
    0xF0, 0x00, 0x0E, // ####                ###
    0xF0, 0x00, 0x0E, // ####                ###
    0xF0, 0x00, 0x0E, // ####                ###
    0xF0, 0x00, 0x0E, // ####                ###
    0xF0, 0x00, 0x0E, // ####                ###
    0xF0, 0x00, 0x0E, // ####                ###
    0xF0, 0x00, 0x0E, // ####                ###
    0xF0, 0x00, 0x0E, // ####                ###
    0xF0, 0x00, 0x0E, // ####                ###
    0xF0, 0x00, 0x0E, // ####                ###
    0xF0, 0x00, 0x0E, // ####                ###
    0xF0, 0x00, 0x0E, // ####                ###
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //

    // @4982 'I' (4 pixels wide)
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0xF0, // ####
    0xF0, // ####
    0xF0, // ####
    0xF0, // ####
    0xF0, // ####
    0xF0, // ####
    0xF0, // ####
    0xF0, // ####
    0xF0, // ####
    0xF0, // ####
    0xF0, // ####
    0xF0, // ####
    0xF0, // ####
    0xF0, // ####
    0xF0, // ####
    0xF0, // ####
    0xF0, // ####
    0xF0, // ####
    0xF0, // ####
    0xF0, // ####
    0xF0, // ####
    0xF0, // ####
    0xF0, // ####
    0xF0, // ####
    0xF0, // ####
    0xF0, // ####
    0xF0, // ####
    0xF0, // ####
    0xF0, // ####
    0xF0, // ####
    0xF0, // ####
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //

    // @5029 'J' (10 pixels wide)
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x03, 0xC0, //       ####
    0x03, 0xC0, //       ####
    0x03, 0xC0, //       ####
    0x03, 0xC0, //       ####
    0x03, 0xC0, //       ####
    0x03, 0xC0, //       ####
    0x03, 0xC0, //       ####
    0x03, 0xC0, //       ####
    0x03, 0xC0, //       ####
    0x03, 0xC0, //       ####
    0x03, 0xC0, //       ####
    0x03, 0xC0, //       ####
    0x03, 0xC0, //       ####
    0x03, 0xC0, //       ####
    0x03, 0xC0, //       ####
    0x03, 0xC0, //       ####
    0x03, 0xC0, //       ####
    0x03, 0xC0, //       ####
    0x03, 0xC0, //       ####
    0x03, 0xC0, //       ####
    0x03, 0xC0, //       ####
    0x03, 0xC0, //       ####
    0x03, 0xC0, //       ####
    0x03, 0xC0, //       ####
    0x03, 0xC0, //       ####
    0x03, 0xC0, //       ####
    0x03, 0x80, //       ###
    0x07, 0x80, //      ####
    0xFF, 0x00, // ########
    0xFE, 0x00, // #######
    0xFC, 0x00, // ######
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //

    // @5123 'K' (21 pixels wide)
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0xF0, 0x01, 0xF8, // ####           ######
    0xF0, 0x01, 0xE0, // ####           ####
    0xF0, 0x03, 0xC0, // ####          ####
    0xF0, 0x07, 0x80, // ####         ####
    0xF0, 0x0F, 0x00, // ####        ####
    0xF0, 0x1E, 0x00, // ####       ####
    0xF0, 0x3E, 0x00, // ####      #####
    0xF0, 0x7C, 0x00, // ####     #####
    0xF0, 0xF8, 0x00, // ####    #####
    0xF1, 0xF0, 0x00, // ####   #####
    0xF3, 0xE0, 0x00, // ####  #####
    0xF7, 0xC0, 0x00, // #### #####
    0xFF, 0x80, 0x00, // #########
    0xFF, 0x00, 0x00, // ########
    0xFF, 0x00, 0x00, // ########
    0xFF, 0x80, 0x00, // #########
    0xF7, 0xC0, 0x00, // #### #####
    0xF3, 0xE0, 0x00, // ####  #####
    0xF1, 0xF0, 0x00, // ####   #####
    0xF0, 0xF8, 0x00, // ####    #####
    0xF0, 0x7C, 0x00, // ####     #####
    0xF0, 0x3E, 0x00, // ####      #####
    0xF0, 0x1F, 0x00, // ####       #####
    0xF0, 0x0F, 0x80, // ####        #####
    0xF0, 0x07, 0xC0, // ####         #####
    0xF0, 0x03, 0xE0, // ####          #####
    0xF0, 0x01, 0xF0, // ####           #####
    0xF0, 0x00, 0xF8, // ####            #####
    0xF0, 0x00, 0x78, // ####             ####
    0xF0, 0x00, 0x38, // ####              ###
    0xF0, 0x00, 0x18, // ####               ##
    0x00, 0x00, 0x08, //                     #
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //

    // @5264 'L' (15 pixels wide)
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0xF0, 0x00, // ####
    0xF0, 0x00, // ####
    0xF0, 0x00, // ####
    0xF0, 0x00, // ####
    0xF0, 0x00, // ####
    0xF0, 0x00, // ####
    0xF0, 0x00, // ####
    0xF0, 0x00, // ####
    0xF0, 0x00, // ####
    0xF0, 0x00, // ####
    0xF0, 0x00, // ####
    0xF0, 0x00, // ####
    0xF0, 0x00, // ####
    0xF0, 0x00, // ####
    0xF0, 0x00, // ####
    0xF0, 0x00, // ####
    0xF0, 0x00, // ####
    0xF0, 0x00, // ####
    0xF0, 0x00, // ####
    0xF0, 0x00, // ####
    0xF0, 0x00, // ####
    0xF0, 0x00, // ####
    0xF0, 0x00, // ####
    0xF0, 0x00, // ####
    0xF0, 0x00, // ####
    0xF0, 0x00, // ####
    0xF0, 0x00, // ####
    0xF0, 0x00, // ####
    0xFF, 0xFE, // ###############
    0xFF, 0xFE, // ###############
    0xFF, 0xFE, // ###############
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //

    // @5358 'M' (30 pixels wide)
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0xF0, 0x00, 0x00, 0x3C, // ####                      ####
    0xF8, 0x00, 0x00, 0x7C, // #####                    #####
    0xF8, 0x00, 0x00, 0x7C, // #####                    #####
    0xFC, 0x00, 0x00, 0xFC, // ######                  ######
    0xFE, 0x00, 0x01, 0xFC, // #######                #######
    0xFE, 0x00, 0x01, 0xFC, // #######                #######
    0xFF, 0x00, 0x03, 0xDC, // ########              #### ###
    0xF7, 0x80, 0x03, 0x9C, // #### ####             ###  ###
    0xF7, 0x80, 0x07, 0x9C, // #### ####            ####  ###
    0xF3, 0xC0, 0x0F, 0x1C, // ####  ####          ####   ###
    0xF1, 0xE0, 0x0E, 0x1C, // ####   ####         ###    ###
    0xF1, 0xE0, 0x1E, 0x1C, // ####   ####        ####    ###
    0xF0, 0xF0, 0x3C, 0x1C, // ####    ####      ####     ###
    0xF0, 0x78, 0x3C, 0x1C, // ####     ####     ####     ###
    0xF0, 0x78, 0x78, 0x1C, // ####     ####    ####      ###
    0xF0, 0x3C, 0xF0, 0x1C, // ####      ####  ####       ###
    0xF0, 0x1E, 0xF0, 0x1C, // ####       #### ####       ###
    0xF0, 0x1F, 0xE0, 0x1C, // ####       ########        ###
    0xF0, 0x0F, 0xC0, 0x1C, // ####        ######         ###
    0xF0, 0x07, 0xC0, 0x1C, // ####         #####         ###
    0xF0, 0x07, 0x80, 0x1C, // ####         ####          ###
    0xF0, 0x03, 0x00, 0x1C, // ####          ##           ###
    0xF0, 0x03, 0x00, 0x1C, // ####          ##           ###
    0xF0, 0x00, 0x00, 0x1C, // ####                       ###
    0xF0, 0x00, 0x00, 0x1C, // ####                       ###
    0xF0, 0x00, 0x00, 0x1C, // ####                       ###
    0xF0, 0x00, 0x00, 0x1C, // ####                       ###
    0xF0, 0x00, 0x00, 0x1C, // ####                       ###
    0xF0, 0x00, 0x00, 0x1C, // ####                       ###
    0xF0, 0x00, 0x00, 0x1C, // ####                       ###
    0xF0, 0x00, 0x00, 0x1C, // ####                       ###
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //

    // @5546 'N' (25 pixels wide)
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0xF0, 0x00, 0x03, 0x80, // ####                  ###
    0xF8, 0x00, 0x03, 0x80, // #####                 ###
    0xF8, 0x00, 0x03, 0x80, // #####                 ###
    0xFC, 0x00, 0x03, 0x80, // ######                ###
    0xFE, 0x00, 0x03, 0x80, // #######               ###
    0xFE, 0x00, 0x03, 0x80, // #######               ###
    0xFF, 0x00, 0x03, 0x80, // ########              ###
    0xF7, 0x80, 0x03, 0x80, // #### ####             ###
    0xF7, 0xC0, 0x03, 0x80, // #### #####            ###
    0xF3, 0xC0, 0x03, 0x80, // ####  ####            ###
    0xF1, 0xE0, 0x03, 0x80, // ####   ####           ###
    0xF1, 0xF0, 0x03, 0x80, // ####   #####          ###
    0xF0, 0xF8, 0x03, 0x80, // ####    #####         ###
    0xF0, 0x78, 0x03, 0x80, // ####     ####         ###
    0xF0, 0x3C, 0x03, 0x80, // ####      ####        ###
    0xF0, 0x3E, 0x03, 0x80, // ####      #####       ###
    0xF0, 0x1E, 0x03, 0x80, // ####       ####       ###
    0xF0, 0x0F, 0x03, 0x80, // ####        ####      ###
    0xF0, 0x07, 0x83, 0x80, // ####         ####     ###
    0xF0, 0x07, 0xC3, 0x80, // ####         #####    ###
    0xF0, 0x03, 0xC3, 0x80, // ####          ####    ###
    0xF0, 0x01, 0xE3, 0x80, // ####           ####   ###
    0xF0, 0x01, 0xF3, 0x80, // ####           #####  ###
    0xF0, 0x00, 0xF3, 0x80, // ####            ####  ###
    0xF0, 0x00, 0x7B, 0x80, // ####             #### ###
    0xF0, 0x00, 0x3F, 0x80, // ####              #######
    0xF0, 0x00, 0x3F, 0x80, // ####              #######
    0xF0, 0x00, 0x1F, 0x80, // ####               ######
    0xF0, 0x00, 0x0F, 0x80, // ####                #####
    0xF0, 0x00, 0x07, 0x80, // ####                 ####
    0xF0, 0x00, 0x07, 0x80, // ####                 ####
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //

    // @5734 'O' (29 pixels wide)
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x1F, 0xE0, 0x00, //            ########
    0x00, 0xFF, 0xF8, 0x00, //         #############
    0x03, 0xFF, 0xFE, 0x00, //       #################
    0x07, 0xE0, 0x3F, 0x00, //      ######       ######
    0x0F, 0x80, 0x0F, 0x80, //     #####           #####
    0x1F, 0x00, 0x03, 0xC0, //    #####              ####
    0x1E, 0x00, 0x01, 0xE0, //    ####                ####
    0x3C, 0x00, 0x01, 0xE0, //   ####                 ####
    0x78, 0x00, 0x00, 0xF0, //  ####                   ####
    0x78, 0x00, 0x00, 0x70, //  ####                    ###
    0x70, 0x00, 0x00, 0x78, //  ###                     ####
    0xF0, 0x00, 0x00, 0x78, // ####                     ####
    0xF0, 0x00, 0x00, 0x38, // ####                      ###
    0xF0, 0x00, 0x00, 0x38, // ####                      ###
    0xF0, 0x00, 0x00, 0x38, // ####                      ###
    0xE0, 0x00, 0x00, 0x38, // ###                       ###
    0xF0, 0x00, 0x00, 0x38, // ####                      ###
    0xF0, 0x00, 0x00, 0x38, // ####                      ###
    0xF0, 0x00, 0x00, 0x38, // ####                      ###
    0xF0, 0x00, 0x00, 0x78, // ####                     ####
    0x70, 0x00, 0x00, 0x78, //  ###                     ####
    0x78, 0x00, 0x00, 0x70, //  ####                    ###
    0x38, 0x00, 0x00, 0xF0, //   ###                   ####
    0x3C, 0x00, 0x01, 0xE0, //   ####                 ####
    0x1E, 0x00, 0x01, 0xE0, //    ####                ####
    0x1F, 0x00, 0x03, 0xC0, //    #####              ####
    0x0F, 0x80, 0x0F, 0x80, //     #####           #####
    0x07, 0xE0, 0x3F, 0x00, //      ######       ######
    0x03, 0xFF, 0xFE, 0x00, //       #################
    0x00, 0xFF, 0xF8, 0x00, //         #############
    0x00, 0x1F, 0xE0, 0x00, //            ########
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //

    // @5922 'P' (19 pixels wide)
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0xFF, 0xFC, 0x00, // ##############
    0xFF, 0xFF, 0x00, // ################
    0xFF, 0xFF, 0x80, // #################
    0xF0, 0x07, 0xC0, // ####         #####
    0xF0, 0x03, 0xC0, // ####          ####
    0xF0, 0x01, 0xE0, // ####           ####
    0xF0, 0x00, 0xE0, // ####            ###
    0xF0, 0x00, 0xE0, // ####            ###
    0xF0, 0x00, 0xE0, // ####            ###
    0xF0, 0x00, 0xE0, // ####            ###
    0xF0, 0x00, 0xE0, // ####            ###
    0xF0, 0x01, 0xE0, // ####           ####
    0xF0, 0x03, 0xE0, // ####          #####
    0xF0, 0x07, 0xC0, // ####         #####
    0xFF, 0xFF, 0x80, // #################
    0xFF, 0xFF, 0x00, // ################
    0xFF, 0xFC, 0x00, // ##############
    0xF0, 0x00, 0x00, // ####
    0xF0, 0x00, 0x00, // ####
    0xF0, 0x00, 0x00, // ####
    0xF0, 0x00, 0x00, // ####
    0xF0, 0x00, 0x00, // ####
    0xF0, 0x00, 0x00, // ####
    0xF0, 0x00, 0x00, // ####
    0xF0, 0x00, 0x00, // ####
    0xF0, 0x00, 0x00, // ####
    0xF0, 0x00, 0x00, // ####
    0xF0, 0x00, 0x00, // ####
    0xF0, 0x00, 0x00, // ####
    0xF0, 0x00, 0x00, // ####
    0xF0, 0x00, 0x00, // ####
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //

    // @6063 'Q' (30 pixels wide)
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x1F, 0xE0, 0x00, //            ########
    0x00, 0xFF, 0xF8, 0x00, //         #############
    0x03, 0xFF, 0xFE, 0x00, //       #################
    0x07, 0xE0, 0x3F, 0x00, //      ######       ######
    0x0F, 0x80, 0x0F, 0x80, //     #####           #####
    0x1F, 0x00, 0x03, 0xC0, //    #####              ####
    0x1E, 0x00, 0x01, 0xE0, //    ####                ####
    0x3C, 0x00, 0x01, 0xE0, //   ####                 ####
    0x78, 0x00, 0x00, 0xF0, //  ####                   ####
    0x78, 0x00, 0x00, 0x70, //  ####                    ###
    0x70, 0x00, 0x00, 0x78, //  ###                     ####
    0xF0, 0x00, 0x00, 0x78, // ####                     ####
    0xF0, 0x00, 0x00, 0x38, // ####                      ###
    0xF0, 0x00, 0x00, 0x38, // ####                      ###
    0xF0, 0x00, 0x00, 0x38, // ####                      ###
    0xE0, 0x00, 0x00, 0x38, // ###                       ###
    0xF0, 0x00, 0x00, 0x38, // ####                      ###
    0xF0, 0x00, 0x00, 0x38, // ####                      ###
    0xF0, 0x00, 0x10, 0x38, // ####               #      ###
    0xF0, 0x00, 0x30, 0x78, // ####              ##     ####
    0x70, 0x00, 0x78, 0x78, //  ###             ####    ####
    0x78, 0x00, 0x3C, 0x70, //  ####             ####   ###
    0x38, 0x00, 0x1E, 0xF0, //   ###              #### ####
    0x3C, 0x00, 0x0F, 0xF0, //   ####              ########
    0x1E, 0x00, 0x0F, 0xE0, //    ####             #######
    0x1F, 0x00, 0x07, 0xE0, //    #####             ######
    0x0F, 0x80, 0x07, 0xE0, //     #####            ######
    0x07, 0xE0, 0x3F, 0xF0, //      ######       ##########
    0x03, 0xFF, 0xFE, 0xFC, //       ################# ######
    0x00, 0xFF, 0xFC, 0x7C, //         ##############   #####
    0x00, 0x1F, 0xE0, 0x1C, //            ########        ###
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //

    // @6251 'R' (20 pixels wide)
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0xFF, 0xFC, 0x00, // ##############
    0xFF, 0xFF, 0x00, // ################
    0xFF, 0xFF, 0x80, // #################
    0xF0, 0x07, 0xC0, // ####         #####
    0xF0, 0x03, 0xC0, // ####          ####
    0xF0, 0x01, 0xE0, // ####           ####
    0xF0, 0x00, 0xE0, // ####            ###
    0xF0, 0x00, 0xE0, // ####            ###
    0xF0, 0x00, 0xE0, // ####            ###
    0xF0, 0x00, 0xE0, // ####            ###
    0xF0, 0x00, 0xE0, // ####            ###
    0xF0, 0x01, 0xE0, // ####           ####
    0xF0, 0x03, 0xE0, // ####          #####
    0xF0, 0x07, 0xC0, // ####         #####
    0xFF, 0xFF, 0x80, // #################
    0xFF, 0xFF, 0x00, // ################
    0xFF, 0xFC, 0x00, // ##############
    0xF0, 0x78, 0x00, // ####     ####
    0xF0, 0x38, 0x00, // ####      ###
    0xF0, 0x3C, 0x00, // ####      ####
    0xF0, 0x1E, 0x00, // ####       ####
    0xF0, 0x1E, 0x00, // ####       ####
    0xF0, 0x0F, 0x00, // ####        ####
    0xF0, 0x0F, 0x00, // ####        ####
    0xF0, 0x07, 0x80, // ####         ####
    0xF0, 0x07, 0x80, // ####         ####
    0xF0, 0x03, 0xC0, // ####          ####
    0xF0, 0x03, 0xC0, // ####          ####
    0xF0, 0x01, 0xE0, // ####           ####
    0xF0, 0x01, 0xE0, // ####           ####
    0xF0, 0x00, 0xF0, // ####            ####
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //

    // @6392 'S' (21 pixels wide)
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x01, 0xFE, 0x00, //        ########
    0x07, 0xFF, 0xC0, //      #############
    0x0F, 0xFF, 0xE0, //     ###############
    0x1F, 0x03, 0xF0, //    #####      ######
    0x3C, 0x00, 0xF8, //   ####          #####
    0x3C, 0x00, 0x30, //   ####            ##
    0x38, 0x00, 0x20, //   ###             #
    0x38, 0x00, 0x00, //   ###
    0x38, 0x00, 0x00, //   ###
    0x3C, 0x00, 0x00, //   ####
    0x3E, 0x00, 0x00, //   #####
    0x1F, 0x00, 0x00, //    #####
    0x1F, 0xC0, 0x00, //    #######
    0x0F, 0xF8, 0x00, //     #########
    0x03, 0xFE, 0x00, //       #########
    0x00, 0xFF, 0x80, //         #########
    0x00, 0x1F, 0xC0, //            #######
    0x00, 0x07, 0xF0, //              #######
    0x00, 0x01, 0xF0, //                #####
    0x00, 0x00, 0xF8, //                 #####
    0x00, 0x00, 0x78, //                  ####
    0x00, 0x00, 0x78, //                  ####
    0x00, 0x00, 0x78, //                  ####
    0x20, 0x00, 0x38, //   #               ###
    0x60, 0x00, 0x78, //  ##              ####
    0xF0, 0x00, 0x78, // ####             ####
    0xFC, 0x00, 0xF0, // ######          ####
    0x7F, 0x03, 0xF0, //  #######      ######
    0x1F, 0xFF, 0xE0, //    ################
    0x0F, 0xFF, 0x80, //     #############
    0x01, 0xFE, 0x00, //        ########
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //

    // @6533 'T' (20 pixels wide)
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0xFF, 0xFF, 0xF0, // ####################
    0xFF, 0xFF, 0xF0, // ####################
    0xFF, 0xFF, 0xF0, // ####################
    0x00, 0xF0, 0x00, //         ####
    0x00, 0xF0, 0x00, //         ####
    0x00, 0xF0, 0x00, //         ####
    0x00, 0xF0, 0x00, //         ####
    0x00, 0xF0, 0x00, //         ####
    0x00, 0xF0, 0x00, //         ####
    0x00, 0xF0, 0x00, //         ####
    0x00, 0xF0, 0x00, //         ####
    0x00, 0xF0, 0x00, //         ####
    0x00, 0xF0, 0x00, //         ####
    0x00, 0xF0, 0x00, //         ####
    0x00, 0xF0, 0x00, //         ####
    0x00, 0xF0, 0x00, //         ####
    0x00, 0xF0, 0x00, //         ####
    0x00, 0xF0, 0x00, //         ####
    0x00, 0xF0, 0x00, //         ####
    0x00, 0xF0, 0x00, //         ####
    0x00, 0xF0, 0x00, //         ####
    0x00, 0xF0, 0x00, //         ####
    0x00, 0xF0, 0x00, //         ####
    0x00, 0xF0, 0x00, //         ####
    0x00, 0xF0, 0x00, //         ####
    0x00, 0xF0, 0x00, //         ####
    0x00, 0xF0, 0x00, //         ####
    0x00, 0xF0, 0x00, //         ####
    0x00, 0xF0, 0x00, //         ####
    0x00, 0xF0, 0x00, //         ####
    0x00, 0xF0, 0x00, //         ####
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //

    // @6674 'U' (23 pixels wide)
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0xF0, 0x00, 0x1E, // ####               ####
    0xF0, 0x00, 0x1E, // ####               ####
    0xF0, 0x00, 0x1E, // ####               ####
    0xF0, 0x00, 0x1E, // ####               ####
    0xF0, 0x00, 0x1E, // ####               ####
    0xF0, 0x00, 0x1E, // ####               ####
    0xF0, 0x00, 0x1E, // ####               ####
    0xF0, 0x00, 0x1E, // ####               ####
    0xF0, 0x00, 0x1E, // ####               ####
    0xF0, 0x00, 0x1E, // ####               ####
    0xF0, 0x00, 0x1E, // ####               ####
    0xF0, 0x00, 0x1E, // ####               ####
    0xF0, 0x00, 0x1E, // ####               ####
    0xF0, 0x00, 0x1E, // ####               ####
    0xF0, 0x00, 0x1E, // ####               ####
    0xF0, 0x00, 0x1E, // ####               ####
    0xF0, 0x00, 0x1E, // ####               ####
    0xF0, 0x00, 0x1E, // ####               ####
    0xF0, 0x00, 0x1E, // ####               ####
    0xF0, 0x00, 0x1E, // ####               ####
    0xF0, 0x00, 0x1E, // ####               ####
    0xF0, 0x00, 0x1E, // ####               ####
    0xF0, 0x00, 0x1E, // ####               ####
    0x78, 0x00, 0x1E, //  ####              ####
    0x78, 0x00, 0x3C, //  ####             ####
    0x3C, 0x00, 0x7C, //   ####           #####
    0x3E, 0x00, 0xF8, //   #####         #####
    0x1F, 0x83, 0xF0, //    ######     ######
    0x0F, 0xFF, 0xE0, //     ###############
    0x03, 0xFF, 0xC0, //       ############
    0x00, 0xFE, 0x00, //         #######
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //

    // @6815 'V' (28 pixels wide)
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0xF8, 0x00, 0x00, 0xF0, // #####                   ####
    0x78, 0x00, 0x00, 0xF0, //  ####                   ####
    0x78, 0x00, 0x01, 0xE0, //  ####                  ####
    0x3C, 0x00, 0x01, 0xE0, //   ####                 ####
    0x3C, 0x00, 0x01, 0xE0, //   ####                 ####
    0x3C, 0x00, 0x03, 0xC0, //   ####                ####
    0x1E, 0x00, 0x03, 0xC0, //    ####               ####
    0x1E, 0x00, 0x03, 0xC0, //    ####               ####
    0x0F, 0x00, 0x07, 0x80, //     ####             ####
    0x0F, 0x00, 0x07, 0x80, //     ####             ####
    0x0F, 0x00, 0x0F, 0x00, //     ####            ####
    0x07, 0x80, 0x0F, 0x00, //      ####           ####
    0x07, 0x80, 0x0F, 0x00, //      ####           ####
    0x07, 0x80, 0x1E, 0x00, //      ####          ####
    0x03, 0xC0, 0x1E, 0x00, //       ####         ####
    0x03, 0xC0, 0x1C, 0x00, //       ####         ###
    0x01, 0xE0, 0x3C, 0x00, //        ####       ####
    0x01, 0xE0, 0x3C, 0x00, //        ####       ####
    0x01, 0xE0, 0x78, 0x00, //        ####      ####
    0x00, 0xF0, 0x78, 0x00, //         ####     ####
    0x00, 0xF0, 0x70, 0x00, //         ####     ###
    0x00, 0x70, 0xF0, 0x00, //          ###    ####
    0x00, 0x78, 0xF0, 0x00, //          ####   ####
    0x00, 0x78, 0xE0, 0x00, //          ####   ###
    0x00, 0x3D, 0xE0, 0x00, //           #### ####
    0x00, 0x3D, 0xE0, 0x00, //           #### ####
    0x00, 0x1F, 0xC0, 0x00, //            #######
    0x00, 0x1F, 0xC0, 0x00, //            #######
    0x00, 0x1F, 0x80, 0x00, //            ######
    0x00, 0x0F, 0x80, 0x00, //             #####
    0x00, 0x0F, 0x80, 0x00, //             #####
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //

    // @7003 'W' (44 pixels wide)
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //
    0xF0, 0x03, 0xC0, 0x00, 0x00, 0xF0, // ####          ####                      ####
    0xF0, 0x01, 0xE0, 0x00, 0x01, 0xE0, // ####           ####                    ####
    0x78, 0x01, 0xE0, 0x00, 0x01, 0xE0, //  ####          ####                    ####
    0x78, 0x00, 0xF0, 0x00, 0x03, 0xC0, //  ####           ####                  ####
    0x78, 0x00, 0xF0, 0x00, 0x03, 0xC0, //  ####           ####                  ####
    0x3C, 0x00, 0x70, 0x00, 0x03, 0x80, //   ####           ###                  ###
    0x3C, 0x00, 0x78, 0x00, 0x07, 0x80, //   ####           ####                ####
    0x1E, 0x00, 0x78, 0x00, 0x07, 0x80, //    ####          ####                ####
    0x1E, 0x00, 0x3C, 0x00, 0x0F, 0x00, //    ####           ####              ####
    0x1E, 0x00, 0x3C, 0x00, 0x0F, 0x00, //    ####           ####              ####
    0x0F, 0x00, 0x1C, 0x00, 0x0E, 0x00, //     ####           ###              ###
    0x0F, 0x00, 0x1E, 0x00, 0x1E, 0x00, //     ####           ####            ####
    0x07, 0x80, 0x0E, 0x00, 0x1E, 0x00, //      ####           ###            ####
    0x07, 0x80, 0x0F, 0x00, 0x3C, 0x00, //      ####           ####          ####
    0x03, 0x80, 0x1F, 0x00, 0x3C, 0x00, //       ###          #####          ####
    0x03, 0xC0, 0x1F, 0x00, 0x78, 0x00, //       ####         #####         ####
    0x03, 0xC0, 0x1F, 0x80, 0x78, 0x00, //       ####         ######        ####
    0x01, 0xE0, 0x3F, 0x80, 0x78, 0x00, //        ####       #######        ####
    0x01, 0xE0, 0x3B, 0xC0, 0xF0, 0x00, //        ####       ### ####      ####
    0x00, 0xF0, 0x7B, 0xC0, 0xF0, 0x00, //         ####     #### ####      ####
    0x00, 0xF0, 0x79, 0xC1, 0xE0, 0x00, //         ####     ####  ###     ####
    0x00, 0xF0, 0xF1, 0xE1, 0xE0, 0x00, //         ####    ####   ####    ####
    0x00, 0x78, 0xF0, 0xE1, 0xE0, 0x00, //          ####   ####    ###    ####
    0x00, 0x78, 0xE0, 0xF3, 0xC0, 0x00, //          ####   ###     ####  ####
    0x00, 0x3D, 0xE0, 0xF3, 0xC0, 0x00, //           #### ####     ####  ####
    0x00, 0x3D, 0xE0, 0x7F, 0x80, 0x00, //           #### ####      ########
    0x00, 0x3F, 0xC0, 0x7F, 0x80, 0x00, //           ########       ########
    0x00, 0x1F, 0xC0, 0x3F, 0x80, 0x00, //            #######        #######
    0x00, 0x1F, 0x80, 0x3F, 0x00, 0x00, //            ######         ######
    0x00, 0x0F, 0x80, 0x1F, 0x00, 0x00, //             #####          #####
    0x00, 0x0F, 0x00, 0x1E, 0x00, 0x00, //             ####           ####
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //

    // @7285 'X' (24 pixels wide)
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0xF8, 0x00, 0x1F, // #####              #####
    0x78, 0x00, 0x1E, //  ####              ####
    0x3C, 0x00, 0x3C, //   ####            ####
    0x3C, 0x00, 0x7C, //   ####           #####
    0x1E, 0x00, 0x78, //    ####          ####
    0x0F, 0x00, 0xF0, //     ####        ####
    0x0F, 0x00, 0xF0, //     ####        ####
    0x07, 0x81, 0xE0, //      ####      ####
    0x03, 0xC3, 0xC0, //       ####    ####
    0x03, 0xC3, 0xC0, //       ####    ####
    0x01, 0xE7, 0x80, //        ####  ####
    0x01, 0xEF, 0x00, //        #### ####
    0x00, 0xFF, 0x00, //         ########
    0x00, 0x7E, 0x00, //          ######
    0x00, 0x7C, 0x00, //          #####
    0x00, 0x3C, 0x00, //           ####
    0x00, 0x7E, 0x00, //          ######
    0x00, 0x7E, 0x00, //          ######
    0x00, 0xFF, 0x00, //         ########
    0x01, 0xE7, 0x80, //        ####  ####
    0x01, 0xE7, 0x80, //        ####  ####
    0x03, 0xC3, 0xC0, //       ####    ####
    0x07, 0x81, 0xE0, //      ####      ####
    0x07, 0x81, 0xE0, //      ####      ####
    0x0F, 0x00, 0xF0, //     ####        ####
    0x1E, 0x00, 0xF8, //    ####         #####
    0x1E, 0x00, 0x78, //    ####          ####
    0x3C, 0x00, 0x3C, //   ####            ####
    0x7C, 0x00, 0x3E, //  #####            #####
    0x78, 0x00, 0x1E, //  ####              ####
    0xF0, 0x00, 0x0F, // ####                ####
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //

    // @7426 'Y' (24 pixels wide)
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0xF0, 0x00, 0x0F, // ####                ####
    0x78, 0x00, 0x1E, //  ####              ####
    0x78, 0x00, 0x1E, //  ####              ####
    0x3C, 0x00, 0x3C, //   ####            ####
    0x1E, 0x00, 0x78, //    ####          ####
    0x1E, 0x00, 0x78, //    ####          ####
    0x0F, 0x00, 0xF0, //     ####        ####
    0x07, 0x80, 0xF0, //      ####       ####
    0x07, 0x81, 0xE0, //      ####      ####
    0x03, 0xC3, 0xC0, //       ####    ####
    0x03, 0xC3, 0xC0, //       ####    ####
    0x01, 0xE7, 0x80, //        ####  ####
    0x00, 0xF7, 0x00, //         #### ###
    0x00, 0xFF, 0x00, //         ########
    0x00, 0x7E, 0x00, //          ######
    0x00, 0x3C, 0x00, //           ####
    0x00, 0x3C, 0x00, //           ####
    0x00, 0x3C, 0x00, //           ####
    0x00, 0x3C, 0x00, //           ####
    0x00, 0x3C, 0x00, //           ####
    0x00, 0x3C, 0x00, //           ####
    0x00, 0x3C, 0x00, //           ####
    0x00, 0x3C, 0x00, //           ####
    0x00, 0x3C, 0x00, //           ####
    0x00, 0x3C, 0x00, //           ####
    0x00, 0x3C, 0x00, //           ####
    0x00, 0x3C, 0x00, //           ####
    0x00, 0x3C, 0x00, //           ####
    0x00, 0x3C, 0x00, //           ####
    0x00, 0x3C, 0x00, //           ####
    0x00, 0x3C, 0x00, //           ####
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //

    // @7567 'Z' (23 pixels wide)
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0xFF, 0xFF, 0xFC, // ######################
    0xFF, 0xFF, 0xFC, // ######################
    0xFF, 0xFF, 0xFC, // ######################
    0x00, 0x00, 0x78, //                  ####
    0x00, 0x00, 0xF0, //                 ####
    0x00, 0x01, 0xF0, //                #####
    0x00, 0x01, 0xE0, //                ####
    0x00, 0x03, 0xC0, //               ####
    0x00, 0x07, 0xC0, //              #####
    0x00, 0x07, 0x80, //              ####
    0x00, 0x0F, 0x00, //             ####
    0x00, 0x1F, 0x00, //            #####
    0x00, 0x1E, 0x00, //            ####
    0x00, 0x3C, 0x00, //           ####
    0x00, 0x7C, 0x00, //          #####
    0x00, 0x78, 0x00, //          ####
    0x00, 0xF0, 0x00, //         ####
    0x00, 0xF0, 0x00, //         ####
    0x01, 0xE0, 0x00, //        ####
    0x03, 0xC0, 0x00, //       ####
    0x03, 0xC0, 0x00, //       ####
    0x07, 0x80, 0x00, //      ####
    0x0F, 0x00, 0x00, //     ####
    0x0F, 0x00, 0x00, //     ####
    0x1E, 0x00, 0x00, //    ####
    0x3C, 0x00, 0x00, //   ####
    0x3C, 0x00, 0x00, //   ####
    0x78, 0x00, 0x00, //  ####
    0xFF, 0xFF, 0xFE, // #######################
    0xFF, 0xFF, 0xFE, // #######################
    0xFF, 0xFF, 0xFE, // #######################
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //

    // @7708 '[' (8 pixels wide)
    0x00, //
    0xFF, // ########
    0xFF, // ########
    0xFF, // ########
    0xE0, // ###
    0xE0, // ###
    0xE0, // ###
    0xE0, // ###
    0xE0, // ###
    0xE0, // ###
    0xE0, // ###
    0xE0, // ###
    0xE0, // ###
    0xE0, // ###
    0xE0, // ###
    0xE0, // ###
    0xE0, // ###
    0xE0, // ###
    0xE0, // ###
    0xE0, // ###
    0xE0, // ###
    0xE0, // ###
    0xE0, // ###
    0xE0, // ###
    0xE0, // ###
    0xE0, // ###
    0xE0, // ###
    0xE0, // ###
    0xE0, // ###
    0xE0, // ###
    0xE0, // ###
    0xE0, // ###
    0xE0, // ###
    0xE0, // ###
    0xE0, // ###
    0xE0, // ###
    0xE0, // ###
    0xE0, // ###
    0xE0, // ###
    0xE0, // ###
    0xE0, // ###
    0xE0, // ###
    0xE0, // ###
    0xE0, // ###
    0xE0, // ###
    0xE0, // ###
    0xFF, // ########

    // @7755 '\' (25 pixels wide)
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0xF0, 0x00, 0x00, 0x00, // ####
    0x70, 0x00, 0x00, 0x00, //  ###
    0x38, 0x00, 0x00, 0x00, //   ###
    0x3C, 0x00, 0x00, 0x00, //   ####
    0x1C, 0x00, 0x00, 0x00, //    ###
    0x1E, 0x00, 0x00, 0x00, //    ####
    0x0E, 0x00, 0x00, 0x00, //     ###
    0x0F, 0x00, 0x00, 0x00, //     ####
    0x07, 0x00, 0x00, 0x00, //      ###
    0x07, 0x80, 0x00, 0x00, //      ####
    0x03, 0x80, 0x00, 0x00, //       ###
    0x03, 0xC0, 0x00, 0x00, //       ####
    0x01, 0xC0, 0x00, 0x00, //        ###
    0x01, 0xE0, 0x00, 0x00, //        ####
    0x00, 0xE0, 0x00, 0x00, //         ###
    0x00, 0xF0, 0x00, 0x00, //         ####
    0x00, 0x70, 0x00, 0x00, //          ###
    0x00, 0x78, 0x00, 0x00, //          ####
    0x00, 0x38, 0x00, 0x00, //           ###
    0x00, 0x3C, 0x00, 0x00, //           ####
    0x00, 0x1C, 0x00, 0x00, //            ###
    0x00, 0x1E, 0x00, 0x00, //            ####
    0x00, 0x0E, 0x00, 0x00, //             ###
    0x00, 0x0F, 0x00, 0x00, //             ####
    0x00, 0x07, 0x00, 0x00, //              ###
    0x00, 0x07, 0x80, 0x00, //              ####
    0x00, 0x03, 0x80, 0x00, //               ###
    0x00, 0x03, 0xC0, 0x00, //               ####
    0x00, 0x01, 0xC0, 0x00, //                ###
    0x00, 0x00, 0xE0, 0x00, //                 ###
    0x00, 0x00, 0xE0, 0x00, //                 ###
    0x00, 0x00, 0x70, 0x00, //                  ###
    0x00, 0x00, 0x70, 0x00, //                  ###
    0x00, 0x00, 0x38, 0x00, //                   ###
    0x00, 0x00, 0x38, 0x00, //                   ###
    0x00, 0x00, 0x1C, 0x00, //                    ###
    0x00, 0x00, 0x1C, 0x00, //                    ###
    0x00, 0x00, 0x0E, 0x00, //                     ###
    0x00, 0x00, 0x0F, 0x00, //                     ####
    0x00, 0x00, 0x07, 0x00, //                      ###
    0x00, 0x00, 0x07, 0x80, //                      ####
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //

    // @7943 ']' (8 pixels wide)
    0x00, //
    0xFF, // ########
    0xFF, // ########
    0xFF, // ########
    0x07, //      ###
    0x07, //      ###
    0x07, //      ###
    0x07, //      ###
    0x07, //      ###
    0x07, //      ###
    0x07, //      ###
    0x07, //      ###
    0x07, //      ###
    0x07, //      ###
    0x07, //      ###
    0x07, //      ###
    0x07, //      ###
    0x07, //      ###
    0x07, //      ###
    0x07, //      ###
    0x07, //      ###
    0x07, //      ###
    0x07, //      ###
    0x07, //      ###
    0x07, //      ###
    0x07, //      ###
    0x07, //      ###
    0x07, //      ###
    0x07, //      ###
    0x07, //      ###
    0x07, //      ###
    0x07, //      ###
    0x07, //      ###
    0x07, //      ###
    0x07, //      ###
    0x07, //      ###
    0x07, //      ###
    0x07, //      ###
    0x07, //      ###
    0x07, //      ###
    0x07, //      ###
    0x07, //      ###
    0x07, //      ###
    0x07, //      ###
    0x07, //      ###
    0x07, //      ###
    0xFF, // ########

    // @7990 '^' (26 pixels wide)
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x1E, 0x00, 0x00, //            ####
    0x00, 0x1E, 0x00, 0x00, //            ####
    0x00, 0x3F, 0x00, 0x00, //           ######
    0x00, 0x3F, 0x00, 0x00, //           ######
    0x00, 0x77, 0x80, 0x00, //          ### ####
    0x00, 0x73, 0x80, 0x00, //          ###  ###
    0x00, 0xE3, 0xC0, 0x00, //         ###   ####
    0x00, 0xE1, 0xC0, 0x00, //         ###    ###
    0x01, 0xC1, 0xE0, 0x00, //        ###     ####
    0x01, 0xC0, 0xE0, 0x00, //        ###      ###
    0x03, 0x80, 0xF0, 0x00, //       ###       ####
    0x03, 0x80, 0x70, 0x00, //       ###        ###
    0x07, 0x00, 0x70, 0x00, //      ###         ###
    0x07, 0x00, 0x38, 0x00, //      ###          ###
    0x0E, 0x00, 0x38, 0x00, //     ###           ###
    0x0E, 0x00, 0x1C, 0x00, //     ###            ###
    0x1C, 0x00, 0x1C, 0x00, //    ###             ###
    0x1C, 0x00, 0x0E, 0x00, //    ###              ###
    0x38, 0x00, 0x0E, 0x00, //   ###               ###
    0x38, 0x00, 0x07, 0x00, //   ###                ###
    0x70, 0x00, 0x07, 0x00, //  ###                 ###
    0x70, 0x00, 0x03, 0x80, //  ###                  ###
    0xE0, 0x00, 0x03, 0xC0, // ###                   ####
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //

    // @8178 '_' (20 pixels wide)
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0xFF, 0xFF, 0xF0, // ####################
    0xFF, 0xFF, 0xF0, // ####################
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //

    // @8319 '`' (7 pixels wide)
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0xF0, // ####
    0x70, //  ###
    0x38, //   ###
    0x3C, //   ####
    0x1C, //    ###
    0x0E, //     ###
    0x0E, //     ###
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //

    // @8366 'a' (17 pixels wide)
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x07, 0xF0, 0x00, //      #######
    0x1F, 0xFC, 0x00, //    ###########
    0x7F, 0xFE, 0x00, //  ##############
    0xFC, 0x1F, 0x00, // ######     #####
    0x70, 0x0F, 0x00, //  ###        ####
    0x20, 0x07, 0x00, //   #          ###
    0x00, 0x07, 0x80, //              ####
    0x00, 0x07, 0x80, //              ####
    0x00, 0x07, 0x80, //              ####
    0x0F, 0xC7, 0x80, //     ######   ####
    0x3F, 0xFF, 0x80, //   ###############
    0x7F, 0xFF, 0x80, //  ################
    0xF8, 0x07, 0x80, // #####        ####
    0xF0, 0x07, 0x80, // ####         ####
    0xE0, 0x07, 0x80, // ###          ####
    0xE0, 0x07, 0x80, // ###          ####
    0xF0, 0x1F, 0x80, // ####       ######
    0xF8, 0x7F, 0x80, // #####    ########
    0x7F, 0xFF, 0x80, //  ################
    0x3F, 0xE7, 0x80, //   #########  ####
    0x1F, 0x87, 0x80, //    ######    ####
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //

    // @8507 'b' (19 pixels wide)
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0xE0, 0x00, 0x00, // ###
    0xE0, 0x00, 0x00, // ###
    0xE0, 0x00, 0x00, // ###
    0xE0, 0x00, 0x00, // ###
    0xE0, 0x00, 0x00, // ###
    0xE0, 0x00, 0x00, // ###
    0xE0, 0x00, 0x00, // ###
    0xE0, 0x00, 0x00, // ###
    0xE0, 0x00, 0x00, // ###
    0xE0, 0x00, 0x00, // ###
    0xE1, 0xF8, 0x00, // ###    ######
    0xE7, 0xFE, 0x00, // ###  ##########
    0xEF, 0xFF, 0x00, // ### ############
    0xFE, 0x0F, 0x80, // #######     #####
    0xFC, 0x07, 0xC0, // ######       #####
    0xF8, 0x03, 0xC0, // #####         ####
    0xF0, 0x01, 0xE0, // ####           ####
    0xF0, 0x01, 0xE0, // ####           ####
    0xE0, 0x00, 0xE0, // ###             ###
    0xE0, 0x00, 0xE0, // ###             ###
    0xE0, 0x00, 0xE0, // ###             ###
    0xE0, 0x00, 0xE0, // ###             ###
    0xE0, 0x00, 0xE0, // ###             ###
    0xF0, 0x01, 0xE0, // ####           ####
    0xF0, 0x01, 0xE0, // ####           ####
    0xF8, 0x03, 0xC0, // #####         ####
    0xFC, 0x07, 0xC0, // ######       #####
    0xFE, 0x0F, 0x80, // #######     #####
    0xEF, 0xFF, 0x00, // ### ############
    0xE7, 0xFE, 0x00, // ###  ##########
    0xE1, 0xF8, 0x00, // ###    ######
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //

    // @8648 'c' (14 pixels wide)
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x03, 0xF0, //       ######
    0x0F, 0xFC, //     ##########
    0x1F, 0xFC, //    ###########
    0x3E, 0x0C, //   #####     ##
    0x78, 0x04, //  ####        #
    0x70, 0x00, //  ###
    0xF0, 0x00, // ####
    0xF0, 0x00, // ####
    0xE0, 0x00, // ###
    0xE0, 0x00, // ###
    0xE0, 0x00, // ###
    0xE0, 0x00, // ###
    0xE0, 0x00, // ###
    0xF0, 0x00, // ####
    0xF0, 0x00, // ####
    0x70, 0x00, //  ###
    0x78, 0x04, //  ####        #
    0x3E, 0x0C, //   #####     ##
    0x1F, 0xFC, //    ###########
    0x0F, 0xFC, //     ##########
    0x03, 0xF0, //       ######
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //

    // @8742 'd' (19 pixels wide)
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x01, 0xE0, //                ####
    0x00, 0x01, 0xE0, //                ####
    0x00, 0x01, 0xE0, //                ####
    0x00, 0x01, 0xE0, //                ####
    0x00, 0x01, 0xE0, //                ####
    0x00, 0x01, 0xE0, //                ####
    0x00, 0x01, 0xE0, //                ####
    0x00, 0x01, 0xE0, //                ####
    0x00, 0x01, 0xE0, //                ####
    0x00, 0x01, 0xE0, //                ####
    0x03, 0xF1, 0xE0, //       ######   ####
    0x0F, 0xFD, 0xE0, //     ########## ####
    0x1F, 0xFF, 0xE0, //    ################
    0x3E, 0x0F, 0xE0, //   #####     #######
    0x78, 0x07, 0xE0, //  ####        ######
    0x78, 0x03, 0xE0, //  ####         #####
    0xF0, 0x01, 0xE0, // ####           ####
    0xF0, 0x01, 0xE0, // ####           ####
    0xE0, 0x01, 0xE0, // ###            ####
    0xE0, 0x01, 0xE0, // ###            ####
    0xE0, 0x01, 0xE0, // ###            ####
    0xE0, 0x01, 0xE0, // ###            ####
    0xE0, 0x01, 0xE0, // ###            ####
    0xF0, 0x01, 0xE0, // ####           ####
    0xF0, 0x01, 0xE0, // ####           ####
    0x70, 0x03, 0xE0, //  ###          #####
    0x78, 0x07, 0xE0, //  ####        ######
    0x3E, 0x0F, 0xE0, //   #####     #######
    0x1F, 0xFE, 0xE0, //    ############ ###
    0x0F, 0xFC, 0xE0, //     ##########  ###
    0x03, 0xF0, 0xE0, //       ######    ###
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //

    // @8883 'e' (18 pixels wide)
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x03, 0xF0, 0x00, //       ######
    0x0F, 0xFC, 0x00, //     ##########
    0x1F, 0xFE, 0x00, //    ############
    0x3E, 0x0F, 0x00, //   #####     ####
    0x78, 0x07, 0x80, //  ####        ####
    0x70, 0x03, 0x80, //  ###          ###
    0xF0, 0x03, 0xC0, // ####          ####
    0xE0, 0x01, 0xC0, // ###            ###
    0xE0, 0x01, 0xC0, // ###            ###
    0xFF, 0xFF, 0xC0, // ##################
    0xFF, 0xFF, 0xC0, // ##################
    0xFF, 0xFF, 0xC0, // ##################
    0xE0, 0x00, 0x00, // ###
    0xE0, 0x00, 0x00, // ###
    0xF0, 0x00, 0x00, // ####
    0x70, 0x03, 0x00, //  ###          ##
    0x78, 0x07, 0x80, //  ####        ####
    0x3E, 0x0F, 0x80, //   #####     #####
    0x1F, 0xFF, 0x00, //    #############
    0x0F, 0xFE, 0x00, //     ###########
    0x03, 0xF0, 0x00, //       ######
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //

    // @9024 'f' (14 pixels wide)
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x01, 0xF0, //        #####
    0x07, 0xF8, //      ########
    0x0F, 0xFC, //     ##########
    0x1F, 0x10, //    #####   #
    0x1E, 0x00, //    ####
    0x1E, 0x00, //    ####
    0x1E, 0x00, //    ####
    0x1E, 0x00, //    ####
    0x1E, 0x00, //    ####
    0x1E, 0x00, //    ####
    0xFF, 0xF0, // ############
    0xFF, 0xF0, // ############
    0xFF, 0xF0, // ############
    0x1E, 0x00, //    ####
    0x1E, 0x00, //    ####
    0x1E, 0x00, //    ####
    0x1E, 0x00, //    ####
    0x1E, 0x00, //    ####
    0x1E, 0x00, //    ####
    0x1E, 0x00, //    ####
    0x1E, 0x00, //    ####
    0x1E, 0x00, //    ####
    0x1E, 0x00, //    ####
    0x1E, 0x00, //    ####
    0x1E, 0x00, //    ####
    0x1E, 0x00, //    ####
    0x1E, 0x00, //    ####
    0x1E, 0x00, //    ####
    0x1E, 0x00, //    ####
    0x1E, 0x00, //    ####
    0x1E, 0x00, //    ####
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //

    // @9118 'g' (19 pixels wide)
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x03, 0xF0, 0xE0, //       ######    ###
    0x0F, 0xFC, 0xE0, //     ##########  ###
    0x1F, 0xFE, 0xE0, //    ############ ###
    0x3E, 0x0F, 0xE0, //   #####     #######
    0x78, 0x07, 0xE0, //  ####        ######
    0x78, 0x03, 0xE0, //  ####         #####
    0xF0, 0x01, 0xE0, // ####           ####
    0xF0, 0x01, 0xE0, // ####           ####
    0xE0, 0x01, 0xE0, // ###            ####
    0xE0, 0x01, 0xE0, // ###            ####
    0xE0, 0x01, 0xE0, // ###            ####
    0xE0, 0x01, 0xE0, // ###            ####
    0xE0, 0x01, 0xE0, // ###            ####
    0xF0, 0x01, 0xE0, // ####           ####
    0xF0, 0x01, 0xE0, // ####           ####
    0x78, 0x03, 0xE0, //  ####         #####
    0x78, 0x07, 0xE0, //  ####        ######
    0x3E, 0x0F, 0xE0, //   #####     #######
    0x1F, 0xFF, 0xE0, //    ################
    0x0F, 0xFD, 0xE0, //     ########## ####
    0x03, 0xF1, 0xE0, //       ######   ####
    0x00, 0x01, 0xE0, //                ####
    0x00, 0x01, 0xE0, //                ####
    0x00, 0x01, 0xE0, //                ####
    0x00, 0x01, 0xC0, //                ###
    0x10, 0x03, 0xC0, //    #          ####
    0x18, 0x07, 0x80, //    ##        ####
    0x1E, 0x0F, 0x80, //    ####     #####
    0x1F, 0xFF, 0x00, //    #############
    0x0F, 0xFE, 0x00, //     ###########
    0x03, 0xF8, 0x00, //       #######
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //

    // @9259 'h' (18 pixels wide)
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0xF0, 0x00, 0x00, // ####
    0xF0, 0x00, 0x00, // ####
    0xF0, 0x00, 0x00, // ####
    0xF0, 0x00, 0x00, // ####
    0xF0, 0x00, 0x00, // ####
    0xF0, 0x00, 0x00, // ####
    0xF0, 0x00, 0x00, // ####
    0xF0, 0x00, 0x00, // ####
    0xF0, 0x00, 0x00, // ####
    0xF0, 0x00, 0x00, // ####
    0xF1, 0xF8, 0x00, // ####   ######
    0xF7, 0xFE, 0x00, // #### ##########
    0xFF, 0xFF, 0x00, // ################
    0xFE, 0x1F, 0x00, // #######    #####
    0xF8, 0x07, 0x80, // #####        ####
    0xF0, 0x07, 0x80, // ####         ####
    0xF0, 0x03, 0xC0, // ####          ####
    0xF0, 0x03, 0xC0, // ####          ####
    0xF0, 0x03, 0xC0, // ####          ####
    0xF0, 0x03, 0xC0, // ####          ####
    0xF0, 0x03, 0xC0, // ####          ####
    0xF0, 0x03, 0xC0, // ####          ####
    0xF0, 0x03, 0xC0, // ####          ####
    0xF0, 0x03, 0xC0, // ####          ####
    0xF0, 0x03, 0xC0, // ####          ####
    0xF0, 0x03, 0xC0, // ####          ####
    0xF0, 0x03, 0xC0, // ####          ####
    0xF0, 0x03, 0xC0, // ####          ####
    0xF0, 0x03, 0xC0, // ####          ####
    0xF0, 0x03, 0xC0, // ####          ####
    0xF0, 0x03, 0xC0, // ####          ####
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //

    // @9400 'i' (5 pixels wide)
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x70, //  ###
    0xF8, // #####
    0xF8, // #####
    0xF8, // #####
    0x70, //  ###
    0x00, //
    0x00, //
    0x00, //
    0x70, //  ###
    0x70, //  ###
    0x70, //  ###
    0x70, //  ###
    0x70, //  ###
    0x70, //  ###
    0x70, //  ###
    0x70, //  ###
    0x70, //  ###
    0x70, //  ###
    0x70, //  ###
    0x70, //  ###
    0x70, //  ###
    0x70, //  ###
    0x70, //  ###
    0x70, //  ###
    0x70, //  ###
    0x70, //  ###
    0x70, //  ###
    0x70, //  ###
    0x70, //  ###
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //

    // @9447 'j' (9 pixels wide)
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x03, 0x00, //       ##
    0x07, 0x80, //      ####
    0x07, 0x80, //      ####
    0x07, 0x80, //      ####
    0x03, 0x00, //       ##
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x07, 0x80, //      ####
    0x07, 0x80, //      ####
    0x07, 0x80, //      ####
    0x07, 0x80, //      ####
    0x07, 0x80, //      ####
    0x07, 0x80, //      ####
    0x07, 0x80, //      ####
    0x07, 0x80, //      ####
    0x07, 0x80, //      ####
    0x07, 0x80, //      ####
    0x07, 0x80, //      ####
    0x07, 0x80, //      ####
    0x07, 0x80, //      ####
    0x07, 0x80, //      ####
    0x07, 0x80, //      ####
    0x07, 0x80, //      ####
    0x07, 0x80, //      ####
    0x07, 0x80, //      ####
    0x07, 0x80, //      ####
    0x07, 0x80, //      ####
    0x07, 0x80, //      ####
    0x07, 0x80, //      ####
    0x07, 0x80, //      ####
    0x07, 0x80, //      ####
    0x07, 0x80, //      ####
    0x07, 0x80, //      ####
    0x07, 0x80, //      ####
    0x8F, 0x00, // #   ####
    0xFF, 0x00, // ########
    0xFE, 0x00, // #######
    0xF8, 0x00, // #####
    0x00, 0x00, //
    0x00, 0x00, //

    // @9541 'k' (17 pixels wide)
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0xF0, 0x00, 0x00, // ####
    0xF0, 0x00, 0x00, // ####
    0xF0, 0x00, 0x00, // ####
    0xF0, 0x00, 0x00, // ####
    0xF0, 0x00, 0x00, // ####
    0xF0, 0x00, 0x00, // ####
    0xF0, 0x00, 0x00, // ####
    0xF0, 0x00, 0x00, // ####
    0xF0, 0x00, 0x00, // ####
    0xF0, 0x00, 0x00, // ####
    0xF0, 0x1F, 0x00, // ####       #####
    0xF0, 0x3E, 0x00, // ####      #####
    0xF0, 0x7C, 0x00, // ####     #####
    0xF0, 0xF8, 0x00, // ####    #####
    0xF1, 0xF0, 0x00, // ####   #####
    0xF3, 0xE0, 0x00, // ####  #####
    0xF7, 0xC0, 0x00, // #### #####
    0xFF, 0x80, 0x00, // #########
    0xFF, 0x00, 0x00, // ########
    0xFE, 0x00, 0x00, // #######
    0xFF, 0x00, 0x00, // ########
    0xFF, 0x80, 0x00, // #########
    0xF3, 0xC0, 0x00, // ####  ####
    0xF1, 0xE0, 0x00, // ####   ####
    0xF0, 0xF0, 0x00, // ####    ####
    0xF0, 0x7C, 0x00, // ####     #####
    0xF0, 0x3E, 0x00, // ####      #####
    0xF0, 0x1F, 0x00, // ####       #####
    0xF0, 0x0F, 0x80, // ####        #####
    0xF0, 0x07, 0x80, // ####         ####
    0xF0, 0x03, 0x80, // ####          ###
    0x00, 0x01, 0x80, //                ##
    0x00, 0x00, 0x80, //                 #
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //

    // @9682 'l' (10 pixels wide)
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0xF0, 0x00, // ####
    0xF0, 0x00, // ####
    0xF0, 0x00, // ####
    0xF0, 0x00, // ####
    0xF0, 0x00, // ####
    0xF0, 0x00, // ####
    0xF0, 0x00, // ####
    0xF0, 0x00, // ####
    0xF0, 0x00, // ####
    0xF0, 0x00, // ####
    0xF0, 0x00, // ####
    0xF0, 0x00, // ####
    0xF0, 0x00, // ####
    0xF0, 0x00, // ####
    0xF0, 0x00, // ####
    0xF0, 0x00, // ####
    0xF0, 0x00, // ####
    0xF0, 0x00, // ####
    0xF0, 0x00, // ####
    0xF0, 0x00, // ####
    0xF0, 0x00, // ####
    0xF0, 0x00, // ####
    0xF0, 0x00, // ####
    0xF0, 0x00, // ####
    0xF0, 0x00, // ####
    0xF0, 0x00, // ####
    0xF0, 0x00, // ####
    0x78, 0x80, //  ####   #
    0x7F, 0xC0, //  #########
    0x3F, 0xC0, //   ########
    0x0F, 0x80, //     #####
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //

    // @9776 'm' (30 pixels wide)
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0xE1, 0xF0, 0x0F, 0xC0, // ###    #####        ######
    0xE7, 0xFC, 0x3F, 0xE0, // ###  #########    #########
    0xEF, 0xFE, 0x7F, 0xF0, // ### ###########  ###########
    0xFC, 0x1F, 0xF0, 0xF8, // ######     #########    #####
    0xF8, 0x0F, 0xC0, 0x7C, // #####       ######       #####
    0xF0, 0x07, 0xC0, 0x3C, // ####         #####        ####
    0xF0, 0x07, 0x80, 0x3C, // ####         ####         ####
    0xF0, 0x07, 0x80, 0x1C, // ####         ####          ###
    0xF0, 0x07, 0x80, 0x1C, // ####         ####          ###
    0xF0, 0x07, 0x80, 0x1C, // ####         ####          ###
    0xF0, 0x07, 0x80, 0x1C, // ####         ####          ###
    0xF0, 0x07, 0x80, 0x1C, // ####         ####          ###
    0xF0, 0x07, 0x80, 0x1C, // ####         ####          ###
    0xF0, 0x07, 0x80, 0x1C, // ####         ####          ###
    0xF0, 0x07, 0x80, 0x1C, // ####         ####          ###
    0xF0, 0x07, 0x80, 0x1C, // ####         ####          ###
    0xF0, 0x07, 0x80, 0x1C, // ####         ####          ###
    0xF0, 0x07, 0x80, 0x1C, // ####         ####          ###
    0xF0, 0x07, 0x80, 0x1C, // ####         ####          ###
    0xF0, 0x07, 0x80, 0x1C, // ####         ####          ###
    0xF0, 0x07, 0x80, 0x1C, // ####         ####          ###
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //

    // @9964 'n' (18 pixels wide)
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0xE1, 0xF8, 0x00, // ###    ######
    0xE7, 0xFE, 0x00, // ###  ##########
    0xEF, 0xFF, 0x00, // ### ############
    0xFE, 0x1F, 0x00, // #######    #####
    0xF8, 0x07, 0x80, // #####        ####
    0xF0, 0x07, 0x80, // ####         ####
    0xF0, 0x03, 0xC0, // ####          ####
    0xF0, 0x03, 0xC0, // ####          ####
    0xF0, 0x03, 0xC0, // ####          ####
    0xF0, 0x03, 0xC0, // ####          ####
    0xF0, 0x03, 0xC0, // ####          ####
    0xF0, 0x03, 0xC0, // ####          ####
    0xF0, 0x03, 0xC0, // ####          ####
    0xF0, 0x03, 0xC0, // ####          ####
    0xF0, 0x03, 0xC0, // ####          ####
    0xF0, 0x03, 0xC0, // ####          ####
    0xF0, 0x03, 0xC0, // ####          ####
    0xF0, 0x03, 0xC0, // ####          ####
    0xF0, 0x03, 0xC0, // ####          ####
    0xF0, 0x03, 0xC0, // ####          ####
    0xF0, 0x03, 0xC0, // ####          ####
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //

    // @10105 'o' (19 pixels wide)
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x03, 0xF8, 0x00, //       #######
    0x0F, 0xFE, 0x00, //     ###########
    0x1F, 0xFF, 0x00, //    #############
    0x3E, 0x0F, 0x80, //   #####     #####
    0x78, 0x07, 0xC0, //  ####        #####
    0x78, 0x03, 0xC0, //  ####         ####
    0xF0, 0x01, 0xC0, // ####           ###
    0xF0, 0x01, 0xE0, // ####           ####
    0xE0, 0x01, 0xE0, // ###            ####
    0xE0, 0x00, 0xE0, // ###             ###
    0xE0, 0x00, 0xE0, // ###             ###
    0xE0, 0x00, 0xE0, // ###             ###
    0xE0, 0x01, 0xE0, // ###            ####
    0xF0, 0x01, 0xE0, // ####           ####
    0xF0, 0x01, 0xC0, // ####           ###
    0x70, 0x03, 0xC0, //  ###          ####
    0x78, 0x07, 0xC0, //  ####        #####
    0x3E, 0x0F, 0x80, //   #####     #####
    0x1F, 0xFF, 0x00, //    #############
    0x0F, 0xFE, 0x00, //     ###########
    0x03, 0xF8, 0x00, //       #######
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //

    // @10246 'p' (19 pixels wide)
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0xE1, 0xF8, 0x00, // ###    ######
    0xE7, 0xFE, 0x00, // ###  ##########
    0xEF, 0xFF, 0x00, // ### ############
    0xFE, 0x0F, 0x80, // #######     #####
    0xFC, 0x07, 0xC0, // ######       #####
    0xF8, 0x03, 0xC0, // #####         ####
    0xF0, 0x01, 0xE0, // ####           ####
    0xF0, 0x01, 0xE0, // ####           ####
    0xE0, 0x00, 0xE0, // ###             ###
    0xE0, 0x00, 0xE0, // ###             ###
    0xE0, 0x00, 0xE0, // ###             ###
    0xE0, 0x00, 0xE0, // ###             ###
    0xE0, 0x00, 0xE0, // ###             ###
    0xF0, 0x01, 0xE0, // ####           ####
    0xF0, 0x01, 0xE0, // ####           ####
    0xF8, 0x03, 0xC0, // #####         ####
    0xFC, 0x07, 0xC0, // ######       #####
    0xFE, 0x0F, 0x80, // #######     #####
    0xEF, 0xFF, 0x00, // ### ############
    0xE7, 0xFE, 0x00, // ###  ##########
    0xE1, 0xF8, 0x00, // ###    ######
    0xE0, 0x00, 0x00, // ###
    0xE0, 0x00, 0x00, // ###
    0xE0, 0x00, 0x00, // ###
    0xE0, 0x00, 0x00, // ###
    0xE0, 0x00, 0x00, // ###
    0xE0, 0x00, 0x00, // ###
    0xE0, 0x00, 0x00, // ###
    0xE0, 0x00, 0x00, // ###
    0xE0, 0x00, 0x00, // ###
    0xE0, 0x00, 0x00, // ###
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //

    // @10387 'q' (19 pixels wide)
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x03, 0xF1, 0xE0, //       ######   ####
    0x0F, 0xFD, 0xE0, //     ########## ####
    0x1F, 0xFF, 0xE0, //    ################
    0x3E, 0x0F, 0xE0, //   #####     #######
    0x78, 0x07, 0xE0, //  ####        ######
    0x78, 0x03, 0xE0, //  ####         #####
    0xF0, 0x01, 0xE0, // ####           ####
    0xF0, 0x01, 0xE0, // ####           ####
    0xE0, 0x01, 0xE0, // ###            ####
    0xE0, 0x01, 0xE0, // ###            ####
    0xE0, 0x01, 0xE0, // ###            ####
    0xE0, 0x01, 0xE0, // ###            ####
    0xE0, 0x01, 0xE0, // ###            ####
    0xF0, 0x01, 0xE0, // ####           ####
    0xF0, 0x01, 0xE0, // ####           ####
    0x70, 0x03, 0xE0, //  ###          #####
    0x78, 0x07, 0xE0, //  ####        ######
    0x3E, 0x0F, 0xE0, //   #####     #######
    0x1F, 0xFF, 0xE0, //    ################
    0x0F, 0xFD, 0xE0, //     ########## ####
    0x03, 0xF1, 0xE0, //       ######   ####
    0x00, 0x01, 0xE0, //                ####
    0x00, 0x01, 0xE0, //                ####
    0x00, 0x01, 0xE0, //                ####
    0x00, 0x01, 0xE0, //                ####
    0x00, 0x01, 0xE0, //                ####
    0x00, 0x01, 0xE0, //                ####
    0x00, 0x01, 0xE0, //                ####
    0x00, 0x01, 0xE0, //                ####
    0x00, 0x01, 0xE0, //                ####
    0x00, 0x01, 0xE0, //                ####
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //

    // @10528 'r' (14 pixels wide)
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0xE1, 0xF8, // ###    ######
    0xE7, 0xFC, // ###  #########
    0xEF, 0xF8, // ### #########
    0xFE, 0x10, // #######    #
    0xFC, 0x00, // ######
    0xF8, 0x00, // #####
    0xF0, 0x00, // ####
    0xF0, 0x00, // ####
    0xF0, 0x00, // ####
    0xF0, 0x00, // ####
    0xF0, 0x00, // ####
    0xF0, 0x00, // ####
    0xF0, 0x00, // ####
    0xF0, 0x00, // ####
    0xF0, 0x00, // ####
    0xF0, 0x00, // ####
    0xF0, 0x00, // ####
    0xF0, 0x00, // ####
    0xF0, 0x00, // ####
    0xF0, 0x00, // ####
    0xF0, 0x00, // ####
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //

    // @10622 's' (16 pixels wide)
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x03, 0xF0, //       ######
    0x0F, 0xFC, //     ##########
    0x1F, 0xFF, //    #############
    0x3C, 0x0E, //   ####      ###
    0x38, 0x04, //   ###        #
    0x78, 0x00, //  ####
    0x38, 0x00, //   ###
    0x3C, 0x00, //   ####
    0x3F, 0x00, //   ######
    0x1F, 0xF0, //    #########
    0x07, 0xFC, //      #########
    0x00, 0xFE, //         #######
    0x00, 0x1F, //            #####
    0x00, 0x0F, //             ####
    0x00, 0x07, //              ###
    0x20, 0x07, //   #          ###
    0x60, 0x0F, //  ##         ####
    0xFC, 0x1F, // ######     #####
    0x7F, 0xFE, //  ##############
    0x1F, 0xFC, //    ###########
    0x07, 0xF0, //      #######
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //

    // @10716 't' (13 pixels wide)
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x1C, 0x00, //    ###
    0x1C, 0x00, //    ###
    0x1C, 0x00, //    ###
    0x1C, 0x00, //    ###
    0x1C, 0x00, //    ###
    0x1C, 0x00, //    ###
    0x1C, 0x00, //    ###
    0xFF, 0xE0, // ###########
    0xFF, 0xE0, // ###########
    0xFF, 0xE0, // ###########
    0x1C, 0x00, //    ###
    0x1C, 0x00, //    ###
    0x1C, 0x00, //    ###
    0x1C, 0x00, //    ###
    0x1C, 0x00, //    ###
    0x1C, 0x00, //    ###
    0x1C, 0x00, //    ###
    0x1C, 0x00, //    ###
    0x1C, 0x00, //    ###
    0x1C, 0x00, //    ###
    0x1C, 0x00, //    ###
    0x1C, 0x00, //    ###
    0x1C, 0x00, //    ###
    0x1C, 0x00, //    ###
    0x1E, 0x20, //    ####   #
    0x0F, 0xF0, //     ########
    0x0F, 0xF8, //     #########
    0x03, 0xE0, //       #####
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //
    0x00, 0x00, //

    // @10810 'u' (18 pixels wide)
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0xF0, 0x03, 0xC0, // ####          ####
    0xF0, 0x03, 0xC0, // ####          ####
    0xF0, 0x03, 0xC0, // ####          ####
    0xF0, 0x03, 0xC0, // ####          ####
    0xF0, 0x03, 0xC0, // ####          ####
    0xF0, 0x03, 0xC0, // ####          ####
    0xF0, 0x03, 0xC0, // ####          ####
    0xF0, 0x03, 0xC0, // ####          ####
    0xF0, 0x03, 0xC0, // ####          ####
    0xF0, 0x03, 0xC0, // ####          ####
    0xF0, 0x03, 0xC0, // ####          ####
    0xF0, 0x03, 0xC0, // ####          ####
    0xF0, 0x03, 0xC0, // ####          ####
    0xF0, 0x03, 0xC0, // ####          ####
    0xF0, 0x03, 0xC0, // ####          ####
    0xF0, 0x07, 0xC0, // ####         #####
    0x78, 0x07, 0xC0, //  ####        #####
    0x7E, 0x1F, 0xC0, //  ######    #######
    0x3F, 0xFF, 0xC0, //   ################
    0x1F, 0xFB, 0xC0, //    ########## ####
    0x07, 0xE3, 0xC0, //      ######   ####
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //

    // @10951 'v' (20 pixels wide)
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0xF0, 0x00, 0xF0, // ####            ####
    0x78, 0x01, 0xE0, //  ####          ####
    0x78, 0x01, 0xE0, //  ####          ####
    0x38, 0x01, 0xC0, //   ###          ###
    0x3C, 0x03, 0xC0, //   ####        ####
    0x3C, 0x03, 0xC0, //   ####        ####
    0x1C, 0x03, 0x80, //    ###        ###
    0x1E, 0x07, 0x80, //    ####      ####
    0x0E, 0x07, 0x00, //     ###      ###
    0x0E, 0x07, 0x00, //     ###      ###
    0x0F, 0x0F, 0x00, //     ####    ####
    0x07, 0x0E, 0x00, //      ###    ###
    0x07, 0x8E, 0x00, //      ####   ###
    0x07, 0x9E, 0x00, //      ####  ####
    0x03, 0x9C, 0x00, //       ###  ###
    0x03, 0xFC, 0x00, //       ########
    0x01, 0xF8, 0x00, //        ######
    0x01, 0xF8, 0x00, //        ######
    0x01, 0xF8, 0x00, //        ######
    0x00, 0xF0, 0x00, //         ####
    0x00, 0xF0, 0x00, //         ####
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //

    // @11092 'w' (28 pixels wide)
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0xF8, 0x1C, 0x00, 0xF0, // #####      ###          ####
    0x78, 0x1E, 0x00, 0xF0, //  ####      ####         ####
    0x78, 0x1E, 0x00, 0xE0, //  ####      ####         ###
    0x38, 0x0E, 0x01, 0xE0, //   ###       ###        ####
    0x3C, 0x0E, 0x01, 0xE0, //   ####      ###        ####
    0x3C, 0x0F, 0x01, 0xE0, //   ####      ####       ####
    0x3C, 0x07, 0x03, 0xC0, //   ####       ###      ####
    0x1E, 0x0F, 0x03, 0xC0, //    ####     ####      ####
    0x1E, 0x0F, 0x83, 0xC0, //    ####     #####     ####
    0x1E, 0x0F, 0x83, 0x80, //    ####     #####     ###
    0x0F, 0x1F, 0x87, 0x80, //     ####   ######    ####
    0x0F, 0x1F, 0xC7, 0x80, //     ####   #######   ####
    0x0F, 0x1D, 0xC7, 0x00, //     ####   ### ###   ###
    0x07, 0x39, 0xCF, 0x00, //      ###  ###  ###  ####
    0x07, 0xB9, 0xEF, 0x00, //      #### ###  #### ####
    0x07, 0xB8, 0xEE, 0x00, //      #### ###   ### ###
    0x03, 0xF8, 0xFE, 0x00, //       #######   #######
    0x03, 0xF0, 0xFE, 0x00, //       ######    #######
    0x03, 0xF0, 0x7C, 0x00, //       ######     #####
    0x01, 0xF0, 0x7C, 0x00, //        #####     #####
    0x01, 0xE0, 0x7C, 0x00, //        ####      #####
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, 0x00, //

    // @11280 'x' (21 pixels wide)
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0xF0, 0x00, 0xF8, // ####            #####
    0x78, 0x01, 0xF0, //  ####          #####
    0x3C, 0x03, 0xE0, //   ####        #####
    0x1E, 0x03, 0xC0, //    ####       ####
    0x1F, 0x07, 0x80, //    #####     ####
    0x0F, 0x0F, 0x00, //     ####    ####
    0x07, 0x9F, 0x00, //      ####  #####
    0x03, 0xDE, 0x00, //       #### ####
    0x03, 0xFC, 0x00, //       ########
    0x01, 0xF8, 0x00, //        ######
    0x00, 0xF0, 0x00, //         ####
    0x01, 0xF8, 0x00, //        ######
    0x01, 0xFC, 0x00, //        #######
    0x03, 0xFE, 0x00, //       #########
    0x07, 0x9F, 0x00, //      ####  #####
    0x0F, 0x0F, 0x00, //     ####    ####
    0x1F, 0x07, 0x80, //    #####     ####
    0x1E, 0x03, 0xC0, //    ####       ####
    0x3C, 0x03, 0xE0, //   ####        #####
    0x78, 0x01, 0xF0, //  ####          #####
    0xF0, 0x00, 0xF8, // ####            #####
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //

    // @11421 'y' (18 pixels wide)
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0xF0, 0x03, 0xC0, // ####          ####
    0xF0, 0x03, 0xC0, // ####          ####
    0xF0, 0x03, 0xC0, // ####          ####
    0xF0, 0x03, 0xC0, // ####          ####
    0xF0, 0x03, 0xC0, // ####          ####
    0xF0, 0x03, 0xC0, // ####          ####
    0xF0, 0x03, 0xC0, // ####          ####
    0xF0, 0x03, 0xC0, // ####          ####
    0xF0, 0x03, 0xC0, // ####          ####
    0xF0, 0x03, 0xC0, // ####          ####
    0xF0, 0x03, 0xC0, // ####          ####
    0xF0, 0x03, 0xC0, // ####          ####
    0xF0, 0x03, 0xC0, // ####          ####
    0xF0, 0x03, 0xC0, // ####          ####
    0xF0, 0x03, 0xC0, // ####          ####
    0xF0, 0x07, 0xC0, // ####         #####
    0x78, 0x07, 0xC0, //  ####        #####
    0x7E, 0x1F, 0xC0, //  ######    #######
    0x3F, 0xFF, 0xC0, //   ################
    0x1F, 0xFB, 0xC0, //    ########## ####
    0x07, 0xE3, 0xC0, //      ######   ####
    0x00, 0x03, 0xC0, //               ####
    0x00, 0x03, 0xC0, //               ####
    0x00, 0x03, 0xC0, //               ####
    0x00, 0x03, 0x80, //               ###
    0x40, 0x07, 0x80, //  #           ####
    0x60, 0x07, 0x00, //  ##          ###
    0x78, 0x1F, 0x00, //  ####      #####
    0x7F, 0xFE, 0x00, //  ##############
    0x3F, 0xFC, 0x00, //   ############
    0x0F, 0xF0, 0x00, //     ########
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //

    // @11562 'z' (17 pixels wide)
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0xFF, 0xFF, 0x80, // #################
    0xFF, 0xFF, 0x80, // #################
    0xFF, 0xFF, 0x80, // #################
    0x00, 0x0F, 0x00, //             ####
    0x00, 0x1E, 0x00, //            ####
    0x00, 0x3E, 0x00, //           #####
    0x00, 0x7C, 0x00, //          #####
    0x00, 0x78, 0x00, //          ####
    0x00, 0xF0, 0x00, //         ####
    0x01, 0xE0, 0x00, //        ####
    0x03, 0xE0, 0x00, //       #####
    0x07, 0xC0, 0x00, //      #####
    0x07, 0x80, 0x00, //      ####
    0x0F, 0x00, 0x00, //     ####
    0x1E, 0x00, 0x00, //    ####
    0x3E, 0x00, 0x00, //   #####
    0x3C, 0x00, 0x00, //   ####
    0x78, 0x00, 0x00, //  ####
    0xFF, 0xFF, 0x80, // #################
    0xFF, 0xFF, 0x80, // #################
    0xFF, 0xFF, 0x80, // #################
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //

    // @11703 '{' (9 pixels wide)
    0x07, 0x80, //      ####
    0x0F, 0x80, //     #####
    0x0F, 0x80, //     #####
    0x1E, 0x00, //    ####
    0x1C, 0x00, //    ###
    0x1C, 0x00, //    ###
    0x1C, 0x00, //    ###
    0x1C, 0x00, //    ###
    0x1C, 0x00, //    ###
    0x1C, 0x00, //    ###
    0x1C, 0x00, //    ###
    0x1C, 0x00, //    ###
    0x1C, 0x00, //    ###
    0x1C, 0x00, //    ###
    0x1C, 0x00, //    ###
    0x1C, 0x00, //    ###
    0x1C, 0x00, //    ###
    0x1C, 0x00, //    ###
    0x1C, 0x00, //    ###
    0x1C, 0x00, //    ###
    0x38, 0x00, //   ###
    0x78, 0x00, //  ####
    0xF0, 0x00, // ####
    0xE0, 0x00, // ###
    0xF0, 0x00, // ####
    0x78, 0x00, //  ####
    0x38, 0x00, //   ###
    0x1C, 0x00, //    ###
    0x1C, 0x00, //    ###
    0x1C, 0x00, //    ###
    0x1C, 0x00, //    ###
    0x1C, 0x00, //    ###
    0x1C, 0x00, //    ###
    0x1C, 0x00, //    ###
    0x1C, 0x00, //    ###
    0x1C, 0x00, //    ###
    0x1C, 0x00, //    ###
    0x1C, 0x00, //    ###
    0x1C, 0x00, //    ###
    0x1C, 0x00, //    ###
    0x1C, 0x00, //    ###
    0x1C, 0x00, //    ###
    0x1C, 0x00, //    ###
    0x1E, 0x00, //    ####
    0x0F, 0x80, //     #####
    0x0F, 0x80, //     #####
    0x07, 0x80, //      ####

    // @11797 '|' (2 pixels wide)
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0xC0, // ##
    0xC0, // ##
    0xC0, // ##
    0xC0, // ##
    0xC0, // ##
    0xC0, // ##
    0xC0, // ##
    0xC0, // ##
    0xC0, // ##
    0xC0, // ##
    0xC0, // ##
    0xC0, // ##
    0xC0, // ##
    0xC0, // ##
    0xC0, // ##
    0xC0, // ##
    0xC0, // ##
    0xC0, // ##
    0xC0, // ##
    0xC0, // ##
    0xC0, // ##
    0xC0, // ##
    0xC0, // ##
    0xC0, // ##
    0xC0, // ##
    0xC0, // ##
    0xC0, // ##
    0xC0, // ##
    0xC0, // ##
    0xC0, // ##
    0xC0, // ##
    0xC0, // ##
    0xC0, // ##
    0xC0, // ##
    0xC0, // ##
    0xC0, // ##
    0xC0, // ##
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //
    0x00, //

    // @11844 '}' (9 pixels wide)
    0xF0, 0x00, // ####
    0xF8, 0x00, // #####
    0xFC, 0x00, // ######
    0x1C, 0x00, //    ###
    0x1C, 0x00, //    ###
    0x1C, 0x00, //    ###
    0x1C, 0x00, //    ###
    0x1C, 0x00, //    ###
    0x1C, 0x00, //    ###
    0x1C, 0x00, //    ###
    0x1C, 0x00, //    ###
    0x1C, 0x00, //    ###
    0x1C, 0x00, //    ###
    0x1C, 0x00, //    ###
    0x1C, 0x00, //    ###
    0x1C, 0x00, //    ###
    0x1C, 0x00, //    ###
    0x1C, 0x00, //    ###
    0x0C, 0x00, //     ##
    0x0E, 0x00, //     ###
    0x0E, 0x00, //     ###
    0x0F, 0x00, //     ####
    0x07, 0x80, //      ####
    0x03, 0x80, //       ###
    0x07, 0x80, //      ####
    0x0F, 0x00, //     ####
    0x0E, 0x00, //     ###
    0x0E, 0x00, //     ###
    0x0C, 0x00, //     ##
    0x1C, 0x00, //    ###
    0x1C, 0x00, //    ###
    0x1C, 0x00, //    ###
    0x1C, 0x00, //    ###
    0x1C, 0x00, //    ###
    0x1C, 0x00, //    ###
    0x1C, 0x00, //    ###
    0x1C, 0x00, //    ###
    0x1C, 0x00, //    ###
    0x1C, 0x00, //    ###
    0x1C, 0x00, //    ###
    0x1C, 0x00, //    ###
    0x1C, 0x00, //    ###
    0x1C, 0x00, //    ###
    0x3C, 0x00, //   ####
    0xFC, 0x00, // ######
    0xF8, 0x00, // #####
    0xF0, 0x00, // ####

    // @11938 '~' (19 pixels wide)
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x1E, 0x00, 0x00, //    ####
    0x7F, 0x80, 0x60, //  ########        ##
    0x61, 0xC0, 0x60, //  ##    ###       ##
    0xC0, 0xE0, 0xC0, // ##      ###     ##
    0xC0, 0x71, 0xC0, // ##       ###   ###
    0xC0, 0x3F, 0x80, // ##        #######
    0xC0, 0x1F, 0x00, // ##         #####
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
    0x00, 0x00, 0x00, //
};

// Character descriptors for Orkney 24pt
// { [Char width in bits], [Offset into orkney_24ptCharBitmaps in bytes] }
const FONT_CHAR_INFO orkney_24ptDescriptors[] =
{
    {4, 0},           // !
    {10, 47},         // "
    {27, 141},        // #
    {20, 329},        // $
    {26, 470},        // %
    {24, 658},        // &
    {3, 799},         // '
    {11, 846},        // (
    {10, 940},        // )
    {10, 1034},       // *
    {24, 1128},       // +
    {5, 1269},        // ,
    {14, 1316},       // -
    {5, 1410},        // .
    {25, 1457},       // /
    {21, 1645},       // 0
    {9, 1786},        // 1
    {20, 1880},       // 2
    {19, 2021},       // 3
    {21, 2162},       // 4
    {19, 2303},       // 5
    {22, 2444},       // 6
    {20, 2585},       // 7
    {19, 2726},       // 8
    {22, 2867},       // 9
    {4, 3008},        // :
    {5, 3055},        // ;
    {22, 3102},       // <
    {24, 3243},       // =
    {22, 3384},       // >
    {18, 3525},       // ?
    {19, 3666},       // @
    {28, 3807},       // A
    {21, 3995},       // B
    {23, 4136},       // C
    {23, 4277},       // D
    {17, 4418},       // E
    {17, 4559},       // F
    {24, 4700},       // G
    {23, 4841},       // H
    {4, 4982},        // I
    {10, 5029},       // J
    {21, 5123},       // K
    {15, 5264},       // L
    {30, 5358},       // M
    {25, 5546},       // N
    {29, 5734},       // O
    {19, 5922},       // P
    {30, 6063},       // Q
    {20, 6251},       // R
    {21, 6392},       // S
    {20, 6533},       // T
    {23, 6674},       // U
    {28, 6815},       // V
    {44, 7003},       // W
    {24, 7285},       // X
    {24, 7426},       // Y
    {23, 7567},       // Z
    {8, 7708},        // [
    {25, 7755},       // '\'
    {8, 7943},        // ]
    {26, 7990},       // ^
    {20, 8178},       // _
    {7, 8319},        // `
    {17, 8366},       // a
    {19, 8507},       // b
    {14, 8648},       // c
    {19, 8742},       // d
    {18, 8883},       // e
    {14, 9024},       // f
    {19, 9118},       // g
    {18, 9259},       // h
    {5, 9400},        // i
    {9, 9447},        // j
    {17, 9541},       // k
    {10, 9682},       // l
    {30, 9776},       // m
    {18, 9964},       // n
    {19, 10105},      // o
    {19, 10246},      // p
    {19, 10387},      // q
    {14, 10528},      // r
    {16, 10622},      // s
    {13, 10716},      // t
    {18, 10810},      // u
    {20, 10951},      // v
    {28, 11092},      // w
    {21, 11280},      // x
    {18, 11421},      // y
    {17, 11562},      // z
    {9, 11703},       // {
    {2, 11797},       // |
    {9, 11844},       // }
    {19, 11938},      // ~
};

// Font information for Orkney 24pt
const FONT_INFO orkney_24ptFontInfo =
{
    45, //  Character height
    '!', //  Start character
    '~', //  End character
    2, //  Width, in pixels, of space character
    orkney_24ptDescriptors, //  Character descriptor array
    orkney_24ptBitmaps, //  Character bitmap array
};
