mbed TLS ChangeLog (Sorted per branch, date)

= mbed TLS 2.16.5 branch released 2020-02-20

Security
   * Fix potential memory overread when performing an ECDSA signature
     operation. The overread only happens with cryptographically low
     probability (of the order of 2^-n where n is the bitsize of the curve)
     unless the RNG is broken, and could result in information disclosure or
     denial of service (application crash or extra resource consumption).
     Found by <PERSON><PERSON> and <PERSON>, using static analysis.
   * To avoid a side channel vulnerability when parsing an RSA private key,
     read all the CRT parameters from the DER structure rather than
     reconstructing them. Found by <PERSON> and <PERSON>. Reported and fix contributed by <PERSON>.
     ARMmbed/mbed-crypto#352

Bugfix
   * Fix an unchecked call to mbedtls_md() in the x509write module.
   * Fix a bug in mbedtls_pk_parse_key() that would cause it to accept some
     RSA keys that would later be rejected by functions expecting private
     keys. Found by <PERSON><PERSON> cyber using oss-fuzz (issue 20467).
   * Fix a bug in mbedtls_pk_parse_key() that would cause it to accept some
     RSA keys with invalid values by silently fixing those values.

= mbed TLS 2.16.4 branch released 2020-01-15

Security
   * Fix side channel vulnerability in ECDSA. Our bignum implementation is not
     constant time/constant trace, so side channel attacks can retrieve the
     blinded value, factor it (as it is smaller than RSA keys and not guaranteed
     to have only large prime factors), and then, by brute force, recover the
     key. Reported by Alejandro Cabrera Aldaya and Billy Brumley.
   * Zeroize local variables in mbedtls_internal_aes_encrypt() and
     mbedtls_internal_aes_decrypt() before exiting the function. The value of
     these variables can be used to recover the last round key. To follow best
     practice and to limit the impact of buffer overread vulnerabilities (like
     Heartbleed) we need to zeroize them before exiting the function.
     Issue reported by Tuba Yavuz, Farhaan Fowze, Ken (Yihang) Bai,
     Grant Hernandez, and Kevin Butler (University of Florida) and
     Dave Tian (Purdue University).
   * Fix side channel vulnerability in ECDSA key generation. Obtaining precise
     timings on the comparison in the key generation enabled the attacker to
     learn leading bits of the ephemeral key used during ECDSA signatures and to
     recover the private key. Reported by Jeremy Dubeuf.
   * Catch failure of AES functions in mbedtls_ctr_drbg_random(). Uncaught
     failures could happen with alternative implementations of AES. Bug
     reported and fix proposed by Johan Uppman Bruce and Christoffer Lauri,
     Sectra.

Bugfix
   * Remove redundant line for getting the bitlen of a bignum, since the variable
     holding the returned value is overwritten a line after.
     Found by irwir in #2377.
   * Support mbedtls_hmac_drbg_set_entropy_len() and
     mbedtls_ctr_drbg_set_entropy_len() before the DRBG is seeded. Before,
     the initial seeding always reset the entropy length to the compile-time
     default.

Changes
   * Add unit tests for AES-GCM when called through mbedtls_cipher_auth_xxx()
     from the cipher abstraction layer. Fixes #2198.
   * Clarify how the interface of the CTR_DRBG and HMAC modules relates to
     NIST SP 800-90A. In particular CTR_DRBG requires an explicit nonce
     to achieve a 256-bit strength if MBEDTLS_ENTROPY_FORCE_SHA256 is set.

= mbed TLS 2.16.3 branch released 2019-09-06

Security
   * Fix a missing error detection in ECJPAKE. This could have caused a
     predictable shared secret if a hardware accelerator failed and the other
     side of the key exchange had a similar bug.
   * The deterministic ECDSA calculation reused the scheme's HMAC-DRBG to
     implement blinding. Because of this for the same key and message the same
     blinding value was generated. This reduced the effectiveness of the
     countermeasure and leaked information about the private key through side
     channels. Reported by Jack Lloyd.
   * When writing a private EC key, use a constant size for the private
     value, as specified in RFC 5915. Previously, the value was written
     as an ASN.1 INTEGER, which caused the size of the key to leak
     about 1 bit of information on average and could cause the value to be
     1 byte too large for the output buffer.

API Changes
   * The new function mbedtls_ecdsa_sign_det_ext() is similar to
     mbedtls_ecdsa_sign_det() but allows passing an external RNG for the
     purpose of blinding.

Bugfix
   * Fix to allow building test suites with any warning that detects unused
     functions. Fixes #1628.
   * Fix typo in net_would_block(). Fixes #528 reported by github-monoculture.
   * Remove redundant include file in timing.c. Fixes #2640 reported by irwir.
   * Fix Visual Studio Release x64 build configuration by inheriting
     PlatformToolset from the project configuration. Fixes #1430 reported by
     irwir.
   * Enable Suite B with subset of ECP curves. Make sure the code compiles even
     if some curves are not defined. Fixes #1591 reported by dbedev.
   * Fix misuse of signed arithmetic in the HAVEGE module. #2598
   * Update test certificates that were about to expire. Reported by
     Bernhard M. Wiedemann in #2357.
   * Fix the build on ARMv5TE in ARM mode to not use assembly instructions
     that are only available in Thumb mode. Fix contributed by Aurelien Jarno
     in #2169.
   * Fix undefined memset(NULL) call in test_suite_nist_kw.
   * Make NV seed test support MBEDTLS_ENTROPY_FORCE_SHA256.
   * Fix propagation of restart contexts in restartable EC operations.
     This could previously lead to segmentation faults in builds using an
     address-sanitizer and enabling but not using MBEDTLS_ECP_RESTARTABLE.
   * Fix memory leak in in mpi_miller_rabin(). Contributed by
     Jens Wiklander <<EMAIL>> in #2363
   * Improve code clarity in x509_crt module, removing false-positive
     uninitialized variable warnings on some recent toolchains (GCC8, etc).
     Discovered and fixed by Andy Gross (Linaro), #2392.
   * Zero length buffer check for undefined behavior in
     mbedtls_platform_zeroize(). Fixes ARMmbed/mbed-crypto#49.
   * Fix bug in endianness conversion in bignum module. This lead to
     functionally incorrect code on bigendian systems which don't have
     __BYTE_ORDER__ defined. Reported by Brendan Shanks. Fixes #2622.

Changes
   * Make it easier to define MBEDTLS_PARAM_FAILED as assert (which config.h
     suggests). #2671
   * Make `make clean` clean all programs always. Fixes #1862.

= mbed TLS 2.16.2 branch released 2019-06-11

Security
   * Make mbedtls_ecdh_get_params return an error if the second key
     belongs to a different group from the first. Before, if an application
     passed keys that belonged to different group, the first key's data was
     interpreted according to the second group, which could lead to either
     an error or a meaningless output from mbedtls_ecdh_get_params. In the
     latter case, this could expose at most 5 bits of the private key.

Bugfix
   * Server's RSA certificate in certs.c was SHA-1 signed. In the default
     mbedTLS configuration only SHA-2 signed certificates are accepted.
     This certificate is used in the demo server programs, which lead the
     client programs to fail at the peer's certificate verification
     due to an unacceptable hash signature. The certificate has been
     updated to one that is SHA-256 signed. Fix contributed by
     Illya Gerasymchuk.
   * Fix private key DER output in the key_app_writer example. File contents
     were shifted by one byte, creating an invalid ASN.1 tag. Fixed by
     Christian Walther in #2239.
   * Fix potential memory leak in X.509 self test. Found and fixed by
     Junhwan Park, #2106.
   * Reduce stack usage of hkdf tests. Fixes #2195.
   * Fix 1-byte buffer overflow in mbedtls_mpi_write_string() when
     used with negative inputs. Found by Guido Vranken in #2404. Credit to
     OSS-Fuzz.
   * Fix bugs in the AEAD test suite which would be exposed by ciphers which
     either used both encrypt and decrypt key schedules, or which perform padding.
     GCM and CCM were not affected. Fixed by Jack Lloyd.
   * Fix incorrect default port number in ssl_mail_client example's usage.
     Found and fixed by irwir. #2337
   * Add missing parentheses around parameters in the definition of the
     public macro MBEDTLS_X509_ID_FLAG. This could lead to invalid evaluation
     in case operators binding less strongly than subtraction were used
     for the parameter.
   * Add a check for MBEDTLS_X509_CRL_PARSE_C in ssl_server2, guarding the crl
     sni entry parameter. Reported by inestlerode in #560.
   * Add DER-encoded test CRTs to library/certs.c, allowing
     the example programs ssl_server2 and ssl_client2 to be run
     if MBEDTLS_FS_IO and MBEDTLS_PEM_PARSE_C are unset. Fixes #2254.
   * Fix missing bounds checks in X.509 parsing functions that could
     lead to successful parsing of ill-formed X.509 CRTs. Fixes #2437.
   * Fix multiple X.509 functions previously returning ASN.1 low-level error
     codes to always wrap these codes into X.509 high level error codes before
     returning. Fixes #2431.

Changes
   * Return from various debugging routines immediately if the
     provided SSL context is unset.
   * Remove dead code from bignum.c in the default configuration.
     Found by Coverity, reported and fixed by Peter Kolbus (Garmin). Fixes #2309.
   * Add test for minimal value of MBEDTLS_MPI_WINDOW_SIZE to all.sh.
     Contributed by Peter Kolbus (Garmin).
   * Change wording in the `mbedtls_ssl_conf_max_frag_len()`'s documentation to
     improve clarity. Fixes #2258.
   * Replace multiple uses of MD2 by SHA-256 in X.509 test suite. Fixes #821.

= mbed TLS 2.16.1 branch released 2019-03-19

Features
   * Add MBEDTLS_REMOVE_3DES_CIPHERSUITES to allow removing 3DES ciphersuites
     from the default list (enabled by default). See
     https://sweet32.info/SWEET32_CCS16.pdf.

Bugfix
   * Fix a compilation issue with mbedtls_ecp_restart_ctx not being defined
     when MBEDTLS_ECP_ALT is defined. Reported by jwhui. Fixes #2242.
   * Run the AD too long test only if MBEDTLS_CCM_ALT is not defined.
     Raised as a comment in #1996.
   * Reduce the stack consumption of mbedtls_mpi_fill_random() which could
     previously lead to a stack overflow on constrained targets.
   * Add `MBEDTLS_SELF_TEST` for the mbedtls_self_test functions
     in the header files, which missed the precompilation check. #971
   * Fix clobber list in MIPS assembly for large integer multiplication.
     Previously, this could lead to functionally incorrect assembly being
     produced by some optimizing compilers, showing up as failures in
     e.g. RSA or ECC signature operations. Reported in #1722, fix suggested
     by Aurelien Jarno and submitted by Jeffrey Martin.
   * Fix signed-to-unsigned integer conversion warning
     in X.509 module. Fixes #2212.
   * Reduce stack usage of `mpi_write_hlp()` by eliminating recursion.
     Fixes #2190.
   * Remove a duplicate #include in a sample program. Fixed by Masashi Honma #2326.
   * Remove the mbedtls namespacing from the header file, to fix a "file not found"
     build error. Fixed by Haijun Gu #2319.
   * Fix returning the value 1 when mbedtls_ecdsa_genkey failed.
   * Fix false failure in all.sh when backup files exist in include/mbedtls
     (e.g. config.h.bak). Fixed by Peter Kolbus (Garmin) #2407.
   * Ensure that unused bits are zero when writing ASN.1 bitstrings when using
     mbedtls_asn1_write_bitstring().
   * Fix issue when writing the named bitstrings in KeyUsage and NsCertType
     extensions in CSRs and CRTs that caused these bitstrings to not be encoded
     correctly as trailing zeroes were not accounted for as unused bits in the
     leading content octet. Fixes #1610.

Changes
   * Include configuration file in all header files that use configuration,
     instead of relying on other header files that they include.
     Inserted as an enhancement for #1371
   * Add support for alternative CSR headers, as used by Microsoft and defined
     in RFC 7468. Found by Michael Ernst. Fixes #767.
   * Fix configuration queries in ssl-opt.h. #2030
   * Ensure that ssl-opt.h can be run in OS X. #2029
   * Reduce the complexity of the timing tests. They were assuming more than the
     underlying OS actually guarantees.
   * Re-enable certain interoperability tests in ssl-opt.sh which had previously
     been disabled for lack of a sufficiently recent version of GnuTLS on the CI.
   * Ciphersuites based on 3DES now have the lowest priority by default when
     they are enabled.

= mbed TLS 2.16.0 branch released 2018-12-21

Features
   * Add a new config.h option of MBEDTLS_CHECK_PARAMS that enables validation
     of parameters in the API. This allows detection of obvious misuses of the
     API, such as passing NULL pointers. The API of existing functions hasn't
     changed, but requirements on parameters have been made more explicit in
     the documentation. See the corresponding API documentation for each
     function to see for which parameter values it is defined. This feature is
     disabled by default. See its API documentation in config.h for additional
     steps you have to take when enabling it.

API Changes
   * The following functions in the random generator modules have been
     deprecated and replaced as shown below. The new functions change
     the return type from void to int to allow returning error codes when
     using MBEDTLS_<MODULE>_ALT for the underlying AES or message digest
     primitive. Fixes #1798.
     mbedtls_ctr_drbg_update() -> mbedtls_ctr_drbg_update_ret()
     mbedtls_hmac_drbg_update() -> mbedtls_hmac_drbg_update_ret()
   * Extend ECDH interface to enable alternative implementations.
   * Deprecate error codes of the form MBEDTLS_ERR_xxx_INVALID_KEY_LENGTH for
     ARIA, CAMELLIA and Blowfish. These error codes will be replaced by
     the more generic per-module error codes MBEDTLS_ERR_xxx_BAD_INPUT_DATA.
   * Additional parameter validation checks have been added for the following
     modules - AES, ARIA, Blowfish, CAMELLIA, CCM, GCM, DHM, ECP, ECDSA, ECDH,
     ECJPAKE, SHA, Chacha20 and Poly1305, cipher, pk, RSA, and MPI.
     Where modules have had parameter validation added, existing parameter
     checks may have changed. Some modules, such as Chacha20 had existing
     parameter validation whereas other modules had little. This has now been
     changed so that the same level of validation is present in all modules, and
     that it is now optional with the MBEDTLS_CHECK_PARAMS flag which by default
     is off. That means that checks which were previously present by default
     will no longer be.

New deprecations
   * Deprecate mbedtls_ctr_drbg_update and mbedtls_hmac_drbg_update
     in favor of functions that can return an error code.

Bugfix
   * Fix for Clang, which was reporting a warning for the bignum.c inline
     assembly for AMD64 targets creating string literals greater than those
     permitted by the ISO C99 standard. Found by Aaron Jones. Fixes #482.
   * Fix runtime error in `mbedtls_platform_entropy_poll()` when run
     through qemu user emulation. Reported and fix suggested by randombit
     in #1212. Fixes #1212.
   * Fix an unsafe bounds check when restoring an SSL session from a ticket.
     This could lead to a buffer overflow, but only in case ticket authentication
     was broken. Reported and fix suggested by Guido Vranken in #659.
   * Add explicit integer to enumeration type casts to example program
     programs/pkey/gen_key which previously led to compilation failure
     on some toolchains. Reported by phoenixmcallister. Fixes #2170.
   * Fix double initialization of ECC hardware that made some accelerators
     hang.
   * Clarify documentation of mbedtls_ssl_set_own_cert() regarding the absence
     of check for certificate/key matching. Reported by Attila Molnar, #507.

= mbed TLS 2.14.1 branch released 2018-11-30

Security
   * Fix timing variations and memory access variations in RSA PKCS#1 v1.5
     decryption that could lead to a Bleichenbacher-style padding oracle
     attack. In TLS, this affects servers that accept ciphersuites based on
     RSA decryption (i.e. ciphersuites whose name contains RSA but not
     (EC)DH(E)). Discovered by Eyal Ronen (Weizmann Institute),  Robert Gillham
     (University of Adelaide), Daniel Genkin (University of Michigan),
     Adi Shamir (Weizmann Institute), David Wong (NCC Group), and Yuval Yarom
     (University of Adelaide, Data61). The attack is described in more detail
     in the paper available here: http://cat.eyalro.net/cat.pdf  CVE-2018-19608
   * In mbedtls_mpi_write_binary(), don't leak the exact size of the number
     via branching and memory access patterns. An attacker who could submit
     a plaintext for RSA PKCS#1 v1.5 decryption but only observe the timing
     of the decryption and not its result could nonetheless decrypt RSA
     plaintexts and forge RSA signatures. Other asymmetric algorithms may
     have been similarly vulnerable. Reported by Eyal Ronen, Robert Gillham,
     Daniel Genkin, Adi Shamir, David Wong and Yuval Yarom.
   * Wipe sensitive buffers on the stack in the CTR_DRBG and HMAC_DRBG
     modules.

API Changes
   * The new functions mbedtls_ctr_drbg_update_ret() and
     mbedtls_hmac_drbg_update_ret() are similar to mbedtls_ctr_drbg_update()
     and mbedtls_hmac_drbg_update() respectively, but the new functions
     report errors whereas the old functions return void. We recommend that
     applications use the new functions.

= mbed TLS 2.14.0 branch released 2018-11-19

Security
   * Fix overly strict DN comparison when looking for CRLs belonging to a
     particular CA. This previously led to ignoring CRLs when the CRL's issuer
     name and the CA's subject name differed in their string encoding (e.g.,
     one using PrintableString and the other UTF8String) or in the choice of
     upper and lower case. Reported by Henrik Andersson of Bosch GmbH in issue
     #1784.
   * Fix a flawed bounds check in server PSK hint parsing. In case the
     incoming message buffer was placed within the first 64KiB of address
     space and a PSK-(EC)DHE ciphersuite was used, this allowed an attacker
     to trigger a memory access up to 64KiB beyond the incoming message buffer,
     potentially leading to an application crash or information disclosure.
   * Fix mbedtls_mpi_is_prime() to use more rounds of probabilistic testing. The
     previous settings for the number of rounds made it practical for an
     adversary to construct non-primes that would be erroneously accepted as
     primes with high probability. This does not have an impact on the
     security of TLS, but can matter in other contexts with numbers chosen
     potentially by an adversary that should be prime and can be validated.
     For example, the number of rounds was enough to securely generate RSA key
     pairs or Diffie-Hellman parameters, but was insufficient to validate
     Diffie-Hellman parameters properly.
     See "Prime and Prejudice" by by Martin R. Albrecht and Jake Massimo and
     Kenneth G. Paterson and Juraj Somorovsky.

Features
   * Add support for temporarily suspending expensive ECC computations after
     some configurable amount of operations. This is intended to be used in
     constrained, single-threaded systems where ECC is time consuming and can
     block other operations until they complete. This is disabled by default,
     but can be enabled by MBEDTLS_ECP_RESTARTABLE at compile time and
     configured by mbedtls_ecp_set_max_ops() at runtime. It applies to the new
     xxx_restartable functions in ECP, ECDSA, PK and X.509 (CRL not supported
     yet), and to existing functions in ECDH and SSL (currently only
     implemented client-side, for ECDHE-ECDSA ciphersuites in TLS 1.2,
     including client authentication).
   * Add support for Arm CPU DSP extensions to accelerate asymmetric key
     operations. On CPUs where the extensions are available, they can accelerate
     MPI multiplications used in ECC and RSA cryptography. Contributed by
     Aurelien Jarno.
   * Extend RSASSA-PSS signature to allow a smaller salt size. Previously, PSS
     signature always used a salt with the same length as the hash, and returned
     an error if this was not possible. Now the salt size may be up to two bytes
     shorter. This allows the library to support all hash and signature sizes
     that comply with FIPS 186-4, including SHA-512 with a 1024-bit key.
   * Add support for 128-bit keys in CTR_DRBG. Note that using keys shorter
     than 256 bits limits the security of generated material to 128 bits.

API Changes
   * Add a common error code of `MBEDTLS_ERR_PLATFORM_FEATURE_UNSUPPORTED` for
     a feature that is not supported by underlying alternative
     implementations implementing cryptographic primitives. This is useful for
     hardware accelerators that don't implement all options or features.

New deprecations
   * All module specific errors following the form
     MBEDTLS_ERR_XXX_FEATURE_UNAVAILABLE that indicate a feature is not
     supported are deprecated and are now replaced by the new equivalent
     platform error.
   * All module specific generic hardware acceleration errors following the
     form MBEDTLS_ERR_XXX_HW_ACCEL_FAILED that are deprecated and are replaced
     by the equivalent plaform error.
   * Deprecate the function mbedtls_mpi_is_prime() in favor of
     mbedtls_mpi_is_prime_ext() which allows specifying the number of
     Miller-Rabin rounds.

Bugfix
   * Fix wrong order of freeing in programs/ssl/ssl_server2 example
     application leading to a memory leak in case both
     MBEDTLS_MEMORY_BUFFER_ALLOC_C and MBEDTLS_MEMORY_BACKTRACE are set.
     Fixes #2069.
   * Fix a bug in the update function for SSL ticket keys which previously
     invalidated keys of a lifetime of less than a 1s. Fixes #1968.
   * Fix failure in hmac_drbg in the benchmark sample application, when
     MBEDTLS_THREADING_C is defined. Found by TrinityTonic, #1095
   * Fix a bug in the record decryption routine ssl_decrypt_buf()
     which lead to accepting properly authenticated but improperly
     padded records in case of CBC ciphersuites using Encrypt-then-MAC.
   * Fix memory leak and freeing without initialization in the example
     program programs/x509/cert_write. Fixes #1422.
   * Ignore IV in mbedtls_cipher_set_iv() when the cipher mode is
     MBEDTLS_MODE_ECB. Found by ezdevelop. Fixes #1091.
   * Zeroize memory used for buffering or reassembling handshake messages
     after use.
   * Use `mbedtls_platform_zeroize()` instead of `memset()` for zeroization
     of sensitive data in the example programs aescrypt2 and crypt_and_hash.
   * Change the default string format used for various X.509 DN attributes to
     UTF8String. Previously, the use of the PrintableString format led to
     wildcards and non-ASCII characters being unusable in some DN attributes.
     Reported by raprepo in #1860 and by kevinpt in #468. Fix contributed by
     Thomas-Dee.
   * Fix compilation failure for configurations which use compile time
     replacements of standard calloc/free functions through the macros
     MBEDTLS_PLATFORM_CALLOC_MACRO and MBEDTLS_PLATFORM_FREE_MACRO.
     Reported by ole-de and ddhome2006. Fixes #882, #1642 and #1706.

Changes
   * Removed support for Yotta as a build tool.
   * Add tests for session resumption in DTLS.
   * Close a test gap in (D)TLS between the client side and the server side:
     test the handling of large packets and small packets on the client side
     in the same way as on the server side.
   * Change the dtls_client and dtls_server samples to work by default over
     IPv6 and optionally by a build option over IPv4.
   * Change the use of Windows threading to use Microsoft Visual C++ runtime
     calls, rather than Win32 API calls directly. This is necessary to avoid
     conflict with C runtime usage. Found and fixed by irwir.
   * Remember the string format of X.509 DN attributes when replicating
     X.509 DNs. Previously, DN attributes were always written in their default
     string format (mostly PrintableString), which could lead to CRTs being
     created which used PrintableStrings in the issuer field even though the
     signing CA used UTF8Strings in its subject field; while X.509 compliant,
     such CRTs were rejected in some applications, e.g. some versions of
     Firefox, curl and GnuTLS. Reported in #1033 by Moschn. Fix contributed by
     Thomas-Dee.
   * Improve documentation of mbedtls_ssl_get_verify_result().
     Fixes #517 reported by github-monoculture.
   * Add MBEDTLS_MPI_GEN_PRIME_FLAG_LOW_ERR flag to mbedtls_mpi_gen_prime() and
     use it to reduce error probability in RSA key generation to levels mandated
     by FIPS-186-4.

= mbed TLS 2.13.1 branch released 2018-09-06

API Changes
   * Extend the platform module with an abstraction mbedtls_platform_gmtime_r()
     whose implementation should behave as a thread-safe version of gmtime().
     This allows users to configure such an implementation at compile time when
     the target system cannot be deduced automatically, by setting the option
     MBEDTLS_PLATFORM_GMTIME_R_ALT. At this stage Mbed TLS is only able to
     automatically select implementations for Windows and POSIX C libraries.

Bugfix
   * Fix build failures on platforms where only gmtime() is available but
     neither gmtime_r() nor gmtime_s() are present. Fixes #1907.

= mbed TLS 2.13.0 branch released 2018-08-31

Security
   * Fix an issue in the X.509 module which could lead to a buffer overread
     during certificate extensions parsing. In case of receiving malformed
     input (extensions length field equal to 0), an illegal read of one byte
     beyond the input buffer is made. Found and analyzed by Nathan Crandall.

Features
   * Add support for fragmentation of outgoing DTLS handshake messages. This
     is controlled by the maximum fragment length as set locally or negotiated
     with the peer, as well as by a new per-connection MTU option, set using
     mbedtls_ssl_set_mtu().
   * Add support for auto-adjustment of MTU to a safe value during the
     handshake when flights do not get through (RFC 6347, section *******,
     last paragraph).
   * Add support for packing multiple records within a single datagram,
     enabled by default.
   * Add support for buffering out-of-order handshake messages in DTLS.
     The maximum amount of RAM used for this can be controlled by the
     compile-time constant MBEDTLS_SSL_DTLS_MAX_BUFFERING defined
     in mbedtls/config.h.

API Changes
   * Add function mbedtls_ssl_set_datagram_packing() to configure
     the use of datagram packing (enabled by default).

Bugfix
   * Fix a potential memory leak in mbedtls_ssl_setup() function. An allocation
     failure in the function could lead to other buffers being leaked.
   * Fixes an issue with MBEDTLS_CHACHAPOLY_C which would not compile if
     MBEDTLS_ARC4_C and MBEDTLS_CIPHER_NULL_CIPHER weren't also defined. #1890
   * Fix a memory leak in ecp_mul_comb() if ecp_precompute_comb() fails.
     Fix contributed by Espressif Systems.
   * Add ecc extensions only if an ecc based ciphersuite is used.
     This improves compliance to RFC 4492, and as a result, solves
     interoperability issues with BouncyCastle. Raised by milenamil in #1157.
   * Replace printf with mbedtls_printf in the ARIA module. Found by
     TrinityTonic in #1908.
   * Fix potential use-after-free in mbedtls_ssl_get_max_frag_len()
     and mbedtls_ssl_get_record_expansion() after a session reset. Fixes #1941.
   * Fix a bug that caused SSL/TLS clients to incorrectly abort the handshake
     with TLS versions 1.1 and earlier when the server requested authentication
     without providing a list of CAs. This was due to an overly strict bounds
     check in parsing the CertificateRequest message,
     introduced in Mbed TLS 2.12.0. Fixes #1954.
   * Fix a miscalculation of the maximum record expansion in
     mbedtls_ssl_get_record_expansion() in case of ChachaPoly ciphersuites,
     or CBC ciphersuites in (D)TLS versions 1.1 or higher. Fixes #1913, #1914.
   * Fix undefined shifts with negative values in certificates parsing
     (found by Catena cyber using oss-fuzz)
   * Fix memory leak and free without initialization in pk_encrypt
     and pk_decrypt example programs. Reported by Brace Stout. Fixes #1128.
   * Remove redundant else statement. Raised by irwir. Fixes #1776.

Changes
   * Copy headers preserving timestamps when doing a "make install".
     Contributed by xueruini.
   * Allow the forward declaration of public structs. Contributed by Dawid
     Drozd. Fixes #1215 raised by randombit.
   * Improve compatibility with some alternative CCM implementations by using
     CCM test vectors from RAM.
   * Add support for buffering of out-of-order handshake messages.
   * Add warnings to the documentation of the HKDF module to reduce the risk
     of misusing the mbedtls_hkdf_extract() and mbedtls_hkdf_expand()
     functions. Fixes #1775. Reported by Brian J. Murray.

= mbed TLS 2.12.0 branch released 2018-07-25

Security
   * Fix a vulnerability in TLS ciphersuites based on CBC and using SHA-384,
     in (D)TLS 1.0 to 1.2, that allowed an active network attacker to
     partially recover the plaintext of messages under some conditions by
     exploiting timing measurements. With DTLS, the attacker could perform
     this recovery by sending many messages in the same connection. With TLS
     or if mbedtls_ssl_conf_dtls_badmac_limit() was used, the attack only
     worked if the same secret (for example a HTTP Cookie) has been repeatedly
     sent over connections manipulated by the attacker. Connections using GCM
     or CCM instead of CBC, using hash sizes other than SHA-384, or using
     Encrypt-then-Mac (RFC 7366) were not affected. The vulnerability was
     caused by a miscalculation (for SHA-384) in a countermeasure to the
     original Lucky 13 attack. Found by Kenny Paterson, Eyal Ronen and Adi
     Shamir.
   * Fix a vulnerability in TLS ciphersuites based on CBC, in (D)TLS 1.0 to
     1.2, that allowed a local attacker, able to execute code on the local
     machine as well as manipulate network packets, to partially recover the
     plaintext of messages under some conditions by using a cache attack
     targeting an internal MD/SHA buffer. With TLS or if
     mbedtls_ssl_conf_dtls_badmac_limit() was used, the attack only worked if
     the same secret (for example a HTTP Cookie) has been repeatedly sent over
     connections manipulated by the attacker. Connections using GCM or CCM
     instead of CBC or using Encrypt-then-Mac (RFC 7366) were not affected.
     Found by Kenny Paterson, Eyal Ronen and Adi Shamir.
   * Add a counter-measure against a vulnerability in TLS ciphersuites based
     on CBC, in (D)TLS 1.0 to 1.2, that allowed a local attacker, able to
     execute code on the local machine as well as manipulate network packets,
     to partially recover the plaintext of messages under some conditions (see
     previous entry) by using a cache attack targeting the SSL input record
     buffer. Connections using GCM or CCM instead of CBC or using
     Encrypt-then-Mac (RFC 7366) were not affected. Found by Kenny Paterson,
     Eyal Ronen and Adi Shamir.

Features
   * Add new crypto primitives from RFC 7539: stream cipher Chacha20, one-time
     authenticator Poly1305 and AEAD construct Chacha20-Poly1305. Contributed
     by Daniel King.
   * Add support for CHACHA20-POLY1305 ciphersuites from RFC 7905.
   * Add platform support for the Haiku OS. (https://www.haiku-os.org).
     Contributed by Augustin Cavalier.
   * Make the receive and transmit buffers independent sizes, for situations
     where the outgoing buffer can be fixed at a smaller size than the incoming
     buffer, which can save some RAM. If buffer lengths are kept equal, there
     is no functional difference. Contributed by Angus Gratton, and also
     independently contributed again by Paul Sokolovsky.
   * Add support for key wrapping modes based on AES as defined by
     NIST SP 800-38F algorithms KW and KWP and by RFC 3394 and RFC 5649.

Bugfix
   * Fix the key_app_writer example which was writing a leading zero byte which
     was creating an invalid ASN.1 tag. Found by Aryeh R. Fixes #1257.
   * Fix compilation error on C++, because of a variable named new.
     Found and fixed by Hirotaka Niisato in #1783.
   * Fix "no symbols" warning issued by ranlib when building on Mac OS X. Fix
     contributed by tabascoeye.
   * Clarify documentation for mbedtls_ssl_write() to include 0 as a valid
     return value. Found by @davidwu2000. #839
   * Fix a memory leak in mbedtls_x509_csr_parse(), found by catenacyber,
     Philippe Antoine. Fixes #1623.
   * Remove unused headers included in x509.c. Found by Chris Hanson and fixed
     by Brendan Shanks. Part of a fix for #992.
   * Fix compilation error when MBEDTLS_ARC4_C is disabled and
     MBEDTLS_CIPHER_NULL_CIPHER is enabled. Found by TrinityTonic in #1719.
   * Added length checks to some TLS parsing functions. Found and fixed by
     Philippe Antoine from Catena cyber. #1663.
   * Fix the inline assembly for the MPI multiply helper function for i386 and
     i386 with SSE2. Found by László Langó. Fixes #1550
   * Fix namespacing in header files. Remove the `mbedtls` namespacing in
     the `#include` in the header files. Resolves #857
   * Fix compiler warning of 'use before initialisation' in
     mbedtls_pk_parse_key(). Found by Martin Boye Petersen and fixed by Dawid
     Drozd. #1098
   * Fix decryption for zero length messages (which contain all padding) when a
     CBC based ciphersuite is used together with Encrypt-then-MAC. Previously,
     such a message was wrongly reported as an invalid record and therefore lead
     to the connection being terminated. Seen most often with OpenSSL using
     TLS 1.0. Reported by @kFYatek and by Conor Murphy on the forum. Fix
     contributed by Espressif Systems. Fixes #1632
   * Fix ssl_client2 example to send application data with 0-length content
     when the request_size argument is set to 0 as stated in the documentation.
     Fixes #1833.
   * Correct the documentation for `mbedtls_ssl_get_session()`. This API has
     deep copy of the session, and the peer certificate is not lost. Fixes #926.
   * Fix build using -std=c99. Fixed by Nick Wilson.

Changes
   * Fail when receiving a TLS alert message with an invalid length, or invalid
     zero-length messages when using TLS 1.2. Contributed by Espressif Systems.
   * Change the default behaviour of mbedtls_hkdf_extract() to return an error
     when calling with a NULL salt and non-zero salt_len. Contributed by
     Brian J Murray
   * Change the shebang line in Perl scripts to look up perl in the PATH.
     Contributed by fbrosson.
   * Allow overriding the time on Windows via the platform-time abstraction.
     Fixed by Nick Wilson.
   * Use gmtime_r/gmtime_s for thread-safety. Fixed by Nick Wilson.

= mbed TLS 2.11.0 branch released 2018-06-18

Features
   * Add additional block mode, OFB (Output Feedback), to the AES module and
     cipher abstraction module.
   * Implement the HMAC-based extract-and-expand key derivation function
     (HKDF) per RFC 5869. Contributed by Thomas Fossati.
   * Add support for the CCM* block cipher mode as defined in IEEE Std. 802.15.4.
   * Add support for the XTS block cipher mode with AES (AES-XTS).
     Contributed by Aorimn in pull request #414.
   * In TLS servers, support offloading private key operations to an external
     cryptoprocessor. Private key operations can be asynchronous to allow
     non-blocking operation of the TLS server stack.

Bugfix
   * Fix the cert_write example to handle certificates signed with elliptic
     curves as well as RSA. Fixes #777 found by dbedev.
   * Fix for redefinition of _WIN32_WINNT to avoid overriding a definition
     used by user applications. Found and fixed by Fabio Alessandrelli.
   * Fix compilation warnings with IAR toolchain, on 32 bit platform.
     Reported by rahmanih in #683
   * Fix braces in mbedtls_memory_buffer_alloc_status(). Found by sbranden, #552.

Changes
   * Changed CMake defaults for IAR to treat all compiler warnings as errors.
   * Changed the Clang parameters used in the CMake build files to work for
     versions later than 3.6. Versions of Clang earlier than this may no longer
     work. Fixes #1072

= mbed TLS 2.10.0 branch released 2018-06-06

Features
   * Add support for ARIA cipher (RFC 5794) and associated TLS ciphersuites
     (RFC 6209). Disabled by default, see MBEDTLS_ARIA_C in config.h

API Changes
   * Extend the platform module with a util component that contains
     functionality shared by multiple Mbed TLS modules. At this stage
     platform_util.h (and its associated platform_util.c) only contain
     mbedtls_platform_zeroize(), which is a critical function from a security
     point of view. mbedtls_platform_zeroize() needs to be regularly tested
     against compilers to ensure that calls to it are not removed from the
     output binary as part of redundant code elimination optimizations.
     Therefore, mbedtls_platform_zeroize() is moved to the platform module to
     facilitate testing and maintenance.

Bugfix
   * Fix an issue with MicroBlaze support in bn_mul.h which was causing the
     build to fail. Found by zv-io. Fixes #1651.

Changes
   * Support TLS testing in out-of-source builds using cmake. Fixes #1193.
   * Fix redundant declaration of mbedtls_ssl_list_ciphersuites. Raised by
     TrinityTonic. #1359.

= mbed TLS 2.9.0 branch released 2018-04-30

Security
   * Fix an issue in the X.509 module which could lead to a buffer overread
     during certificate validation. Additionally, the issue could also lead to
     unnecessary callback checks being made or to some validation checks to be
     omitted. The overread could be triggered remotely, while the other issues
     would require a non DER-compliant certificate to be correctly signed by a
     trusted CA, or a trusted CA with a non DER-compliant certificate. Found by
     luocm. Fixes #825.
   * Fix the buffer length assertion in the ssl_parse_certificate_request()
     function which led to an arbitrary overread of the message buffer. The
     overreads could be caused by receiving a malformed message at the point
     where an optional signature algorithms list is expected when the signature
     algorithms section is too short. In builds with debug output, the overread
     data is output with the debug data.
   * Fix a client-side bug in the validation of the server's ciphersuite choice
     which could potentially lead to the client accepting a ciphersuite it didn't
     offer or a ciphersuite that cannot be used with the TLS or DTLS version
     chosen by the server. This could lead to corruption of internal data
     structures for some configurations.

Features
   * Add an option, MBEDTLS_AES_FEWER_TABLES, to dynamically compute smaller AES
     tables during runtime, thereby reducing the RAM/ROM footprint by ~6KiB.
     Suggested and contributed by jkivilin in pull request #394.
   * Add initial support for Curve448 (RFC 7748). Only mbedtls_ecp_mul() and
     ECDH primitive functions (mbedtls_ecdh_gen_public(),
     mbedtls_ecdh_compute_shared()) are supported for now. Contributed by
     Nicholas Wilson in pull request #348.

API Changes
   * Extend the public API with the function of mbedtls_net_poll() to allow user
     applications to wait for a network context to become ready before reading
     or writing.
   * Add function mbedtls_ssl_check_pending() to the public API to allow
     a check for whether more more data is pending to be processed in the
     internal message buffers.
     This function is necessary to determine when it is safe to idle on the
     underlying transport in case event-driven IO is used.

Bugfix
   * Fix a spurious uninitialized variable warning in cmac.c. Fix independently
     contributed by Brian J Murray and David Brown.
   * Add missing dependencies in test suites that led to build failures
     in configurations that omit certain hashes or public-key algorithms.
     Fixes #1040.
   * Fix C89 incompatibility in benchmark.c. Contributed by Brendan Shanks.
     #1353
   * Add missing dependencies for MBEDTLS_HAVE_TIME_DATE and
     MBEDTLS_VERSION_FEATURES in some test suites. Contributed by
     Deomid Ryabkov. Fixes #1299, #1475.
   * Fix the Makefile build process for building shared libraries on Mac OS X.
     Fixed by mnacamura.
   * Fix parsing of PKCS#8 encoded Elliptic Curve keys. Previously Mbed TLS was
     unable to parse keys which had only the optional parameters field of the
     ECPrivateKey structure. Found by Jethro Beekman, fixed in #1379.
   * Return the plaintext data more quickly on unpadded CBC decryption, as
     stated in the mbedtls_cipher_update() documentation. Contributed by
     Andy Leiserson.
   * Fix overriding and ignoring return values when parsing and writing to
     a file in pk_sign program. Found by kevlut in #1142.
   * Restrict usage of error code MBEDTLS_ERR_SSL_WANT_READ to situations
     where data needs to be fetched from the underlying transport in order
     to make progress. Previously, this error code was also occasionally
     returned when unexpected messages were being discarded, ignoring that
     further messages could potentially already be pending to be processed
     in the internal buffers; these cases led to deadlocks when event-driven
     I/O was used. Found and reported by Hubert Mis in #772.
   * Fix buffer length assertions in the ssl_parse_certificate_request()
     function which leads to a potential one byte overread of the message
     buffer.
   * Fix invalid buffer sizes passed to zlib during record compression and
     decompression.
   * Fix the soversion of libmbedcrypto to match the soversion of the
     maintained 2.7 branch. The soversion was increased in Mbed TLS
     version 2.7.1 to reflect breaking changes in that release, but the
     increment was missed in 2.8.0 and later releases outside of the 2.7 branch.

Changes
   * Remove some redundant code in bignum.c. Contributed by Alexey Skalozub.
   * Support cmake builds where Mbed TLS is a subproject. Fix contributed
     independently by Matthieu Volat and Arne Schwabe.
   * Improve testing in configurations that omit certain hashes or
     public-key algorithms. Includes contributions by Gert van Dijk.
   * Improve negative testing of X.509 parsing.
   * Do not define global mutexes around readdir() and gmtime() in
     configurations where the feature is disabled. Found and fixed by Gergely
     Budai.
   * Harden the function mbedtls_ssl_config_free() against misuse, so that it
     doesn't leak memory if the user doesn't use mbedtls_ssl_conf_psk() and
     instead incorrectly manipulates the configuration structure directly.
     Found and fix submitted by junyeonLEE in #1220.
   * Provide an empty implementation of mbedtls_pkcs5_pbes2() when
     MBEDTLS_ASN1_PARSE_C is not enabled. This allows the use of PBKDF2
     without PBES2. Fixed by Marcos Del Sol Vives.
   * Add the order of the base point as N in the mbedtls_ecp_group structure
     for Curve25519 (other curves had it already). Contributed by Nicholas
     Wilson #481
   * Improve the documentation of mbedtls_net_accept(). Contributed by Ivan
     Krylov.
   * Improve the documentation of mbedtls_ssl_write(). Suggested by
     Paul Sokolovsky in #1356.
   * Add an option in the Makefile to support ar utilities where the operation
     letter must not be prefixed by '-', such as LLVM. Found and fixed by
     Alex Hixon.
   * Allow configuring the shared library extension by setting the DLEXT
     environment variable when using the project makefiles.
   * Optimize unnecessary zeroing in mbedtls_mpi_copy. Based on a contribution
     by Alexey Skalozub in #405.
   * In the SSL module, when f_send, f_recv or f_recv_timeout report
     transmitting more than the required length, return an error. Raised by
     Sam O'Connor in #1245.
   * Improve robustness of mbedtls_ssl_derive_keys against the use of
     HMAC functions with non-HMAC ciphersuites. Independently contributed
     by Jiayuan Chen in #1377. Fixes #1437.
   * Improve security of RSA key generation by including criteria from
     FIPS 186-4. Contributed by Jethro Beekman. #1380
   * Declare functions in header files even when an alternative implementation
     of the corresponding module is activated by defining the corresponding
     MBEDTLS_XXX_ALT macro. This means that alternative implementations do
     not need to copy the declarations, and ensures that they will have the
     same API.
   * Add platform setup and teardown calls in test suites.

= mbed TLS 2.8.0 branch released 2018-03-16

Default behavior changes
   * The truncated HMAC extension now conforms to RFC 6066. This means
     that when both sides of a TLS connection negotiate the truncated
     HMAC extension, Mbed TLS can now interoperate with other
     compliant implementations, but this breaks interoperability with
     prior versions of Mbed TLS. To restore the old behavior, enable
     the (deprecated) option MBEDTLS_SSL_TRUNCATED_HMAC_COMPAT in
     config.h. Found by Andreas Walz (ivESK, Offenburg University of
     Applied Sciences).

Security
   * Fix implementation of the truncated HMAC extension. The previous
     implementation allowed an offline 2^80 brute force attack on the
     HMAC key of a single, uninterrupted connection (with no
     resumption of the session).
   * Verify results of RSA private key operations to defend
     against Bellcore glitch attack.
   * Fix a buffer overread in ssl_parse_server_key_exchange() that could cause
     a crash on invalid input.
   * Fix a buffer overread in ssl_parse_server_psk_hint() that could cause a
     crash on invalid input.
   * Fix CRL parsing to reject CRLs containing unsupported critical
     extensions. Found by Falko Strenzke and Evangelos Karatsiolis.

Features
   * Extend PKCS#8 interface by introducing support for the entire SHA
     algorithms family when encrypting private keys using PKCS#5 v2.0.
     This allows reading encrypted PEM files produced by software that
     uses PBKDF2-SHA2, such as OpenSSL 1.1. Submitted by Antonio Quartulli,
     OpenVPN Inc. Fixes #1339
   * Add support for public keys encoded in PKCS#1 format. #1122

New deprecations
   * Deprecate support for record compression (configuration option
     MBEDTLS_ZLIB_SUPPORT).

Bugfix
   * Fix the name of a DHE parameter that was accidentally changed in 2.7.0.
     Fixes #1358.
   * Fix test_suite_pk to work on 64-bit ILP32 systems. #849
   * Fix mbedtls_x509_crt_profile_suiteb, which used to reject all certificates
     with flag MBEDTLS_X509_BADCERT_BAD_PK even when the key type was correct.
     In the context of SSL, this resulted in handshake failure. Reported by
     daniel in the Mbed TLS forum. #1351
   * Fix Windows x64 builds with the included mbedTLS.sln file. #1347
   * Fix setting version TLSv1 as minimal version, even if TLS 1
     is not enabled. Set MBEDTLS_SSL_MIN_MAJOR_VERSION
     and MBEDTLS_SSL_MIN_MINOR_VERSION instead of
     MBEDTLS_SSL_MAJOR_VERSION_3 and MBEDTLS_SSL_MINOR_VERSION_1. #664
   * Fix compilation error on Mingw32 when _TRUNCATE is defined. Use _TRUNCATE
     only if __MINGW32__ not defined. Fix suggested by Thomas Glanzmann and
     Nick Wilson on issue #355
   * In test_suite_pk, pass valid parameters when testing for hash length
     overflow. #1179
   * Fix memory allocation corner cases in memory_buffer_alloc.c module. Found
     by Guido Vranken. #639
   * Log correct number of ciphersuites used in Client Hello message. #918
   * Fix X509 CRT parsing that would potentially accept an invalid tag when
     parsing the subject alternative names.
   * Fix a possible arithmetic overflow in ssl_parse_server_key_exchange()
     that could cause a key exchange to fail on valid data.
   * Fix a possible arithmetic overflow in ssl_parse_server_psk_hint() that
     could cause a key exchange to fail on valid data.
   * Don't define mbedtls_aes_decrypt and mbedtls_aes_encrypt under
     MBEDTLS_DEPRECATED_REMOVED. #1388
   * Fix a 1-byte heap buffer overflow (read-only) during private key parsing.
     Found through fuzz testing.

Changes
   * Fix tag lengths and value ranges in the documentation of CCM encryption.
     Contributed by Mathieu Briand.
   * Fix typo in a comment ctr_drbg.c. Contributed by Paul Sokolovsky.
   * Remove support for the library reference configuration for picocoin.
   * MD functions deprecated in 2.7.0 are no longer inline, to provide
     a migration path for those depending on the library's ABI.
   * Clarify the documentation of mbedtls_ssl_setup.
   * Use (void) when defining functions with no parameters. Contributed by
     Joris Aerts. #678

= mbed TLS 2.7.0 branch released 2018-02-03

Security
   * Fix a heap corruption issue in the implementation of the truncated HMAC
     extension. When the truncated HMAC extension is enabled and CBC is used,
     sending a malicious application packet could be used to selectively corrupt
     6 bytes on the peer's heap, which could potentially lead to crash or remote
     code execution. The issue could be triggered remotely from either side in
     both TLS and DTLS. CVE-2018-0488
   * Fix a buffer overflow in RSA-PSS verification when the hash was too large
     for the key size, which could potentially lead to crash or remote code
     execution. Found by Seth Terashima, Qualcomm Product Security Initiative,
     Qualcomm Technologies Inc. CVE-2018-0487
   * Fix buffer overflow in RSA-PSS verification when the unmasked data is all
     zeros.
   * Fix an unsafe bounds check in ssl_parse_client_psk_identity() when adding
     64 KiB to the address of the SSL buffer and causing a wrap around.
   * Fix a potential heap buffer overflow in mbedtls_ssl_write(). When the (by
     default enabled) maximum fragment length extension is disabled in the
     config and the application data buffer passed to mbedtls_ssl_write
     is larger than the internal message buffer (16384 bytes by default), the
     latter overflows. The exploitability of this issue depends on whether the
     application layer can be forced into sending such large packets. The issue
     was independently reported by Tim Nordell via e-mail and by Florin Petriuc
     and sjorsdewit on GitHub. Fix proposed by Florin Petriuc in #1022.
     Fixes #707.
   * Add a provision to prevent compiler optimizations breaking the time
     constancy of mbedtls_ssl_safer_memcmp().
   * Ensure that buffers are cleared after use if they contain sensitive data.
     Changes were introduced in multiple places in the library.
   * Set PEM buffer to zero before freeing it, to avoid decoded private keys
     being leaked to memory after release.
   * Fix dhm_check_range() failing to detect trivial subgroups and potentially
     leaking 1 bit of the private key. Reported by prashantkspatil.
   * Make mbedtls_mpi_read_binary() constant-time with respect to the input
     data. Previously, trailing zero bytes were detected and omitted for the
     sake of saving memory, but potentially leading to slight timing
     differences. Reported by Marco Macchetti, Kudelski Group.
   * Wipe stack buffer temporarily holding EC private exponent
     after keypair generation.
   * Fix a potential heap buffer over-read in ALPN extension parsing
     (server-side). Could result in application crash, but only if an ALPN
     name larger than 16 bytes had been configured on the server.
   * Change default choice of DHE parameters from untrustworthy RFC 5114
     to RFC 3526 containing parameters generated in a nothing-up-my-sleeve
     manner.

Features
   * Allow comments in test data files.
   * The selftest program can execute a subset of the tests based on command
     line arguments.
   * New unit tests for timing. Improve the self-test to be more robust
     when run on a heavily-loaded machine.
   * Add alternative implementation support for CCM and CMAC (MBEDTLS_CCM_ALT,
     MBEDTLS_CMAC_ALT). Submitted by Steven Cooreman, Silicon Labs.
   * Add support for alternative implementations of GCM, selected by the
     configuration flag MBEDTLS_GCM_ALT.
   * Add support for alternative implementations for ECDSA, controlled by new
     configuration flags MBEDTLS_ECDSA_SIGN_ALT, MBEDTLS_ECDSA_VERIFY_ALT and
     MBEDTLS_ECDSDA_GENKEY_AT in config.h.
     The following functions from the ECDSA module can be replaced
     with alternative implementation:
     mbedtls_ecdsa_sign(), mbedtls_ecdsa_verify() and mbedtls_ecdsa_genkey().
   * Add support for alternative implementation of ECDH, controlled by the
     new configuration flags MBEDTLS_ECDH_COMPUTE_SHARED_ALT and
     MBEDTLS_ECDH_GEN_PUBLIC_ALT in config.h.
     The following functions from the ECDH module can be replaced
     with an alternative implementation:
     mbedtls_ecdh_gen_public() and mbedtls_ecdh_compute_shared().
   * Add support for alternative implementation of ECJPAKE, controlled by
     the new configuration flag MBEDTLS_ECJPAKE_ALT.
   * Add mechanism to provide alternative implementation of the DHM module.

API Changes
   * Extend RSA interface by multiple functions allowing structure-
     independent setup and export of RSA contexts. Most notably,
     mbedtls_rsa_import() and mbedtls_rsa_complete() are introduced for setting
     up RSA contexts from partial key material and having them completed to the
     needs of the implementation automatically. This allows to setup private RSA
     contexts from keys consisting of N,D,E only, even if P,Q are needed for the
     purpose or CRT and/or blinding.
   * The configuration option MBEDTLS_RSA_ALT can be used to define alternative
     implementations of the RSA interface declared in rsa.h.
   * The following functions in the message digest modules (MD2, MD4, MD5,
     SHA1, SHA256, SHA512) have been deprecated and replaced as shown below.
     The new functions change the return type from void to int to allow
     returning error codes when using MBEDTLS_<MODULE>_ALT.
     mbedtls_<MODULE>_starts() -> mbedtls_<MODULE>_starts_ret()
     mbedtls_<MODULE>_update() -> mbedtls_<MODULE>_update_ret()
     mbedtls_<MODULE>_finish() -> mbedtls_<MODULE>_finish_ret()
     mbedtls_<MODULE>_process() -> mbedtls_internal_<MODULE>_process()

New deprecations
   * Deprecate usage of RSA primitives with non-matching key-type
     (e.g. signing with a public key).
   * Direct manipulation of structure fields of RSA contexts is deprecated.
     Users are advised to use the extended RSA API instead.
   * Deprecate usage of message digest functions that return void
     (mbedtls_<MODULE>_starts, mbedtls_<MODULE>_update,
     mbedtls_<MODULE>_finish and mbedtls_<MODULE>_process where <MODULE> is
     any of MD2, MD4, MD5, SHA1, SHA256, SHA512) in favor of functions
     that can return an error code.
   * Deprecate untrustworthy DHE parameters from RFC 5114. Superseded by
     parameters from RFC 3526 or the newly added parameters from RFC 7919.
   * Deprecate hex string DHE constants MBEDTLS_DHM_RFC3526_MODP_2048_P etc.
     Supserseded by binary encoded constants MBEDTLS_DHM_RFC3526_MODP_2048_P_BIN
     etc.
   * Deprecate mbedtls_ssl_conf_dh_param() for setting default DHE parameters
     from hex strings. Superseded by mbedtls_ssl_conf_dh_param_bin()
     accepting DHM parameters in binary form, matching the new constants.

Bugfix
   * Fix ssl_parse_record_header() to silently discard invalid DTLS records
     as recommended in RFC 6347 Section *******.
   * Fix memory leak in mbedtls_ssl_set_hostname() when called multiple times.
     Found by projectgus and Jethro Beekman, #836.
   * Fix usage help in ssl_server2 example. Found and fixed by Bei Lin.
   * Parse signature algorithm extension when renegotiating. Previously,
     renegotiated handshakes would only accept signatures using SHA-1
     regardless of the peer's preferences, or fail if SHA-1 was disabled.
   * Fix leap year calculation in x509_date_is_valid() to ensure that invalid
     dates on leap years with 100 and 400 intervals are handled correctly. Found
     by Nicholas Wilson. #694
   * Fix some invalid RSA-PSS signatures with keys of size 8N+1 that were
     accepted. Generating these signatures required the private key.
   * Fix out-of-memory problem when parsing 4096-bit PKCS8-encrypted RSA keys.
     Found independently by Florian in the mbed TLS forum and by Mishamax.
     #878, #1019.
   * Fix variable used before assignment compilation warnings with IAR
     toolchain. Found by gkerrien38.
   * Fix unchecked return codes from AES, DES and 3DES functions in
     pem_aes_decrypt(), pem_des_decrypt() and pem_des3_decrypt() respectively.
     If a call to one of the functions of the cryptographic primitive modules
     failed, the error may not be noticed by the function
     mbedtls_pem_read_buffer() causing it to return invalid values. Found by
     Guido Vranken. #756
   * Include configuration file in md.h, to fix compilation warnings.
     Reported by aaronmdjones in #1001
   * Correct extraction of signature-type from PK instance in X.509 CRT and CSR
     writing routines that prevented these functions to work with alternative
     RSA implementations. Raised by J.B. in the Mbed TLS forum. Fixes #1011.
   * Don't print X.509 version tag for v1 CRT's, and omit extensions for
     non-v3 CRT's.
   * Fix bugs in RSA test suite under MBEDTLS_NO_PLATFORM_ENTROPY. #1023 #1024
   * Fix net_would_block() to avoid modification by errno through fcntl() call.
     Found by nkolban. Fixes #845.
   * Fix handling of handshake messages in mbedtls_ssl_read() in case
     MBEDTLS_SSL_RENEGOTIATION is disabled. Found by erja-gp.
   * Add a check for invalid private parameters in mbedtls_ecdsa_sign().
     Reported by Yolan Romailler.
   * Fix word size check in in pk.c to not depend on MBEDTLS_HAVE_INT64.
   * Fix incorrect unit in benchmark output. #850
   * Add size-checks for record and handshake message content, securing
     fragile yet non-exploitable code-paths.
   * Fix crash when calling mbedtls_ssl_cache_free() twice. Found by
     MilenkoMitrovic, #1104
   * Fix mbedtls_timing_alarm(0) on Unix and MinGW.
   * Fix use of uninitialized memory in mbedtls_timing_get_timer() when reset=1.
   * Fix possible memory leaks in mbedtls_gcm_self_test().
   * Added missing return code checks in mbedtls_aes_self_test().
   * Fix issues in RSA key generation program programs/x509/rsa_genkey and the
     RSA test suite where the failure of CTR DRBG initialization lead to
     freeing an RSA context and several MPI's without proper initialization
     beforehand.
   * Fix error message in programs/pkey/gen_key.c. Found and fixed by Chris Xue.
   * Fix programs/pkey/dh_server.c so that it actually works with dh_client.c.
     Found and fixed by Martijn de Milliano.
   * Fix an issue in the cipher decryption with the mode
     MBEDTLS_PADDING_ONE_AND_ZEROS that sometimes accepted invalid padding.
     Note, this padding mode is not used by the TLS protocol. Found and fixed by
     Micha Kraus.
   * Fix the entropy.c module to not call mbedtls_sha256_starts() or
     mbedtls_sha512_starts() in the mbedtls_entropy_init() function.
   * Fix the entropy.c module to ensure that mbedtls_sha256_init() or
     mbedtls_sha512_init() is called before operating on the relevant context
     structure. Do not assume that zeroizing a context is a correct way to
     reset it. Found independently by ccli8 on Github.
   * In mbedtls_entropy_free(), properly free the message digest context.
   * Fix status handshake status message in programs/ssl/dtls_client.c. Found
     and fixed by muddog.

Changes
   * Extend cert_write example program by options to set the certificate version
     and the message digest. Further, allow enabling/disabling of authority
     identifier, subject identifier and basic constraints extensions.
   * Only check for necessary RSA structure fields in `mbedtls_rsa_private`. In
     particular, don't require P,Q if neither CRT nor blinding are
     used. Reported and fix proposed independently by satur9nine and sliai
     on GitHub.
   * Only run AES-192 self-test if AES-192 is available. Fixes #963.
   * Tighten the RSA PKCS#1 v1.5 signature verification code and remove the
     undeclared dependency of the RSA module on the ASN.1 module.
   * Update all internal usage of deprecated message digest functions to the
     new ones with return codes. In particular, this modifies the
     mbedtls_md_info_t structure. Propagate errors from these functions
     everywhere except some locations in the ssl_tls.c module.
   * Improve CTR_DRBG error handling by propagating underlying AES errors.
   * Add MBEDTLS_ERR_XXX_HW_ACCEL_FAILED error codes for all cryptography
     modules where the software implementation can be replaced by a hardware
     implementation.
   * Add explicit warnings for the use of MD2, MD4, MD5, SHA-1, DES and ARC4
     throughout the library.

= mbed TLS 2.6.0 branch released 2017-08-10

Security
   * Fix authentication bypass in SSL/TLS: when authmode is set to optional,
     mbedtls_ssl_get_verify_result() would incorrectly return 0 when the peer's
     X.509 certificate chain had more than MBEDTLS_X509_MAX_INTERMEDIATE_CA
     (default: 8) intermediates, even when it was not trusted. This could be
     triggered remotely from either side. (With authmode set to 'required'
     (the default), the handshake was correctly aborted).
   * Reliably wipe sensitive data after use in the AES example applications
     programs/aes/aescrypt2 and programs/aes/crypt_and_hash.
     Found by Laurent Simon.

Features
   * Add the functions mbedtls_platform_setup() and mbedtls_platform_teardown()
     and the context struct mbedtls_platform_context to perform
     platform-specific setup and teardown operations. The macro
     MBEDTLS_PLATFORM_SETUP_TEARDOWN_ALT allows the functions to be overridden
     by the user in a platform_alt.h file. These new functions are required in
     some embedded environments to provide a means of initialising underlying
     cryptographic acceleration hardware.

API Changes
   * Reverted API/ABI breaking changes introduced in mbed TLS 2.5.1, to make the
     API consistent with mbed TLS 2.5.0. Specifically removed the inline
     qualifier from the functions mbedtls_aes_decrypt, mbedtls_aes_encrypt,
     mbedtls_ssl_ciphersuite_uses_ec and mbedtls_ssl_ciphersuite_uses_psk. Found
     by James Cowgill. #978
   * Certificate verification functions now set flags to -1 in case the full
     chain was not verified due to an internal error (including in the verify
     callback) or chain length limitations.
   * With authmode set to optional, the TLS handshake is now aborted if the
     verification of the peer's certificate failed due to an overlong chain or
     a fatal error in the verify callback.

Bugfix
   * Add a check if iv_len is zero in GCM, and return an error if it is zero.
     Reported by roberto. #716
   * Replace preprocessor condition from #if defined(MBEDTLS_THREADING_PTHREAD)
     to #if defined(MBEDTLS_THREADING_C) as the library cannot assume they will
     always be implemented by pthread support. #696
   * Fix a resource leak on Windows platforms in mbedtls_x509_crt_parse_path(),
     in the case of an error. Found by redplait. #590
   * Add MBEDTLS_MPI_CHK to check for error value of mbedtls_mpi_fill_random.
     Reported and fix suggested by guidovranken. #740
   * Fix conditional preprocessor directives in bignum.h to enable 64-bit
     compilation when using ARM Compiler 6.
   * Fix a potential integer overflow in the version verification for DER
     encoded X.509 CRLs. The overflow could enable maliciously constructed CRLs
     to bypass the version verification check. Found by Peng Li/Yueh-Hsun Lin,
     KNOX Security, Samsung Research America
   * Fix potential integer overflow in the version verification for DER
     encoded X.509 CSRs. The overflow could enable maliciously constructed CSRs
     to bypass the version verification check. Found by Peng Li/Yueh-Hsun Lin,
     KNOX Security, Samsung Research America
   * Fix a potential integer overflow in the version verification for DER
     encoded X.509 certificates. The overflow could enable maliciously
     constructed certificates to bypass the certificate verification check.
   * Fix a call to the libc function time() to call the platform abstraction
     function mbedtls_time() instead. Found by wairua. #666
   * Avoid shadowing of time and index functions through mbed TLS function
     arguments. Found by inestlerode. #557.

Changes
   * Added config.h option MBEDTLS_NO_UDBL_DIVISION, to prevent the use of
     64-bit division. This is useful on embedded platforms where 64-bit division
     created a dependency on external libraries. #708
   * Removed mutexes from ECP hardware accelerator code. Now all hardware
     accelerator code in the library leaves concurrency handling to the
     platform. Reported by Steven Cooreman. #863
   * Define the macro MBEDTLS_AES_ROM_TABLES in the configuration file
     config-no-entropy.h to reduce the RAM footprint.
   * Added a test script that can be hooked into git that verifies commits
     before they are pushed.
   * Improve documentation of PKCS1 decryption functions.

= mbed TLS 2.5.1 released 2017-06-21

Security
   * Fixed unlimited overread of heap-based buffer in mbedtls_ssl_read().
     The issue could only happen client-side with renegotiation enabled.
     Could result in DoS (application crash) or information leak
     (if the application layer sent data read from mbedtls_ssl_read()
     back to the server or to a third party). Can be triggered remotely.
   * Removed SHA-1 and RIPEMD-160 from the default hash algorithms for
     certificate verification. SHA-1 can be turned back on with a compile-time
     option if needed.
   * Fixed offset in FALLBACK_SCSV parsing that caused TLS server to fail to
     detect it sometimes. Reported by Hugo Leisink. #810
   * Tighten parsing of RSA PKCS#1 v1.5 signatures, to avoid a
     potential Bleichenbacher/BERserk-style attack.

Bugfix
   * Remove size zero arrays from ECJPAKE test suite. Size zero arrays are not
     valid C and they prevented the test from compiling in Visual Studio 2015
     and with GCC using the -Wpedantic compilation option.
   * Fix insufficient support for signature-hash-algorithm extension,
     resulting in compatibility problems with Chrome. Found by hfloyrd. #823
   * Fix behaviour that hid the original cause of fatal alerts in some cases
     when sending the alert failed. The fix makes sure not to hide the error
     that triggered the alert.
   * Fix SSLv3 renegotiation behaviour and stop processing data received from
     peer after sending a fatal alert to refuse a renegotiation attempt.
     Previous behaviour was to keep processing data even after the alert has
     been sent.
   * Accept empty trusted CA chain in authentication mode
     MBEDTLS_SSL_VERIFY_OPTIONAL. Found by Jethro Beekman. #864
   * Fix implementation of mbedtls_ssl_parse_certificate() to not annihilate
     fatal errors in authentication mode MBEDTLS_SSL_VERIFY_OPTIONAL and to
     reflect bad EC curves within verification result.
   * Fix bug that caused the modular inversion function to accept the invalid
     modulus 1 and therefore to hang. Found by blaufish. #641.
   * Fix incorrect sign computation in modular exponentiation when the base is
     a negative MPI. Previously the result was always negative. Found by Guido
     Vranken.
   * Fix a numerical underflow leading to stack overflow in mpi_read_file()
     that was triggered uppon reading an empty line. Found by Guido Vranken.

Changes
   * Send fatal alerts in more cases. The previous behaviour was to skip
     sending the fatal alert and just drop the connection.
   * Clarify ECDSA documentation and improve the sample code to avoid
     misunderstanding and potentially dangerous use of the API. Pointed out
     by Jean-Philippe Aumasson.

= mbed TLS 2.5.0 branch released 2017-05-17

Security
   * Wipe stack buffers in RSA private key operations
     (rsa_rsaes_pkcs1_v15_decrypt(), rsa_rsaes_oaep_decrypt). Found by Laurent
     Simon.
   * Add exponent blinding to RSA private operations as a countermeasure
     against side-channel attacks like the cache attack described in
     https://arxiv.org/abs/1702.08719v2.
     Found and fix proposed by Michael Schwarz, Samuel Weiser, Daniel Gruss,
     Clémentine Maurice and Stefan Mangard.

Features
   * Add hardware acceleration support for the Elliptic Curve Point module.
     This involved exposing parts of the internal interface to enable
     replacing the core functions and adding and alternative, module level
     replacement support for enabling the extension of the interface.
   * Add a new configuration option to 'mbedtls_ssl_config' to enable
     suppressing the CA list in Certificate Request messages. The default
     behaviour has not changed, namely every configured CAs name is included.

API Changes
   * The following functions in the AES module have been deprecated and replaced
     by the functions shown below. The new functions change the return type from
     void to int to allow returning error codes when using MBEDTLS_AES_ALT,
     MBEDTLS_AES_DECRYPT_ALT or MBEDTLS_AES_ENCRYPT_ALT.
     mbedtls_aes_decrypt() -> mbedtls_internal_aes_decrypt()
     mbedtls_aes_encrypt() -> mbedtls_internal_aes_encrypt()

Bugfix
   * Remove macros from compat-1.3.h that correspond to deleted items from most
     recent versions of the library. Found by Kyle Keen.
   * Fixed issue in the Threading module that prevented mutexes from
     initialising. Found by sznaider. #667 #843
   * Add checks in the PK module for the RSA functions on 64-bit systems.
     The PK and RSA modules use different types for passing hash length and
     without these checks the type cast could lead to data loss. Found by Guido
     Vranken.

= mbed TLS 2.4.2 branch released 2017-03-08

Security
   * Add checks to prevent signature forgeries for very large messages while
     using RSA through the PK module in 64-bit systems. The issue was caused by
     some data loss when casting a size_t to an unsigned int value in the
     functions rsa_verify_wrap(), rsa_sign_wrap(), rsa_alt_sign_wrap() and
     mbedtls_pk_sign(). Found by Jean-Philippe Aumasson.
   * Fixed potential livelock during the parsing of a CRL in PEM format in
     mbedtls_x509_crl_parse(). A string containing a CRL followed by trailing
     characters after the footer could result in the execution of an infinite
     loop. The issue can be triggered remotely. Found by Greg Zaverucha,
     Microsoft.
   * Removed MD5 from the allowed hash algorithms for CertificateRequest and
     CertificateVerify messages, to prevent SLOTH attacks against TLS 1.2.
     Introduced by interoperability fix for #513.
   * Fixed a bug that caused freeing a buffer that was allocated on the stack,
     when verifying the validity of a key on secp224k1. This could be
     triggered remotely for example with a maliciously constructed certificate
     and potentially could lead to remote code execution on some platforms.
     Reported independently by rongsaws and Aleksandar Nikolic, Cisco Talos
     team. #569 CVE-2017-2784

Bugfix
   * Fix output certificate verification flags set by x509_crt_verify_top() when
     traversing a chain of trusted CA. The issue would cause both flags,
     MBEDTLS_X509_BADCERT_NOT_TRUSTED and MBEDTLS_X509_BADCERT_EXPIRED, to be
     set when the verification conditions are not met regardless of the cause.
     Found by Harm Verhagen and inestlerode. #665 #561
   * Fix the redefinition of macro ssl_set_bio to an undefined symbol
     mbedtls_ssl_set_bio_timeout in compat-1.3.h, by removing it.
     Found by omlib-lin. #673
   * Fix unused variable/function compilation warnings in pem.c, x509_crt.c and
     x509_csr.c that are reported when building mbed TLS with a config.h that
     does not define MBEDTLS_PEM_PARSE_C. Found by omnium21. #562
   * Fix incorrect renegotiation condition in ssl_check_ctr_renegotiate() that
     would compare 64 bits of the record counter instead of 48 bits as indicated
     in RFC 6347 Section 4.3.1. This could cause the execution of the
     renegotiation routines at unexpected times when the protocol is DTLS. Found
     by wariua. #687
   * Fixed multiple buffer overreads in mbedtls_pem_read_buffer() when parsing
     the input string in PEM format to extract the different components. Found
     by Eyal Itkin.
   * Fixed potential arithmetic overflow in mbedtls_ctr_drbg_reseed() that could
     cause buffer bound checks to be bypassed. Found by Eyal Itkin.
   * Fixed potential arithmetic overflows in mbedtls_cipher_update() that could
     cause buffer bound checks to be bypassed. Found by Eyal Itkin.
   * Fixed potential arithmetic overflow in mbedtls_md2_update() that could
     cause buffer bound checks to be bypassed. Found by Eyal Itkin.
   * Fixed potential arithmetic overflow in mbedtls_base64_decode() that could
     cause buffer bound checks to be bypassed. Found by Eyal Itkin.
   * Fixed heap overreads in mbedtls_x509_get_time(). Found by Peng
     Li/Yueh-Hsun Lin, KNOX Security, Samsung Research America.
   * Fix potential memory leak in mbedtls_x509_crl_parse(). The leak was caused
     by missing calls to mbedtls_pem_free() in cases when a
     MBEDTLS_ERR_PEM_NO_HEADER_FOOTER_PRESENT error was encountered. Found and
     fix proposed by Guido Vranken. #722
   * Fixed the templates used to generate project and solution files for Visual
     Studio 2015 as well as the files themselves, to remove a build warning
     generated in Visual Studio 2015. Reported by Steve Valliere. #742
   * Fix a resource leak in ssl_cookie, when using MBEDTLS_THREADING_C.
     Raised and fix suggested by Alan Gillingham in the mbed TLS forum. #771
   * Fix 1 byte buffer overflow in mbedtls_mpi_write_string() when the MPI
     number to write in hexadecimal is negative and requires an odd number of
     digits. Found and fixed by Guido Vranken.
   * Fix unlisted DES configuration dependency in some pkparse test cases. Found
     by inestlerode. #555

= mbed TLS 2.4.1 branch released 2016-12-13

Changes
   * Update to CMAC test data, taken from - NIST Special Publication 800-38B -
     Recommendation for Block Cipher Modes of Operation: The CMAC Mode for
     Authentication – October  2016

= mbed TLS 2.4.0 branch released 2016-10-17

Security
   * Removed the MBEDTLS_SSL_AEAD_RANDOM_IV option, because it was not compliant
     with RFC-5116 and could lead to session key recovery in very long TLS
     sessions. "Nonce-Disrespecting Adversaries Practical Forgery Attacks on GCM in
     TLS" - H. Bock, A. Zauner, S. Devlin, J. Somorovsky, P. Jovanovic.
     https://eprint.iacr.org/2016/475.pdf
   * Fixed potential stack corruption in mbedtls_x509write_crt_der() and
     mbedtls_x509write_csr_der() when the signature is copied to the buffer
     without checking whether there is enough space in the destination. The
     issue cannot be triggered remotely. Found by Jethro Beekman.

Features
   * Added support for CMAC for AES and 3DES and AES-CMAC-PRF-128, as defined by
     NIST SP 800-38B, RFC-4493 and RFC-4615.
   * Added hardware entropy selftest to verify that the hardware entropy source
     is functioning correctly.
   * Added a script to print build environment info for diagnostic use in test
     scripts, which is also now called by all.sh.
   * Added the macro MBEDTLS_X509_MAX_FILE_PATH_LEN that enables the user to
     configure the maximum length of a file path that can be buffered when
     calling mbedtls_x509_crt_parse_path().
   * Added a configuration file config-no-entropy.h that configures the subset of
     library features that do not require an entropy source.
   * Added the macro MBEDTLS_ENTROPY_MIN_HARDWARE in config.h. This allows users
     to configure the minimum number of bytes for entropy sources using the
     mbedtls_hardware_poll() function.

Bugfix
   * Fix for platform time abstraction to avoid dependency issues where a build
     may need time but not the standard C library abstraction, and added
     configuration consistency checks to check_config.h
   * Fix dependency issue in Makefile to allow parallel builds.
   * Fix incorrect handling of block lengths in crypt_and_hash.c sample program,
     when GCM is used. Found by udf2457. #441
   * Fix for key exchanges based on ECDH-RSA or ECDH-ECDSA which weren't
     enabled unless others were also present. Found by David Fernandez. #428
   * Fix for out-of-tree builds using CMake. Found by jwurzer, and fix based on
     a contribution from Tobias Tangemann. #541
   * Fixed cert_app.c sample program for debug output and for use when no root
     certificates are provided.
   * Fix conditional statement that would cause a 1 byte overread in
     mbedtls_asn1_get_int(). Found and fixed by Guido Vranken. #599
   * Fixed pthread implementation to avoid unintended double initialisations
     and double frees. Found by Niklas Amnebratt.
   * Fixed the sample applications gen_key.c, cert_req.c and cert_write.c for
     builds where the configuration MBEDTLS_PEM_WRITE_C is not defined. Found
     by inestlerode. #559.
   * Fix mbedtls_x509_get_sig() to update the ASN1 type in the mbedtls_x509_buf
     data structure until after error checks are successful. Found by
     subramanyam-c. #622
   * Fix documentation and implementation missmatch for function arguments of
     mbedtls_gcm_finish(). Found by cmiatpaar. #602
   * Guarantee that P>Q at RSA key generation. Found by inestlerode. #558
   * Fix potential byte overread when verifying malformed SERVER_HELLO in
     ssl_parse_hello_verify_request() for DTLS. Found by Guido Vranken.
   * Fix check for validity of date when parsing in mbedtls_x509_get_time().
     Found by subramanyam-c. #626
   * Fix compatibility issue with Internet Explorer client authentication,
     where the limited hash choices prevented the client from sending its
     certificate. Found by teumas. #513
   * Fix compilation without MBEDTLS_SELF_TEST enabled.

Changes
   * Extended test coverage of special cases, and added new timing test suite.
   * Removed self-tests from the basic-built-test.sh script, and added all
     missing self-tests to the test suites, to ensure self-tests are only
     executed once.
   * Added support for 3 and 4 byte lengths to mbedtls_asn1_write_len().
   * Added support for a Yotta specific configuration file -
     through the symbol YOTTA_CFG_MBEDTLS_TARGET_CONFIG_FILE.
   * Added optimization for code space for X.509/OID based on configured
     features. Contributed by Aviv Palivoda.
   * Renamed source file library/net.c to library/net_sockets.c to avoid
     naming collision in projects which also have files with the common name
     net.c. For consistency, the corresponding header file, net.h, is marked as
     deprecated, and its contents moved to net_sockets.h.
   * Changed the strategy for X.509 certificate parsing and validation, to no
     longer disregard certificates with unrecognised fields.

= mbed TLS 2.3.0 branch released 2016-06-28

Security
   * Fix missing padding length check in mbedtls_rsa_rsaes_pkcs1_v15_decrypt
     required by PKCS1 v2.2
   * Fix potential integer overflow to buffer overflow in
     mbedtls_rsa_rsaes_pkcs1_v15_encrypt and mbedtls_rsa_rsaes_oaep_encrypt
     (not triggerable remotely in (D)TLS).
   * Fix a potential integer underflow to buffer overread in
     mbedtls_rsa_rsaes_oaep_decrypt. It is not triggerable remotely in
     SSL/TLS.

Features
   * Support for platform abstraction of the standard C library time()
     function.

Bugfix
   * Fix bug in mbedtls_mpi_add_mpi() that caused wrong results when the three
     arguments where the same (in-place doubling). Found and fixed by Janos
     Follath. #309
   * Fix potential build failures related to the 'apidoc' target, introduced
     in the previous patch release. Found by Robert Scheck. #390 #391
   * Fix issue in Makefile that prevented building using armar. #386
   * Fix memory leak that occurred only when ECJPAKE was enabled and ECDHE and
     ECDSA was disabled in config.h . The leak didn't occur by default.
   * Fix an issue that caused valid certificates to be rejected whenever an
     expired or not yet valid certificate was parsed before a valid certificate
     in the trusted certificate list.
   * Fix bug in mbedtls_x509_crt_parse that caused trailing extra data in the
     buffer after DER certificates to be included in the raw representation.
   * Fix issue that caused a hang when generating RSA keys of odd bitlength
   * Fix bug in mbedtls_rsa_rsaes_pkcs1_v15_encrypt that made null pointer
     dereference possible.
   * Fix issue that caused a crash if invalid curves were passed to
     mbedtls_ssl_conf_curves. #373
   * Fix issue in ssl_fork_server which was preventing it from functioning. #429
   * Fix memory leaks in test framework
   * Fix test in ssl-opt.sh that does not run properly with valgrind
   * Fix unchecked calls to mmbedtls_md_setup(). Fix by Brian Murray. #502

Changes
   * On ARM platforms, when compiling with -O0 with GCC, Clang or armcc5,
     don't use the optimized assembly for bignum multiplication. This removes
     the need to pass -fomit-frame-pointer to avoid a build error with -O0.
   * Disabled SSLv3 in the default configuration.
   * Optimized mbedtls_mpi_zeroize() for MPI integer size. (Fix by Alexey
     Skalozub).
   * Fix non-compliance server extension handling. Extensions for SSLv3 are now
     ignored, as required by RFC6101.

= mbed TLS 2.2.1 released 2016-01-05

Security
   * Fix potential double free when mbedtls_asn1_store_named_data() fails to
     allocate memory. Only used for certificate generation, not triggerable
     remotely in SSL/TLS. Found by Rafał Przywara. #367
   * Disable MD5 handshake signatures in TLS 1.2 by default to prevent the
     SLOTH attack on TLS 1.2 server authentication (other attacks from the
     SLOTH paper do not apply to any version of mbed TLS or PolarSSL).
     https://www.mitls.org/pages/attacks/SLOTH

Bugfix
   * Fix over-restrictive length limit in GCM. Found by Andreas-N. #362
   * Fix bug in certificate validation that caused valid chains to be rejected
     when the first intermediate certificate has pathLenConstraint=0. Found by
     Nicholas Wilson. Introduced in mbed TLS 2.2.0. #280
   * Removed potential leak in mbedtls_rsa_rsassa_pkcs1_v15_sign(), found by
     JayaraghavendranK. #372
   * Fix suboptimal handling of unexpected records that caused interop issues
     with some peers over unreliable links. Avoid dropping an entire DTLS
     datagram if a single record in a datagram is unexpected, instead only
     drop the record and look at subsequent records (if any are present) in
     the same datagram. Found by jeannotlapin. #345

= mbed TLS 2.2.0 released 2015-11-04

Security
   * Fix potential double free if mbedtls_ssl_conf_psk() is called more than
     once and some allocation fails. Cannot be forced remotely. Found by Guido
     Vranken, Intelworks.
   * Fix potential heap corruption on Windows when
     mbedtls_x509_crt_parse_path() is passed a path longer than 2GB. Cannot be
     triggered remotely. Found by Guido Vranken, Intelworks.
   * Fix potential buffer overflow in some asn1_write_xxx() functions.
     Cannot be triggered remotely unless you create X.509 certificates based
     on untrusted input or write keys of untrusted origin. Found by Guido
     Vranken, Intelworks.
   * The X509 max_pathlen constraint was not enforced on intermediate
     certificates. Found by Nicholas Wilson, fix and tests provided by
     Janos Follath. #280 and #319

Features
   * Experimental support for EC J-PAKE as defined in Thread 1.0.0.
     Disabled by default as the specification might still change.
   * Added a key extraction callback to accees the master secret and key
     block. (Potential uses include EAP-TLS and Thread.)

Bugfix
   * Self-signed certificates were not excluded from pathlen counting,
     resulting in some valid X.509 being incorrectly rejected. Found and fix
     provided by Janos Follath. #319
   * Fix build error with configurations where ECDHE-PSK is the only key
     exchange. Found and fix provided by Chris Hammond. #270
   * Fix build error with configurations where RSA, RSA-PSK, ECDH-RSA or
     ECHD-ECDSA if the only key exchange. Multiple reports. #310
   * Fixed a bug causing some handshakes to fail due to some non-fatal alerts
     not being properly ignored. Found by mancha and Kasom Koht-arsa, #308
   * mbedtls_x509_crt_verify(_with_profile)() now also checks the key type and
     size/curve against the profile. Before that, there was no way to set a
     minimum key size for end-entity certificates with RSA keys. Found by
     Matthew Page of Scannex Electronics Ltd.
   * Fix failures in MPI on Sparc(64) due to use of bad assembly code.
     Found by Kurt Danielson. #292
   * Fix typo in name of the extKeyUsage OID. Found by inestlerode, #314
   * Fix bug in ASN.1 encoding of booleans that caused generated CA
     certificates to be rejected by some applications, including OS X
     Keychain. Found and fixed by Jonathan Leroy, Inikup.

Changes
   * Improved performance of mbedtls_ecp_muladd() when one of the scalars is 1
     or -1.

= mbed TLS 2.1.2 released 2015-10-06

Security
   * Added fix for CVE-2015-5291 to prevent heap corruption due to buffer
     overflow of the hostname or session ticket. Found by Guido Vranken,
     Intelworks.
   * Fix potential double-free if mbedtls_ssl_set_hs_psk() is called more than
     once in the same handhake and mbedtls_ssl_conf_psk() was used.
     Found and patch provided by Guido Vranken, Intelworks. Cannot be forced
     remotely.
   * Fix stack buffer overflow in pkcs12 decryption (used by
     mbedtls_pk_parse_key(file)() when the password is > 129 bytes.
     Found by Guido Vranken, Intelworks. Not triggerable remotely.
   * Fix potential buffer overflow in mbedtls_mpi_read_string().
     Found by Guido Vranken, Intelworks. Not exploitable remotely in the context
     of TLS, but might be in other uses. On 32 bit machines, requires reading a
     string of close to or larger than 1GB to exploit; on 64 bit machines, would
     require reading a string of close to or larger than 2^62 bytes.
   * Fix potential random memory allocation in mbedtls_pem_read_buffer()
     on crafted PEM input data. Found and fix provided by Guido Vranken,
     Intelworks. Not triggerable remotely in TLS. Triggerable remotely if you
     accept PEM data from an untrusted source.
   * Fix possible heap buffer overflow in base64_encoded() when the input
     buffer is 512MB or larger on 32-bit platforms. Found by Guido Vranken,
     Intelworks. Not trigerrable remotely in TLS.
   * Fix potential double-free if mbedtls_conf_psk() is called repeatedly on
     the same mbedtls_ssl_config object and memory allocation fails. Found by
     Guido Vranken, Intelworks. Cannot be forced remotely.
   * Fix potential heap buffer overflow in servers that perform client
     authentication against a crafted CA cert. Cannot be triggered remotely
     unless you allow third parties to pick trust CAs for client auth.
     Found by Guido Vranken, Intelworks.

Bugfix
   * Fix compile error in net.c with musl libc. Found and patch provided by
     zhasha (#278).
   * Fix macroization of 'inline' keyword when building as C++. (#279)

Changes
   * Added checking of hostname length in mbedtls_ssl_set_hostname() to ensure
     domain names are compliant with RFC 1035.
   * Fixed paths for check_config.h in example config files. (Found by bachp)
     (#291)

= mbed TLS 2.1.1 released 2015-09-17

Security
   * Add countermeasure against Lenstra's RSA-CRT attack for PKCS#1 v1.5
     signatures. (Found by Florian Weimer, Red Hat.)
     https://securityblog.redhat.com/2015/09/02/factoring-rsa-keys-with-tls-perfect-forward-secrecy/
   * Fix possible client-side NULL pointer dereference (read) when the client
     tries to continue the handshake after it failed (a misuse of the API).
     (Found and patch provided by Fabian Foerg, Gotham Digital Science using
     afl-fuzz.)

Bugfix
   * Fix warning when using a 64bit platform. (found by embedthis) (#275)
   * Fix off-by-one error in parsing Supported Point Format extension that
     caused some handshakes to fail.

Changes
   * Made X509 profile pointer const in mbedtls_ssl_conf_cert_profile() to allow
     use of mbedtls_x509_crt_profile_next. (found by NWilson)
   * When a client initiates a reconnect from the same port as a live
     connection, if cookie verification is available
     (MBEDTLS_SSL_DTLS_HELLO_VERIFY defined in config.h, and usable cookie
     callbacks set with mbedtls_ssl_conf_dtls_cookies()), this will be
     detected and mbedtls_ssl_read() will return
     MBEDTLS_ERR_SSL_CLIENT_RECONNECT - it is then possible to start a new
     handshake with the same context. (See RFC 6347 section 4.2.8.)

= mbed TLS 2.1.0 released 2015-09-04

Features
   * Added support for yotta as a build system.
   * Primary open source license changed to Apache 2.0 license.

Bugfix
   * Fix segfault in the benchmark program when benchmarking DHM.
   * Fix build error with CMake and pre-4.5 versions of GCC (found by Hugo
     Leisink).
   * Fix bug when parsing a ServerHello without extensions (found by David
     Sears).
   * Fix bug in CMake lists that caused libmbedcrypto.a not to be installed
     (found by Benoit Lecocq).
   * Fix bug in Makefile that caused libmbedcrypto and libmbedx509 not to be
     installed (found by Rawi666).
   * Fix compile error with armcc 5 with --gnu option.
   * Fix bug in Makefile that caused programs not to be installed correctly
     (found by robotanarchy) (#232).
   * Fix bug in Makefile that prevented from installing without building the
     tests (found by robotanarchy) (#232).
   * Fix missing -static-libgcc when building shared libraries for Windows
     with make.
   * Fix link error when building shared libraries for Windows with make.
   * Fix error when loading libmbedtls.so.
   * Fix bug in mbedtls_ssl_conf_default() that caused the default preset to
     be always used (found by dcb314) (#235)
   * Fix bug in mbedtls_rsa_public() and mbedtls_rsa_private() that could
     result trying to unlock an unlocked mutex on invalid input (found by
     Fredrik Axelsson) (#257)
   * Fix -Wshadow warnings (found by hnrkp) (#240)
   * Fix memory corruption on client with overlong PSK identity, around
     SSL_MAX_CONTENT_LEN or higher - not triggerrable remotely (found by
     Aleksandrs Saveljevs) (#238)
   * Fix unused function warning when using MBEDTLS_MDx_ALT or
     MBEDTLS_SHAxxx_ALT (found by Henrik) (#239)
   * Fix memory corruption in pkey programs (found by yankuncheng) (#210)

Changes
   * The PEM parser now accepts a trailing space at end of lines (#226).
   * It is now possible to #include a user-provided configuration file at the
     end of the default config.h by defining MBEDTLS_USER_CONFIG_FILE on the
     compiler's command line.
   * When verifying a certificate chain, if an intermediate certificate is
     trusted, no later cert is checked. (suggested by hannes-landeholm)
     (#220).
   * Prepend a "thread identifier" to debug messages (issue pointed out by
     Hugo Leisink) (#210).
   * Add mbedtls_ssl_get_max_frag_len() to query the current maximum fragment
     length.

= mbed TLS 2.0.0 released 2015-07-13

Features
   * Support for DTLS 1.0 and 1.2 (RFC 6347).
   * Ability to override core functions from MDx, SHAx, AES and DES modules
     with custom implementation (eg hardware accelerated), complementing the
     ability to override the whole module.
   * New server-side implementation of session tickets that rotate keys to
     preserve forward secrecy, and allows sharing across multiple contexts.
   * Added a concept of X.509 cerificate verification profile that controls
     which algorithms and key sizes (curves for ECDSA) are acceptable.
   * Expanded configurability of security parameters in the SSL module with
     mbedtls_ssl_conf_dhm_min_bitlen() and mbedtls_ssl_conf_sig_hashes().
   * Introduced a concept of presets for SSL security-relevant configuration
     parameters.

API Changes
   * The library has been split into libmbedcrypto, libmbedx509, libmbedtls.
     You now need to link to all of them if you use TLS for example.
   * All public identifiers moved to the mbedtls_* or MBEDTLS_* namespace.
     Some names have been further changed to make them more consistent.
     Migration helpers scripts/rename.pl and include/mbedtls/compat-1.3.h are
     provided. Full list of renamings in scripts/data_files/rename-1.3-2.0.txt
   * Renamings of fields inside structures, not covered by the previous list:
     mbedtls_cipher_info_t.key_length -> key_bitlen
     mbedtls_cipher_context_t.key_length -> key_bitlen
     mbedtls_ecp_curve_info.size -> bit_size
   * Headers are now found in the 'mbedtls' directory (previously 'polarssl').
   * The following _init() functions that could return errors have
     been split into an _init() that returns void and another function that
     should generally be the first function called on this context after init:
     mbedtls_ssl_init() -> mbedtls_ssl_setup()
     mbedtls_ccm_init() -> mbedtls_ccm_setkey()
     mbedtls_gcm_init() -> mbedtls_gcm_setkey()
     mbedtls_hmac_drbg_init() -> mbedtls_hmac_drbg_seed(_buf)()
     mbedtls_ctr_drbg_init()  -> mbedtls_ctr_drbg_seed()
     Note that for mbedtls_ssl_setup(), you need to be done setting up the
     ssl_config structure before calling it.
   * Most ssl_set_xxx() functions (all except ssl_set_bio(), ssl_set_hostname(),
     ssl_set_session() and ssl_set_client_transport_id(), plus
     ssl_legacy_renegotiation()) have been renamed to mbedtls_ssl_conf_xxx()
     (see rename.pl and compat-1.3.h above) and their first argument's type
     changed from ssl_context to ssl_config.
   * ssl_set_bio() changed signature (contexts merged, order switched, one
     additional callback for read-with-timeout).
   * The following functions have been introduced and must be used in callback
     implementations (SNI, PSK) instead of their *conf counterparts:
     mbedtls_ssl_set_hs_own_cert()
     mbedtls_ssl_set_hs_ca_chain()
     mbedtls_ssl_set_hs_psk()
   * mbedtls_ssl_conf_ca_chain() lost its last argument (peer_cn), now set
     using mbedtls_ssl_set_hostname().
   * mbedtls_ssl_conf_session_cache() changed prototype (only one context
     pointer, parameters reordered).
   * On server, mbedtls_ssl_conf_session_tickets_cb() must now be used in
     place of mbedtls_ssl_conf_session_tickets() to enable session tickets.
   * The SSL debug callback gained two new arguments (file name, line number).
   * Debug modes were removed.
   * mbedtls_ssl_conf_truncated_hmac() now returns void.
   * mbedtls_memory_buffer_alloc_init() now returns void.
   * X.509 verification flags are now an uint32_t. Affect the signature of:
     mbedtls_ssl_get_verify_result()
     mbedtls_x509_ctr_verify_info()
     mbedtls_x509_crt_verify() (flags, f_vrfy -> needs to be updated)
     mbedtls_ssl_conf_verify() (f_vrfy -> needs to be updated)
   * The following functions changed prototype to avoid an in-out length
     parameter:
     mbedtls_base64_encode()
     mbedtls_base64_decode()
     mbedtls_mpi_write_string()
     mbedtls_dhm_calc_secret()
   * In the NET module, all "int" and "int *" arguments for file descriptors
     changed type to "mbedtls_net_context *".
   * net_accept() gained new arguments for the size of the client_ip buffer.
   * In the threading layer, mbedtls_mutex_init() and mbedtls_mutex_free() now
     return void.
   * ecdsa_write_signature() gained an additional md_alg argument and
     ecdsa_write_signature_det() was deprecated.
   * pk_sign() no longer accepts md_alg == POLARSSL_MD_NONE with ECDSA.
   * Last argument of x509_crt_check_key_usage() and
     mbedtls_x509write_crt_set_key_usage() changed from int to unsigned.
   * test_ca_list (from certs.h) is renamed to test_cas_pem and is only
     available if POLARSSL_PEM_PARSE_C is defined (it never worked without).
   * Test certificates in certs.c are no longer guaranteed to be nul-terminated
     strings; use the new *_len variables instead of strlen().
   * Functions mbedtls_x509_xxx_parse(), mbedtls_pk_parse_key(),
     mbedtls_pk_parse_public_key() and mbedtls_dhm_parse_dhm() now expect the
     length parameter to include the terminating null byte for PEM input.
   * Signature of mpi_mul_mpi() changed to make the last argument unsigned
   * calloc() is now used instead of malloc() everywhere. API of platform
     layer and the memory_buffer_alloc module changed accordingly.
     (Thanks to Mansour Moufid for helping with the replacement.)
   * Change SSL_DISABLE_RENEGOTIATION config.h flag to SSL_RENEGOTIATION
     (support for renegotiation now needs explicit enabling in config.h).
   * Split MBEDTLS_HAVE_TIME into MBEDTLS_HAVE_TIME and MBEDTLS_HAVE_TIME_DATE
     in config.h
   * net_connect() and net_bind() have a new 'proto' argument to choose
     between TCP and UDP, using the macros NET_PROTO_TCP or NET_PROTO_UDP.
     Their 'port' argument type is changed to a string.
   * Some constness fixes

Removals
   * Removed mbedtls_ecp_group_read_string(). Only named groups are supported.
   * Removed mbedtls_ecp_sub() and mbedtls_ecp_add(), use
     mbedtls_ecp_muladd().
   * Removed individual mdX_hmac, shaX_hmac, mdX_file and shaX_file functions
     (use generic functions from md.h)
   * Removed mbedtls_timing_msleep(). Use mbedtls_net_usleep() or a custom
     waiting function.
   * Removed test DHM parameters from the test certs module.
   * Removed the PBKDF2 module (use PKCS5).
   * Removed POLARSSL_ERROR_STRERROR_BC (use mbedtls_strerror()).
   * Removed compat-1.2.h (helper for migrating from 1.2 to 1.3).
   * Removed openssl.h (very partial OpenSSL compatibility layer).
   * Configuration options POLARSSL_HAVE_LONGLONG was removed (now always on).
   * Configuration options POLARSSL_HAVE_INT8 and POLARSSL_HAVE_INT16 have
     been removed (compiler is required to support 32-bit operations).
   * Configuration option POLARSSL_HAVE_IPV6 was removed (always enabled).
   * Removed test program o_p_test, the script compat.sh does more.
   * Removed test program ssl_test, superseded by ssl-opt.sh.
   * Removed helper script active-config.pl

New deprecations
   * md_init_ctx() is deprecated in favour of md_setup(), that adds a third
     argument (allowing memory savings if HMAC is not used)

Semi-API changes (technically public, morally private)
   * Renamed a few headers to include _internal in the name. Those headers are
     not supposed to be included by users.
   * Changed md_info_t into an opaque structure (use md_get_xxx() accessors).
   * Changed pk_info_t into an opaque structure.
   * Changed cipher_base_t into an opaque structure.
   * Removed sig_oid2 and rename sig_oid1 to sig_oid in x509_crt and x509_crl.
   * x509_crt.key_usage changed from unsigned char to unsigned int.
   * Removed r and s from ecdsa_context
   * Removed mode from des_context and des3_context

Default behavior changes
   * The default minimum TLS version is now TLS 1.0.
   * RC4 is now blacklisted by default in the SSL/TLS layer, and excluded from the
     default ciphersuite list returned by ssl_list_ciphersuites()
   * Support for receiving SSLv2 ClientHello is now disabled by default at
     compile time.
   * The default authmode for SSL/TLS clients is now REQUIRED.
   * Support for RSA_ALT contexts in the PK layer is now optional. Since is is
     enabled in the default configuration, this is only noticeable if using a
     custom config.h
   * Default DHM parameters server-side upgraded from 1024 to 2048 bits.
   * A minimum RSA key size of 2048 bits is now enforced during ceritificate
     chain verification.
   * Negotiation of truncated HMAC is now disabled by default on server too.
   * The following functions are now case-sensitive:
     mbedtls_cipher_info_from_string()
     mbedtls_ecp_curve_info_from_name()
     mbedtls_md_info_from_string()
     mbedtls_ssl_ciphersuite_from_string()
     mbedtls_version_check_feature()

Requirement changes
   * The minimum MSVC version required is now 2010 (better C99 support).
   * The NET layer now unconditionnaly relies on getaddrinfo() and select().
   * Compiler is required to support C99 types such as long long and uint32_t.

API changes from the 1.4 preview branch
   * ssl_set_bio_timeout() was removed, split into mbedtls_ssl_set_bio() with
     new prototype, and mbedtls_ssl_set_read_timeout().
   * The following functions now return void:
     mbedtls_ssl_conf_transport()
     mbedtls_ssl_conf_max_version()
     mbedtls_ssl_conf_min_version()
   * DTLS no longer hard-depends on TIMING_C, but uses a callback interface
     instead, see mbedtls_ssl_set_timer_cb(), with the Timing module providing
     an example implementation, see mbedtls_timing_delay_context and
     mbedtls_timing_set/get_delay().
   * With UDP sockets, it is no longer necessary to call net_bind() again
     after a successful net_accept().

Changes
   * mbedtls_ctr_drbg_random() and mbedtls_hmac_drbg_random() are now
     thread-safe if MBEDTLS_THREADING_C is enabled.
   * Reduced ROM fooprint of SHA-256 and added an option to reduce it even
     more (at the expense of performance) MBEDTLS_SHA256_SMALLER.

= mbed TLS 1.3 branch

Security
   * With authmode set to SSL_VERIFY_OPTIONAL, verification of keyUsage and
     extendedKeyUsage on the leaf certificate was lost (results not accessible
     via ssl_get_verify_results()).
   * Add countermeasure against "Lucky 13 strikes back" cache-based attack,
     https://dl.acm.org/citation.cfm?id=2714625

Features
   * Improve ECC performance by using more efficient doubling formulas
     (contributed by Peter Dettman).
   * Add x509_crt_verify_info() to display certificate verification results.
   * Add support for reading DH parameters with privateValueLength included
     (contributed by Daniel Kahn Gillmor).
   * Add support for bit strings in X.509 names (request by Fredrik Axelsson).
   * Add support for id-at-uniqueIdentifier in X.509 names.
   * Add support for overriding snprintf() (except on Windows) and exit() in
     the platform layer.
   * Add an option to use macros instead of function pointers in the platform
     layer (helps get rid of unwanted references).
   * Improved Makefiles for Windows targets by fixing library targets and making
     cross-compilation easier (thanks to Alon Bar-Lev).
   * The benchmark program also prints heap usage for public-key primitives
     if POLARSSL_MEMORY_BUFFER_ALLOC_C and POLARSSL_MEMORY_DEBUG are defined.
   * New script ecc-heap.sh helps measuring the impact of ECC parameters on
     speed and RAM (heap only for now) usage.
   * New script memory.sh helps measuring the ROM and RAM requirements of two
     reduced configurations (PSK-CCM and NSA suite B).
   * Add config flag POLARSSL_DEPRECATED_WARNING (off by default) to produce
     warnings on use of deprecated functions (with GCC and Clang only).
   * Add config flag POLARSSL_DEPRECATED_REMOVED (off by default) to produce
     errors on use of deprecated functions.

Bugfix
   * Fix compile errors with PLATFORM_NO_STD_FUNCTIONS.
   * Fix compile error with PLATFORM_EXIT_ALT (thanks to Rafał Przywara).
   * Fix bug in entropy.c when THREADING_C is also enabled that caused
     entropy_free() to crash (thanks to Rafał Przywara).
   * Fix memory leak when gcm_setkey() and ccm_setkey() are used more than
     once on the same context.
   * Fix bug in ssl_mail_client when password is longer that username (found
     by Bruno Pape).
   * Fix undefined behaviour (memcmp( NULL, NULL, 0 );) in X.509 modules
     (detected by Clang's 3.6 UBSan).
   * mpi_size() and mpi_msb() would segfault when called on an mpi that is
     initialized but not set (found by pravic).
   * Fix detection of support for getrandom() on Linux (reported by syzzer) by
     doing it at runtime (using uname) rather that compile time.
   * Fix handling of symlinks by "make install" (found by Gaël PORTAY).
   * Fix potential NULL pointer dereference (not trigerrable remotely) when
     ssl_write() is called before the handshake is finished (introduced in
     1.3.10) (first reported by Martin Blumenstingl).
   * Fix bug in pk_parse_key() that caused some valid private EC keys to be
     rejected.
   * Fix bug in Via Padlock support (found by Nikos Mavrogiannopoulos).
   * Fix thread safety bug in RSA operations (found by Fredrik Axelsson).
   * Fix hardclock() (only used in the benchmarking program) with some
     versions of mingw64 (found by kxjhlele).
   * Fix warnings from mingw64 in timing.c (found by kxjklele).
   * Fix potential unintended sign extension in asn1_get_len() on 64-bit
     platforms.
   * Fix potential memory leak in ssl_set_psk() (found by Mansour Moufid).
   * Fix compile error when POLARSSL_SSL_DISABLE_RENEGOTATION and
     POLARSSL_SSL_SSESSION_TICKETS where both enabled in config.h (introduced
     in 1.3.10).
   * Add missing extern "C" guard in aesni.h (reported by amir zamani).
   * Add missing dependency on SHA-256 in some x509 programs (reported by
     Gergely Budai).
   * Fix bug related to ssl_set_curves(): the client didn't check that the
     curve picked by the server was actually allowed.

Changes
   * Remove bias in mpi_gen_prime (contributed by Pascal Junod).
   * Remove potential sources of timing variations (some contributed by Pascal
     Junod).
   * Options POLARSSL_HAVE_INT8 and POLARSSL_HAVE_INT16 are deprecated.
   * Enabling POLARSSL_NET_C without POLARSSL_HAVE_IPV6 is deprecated.
   * compat-1.2.h and openssl.h are deprecated.
   * Adjusting/overriding CFLAGS and LDFLAGS with the make build system is now
     more flexible (warning: OFLAGS is not used any more) (see the README)
     (contributed by Alon Bar-Lev).
   * ssl_set_own_cert() no longer calls pk_check_pair() since the
     performance impact was bad for some users (this was introduced in 1.3.10).
   * Move from SHA-1 to SHA-256 in example programs using signatures
     (suggested by Thorsten Mühlfelder).
   * Remove some unneeded inclusions of header files from the standard library
     "minimize" others (eg use stddef.h if only size_t is needed).
   * Change #include lines in test files to use double quotes instead of angle
     brackets for uniformity with the rest of the code.
   * Remove dependency on sscanf() in X.509 parsing modules.

= mbed TLS 1.3.10 released 2015-02-09
Security
   * NULL pointer dereference in the buffer-based allocator when the buffer is
     full and polarssl_free() is called (found by Mark Hasemeyer)
     (only possible if POLARSSL_MEMORY_BUFFER_ALLOC_C is enabled, which it is
     not by default).
   * Fix remotely-triggerable uninitialised pointer dereference caused by
     crafted X.509 certificate (TLS server is not affected if it doesn't ask for a
     client certificate) (found using Codenomicon Defensics).
   * Fix remotely-triggerable memory leak caused by crafted X.509 certificates
     (TLS server is not affected if it doesn't ask for a client certificate)
     (found using Codenomicon Defensics).
   * Fix potential stack overflow while parsing crafted X.509 certificates
     (TLS server is not affected if it doesn't ask for a client certificate)
     (found using Codenomicon Defensics).
   * Fix timing difference that could theoretically lead to a
     Bleichenbacher-style attack in the RSA and RSA-PSK key exchanges
     (reported by Sebastian Schinzel).

Features
   * Add support for FALLBACK_SCSV (draft-ietf-tls-downgrade-scsv).
   * Add support for Extended Master Secret (draft-ietf-tls-session-hash).
   * Add support for Encrypt-then-MAC (RFC 7366).
   * Add function pk_check_pair() to test if public and private keys match.
   * Add x509_crl_parse_der().
   * Add compile-time option POLARSSL_X509_MAX_INTERMEDIATE_CA to limit the
     length of an X.509 verification chain.
   * Support for renegotiation can now be disabled at compile-time
   * Support for 1/n-1 record splitting, a countermeasure against BEAST.
   * Certificate selection based on signature hash, preferring SHA-1 over SHA-2
     for pre-1.2 clients when multiple certificates are available.
   * Add support for getrandom() syscall on recent Linux kernels with Glibc or
     a compatible enough libc (eg uClibc).
   * Add ssl_set_arc4_support() to make it easier to disable RC4 at runtime
     while using the default ciphersuite list.
   * Added new error codes and debug messages about selection of
     ciphersuite/certificate.

Bugfix
   * Stack buffer overflow if ctr_drbg_update() is called with too large
     add_len (found by Jean-Philippe Aumasson) (not triggerable remotely).
   * Possible buffer overflow of length at most POLARSSL_MEMORY_ALIGN_MULTIPLE
     if memory_buffer_alloc_init() was called with buf not aligned and len not
     a multiple of POLARSSL_MEMORY_ALIGN_MULTIPLE (not triggerable remotely).
   * User set CFLAGS were ignored by Cmake with gcc (introduced in 1.3.9, found
     by Julian Ospald).
   * Fix potential undefined behaviour in Camellia.
   * Fix potential failure in ECDSA signatures when POLARSSL_ECP_MAX_BITS is a
     multiple of 8 (found by Gergely Budai).
   * Fix unchecked return code in x509_crt_parse_path() on Windows (found by
     Peter Vaskovic).
   * Fix assembly selection for MIPS64 (thanks to James Cowgill).
   * ssl_get_verify_result() now works even if the handshake was aborted due
     to a failed verification (found by Fredrik Axelsson).
   * Skip writing and parsing signature_algorithm extension if none of the
     key exchanges enabled needs certificates. This fixes a possible interop
     issue with some servers when a zero-length extension was sent. (Reported
     by Peter Dettman.)
   * On a 0-length input, base64_encode() did not correctly set output length
     (found by Hendrik van den Boogaard).

Changes
   * Use deterministic nonces for AEAD ciphers in TLS by default (possible to
     switch back to random with POLARSSL_SSL_AEAD_RANDOM_IV in config.h).
   * Blind RSA private operations even when POLARSSL_RSA_NO_CRT is defined.
   * ssl_set_own_cert() now returns an error on key-certificate mismatch.
   * Forbid repeated extensions in X.509 certificates.
   * debug_print_buf() now prints a text view in addition to hexadecimal.
   * A specific error is now returned when there are ciphersuites in common
     but none of them is usable due to external factors such as no certificate
     with a suitable (extended)KeyUsage or curve or no PSK set.
   * It is now possible to disable negotiation of truncated HMAC server-side
     at runtime with ssl_set_truncated_hmac().
   * Example programs for SSL client and server now disable SSLv3 by default.
   * Example programs for SSL client and server now disable RC4 by default.
   * Use platform.h in all test suites and programs.

= PolarSSL 1.3.9 released 2014-10-20
Security
   * Lowest common hash was selected from signature_algorithms extension in
     TLS 1.2 (found by Darren Bane) (introduced in 1.3.8).
   * Remotely-triggerable memory leak when parsing some X.509 certificates
     (server is not affected if it doesn't ask for a client certificate)
     (found using Codenomicon Defensics).
   * Remotely-triggerable memory leak when parsing crafted ClientHello
     (not affected if ECC support was compiled out) (found using Codenomicon
     Defensics).

Bugfix
   * Support escaping of commas in x509_string_to_names()
   * Fix compile error in ssl_pthread_server (found by Julian Ospald).
   * Fix net_accept() regarding non-blocking sockets (found by Luca Pesce).
   * Don't print uninitialised buffer in ssl_mail_client (found by Marc Abel).
   * Fix warnings from Clang's scan-build (contributed by Alfred Klomp).
   * Fix compile error in timing.c when POLARSSL_NET_C and POLARSSL_SELFTEST
     are defined but not POLARSSL_HAVE_TIME (found by Stephane Di Vito).
   * Remove non-existent file from VS projects (found by Peter Vaskovic).
   * ssl_read() could return non-application data records on server while
     renegotation was pending, and on client when a HelloRequest was received.
   * Server-initiated renegotiation would fail with non-blocking I/O if the
     write callback returned WANT_WRITE when requesting renegotiation.
   * ssl_close_notify() could send more than one message in some circumstances
     with non-blocking I/O.
   * Fix compiler warnings on iOS (found by Sander Niemeijer).
   * x509_crt_parse() did not increase total_failed on PEM error
   * Fix compile error with armcc in mpi_is_prime()
   * Fix potential bad read in parsing ServerHello (found by Adrien
     Vialletelle).

Changes
   * Ciphersuites using SHA-256 or SHA-384 now require TLS 1.x (there is no
     standard defining how to use SHA-2 with SSL 3.0).
   * Ciphersuites using RSA-PSK key exchange new require TLS 1.x (the spec is
     ambiguous on how to encode some packets with SSL 3.0).
   * Made buffer size in pk_write_(pub)key_pem() more dynamic, eg smaller if
     RSA is disabled, larger if POLARSSL_MPI_MAX_SIZE is larger.
   * ssl_read() now returns POLARSSL_ERR_NET_WANT_READ rather than
     POLARSSL_ERR_SSL_UNEXPECTED_MESSAGE on harmless alerts.
   * POLARSSL_MPI_MAX_SIZE now defaults to 1024 in order to allow 8192 bits
     RSA keys.
   * Accept spaces at end of line or end of buffer in base64_decode().
   * X.509 certificates with more than one AttributeTypeAndValue per
     RelativeDistinguishedName are not accepted any more.

= PolarSSL 1.3.8 released 2014-07-11
Security
   * Fix length checking for AEAD ciphersuites (found by Codenomicon).
     It was possible to crash the server (and client) using crafted messages
     when a GCM suite was chosen.

Features
   * Add CCM module and cipher mode to Cipher Layer
   * Support for CCM and CCM_8 ciphersuites
   * Support for parsing and verifying RSASSA-PSS signatures in the X.509
     modules (certificates, CRLs and CSRs).
   * Blowfish in the cipher layer now supports variable length keys.
   * Add example config.h for PSK with CCM, optimized for low RAM usage.
   * Optimize for RAM usage in example config.h for NSA Suite B profile.
   * Add POLARSSL_REMOVE_ARC4_CIPHERSUITES to allow removing RC4 ciphersuites
     from the default list (inactive by default).
   * Add server-side enforcement of sent renegotiation requests
     (ssl_set_renegotiation_enforced())
   * Add SSL_CIPHERSUITES config.h flag to allow specifying a list of
     ciphersuites to use and save some memory if the list is small.

Changes
   * Add LINK_WITH_PTHREAD option in CMake for explicit linking that is
     required on some platforms (e.g. OpenBSD)
   * Migrate zeroizing of data to polarssl_zeroize() instead of memset()
     against unwanted compiler optimizations
   * md_list() now returns hashes strongest first
   * Selection of hash for signing ServerKeyExchange in TLS 1.2 now picks
     strongest offered by client.
   * All public contexts have _init() and _free() functions now for simpler
     usage pattern

Bugfix
   * Fix in debug_print_msg()
   * Enforce alignment in the buffer allocator even if buffer is not aligned
   * Remove less-than-zero checks on unsigned numbers
   * Stricter check on SSL ClientHello internal sizes compared to actual packet
     size (found by TrustInSoft)
   * Fix WSAStartup() return value check (found by Peter Vaskovic)
   * Other minor issues (found by Peter Vaskovic)
   * Fix symlink command for cross compiling with CMake (found by Andre
     Heinecke)
   * Fix DER output of gen_key app (found by Gergely Budai)
   * Very small records were incorrectly rejected when truncated HMAC was in
     use with some ciphersuites and versions (RC4 in all versions, CBC with
     versions < TLS 1.1).
   * Very large records using more than 224 bytes of padding were incorrectly
     rejected with CBC-based ciphersuites and TLS >= 1.1
   * Very large records using less padding could cause a buffer overread of up
     to 32 bytes with CBC-based ciphersuites and TLS >= 1.1
   * Restore ability to use a v1 cert as a CA if trusted locally. (This had
     been removed in 1.3.6.)
   * Restore ability to locally trust a self-signed cert that is not a proper
     CA for use as an end entity certificate. (This had been removed in
     1.3.6.)
   * Fix preprocessor checks for bn_mul PPC asm (found by Barry K. Nathan).
   * Use \n\t rather than semicolons for bn_mul asm, since some assemblers
     interpret semicolons as comment delimiters (found by Barry K. Nathan).
   * Fix off-by-one error in parsing Supported Point Format extension that
     caused some handshakes to fail.
   * Fix possible miscomputation of the premaster secret with DHE-PSK key
     exchange that caused some handshakes to fail with other implementations.
     (Failure rate <= 1/255 with common DHM moduli.)
   * Disable broken Sparc64 bn_mul assembly (found by Florian Obser).
   * Fix base64_decode() to return and check length correctly (in case of
     tight buffers)
   * Fix mpi_write_string() to write "00" as hex output for empty MPI (found
     by Hui Dong)

= PolarSSL 1.3.7 released on 2014-05-02
Features
   * debug_set_log_mode() added to determine raw or full logging
   * debug_set_threshold() added to ignore messages over threshold level
   * version_check_feature() added to check for compile-time options at
     run-time

Changes
   * POLARSSL_CONFIG_OPTIONS has been removed. All values are individually
     checked and filled in the relevant module headers
   * Debug module only outputs full lines instead of parts
   * Better support for the different Attribute Types from IETF PKIX (RFC 5280)
   * AES-NI now compiles with "old" assemblers too
   * Ciphersuites based on RC4 now have the lowest priority by default

Bugfix
   * Only iterate over actual certificates in ssl_write_certificate_request()
     (found by Matthew Page)
   * Typos in platform.c and pkcs11.c (found by Daniel Phillips and Steffan
     Karger)
   * cert_write app should use subject of issuer certificate as issuer of cert
   * Fix false reject in padding check in ssl_decrypt_buf() for CBC
     ciphersuites, for full SSL frames of data.
   * Improve interoperability by not writing extension length in ClientHello /
     ServerHello when no extensions are present (found by Matthew Page)
   * rsa_check_pubkey() now allows an E up to N
   * On OpenBSD, use arc4random_buf() instead of rand() to prevent warnings
   * mpi_fill_random() was creating numbers larger than requested on
     big-endian platform when size was not an integer number of limbs
   * Fix dependencies issues in X.509 test suite.
   * Some parts of ssl_tls.c were compiled even when the module was disabled.
   * Fix detection of DragonflyBSD in net.c (found by Markus Pfeiffer)
   * Fix detection of Clang on some Apple platforms with CMake
     (found by Barry K. Nathan)

= PolarSSL 1.3.6 released on 2014-04-11

Features
   * Support for the ALPN SSL extension
   * Add option 'use_dev_random' to gen_key application
   * Enable verification of the keyUsage extension for CA and leaf
     certificates (POLARSSL_X509_CHECK_KEY_USAGE)
   * Enable verification of the extendedKeyUsage extension
     (POLARSSL_X509_CHECK_EXTENDED_KEY_USAGE)

Changes
   * x509_crt_info() now prints information about parsed extensions as well
   * pk_verify() now returns a specific error code when the signature is valid
     but shorter than the supplied length.
   * Use UTC time to check certificate validity.
   * Reject certificates with times not in UTC, per RFC 5280.

Security
   * Avoid potential timing leak in ecdsa_sign() by blinding modular division.
     (Found by Watson Ladd.)
   * The notAfter date of some certificates was no longer checked since 1.3.5.
     This affects certificates in the user-supplied chain except the top
     certificate. If the user-supplied chain contains only one certificates,
     it is not affected (ie, its notAfter date is properly checked).
   * Prevent potential NULL pointer dereference in ssl_read_record() (found by
     TrustInSoft)

Bugfix
   * The length of various ClientKeyExchange messages was not properly checked.
   * Some example server programs were not sending the close_notify alert.
   * Potential memory leak in mpi_exp_mod() when error occurs during
     calculation of RR.
   * Fixed malloc/free default #define in platform.c (found by Gergely Budai).
   * Fixed type which made POLARSSL_ENTROPY_FORCE_SHA256 uneffective (found by
     Gergely Budai).
   * Fix #include path in ecdsa.h which wasn't accepted by some compilers.
     (found by Gergely Budai)
   * Fix compile errors when POLARSSL_ERROR_STRERROR_BC is undefined (found by
     Shuo Chen).
   * oid_get_numeric_string() used to truncate the output without returning an
     error if the output buffer was just 1 byte too small.
   * dhm_parse_dhm() (hence dhm_parse_dhmfile()) did not set dhm->len.
   * Calling pk_debug() on an RSA-alt key would segfault.
   * pk_get_size() and pk_get_len() were off by a factor 8 for RSA-alt keys.
   * Potential buffer overwrite in pem_write_buffer() because of low length
     indication (found by Thijs Alkemade)
   * EC curves constants, which should be only in ROM since 1.3.3, were also
     stored in RAM due to missing 'const's (found by Gergely Budai).

= PolarSSL 1.3.5 released on 2014-03-26
Features
   * HMAC-DRBG as a separate module
   * Option to set the Curve preference order (disabled by default)
   * Single Platform compatilibity layer (for memory / printf / fprintf)
   * Ability to provide alternate timing implementation
   * Ability to force the entropy module to use SHA-256 as its basis
     (POLARSSL_ENTROPY_FORCE_SHA256)
   * Testing script ssl-opt.sh added for testing 'live' ssl option
     interoperability against OpenSSL and PolarSSL
   * Support for reading EC keys that use SpecifiedECDomain in some cases.
   * Entropy module now supports seed writing and reading

Changes
   * Deprecated the Memory layer
   * entropy_add_source(), entropy_update_manual() and entropy_gather()
     now thread-safe if POLARSSL_THREADING_C defined
   * Improvements to the CMake build system, contributed by Julian Ospald.
   * Work around a bug of the version of Clang shipped by Apple with Mavericks
     that prevented bignum.c from compiling. (Reported by Rafael Baptista.)
   * Revamped the compat.sh interoperatibility script to include support for
     testing against GnuTLS
   * Deprecated ssl_set_own_cert_rsa() and ssl_set_own_cert_rsa_alt()
   * Improvements to tests/Makefile, contributed by Oden Eriksson.

Security
   * Forbid change of server certificate during renegotiation to prevent
     "triple handshake" attack when authentication mode is 'optional' (the
     attack was already impossible when authentication is required).
   * Check notBefore timestamp of certificates and CRLs from the future.
   * Forbid sequence number wrapping
   * Fixed possible buffer overflow with overlong PSK
   * Possible remotely-triggered out-of-bounds memory access fixed (found by
     TrustInSoft)

Bugfix
   * ecp_gen_keypair() does more tries to prevent failure because of
     statistics
   * Fixed bug in RSA PKCS#1 v1.5 "reversed" operations
   * Fixed testing with out-of-source builds using cmake
   * Fixed version-major intolerance in server
   * Fixed CMake symlinking on out-of-source builds
   * Fixed dependency issues in test suite
   * Programs rsa_sign_pss and rsa_verify_pss were not using PSS since 1.3.0
   * Bignum's MIPS-32 assembly was used on MIPS-64, causing chaos. (Found by
     Alex Wilson.)
   * ssl_cache was creating entries when max_entries=0 if TIMING_C was enabled.
   * m_sleep() was sleeping twice too long on most Unix platforms.
   * Fixed bug with session tickets and non-blocking I/O in the unlikely case
     send() would return an EAGAIN error when sending the ticket.
   * ssl_cache was leaking memory when reusing a timed out entry containing a
     client certificate.
   * ssl_srv was leaking memory when client presented a timed out ticket
     containing a client certificate
   * ssl_init() was leaving a dirty pointer in ssl_context if malloc of
     out_ctr failed
   * ssl_handshake_init() was leaving dirty pointers in subcontexts if malloc
     of one of them failed
   * Fix typo in rsa_copy() that impacted PKCS#1 v2 contexts
   * x509_get_current_time() uses localtime_r() to prevent thread issues

= PolarSSL 1.3.4 released on 2014-01-27
Features
   * Support for the Koblitz curves: secp192k1, secp224k1, secp256k1
   * Support for RIPEMD-160
   * Support for AES CFB8 mode
   * Support for deterministic ECDSA (RFC 6979)

Bugfix
   * Potential memory leak in bignum_selftest()
   * Replaced expired test certificate
   * ssl_mail_client now terminates lines with CRLF, instead of LF
   * net module handles timeouts on blocking sockets better (found by Tilman
     Sauerbeck)
   * Assembly format fixes in bn_mul.h

Security
   * Missing MPI_CHK calls added around unguarded mpi calls (found by
     TrustInSoft)

= PolarSSL 1.3.3 released on 2013-12-31
Features
   * EC key generation support in gen_key app
   * Support for adhering to client ciphersuite order preference
     (POLARSSL_SSL_SRV_RESPECT_CLIENT_PREFERENCE)
   * Support for Curve25519
   * Support for ECDH-RSA and ECDH-ECDSA key exchanges and ciphersuites
   * Support for IPv6 in the NET module
   * AES-NI support for AES, AES-GCM and AES key scheduling
   * SSL Pthread-based server example added (ssl_pthread_server)

Changes
   * gen_prime() speedup
   * Speedup of ECP multiplication operation
   * Relaxed some SHA2 ciphersuite's version requirements
   * Dropped use of readdir_r() instead of readdir() with threading support
   * More constant-time checks in the RSA module
   * Split off curves from ecp.c into ecp_curves.c
   * Curves are now stored fully in ROM
   * Memory usage optimizations in ECP module
   * Removed POLARSSL_THREADING_DUMMY

Bugfix
   * Fixed bug in mpi_set_bit() on platforms where t_uint is wider than int
   * Fixed X.509 hostname comparison (with non-regular characters)
   * SSL now gracefully handles missing RNG
   * Missing defines / cases for RSA_PSK key exchange
   * crypt_and_hash app checks MAC before final decryption
   * Potential memory leak in ssl_ticket_keys_init()
   * Memory leak in benchmark application
   * Fixed x509_crt_parse_path() bug on Windows platforms
   * Added missing MPI_CHK() around some statements in mpi_div_mpi() (found by
     TrustInSoft)
   * Fixed potential overflow in certificate size verification in
     ssl_write_certificate() (found by TrustInSoft)

Security
   * Possible remotely-triggered out-of-bounds memory access fixed (found by
     TrustInSoft)

= PolarSSL 1.3.2 released on 2013-11-04
Features
   * PK tests added to test framework
   * Added optional optimization for NIST MODP curves (POLARSSL_ECP_NIST_OPTIM)
   * Support for Camellia-GCM mode and ciphersuites

Changes
   * Padding checks in cipher layer are now constant-time
   * Value comparisons in SSL layer are now constant-time
   * Support for serialNumber, postalAddress and postalCode in X509 names
   * SSL Renegotiation was refactored

Bugfix
   * More stringent checks in cipher layer
   * Server does not send out extensions not advertised by client
   * Prevent possible alignment warnings on casting from char * to 'aligned *'
   * Misc fixes and additions to dependency checks
   * Const correctness
   * cert_write with selfsign should use issuer_name as subject_name
   * Fix ECDSA corner case: missing reduction mod N (found by DualTachyon)
   * Defines to handle UEFI environment under MSVC
   * Server-side initiated renegotiations send HelloRequest

= PolarSSL 1.3.1 released on 2013-10-15
Features
   * Support for Brainpool curves and TLS ciphersuites (RFC 7027)
   * Support for ECDHE-PSK key-exchange and ciphersuites
   * Support for RSA-PSK key-exchange and ciphersuites

Changes
   * RSA blinding locks for a smaller amount of time
   * TLS compression only allocates working buffer once
   * Introduced POLARSSL_HAVE_READDIR_R for systems without it
   * config.h is more script-friendly

Bugfix
   * Missing MSVC defines added
   * Compile errors with POLARSSL_RSA_NO_CRT
   * Header files with 'polarssl/'
   * Const correctness
   * Possible naming collision in dhm_context
   * Better support for MSVC
   * threading_set_alt() name
   * Added missing x509write_crt_set_version()

= PolarSSL 1.3.0 released on 2013-10-01
Features
   * Elliptic Curve Cryptography module added
   * Elliptic Curve Diffie Hellman module added
   * Ephemeral Elliptic Curve Diffie Hellman support for SSL/TLS
    (ECDHE-based ciphersuites)
   * Ephemeral Elliptic Curve Digital Signature Algorithm support for SSL/TLS
    (ECDSA-based ciphersuites)
   * Ability to specify allowed ciphersuites based on the protocol version.
   * PSK and DHE-PSK based ciphersuites added
   * Memory allocation abstraction layer added
   * Buffer-based memory allocator added (no malloc() / free() / HEAP usage)
   * Threading abstraction layer added (dummy / pthread / alternate)
   * Public Key abstraction layer added
   * Parsing Elliptic Curve keys
   * Parsing Elliptic Curve certificates
   * Support for max_fragment_length extension (RFC 6066)
   * Support for truncated_hmac extension (RFC 6066)
   * Support for zeros-and-length (ANSI X.923) padding, one-and-zeros
     (ISO/IEC 7816-4) padding and zero padding in the cipher layer
   * Support for session tickets (RFC 5077)
   * Certificate Request (CSR) generation with extensions (key_usage,
     ns_cert_type)
   * X509 Certificate writing with extensions (basic_constraints,
     issuer_key_identifier, etc)
   * Optional blinding for RSA, DHM and EC
   * Support for multiple active certificate / key pairs in SSL servers for
     the same host (Not to be confused with SNI!)

Changes
   * Ability to enable / disable SSL v3 / TLS 1.0 / TLS 1.1 / TLS 1.2
     individually
   * Introduced separate SSL Ciphersuites module that is based on
     Cipher and MD information
   * Internals for SSL module adapted to have separate IV pointer that is
     dynamically set (Better support for hardware acceleration)
   * Moved all OID functionality to a separate module. RSA function
     prototypes for the RSA sign and verify functions changed as a result
   * Split up the GCM module into a starts/update/finish cycle
   * Client and server now filter sent and accepted ciphersuites on minimum
     and maximum protocol version
   * Ability to disable server_name extension (RFC 6066)
   * Renamed error_strerror() to the less conflicting polarssl_strerror()
     (Ability to keep old as well with POLARSSL_ERROR_STRERROR_BC)
   * SHA2 renamed to SHA256, SHA4 renamed to SHA512 and functions accordingly
   * All RSA operations require a random generator for blinding purposes
   * X509 core refactored
   * x509_crt_verify() now case insensitive for cn (RFC 6125 6.4)
   * Also compiles / runs without time-based functions (!POLARSSL_HAVE_TIME)
   * Support faulty X509 v1 certificates with extensions
     (POLARSSL_X509_ALLOW_EXTENSIONS_NON_V3)

Bugfix
   * Fixed parse error in ssl_parse_certificate_request()
   * zlib compression/decompression skipped on empty blocks
   * Support for AIX header locations in net.c module
   * Fixed file descriptor leaks

Security
   * RSA blinding on CRT operations to counter timing attacks
     (found by Cyril Arnaud and Pierre-Alain Fouque)


= Version 1.2.14 released 2015-05-??

Security
   * Fix potential invalid memory read in the server, that allows a client to
     crash it remotely (found by Caj Larsson).
   * Fix potential invalid memory read in certificate parsing, that allows a
     client to crash the server remotely if client authentication is enabled
     (found using Codenomicon Defensics).
   * Add countermeasure against "Lucky 13 strikes back" cache-based attack,
     https://dl.acm.org/citation.cfm?id=2714625

Bugfix
   * Fix bug in Via Padlock support (found by Nikos Mavrogiannopoulos).
   * Fix hardclock() (only used in the benchmarking program) with some
     versions of mingw64 (found by kxjhlele).
   * Fix warnings from mingw64 in timing.c (found by kxjklele).
   * Fix potential unintended sign extension in asn1_get_len() on 64-bit
     platforms (found with Coverity Scan).

= Version 1.2.13 released 2015-02-16
Note: Although PolarSSL has been renamed to mbed TLS, no changes reflecting
      this will be made in the 1.2 branch at this point.

Security
   * Fix remotely-triggerable uninitialised pointer dereference caused by
     crafted X.509 certificate (TLS server is not affected if it doesn't ask
     for a client certificate) (found using Codenomicon Defensics).
   * Fix remotely-triggerable memory leak caused by crafted X.509 certificates
     (TLS server is not affected if it doesn't ask for a client certificate)
     (found using Codenomicon Defensics).
   * Fix potential stack overflow while parsing crafted X.509 certificates
     (TLS server is not affected if it doesn't ask for a client certificate)
     found using Codenomicon Defensics).
   * Fix buffer overread of size 1 when parsing crafted X.509 certificates
     (TLS server is not affected if it doesn't ask for a client certificate).

Bugfix
   * Fix potential undefined behaviour in Camellia.
   * Fix memory leaks in PKCS#5 and PKCS#12.
   * Stack buffer overflow if ctr_drbg_update() is called with too large
     add_len (found by Jean-Philippe Aumasson) (not triggerable remotely).
   * Fix bug in MPI/bignum on s390/s390x (reported by Dan Horák) (introduced
     in 1.2.12).
   * Fix unchecked return code in x509_crt_parse_path() on Windows (found by
     Peter Vaskovic).
   * Fix assembly selection for MIPS64 (thanks to James Cowgill).
   * ssl_get_verify_result() now works even if the handshake was aborted due
     to a failed verification (found by Fredrik Axelsson).
   * Skip writing and parsing signature_algorithm extension if none of the
     key exchanges enabled needs certificates. This fixes a possible interop
     issue with some servers when a zero-length extension was sent. (Reported
     by Peter Dettman.)
   * On a 0-length input, base64_encode() did not correctly set output length
     (found by Hendrik van den Boogaard).

Changes
   * Blind RSA private operations even when POLARSSL_RSA_NO_CRT is defined.
   * Forbid repeated extensions in X.509 certificates.
   * Add compile-time option POLARSSL_X509_MAX_INTERMEDIATE_CA to limit the
     length of an X.509 verification chain (default = 8).
= Version 1.2.12 released 2014-10-24

Security
   * Remotely-triggerable memory leak when parsing some X.509 certificates
     (server is not affected if it doesn't ask for a client certificate).
     (Found using Codenomicon Defensics.)

Bugfix
   * Fix potential bad read in parsing ServerHello (found by Adrien
     Vialletelle).
   * ssl_close_notify() could send more than one message in some circumstances
     with non-blocking I/O.
   * x509_crt_parse() did not increase total_failed on PEM error
   * Fix compiler warnings on iOS (found by Sander Niemeijer).
   * Don't print uninitialised buffer in ssl_mail_client (found by Marc Abel).
   * Fix net_accept() regarding non-blocking sockets (found by Luca Pesce).
   * ssl_read() could return non-application data records on server while
     renegotation was pending, and on client when a HelloRequest was received.
   * Fix warnings from Clang's scan-build (contributed by Alfred Klomp).

Changes
   * X.509 certificates with more than one AttributeTypeAndValue per
     RelativeDistinguishedName are not accepted any more.
   * ssl_read() now returns POLARSSL_ERR_NET_WANT_READ rather than
     POLARSSL_ERR_SSL_UNEXPECTED_MESSAGE on harmless alerts.
   * Accept spaces at end of line or end of buffer in base64_decode().

= Version 1.2.11 released 2014-07-11
Features
   * Entropy module now supports seed writing and reading

Changes
   * Introduced POLARSSL_HAVE_READDIR_R for systems without it
   * Improvements to the CMake build system, contributed by Julian Ospald.
   * Work around a bug of the version of Clang shipped by Apple with Mavericks
     that prevented bignum.c from compiling. (Reported by Rafael Baptista.)
   * Improvements to tests/Makefile, contributed by Oden Eriksson.
   * Use UTC time to check certificate validity.
   * Reject certificates with times not in UTC, per RFC 5280.
   * Migrate zeroizing of data to polarssl_zeroize() instead of memset()
     against unwanted compiler optimizations

Security
   * Forbid change of server certificate during renegotiation to prevent
     "triple handshake" attack when authentication mode is optional (the
     attack was already impossible when authentication is required).
   * Check notBefore timestamp of certificates and CRLs from the future.
   * Forbid sequence number wrapping
   * Prevent potential NULL pointer dereference in ssl_read_record() (found by
     TrustInSoft)
   * Fix length checking for AEAD ciphersuites (found by Codenomicon).
     It was possible to crash the server (and client) using crafted messages
     when a GCM suite was chosen.

Bugfix
   * Fixed X.509 hostname comparison (with non-regular characters)
   * SSL now gracefully handles missing RNG
   * crypt_and_hash app checks MAC before final decryption
   * Fixed x509_crt_parse_path() bug on Windows platforms
   * Added missing MPI_CHK() around some statements in mpi_div_mpi() (found by
     TrustInSoft)
   * Fixed potential overflow in certificate size verification in
     ssl_write_certificate() (found by TrustInSoft)
   * Fix ASM format in bn_mul.h
   * Potential memory leak in bignum_selftest()
   * Replaced expired test certificate
   * ssl_mail_client now terminates lines with CRLF, instead of LF
   * Fix bug in RSA PKCS#1 v1.5 "reversed" operations
   * Fixed testing with out-of-source builds using cmake
   * Fixed version-major intolerance in server
   * Fixed CMake symlinking on out-of-source builds
   * Bignum's MIPS-32 assembly was used on MIPS-64, causing chaos. (Found by
     Alex Wilson.)
   * ssl_init() was leaving a dirty pointer in ssl_context if malloc of
     out_ctr failed
   * ssl_handshake_init() was leaving dirty pointers in subcontexts if malloc
     of one of them failed
   * x509_get_current_time() uses localtime_r() to prevent thread issues
   * Some example server programs were not sending the close_notify alert.
   * Potential memory leak in mpi_exp_mod() when error occurs during
     calculation of RR.
   * Improve interoperability by not writing extension length in ClientHello
     when no extensions are present (found by Matthew Page)
   * rsa_check_pubkey() now allows an E up to N
   * On OpenBSD, use arc4random_buf() instead of rand() to prevent warnings
   * mpi_fill_random() was creating numbers larger than requested on
     big-endian platform when size was not an integer number of limbs
   * Fix detection of DragonflyBSD in net.c (found by Markus Pfeiffer)
   * Stricter check on SSL ClientHello internal sizes compared to actual packet
     size (found by TrustInSoft)
   * Fix preprocessor checks for bn_mul PPC asm (found by Barry K. Nathan).
   * Use \n\t rather than semicolons for bn_mul asm, since some assemblers
     interpret semicolons as comment delimiters (found by Barry K. Nathan).
   * Disable broken Sparc64 bn_mul assembly (found by Florian Obser).
   * Fix base64_decode() to return and check length correctly (in case of
     tight buffers)

= Version 1.2.10 released 2013-10-07
Changes
   * Changed RSA blinding to a slower but thread-safe version

Bugfix
   * Fixed memory leak in RSA as a result of introduction of blinding
   * Fixed ssl_pkcs11_decrypt() prototype
   * Fixed MSVC project files

= Version 1.2.9 released 2013-10-01
Changes
   * x509_verify() now case insensitive for cn (RFC 6125 6.4)

Bugfix
   * Fixed potential memory leak when failing to resume a session
   * Fixed potential file descriptor leaks (found by Remi Gacogne)
   * Minor fixes

Security
   * Fixed potential heap buffer overflow on large hostname setting
   * Fixed potential negative value misinterpretation in load_file()
   * RSA blinding on CRT operations to counter timing attacks
     (found by Cyril Arnaud and Pierre-Alain Fouque)

= Version 1.2.8 released 2013-06-19
Features
   * Parsing of PKCS#8 encrypted private key files
   * PKCS#12 PBE and derivation functions
   * Centralized module option values in config.h to allow user-defined
     settings without editing header files by using POLARSSL_CONFIG_OPTIONS

Changes
   * HAVEGE random generator disabled by default
   * Internally split up x509parse_key() into a (PEM) handler function
     and specific DER parser functions for the PKCS#1 and unencrypted
     PKCS#8 private key formats
   * Added mechanism to provide alternative implementations for all
     symmetric cipher and hash algorithms (e.g. POLARSSL_AES_ALT in
     config.h)
   * PKCS#5 module added. Moved PBKDF2 functionality inside and deprecated
     old PBKDF2 module

Bugfix
   * Secure renegotiation extension should only be sent in case client
     supports secure renegotiation
   * Fixed offset for cert_type list in ssl_parse_certificate_request()
   * Fixed const correctness issues that have no impact on the ABI
   * x509parse_crt() now better handles PEM error situations
   * ssl_parse_certificate() now calls x509parse_crt_der() directly
     instead of the x509parse_crt() wrapper that can also parse PEM
     certificates
   * x509parse_crtpath() is now reentrant and uses more portable stat()
   * Fixed bignum.c and bn_mul.h to support Thumb2 and LLVM compiler
   * Fixed values for 2-key Triple DES in cipher layer
   * ssl_write_certificate_request() can handle empty ca_chain

Security
   * A possible DoS during the SSL Handshake, due to faulty parsing of
     PEM-encoded certificates has been fixed (found by Jack Lloyd)

= Version 1.2.7 released 2013-04-13
Features
   * Ability to specify allowed ciphersuites based on the protocol version.

Changes
   * Default Blowfish keysize is now 128-bits
   * Test suites made smaller to accommodate Raspberry Pi

Bugfix
   * Fix for MPI assembly for ARM
   * GCM adapted to support sizes > 2^29

= Version 1.2.6 released 2013-03-11
Bugfix
   * Fixed memory leak in ssl_free() and ssl_reset() for active session
   * Corrected GCM counter incrementation to use only 32-bits instead of
     128-bits (found by Yawning Angel)
   * Fixes for 64-bit compilation with MS Visual Studio
   * Fixed net_bind() for specified IP addresses on little endian systems
   * Fixed assembly code for ARM (Thumb and regular) for some compilers

Changes
   * Internally split up rsa_pkcs1_encrypt(), rsa_pkcs1_decrypt(),
     rsa_pkcs1_sign() and rsa_pkcs1_verify() to separate PKCS#1 v1.5 and
     PKCS#1 v2.1 functions
   * Added support for custom labels when using rsa_rsaes_oaep_encrypt()
     or rsa_rsaes_oaep_decrypt()
   * Re-added handling for SSLv2 Client Hello when the define
     POLARSSL_SSL_SRV_SUPPORT_SSLV2_CLIENT_HELLO is set
   * The SSL session cache module (ssl_cache) now also retains peer_cert
     information (not the entire chain)

Security
   * Removed further timing differences during SSL message decryption in
     ssl_decrypt_buf()
   * Removed timing differences due to bad padding from
     rsa_rsaes_pkcs1_v15_decrypt() and rsa_pkcs1_decrypt() for PKCS#1 v1.5
     operations

= Version 1.2.5 released 2013-02-02
Changes
   * Allow enabling of dummy error_strerror() to support some use-cases
   * Debug messages about padding errors during SSL message decryption are
     disabled by default and can be enabled with POLARSSL_SSL_DEBUG_ALL
   * Sending of security-relevant alert messages that do not break
     interoperability can be switched on/off with the flag
     POLARSSL_SSL_ALL_ALERT_MESSAGES

Security
   * Removed timing differences during SSL message decryption in
     ssl_decrypt_buf() due to badly formatted padding

= Version 1.2.4 released 2013-01-25
Changes
   * More advanced SSL ciphersuite representation and moved to more dynamic
     SSL core
   * Added ssl_handshake_step() to allow single stepping the handshake process

Bugfix
   * Memory leak when using RSA_PKCS_V21 operations fixed
   * Handle future version properly in ssl_write_certificate_request()
   * Correctly handle CertificateRequest message in client for <= TLS 1.1
     without DN list

= Version 1.2.3 released 2012-11-26
Bugfix
   * Server not always sending correct CertificateRequest message

= Version 1.2.2 released 2012-11-24
Changes
   * Added p_hw_data to ssl_context for context specific hardware acceleration
     data
   * During verify trust-CA is only checked for expiration and CRL presence

Bugfixes
   * Fixed client authentication compatibility
   * Fixed dependency on POLARSSL_SHA4_C in SSL modules

= Version 1.2.1 released 2012-11-20
Changes
   * Depth that the certificate verify callback receives is now numbered
     bottom-up (Peer cert depth is 0)

Bugfixes
   * Fixes for MSVC6
   * Moved mpi_inv_mod() outside POLARSSL_GENPRIME
   * Allow R and A to point to same mpi in mpi_div_mpi (found by Manuel
     Pégourié-Gonnard)
   * Fixed possible segfault in mpi_shift_r() (found by Manuel
     Pégourié-Gonnard)
   * Added max length check for rsa_pkcs1_sign with PKCS#1 v2.1

= Version 1.2.0 released 2012-10-31
Features
   * Added support for NULL cipher (POLARSSL_CIPHER_NULL_CIPHER) and weak
     ciphersuites (POLARSSL_ENABLE_WEAK_CIPHERSUITES). They are disabled by
     default!
   * Added support for wildcard certificates
   * Added support for multi-domain certificates through the X509 Subject
     Alternative Name extension
   * Added preliminary ASN.1 buffer writing support
   * Added preliminary X509 Certificate Request writing support
   * Added key_app_writer example application
   * Added cert_req example application
   * Added base Galois Counter Mode (GCM) for AES
   * Added TLS 1.2 support (RFC 5246)
   * Added GCM suites to TLS 1.2 (RFC 5288)
   * Added commandline error code convertor (util/strerror)
   * Added support for Hardware Acceleration hooking in SSL/TLS
   * Added OpenSSL / PolarSSL compatibility script (tests/compat.sh) and
     example application (programs/ssl/o_p_test) (requires OpenSSL)
   * Added X509 CA Path support
   * Added Thumb assembly optimizations
   * Added DEFLATE compression support as per RFC3749 (requires zlib)
   * Added blowfish algorithm (Generic and cipher layer)
   * Added PKCS#5 PBKDF2 key derivation function
   * Added Secure Renegotiation (RFC 5746)
   * Added predefined DHM groups from RFC 5114
   * Added simple SSL session cache implementation
   * Added ServerName extension parsing (SNI) at server side
   * Added option to add minimum accepted SSL/TLS protocol version

Changes
   * Removed redundant POLARSSL_DEBUG_MSG define
   * AES code only check for Padlock once
   * Fixed const-correctness mpi_get_bit()
   * Documentation for mpi_lsb() and mpi_msb()
   * Moved out_msg to out_hdr + 32 to support hardware acceleration
   * Changed certificate verify behaviour to comply with RFC 6125 section 6.3
     to not match CN if subjectAltName extension is present (Closes ticket #56)
   * Cipher layer cipher_mode_t POLARSSL_MODE_CFB128 is renamed to
     POLARSSL_MODE_CFB, to also handle different block size CFB modes.
   * Removed handling for SSLv2 Client Hello (as per RFC 5246 recommendation)
   * Revamped session resumption handling
   * Generalized external private key implementation handling (like PKCS#11)
     in SSL/TLS
   * Revamped x509_verify() and the SSL f_vrfy callback implementations
   * Moved from unsigned long to fixed width uint32_t types throughout code
   * Renamed ciphersuites naming scheme to IANA reserved names

Bugfix
   * Fixed handling error in mpi_cmp_mpi() on longer B values (found by
     Hui Dong)
   * Fixed potential heap corruption in x509_name allocation
   * Fixed single RSA test that failed on Big Endian systems (Closes ticket #54)
   * mpi_exp_mod() now correctly handles negative base numbers (Closes ticket
     #52)
   * Handle encryption with private key and decryption with public key as per
     RFC 2313
   * Handle empty certificate subject names
   * Prevent reading over buffer boundaries on X509 certificate parsing
   * mpi_add_abs() now correctly handles adding short numbers to long numbers
     with carry rollover (found by Ruslan Yushchenko)
   * Handle existence of OpenSSL Trust Extensions at end of X.509 DER blob
   * Fixed MPI assembly for SPARC64 platform

Security
   * Fixed potential memory zeroization on miscrafted RSA key (found by Eloi
     Vanderbeken)

= Version 1.1.8 released on 2013-10-01
Bugfix
   * Fixed potential memory leak when failing to resume a session
   * Fixed potential file descriptor leaks

Security
   * Potential buffer-overflow for ssl_read_record() (independently found by
     both TrustInSoft and Paul Brodeur of Leviathan Security Group)
   * Potential negative value misinterpretation in load_file()
   * Potential heap buffer overflow on large hostname setting

= Version 1.1.7 released on 2013-06-19
Changes
   * HAVEGE random generator disabled by default

Bugfix
   * x509parse_crt() now better handles PEM error situations
   * ssl_parse_certificate() now calls x509parse_crt_der() directly
     instead of the x509parse_crt() wrapper that can also parse PEM
     certificates
   * Fixed values for 2-key Triple DES in cipher layer
   * ssl_write_certificate_request() can handle empty ca_chain

Security
   * A possible DoS during the SSL Handshake, due to faulty parsing of
     PEM-encoded certificates has been fixed (found by Jack Lloyd)

= Version 1.1.6 released on 2013-03-11
Bugfix
   * Fixed net_bind() for specified IP addresses on little endian systems

Changes
   * Allow enabling of dummy error_strerror() to support some use-cases
   * Debug messages about padding errors during SSL message decryption are
     disabled by default and can be enabled with POLARSSL_SSL_DEBUG_ALL

Security
   * Removed timing differences during SSL message decryption in
     ssl_decrypt_buf()
   * Removed timing differences due to bad padding from
     rsa_rsaes_pkcs1_v15_decrypt() and rsa_pkcs1_decrypt() for PKCS#1 v1.5
     operations

= Version 1.1.5 released on 2013-01-16
Bugfix
   * Fixed MPI assembly for SPARC64 platform
   * Handle existence of OpenSSL Trust Extensions at end of X.509 DER blob
   * mpi_add_abs() now correctly handles adding short numbers to long numbers
     with carry rollover
   * Moved mpi_inv_mod() outside POLARSSL_GENPRIME
   * Prevent reading over buffer boundaries on X509 certificate parsing
   * mpi_exp_mod() now correctly handles negative base numbers (Closes ticket
     #52)
   * Fixed possible segfault in mpi_shift_r() (found by Manuel
     Pégourié-Gonnard)
   * Allow R and A to point to same mpi in mpi_div_mpi (found by Manuel
     Pégourié-Gonnard)
   * Added max length check for rsa_pkcs1_sign with PKCS#1 v2.1
   * Memory leak when using RSA_PKCS_V21 operations fixed
   * Handle encryption with private key and decryption with public key as per
     RFC 2313
   * Fixes for MSVC6

Security
   * Fixed potential memory zeroization on miscrafted RSA key (found by Eloi
     Vanderbeken)

= Version 1.1.4 released on 2012-05-31
Bugfix
   * Correctly handle empty SSL/TLS packets (Found by James Yonan)
   * Fixed potential heap corruption in x509_name allocation
   * Fixed single RSA test that failed on Big Endian systems (Closes ticket #54)

= Version 1.1.3 released on 2012-04-29
Bugfix
   * Fixed random MPI generation to not generate more size than requested.

= Version 1.1.2 released on 2012-04-26
Bugfix
   * Fixed handling error in mpi_cmp_mpi() on longer B values (found by
     Hui Dong)

Security
   * Fixed potential memory corruption on miscrafted client messages (found by
     Frama-C team at CEA LIST)
   * Fixed generation of DHM parameters to correct length (found by Ruslan
     Yushchenko)

= Version 1.1.1 released on 2012-01-23
Bugfix
   * Check for failed malloc() in ssl_set_hostname() and x509_get_entries()
     (Closes ticket #47, found by Hugo Leisink)
   * Fixed issues with Intel compiler on 64-bit systems (Closes ticket #50)
   * Fixed multiple compiler warnings for VS6 and armcc
   * Fixed bug in CTR_CRBG selftest

= Version 1.1.0 released on 2011-12-22
Features
   * Added ssl_session_reset() to allow better multi-connection pools of
     SSL contexts without needing to set all non-connection-specific
     data and pointers again. Adapted ssl_server to use this functionality.
   * Added ssl_set_max_version() to allow clients to offer a lower maximum
     supported version to a server to help buggy server implementations.
     (Closes ticket #36)
   * Added cipher_get_cipher_mode() and cipher_get_cipher_operation()
     introspection functions (Closes ticket #40)
   * Added CTR_DRBG based on AES-256-CTR (NIST SP 800-90) random generator
   * Added a generic entropy accumulator that provides support for adding
     custom entropy sources and added some generic and platform dependent
     entropy sources

Changes
   * Documentation for AES and Camellia in modes CTR and CFB128 clarified.
   * Fixed rsa_encrypt and rsa_decrypt examples to use public key for
     encryption and private key for decryption. (Closes ticket #34)
   * Inceased maximum size of ASN1 length reads to 32-bits.
   * Added an EXPLICIT tag number parameter to x509_get_ext()
   * Added a separate CRL entry extension parsing function
   * Separated the ASN.1 parsing code from the X.509 specific parsing code.
     So now there is a module that is controlled with POLARSSL_ASN1_PARSE_C.
   * Changed the defined key-length of DES ciphers in cipher.h to include the
     parity bits, to prevent mistakes in copying data. (Closes ticket #33)
   * Loads of minimal changes to better support WINCE as a build target
     (Credits go to Marco Lizza)
   * Added POLARSSL_MPI_WINDOW_SIZE definition to allow easier time to memory
     trade-off
   * Introduced POLARSSL_MPI_MAX_SIZE and POLARSSL_MPI_MAX_BITS for MPI size
     management (Closes ticket #44)
   * Changed the used random function pointer to more flexible format. Renamed
     havege_rand() to havege_random() to prevent mistakes. Lots of changes as
     a consequence in library code and programs
   * Moved all examples programs to use the new entropy and CTR_DRBG
   * Added permissive certificate parsing to x509parse_crt() and
     x509parse_crtfile(). With permissive parsing the parsing does not stop on
     encountering a parse-error. Beware that the meaning of return values has
     changed!
   * All error codes are now negative. Even on mermory failures and IO errors.

Bugfix
   * Fixed faulty HMAC-MD2 implementation. Found by dibac. (Closes
     ticket #37)
   * Fixed a bug where the CRL parser expected an EXPLICIT ASN.1 tag
     before version numbers
   * Allowed X509 key usage parsing to accept 4 byte values instead of the
     standard 1 byte version sometimes used by Microsoft. (Closes ticket #38)
   * Fixed incorrect behaviour in case of RSASSA-PSS with a salt length
     smaller than the hash length. (Closes ticket #41)
   * If certificate serial is longer than 32 octets, serial number is now
     appended with '....' after first 28 octets
   * Improved build support for s390x and sparc64 in bignum.h
   * Fixed MS Visual C++ name clash with int64 in sha4.h
   * Corrected removal of leading "00:" in printing serial numbers in
     certificates and CRLs

= Version 1.0.0 released on 2011-07-27
Features
   * Expanded cipher layer with support for CFB128 and CTR mode
   * Added rsa_encrypt and rsa_decrypt simple example programs.

Changes
   * The generic cipher and message digest layer now have normal error
     codes instead of integers

Bugfix
   * Undid faulty bug fix in ssl_write() when flushing old data (Ticket
     #18)

= Version 0.99-pre5 released on 2011-05-26
Features
   * Added additional Cipher Block Modes to symmetric ciphers
     (AES CTR, Camellia CTR, XTEA CBC) including the option to
     enable and disable individual modes when needed
   * Functions requiring File System functions can now be disabled
     by undefining POLARSSL_FS_IO
   * A error_strerror function() has been added to translate between
     error codes and their description.
   * Added mpi_get_bit() and mpi_set_bit() individual bit setter/getter
     functions.
   * Added ssl_mail_client and ssl_fork_server as example programs.

Changes
   * Major argument / variable rewrite. Introduced use of size_t
     instead of int for buffer lengths and loop variables for
     better unsigned / signed use. Renamed internal bigint types
     t_int and t_dbl to t_uint and t_udbl in the process
   * mpi_init() and mpi_free() now only accept a single MPI
     argument and do not accept variable argument lists anymore.
   * The error codes have been remapped and combining error codes
     is now done with a PLUS instead of an OR as error codes
     used are negative.
   * Changed behaviour of net_read(), ssl_fetch_input() and ssl_recv().
     net_recv() now returns 0 on EOF instead of
     POLARSSL_ERR_NET_CONN_RESET. ssl_fetch_input() returns
     POLARSSL_ERR_SSL_CONN_EOF on an EOF from its f_recv() function.
     ssl_read() returns 0 if a POLARSSL_ERR_SSL_CONN_EOF is received
     after the handshake.
   * Network functions now return POLARSSL_ERR_NET_WANT_READ or
     POLARSSL_ERR_NET_WANT_WRITE instead of the ambiguous
     POLARSSL_ERR_NET_TRY_AGAIN

= Version 0.99-pre4 released on 2011-04-01
Features
   * Added support for PKCS#1 v2.1 encoding and thus support
     for the RSAES-OAEP and RSASSA-PSS operations.
   * Reading of Public Key files incorporated into default x509
     functionality as well.
   * Added mpi_fill_random() for centralized filling of big numbers
     with random data (Fixed ticket #10)

Changes
   * Debug print of MPI now removes leading zero octets and
     displays actual bit size of the value.
   * x509parse_key() (and as a consequence x509parse_keyfile())
     does not zeroize memory in advance anymore. Use rsa_init()
     before parsing a key or keyfile!

Bugfix
   * Debug output of MPI's now the same independent of underlying
     platform (32-bit / 64-bit) (Fixes ticket #19, found by Mads
     Kiilerich and Mihai Militaru)
   * Fixed bug in ssl_write() when flushing old data (Fixed ticket
     #18, found by Nikolay Epifanov)
   * Fixed proper handling of RSASSA-PSS verification with variable
     length salt lengths

= Version 0.99-pre3 released on 2011-02-28
This release replaces version 0.99-pre2 which had possible copyright issues.
Features
   * Parsing PEM private keys encrypted with DES and AES
     are now supported as well (Fixes ticket #5)
   * Added crl_app program to allow easy reading and
     printing of X509 CRLs from file

Changes
   * Parsing of PEM files moved to separate module (Fixes
     ticket #13). Also possible to remove PEM support for
     systems only using DER encoding

Bugfixes
   * Corrected parsing of UTCTime dates before 1990 and
     after 1950
   * Support more exotic OID's when parsing certificates
     (found by Mads Kiilerich)
   * Support more exotic name representations when parsing
     certificates (found by Mads Kiilerich)
   * Replaced the expired test certificates
   * Do not bail out if no client certificate specified. Try
     to negotiate anonymous connection (Fixes ticket #12,
     found by Boris Krasnovskiy)

Security fixes
   * Fixed a possible Man-in-the-Middle attack on the
     Diffie Hellman key exchange (thanks to Larry Highsmith,
     Subreption LLC)

= Version 0.99-pre1 released on 2011-01-30
Features
Note: Most of these features have been donated by Fox-IT
   * Added Doxygen source code documentation parts
   * Added reading of DHM context from memory and file
   * Improved X509 certificate parsing to include extended
     certificate fields, including Key Usage
   * Improved certificate verification and verification
     against the available CRLs
   * Detection for DES weak keys and parity bits added
   * Improvements to support integration in other
     applications:
       + Added generic message digest and cipher wrapper
       + Improved information about current capabilities,
         status, objects and configuration
       + Added verification callback on certificate chain
         verification to allow external blacklisting
       + Additional example programs to show usage
   * Added support for PKCS#11 through the use of the
     libpkcs11-helper library

Changes
   * x509parse_time_expired() checks time in addition to
     the existing date check
   * The ciphers member of ssl_context and the cipher member
     of ssl_session have been renamed to ciphersuites and
     ciphersuite respectively. This clarifies the difference
     with the generic cipher layer and is better naming
     altogether

= Version 0.14.0 released on 2010-08-16
Features
   * Added support for SSL_EDH_RSA_AES_128_SHA and
     SSL_EDH_RSA_CAMELLIA_128_SHA ciphersuites
   * Added compile-time and run-time version information
   * Expanded ssl_client2 arguments for more flexibility
   * Added support for TLS v1.1

Changes
   * Made Makefile cleaner
   * Removed dependency on rand() in rsa_pkcs1_encrypt().
     Now using random fuction provided to function and
     changed the prototype of rsa_pkcs1_encrypt(),
     rsa_init() and rsa_gen_key().
   * Some SSL defines were renamed in order to avoid
     future confusion

Bug fixes
   * Fixed CMake out of source build for tests (found by
     kkert)
   * rsa_check_private() now supports PKCS1v2 keys as well
   * Fixed deadlock in rsa_pkcs1_encrypt() on failing random
     generator

= Version 0.13.1 released on 2010-03-24
Bug fixes
   * Fixed Makefile in library that was mistakenly merged
   * Added missing const string fixes

= Version 0.13.0 released on 2010-03-21
Features
   * Added option parsing for host and port selection to
     ssl_client2
   * Added support for GeneralizedTime in X509 parsing
   * Added cert_app program to allow easy reading and
     printing of X509 certificates from file or SSL
     connection.

Changes
   * Added const correctness for main code base
   * X509 signature algorithm determination is now
     in a function to allow easy future expansion
   * Changed symmetric cipher functions to
     identical interface (returning int result values)
   * Changed ARC4 to use separate input/output buffer
   * Added reset function for HMAC context as speed-up
     for specific use-cases

Bug fixes
   * Fixed bug resulting in failure to send the last
     certificate in the chain in ssl_write_certificate() and
     ssl_write_certificate_request() (found by fatbob)
   * Added small fixes for compiler warnings on a Mac
     (found by Frank de Brabander)
   * Fixed algorithmic bug in mpi_is_prime() (found by
     Smbat Tonoyan)

= Version 0.12.1 released on 2009-10-04
Changes
   * Coverage test definitions now support 'depends_on'
     tagging system.
   * Tests requiring specific hashing algorithms now honor
     the defines.

Bug fixes
   * Changed typo in #ifdef in x509parse.c (found
     by Eduardo)

= Version 0.12.0 released on 2009-07-28
Features
   * Added CMake makefiles as alternative to regular Makefiles.
   * Added preliminary Code Coverage tests for AES, ARC4,
     Base64, MPI, SHA-family, MD-family, HMAC-SHA-family,
     Camellia, DES, 3-DES, RSA PKCS#1, XTEA, Diffie-Hellman
     and X509parse.

Changes
   * Error codes are not (necessarily) negative. Keep
     this is mind when checking for errors.
   * RSA_RAW renamed to SIG_RSA_RAW for consistency.
   * Fixed typo in name of POLARSSL_ERR_RSA_OUTPUT_TOO_LARGE.
   * Changed interface for AES and Camellia setkey functions
     to indicate invalid key lengths.

Bug fixes
   * Fixed include location of endian.h on FreeBSD (found by
     Gabriel)
   * Fixed include location of endian.h and name clash on
     Apples (found by Martin van Hensbergen)
   * Fixed HMAC-MD2 by modifying md2_starts(), so that the
     required HMAC ipad and opad variables are not cleared.
     (found by code coverage tests)
   * Prevented use of long long in bignum if
     POLARSSL_HAVE_LONGLONG not defined (found by Giles
     Bathgate).
   * Fixed incorrect handling of negative strings in
     mpi_read_string() (found by code coverage tests).
   * Fixed segfault on handling empty rsa_context in
     rsa_check_pubkey() and rsa_check_privkey() (found by
     code coverage tests).
   * Fixed incorrect handling of one single negative input
     value in mpi_add_abs() (found by code coverage tests).
   * Fixed incorrect handling of negative first input
     value in mpi_sub_abs() (found by code coverage tests).
   * Fixed incorrect handling of negative first input
     value in mpi_mod_mpi() and mpi_mod_int(). Resulting
     change also affects mpi_write_string() (found by code
     coverage tests).
   * Corrected is_prime() results for 0, 1 and 2 (found by
     code coverage tests).
   * Fixed Camellia and XTEA for 64-bit Windows systems.

= Version 0.11.1 released on 2009-05-17
   * Fixed missing functionality for SHA-224, SHA-256, SHA384,
     SHA-512 in rsa_pkcs1_sign()

= Version 0.11.0 released on 2009-05-03
   * Fixed a bug in mpi_gcd() so that it also works when both
     input numbers are even and added testcases to check
     (found by Pierre Habouzit).
   * Added support for SHA-224, SHA-256, SHA-384 and SHA-512
     one way hash functions with the PKCS#1 v1.5 signing and
     verification.
   * Fixed minor bug regarding mpi_gcd located within the
     POLARSSL_GENPRIME block.
   * Fixed minor memory leak in x509parse_crt() and added better
     handling of 'full' certificate chains (found by Mathias
     Olsson).
   * Centralized file opening and reading for x509 files into
     load_file()
   * Made definition of net_htons() endian-clean for big endian
     systems (Found by Gernot).
   * Undefining POLARSSL_HAVE_ASM now also handles prevents asm in
     padlock and timing code.
   * Fixed an off-by-one buffer allocation in ssl_set_hostname()
     responsible for crashes and unwanted behaviour.
   * Added support for Certificate Revocation List (CRL) parsing.
   * Added support for CRL revocation to x509parse_verify() and
     SSL/TLS code.
   * Fixed compatibility of XTEA and Camellia on a 64-bit system
     (found by Felix von Leitner).

= Version 0.10.0 released on 2009-01-12
   * Migrated XySSL to PolarSSL
   * Added XTEA symmetric cipher
   * Added Camellia symmetric cipher
   * Added support for ciphersuites: SSL_RSA_CAMELLIA_128_SHA,
     SSL_RSA_CAMELLIA_256_SHA and SSL_EDH_RSA_CAMELLIA_256_SHA
   * Fixed dangerous bug that can cause a heap overflow in
     rsa_pkcs1_decrypt (found by Christophe Devine)

================================================================
XySSL ChangeLog

= Version 0.9 released on 2008-03-16

    * Added support for ciphersuite: SSL_RSA_AES_128_SHA
    * Enabled support for large files by default in aescrypt2.c
    * Preliminary openssl wrapper contributed by David Barrett
    * Fixed a bug in ssl_write() that caused the same payload to
      be sent twice in non-blocking mode when send returns EAGAIN
    * Fixed ssl_parse_client_hello(): session id and challenge must
      not be swapped in the SSLv2 ClientHello (found by Greg Robson)
    * Added user-defined callback debug function (Krystian Kolodziej)
    * Before freeing a certificate, properly zero out all cert. data
    * Fixed the "mode" parameter so that encryption/decryption are
      not swapped on PadLock; also fixed compilation on older versions
      of gcc (bug reported by David Barrett)
    * Correctly handle the case in padlock_xcryptcbc() when input or
      output data is non-aligned by falling back to the software
      implementation, as VIA Nehemiah cannot handle non-aligned buffers
    * Fixed a memory leak in x509parse_crt() which was reported by Greg
      Robson-Garth; some x509write.c fixes by Pascal Vizeli, thanks to
      Matthew Page who reported several bugs
    * Fixed x509_get_ext() to accept some rare certificates which have
      an INTEGER instead of a BOOLEAN for BasicConstraints::cA.
    * Added support on the client side for the TLS "hostname" extension
      (patch contributed by David Patino)
    * Make x509parse_verify() return BADCERT_CN_MISMATCH when an empty
      string is passed as the CN (bug reported by spoofy)
    * Added an option to enable/disable the BN assembly code
    * Updated rsa_check_privkey() to verify that (D*E) = 1 % (P-1)*(Q-1)
    * Disabled obsolete hash functions by default (MD2, MD4); updated
      selftest and benchmark to not test ciphers that have been disabled
    * Updated x509parse_cert_info() to correctly display byte 0 of the
      serial number, setup correct server port in the ssl client example
    * Fixed a critical denial-of-service with X.509 cert. verification:
      peer may cause xyssl to loop indefinitely by sending a certificate
      for which the RSA signature check fails (bug reported by Benoit)
    * Added test vectors for: AES-CBC, AES-CFB, DES-CBC and 3DES-CBC,
      HMAC-MD5, HMAC-SHA1, HMAC-SHA-256, HMAC-SHA-384, and HMAC-SHA-512
    * Fixed HMAC-SHA-384 and HMAC-SHA-512 (thanks to Josh Sinykin)
    * Modified ssl_parse_client_key_exchange() to protect against
      Daniel Bleichenbacher attack on PKCS#1 v1.5 padding, as well
      as the Klima-Pokorny-Rosa extension of Bleichenbacher's attack
    * Updated rsa_gen_key() so that ctx->N is always nbits in size
    * Fixed assembly PPC compilation errors on Mac OS X, thanks to
      David Barrett and Dusan Semen

= Version 0.8 released on 2007-10-20

    * Modified the HMAC functions to handle keys larger
      than 64 bytes, thanks to Stephane Desneux and gary ng
    * Fixed ssl_read_record() to properly update the handshake
      message digests, which fixes IE6/IE7 client authentication
    * Cleaned up the XYSSL* #defines, suggested by Azriel Fasten
    * Fixed net_recv(), thanks to Lorenz Schori and Egon Kocjan
    * Added user-defined callbacks for handling I/O and sessions
    * Added lots of debugging output in the SSL/TLS functions
    * Added preliminary X.509 cert. writing by Pascal Vizeli
    * Added preliminary support for the VIA PadLock routines
    * Added AES-CFB mode of operation, contributed by chmike
    * Added an SSL/TLS stress testing program (ssl_test.c)
    * Updated the RSA PKCS#1 code to allow choosing between
      RSA_PUBLIC and RSA_PRIVATE, as suggested by David Barrett
    * Updated ssl_read() to skip 0-length records from OpenSSL
    * Fixed the make install target to comply with *BSD make
    * Fixed a bug in mpi_read_binary() on 64-bit platforms
    * mpi_is_prime() speedups, thanks to Kevin McLaughlin
    * Fixed a long standing memory leak in mpi_is_prime()
    * Replaced realloc with malloc in mpi_grow(), and set
      the sign of zero as positive in mpi_init() (reported
      by Jonathan M. McCune)

= Version 0.7 released on 2007-07-07

    * Added support for the MicroBlaze soft-core processor
    * Fixed a bug in ssl_tls.c which sometimes prevented SSL
      connections from being established with non-blocking I/O
    * Fixed a couple bugs in the VS6 and UNIX Makefiles
    * Fixed the "PIC register ebx clobbered in asm" bug
    * Added HMAC starts/update/finish support functions
    * Added the SHA-224, SHA-384 and SHA-512 hash functions
    * Fixed the net_set_*block routines, thanks to Andreas
    * Added a few demonstration programs: md5sum, sha1sum,
      dh_client, dh_server, rsa_genkey, rsa_sign, rsa_verify
    * Added new bignum import and export helper functions
    * Rewrote README.txt in program/ssl/ca to better explain
      how to create a test PKI

= Version 0.6 released on 2007-04-01

    * Ciphers used in SSL/TLS can now be disabled at compile
      time, to reduce the memory footprint on embedded systems
    * Added multiply assembly code for the TriCore and modified
      havege_struct for this processor, thanks to David Patiño
    * Added multiply assembly code for 64-bit PowerPCs,
      thanks to Peking University and the OSU Open Source Lab
    * Added experimental support of Quantum Cryptography
    * Added support for autoconf, contributed by Arnaud Cornet
    * Fixed "long long" compilation issues on IA-64 and PPC64
    * Fixed a bug introduced in xyssl-0.5/timing.c: hardclock
      was not being correctly defined on ARM and MIPS

= Version 0.5 released on 2007-03-01

    * Added multiply assembly code for SPARC and Alpha
    * Added (beta) support for non-blocking I/O operations
    * Implemented session resuming and client authentication
    * Fixed some portability issues on WinCE, MINIX 3, Plan9
      (thanks to Benjamin Newman), HP-UX, FreeBSD and Solaris
    * Improved the performance of the EDH key exchange
    * Fixed a bug that caused valid packets with a payload
      size of 16384 bytes to be rejected

= Version 0.4 released on 2007-02-01

    * Added support for Ephemeral Diffie-Hellman key exchange
    * Added multiply asm code for SSE2, ARM, PPC, MIPS and M68K
    * Various improvement to the modular exponentiation code
    * Rewrote the headers to generate the API docs with doxygen
    * Fixed a bug in ssl_encrypt_buf (incorrect padding was
      generated) and in ssl_parse_client_hello (max. client
      version was not properly set), thanks to Didier Rebeix
    * Fixed another bug in ssl_parse_client_hello: clients with
      cipherlists larger than 96 bytes were incorrectly rejected
    * Fixed a couple memory leak in x509_read.c

= Version 0.3 released on 2007-01-01

    * Added server-side SSLv3 and TLSv1.0 support
    * Multiple fixes to enhance the compatibility with g++,
      thanks to Xosé Antón Otero Ferreira
    * Fixed a bug in the CBC code, thanks to dowst; also,
      the bignum code is no longer dependent on long long
    * Updated rsa_pkcs1_sign to handle arbitrary large inputs
    * Updated timing.c for improved compatibility with i386
      and 486 processors, thanks to Arnaud Cornet

= Version 0.2 released on 2006-12-01

    * Updated timing.c to support ARM and MIPS arch
    * Updated the MPI code to support 8086 on MSVC 1.5
    * Added the copyright notice at the top of havege.h
    * Fixed a bug in sha2_hmac, thanks to newsoft/Wenfang Zhang
    * Fixed a bug reported by Adrian Rüegsegger in x509_read_key
    * Fixed a bug reported by Torsten Lauter in ssl_read_record
    * Fixed a bug in rsa_check_privkey that would wrongly cause
      valid RSA keys to be dismissed (thanks to oldwolf)
    * Fixed a bug in mpi_is_prime that caused some primes to fail
      the Miller-Rabin primality test

    I'd also like to thank Younès Hafri for the CRUX linux port,
    Khalil Petit who added XySSL into pkgsrc and Arnaud Cornet
    who maintains the Debian package :-)

= Version 0.1 released on 2006-11-01
