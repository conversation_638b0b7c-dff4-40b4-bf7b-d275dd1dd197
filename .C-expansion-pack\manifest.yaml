name: c-embedded-dev
version: 1.0.0
description: >-
  专门针对单片机嵌入式C开发的AI辅助工具包，重点支持nrf52832和ads129x系列芯片的软件设计。
  提供完整的嵌入式开发工作流，从芯片配置到功耗优化的全方位支持。
author: PhD研究生
bmad_version: "4.0.0"

# 扩展包中要创建的文件
files:
  agents:
    - c-embedded-orchestrator.md    # 李工 - 嵌入式开发协调员
    - hardware-abstraction-specialist.md  # 张博士 - HAL专家
    - realtime-systems-expert.md    # 王工 - 实时系统专家
    - power-management-specialist.md # 陈工 - 功耗管理专家
    - communication-protocol-expert.md # 刘工 - 通信协议专家

  data:
    - embedded-c-best-practices.md   # 嵌入式C开发最佳实践
    - embedded-terminology.md        # 嵌入式系统术语和概念
    - embedded-standards.md          # 嵌入式开发标准和规范

  tasks:
    # 核心工具 (必需 - 从bmad-core复制)
    - create-doc.md                 # 文档创建系统
    - execute-checklist.md          # 检查清单验证系统
    # 领域特定任务
    - chip-initialization.md        # 芯片初始化配置
    - peripheral-driver-development.md # 外设驱动开发
    - interrupt-handler-design.md   # 中断处理程序设计
    - power-optimization.md         # 功耗优化分析
    - communication-protocol-implementation.md # 通信协议实现
    - memory-optimization.md        # 内存优化分析
    - debugging-strategy.md         # 调试策略制定
    - code-review-embedded.md       # 嵌入式代码审查

  utils:
    # 核心工具 (必需 - 从bmad-core复制)
    - template-format.md            # 模板标记约定
    - workflow-management.md        # 工作流编排系统

  templates:
    - chip-config-template.md       # 芯片配置文档模板
    - driver-implementation-template.md # 驱动实现模板
    - interrupt-handler-template.md # 中断处理程序模板
    - power-analysis-template.md    # 功耗分析报告模板

  checklists:
    - embedded-code-quality-checklist.md # 嵌入式代码质量检查
    - hardware-integration-checklist.md  # 硬件集成检查
    - power-consumption-checklist.md     # 功耗检查清单

  workflows:
    - embedded-development-workflow.md   # 嵌入式开发工作流

  agent-teams:
    - embedded-dev-team.yaml            # 嵌入式开发团队配置

# 用户必须提供的数据文件 (放置在bmad-core/data/目录)
required_user_data:
  - filename: nrf52832-specs.md
    description: nrf52832芯片技术规格、寄存器映射和配置参数
    format: Markdown格式，包含寄存器地址、位域定义、时序参数
    example: "寄存器映射表、中断向量表、时钟配置选项"
    validation: 包含完整的外设寄存器定义和配置示例

  - filename: ads129x-specs.md
    description: ads129x系列芯片技术规格和配置参数
    format: Markdown格式，包含寄存器配置、采样参数、通信协议
    example: "ADC配置寄存器、采样率设置、SPI通信时序"
    validation: 包含完整的配置寄存器说明和使用示例

  - filename: project-requirements.md
    description: 具体项目需求和技术约束条件
    format: Markdown格式，包含功能需求、性能指标、约束条件
    example: "采样频率要求、功耗限制、实时性要求"
    validation: 明确的量化指标和验收标准

  - filename: hardware-schematic.md
    description: 硬件原理图和引脚连接信息
    format: Markdown格式，包含引脚分配、电路连接、信号定义
    example: "GPIO分配表、SPI连接图、电源连接"
    validation: 完整的硬件接口定义和连接关系

  - filename: power-budget.md
    description: 功耗预算和电源管理要求
    format: Markdown格式，包含功耗目标、工作模式、电池规格
    example: "待机功耗<10μA、工作功耗<5mA、电池容量"
    validation: 明确的功耗指标和测量方法

# 嵌入在扩展包中的知识库文件
embedded_knowledge:
  - embedded-c-best-practices.md
  - embedded-terminology.md
  - embedded-standards.md

# 对核心BMad组件的依赖
core_dependencies:
  agents:
    - architect        # 用于系统设计
    - developer       # 用于代码实现
    - qa-specialist   # 用于质量保证
  tasks:
    - create-doc
    - execute-checklist
  workflows:
    - brownfield-service

# 代理协调模式
agent_coordination:
  orchestrator: c-embedded-orchestrator
  handoff_protocols: true
  numbered_options: true
  quality_integration: comprehensive

# 安装后消息
post_install_message: |
  🎯 C嵌入式开发扩展包已就绪！

  🧙 协调员: 李工 (c-embedded-orchestrator)
  👥 专家代理: 5个专业领域专家
  📝 模板: 4个智能模板，支持LLM指令嵌入
  ✅ 质量保证: 多级验证，星级评分系统

  📋 必需的用户数据文件 (放置在 bmad-core/data/):
  - nrf52832-specs.md: nrf52832芯片完整技术规格
  - ads129x-specs.md: ads129x系列芯片配置参数
  - project-requirements.md: 项目需求和约束条件
  - hardware-schematic.md: 硬件原理图和连接信息
  - power-budget.md: 功耗预算和电源管理要求

  🚀 快速开始:
  1. 将必需的数据文件添加到 bmad-core/data/
  2. 运行: npm run agent c-embedded-orchestrator
  3. 跟随李工的编号选项进行开发

  💡 嵌入式知识库:
  - 嵌入式C开发最佳实践和术语
  - 质量标准和验证标准
  - 工作流编排和移交协议
