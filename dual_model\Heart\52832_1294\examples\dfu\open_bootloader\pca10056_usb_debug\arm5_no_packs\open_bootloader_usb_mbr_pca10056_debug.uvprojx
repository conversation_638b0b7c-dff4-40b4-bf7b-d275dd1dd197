<?xml version="1.0" encoding="UTF-8" standalone="no" ?>
<Project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="project_projx.xsd">

  <SchemaVersion>2.1</SchemaVersion>

  <Header>### uVision Project, (C) Keil Software</Header>

  <Targets>    <Target>
      <TargetName>nrf52840_xxaa_debug</TargetName>
      <ToolsetNumber>0x4</ToolsetNumber>
      <ToolsetName>ARM-ADS</ToolsetName>
      <TargetOption>
        <TargetCommonOption>          <Device>nRF52840_xxAA</Device>
          <Vendor>Nordic Semiconductor</Vendor>
          <PackID>NordicSemiconductor.nRF_DeviceFamilyPack.8.32.1</PackID>
          <PackURL>http://developer.nordicsemi.com/nRF51_SDK/pieces/nRF_DeviceFamilyPack/</PackURL>          <Cpu>IROM(0x00000000,0x100000) IRAM(0x20000000,0x40000) CPUTYPE("Cortex-M4") FPU2 CLOCK(64000000) ELITTLE</Cpu>
          <FlashUtilSpec></FlashUtilSpec>
          <StartupFile></StartupFile>
          <FlashDriverDll></FlashDriverDll>
          <DeviceId>0</DeviceId>
          <RegisterFile>$$Device:nRF52832_xxAA$Device\Include\nrf.h</RegisterFile>
          <MemoryEnv></MemoryEnv>
          <Cmp></Cmp>
          <Asm></Asm>
          <Linker></Linker>
          <OHString></OHString>
          <InfinionOptionDll></InfinionOptionDll>
          <SLE66CMisc></SLE66CMisc>
          <SLE66AMisc></SLE66AMisc>
          <SLE66LinkerMisc></SLE66LinkerMisc>
          <SFDFile>..\..\..\..\..\modules\nrfx\mdk\nrf52840.svd</SFDFile>
          <bCustSvd>0</bCustSvd>
          <UseEnv>0</UseEnv>
          <BinPath></BinPath>
          <IncludePath></IncludePath>
          <LibPath></LibPath>
          <RegisterFilePath></RegisterFilePath>
          <DBRegisterFilePath></DBRegisterFilePath>
          <TargetStatus>
            <Error>0</Error>
            <ExitCodeStop>0</ExitCodeStop>
            <ButtonStop>0</ButtonStop>
            <NotGenerated>0</NotGenerated>
            <InvalidFlash>1</InvalidFlash>
          </TargetStatus>
          <OutputDirectory>.\_build\</OutputDirectory>
          <OutputName>nrf52840_xxaa_debug</OutputName>
          <CreateExecutable>1</CreateExecutable>
          <CreateLib>0</CreateLib>
          <CreateHexFile>1</CreateHexFile>
          <DebugInformation>1</DebugInformation>
          <BrowseInformation>1</BrowseInformation>
          <ListingPath>.\_build\</ListingPath>
          <HexFormatSelection>1</HexFormatSelection>
          <Merge32K>0</Merge32K>
          <CreateBatchFile>0</CreateBatchFile>
          <BeforeCompile>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopU1X>0</nStopU1X>
            <nStopU2X>0</nStopU2X>
          </BeforeCompile>
          <BeforeMake>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopB1X>0</nStopB1X>
            <nStopB2X>0</nStopB2X>
          </BeforeMake>
          <AfterMake>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopA1X>0</nStopA1X>
            <nStopA2X>0</nStopA2X>
          </AfterMake>
          <SelectedForBatchBuild>0</SelectedForBatchBuild>
          <SVCSIdString></SVCSIdString>
        </TargetCommonOption>
        <CommonProperty>
          <UseCPPCompiler>0</UseCPPCompiler>
          <RVCTCodeConst>0</RVCTCodeConst>
          <RVCTZI>0</RVCTZI>
          <RVCTOtherData>0</RVCTOtherData>
          <ModuleSelection>0</ModuleSelection>
          <IncludeInBuild>1</IncludeInBuild>
          <AlwaysBuild>0</AlwaysBuild>
          <GenerateAssemblyFile>0</GenerateAssemblyFile>
          <AssembleAssemblyFile>0</AssembleAssemblyFile>
          <PublicsOnly>0</PublicsOnly>
          <StopOnExitCode>3</StopOnExitCode>
          <CustomArgument></CustomArgument>
          <IncludeLibraryModules></IncludeLibraryModules>
          <ComprImg>1</ComprImg>
        </CommonProperty>
        <DllOption>
          <SimDllName></SimDllName>
          <SimDllArguments></SimDllArguments>
          <SimDlgDll></SimDlgDll>
          <SimDlgDllArguments></SimDlgDllArguments>
          <TargetDllName>SARMCM3.DLL</TargetDllName>
          <TargetDllArguments>-MPU</TargetDllArguments>
          <TargetDlgDll>TCM.DLL</TargetDlgDll>
          <TargetDlgDllArguments>-pCM4</TargetDlgDllArguments>
        </DllOption>
        <DebugOption>
          <OPTHX>
            <HexSelection>1</HexSelection>
            <HexRangeLowAddress>0</HexRangeLowAddress>
            <HexRangeHighAddress>0</HexRangeHighAddress>
            <HexOffset>0</HexOffset>
            <Oh166RecLen>16</Oh166RecLen>
          </OPTHX>
          <Simulator>
            <UseSimulator>0</UseSimulator>
            <LoadApplicationAtStartup>1</LoadApplicationAtStartup>
            <RunToMain>1</RunToMain>
            <RestoreBreakpoints>1</RestoreBreakpoints>
            <RestoreWatchpoints>1</RestoreWatchpoints>
            <RestoreMemoryDisplay>1</RestoreMemoryDisplay>
            <RestoreFunctions>1</RestoreFunctions>
            <RestoreToolbox>1</RestoreToolbox>
            <LimitSpeedToRealTime>0</LimitSpeedToRealTime>
            <RestoreSysVw>1</RestoreSysVw>
          </Simulator>
          <Target>
            <UseTarget>1</UseTarget>
            <LoadApplicationAtStartup>1</LoadApplicationAtStartup>
            <RunToMain>1</RunToMain>
            <RestoreBreakpoints>1</RestoreBreakpoints>
            <RestoreWatchpoints>1</RestoreWatchpoints>
            <RestoreMemoryDisplay>1</RestoreMemoryDisplay>
            <RestoreFunctions>0</RestoreFunctions>
            <RestoreToolbox>1</RestoreToolbox>
            <RestoreTracepoints>0</RestoreTracepoints>
            <RestoreSysVw>1</RestoreSysVw>            <UsePdscDebugDescription>1</UsePdscDebugDescription>          </Target>
          <RunDebugAfterBuild>0</RunDebugAfterBuild>
          <TargetSelection>-1</TargetSelection>
          <SimDlls>
            <CpuDll></CpuDll>
            <CpuDllArguments></CpuDllArguments>
            <PeripheralDll></PeripheralDll>
            <PeripheralDllArguments></PeripheralDllArguments>
            <InitializationFile></InitializationFile>
          </SimDlls>
          <TargetDlls>
            <CpuDll></CpuDll>
            <CpuDllArguments></CpuDllArguments>
            <PeripheralDll></PeripheralDll>
            <PeripheralDllArguments></PeripheralDllArguments>
            <InitializationFile></InitializationFile>
            <Driver>Segger\JL2CM3.dll</Driver>
          </TargetDlls>
        </DebugOption>
        <Utilities>
          <Flash1>
            <UseTargetDll>1</UseTargetDll>
            <UseExternalTool>0</UseExternalTool>
            <RunIndependent>0</RunIndependent>
            <UpdateFlashBeforeDebugging>1</UpdateFlashBeforeDebugging>
            <Capability>1</Capability>
            <DriverSelection>4099</DriverSelection>
          </Flash1>
          <bUseTDR>1</bUseTDR>
          <Flash2>Segger\JL2CM3.dll</Flash2>
          <Flash3></Flash3>
          <Flash4></Flash4>
        </Utilities>
        <TargetArmAds>
          <ArmAdsMisc>
            <GenerateListings>0</GenerateListings>
            <asHll>1</asHll>
            <asAsm>1</asAsm>
            <asMacX>1</asMacX>
            <asSyms>1</asSyms>
            <asFals>1</asFals>
            <asDbgD>1</asDbgD>
            <asForm>1</asForm>
            <ldLst>0</ldLst>
            <ldmm>1</ldmm>
            <ldXref>1</ldXref>
            <BigEnd>0</BigEnd>
            <AdsALst>1</AdsALst>
            <AdsACrf>1</AdsACrf>
            <AdsANop>0</AdsANop>
            <AdsANot>0</AdsANot>
            <AdsLLst>1</AdsLLst>
            <AdsLmap>1</AdsLmap>
            <AdsLcgr>1</AdsLcgr>
            <AdsLsym>1</AdsLsym>
            <AdsLszi>1</AdsLszi>
            <AdsLtoi>1</AdsLtoi>
            <AdsLsun>1</AdsLsun>
            <AdsLven>1</AdsLven>
            <AdsLsxf>1</AdsLsxf>
            <RvctClst>0</RvctClst>
            <GenPPlst>0</GenPPlst>
            <AdsCpuType>"Cortex-M4"</AdsCpuType>
            <RvctDeviceName></RvctDeviceName>
            <mOS>0</mOS>
            <uocRom>0</uocRom>
            <uocRam>0</uocRam>
            <hadIROM>1</hadIROM>
            <hadIRAM>1</hadIRAM>
            <hadXRAM>0</hadXRAM>
            <uocXRam>0</uocXRam>
            <RvdsVP>2</RvdsVP>
            <hadIRAM2>0</hadIRAM2>
            <hadIROM2>0</hadIROM2>
            <StupSel>8</StupSel>
            <useUlib>1</useUlib>
            <EndSel>0</EndSel>
            <uLtcg>0</uLtcg>
            <nSecure>0</nSecure>
            <RoSelD>3</RoSelD>
            <RwSelD>3</RwSelD>
            <CodeSel>0</CodeSel>
            <OptFeed>0</OptFeed>
            <NoZi1>0</NoZi1>
            <NoZi2>0</NoZi2>
            <NoZi3>0</NoZi3>
            <NoZi4>0</NoZi4>
            <NoZi5>0</NoZi5>
            <Ro1Chk>0</Ro1Chk>
            <Ro2Chk>0</Ro2Chk>
            <Ro3Chk>0</Ro3Chk>
            <Ir1Chk>1</Ir1Chk>
            <Ir2Chk>0</Ir2Chk>
            <Ra1Chk>0</Ra1Chk>
            <Ra2Chk>0</Ra2Chk>
            <Ra3Chk>0</Ra3Chk>
            <Im1Chk>1</Im1Chk>
            <Im2Chk>0</Im2Chk>
            <OnChipMemories>
              <Ocm1>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm1>
              <Ocm2>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm2>
              <Ocm3>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm3>
              <Ocm4>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm4>
              <Ocm5>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm5>
              <Ocm6>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm6>
              <IRAM>
                <Type>0</Type>
                <StartAddress>0x20000000</StartAddress>
                <Size>0x40000</Size>
              </IRAM>
              <IROM>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x100000</Size>
              </IROM>
              <XRAM>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </XRAM>
              <OCR_RVCT1>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT1>
              <OCR_RVCT2>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT2>
              <OCR_RVCT3>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT3>
              <OCR_RVCT4>
                <Type>1</Type>
                <StartAddress>0xe0000</StartAddress>
                <Size>0x1e000</Size>
              </OCR_RVCT4>
              <OCR_RVCT5>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT5>
              <OCR_RVCT6>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT6>
              <OCR_RVCT7>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT7>
              <OCR_RVCT8>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT8>
              <OCR_RVCT9>
                <Type>0</Type>
                <StartAddress>0x20000008</StartAddress>
                <Size>0x3fff8</Size>
              </OCR_RVCT9>
              <OCR_RVCT10>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT10>
            </OnChipMemories>
            <RvctStartVector></RvctStartVector>
          </ArmAdsMisc>
          <Cads>
            <interw>1</interw>
            <Optim>4</Optim>
            <oTime>0</oTime>
            <SplitLS>0</SplitLS>
            <OneElfS>1</OneElfS>
            <Strict>0</Strict>
            <EnumInt>0</EnumInt>
            <PlainCh>0</PlainCh>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <wLevel>0</wLevel>
            <uThumb>0</uThumb>
            <uSurpInc>0</uSurpInc>
            <uC99>1</uC99>
            <useXO>0</useXO>
            <v6Lang>0</v6Lang>
            <v6LangP>0</v6LangP>
            <vShortEn>0</vShortEn>
            <vShortWch>0</vShortWch>
            <VariousControls>
              <MiscControls>--reduce_paths</MiscControls>
              <Define> APP_TIMER_V2 APP_TIMER_V2_RTC1_ENABLED BOARD_PCA10056 CONFIG_GPIO_AS_PINRESET DEBUG_NRF FLOAT_ABI_HARD MBR_PRESENT NRF52840_XXAA NRF_DFU_DEBUG_VERSION NRF_DFU_SETTINGS_VERSION=2 SVC_INTERFACE_CALL_AS_NORMAL_FUNCTION __HEAP_SIZE=0</Define>
              <Undefine></Undefine>
              <IncludePath>..\..\config;..\..\..\..\..\components\boards;..\..\..\..\..\components\drivers_nrf\nrf_soc_nosd;..\..\..\..\..\components\libraries\atomic;..\..\..\..\..\components\libraries\atomic_fifo;..\..\..\..\..\components\libraries\balloc;..\..\..\..\..\components\libraries\bootloader;..\..\..\..\..\components\libraries\bootloader\dfu;..\..\..\..\..\components\libraries\bootloader\serial_dfu;..\..\..\..\..\components\libraries\crc32;..\..\..\..\..\components\libraries\crypto;..\..\..\..\..\components\libraries\crypto\backend\cc310;..\..\..\..\..\components\libraries\crypto\backend\cc310_bl;..\..\..\..\..\components\libraries\crypto\backend\cifra;..\..\..\..\..\components\libraries\crypto\backend\mbedtls;..\..\..\..\..\components\libraries\crypto\backend\micro_ecc;..\..\..\..\..\components\libraries\crypto\backend\nrf_hw;..\..\..\..\..\components\libraries\crypto\backend\nrf_sw;..\..\..\..\..\components\libraries\crypto\backend\oberon;..\..\..\..\..\components\libraries\crypto\backend\optiga;..\..\..\..\..\components\libraries\delay;..\..\..\..\..\components\libraries\experimental_section_vars;..\..\..\..\..\components\libraries\fstorage;..\..\..\..\..\components\libraries\led_softblink;..\..\..\..\..\components\libraries\log;..\..\..\..\..\components\libraries\log\src;..\..\..\..\..\components\libraries\low_power_pwm;..\..\..\..\..\components\libraries\mem_manager;..\..\..\..\..\components\libraries\memobj;..\..\..\..\..\components\libraries\mutex;..\..\..\..\..\components\libraries\queue;..\..\..\..\..\components\libraries\ringbuf;..\..\..\..\..\components\libraries\scheduler;..\..\..\..\..\components\libraries\slip;..\..\..\..\..\components\libraries\sortlist;..\..\..\..\..\components\libraries\stack_info;..\..\..\..\..\components\libraries\strerror;..\..\..\..\..\components\libraries\timer;..\..\..\..\..\components\libraries\usbd;..\..\..\..\..\components\libraries\usbd\class\cdc;..\..\..\..\..\components\libraries\usbd\class\cdc\acm;..\..\..\..\..\components\libraries\util;..\..\..\..\..\components\softdevice\mbr\headers;..\..;..\..\..\..\..\external\fprintf;..\..\..\..\..\external\nano-pb;..\..\..\..\..\external\nrf_cc310\include;..\..\..\..\..\external\nrf_cc310_bl\include;..\..\..\..\..\external\nrf_oberon;..\..\..\..\..\external\nrf_oberon\include;..\..\..\..\..\external\segger_rtt;..\..\..\..\..\external\utf_converter;..\..\..\..\..\integration\nrfx;..\..\..\..\..\integration\nrfx\legacy;..\..\..\..\..\modules\nrfx;..\..\..\..\..\modules\nrfx\drivers\include;..\..\..\..\..\modules\nrfx\hal;..\config</IncludePath>
            </VariousControls>
          </Cads>
          <Aads>
            <interw>1</interw>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <thumb>0</thumb>
            <SplitLS>0</SplitLS>
            <SwStkChk>0</SwStkChk>
            <NoWarn>0</NoWarn>
            <uSurpInc>0</uSurpInc>
            <useXO>0</useXO>
            <VariousControls>
              <MiscControls> --cpreproc_opts=-DAPP_TIMER_V2,-DAPP_TIMER_V2_RTC1_ENABLED,-DBOARD_PCA10056,-DCONFIG_GPIO_AS_PINRESET,-DDEBUG_NRF,-DFLOAT_ABI_HARD,-DMBR_PRESENT,-DNRF52840_XXAA,-DNRF_DFU_DEBUG_VERSION,-DNRF_DFU_SETTINGS_VERSION=2,-DSVC_INTERFACE_CALL_AS_NORMAL_FUNCTION,-D__HEAP_SIZE=0</MiscControls>
              <Define> APP_TIMER_V2 APP_TIMER_V2_RTC1_ENABLED BOARD_PCA10056 CONFIG_GPIO_AS_PINRESET DEBUG_NRF FLOAT_ABI_HARD MBR_PRESENT NRF52840_XXAA NRF_DFU_DEBUG_VERSION NRF_DFU_SETTINGS_VERSION=2 SVC_INTERFACE_CALL_AS_NORMAL_FUNCTION __HEAP_SIZE=0</Define>
              <Undefine></Undefine>
              <IncludePath>..\..\config;..\..\..\..\..\components\boards;..\..\..\..\..\components\drivers_nrf\nrf_soc_nosd;..\..\..\..\..\components\libraries\atomic;..\..\..\..\..\components\libraries\atomic_fifo;..\..\..\..\..\components\libraries\balloc;..\..\..\..\..\components\libraries\bootloader;..\..\..\..\..\components\libraries\bootloader\dfu;..\..\..\..\..\components\libraries\bootloader\serial_dfu;..\..\..\..\..\components\libraries\crc32;..\..\..\..\..\components\libraries\crypto;..\..\..\..\..\components\libraries\crypto\backend\cc310;..\..\..\..\..\components\libraries\crypto\backend\cc310_bl;..\..\..\..\..\components\libraries\crypto\backend\cifra;..\..\..\..\..\components\libraries\crypto\backend\mbedtls;..\..\..\..\..\components\libraries\crypto\backend\micro_ecc;..\..\..\..\..\components\libraries\crypto\backend\nrf_hw;..\..\..\..\..\components\libraries\crypto\backend\nrf_sw;..\..\..\..\..\components\libraries\crypto\backend\oberon;..\..\..\..\..\components\libraries\crypto\backend\optiga;..\..\..\..\..\components\libraries\delay;..\..\..\..\..\components\libraries\experimental_section_vars;..\..\..\..\..\components\libraries\fstorage;..\..\..\..\..\components\libraries\led_softblink;..\..\..\..\..\components\libraries\log;..\..\..\..\..\components\libraries\log\src;..\..\..\..\..\components\libraries\low_power_pwm;..\..\..\..\..\components\libraries\mem_manager;..\..\..\..\..\components\libraries\memobj;..\..\..\..\..\components\libraries\mutex;..\..\..\..\..\components\libraries\queue;..\..\..\..\..\components\libraries\ringbuf;..\..\..\..\..\components\libraries\scheduler;..\..\..\..\..\components\libraries\slip;..\..\..\..\..\components\libraries\sortlist;..\..\..\..\..\components\libraries\stack_info;..\..\..\..\..\components\libraries\strerror;..\..\..\..\..\components\libraries\timer;..\..\..\..\..\components\libraries\usbd;..\..\..\..\..\components\libraries\usbd\class\cdc;..\..\..\..\..\components\libraries\usbd\class\cdc\acm;..\..\..\..\..\components\libraries\util;..\..\..\..\..\components\softdevice\mbr\headers;..\..;..\..\..\..\..\external\fprintf;..\..\..\..\..\external\nano-pb;..\..\..\..\..\external\nrf_cc310\include;..\..\..\..\..\external\nrf_cc310_bl\include;..\..\..\..\..\external\nrf_oberon;..\..\..\..\..\external\nrf_oberon\include;..\..\..\..\..\external\segger_rtt;..\..\..\..\..\external\utf_converter;..\..\..\..\..\integration\nrfx;..\..\..\..\..\integration\nrfx\legacy;..\..\..\..\..\modules\nrfx;..\..\..\..\..\modules\nrfx\drivers\include;..\..\..\..\..\modules\nrfx\hal;..\config</IncludePath>
            </VariousControls>
          </Aads>
          <LDads>
            <umfTarg>1</umfTarg>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <noStLib>0</noStLib>
            <RepFail>1</RepFail>
            <useFile>0</useFile>
            <TextAddressRange>0x00000000</TextAddressRange>
            <DataAddressRange>0x20000000</DataAddressRange>
            <pXoBase></pXoBase>
            <ScatterFile></ScatterFile>
            <IncludeLibs></IncludeLibs>
            <IncludeLibsPath></IncludeLibsPath>
            <Misc>--diag_suppress 6330</Misc>
            <LinkerInputFile></LinkerInputFile>
            <DisabledWarnings></DisabledWarnings>
          </LDads>
        </TargetArmAds>
      </TargetOption>
      <Groups>        <Group>
          <GroupName>Application</GroupName>
          <Files>            <File>
              <FileName>dfu_public_key.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\dfu_public_key.c</FilePath>            </File>            <File>
              <FileName>main.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\main.c</FilePath>            </File>            <File>
              <FileName>sdk_config.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\config\sdk_config.h</FilePath>            </File>          </Files>
        </Group>        <Group>
          <GroupName>Board Definition</GroupName>
          <Files>            <File>
              <FileName>boards.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\boards\boards.c</FilePath>            </File>          </Files>
        </Group>        <Group>
          <GroupName>UTF8/UTF16 converter</GroupName>
          <Files>            <File>
              <FileName>utf.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\external\utf_converter\utf.c</FilePath>            </File>          </Files>
        </Group>        <Group>
          <GroupName>nRF_Bootloader</GroupName>
          <Files>            <File>
              <FileName>nrf_bootloader.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\libraries\bootloader\nrf_bootloader.c</FilePath>            </File>            <File>
              <FileName>nrf_bootloader_app_start.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\libraries\bootloader\nrf_bootloader_app_start.c</FilePath>            </File>            <File>
              <FileName>nrf_bootloader_app_start_final.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\libraries\bootloader\nrf_bootloader_app_start_final.c</FilePath>            </File>            <File>
              <FileName>nrf_bootloader_dfu_timers.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\libraries\bootloader\nrf_bootloader_dfu_timers.c</FilePath>            </File>            <File>
              <FileName>nrf_bootloader_fw_activation.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\libraries\bootloader\nrf_bootloader_fw_activation.c</FilePath>            </File>            <File>
              <FileName>nrf_bootloader_info.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\libraries\bootloader\nrf_bootloader_info.c</FilePath>            </File>            <File>
              <FileName>nrf_bootloader_wdt.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\libraries\bootloader\nrf_bootloader_wdt.c</FilePath>            </File>          </Files>
        </Group>        <Group>
          <GroupName>nRF_Crypto</GroupName>
          <Files>            <File>
              <FileName>nrf_crypto_ecc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\libraries\crypto\nrf_crypto_ecc.c</FilePath>            </File>            <File>
              <FileName>nrf_crypto_ecdsa.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\libraries\crypto\nrf_crypto_ecdsa.c</FilePath>            </File>            <File>
              <FileName>nrf_crypto_hash.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\libraries\crypto\nrf_crypto_hash.c</FilePath>            </File>            <File>
              <FileName>nrf_crypto_init.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\libraries\crypto\nrf_crypto_init.c</FilePath>            </File>            <File>
              <FileName>nrf_crypto_shared.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\libraries\crypto\nrf_crypto_shared.c</FilePath>            </File>          </Files>
        </Group>        <Group>
          <GroupName>nRF_Crypto backend CC310_BL</GroupName>
          <Files>            <File>
              <FileName>cc310_bl_backend_ecc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\libraries\crypto\backend\cc310_bl\cc310_bl_backend_ecc.c</FilePath>            </File>            <File>
              <FileName>cc310_bl_backend_ecdsa.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\libraries\crypto\backend\cc310_bl\cc310_bl_backend_ecdsa.c</FilePath>            </File>            <File>
              <FileName>cc310_bl_backend_hash.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\libraries\crypto\backend\cc310_bl\cc310_bl_backend_hash.c</FilePath>            </File>            <File>
              <FileName>cc310_bl_backend_init.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\libraries\crypto\backend\cc310_bl\cc310_bl_backend_init.c</FilePath>            </File>            <File>
              <FileName>cc310_bl_backend_shared.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\libraries\crypto\backend\cc310_bl\cc310_bl_backend_shared.c</FilePath>            </File>          </Files>
        </Group>        <Group>
          <GroupName>nRF_Crypto backend Oberon</GroupName>
          <Files>            <File>
              <FileName>oberon_backend_chacha_poly_aead.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\libraries\crypto\backend\oberon\oberon_backend_chacha_poly_aead.c</FilePath>            </File>            <File>
              <FileName>oberon_backend_ecc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\libraries\crypto\backend\oberon\oberon_backend_ecc.c</FilePath>            </File>            <File>
              <FileName>oberon_backend_ecdh.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\libraries\crypto\backend\oberon\oberon_backend_ecdh.c</FilePath>            </File>            <File>
              <FileName>oberon_backend_ecdsa.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\libraries\crypto\backend\oberon\oberon_backend_ecdsa.c</FilePath>            </File>            <File>
              <FileName>oberon_backend_eddsa.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\libraries\crypto\backend\oberon\oberon_backend_eddsa.c</FilePath>            </File>            <File>
              <FileName>oberon_backend_hash.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\libraries\crypto\backend\oberon\oberon_backend_hash.c</FilePath>            </File>            <File>
              <FileName>oberon_backend_hmac.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\libraries\crypto\backend\oberon\oberon_backend_hmac.c</FilePath>            </File>          </Files>
        </Group>        <Group>
          <GroupName>nRF_DFU</GroupName>
          <Files>            <File>
              <FileName>dfu-cc.pb.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\libraries\bootloader\dfu\dfu-cc.pb.c</FilePath>            </File>            <File>
              <FileName>nrf_dfu.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\libraries\bootloader\dfu\nrf_dfu.c</FilePath>            </File>            <File>
              <FileName>nrf_dfu_flash.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\libraries\bootloader\dfu\nrf_dfu_flash.c</FilePath>            </File>            <File>
              <FileName>nrf_dfu_handling_error.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\libraries\bootloader\dfu\nrf_dfu_handling_error.c</FilePath>            </File>            <File>
              <FileName>nrf_dfu_mbr.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\libraries\bootloader\dfu\nrf_dfu_mbr.c</FilePath>            </File>            <File>
              <FileName>nrf_dfu_req_handler.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\libraries\bootloader\dfu\nrf_dfu_req_handler.c</FilePath>            </File>            <File>
              <FileName>nrf_dfu_settings.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\libraries\bootloader\dfu\nrf_dfu_settings.c</FilePath>            </File>            <File>
              <FileName>nrf_dfu_transport.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\libraries\bootloader\dfu\nrf_dfu_transport.c</FilePath>            </File>            <File>
              <FileName>nrf_dfu_utils.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\libraries\bootloader\dfu\nrf_dfu_utils.c</FilePath>            </File>            <File>
              <FileName>nrf_dfu_validation.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\libraries\bootloader\dfu\nrf_dfu_validation.c</FilePath>            </File>            <File>
              <FileName>nrf_dfu_ver_validation.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\libraries\bootloader\dfu\nrf_dfu_ver_validation.c</FilePath>            </File>          </Files>
        </Group>        <Group>
          <GroupName>nRF_Drivers</GroupName>
          <Files>            <File>
              <FileName>nrf_drv_clock.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\integration\nrfx\legacy\nrf_drv_clock.c</FilePath>            </File>            <File>
              <FileName>nrf_drv_power.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\integration\nrfx\legacy\nrf_drv_power.c</FilePath>            </File>            <File>
              <FileName>nrf_drv_uart.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\integration\nrfx\legacy\nrf_drv_uart.c</FilePath>            </File>            <File>
              <FileName>nrf_nvic.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\drivers_nrf\nrf_soc_nosd\nrf_nvic.c</FilePath>            </File>            <File>
              <FileName>nrf_nvmc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\modules\nrfx\hal\nrf_nvmc.c</FilePath>            </File>            <File>
              <FileName>nrf_soc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\drivers_nrf\nrf_soc_nosd\nrf_soc.c</FilePath>            </File>            <File>
              <FileName>nrfx_atomic.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\modules\nrfx\soc\nrfx_atomic.c</FilePath>            </File>            <File>
              <FileName>nrfx_clock.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\modules\nrfx\drivers\src\nrfx_clock.c</FilePath>            </File>            <File>
              <FileName>nrfx_power.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\modules\nrfx\drivers\src\nrfx_power.c</FilePath>            </File>            <File>
              <FileName>nrfx_prs.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\modules\nrfx\drivers\src\prs\nrfx_prs.c</FilePath>            </File>            <File>
              <FileName>nrfx_systick.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\modules\nrfx\drivers\src\nrfx_systick.c</FilePath>            </File>            <File>
              <FileName>nrfx_uart.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\modules\nrfx\drivers\src\nrfx_uart.c</FilePath>            </File>            <File>
              <FileName>nrfx_uarte.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\modules\nrfx\drivers\src\nrfx_uarte.c</FilePath>            </File>            <File>
              <FileName>nrfx_usbd.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\modules\nrfx\drivers\src\nrfx_usbd.c</FilePath>            </File>          </Files>
        </Group>        <Group>
          <GroupName>nRF_Libraries</GroupName>
          <Files>            <File>
              <FileName>app_error_weak.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\libraries\util\app_error_weak.c</FilePath>            </File>            <File>
              <FileName>app_scheduler.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\libraries\scheduler\app_scheduler.c</FilePath>            </File>            <File>
              <FileName>app_timer2.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\libraries\timer\app_timer2.c</FilePath>            </File>            <File>
              <FileName>app_usbd.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\libraries\usbd\app_usbd.c</FilePath>            </File>            <File>
              <FileName>app_usbd_cdc_acm.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\libraries\usbd\class\cdc\acm\app_usbd_cdc_acm.c</FilePath>            </File>            <File>
              <FileName>app_usbd_core.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\libraries\usbd\app_usbd_core.c</FilePath>            </File>            <File>
              <FileName>app_usbd_serial_num.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\libraries\usbd\app_usbd_serial_num.c</FilePath>            </File>            <File>
              <FileName>app_usbd_string_desc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\libraries\usbd\app_usbd_string_desc.c</FilePath>            </File>            <File>
              <FileName>app_util_platform.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\libraries\util\app_util_platform.c</FilePath>            </File>            <File>
              <FileName>crc32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\libraries\crc32\crc32.c</FilePath>            </File>            <File>
              <FileName>drv_rtc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\libraries\timer\drv_rtc.c</FilePath>            </File>            <File>
              <FileName>led_softblink.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\libraries\led_softblink\led_softblink.c</FilePath>            </File>            <File>
              <FileName>low_power_pwm.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\libraries\low_power_pwm\low_power_pwm.c</FilePath>            </File>            <File>
              <FileName>mem_manager.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\libraries\mem_manager\mem_manager.c</FilePath>            </File>            <File>
              <FileName>nrf_assert.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\libraries\util\nrf_assert.c</FilePath>            </File>            <File>
              <FileName>nrf_atfifo.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\libraries\atomic_fifo\nrf_atfifo.c</FilePath>            </File>            <File>
              <FileName>nrf_atomic.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\libraries\atomic\nrf_atomic.c</FilePath>            </File>            <File>
              <FileName>nrf_balloc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\libraries\balloc\nrf_balloc.c</FilePath>            </File>            <File>
              <FileName>nrf_fprintf.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\external\fprintf\nrf_fprintf.c</FilePath>            </File>            <File>
              <FileName>nrf_fprintf_format.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\external\fprintf\nrf_fprintf_format.c</FilePath>            </File>            <File>
              <FileName>nrf_fstorage.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\libraries\fstorage\nrf_fstorage.c</FilePath>            </File>            <File>
              <FileName>nrf_fstorage_nvmc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\libraries\fstorage\nrf_fstorage_nvmc.c</FilePath>            </File>            <File>
              <FileName>nrf_memobj.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\libraries\memobj\nrf_memobj.c</FilePath>            </File>            <File>
              <FileName>nrf_queue.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\libraries\queue\nrf_queue.c</FilePath>            </File>            <File>
              <FileName>nrf_ringbuf.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\libraries\ringbuf\nrf_ringbuf.c</FilePath>            </File>            <File>
              <FileName>nrf_sortlist.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\libraries\sortlist\nrf_sortlist.c</FilePath>            </File>            <File>
              <FileName>nrf_strerror.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\libraries\strerror\nrf_strerror.c</FilePath>            </File>            <File>
              <FileName>slip.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\libraries\slip\slip.c</FilePath>            </File>          </Files>
        </Group>        <Group>
          <GroupName>nRF_Log</GroupName>
          <Files>            <File>
              <FileName>nrf_log_backend_rtt.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\libraries\log\src\nrf_log_backend_rtt.c</FilePath>            </File>            <File>
              <FileName>nrf_log_backend_serial.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\libraries\log\src\nrf_log_backend_serial.c</FilePath>            </File>            <File>
              <FileName>nrf_log_backend_uart.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\libraries\log\src\nrf_log_backend_uart.c</FilePath>            </File>            <File>
              <FileName>nrf_log_default_backends.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\libraries\log\src\nrf_log_default_backends.c</FilePath>            </File>            <File>
              <FileName>nrf_log_frontend.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\libraries\log\src\nrf_log_frontend.c</FilePath>            </File>            <File>
              <FileName>nrf_log_str_formatter.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\libraries\log\src\nrf_log_str_formatter.c</FilePath>            </File>          </Files>
        </Group>        <Group>
          <GroupName>nRF_Oberon_Crypto</GroupName>
          <Files>            <File>
              <FileName>oberon_3.0.5.lib</FileName>
              <FileType>4</FileType>
              <FilePath>..\..\..\..\..\external\nrf_oberon\lib\cortex-m4\hard-float\short-wchar\oberon_3.0.5.lib</FilePath>            </File>          </Files>
        </Group>        <Group>
          <GroupName>nRF_Segger_RTT</GroupName>
          <Files>            <File>
              <FileName>SEGGER_RTT.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\external\segger_rtt\SEGGER_RTT.c</FilePath>            </File>            <File>
              <FileName>SEGGER_RTT_Syscalls_KEIL.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\external\segger_rtt\SEGGER_RTT_Syscalls_KEIL.c</FilePath>            </File>            <File>
              <FileName>SEGGER_RTT_printf.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\external\segger_rtt\SEGGER_RTT_printf.c</FilePath>            </File>          </Files>
        </Group>        <Group>
          <GroupName>nRF_Serial_DFU</GroupName>
          <Files>            <File>
              <FileName>nrf_dfu_serial.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\libraries\bootloader\serial_dfu\nrf_dfu_serial.c</FilePath>            </File>          </Files>
        </Group>        <Group>
          <GroupName>nRF_USB_DFU</GroupName>
          <Files>            <File>
              <FileName>nrf_dfu_serial_usb.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\libraries\bootloader\serial_dfu\nrf_dfu_serial_usb.c</FilePath>            </File>          </Files>
        </Group>        <Group>
          <GroupName>nano-pb</GroupName>
          <Files>            <File>
              <FileName>pb_common.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\external\nano-pb\pb_common.c</FilePath>            </File>            <File>
              <FileName>pb_decode.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\external\nano-pb\pb_decode.c</FilePath>            </File>          </Files>
        </Group>        <Group>
          <GroupName>nrf_cc310_bl</GroupName>
          <Files>            <File>
              <FileName>nrf_cc310_bl_0.9.13.lib</FileName>
              <FileType>4</FileType>
              <FilePath>..\..\..\..\..\external\nrf_cc310_bl\lib\cortex-m4\hard-float\short-wchar\nrf_cc310_bl_0.9.13.lib</FilePath>            </File>          </Files>
        </Group>      </Groups>
    </Target>    <Target>
      <TargetName>flash_mbr</TargetName>
      <ToolsetNumber>0x4</ToolsetNumber>
      <ToolsetName>ARM-ADS</ToolsetName>
      <TargetOption>
        <TargetCommonOption>          <Device>nRF52840_xxAA</Device>
          <Vendor>Nordic Semiconductor</Vendor>
          <PackID>NordicSemiconductor.nRF_DeviceFamilyPack.8.32.1</PackID>
          <PackURL>http://developer.nordicsemi.com/nRF51_SDK/pieces/nRF_DeviceFamilyPack/</PackURL>          <Cpu>IROM(0x00000000,0x100000) IRAM(0x20000000,0x40000) CPUTYPE("Cortex-M4") FPU2 CLOCK(64000000) ELITTLE</Cpu>
          <FlashUtilSpec></FlashUtilSpec>
          <StartupFile></StartupFile>
          <FlashDriverDll></FlashDriverDll>
          <DeviceId>0</DeviceId>
          <RegisterFile>$$Device:nRF52832_xxAA$Device\Include\nrf.h</RegisterFile>
          <MemoryEnv></MemoryEnv>
          <Cmp></Cmp>
          <Asm></Asm>
          <Linker></Linker>
          <OHString></OHString>
          <InfinionOptionDll></InfinionOptionDll>
          <SLE66CMisc></SLE66CMisc>
          <SLE66AMisc></SLE66AMisc>
          <SLE66LinkerMisc></SLE66LinkerMisc>
          <SFDFile>..\..\..\..\..\modules\nrfx\mdk\nrf52840.svd</SFDFile>
          <bCustSvd>0</bCustSvd>
          <UseEnv>0</UseEnv>
          <BinPath></BinPath>
          <IncludePath></IncludePath>
          <LibPath></LibPath>
          <RegisterFilePath></RegisterFilePath>
          <DBRegisterFilePath></DBRegisterFilePath>
          <TargetStatus>
            <Error>0</Error>
            <ExitCodeStop>0</ExitCodeStop>
            <ButtonStop>0</ButtonStop>
            <NotGenerated>0</NotGenerated>
            <InvalidFlash>1</InvalidFlash>
          </TargetStatus>
          <OutputDirectory>..\..\..\..\..\components\softdevice\mbr\hex\</OutputDirectory>
          <OutputName>mbr_nrf52_2.4.1_mbr.hex</OutputName>
          <CreateExecutable>1</CreateExecutable>
          <CreateLib>0</CreateLib>
          <CreateHexFile>1</CreateHexFile>
          <DebugInformation>1</DebugInformation>
          <BrowseInformation>1</BrowseInformation>
          <ListingPath>.\_build\</ListingPath>
          <HexFormatSelection>1</HexFormatSelection>
          <Merge32K>0</Merge32K>
          <CreateBatchFile>0</CreateBatchFile>
          <BeforeCompile>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopU1X>0</nStopU1X>
            <nStopU2X>0</nStopU2X>
          </BeforeCompile>
          <BeforeMake>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopB1X>0</nStopB1X>
            <nStopB2X>0</nStopB2X>
          </BeforeMake>
          <AfterMake>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopA1X>0</nStopA1X>
            <nStopA2X>0</nStopA2X>
          </AfterMake>
          <SelectedForBatchBuild>0</SelectedForBatchBuild>
          <SVCSIdString></SVCSIdString>
        </TargetCommonOption>
        <CommonProperty>
          <UseCPPCompiler>0</UseCPPCompiler>
          <RVCTCodeConst>0</RVCTCodeConst>
          <RVCTZI>0</RVCTZI>
          <RVCTOtherData>0</RVCTOtherData>
          <ModuleSelection>0</ModuleSelection>
          <IncludeInBuild>1</IncludeInBuild>
          <AlwaysBuild>0</AlwaysBuild>
          <GenerateAssemblyFile>0</GenerateAssemblyFile>
          <AssembleAssemblyFile>0</AssembleAssemblyFile>
          <PublicsOnly>0</PublicsOnly>
          <StopOnExitCode>3</StopOnExitCode>
          <CustomArgument></CustomArgument>
          <IncludeLibraryModules></IncludeLibraryModules>
          <ComprImg>1</ComprImg>
        </CommonProperty>
        <DllOption>
          <SimDllName></SimDllName>
          <SimDllArguments></SimDllArguments>
          <SimDlgDll></SimDlgDll>
          <SimDlgDllArguments></SimDlgDllArguments>
          <TargetDllName>SARMCM3.DLL</TargetDllName>
          <TargetDllArguments>-MPU</TargetDllArguments>
          <TargetDlgDll>TCM.DLL</TargetDlgDll>
          <TargetDlgDllArguments>-pCM4</TargetDlgDllArguments>
        </DllOption>
        <DebugOption>
          <OPTHX>
            <HexSelection>1</HexSelection>
            <HexRangeLowAddress>0</HexRangeLowAddress>
            <HexRangeHighAddress>0</HexRangeHighAddress>
            <HexOffset>0</HexOffset>
            <Oh166RecLen>16</Oh166RecLen>
          </OPTHX>
          <Simulator>
            <UseSimulator>0</UseSimulator>
            <LoadApplicationAtStartup>1</LoadApplicationAtStartup>
            <RunToMain>1</RunToMain>
            <RestoreBreakpoints>1</RestoreBreakpoints>
            <RestoreWatchpoints>1</RestoreWatchpoints>
            <RestoreMemoryDisplay>1</RestoreMemoryDisplay>
            <RestoreFunctions>1</RestoreFunctions>
            <RestoreToolbox>1</RestoreToolbox>
            <LimitSpeedToRealTime>0</LimitSpeedToRealTime>
            <RestoreSysVw>1</RestoreSysVw>
          </Simulator>
          <Target>
            <UseTarget>1</UseTarget>
            <LoadApplicationAtStartup>1</LoadApplicationAtStartup>
            <RunToMain>1</RunToMain>
            <RestoreBreakpoints>1</RestoreBreakpoints>
            <RestoreWatchpoints>1</RestoreWatchpoints>
            <RestoreMemoryDisplay>1</RestoreMemoryDisplay>
            <RestoreFunctions>0</RestoreFunctions>
            <RestoreToolbox>1</RestoreToolbox>
            <RestoreTracepoints>0</RestoreTracepoints>
            <RestoreSysVw>1</RestoreSysVw>            <UsePdscDebugDescription>1</UsePdscDebugDescription>          </Target>
          <RunDebugAfterBuild>0</RunDebugAfterBuild>
          <TargetSelection>-1</TargetSelection>
          <SimDlls>
            <CpuDll></CpuDll>
            <CpuDllArguments></CpuDllArguments>
            <PeripheralDll></PeripheralDll>
            <PeripheralDllArguments></PeripheralDllArguments>
            <InitializationFile></InitializationFile>
          </SimDlls>
          <TargetDlls>
            <CpuDll></CpuDll>
            <CpuDllArguments></CpuDllArguments>
            <PeripheralDll></PeripheralDll>
            <PeripheralDllArguments></PeripheralDllArguments>
            <InitializationFile></InitializationFile>
            <Driver>Segger\JL2CM3.dll</Driver>
          </TargetDlls>
        </DebugOption>
        <Utilities>
          <Flash1>
            <UseTargetDll>1</UseTargetDll>
            <UseExternalTool>0</UseExternalTool>
            <RunIndependent>0</RunIndependent>
            <UpdateFlashBeforeDebugging>1</UpdateFlashBeforeDebugging>
            <Capability>1</Capability>
            <DriverSelection>4099</DriverSelection>
          </Flash1>
          <bUseTDR>1</bUseTDR>
          <Flash2>Segger\JL2CM3.dll</Flash2>
          <Flash3></Flash3>
          <Flash4></Flash4>
        </Utilities>
        <TargetArmAds>
          <ArmAdsMisc>
            <GenerateListings>0</GenerateListings>
            <asHll>1</asHll>
            <asAsm>1</asAsm>
            <asMacX>1</asMacX>
            <asSyms>1</asSyms>
            <asFals>1</asFals>
            <asDbgD>1</asDbgD>
            <asForm>1</asForm>
            <ldLst>0</ldLst>
            <ldmm>1</ldmm>
            <ldXref>1</ldXref>
            <BigEnd>0</BigEnd>
            <AdsALst>1</AdsALst>
            <AdsACrf>1</AdsACrf>
            <AdsANop>0</AdsANop>
            <AdsANot>0</AdsANot>
            <AdsLLst>1</AdsLLst>
            <AdsLmap>1</AdsLmap>
            <AdsLcgr>1</AdsLcgr>
            <AdsLsym>1</AdsLsym>
            <AdsLszi>1</AdsLszi>
            <AdsLtoi>1</AdsLtoi>
            <AdsLsun>1</AdsLsun>
            <AdsLven>1</AdsLven>
            <AdsLsxf>1</AdsLsxf>
            <RvctClst>0</RvctClst>
            <GenPPlst>0</GenPPlst>
            <AdsCpuType>"Cortex-M4"</AdsCpuType>
            <RvctDeviceName></RvctDeviceName>
            <mOS>0</mOS>
            <uocRom>0</uocRom>
            <uocRam>0</uocRam>
            <hadIROM>1</hadIROM>
            <hadIRAM>1</hadIRAM>
            <hadXRAM>0</hadXRAM>
            <uocXRam>0</uocXRam>
            <RvdsVP>2</RvdsVP>
            <hadIRAM2>0</hadIRAM2>
            <hadIROM2>0</hadIROM2>
            <StupSel>8</StupSel>
            <useUlib>1</useUlib>
            <EndSel>0</EndSel>
            <uLtcg>0</uLtcg>
            <nSecure>0</nSecure>
            <RoSelD>3</RoSelD>
            <RwSelD>3</RwSelD>
            <CodeSel>0</CodeSel>
            <OptFeed>0</OptFeed>
            <NoZi1>0</NoZi1>
            <NoZi2>0</NoZi2>
            <NoZi3>0</NoZi3>
            <NoZi4>0</NoZi4>
            <NoZi5>0</NoZi5>
            <Ro1Chk>0</Ro1Chk>
            <Ro2Chk>0</Ro2Chk>
            <Ro3Chk>0</Ro3Chk>
            <Ir1Chk>1</Ir1Chk>
            <Ir2Chk>0</Ir2Chk>
            <Ra1Chk>0</Ra1Chk>
            <Ra2Chk>0</Ra2Chk>
            <Ra3Chk>0</Ra3Chk>
            <Im1Chk>1</Im1Chk>
            <Im2Chk>0</Im2Chk>
            <OnChipMemories>
              <Ocm1>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm1>
              <Ocm2>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm2>
              <Ocm3>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm3>
              <Ocm4>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm4>
              <Ocm5>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm5>
              <Ocm6>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm6>
              <IRAM>
                <Type>0</Type>
                <StartAddress>0x20000000</StartAddress>
                <Size>0x40000</Size>
              </IRAM>
              <IROM>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x100000</Size>
              </IROM>
              <XRAM>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </XRAM>
              <OCR_RVCT1>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT1>
              <OCR_RVCT2>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT2>
              <OCR_RVCT3>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT3>
              <OCR_RVCT4>
                <Type>1</Type>
                <StartAddress>0xe0000</StartAddress>
                <Size>0x1e000</Size>
              </OCR_RVCT4>
              <OCR_RVCT5>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT5>
              <OCR_RVCT6>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT6>
              <OCR_RVCT7>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT7>
              <OCR_RVCT8>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT8>
              <OCR_RVCT9>
                <Type>0</Type>
                <StartAddress>0x20000008</StartAddress>
                <Size>0x3fff8</Size>
              </OCR_RVCT9>
              <OCR_RVCT10>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT10>
            </OnChipMemories>
            <RvctStartVector></RvctStartVector>
          </ArmAdsMisc>
          <Cads>
            <interw>1</interw>
            <Optim>4</Optim>
            <oTime>0</oTime>
            <SplitLS>0</SplitLS>
            <OneElfS>1</OneElfS>
            <Strict>0</Strict>
            <EnumInt>0</EnumInt>
            <PlainCh>0</PlainCh>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <wLevel>0</wLevel>
            <uThumb>0</uThumb>
            <uSurpInc>0</uSurpInc>
            <uC99>1</uC99>
            <useXO>0</useXO>
            <v6Lang>0</v6Lang>
            <v6LangP>0</v6LangP>
            <vShortEn>0</vShortEn>
            <vShortWch>0</vShortWch>
            <VariousControls>
              <MiscControls>--reduce_paths</MiscControls>
              <Define> APP_TIMER_V2 APP_TIMER_V2_RTC1_ENABLED BOARD_PCA10056 CONFIG_GPIO_AS_PINRESET DEBUG_NRF FLOAT_ABI_HARD MBR_PRESENT NRF52840_XXAA NRF_DFU_DEBUG_VERSION NRF_DFU_SETTINGS_VERSION=2 SVC_INTERFACE_CALL_AS_NORMAL_FUNCTION __HEAP_SIZE=0</Define>
              <Undefine></Undefine>
              <IncludePath>..\..\config;..\..\..\..\..\components\boards;..\..\..\..\..\components\drivers_nrf\nrf_soc_nosd;..\..\..\..\..\components\libraries\atomic;..\..\..\..\..\components\libraries\atomic_fifo;..\..\..\..\..\components\libraries\balloc;..\..\..\..\..\components\libraries\bootloader;..\..\..\..\..\components\libraries\bootloader\dfu;..\..\..\..\..\components\libraries\bootloader\serial_dfu;..\..\..\..\..\components\libraries\crc32;..\..\..\..\..\components\libraries\crypto;..\..\..\..\..\components\libraries\crypto\backend\cc310;..\..\..\..\..\components\libraries\crypto\backend\cc310_bl;..\..\..\..\..\components\libraries\crypto\backend\cifra;..\..\..\..\..\components\libraries\crypto\backend\mbedtls;..\..\..\..\..\components\libraries\crypto\backend\micro_ecc;..\..\..\..\..\components\libraries\crypto\backend\nrf_hw;..\..\..\..\..\components\libraries\crypto\backend\nrf_sw;..\..\..\..\..\components\libraries\crypto\backend\oberon;..\..\..\..\..\components\libraries\crypto\backend\optiga;..\..\..\..\..\components\libraries\delay;..\..\..\..\..\components\libraries\experimental_section_vars;..\..\..\..\..\components\libraries\fstorage;..\..\..\..\..\components\libraries\led_softblink;..\..\..\..\..\components\libraries\log;..\..\..\..\..\components\libraries\log\src;..\..\..\..\..\components\libraries\low_power_pwm;..\..\..\..\..\components\libraries\mem_manager;..\..\..\..\..\components\libraries\memobj;..\..\..\..\..\components\libraries\mutex;..\..\..\..\..\components\libraries\queue;..\..\..\..\..\components\libraries\ringbuf;..\..\..\..\..\components\libraries\scheduler;..\..\..\..\..\components\libraries\slip;..\..\..\..\..\components\libraries\sortlist;..\..\..\..\..\components\libraries\stack_info;..\..\..\..\..\components\libraries\strerror;..\..\..\..\..\components\libraries\timer;..\..\..\..\..\components\libraries\usbd;..\..\..\..\..\components\libraries\usbd\class\cdc;..\..\..\..\..\components\libraries\usbd\class\cdc\acm;..\..\..\..\..\components\libraries\util;..\..\..\..\..\components\softdevice\mbr\headers;..\..;..\..\..\..\..\external\fprintf;..\..\..\..\..\external\nano-pb;..\..\..\..\..\external\nrf_cc310\include;..\..\..\..\..\external\nrf_cc310_bl\include;..\..\..\..\..\external\nrf_oberon;..\..\..\..\..\external\nrf_oberon\include;..\..\..\..\..\external\segger_rtt;..\..\..\..\..\external\utf_converter;..\..\..\..\..\integration\nrfx;..\..\..\..\..\integration\nrfx\legacy;..\..\..\..\..\modules\nrfx;..\..\..\..\..\modules\nrfx\drivers\include;..\..\..\..\..\modules\nrfx\hal;..\config</IncludePath>
            </VariousControls>
          </Cads>
          <Aads>
            <interw>1</interw>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <thumb>0</thumb>
            <SplitLS>0</SplitLS>
            <SwStkChk>0</SwStkChk>
            <NoWarn>0</NoWarn>
            <uSurpInc>0</uSurpInc>
            <useXO>0</useXO>
            <VariousControls>
              <MiscControls> --cpreproc_opts=-DAPP_TIMER_V2,-DAPP_TIMER_V2_RTC1_ENABLED,-DBOARD_PCA10056,-DCONFIG_GPIO_AS_PINRESET,-DDEBUG_NRF,-DFLOAT_ABI_HARD,-DMBR_PRESENT,-DNRF52840_XXAA,-DNRF_DFU_DEBUG_VERSION,-DNRF_DFU_SETTINGS_VERSION=2,-DSVC_INTERFACE_CALL_AS_NORMAL_FUNCTION,-D__HEAP_SIZE=0</MiscControls>
              <Define> APP_TIMER_V2 APP_TIMER_V2_RTC1_ENABLED BOARD_PCA10056 CONFIG_GPIO_AS_PINRESET DEBUG_NRF FLOAT_ABI_HARD MBR_PRESENT NRF52840_XXAA NRF_DFU_DEBUG_VERSION NRF_DFU_SETTINGS_VERSION=2 SVC_INTERFACE_CALL_AS_NORMAL_FUNCTION __HEAP_SIZE=0</Define>
              <Undefine></Undefine>
              <IncludePath>..\..\config;..\..\..\..\..\components\boards;..\..\..\..\..\components\drivers_nrf\nrf_soc_nosd;..\..\..\..\..\components\libraries\atomic;..\..\..\..\..\components\libraries\atomic_fifo;..\..\..\..\..\components\libraries\balloc;..\..\..\..\..\components\libraries\bootloader;..\..\..\..\..\components\libraries\bootloader\dfu;..\..\..\..\..\components\libraries\bootloader\serial_dfu;..\..\..\..\..\components\libraries\crc32;..\..\..\..\..\components\libraries\crypto;..\..\..\..\..\components\libraries\crypto\backend\cc310;..\..\..\..\..\components\libraries\crypto\backend\cc310_bl;..\..\..\..\..\components\libraries\crypto\backend\cifra;..\..\..\..\..\components\libraries\crypto\backend\mbedtls;..\..\..\..\..\components\libraries\crypto\backend\micro_ecc;..\..\..\..\..\components\libraries\crypto\backend\nrf_hw;..\..\..\..\..\components\libraries\crypto\backend\nrf_sw;..\..\..\..\..\components\libraries\crypto\backend\oberon;..\..\..\..\..\components\libraries\crypto\backend\optiga;..\..\..\..\..\components\libraries\delay;..\..\..\..\..\components\libraries\experimental_section_vars;..\..\..\..\..\components\libraries\fstorage;..\..\..\..\..\components\libraries\led_softblink;..\..\..\..\..\components\libraries\log;..\..\..\..\..\components\libraries\log\src;..\..\..\..\..\components\libraries\low_power_pwm;..\..\..\..\..\components\libraries\mem_manager;..\..\..\..\..\components\libraries\memobj;..\..\..\..\..\components\libraries\mutex;..\..\..\..\..\components\libraries\queue;..\..\..\..\..\components\libraries\ringbuf;..\..\..\..\..\components\libraries\scheduler;..\..\..\..\..\components\libraries\slip;..\..\..\..\..\components\libraries\sortlist;..\..\..\..\..\components\libraries\stack_info;..\..\..\..\..\components\libraries\strerror;..\..\..\..\..\components\libraries\timer;..\..\..\..\..\components\libraries\usbd;..\..\..\..\..\components\libraries\usbd\class\cdc;..\..\..\..\..\components\libraries\usbd\class\cdc\acm;..\..\..\..\..\components\libraries\util;..\..\..\..\..\components\softdevice\mbr\headers;..\..;..\..\..\..\..\external\fprintf;..\..\..\..\..\external\nano-pb;..\..\..\..\..\external\nrf_cc310\include;..\..\..\..\..\external\nrf_cc310_bl\include;..\..\..\..\..\external\nrf_oberon;..\..\..\..\..\external\nrf_oberon\include;..\..\..\..\..\external\segger_rtt;..\..\..\..\..\external\utf_converter;..\..\..\..\..\integration\nrfx;..\..\..\..\..\integration\nrfx\legacy;..\..\..\..\..\modules\nrfx;..\..\..\..\..\modules\nrfx\drivers\include;..\..\..\..\..\modules\nrfx\hal;..\config</IncludePath>
            </VariousControls>
          </Aads>
          <LDads>
            <umfTarg>1</umfTarg>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <noStLib>0</noStLib>
            <RepFail>1</RepFail>
            <useFile>0</useFile>
            <TextAddressRange>0x00000000</TextAddressRange>
            <DataAddressRange>0x20000000</DataAddressRange>
            <pXoBase></pXoBase>
            <ScatterFile></ScatterFile>
            <IncludeLibs></IncludeLibs>
            <IncludeLibsPath></IncludeLibsPath>
            <Misc>--diag_suppress 6330</Misc>
            <LinkerInputFile></LinkerInputFile>
            <DisabledWarnings></DisabledWarnings>
          </LDads>
        </TargetArmAds>
      </TargetOption>
      <Groups>        <Group>
          <GroupName>Application</GroupName>
          <Files>            <File>
              <FileName>dfu_public_key.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\dfu_public_key.c</FilePath>            </File>            <File>
              <FileName>main.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\main.c</FilePath>            </File>            <File>
              <FileName>sdk_config.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\config\sdk_config.h</FilePath>            </File>          </Files>
        </Group>        <Group>
          <GroupName>Board Definition</GroupName>
          <Files>            <File>
              <FileName>boards.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\boards\boards.c</FilePath>            </File>          </Files>
        </Group>        <Group>
          <GroupName>UTF8/UTF16 converter</GroupName>
          <Files>            <File>
              <FileName>utf.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\external\utf_converter\utf.c</FilePath>            </File>          </Files>
        </Group>        <Group>
          <GroupName>nRF_Bootloader</GroupName>
          <Files>            <File>
              <FileName>nrf_bootloader.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\libraries\bootloader\nrf_bootloader.c</FilePath>            </File>            <File>
              <FileName>nrf_bootloader_app_start.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\libraries\bootloader\nrf_bootloader_app_start.c</FilePath>            </File>            <File>
              <FileName>nrf_bootloader_app_start_final.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\libraries\bootloader\nrf_bootloader_app_start_final.c</FilePath>            </File>            <File>
              <FileName>nrf_bootloader_dfu_timers.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\libraries\bootloader\nrf_bootloader_dfu_timers.c</FilePath>            </File>            <File>
              <FileName>nrf_bootloader_fw_activation.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\libraries\bootloader\nrf_bootloader_fw_activation.c</FilePath>            </File>            <File>
              <FileName>nrf_bootloader_info.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\libraries\bootloader\nrf_bootloader_info.c</FilePath>            </File>            <File>
              <FileName>nrf_bootloader_wdt.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\libraries\bootloader\nrf_bootloader_wdt.c</FilePath>            </File>          </Files>
        </Group>        <Group>
          <GroupName>nRF_Crypto</GroupName>
          <Files>            <File>
              <FileName>nrf_crypto_ecc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\libraries\crypto\nrf_crypto_ecc.c</FilePath>            </File>            <File>
              <FileName>nrf_crypto_ecdsa.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\libraries\crypto\nrf_crypto_ecdsa.c</FilePath>            </File>            <File>
              <FileName>nrf_crypto_hash.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\libraries\crypto\nrf_crypto_hash.c</FilePath>            </File>            <File>
              <FileName>nrf_crypto_init.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\libraries\crypto\nrf_crypto_init.c</FilePath>            </File>            <File>
              <FileName>nrf_crypto_shared.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\libraries\crypto\nrf_crypto_shared.c</FilePath>            </File>          </Files>
        </Group>        <Group>
          <GroupName>nRF_Crypto backend CC310_BL</GroupName>
          <Files>            <File>
              <FileName>cc310_bl_backend_ecc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\libraries\crypto\backend\cc310_bl\cc310_bl_backend_ecc.c</FilePath>            </File>            <File>
              <FileName>cc310_bl_backend_ecdsa.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\libraries\crypto\backend\cc310_bl\cc310_bl_backend_ecdsa.c</FilePath>            </File>            <File>
              <FileName>cc310_bl_backend_hash.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\libraries\crypto\backend\cc310_bl\cc310_bl_backend_hash.c</FilePath>            </File>            <File>
              <FileName>cc310_bl_backend_init.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\libraries\crypto\backend\cc310_bl\cc310_bl_backend_init.c</FilePath>            </File>            <File>
              <FileName>cc310_bl_backend_shared.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\libraries\crypto\backend\cc310_bl\cc310_bl_backend_shared.c</FilePath>            </File>          </Files>
        </Group>        <Group>
          <GroupName>nRF_Crypto backend Oberon</GroupName>
          <Files>            <File>
              <FileName>oberon_backend_chacha_poly_aead.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\libraries\crypto\backend\oberon\oberon_backend_chacha_poly_aead.c</FilePath>            </File>            <File>
              <FileName>oberon_backend_ecc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\libraries\crypto\backend\oberon\oberon_backend_ecc.c</FilePath>            </File>            <File>
              <FileName>oberon_backend_ecdh.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\libraries\crypto\backend\oberon\oberon_backend_ecdh.c</FilePath>            </File>            <File>
              <FileName>oberon_backend_ecdsa.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\libraries\crypto\backend\oberon\oberon_backend_ecdsa.c</FilePath>            </File>            <File>
              <FileName>oberon_backend_eddsa.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\libraries\crypto\backend\oberon\oberon_backend_eddsa.c</FilePath>            </File>            <File>
              <FileName>oberon_backend_hash.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\libraries\crypto\backend\oberon\oberon_backend_hash.c</FilePath>            </File>            <File>
              <FileName>oberon_backend_hmac.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\libraries\crypto\backend\oberon\oberon_backend_hmac.c</FilePath>            </File>          </Files>
        </Group>        <Group>
          <GroupName>nRF_DFU</GroupName>
          <Files>            <File>
              <FileName>dfu-cc.pb.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\libraries\bootloader\dfu\dfu-cc.pb.c</FilePath>            </File>            <File>
              <FileName>nrf_dfu.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\libraries\bootloader\dfu\nrf_dfu.c</FilePath>            </File>            <File>
              <FileName>nrf_dfu_flash.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\libraries\bootloader\dfu\nrf_dfu_flash.c</FilePath>            </File>            <File>
              <FileName>nrf_dfu_handling_error.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\libraries\bootloader\dfu\nrf_dfu_handling_error.c</FilePath>            </File>            <File>
              <FileName>nrf_dfu_mbr.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\libraries\bootloader\dfu\nrf_dfu_mbr.c</FilePath>            </File>            <File>
              <FileName>nrf_dfu_req_handler.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\libraries\bootloader\dfu\nrf_dfu_req_handler.c</FilePath>            </File>            <File>
              <FileName>nrf_dfu_settings.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\libraries\bootloader\dfu\nrf_dfu_settings.c</FilePath>            </File>            <File>
              <FileName>nrf_dfu_transport.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\libraries\bootloader\dfu\nrf_dfu_transport.c</FilePath>            </File>            <File>
              <FileName>nrf_dfu_utils.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\libraries\bootloader\dfu\nrf_dfu_utils.c</FilePath>            </File>            <File>
              <FileName>nrf_dfu_validation.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\libraries\bootloader\dfu\nrf_dfu_validation.c</FilePath>            </File>            <File>
              <FileName>nrf_dfu_ver_validation.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\libraries\bootloader\dfu\nrf_dfu_ver_validation.c</FilePath>            </File>          </Files>
        </Group>        <Group>
          <GroupName>nRF_Drivers</GroupName>
          <Files>            <File>
              <FileName>nrf_drv_clock.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\integration\nrfx\legacy\nrf_drv_clock.c</FilePath>            </File>            <File>
              <FileName>nrf_drv_power.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\integration\nrfx\legacy\nrf_drv_power.c</FilePath>            </File>            <File>
              <FileName>nrf_drv_uart.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\integration\nrfx\legacy\nrf_drv_uart.c</FilePath>            </File>            <File>
              <FileName>nrf_nvic.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\drivers_nrf\nrf_soc_nosd\nrf_nvic.c</FilePath>            </File>            <File>
              <FileName>nrf_nvmc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\modules\nrfx\hal\nrf_nvmc.c</FilePath>            </File>            <File>
              <FileName>nrf_soc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\drivers_nrf\nrf_soc_nosd\nrf_soc.c</FilePath>            </File>            <File>
              <FileName>nrfx_atomic.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\modules\nrfx\soc\nrfx_atomic.c</FilePath>            </File>            <File>
              <FileName>nrfx_clock.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\modules\nrfx\drivers\src\nrfx_clock.c</FilePath>            </File>            <File>
              <FileName>nrfx_power.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\modules\nrfx\drivers\src\nrfx_power.c</FilePath>            </File>            <File>
              <FileName>nrfx_prs.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\modules\nrfx\drivers\src\prs\nrfx_prs.c</FilePath>            </File>            <File>
              <FileName>nrfx_systick.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\modules\nrfx\drivers\src\nrfx_systick.c</FilePath>            </File>            <File>
              <FileName>nrfx_uart.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\modules\nrfx\drivers\src\nrfx_uart.c</FilePath>            </File>            <File>
              <FileName>nrfx_uarte.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\modules\nrfx\drivers\src\nrfx_uarte.c</FilePath>            </File>            <File>
              <FileName>nrfx_usbd.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\modules\nrfx\drivers\src\nrfx_usbd.c</FilePath>            </File>          </Files>
        </Group>        <Group>
          <GroupName>nRF_Libraries</GroupName>
          <Files>            <File>
              <FileName>app_error_weak.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\libraries\util\app_error_weak.c</FilePath>            </File>            <File>
              <FileName>app_scheduler.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\libraries\scheduler\app_scheduler.c</FilePath>            </File>            <File>
              <FileName>app_timer2.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\libraries\timer\app_timer2.c</FilePath>            </File>            <File>
              <FileName>app_usbd.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\libraries\usbd\app_usbd.c</FilePath>            </File>            <File>
              <FileName>app_usbd_cdc_acm.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\libraries\usbd\class\cdc\acm\app_usbd_cdc_acm.c</FilePath>            </File>            <File>
              <FileName>app_usbd_core.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\libraries\usbd\app_usbd_core.c</FilePath>            </File>            <File>
              <FileName>app_usbd_serial_num.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\libraries\usbd\app_usbd_serial_num.c</FilePath>            </File>            <File>
              <FileName>app_usbd_string_desc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\libraries\usbd\app_usbd_string_desc.c</FilePath>            </File>            <File>
              <FileName>app_util_platform.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\libraries\util\app_util_platform.c</FilePath>            </File>            <File>
              <FileName>crc32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\libraries\crc32\crc32.c</FilePath>            </File>            <File>
              <FileName>drv_rtc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\libraries\timer\drv_rtc.c</FilePath>            </File>            <File>
              <FileName>led_softblink.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\libraries\led_softblink\led_softblink.c</FilePath>            </File>            <File>
              <FileName>low_power_pwm.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\libraries\low_power_pwm\low_power_pwm.c</FilePath>            </File>            <File>
              <FileName>mem_manager.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\libraries\mem_manager\mem_manager.c</FilePath>            </File>            <File>
              <FileName>nrf_assert.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\libraries\util\nrf_assert.c</FilePath>            </File>            <File>
              <FileName>nrf_atfifo.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\libraries\atomic_fifo\nrf_atfifo.c</FilePath>            </File>            <File>
              <FileName>nrf_atomic.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\libraries\atomic\nrf_atomic.c</FilePath>            </File>            <File>
              <FileName>nrf_balloc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\libraries\balloc\nrf_balloc.c</FilePath>            </File>            <File>
              <FileName>nrf_fprintf.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\external\fprintf\nrf_fprintf.c</FilePath>            </File>            <File>
              <FileName>nrf_fprintf_format.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\external\fprintf\nrf_fprintf_format.c</FilePath>            </File>            <File>
              <FileName>nrf_fstorage.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\libraries\fstorage\nrf_fstorage.c</FilePath>            </File>            <File>
              <FileName>nrf_fstorage_nvmc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\libraries\fstorage\nrf_fstorage_nvmc.c</FilePath>            </File>            <File>
              <FileName>nrf_memobj.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\libraries\memobj\nrf_memobj.c</FilePath>            </File>            <File>
              <FileName>nrf_queue.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\libraries\queue\nrf_queue.c</FilePath>            </File>            <File>
              <FileName>nrf_ringbuf.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\libraries\ringbuf\nrf_ringbuf.c</FilePath>            </File>            <File>
              <FileName>nrf_sortlist.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\libraries\sortlist\nrf_sortlist.c</FilePath>            </File>            <File>
              <FileName>nrf_strerror.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\libraries\strerror\nrf_strerror.c</FilePath>            </File>            <File>
              <FileName>slip.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\libraries\slip\slip.c</FilePath>            </File>          </Files>
        </Group>        <Group>
          <GroupName>nRF_Log</GroupName>
          <Files>            <File>
              <FileName>nrf_log_backend_rtt.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\libraries\log\src\nrf_log_backend_rtt.c</FilePath>            </File>            <File>
              <FileName>nrf_log_backend_serial.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\libraries\log\src\nrf_log_backend_serial.c</FilePath>            </File>            <File>
              <FileName>nrf_log_backend_uart.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\libraries\log\src\nrf_log_backend_uart.c</FilePath>            </File>            <File>
              <FileName>nrf_log_default_backends.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\libraries\log\src\nrf_log_default_backends.c</FilePath>            </File>            <File>
              <FileName>nrf_log_frontend.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\libraries\log\src\nrf_log_frontend.c</FilePath>            </File>            <File>
              <FileName>nrf_log_str_formatter.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\libraries\log\src\nrf_log_str_formatter.c</FilePath>            </File>          </Files>
        </Group>        <Group>
          <GroupName>nRF_Oberon_Crypto</GroupName>
          <Files>            <File>
              <FileName>oberon_3.0.5.lib</FileName>
              <FileType>4</FileType>
              <FilePath>..\..\..\..\..\external\nrf_oberon\lib\cortex-m4\hard-float\short-wchar\oberon_3.0.5.lib</FilePath>            </File>          </Files>
        </Group>        <Group>
          <GroupName>nRF_Segger_RTT</GroupName>
          <Files>            <File>
              <FileName>SEGGER_RTT.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\external\segger_rtt\SEGGER_RTT.c</FilePath>            </File>            <File>
              <FileName>SEGGER_RTT_Syscalls_KEIL.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\external\segger_rtt\SEGGER_RTT_Syscalls_KEIL.c</FilePath>            </File>            <File>
              <FileName>SEGGER_RTT_printf.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\external\segger_rtt\SEGGER_RTT_printf.c</FilePath>            </File>          </Files>
        </Group>        <Group>
          <GroupName>nRF_Serial_DFU</GroupName>
          <Files>            <File>
              <FileName>nrf_dfu_serial.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\libraries\bootloader\serial_dfu\nrf_dfu_serial.c</FilePath>            </File>          </Files>
        </Group>        <Group>
          <GroupName>nRF_USB_DFU</GroupName>
          <Files>            <File>
              <FileName>nrf_dfu_serial_usb.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\components\libraries\bootloader\serial_dfu\nrf_dfu_serial_usb.c</FilePath>            </File>          </Files>
        </Group>        <Group>
          <GroupName>nano-pb</GroupName>
          <Files>            <File>
              <FileName>pb_common.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\external\nano-pb\pb_common.c</FilePath>            </File>            <File>
              <FileName>pb_decode.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\external\nano-pb\pb_decode.c</FilePath>            </File>          </Files>
        </Group>        <Group>
          <GroupName>nrf_cc310_bl</GroupName>
          <Files>            <File>
              <FileName>nrf_cc310_bl_0.9.13.lib</FileName>
              <FileType>4</FileType>
              <FilePath>..\..\..\..\..\external\nrf_cc310_bl\lib\cortex-m4\hard-float\short-wchar\nrf_cc310_bl_0.9.13.lib</FilePath>            </File>          </Files>
        </Group>      </Groups>
    </Target>  </Targets><RTE>
  <packages>
    <filter>
      <targetInfos/>
    </filter>    <package name="CMSIS" url="http://www.keil.com/pack/" vendor="ARM" version="5.6.0">
      <targetInfos>        <targetInfo name="nrf52840_xxaa_debug" versionMatchMode="fixed"/>        <targetInfo excluded="1" name="flash_mbr"/>      </targetInfos>
    </package>
    <package name="nRF_DeviceFamilyPack" url="http://developer.nordicsemi.com/nRF51_SDK/pieces/nRF_DeviceFamilyPack/" vendor="NordicSemiconductor" version="8.32.1">
      <targetInfos>        <targetInfo name="nrf52840_xxaa_debug" versionMatchMode="fixed"/>        <targetInfo excluded="1" name="flash_mbr"/>      </targetInfos>
    </package>  </packages>
  <apis/>
  <components>    <component Cclass="CMSIS" Cgroup="CORE" Cvendor="ARM" Cversion="5.3.0" condition="CMSIS Core">
      <package name="CMSIS" url="http://www.keil.com/pack/" vendor="ARM" version="5.6.0"/>
      <targetInfos>        <targetInfo name="nrf52840_xxaa_debug" versionMatchMode="fixed"/>        <targetInfo excluded="1" name="flash_mbr"/>      </targetInfos>
    </component>
    <component Cclass="Device" Cgroup="Startup" Cvendor="NordicSemiconductor" Cversion="8.32.1" condition="nRF5x Series CMSIS Device">
      <package name="nRF_DeviceFamilyPack" url="http://developer.nordicsemi.com/nRF51_SDK/pieces/nRF_DeviceFamilyPack/" vendor="NordicSemiconductor" version="8.32.1"/>
      <targetInfos>        <targetInfo name="nrf52840_xxaa_debug"/>        <targetInfo excluded="1" name="flash_mbr"/>      </targetInfos>
    </component>  </components>
  <files>  </files>
</RTE>
</Project>
