# 嵌入式C开发最佳实践

## 代码组织和架构

### 1. 分层架构设计
```
应用层 (Application)
    ↓
中间件层 (Middleware)
    ↓
硬件抽象层 (HAL)
    ↓
底层驱动 (Low-level Drivers)
    ↓
硬件层 (Hardware)
```

**原则:**
- 上层不直接访问下层的具体实现
- 每层都有明确的职责和接口
- 便于移植和测试

### 2. 模块化设计
- 每个模块有独立的.h和.c文件
- 模块间通过定义良好的接口通信
- 避免循环依赖
- 使用前向声明减少头文件依赖

### 3. 文件组织结构
```
project/
├── src/
│   ├── app/          # 应用层代码
│   ├── middleware/   # 中间件
│   ├── hal/          # 硬件抽象层
│   └── drivers/      # 底层驱动
├── inc/              # 头文件
├── config/           # 配置文件
├── docs/             # 文档
└── tests/            # 测试代码
```

## 内存管理

### 1. 栈内存管理
- 避免深度递归调用
- 限制局部变量大小
- 使用静态分析工具检查栈使用
- 为中断预留足够的栈空间

### 2. 堆内存管理
- 嵌入式系统中尽量避免动态内存分配
- 如必须使用，考虑内存池技术
- 防止内存泄漏和碎片化
- 使用静态内存分配替代动态分配

### 3. 全局变量管理
```c
// 好的做法：使用static限制作用域
static uint32_t module_counter = 0;

// 提供访问接口
uint32_t get_module_counter(void) {
    return module_counter;
}

void increment_module_counter(void) {
    module_counter++;
}
```

### 4. 常量和配置
```c
// 使用const关键字
const uint32_t MAX_BUFFER_SIZE = 1024;

// 使用枚举替代魔数
typedef enum {
    STATE_IDLE = 0,
    STATE_RUNNING = 1,
    STATE_ERROR = 2
} system_state_t;
```

## 实时性和性能优化

### 1. 中断处理
```c
// 中断服务程序应该简短快速
void TIMER_IRQHandler(void) {
    // 清除中断标志
    TIMER->INTFLAG = TIMER_INTFLAG_OVF;
    
    // 设置标志，主循环中处理
    timer_overflow_flag = true;
    
    // 避免在ISR中进行复杂操作
}

// 主循环中处理
void main_loop(void) {
    if (timer_overflow_flag) {
        timer_overflow_flag = false;
        handle_timer_overflow();
    }
}
```

### 2. 临界区保护
```c
// 使用原子操作或中断禁用保护临界区
void update_shared_variable(uint32_t new_value) {
    __disable_irq();
    shared_variable = new_value;
    __enable_irq();
}

// 或使用原子操作（如果硬件支持）
#include <stdatomic.h>
atomic_uint32_t atomic_counter = ATOMIC_VAR_INIT(0);
```

### 3. 循环优化
```c
// 避免在循环中进行重复计算
// 不好的做法
for (int i = 0; i < get_array_size(); i++) {
    process_element(array[i]);
}

// 好的做法
int size = get_array_size();
for (int i = 0; i < size; i++) {
    process_element(array[i]);
}
```

## 错误处理和调试

### 1. 错误码设计
```c
typedef enum {
    ERR_OK = 0,
    ERR_INVALID_PARAM = -1,
    ERR_TIMEOUT = -2,
    ERR_BUSY = -3,
    ERR_NO_MEMORY = -4,
    ERR_HARDWARE_FAULT = -5
} error_code_t;

// 函数返回错误码
error_code_t initialize_peripheral(peripheral_config_t *config) {
    if (config == NULL) {
        return ERR_INVALID_PARAM;
    }
    
    // 初始化逻辑
    if (hardware_init_failed()) {
        return ERR_HARDWARE_FAULT;
    }
    
    return ERR_OK;
}
```

### 2. 断言使用
```c
#include <assert.h>

// 在调试版本中启用断言
#ifdef DEBUG
    #define ASSERT(condition) assert(condition)
#else
    #define ASSERT(condition) ((void)0)
#endif

void process_buffer(uint8_t *buffer, size_t size) {
    ASSERT(buffer != NULL);
    ASSERT(size > 0);
    
    // 处理逻辑
}
```

### 3. 调试输出
```c
// 分级调试输出
typedef enum {
    LOG_ERROR = 0,
    LOG_WARNING = 1,
    LOG_INFO = 2,
    LOG_DEBUG = 3
} log_level_t;

#ifdef DEBUG
    #define LOG(level, format, ...) \
        debug_printf("[%s] " format "\r\n", \
                    log_level_strings[level], ##__VA_ARGS__)
#else
    #define LOG(level, format, ...) ((void)0)
#endif
```

## 功耗优化

### 1. 时钟管理
```c
// 动态时钟管理
void enter_low_power_mode(void) {
    // 关闭不必要的外设时钟
    disable_unused_peripheral_clocks();
    
    // 降低CPU频率
    set_cpu_frequency(LOW_POWER_FREQ);
    
    // 进入睡眠模式
    enter_sleep_mode();
}

void exit_low_power_mode(void) {
    // 恢复CPU频率
    set_cpu_frequency(NORMAL_FREQ);
    
    // 重新启用外设时钟
    enable_required_peripheral_clocks();
}
```

### 2. 外设管理
```c
// 按需启用外设
typedef struct {
    bool spi_enabled;
    bool i2c_enabled;
    bool uart_enabled;
    bool adc_enabled;
} peripheral_state_t;

void manage_peripheral_power(peripheral_state_t *state) {
    if (!state->spi_enabled) {
        disable_spi_power();
    }
    if (!state->i2c_enabled) {
        disable_i2c_power();
    }
    // ... 其他外设
}
```

## 代码质量和可维护性

### 1. 命名规范
```c
// 变量命名：小写字母+下划线
uint32_t sensor_data_buffer[MAX_SAMPLES];
bool is_system_ready;

// 函数命名：动词+名词
void init_uart_peripheral(void);
bool read_sensor_data(sensor_data_t *data);

// 常量命名：大写字母+下划线
#define MAX_RETRY_COUNT 3
#define UART_BAUDRATE_115200 115200

// 类型命名：小写字母+下划线+_t后缀
typedef struct {
    uint16_t temperature;
    uint16_t humidity;
} sensor_reading_t;
```

### 2. 注释规范
```c
/**
 * @brief 初始化传感器模块
 * @param config 传感器配置参数
 * @return 初始化结果
 * @retval ERR_OK 初始化成功
 * @retval ERR_INVALID_PARAM 参数无效
 * @retval ERR_HARDWARE_FAULT 硬件故障
 */
error_code_t sensor_init(const sensor_config_t *config);

// 复杂算法需要详细注释
void calculate_fft(complex_t *input, complex_t *output, size_t size) {
    // 使用Cooley-Tukey算法实现FFT
    // 输入数据长度必须是2的幂次
    
    // 第一步：位反转排序
    bit_reverse_copy(input, output, size);
    
    // 第二步：蝶形运算
    for (size_t step = 2; step <= size; step *= 2) {
        // ... 蝶形运算实现
    }
}
```

### 3. 代码复用
```c
// 使用宏定义减少重复代码
#define DEFINE_PERIPHERAL_INIT(name, reg_base) \
    error_code_t name##_init(const name##_config_t *config) { \
        if (config == NULL) return ERR_INVALID_PARAM; \
        reg_base->CTRL = config->control_value; \
        reg_base->CFG = config->config_value; \
        return ERR_OK; \
    }

// 使用函数指针实现多态
typedef struct {
    error_code_t (*init)(void *config);
    error_code_t (*read)(void *buffer, size_t size);
    error_code_t (*write)(const void *buffer, size_t size);
} device_interface_t;
```

## 安全性考虑

### 1. 缓冲区溢出防护
```c
// 使用安全的字符串操作函数
#include <string.h>

void safe_string_copy(char *dest, const char *src, size_t dest_size) {
    if (dest == NULL || src == NULL || dest_size == 0) {
        return;
    }
    
    strncpy(dest, src, dest_size - 1);
    dest[dest_size - 1] = '\0';  // 确保字符串终止
}

// 数组边界检查
bool safe_array_access(uint8_t *array, size_t array_size, size_t index) {
    if (array == NULL || index >= array_size) {
        return false;
    }
    return true;
}
```

### 2. 输入验证
```c
// 严格的参数验证
error_code_t set_pwm_duty_cycle(uint8_t channel, uint16_t duty_cycle) {
    // 检查通道有效性
    if (channel >= MAX_PWM_CHANNELS) {
        return ERR_INVALID_PARAM;
    }
    
    // 检查占空比范围
    if (duty_cycle > PWM_MAX_DUTY_CYCLE) {
        return ERR_INVALID_PARAM;
    }
    
    // 设置PWM占空比
    pwm_registers[channel].duty = duty_cycle;
    return ERR_OK;
}
```

## 测试和验证

### 1. 单元测试
```c
// 使用简单的测试框架
#define TEST_ASSERT(condition) \
    do { \
        if (!(condition)) { \
            printf("Test failed at line %d: %s\n", __LINE__, #condition); \
            return false; \
        } \
    } while(0)

bool test_sensor_init(void) {
    sensor_config_t config = {0};
    
    // 测试空指针
    TEST_ASSERT(sensor_init(NULL) == ERR_INVALID_PARAM);
    
    // 测试正常初始化
    config.sample_rate = 1000;
    config.resolution = 12;
    TEST_ASSERT(sensor_init(&config) == ERR_OK);
    
    return true;
}
```

### 2. 静态分析
- 使用PC-lint、Cppcheck等工具
- 启用编译器的所有警告
- 定期进行代码审查

### 3. 运行时检查
```c
// 栈使用监控
void check_stack_usage(void) {
    extern uint32_t _stack_start;
    extern uint32_t _stack_end;
    
    uint32_t stack_pointer;
    __asm volatile ("mov %0, sp" : "=r" (stack_pointer));
    
    uint32_t stack_used = &_stack_end - stack_pointer;
    uint32_t stack_total = &_stack_end - &_stack_start;
    
    if (stack_used > (stack_total * 0.8)) {
        LOG(LOG_WARNING, "Stack usage high: %d/%d bytes", 
            stack_used, stack_total);
    }
}
```

这些最佳实践应该根据具体项目需求和约束条件进行调整和应用。
