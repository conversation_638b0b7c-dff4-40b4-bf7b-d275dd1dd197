<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html lang="ja">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta http-equiv="Content-Style-Type" content="text/css">
<link rel="up" title="FatFs" href="../00index_j.html">
<link rel="alternate" hreflang="en" title="English" href="../en/stat.html">
<link rel="stylesheet" href="../css_j.css" type="text/css" media="screen" title="ELM Default">
<title>FatFs - f_stat</title>
</head>

<body>

<div class="para func">
<h2>f_stat</h2>
<p>ファイルまたはサブ ディレクトリの存在を調べ、またその情報を取得します。</p>
<pre>
FRESULT f_stat (
  const TCHAR* <span class="arg">path</span>,  <span class="c">/* [IN] オブジェクト名へのポインタ */</span>
  FILINFO* <span class="arg">fno</span>        <span class="c">/* [OUT] ファイル情報構造体へのポインタ */</span>
);
</pre>
</div>

<div class="para arg">
<h4>引数</h4>
<dl class="par">
<dt>path</dt>
<dd>チェックするオブジェクト名を示すヌル文字<tt>'\0'</tt>終端の文字列を指すポインタを指定します。</dd>
<dt>fno</dt>
<dd>オブジェクトが存在したときにその情報を格納するファイル情報構造体へのポインタを指定します。この情報が不要なときはヌル ポインタを指定してください。</dd>
</dl>
</div>


<div class="para ret">
<h4>戻り値</h4>
<p>
<a href="rc.html#ok">FR_OK</a>,
<a href="rc.html#de">FR_DISK_ERR</a>,
<a href="rc.html#ie">FR_INT_ERR</a>,
<a href="rc.html#nr">FR_NOT_READY</a>,
<a href="rc.html#ok">FR_NO_FILE</a>,
<a href="rc.html#np">FR_NO_PATH</a>,
<a href="rc.html#in">FR_INVALID_NAME</a>,
<a href="rc.html#id">FR_INVALID_DRIVE</a>,
<a href="rc.html#ne">FR_NOT_ENABLED</a>,
<a href="rc.html#ns">FR_NO_FILESYSTEM</a>,
<a href="rc.html#tm">FR_TIMEOUT</a>,
<a href="rc.html#nc">FR_NOT_ENOUGH_CORE</a>
</p>
</div>


<div class="para desc">
<h4>解説</h4>
<p>指定されたファイルまたはサブ ディレクトリの存在を調べます。存在しない場合は、<tt>FR_NO_FILE</tt>が帰ります。存在する場合は<tt>FR_OK</tt>が帰り、それ関する情報(サイズ、タイムスタンプおよび属性)がファイル情報構造体にストアされます。</p>
</div>


<div class="para comp">
<h4>対応情報</h4>
<p><tt>_FS_MINIMIZE == 0</tt>のときに使用可能です。</p>
</div>


<div class="para use">
<h4>使用例</h4>
<pre>
    FRESULT fr;
    FILINFO fno;


    printf("Test for 'file.txt'...\n");

    fr = f_stat("file.txt", &amp;fno);
    switch (fr) {

    case FR_OK:
        printf("Size: %lu\n", fno.fsize);
        printf("Timestamp: %u/%02u/%02u, %02u:%02u\n",
               (fno.fdate &gt;&gt; 9) + 1980, fno.fdate &gt;&gt; 5 &amp; 15, fno.fdate &amp; 31,
               fno.ftime &gt;&gt; 11, fno.ftime &gt;&gt; 5 &amp; 63);
        printf("Attributes: %c%c%c%c%c\n",
               (fno.fattrib &amp; AM_DIR) ? 'D' : '-',
               (fno.fattrib &amp; AM_RDO) ? 'R' : '-',
               (fno.fattrib &amp; AM_HID) ? 'H' : '-',
               (fno.fattrib &amp; AM_SYS) ? 'S' : '-',
               (fno.fattrib &amp; AM_ARC) ? 'A' : '-');
        break;

    case FR_NO_FILE:
        printf("It is not exist.\n");
        break;

    default:
        printf("An error occured. (%d)\n", fr);
    }
</pre>
</div>


<div class="para ref">
<h4>参照</h4>
<p><tt><a href="opendir.html">f_opendir</a>, <a href="readdir.html">f_readdir</a>, <a href="sfileinfo.html">FILINFO</a>, <a href="sdir.html">DIR</a></tt></p>
</div>

<p class="foot"><a href="../00index_j.html">戻る</a></p>
</body>
</html>
