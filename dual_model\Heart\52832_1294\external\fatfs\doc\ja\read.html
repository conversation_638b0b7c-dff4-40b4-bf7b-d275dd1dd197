<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html lang="ja">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta http-equiv="Content-Style-Type" content="text/css">
<link rel="up" title="FatFs" href="../00index_j.html">
<link rel="alternate" hreflang="en" title="English" href="../en/read.html">
<link rel="stylesheet" href="../css_j.css" type="text/css" media="screen" title="ELM Default">
<title>FatFs - f_read</title>
</head>

<body>

<div class="para func">
<h2>f_read</h2>
<p>ファイルからデータを読み出します。</p>
<pre>
FRESULT f_read (
  FIL* <span class="arg">fp</span>,     <span class="c">/* [IN] ファイル オブジェクト構造体 */</span>
  void* <span class="arg">buff</span>,  <span class="c">/* [OUT] 読み出したデータを格納するバッファ */</span>
  UINT <span class="arg">btr</span>,    <span class="c">/* [IN] 読み出すバイト数 */</span>
  UINT* <span class="arg">br</span>     <span class="c">/* [OUT] 読み出されたバイト数 */</span>
);
</pre>
</div>

<div class="para arg">
<h4>引数</h4>
<dl class="par">
<dt>fp</dt>
<dd>ファイル オブジェクト構造体へのポインタを指定します。</dd>
<dt>buff</dt>
<dd>読み出したデータを格納するバッファを指すポインタを指定します。</dd>
<dt>btr</dt>
<dd>読み出すバイト数(0～<tt>UINT</tt>型の最大値)を指定します。</dd>
<dt>br</dt>
<dd>実際に読み出されたバイト数を格納する変数を指すポインタを指定します。戻り値は関数の成否にかかわらず常に有効です。</dd>
</dl>
</div>


<div class="para ret">
<h4>戻り値</h4>
<p>
<a href="rc.html#ok">FR_OK</a>,
<a href="rc.html#de">FR_DISK_ERR</a>,
<a href="rc.html#ie">FR_INT_ERR</a>,
<a href="rc.html#de">FR_DENIED</a>,
<a href="rc.html#io">FR_INVALID_OBJECT</a>,
<a href="rc.html#tm">FR_TIMEOUT</a>
</p>
</div>


<div class="para desc">
<h4>解説</h4>
<p>読み込み開始位置は、現在のリード/ライト ポインタからになります。リード/ライト ポインタは読み込まれたバイト数だけ進みます。関数が正常終了した後は、<tt class="arg">*br</tt>の値をチェックすべきです。<tt class="arg">*br</tt>が<tt class="arg">btr</tt>よりも小さいときは、読み込み中にファイルの終端に達したことを示しています。</p>
</div>


<div class="para comp">
<h4>対応情報</h4>
<p>全ての構成で使用可能です。</p>
</div>


<div class="para ref">
<h4>参照</h4>
<p><tt><a href="open.html">f_open</a>, <a href="gets.html">fgets</a>, <a href="write.html">f_write</a>, <a href="close.html">f_close</a>, <a href="sfile.html">FIL</a></tt></p>
</div>

<p class="foot"><a href="../00index_j.html">戻る</a></p>
</body>
</html>
