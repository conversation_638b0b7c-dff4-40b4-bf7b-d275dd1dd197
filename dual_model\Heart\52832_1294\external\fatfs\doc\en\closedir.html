<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html lang="en">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<meta http-equiv="Content-Style-Type" content="text/css">
<link rel="up" title="FatFs" href="../00index_e.html">
<link rel="alternate" hreflang="ja" title="Japanese" href="../ja/close.html">
<link rel="stylesheet" href="../css_e.css" type="text/css" media="screen" title="ELM Default">
<title>FatFs - f_closedir</title>
</head>

<body>

<div class="para func">
<h2>f_closedir</h2>
<p>The f_closedir function closes an open directory.</p>
<pre>
FRESULT f_closedir (
  DIR* <span class="arg">dp</span>     <span class="c">/* [IN] Pointer to the directory object */</span>
);
</pre>
</div>

<div class="para arg">
<h4>Parameter</h4>
<dl class="par">
<dt>dp</dt>
<dd>Pointer to the open directory object structure to be closed.</dd>
</dl>
</div>


<div class="para ret">
<h4>Return Values</h4>
<p>
<a href="rc.html#ok">FR_OK</a>,
<a href="rc.html#ie">FR_INT_ERR</a>,
<a href="rc.html#io">FR_INVALID_OBJECT</a>,
<a href="rc.html#tm">FR_TIMEOUT</a>
</p>
</div>


<div class="para desc">
<h4>Description</h4>
<p>The <tt>f_closedir</tt> function closes an open directory object. After the function succeeded, the directory object is no longer valid and it can be discarded.</p>
<p>Note that the directory object can also be discarded without this process when option <tt>_FS_LOCK</tt> is not enabled. However this is not recommended for future compatibility.</p>
</div>


<div class="para comp">
<h4>QuickInfo</h4>
<p>Available when <tt>_FS_MINIMIZE &lt;= 1</tt>.</p>
</div>


<div class="para ref">
<h4>See Also</h4>
<p><tt><a href="opendir.html">f_opendir</a>, <a href="readdir.html">f_readdir</a>, <a href="sdir.html">DIR</a></tt></p>
</div>

<p class="foot"><a href="../00index_e.html">Return</a></p>
</body>
</html>
