T8C20 000:008.954   SEGGER J-Link V7.22b Log File
T8C20 000:009.322   DLL Compiled: Jun 17 2021 17:22:49
T8C20 000:009.334   Logging started @ 2025-07-29 12:10
T8C20 000:009.344 - 9.349ms
T8C20 000:009.360 JLINK_SetWarnOutHandler(...)
T8C20 000:009.372 - 0.017ms
T8C20 000:009.383 JLINK_OpenEx(...)
T8C20 000:014.628   Firmware: J-Link OB-STM32F072-CortexM compiled Jan  7 2019 14:09:37
T8C20 000:015.394   Firmware: J-Link OB-STM32F072-CortexM compiled Jan  7 2019 14:09:37
T8C20 000:015.561   Decompressing FW timestamp took 114 us
T8C20 000:023.037   Hardware: V1.00
T8C20 000:023.065   S/N: 20221211
T8C20 000:023.090   OEM: SEGGER
T8C20 000:023.106   Feature(s): GDB, R<PERSON>, <PERSON>B<PERSON>, FlashD<PERSON>, J<PERSON><PERSON>, RDDI
T8C20 000:025.418   TELNET listener socket opened on port 19021
T8C20 000:025.703   WEBSRV Starting webserver
T8C20 000:025.897   WEBSRV Webserver running on local port 19080
T8C20 000:025.917 - 16.539ms returns "O.K."
T8C20 000:025.938 JLINK_GetEmuCaps()
T8C20 000:025.950 - 0.017ms returns 0xB8EA5A33
T8C20 000:025.966 JLINK_TIF_GetAvailable(...)
T8C20 000:026.359 - 0.410ms
T8C20 000:026.389 JLINK_SetErrorOutHandler(...)
T8C20 000:026.401 - 0.017ms
T8C20 000:026.434 JLINK_ExecCommand("ProjectFile = "D:\PhD\Code\C\work\7.10\52832_1294\examples\ble_peripheral\ble_app_uart\pca10040\s132\arm5_no_packs\JLinkSettings.ini"", ...). 
T8C20 000:036.472 - 10.053ms returns 0x00
T8C20 000:055.495 JLINK_ExecCommand("Device = nRF52832_xxAA", ...). 
T8C20 000:069.896   Device "NRF52832_XXAA" selected.
T8C20 000:070.836 - 15.319ms returns 0x00
T8C20 000:070.882 JLINK_GetHardwareVersion()
T8C20 000:070.895 - 0.018ms returns 10000
T8C20 000:070.907 JLINK_GetDLLVersion()
T8C20 000:070.918 - 0.016ms returns 72202
T8C20 000:070.930 JLINK_GetOEMString(...)
T8C20 000:070.943 JLINK_GetFirmwareString(...)
T8C20 000:070.956 - 0.018ms
T8C20 000:081.715 JLINK_GetDLLVersion()
T8C20 000:081.753 - 0.043ms returns 72202
T8C20 000:081.766 JLINK_GetCompileDateTime()
T8C20 000:081.777 - 0.016ms
T8C20 000:084.710 JLINK_GetFirmwareString(...)
T8C20 000:084.740 - 0.035ms
T8C20 000:087.742 JLINK_GetHardwareVersion()
T8C20 000:087.769 - 0.033ms returns 10000
T8C20 000:092.604 JLINK_GetSN()
T8C20 000:092.633 - 0.035ms returns 20221211
T8C20 000:095.589 JLINK_GetOEMString(...)
T8C20 000:101.617 JLINK_TIF_Select(JLINKARM_TIF_SWD)
T8C20 000:103.152 - 1.552ms returns 0x00
T8C20 000:103.184 JLINK_HasError()
T8C20 000:103.215 JLINK_SetSpeed(5000)
T8C20 000:103.526 - 0.324ms
T8C20 000:103.554 JLINK_GetId()
T8C20 000:108.956   InitTarget() start
T8C20 000:109.004    J-Link Script File: Executing InitTarget()
T8C20 000:117.272   InitTarget() end
T8C20 000:121.791   Found SW-DP with ID 0x2BA01477
T8C20 000:124.754   Old FW that does not support reading DPIDR via DAP jobs
T8C20 000:129.645   DPv0 detected
T8C20 000:132.718   Scanning AP map to find all available APs
T8C20 000:139.025   AP[2]: Stopped AP scan as end of AP map has been reached
T8C20 000:141.937   AP[0]: AHB-AP (IDR: 0x24770011)
T8C20 000:145.728   AP[1]: JTAG-AP (IDR: 0x02880000)
T8C20 000:148.884   Iterating through AP map to find AHB-AP to use
T8C20 000:155.605   AP[0]: Core found
T8C20 000:159.012   AP[0]: AHB-AP ROM base: 0xE00FF000
T8C20 000:163.048   CPUID register: 0x410FC241. Implementer code: 0x41 (ARM)
T8C20 000:167.745   Found Cortex-M4 r0p1, Little endian.
T8C20 000:269.733   -- Max. mem block: 0x00002340
T8C20 000:270.056   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T8C20 000:270.818   CPU_WriteMem(4 bytes @ 0xE000EDF0)
T8C20 000:271.534   CPU_ReadMem(4 bytes @ 0xE0002000)
T8C20 000:280.375   FPUnit: 6 code (BP) slots and 2 literal slots
T8C20 000:280.420   CPU_ReadMem(4 bytes @ 0xE000EDFC)
T8C20 000:281.177   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T8C20 000:281.951   CPU_ReadMem(4 bytes @ 0xE0001000)
T8C20 000:282.633   CPU_WriteMem(4 bytes @ 0xE0001000)
T8C20 000:283.307   CPU_ReadMem(4 bytes @ 0xE000ED88)
T8C20 000:284.017   CPU_WriteMem(4 bytes @ 0xE000ED88)
T8C20 000:284.678   CPU_ReadMem(4 bytes @ 0xE000ED88)
T8C20 000:285.414   CPU_WriteMem(4 bytes @ 0xE000ED88)
T8C20 000:291.020   CoreSight components:
T8C20 000:295.287   ROMTbl[0] @ E00FF000
T8C20 000:295.343   CPU_ReadMem(64 bytes @ 0xE00FF000)
T8C20 000:296.641   CPU_ReadMem(32 bytes @ 0xE000EFE0)
T8C20 000:301.508   ROMTbl[0][0]: E000E000, CID: B105E00D, PID: 000BB00C SCS-M7
T8C20 000:301.560   CPU_ReadMem(32 bytes @ 0xE0001FE0)
T8C20 000:307.087   ROMTbl[0][1]: E0001000, CID: B105E00D, PID: 003BB002 DWT
T8C20 000:307.137   CPU_ReadMem(32 bytes @ 0xE0002FE0)
T8C20 000:311.731   ROMTbl[0][2]: E0002000, CID: B105E00D, PID: 002BB003 FPB
T8C20 000:311.784   CPU_ReadMem(32 bytes @ 0xE0000FE0)
T8C20 000:316.095   ROMTbl[0][3]: ********, CID: B105E00D, PID: 003BB001 ITM
T8C20 000:316.145   CPU_ReadMem(32 bytes @ 0xE0040FE0)
T8C20 000:325.409   ROMTbl[0][4]: ********, CID: B105900D, PID: 000BB9A1 TPIU
T8C20 000:325.464   CPU_ReadMem(32 bytes @ 0xE0041FE0)
T8C20 000:332.731   ROMTbl[0][5]: ********, CID: B105900D, PID: 000BB925 ETM
T8C20 000:333.324 - 229.785ms returns 0x2BA01477
T8C20 000:333.358 JLINK_GetDLLVersion()
T8C20 000:333.370 - 0.017ms returns 72202
T8C20 000:333.384 JLINK_CORE_GetFound()
T8C20 000:333.395 - 0.017ms returns 0xE0000FF
T8C20 000:333.409 JLINK_GetDebugInfo(0x100 = JLINKARM_ROM_TABLE_ADDR_INDEX)
T8C20 000:333.422   Value=0xE00FF000
T8C20 000:333.439 - 0.034ms returns 0
T8C20 000:340.097 JLINK_GetDebugInfo(0x100 = JLINKARM_ROM_TABLE_ADDR_INDEX)
T8C20 000:340.138   Value=0xE00FF000
T8C20 000:340.155 - 0.063ms returns 0
T8C20 000:340.168 JLINK_GetDebugInfo(0x101 = JLINKARM_DEBUG_INFO_ETM_ADDR_INDEX)
T8C20 000:340.179   Value=0x********
T8C20 000:340.196 - 0.033ms returns 0
T8C20 000:340.225 JLINK_ReadMemEx(0xE0041FD0, 0x20 Bytes, Flags = 0x02000004)
T8C20 000:340.273   CPU_ReadMem(32 bytes @ 0xE0041FD0)
T8C20 000:341.367   Data:  04 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 ...
T8C20 000:341.408 - 1.189ms returns 32 (0x20)
T8C20 000:341.424 JLINK_GetDebugInfo(0x102 = JLINKARM_DEBUG_INFO_MTB_ADDR_INDEX)
T8C20 000:341.436   Value=0x00000000
T8C20 000:341.452 - 0.033ms returns 0
T8C20 000:341.464 JLINK_GetDebugInfo(0x103 = JLINKARM_DEBUG_INFO_TPIU_ADDR_INDEX)
T8C20 000:341.475   Value=0x********
T8C20 000:341.492 - 0.033ms returns 0
T8C20 000:341.503 JLINK_GetDebugInfo(0x104 = JLINKARM_DEBUG_INFO_ITM_ADDR_INDEX)
T8C20 000:341.514   Value=0x********
T8C20 000:341.530 - 0.032ms returns 0
T8C20 000:341.541 JLINK_GetDebugInfo(0x105 = JLINKARM_DEBUG_INFO_DWT_ADDR_INDEX)
T8C20 000:341.552   Value=0xE0001000
T8C20 000:341.568 - 0.032ms returns 0
T8C20 000:341.580 JLINK_GetDebugInfo(0x106 = JLINKARM_DEBUG_INFO_FPB_ADDR_INDEX)
T8C20 000:341.591   Value=0xE0002000
T8C20 000:341.607 - 0.032ms returns 0
T8C20 000:341.618 JLINK_GetDebugInfo(0x107 = JLINKARM_DEBUG_INFO_NVIC_ADDR_INDEX)
T8C20 000:341.629   Value=0xE000E000
T8C20 000:341.645 - 0.032ms returns 0
T8C20 000:341.657 JLINK_GetDebugInfo(0x10C = JLINKARM_DEBUG_INFO_DBG_ADDR_INDEX)
T8C20 000:341.668   Value=0xE000EDF0
T8C20 000:341.684 - 0.032ms returns 0
T8C20 000:341.695 JLINK_GetDebugInfo(0x01 = Unknown)
T8C20 000:341.707   Value=0x00000001
T8C20 000:341.723 - 0.032ms returns 0
T8C20 000:341.734 JLINK_ReadMemU32(0xE000ED00, 0x1 Items)
T8C20 000:341.751   CPU_ReadMem(4 bytes @ 0xE000ED00)
T8C20 000:342.425   Data:  41 C2 0F 41
T8C20 000:342.460   Debug reg: CPUID
T8C20 000:342.477 - 0.748ms returns 1 (0x1)
T8C20 000:342.494 JLINK_GetDebugInfo(0x10F = JLINKARM_DEBUG_INFO_HAS_CORTEX_M_SECURITY_EXT_INDEX)
T8C20 000:342.507   Value=0x00000000
T8C20 000:342.523 - 0.034ms returns 0
T8C20 000:342.535 JLINK_HasError()
T8C20 000:342.548 JLINK_SetResetType(JLINKARM_CM3_RESET_TYPE_NORMAL)
T8C20 000:342.559 - 0.017ms returns JLINKARM_CM3_RESET_TYPE_NORMAL
T8C20 000:342.571 JLINK_Reset()
T8C20 000:342.602   CPU is running
T8C20 000:342.620   CPU_WriteMem(4 bytes @ 0xE000EDF0)
T8C20 000:343.247   CPU is running
T8C20 000:343.278   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T8C20 000:355.296   Reset: Halt core after reset via DEMCR.VC_CORERESET.
T8C20 000:360.602   Reset: Reset device via AIRCR.SYSRESETREQ.
T8C20 000:360.660   CPU is running
T8C20 000:360.682   CPU_WriteMem(4 bytes @ 0xE000ED0C)
T8C20 000:414.637   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T8C20 000:415.363   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T8C20 000:416.098   CPU is running
T8C20 000:416.116   CPU_WriteMem(4 bytes @ 0xE000EDF0)
T8C20 000:416.773   CPU is running
T8C20 000:416.826   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T8C20 000:423.723   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T8C20 000:429.510   CPU_WriteMem(4 bytes @ 0xE0002000)
T8C20 000:430.429   CPU_ReadMem(4 bytes @ 0xE000EDFC)
T8C20 000:431.050   CPU_ReadMem(4 bytes @ 0xE0001000)
T8C20 000:431.774 - 89.210ms
T8C20 000:431.799 JLINK_Halt()
T8C20 000:431.811 - 0.017ms returns 0x00
T8C20 000:431.825 JLINK_ReadMemU32(0xE000EDF0, 0x1 Items)
T8C20 000:431.844   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T8C20 000:432.568   Data:  03 00 03 00
T8C20 000:432.585   Debug reg: DHCSR
T8C20 000:432.601 - 0.780ms returns 1 (0x1)
T8C20 000:432.615 JLINK_WriteU32_64(0xE000EDF0, 0xA05F0003)
T8C20 000:432.631   Debug reg: DHCSR
T8C20 000:432.873   CPU_WriteMem(4 bytes @ 0xE000EDF0)
T8C20 000:433.556 - 0.952ms returns 0 (0x00000000)
T8C20 000:433.581 JLINK_WriteU32_64(0xE000EDFC, 0x01000000)
T8C20 000:433.593   Debug reg: DEMCR
T8C20 000:433.615   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T8C20 000:434.233 - 0.658ms returns 0 (0x00000000)
T8C20 000:450.435 JLINK_GetHWStatus(...)
T8C20 000:450.908 - 0.483ms returns 0
T8C20 000:460.474 JLINK_GetNumBPUnits(Type = 0xFFFFFF00)
T8C20 000:460.535 - 0.067ms returns 0x06
T8C20 000:460.549 JLINK_GetNumBPUnits(Type = 0xF0)
T8C20 000:460.560 - 0.016ms returns 0x2000
T8C20 000:460.572 JLINK_GetNumWPUnits()
T8C20 000:460.583 - 0.016ms returns 4
T8C20 000:470.128 JLINK_GetSpeed()
T8C20 000:470.167 - 0.045ms returns 2000
T8C20 000:476.848 JLINK_ReadMemU32(0xE000E004, 0x1 Items)
T8C20 000:476.892   CPU_ReadMem(4 bytes @ 0xE000E004)
T8C20 000:477.653   Data:  01 00 00 00
T8C20 000:477.685 - 0.842ms returns 1 (0x1)
T8C20 000:477.700 JLINK_ReadMemU32(0xE000E004, 0x1 Items)
T8C20 000:477.729   CPU_ReadMem(4 bytes @ 0xE000E004)
T8C20 000:478.405   Data:  01 00 00 00
T8C20 000:478.428 - 0.733ms returns 1 (0x1)
T8C20 000:478.441 JLINK_WriteMemEx(0xE0001000, 0x0000001C Bytes, Flags = 0x02000004)
T8C20 000:478.453   Data:  01 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 ...
T8C20 000:478.477   CPU_WriteMem(28 bytes @ 0xE0001000)
T8C20 000:479.502 - 1.069ms returns 0x1C
T8C20 000:479.521 JLINK_Halt()
T8C20 000:479.533 - 0.016ms returns 0x00
T8C20 000:479.545 JLINK_IsHalted()
T8C20 000:479.557 - 0.017ms returns TRUE
T8C20 000:484.337 JLINK_WriteMem(0x20000000, 0x5B8 Bytes, ...)
T8C20 000:484.359   Data:  00 BE 0A E0 0D 78 2D 06 68 40 08 24 40 00 00 D3 ...
T8C20 000:484.574   CPU_WriteMem(1464 bytes @ 0x20000000)
T8C20 000:499.103 - 14.795ms returns 0x5B8
T8C20 000:499.190 JLINK_HasError()
T8C20 000:499.208 JLINK_WriteReg(R0, 0x00000000)
T8C20 000:499.224 - 0.021ms returns 0
T8C20 000:499.238 JLINK_WriteReg(R1, 0x03D09000)
T8C20 000:499.250 - 0.017ms returns 0
T8C20 000:499.264 JLINK_WriteReg(R2, 0x00000001)
T8C20 000:499.276 - 0.017ms returns 0
T8C20 000:499.290 JLINK_WriteReg(R3, 0x00000000)
T8C20 000:499.302 - 0.017ms returns 0
T8C20 000:499.315 JLINK_WriteReg(R4, 0x00000000)
T8C20 000:499.327 - 0.017ms returns 0
T8C20 000:499.341 JLINK_WriteReg(R5, 0x00000000)
T8C20 000:499.352 - 0.017ms returns 0
T8C20 000:499.366 JLINK_WriteReg(R6, 0x00000000)
T8C20 000:499.378 - 0.017ms returns 0
T8C20 000:499.391 JLINK_WriteReg(R7, 0x00000000)
T8C20 000:499.403 - 0.017ms returns 0
T8C20 000:499.416 JLINK_WriteReg(R8, 0x00000000)
T8C20 000:499.445 - 0.035ms returns 0
T8C20 000:499.460 JLINK_WriteReg(R9, 0x200005B4)
T8C20 000:499.472 - 0.017ms returns 0
T8C20 000:499.485 JLINK_WriteReg(R10, 0x00000000)
T8C20 000:499.497 - 0.017ms returns 0
T8C20 000:499.511 JLINK_WriteReg(R11, 0x00000000)
T8C20 000:499.523 - 0.017ms returns 0
T8C20 000:499.536 JLINK_WriteReg(R12, 0x00000000)
T8C20 000:499.548 - 0.017ms returns 0
T8C20 000:499.562 JLINK_WriteReg(R13 (SP), 0x20002000)
T8C20 000:499.579 - 0.023ms returns 0
T8C20 000:499.594 JLINK_WriteReg(R14, 0x20000001)
T8C20 000:499.606 - 0.017ms returns 0
T8C20 000:499.620 JLINK_WriteReg(R15 (PC), 0x20000020)
T8C20 000:499.636 - 0.021ms returns 0
T8C20 000:499.648 JLINK_WriteReg(XPSR, 0x01000000)
T8C20 000:499.659 - 0.016ms returns 0
T8C20 000:499.673 JLINK_WriteReg(MSP, 0x20002000)
T8C20 000:499.685 - 0.017ms returns 0
T8C20 000:499.699 JLINK_WriteReg(PSP, 0x20002000)
T8C20 000:499.710 - 0.017ms returns 0
T8C20 000:499.724 JLINK_WriteReg(CFBP, 0x00000000)
T8C20 000:499.736 - 0.017ms returns 0
T8C20 000:499.750 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T8C20 000:499.767   CPU_ReadMem(2 bytes @ 0x20000000)
T8C20 000:500.403 - 0.666ms returns 0x00000001
T8C20 000:500.428 JLINK_Go()
T8C20 000:500.444   CPU_WriteMem(2 bytes @ 0x20000000)
T8C20 000:501.137   CPU_ReadMem(4 bytes @ 0xE0001000)
T8C20 000:501.811   CPU_WriteMem(4 bytes @ 0xE0002008)
T8C20 000:501.830   CPU_WriteMem(4 bytes @ 0xE000200C)
T8C20 000:501.847   CPU_WriteMem(4 bytes @ 0xE0002010)
T8C20 000:501.863   CPU_WriteMem(4 bytes @ 0xE0002014)
T8C20 000:501.880   CPU_WriteMem(4 bytes @ 0xE0002018)
T8C20 000:501.897   CPU_WriteMem(4 bytes @ 0xE000201C)
T8C20 000:504.691   CPU_WriteMem(4 bytes @ 0xE0001004)
T8C20 000:510.210 - 9.804ms
T8C20 000:510.249 JLINK_IsHalted()
T8C20 000:515.073   CPU_ReadMem(2 bytes @ 0x20000000)
T8C20 000:515.755 - 5.514ms returns TRUE
T8C20 000:515.779 JLINK_ReadReg(R15 (PC))
T8C20 000:515.794 - 0.021ms returns 0x20000000
T8C20 000:515.809 JLINK_ClrBPEx(BPHandle = 0x00000001)
T8C20 000:515.821 - 0.018ms returns 0x00
T8C20 000:515.835 JLINK_ReadReg(R0)
T8C20 000:515.847 - 0.018ms returns 0x00000000
T8C20 000:516.156 JLINK_HasError()
T8C20 000:516.179 JLINK_WriteReg(R0, 0x00026000)
T8C20 000:516.193 - 0.019ms returns 0
T8C20 000:516.205 JLINK_WriteReg(R1, 0x00001000)
T8C20 000:516.217 - 0.016ms returns 0
T8C20 000:516.228 JLINK_WriteReg(R2, 0x000000FF)
T8C20 000:516.240 - 0.017ms returns 0
T8C20 000:516.252 JLINK_WriteReg(R3, 0x00000000)
T8C20 000:516.263 - 0.016ms returns 0
T8C20 000:516.274 JLINK_WriteReg(R4, 0x00000000)
T8C20 000:516.285 - 0.016ms returns 0
T8C20 000:516.297 JLINK_WriteReg(R5, 0x00000000)
T8C20 000:516.308 - 0.017ms returns 0
T8C20 000:516.320 JLINK_WriteReg(R6, 0x00000000)
T8C20 000:516.331 - 0.016ms returns 0
T8C20 000:516.342 JLINK_WriteReg(R7, 0x00000000)
T8C20 000:516.354 - 0.016ms returns 0
T8C20 000:516.365 JLINK_WriteReg(R8, 0x00000000)
T8C20 000:516.377 - 0.016ms returns 0
T8C20 000:516.388 JLINK_WriteReg(R9, 0x200005B4)
T8C20 000:516.399 - 0.016ms returns 0
T8C20 000:516.410 JLINK_WriteReg(R10, 0x00000000)
T8C20 000:516.422 - 0.016ms returns 0
T8C20 000:516.439 JLINK_WriteReg(R11, 0x00000000)
T8C20 000:516.451 - 0.017ms returns 0
T8C20 000:516.463 JLINK_WriteReg(R12, 0x00000000)
T8C20 000:516.474 - 0.017ms returns 0
T8C20 000:516.486 JLINK_WriteReg(R13 (SP), 0x20002000)
T8C20 000:516.498 - 0.017ms returns 0
T8C20 000:516.510 JLINK_WriteReg(R14, 0x20000001)
T8C20 000:516.521 - 0.016ms returns 0
T8C20 000:516.532 JLINK_WriteReg(R15 (PC), 0x20000288)
T8C20 000:516.544 - 0.016ms returns 0
T8C20 000:516.555 JLINK_WriteReg(XPSR, 0x01000000)
T8C20 000:516.566 - 0.016ms returns 0
T8C20 000:516.578 JLINK_WriteReg(MSP, 0x20002000)
T8C20 000:516.589 - 0.018ms returns 0
T8C20 000:516.605 JLINK_WriteReg(PSP, 0x20002000)
T8C20 000:516.617 - 0.016ms returns 0
T8C20 000:516.628 JLINK_WriteReg(CFBP, 0x00000000)
T8C20 000:516.640 - 0.016ms returns 0
T8C20 000:516.652 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T8C20 000:516.664 - 0.017ms returns 0x00000002
T8C20 000:516.676 JLINK_Go()
T8C20 000:516.694   CPU_ReadMem(4 bytes @ 0xE0001000)
T8C20 000:522.119 - 5.460ms
T8C20 000:522.145 JLINK_IsHalted()
T8C20 000:527.519   CPU_ReadMem(2 bytes @ 0x20000000)
T8C20 000:528.239 - 6.110ms returns TRUE
T8C20 000:528.266 JLINK_ReadReg(R15 (PC))
T8C20 000:528.281 - 0.020ms returns 0x20000000
T8C20 000:528.293 JLINK_ClrBPEx(BPHandle = 0x00000002)
T8C20 000:528.356 - 0.070ms returns 0x00
T8C20 000:528.371 JLINK_ReadReg(R0)
T8C20 000:528.383 - 0.017ms returns 0x00000001
T8C20 000:528.398 JLINK_HasError()
T8C20 000:528.410 JLINK_WriteReg(R0, 0x00026000)
T8C20 000:528.423 - 0.017ms returns 0
T8C20 000:528.434 JLINK_WriteReg(R1, 0x00001000)
T8C20 000:528.446 - 0.016ms returns 0
T8C20 000:528.457 JLINK_WriteReg(R2, 0x000000FF)
T8C20 000:528.469 - 0.017ms returns 0
T8C20 000:528.481 JLINK_WriteReg(R3, 0x00000000)
T8C20 000:528.492 - 0.016ms returns 0
T8C20 000:528.503 JLINK_WriteReg(R4, 0x00000000)
T8C20 000:528.515 - 0.016ms returns 0
T8C20 000:528.526 JLINK_WriteReg(R5, 0x00000000)
T8C20 000:528.537 - 0.016ms returns 0
T8C20 000:528.549 JLINK_WriteReg(R6, 0x00000000)
T8C20 000:528.560 - 0.016ms returns 0
T8C20 000:528.572 JLINK_WriteReg(R7, 0x00000000)
T8C20 000:528.583 - 0.016ms returns 0
T8C20 000:528.595 JLINK_WriteReg(R8, 0x00000000)
T8C20 000:528.606 - 0.016ms returns 0
T8C20 000:528.618 JLINK_WriteReg(R9, 0x200005B4)
T8C20 000:528.629 - 0.016ms returns 0
T8C20 000:528.641 JLINK_WriteReg(R10, 0x00000000)
T8C20 000:528.652 - 0.016ms returns 0
T8C20 000:528.664 JLINK_WriteReg(R11, 0x00000000)
T8C20 000:528.675 - 0.016ms returns 0
T8C20 000:528.687 JLINK_WriteReg(R12, 0x00000000)
T8C20 000:528.698 - 0.016ms returns 0
T8C20 000:528.710 JLINK_WriteReg(R13 (SP), 0x20002000)
T8C20 000:528.721 - 0.016ms returns 0
T8C20 000:528.733 JLINK_WriteReg(R14, 0x20000001)
T8C20 000:528.744 - 0.016ms returns 0
T8C20 000:528.756 JLINK_WriteReg(R15 (PC), 0x200000D4)
T8C20 000:528.767 - 0.017ms returns 0
T8C20 000:528.779 JLINK_WriteReg(XPSR, 0x01000000)
T8C20 000:528.790 - 0.016ms returns 0
T8C20 000:528.802 JLINK_WriteReg(MSP, 0x20002000)
T8C20 000:528.813 - 0.016ms returns 0
T8C20 000:528.825 JLINK_WriteReg(PSP, 0x20002000)
T8C20 000:528.836 - 0.016ms returns 0
T8C20 000:528.847 JLINK_WriteReg(CFBP, 0x00000000)
T8C20 000:528.859 - 0.016ms returns 0
T8C20 000:528.870 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T8C20 000:528.882 - 0.017ms returns 0x00000003
T8C20 000:528.894 JLINK_Go()
T8C20 000:528.912   CPU_ReadMem(4 bytes @ 0xE0001000)
T8C20 000:534.356 - 5.490ms
T8C20 000:534.393 JLINK_IsHalted()
T8C20 000:535.222 - 0.858ms returns FALSE
T8C20 000:535.258 JLINK_HasError()
T8C20 000:582.583 JLINK_IsHalted()
T8C20 000:587.521   CPU_ReadMem(2 bytes @ 0x20000000)
T8C20 000:588.225 - 5.661ms returns TRUE
T8C20 000:588.255 JLINK_ReadReg(R15 (PC))
T8C20 000:588.270 - 0.020ms returns 0x20000000
T8C20 000:588.283 JLINK_ClrBPEx(BPHandle = 0x00000003)
T8C20 000:588.295 - 0.018ms returns 0x00
T8C20 000:588.307 JLINK_ReadReg(R0)
T8C20 000:588.319 - 0.016ms returns 0x00000000
T8C20 000:588.943 JLINK_HasError()
T8C20 000:588.965 JLINK_WriteReg(R0, 0x00027000)
T8C20 000:588.979 - 0.019ms returns 0
T8C20 000:588.990 JLINK_WriteReg(R1, 0x00001000)
T8C20 000:589.002 - 0.017ms returns 0
T8C20 000:589.014 JLINK_WriteReg(R2, 0x000000FF)
T8C20 000:589.025 - 0.016ms returns 0
T8C20 000:589.037 JLINK_WriteReg(R3, 0x00000000)
T8C20 000:589.048 - 0.016ms returns 0
T8C20 000:589.059 JLINK_WriteReg(R4, 0x00000000)
T8C20 000:589.070 - 0.016ms returns 0
T8C20 000:589.081 JLINK_WriteReg(R5, 0x00000000)
T8C20 000:589.092 - 0.016ms returns 0
T8C20 000:589.104 JLINK_WriteReg(R6, 0x00000000)
T8C20 000:589.115 - 0.016ms returns 0
T8C20 000:589.127 JLINK_WriteReg(R7, 0x00000000)
T8C20 000:589.138 - 0.016ms returns 0
T8C20 000:589.149 JLINK_WriteReg(R8, 0x00000000)
T8C20 000:589.160 - 0.016ms returns 0
T8C20 000:589.172 JLINK_WriteReg(R9, 0x200005B4)
T8C20 000:589.183 - 0.016ms returns 0
T8C20 000:589.194 JLINK_WriteReg(R10, 0x00000000)
T8C20 000:589.206 - 0.017ms returns 0
T8C20 000:589.218 JLINK_WriteReg(R11, 0x00000000)
T8C20 000:589.229 - 0.016ms returns 0
T8C20 000:589.240 JLINK_WriteReg(R12, 0x00000000)
T8C20 000:589.251 - 0.016ms returns 0
T8C20 000:589.263 JLINK_WriteReg(R13 (SP), 0x20002000)
T8C20 000:589.274 - 0.016ms returns 0
T8C20 000:589.286 JLINK_WriteReg(R14, 0x20000001)
T8C20 000:589.297 - 0.021ms returns 0
T8C20 000:589.316 JLINK_WriteReg(R15 (PC), 0x20000288)
T8C20 000:589.327 - 0.016ms returns 0
T8C20 000:589.339 JLINK_WriteReg(XPSR, 0x01000000)
T8C20 000:589.350 - 0.016ms returns 0
T8C20 000:589.362 JLINK_WriteReg(MSP, 0x20002000)
T8C20 000:589.373 - 0.016ms returns 0
T8C20 000:589.385 JLINK_WriteReg(PSP, 0x20002000)
T8C20 000:589.396 - 0.016ms returns 0
T8C20 000:589.407 JLINK_WriteReg(CFBP, 0x00000000)
T8C20 000:589.419 - 0.016ms returns 0
T8C20 000:589.430 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T8C20 000:589.443 - 0.017ms returns 0x00000004
T8C20 000:589.455 JLINK_Go()
T8C20 000:589.473   CPU_ReadMem(4 bytes @ 0xE0001000)
T8C20 000:595.012 - 5.579ms
T8C20 000:595.050 JLINK_IsHalted()
T8C20 000:599.987   CPU_ReadMem(2 bytes @ 0x20000000)
T8C20 000:600.637 - 5.614ms returns TRUE
T8C20 000:600.680 JLINK_ReadReg(R15 (PC))
T8C20 000:600.696 - 0.021ms returns 0x20000000
T8C20 000:600.711 JLINK_ClrBPEx(BPHandle = 0x00000004)
T8C20 000:600.723 - 0.017ms returns 0x00
T8C20 000:600.737 JLINK_ReadReg(R0)
T8C20 000:600.749 - 0.017ms returns 0x00000001
T8C20 000:600.764 JLINK_HasError()
T8C20 000:600.778 JLINK_WriteReg(R0, 0x00027000)
T8C20 000:600.791 - 0.018ms returns 0
T8C20 000:600.804 JLINK_WriteReg(R1, 0x00001000)
T8C20 000:600.816 - 0.017ms returns 0
T8C20 000:600.829 JLINK_WriteReg(R2, 0x000000FF)
T8C20 000:600.841 - 0.017ms returns 0
T8C20 000:600.855 JLINK_WriteReg(R3, 0x00000000)
T8C20 000:600.867 - 0.017ms returns 0
T8C20 000:600.880 JLINK_WriteReg(R4, 0x00000000)
T8C20 000:600.892 - 0.017ms returns 0
T8C20 000:600.905 JLINK_WriteReg(R5, 0x00000000)
T8C20 000:600.917 - 0.017ms returns 0
T8C20 000:600.930 JLINK_WriteReg(R6, 0x00000000)
T8C20 000:600.942 - 0.017ms returns 0
T8C20 000:600.955 JLINK_WriteReg(R7, 0x00000000)
T8C20 000:600.967 - 0.017ms returns 0
T8C20 000:600.981 JLINK_WriteReg(R8, 0x00000000)
T8C20 000:600.993 - 0.017ms returns 0
T8C20 000:601.006 JLINK_WriteReg(R9, 0x200005B4)
T8C20 000:601.018 - 0.017ms returns 0
T8C20 000:601.032 JLINK_WriteReg(R10, 0x00000000)
T8C20 000:601.044 - 0.017ms returns 0
T8C20 000:601.057 JLINK_WriteReg(R11, 0x00000000)
T8C20 000:601.069 - 0.017ms returns 0
T8C20 000:601.082 JLINK_WriteReg(R12, 0x00000000)
T8C20 000:601.094 - 0.017ms returns 0
T8C20 000:601.108 JLINK_WriteReg(R13 (SP), 0x20002000)
T8C20 000:601.122 - 0.020ms returns 0
T8C20 000:601.136 JLINK_WriteReg(R14, 0x20000001)
T8C20 000:601.148 - 0.017ms returns 0
T8C20 000:601.161 JLINK_WriteReg(R15 (PC), 0x200000D4)
T8C20 000:601.172 - 0.016ms returns 0
T8C20 000:601.186 JLINK_WriteReg(XPSR, 0x01000000)
T8C20 000:601.198 - 0.017ms returns 0
T8C20 000:601.211 JLINK_WriteReg(MSP, 0x20002000)
T8C20 000:601.223 - 0.017ms returns 0
T8C20 000:601.237 JLINK_WriteReg(PSP, 0x20002000)
T8C20 000:601.248 - 0.017ms returns 0
T8C20 000:601.262 JLINK_WriteReg(CFBP, 0x00000000)
T8C20 000:601.274 - 0.017ms returns 0
T8C20 000:601.287 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T8C20 000:601.300 - 0.018ms returns 0x00000005
T8C20 000:601.314 JLINK_Go()
T8C20 000:601.331   CPU_ReadMem(4 bytes @ 0xE0001000)
T8C20 000:606.820 - 5.523ms
T8C20 000:606.852 JLINK_IsHalted()
T8C20 000:607.507 - 0.668ms returns FALSE
T8C20 000:607.533 JLINK_HasError()
T8C20 000:615.614 JLINK_IsHalted()
T8C20 000:620.568   CPU_ReadMem(2 bytes @ 0x20000000)
T8C20 000:621.289 - 5.682ms returns TRUE
T8C20 000:621.305 JLINK_ReadReg(R15 (PC))
T8C20 000:621.319 - 0.019ms returns 0x20000000
T8C20 000:621.332 JLINK_ClrBPEx(BPHandle = 0x00000005)
T8C20 000:621.344 - 0.017ms returns 0x00
T8C20 000:621.356 JLINK_ReadReg(R0)
T8C20 000:621.368 - 0.017ms returns 0x00000000
T8C20 000:621.827 JLINK_HasError()
T8C20 000:621.851 JLINK_WriteReg(R0, 0x00028000)
T8C20 000:621.865 - 0.019ms returns 0
T8C20 000:621.878 JLINK_WriteReg(R1, 0x00001000)
T8C20 000:621.889 - 0.017ms returns 0
T8C20 000:621.901 JLINK_WriteReg(R2, 0x000000FF)
T8C20 000:621.912 - 0.016ms returns 0
T8C20 000:621.924 JLINK_WriteReg(R3, 0x00000000)
T8C20 000:621.936 - 0.017ms returns 0
T8C20 000:621.953 JLINK_WriteReg(R4, 0x00000000)
T8C20 000:621.967 - 0.019ms returns 0
T8C20 000:621.979 JLINK_WriteReg(R5, 0x00000000)
T8C20 000:621.991 - 0.017ms returns 0
T8C20 000:622.002 JLINK_WriteReg(R6, 0x00000000)
T8C20 000:622.013 - 0.016ms returns 0
T8C20 000:622.025 JLINK_WriteReg(R7, 0x00000000)
T8C20 000:622.042 - 0.023ms returns 0
T8C20 000:622.054 JLINK_WriteReg(R8, 0x00000000)
T8C20 000:622.066 - 0.017ms returns 0
T8C20 000:622.078 JLINK_WriteReg(R9, 0x200005B4)
T8C20 000:622.089 - 0.034ms returns 0
T8C20 000:622.119 JLINK_WriteReg(R10, 0x00000000)
T8C20 000:622.130 - 0.017ms returns 0
T8C20 000:622.142 JLINK_WriteReg(R11, 0x00000000)
T8C20 000:622.153 - 0.016ms returns 0
T8C20 000:622.165 JLINK_WriteReg(R12, 0x00000000)
T8C20 000:622.179 - 0.019ms returns 0
T8C20 000:622.191 JLINK_WriteReg(R13 (SP), 0x20002000)
T8C20 000:622.202 - 0.017ms returns 0
T8C20 000:622.214 JLINK_WriteReg(R14, 0x20000001)
T8C20 000:622.225 - 0.016ms returns 0
T8C20 000:622.237 JLINK_WriteReg(R15 (PC), 0x20000288)
T8C20 000:622.249 - 0.017ms returns 0
T8C20 000:622.261 JLINK_WriteReg(XPSR, 0x01000000)
T8C20 000:622.272 - 0.017ms returns 0
T8C20 000:622.284 JLINK_WriteReg(MSP, 0x20002000)
T8C20 000:622.296 - 0.017ms returns 0
T8C20 000:622.308 JLINK_WriteReg(PSP, 0x20002000)
T8C20 000:622.319 - 0.017ms returns 0
T8C20 000:622.331 JLINK_WriteReg(CFBP, 0x00000000)
T8C20 000:622.342 - 0.016ms returns 0
T8C20 000:622.354 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T8C20 000:622.367 - 0.017ms returns 0x00000006
T8C20 000:622.379 JLINK_Go()
T8C20 000:622.397   CPU_ReadMem(4 bytes @ 0xE0001000)
T8C20 000:627.867 - 5.514ms
T8C20 000:627.904 JLINK_IsHalted()
T8C20 000:632.817   CPU_ReadMem(2 bytes @ 0x20000000)
T8C20 000:633.523 - 5.639ms returns TRUE
T8C20 000:633.560 JLINK_ReadReg(R15 (PC))
T8C20 000:633.576 - 0.021ms returns 0x20000000
T8C20 000:633.590 JLINK_ClrBPEx(BPHandle = 0x00000006)
T8C20 000:633.602 - 0.017ms returns 0x00
T8C20 000:633.616 JLINK_ReadReg(R0)
T8C20 000:633.629 - 0.017ms returns 0x00000001
T8C20 000:633.643 JLINK_HasError()
T8C20 000:633.658 JLINK_WriteReg(R0, 0x00028000)
T8C20 000:633.670 - 0.018ms returns 0
T8C20 000:633.685 JLINK_WriteReg(R1, 0x00001000)
T8C20 000:633.697 - 0.017ms returns 0
T8C20 000:633.710 JLINK_WriteReg(R2, 0x000000FF)
T8C20 000:633.722 - 0.017ms returns 0
T8C20 000:633.736 JLINK_WriteReg(R3, 0x00000000)
T8C20 000:633.748 - 0.017ms returns 0
T8C20 000:633.761 JLINK_WriteReg(R4, 0x00000000)
T8C20 000:633.773 - 0.017ms returns 0
T8C20 000:633.787 JLINK_WriteReg(R5, 0x00000000)
T8C20 000:633.799 - 0.017ms returns 0
T8C20 000:633.812 JLINK_WriteReg(R6, 0x00000000)
T8C20 000:633.824 - 0.016ms returns 0
T8C20 000:633.837 JLINK_WriteReg(R7, 0x00000000)
T8C20 000:633.849 - 0.017ms returns 0
T8C20 000:633.863 JLINK_WriteReg(R8, 0x00000000)
T8C20 000:633.874 - 0.016ms returns 0
T8C20 000:633.887 JLINK_WriteReg(R9, 0x200005B4)
T8C20 000:633.899 - 0.017ms returns 0
T8C20 000:633.913 JLINK_WriteReg(R10, 0x00000000)
T8C20 000:633.924 - 0.017ms returns 0
T8C20 000:633.938 JLINK_WriteReg(R11, 0x00000000)
T8C20 000:633.950 - 0.017ms returns 0
T8C20 000:633.963 JLINK_WriteReg(R12, 0x00000000)
T8C20 000:633.975 - 0.017ms returns 0
T8C20 000:633.989 JLINK_WriteReg(R13 (SP), 0x20002000)
T8C20 000:634.001 - 0.018ms returns 0
T8C20 000:634.015 JLINK_WriteReg(R14, 0x20000001)
T8C20 000:634.026 - 0.017ms returns 0
T8C20 000:634.040 JLINK_WriteReg(R15 (PC), 0x200000D4)
T8C20 000:634.052 - 0.017ms returns 0
T8C20 000:634.066 JLINK_WriteReg(XPSR, 0x01000000)
T8C20 000:634.077 - 0.017ms returns 0
T8C20 000:634.091 JLINK_WriteReg(MSP, 0x20002000)
T8C20 000:634.103 - 0.017ms returns 0
T8C20 000:634.117 JLINK_WriteReg(PSP, 0x20002000)
T8C20 000:634.128 - 0.017ms returns 0
T8C20 000:634.142 JLINK_WriteReg(CFBP, 0x00000000)
T8C20 000:634.154 - 0.017ms returns 0
T8C20 000:634.168 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T8C20 000:634.180 - 0.018ms returns 0x00000007
T8C20 000:634.194 JLINK_Go()
T8C20 000:634.213   CPU_ReadMem(4 bytes @ 0xE0001000)
T8C20 000:639.816 - 5.643ms
T8C20 000:639.853 JLINK_IsHalted()
T8C20 000:640.711 - 0.866ms returns FALSE
T8C20 000:640.730 JLINK_HasError()
T8C20 000:651.459 JLINK_IsHalted()
T8C20 000:656.300   CPU_ReadMem(2 bytes @ 0x20000000)
T8C20 000:657.100 - 5.650ms returns TRUE
T8C20 000:657.119 JLINK_ReadReg(R15 (PC))
T8C20 000:657.133 - 0.019ms returns 0x20000000
T8C20 000:657.145 JLINK_ClrBPEx(BPHandle = 0x00000007)
T8C20 000:657.156 - 0.017ms returns 0x00
T8C20 000:657.168 JLINK_ReadReg(R0)
T8C20 000:657.180 - 0.016ms returns 0x00000000
T8C20 000:657.596 JLINK_HasError()
T8C20 000:657.615 JLINK_WriteReg(R0, 0x00029000)
T8C20 000:657.628 - 0.018ms returns 0
T8C20 000:657.639 JLINK_WriteReg(R1, 0x00001000)
T8C20 000:657.650 - 0.015ms returns 0
T8C20 000:657.661 JLINK_WriteReg(R2, 0x000000FF)
T8C20 000:657.671 - 0.015ms returns 0
T8C20 000:657.682 JLINK_WriteReg(R3, 0x00000000)
T8C20 000:657.693 - 0.015ms returns 0
T8C20 000:657.704 JLINK_WriteReg(R4, 0x00000000)
T8C20 000:657.714 - 0.015ms returns 0
T8C20 000:657.725 JLINK_WriteReg(R5, 0x00000000)
T8C20 000:657.735 - 0.015ms returns 0
T8C20 000:657.746 JLINK_WriteReg(R6, 0x00000000)
T8C20 000:657.757 - 0.015ms returns 0
T8C20 000:657.768 JLINK_WriteReg(R7, 0x00000000)
T8C20 000:657.778 - 0.015ms returns 0
T8C20 000:657.789 JLINK_WriteReg(R8, 0x00000000)
T8C20 000:657.799 - 0.015ms returns 0
T8C20 000:657.811 JLINK_WriteReg(R9, 0x200005B4)
T8C20 000:657.821 - 0.015ms returns 0
T8C20 000:657.832 JLINK_WriteReg(R10, 0x00000000)
T8C20 000:657.843 - 0.015ms returns 0
T8C20 000:657.854 JLINK_WriteReg(R11, 0x00000000)
T8C20 000:657.864 - 0.015ms returns 0
T8C20 000:657.875 JLINK_WriteReg(R12, 0x00000000)
T8C20 000:657.885 - 0.015ms returns 0
T8C20 000:657.896 JLINK_WriteReg(R13 (SP), 0x20002000)
T8C20 000:657.907 - 0.016ms returns 0
T8C20 000:657.918 JLINK_WriteReg(R14, 0x20000001)
T8C20 000:657.939 - 0.026ms returns 0
T8C20 000:657.950 JLINK_WriteReg(R15 (PC), 0x20000288)
T8C20 000:657.961 - 0.015ms returns 0
T8C20 000:657.972 JLINK_WriteReg(XPSR, 0x01000000)
T8C20 000:657.983 - 0.015ms returns 0
T8C20 000:657.993 JLINK_WriteReg(MSP, 0x20002000)
T8C20 000:658.004 - 0.015ms returns 0
T8C20 000:658.015 JLINK_WriteReg(PSP, 0x20002000)
T8C20 000:658.025 - 0.015ms returns 0
T8C20 000:658.036 JLINK_WriteReg(CFBP, 0x00000000)
T8C20 000:658.046 - 0.015ms returns 0
T8C20 000:658.058 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T8C20 000:658.069 - 0.016ms returns 0x00000008
T8C20 000:658.080 JLINK_Go()
T8C20 000:658.097   CPU_ReadMem(4 bytes @ 0xE0001000)
T8C20 000:663.558 - 5.500ms
T8C20 000:663.589 JLINK_IsHalted()
T8C20 000:668.474   CPU_ReadMem(2 bytes @ 0x20000000)
T8C20 000:669.161 - 5.577ms returns TRUE
T8C20 000:669.179 JLINK_ReadReg(R15 (PC))
T8C20 000:669.192 - 0.017ms returns 0x20000000
T8C20 000:669.204 JLINK_ClrBPEx(BPHandle = 0x00000008)
T8C20 000:669.214 - 0.015ms returns 0x00
T8C20 000:669.226 JLINK_ReadReg(R0)
T8C20 000:669.237 - 0.015ms returns 0x00000001
T8C20 000:669.249 JLINK_HasError()
T8C20 000:669.262 JLINK_WriteReg(R0, 0x00029000)
T8C20 000:669.273 - 0.015ms returns 0
T8C20 000:669.285 JLINK_WriteReg(R1, 0x00001000)
T8C20 000:669.295 - 0.014ms returns 0
T8C20 000:669.307 JLINK_WriteReg(R2, 0x000000FF)
T8C20 000:669.317 - 0.014ms returns 0
T8C20 000:669.328 JLINK_WriteReg(R3, 0x00000000)
T8C20 000:669.338 - 0.014ms returns 0
T8C20 000:669.350 JLINK_WriteReg(R4, 0x00000000)
T8C20 000:669.361 - 0.015ms returns 0
T8C20 000:669.372 JLINK_WriteReg(R5, 0x00000000)
T8C20 000:669.382 - 0.014ms returns 0
T8C20 000:669.394 JLINK_WriteReg(R6, 0x00000000)
T8C20 000:669.405 - 0.015ms returns 0
T8C20 000:669.416 JLINK_WriteReg(R7, 0x00000000)
T8C20 000:669.426 - 0.014ms returns 0
T8C20 000:669.438 JLINK_WriteReg(R8, 0x00000000)
T8C20 000:669.448 - 0.014ms returns 0
T8C20 000:669.460 JLINK_WriteReg(R9, 0x200005B4)
T8C20 000:669.473 - 0.019ms returns 0
T8C20 000:669.486 JLINK_WriteReg(R10, 0x00000000)
T8C20 000:669.499 - 0.018ms returns 0
T8C20 000:669.511 JLINK_WriteReg(R11, 0x00000000)
T8C20 000:669.568 - 0.063ms returns 0
T8C20 000:669.582 JLINK_WriteReg(R12, 0x00000000)
T8C20 000:669.593 - 0.016ms returns 0
T8C20 000:669.606 JLINK_WriteReg(R13 (SP), 0x20002000)
T8C20 000:669.617 - 0.017ms returns 0
T8C20 000:669.630 JLINK_WriteReg(R14, 0x20000001)
T8C20 000:669.642 - 0.016ms returns 0
T8C20 000:669.654 JLINK_WriteReg(R15 (PC), 0x200000D4)
T8C20 000:669.666 - 0.016ms returns 0
T8C20 000:669.679 JLINK_WriteReg(XPSR, 0x01000000)
T8C20 000:669.690 - 0.016ms returns 0
T8C20 000:669.703 JLINK_WriteReg(MSP, 0x20002000)
T8C20 000:669.714 - 0.016ms returns 0
T8C20 000:669.726 JLINK_WriteReg(PSP, 0x20002000)
T8C20 000:669.737 - 0.016ms returns 0
T8C20 000:669.750 JLINK_WriteReg(CFBP, 0x00000000)
T8C20 000:669.761 - 0.016ms returns 0
T8C20 000:669.774 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T8C20 000:669.787 - 0.017ms returns 0x00000009
T8C20 000:669.799 JLINK_Go()
T8C20 000:669.817   CPU_ReadMem(4 bytes @ 0xE0001000)
T8C20 000:675.400 - 5.618ms
T8C20 000:675.430 JLINK_IsHalted()
T8C20 000:676.137 - 0.712ms returns FALSE
T8C20 000:676.151 JLINK_HasError()
T8C20 000:683.573 JLINK_IsHalted()
T8C20 000:688.496   CPU_ReadMem(2 bytes @ 0x20000000)
T8C20 000:689.175 - 5.609ms returns TRUE
T8C20 000:689.190 JLINK_ReadReg(R15 (PC))
T8C20 000:689.203 - 0.018ms returns 0x20000000
T8C20 000:689.215 JLINK_ClrBPEx(BPHandle = 0x00000009)
T8C20 000:689.227 - 0.017ms returns 0x00
T8C20 000:689.239 JLINK_ReadReg(R0)
T8C20 000:689.251 - 0.017ms returns 0x00000000
T8C20 000:689.689 JLINK_HasError()
T8C20 000:689.710 JLINK_WriteReg(R0, 0x0002A000)
T8C20 000:689.724 - 0.019ms returns 0
T8C20 000:689.736 JLINK_WriteReg(R1, 0x00001000)
T8C20 000:689.746 - 0.016ms returns 0
T8C20 000:689.758 JLINK_WriteReg(R2, 0x000000FF)
T8C20 000:689.769 - 0.015ms returns 0
T8C20 000:689.780 JLINK_WriteReg(R3, 0x00000000)
T8C20 000:689.790 - 0.015ms returns 0
T8C20 000:689.802 JLINK_WriteReg(R4, 0x00000000)
T8C20 000:689.812 - 0.015ms returns 0
T8C20 000:689.822 JLINK_WriteReg(R5, 0x00000000)
T8C20 000:689.832 - 0.014ms returns 0
T8C20 000:689.842 JLINK_WriteReg(R6, 0x00000000)
T8C20 000:689.852 - 0.014ms returns 0
T8C20 000:689.862 JLINK_WriteReg(R7, 0x00000000)
T8C20 000:689.873 - 0.015ms returns 0
T8C20 000:689.884 JLINK_WriteReg(R8, 0x00000000)
T8C20 000:689.894 - 0.016ms returns 0
T8C20 000:689.906 JLINK_WriteReg(R9, 0x200005B4)
T8C20 000:689.919 - 0.018ms returns 0
T8C20 000:689.930 JLINK_WriteReg(R10, 0x00000000)
T8C20 000:689.940 - 0.016ms returns 0
T8C20 000:689.952 JLINK_WriteReg(R11, 0x00000000)
T8C20 000:689.963 - 0.016ms returns 0
T8C20 000:689.974 JLINK_WriteReg(R12, 0x00000000)
T8C20 000:689.986 - 0.016ms returns 0
T8C20 000:689.997 JLINK_WriteReg(R13 (SP), 0x20002000)
T8C20 000:690.009 - 0.017ms returns 0
T8C20 000:690.021 JLINK_WriteReg(R14, 0x20000001)
T8C20 000:690.032 - 0.018ms returns 0
T8C20 000:690.045 JLINK_WriteReg(R15 (PC), 0x20000288)
T8C20 000:690.056 - 0.016ms returns 0
T8C20 000:690.067 JLINK_WriteReg(XPSR, 0x01000000)
T8C20 000:690.078 - 0.016ms returns 0
T8C20 000:690.090 JLINK_WriteReg(MSP, 0x20002000)
T8C20 000:690.101 - 0.016ms returns 0
T8C20 000:690.113 JLINK_WriteReg(PSP, 0x20002000)
T8C20 000:690.124 - 0.016ms returns 0
T8C20 000:690.136 JLINK_WriteReg(CFBP, 0x00000000)
T8C20 000:690.148 - 0.016ms returns 0
T8C20 000:690.159 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T8C20 000:690.170 - 0.016ms returns 0x0000000A
T8C20 000:690.182 JLINK_Go()
T8C20 000:690.199   CPU_ReadMem(4 bytes @ 0xE0001000)
T8C20 000:695.812 - 5.656ms
T8C20 000:695.848 JLINK_IsHalted()
T8C20 000:700.894   CPU_ReadMem(2 bytes @ 0x20000000)
T8C20 000:701.607 - 5.767ms returns TRUE
T8C20 000:701.629 JLINK_ReadReg(R15 (PC))
T8C20 000:701.644 - 0.021ms returns 0x20000000
T8C20 000:701.659 JLINK_ClrBPEx(BPHandle = 0x0000000A)
T8C20 000:701.671 - 0.018ms returns 0x00
T8C20 000:701.685 JLINK_ReadReg(R0)
T8C20 000:701.697 - 0.018ms returns 0x00000001
T8C20 000:701.712 JLINK_HasError()
T8C20 000:701.727 JLINK_WriteReg(R0, 0x0002A000)
T8C20 000:701.746 - 0.024ms returns 0
T8C20 000:701.760 JLINK_WriteReg(R1, 0x00001000)
T8C20 000:701.771 - 0.017ms returns 0
T8C20 000:701.785 JLINK_WriteReg(R2, 0x000000FF)
T8C20 000:701.797 - 0.017ms returns 0
T8C20 000:701.811 JLINK_WriteReg(R3, 0x00000000)
T8C20 000:701.822 - 0.017ms returns 0
T8C20 000:701.836 JLINK_WriteReg(R4, 0x00000000)
T8C20 000:701.847 - 0.017ms returns 0
T8C20 000:701.861 JLINK_WriteReg(R5, 0x00000000)
T8C20 000:701.872 - 0.017ms returns 0
T8C20 000:701.886 JLINK_WriteReg(R6, 0x00000000)
T8C20 000:701.898 - 0.017ms returns 0
T8C20 000:701.911 JLINK_WriteReg(R7, 0x00000000)
T8C20 000:701.923 - 0.017ms returns 0
T8C20 000:701.936 JLINK_WriteReg(R8, 0x00000000)
T8C20 000:701.948 - 0.017ms returns 0
T8C20 000:701.962 JLINK_WriteReg(R9, 0x200005B4)
T8C20 000:701.974 - 0.017ms returns 0
T8C20 000:701.987 JLINK_WriteReg(R10, 0x00000000)
T8C20 000:701.999 - 0.017ms returns 0
T8C20 000:702.012 JLINK_WriteReg(R11, 0x00000000)
T8C20 000:702.024 - 0.017ms returns 0
T8C20 000:702.038 JLINK_WriteReg(R12, 0x00000000)
T8C20 000:702.050 - 0.017ms returns 0
T8C20 000:702.063 JLINK_WriteReg(R13 (SP), 0x20002000)
T8C20 000:702.091 - 0.033ms returns 0
T8C20 000:702.106 JLINK_WriteReg(R14, 0x20000001)
T8C20 000:702.118 - 0.018ms returns 0
T8C20 000:702.132 JLINK_WriteReg(R15 (PC), 0x200000D4)
T8C20 000:702.144 - 0.017ms returns 0
T8C20 000:702.158 JLINK_WriteReg(XPSR, 0x01000000)
T8C20 000:702.169 - 0.017ms returns 0
T8C20 000:702.183 JLINK_WriteReg(MSP, 0x20002000)
T8C20 000:702.195 - 0.017ms returns 0
T8C20 000:702.208 JLINK_WriteReg(PSP, 0x20002000)
T8C20 000:702.220 - 0.017ms returns 0
T8C20 000:702.233 JLINK_WriteReg(CFBP, 0x00000000)
T8C20 000:702.245 - 0.017ms returns 0
T8C20 000:702.258 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T8C20 000:702.271 - 0.018ms returns 0x0000000B
T8C20 000:702.285 JLINK_Go()
T8C20 000:702.302   CPU_ReadMem(4 bytes @ 0xE0001000)
T8C20 000:707.853 - 5.588ms
T8C20 000:707.886 JLINK_IsHalted()
T8C20 000:708.540 - 0.662ms returns FALSE
T8C20 000:708.560 JLINK_HasError()
T8C20 000:727.001 JLINK_IsHalted()
T8C20 000:731.895   CPU_ReadMem(2 bytes @ 0x20000000)
T8C20 000:732.641 - 5.662ms returns TRUE
T8C20 000:732.675 JLINK_ReadReg(R15 (PC))
T8C20 000:732.689 - 0.020ms returns 0x20000000
T8C20 000:732.702 JLINK_ClrBPEx(BPHandle = 0x0000000B)
T8C20 000:732.714 - 0.017ms returns 0x00
T8C20 000:732.727 JLINK_ReadReg(R0)
T8C20 000:732.738 - 0.017ms returns 0x00000000
T8C20 000:733.220 JLINK_HasError()
T8C20 000:733.243 JLINK_WriteReg(R0, 0x0002B000)
T8C20 000:733.257 - 0.020ms returns 0
T8C20 000:733.270 JLINK_WriteReg(R1, 0x00001000)
T8C20 000:733.281 - 0.017ms returns 0
T8C20 000:733.293 JLINK_WriteReg(R2, 0x000000FF)
T8C20 000:733.304 - 0.016ms returns 0
T8C20 000:733.317 JLINK_WriteReg(R3, 0x00000000)
T8C20 000:733.328 - 0.017ms returns 0
T8C20 000:733.340 JLINK_WriteReg(R4, 0x00000000)
T8C20 000:733.351 - 0.016ms returns 0
T8C20 000:733.363 JLINK_WriteReg(R5, 0x00000000)
T8C20 000:733.374 - 0.034ms returns 0
T8C20 000:733.407 JLINK_WriteReg(R6, 0x00000000)
T8C20 000:733.419 - 0.017ms returns 0
T8C20 000:733.431 JLINK_WriteReg(R7, 0x00000000)
T8C20 000:733.443 - 0.017ms returns 0
T8C20 000:733.454 JLINK_WriteReg(R8, 0x00000000)
T8C20 000:733.466 - 0.016ms returns 0
T8C20 000:733.477 JLINK_WriteReg(R9, 0x200005B4)
T8C20 000:733.488 - 0.017ms returns 0
T8C20 000:733.500 JLINK_WriteReg(R10, 0x00000000)
T8C20 000:733.513 - 0.019ms returns 0
T8C20 000:733.528 JLINK_WriteReg(R11, 0x00000000)
T8C20 000:733.540 - 0.017ms returns 0
T8C20 000:733.551 JLINK_WriteReg(R12, 0x00000000)
T8C20 000:733.562 - 0.016ms returns 0
T8C20 000:733.574 JLINK_WriteReg(R13 (SP), 0x20002000)
T8C20 000:733.586 - 0.017ms returns 0
T8C20 000:733.597 JLINK_WriteReg(R14, 0x20000001)
T8C20 000:733.609 - 0.016ms returns 0
T8C20 000:733.620 JLINK_WriteReg(R15 (PC), 0x20000288)
T8C20 000:733.632 - 0.016ms returns 0
T8C20 000:733.643 JLINK_WriteReg(XPSR, 0x01000000)
T8C20 000:733.655 - 0.017ms returns 0
T8C20 000:733.671 JLINK_WriteReg(MSP, 0x20002000)
T8C20 000:733.684 - 0.019ms returns 0
T8C20 000:733.696 JLINK_WriteReg(PSP, 0x20002000)
T8C20 000:733.707 - 0.016ms returns 0
T8C20 000:733.719 JLINK_WriteReg(CFBP, 0x00000000)
T8C20 000:733.730 - 0.016ms returns 0
T8C20 000:733.742 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T8C20 000:733.754 - 0.018ms returns 0x0000000C
T8C20 000:733.767 JLINK_Go()
T8C20 000:733.785   CPU_ReadMem(4 bytes @ 0xE0001000)
T8C20 000:739.354 - 5.620ms
T8C20 000:739.403 JLINK_IsHalted()
T8C20 000:744.521   CPU_ReadMem(2 bytes @ 0x20000000)
T8C20 000:745.223 - 5.836ms returns TRUE
T8C20 000:745.255 JLINK_ReadReg(R15 (PC))
T8C20 000:745.271 - 0.021ms returns 0x20000000
T8C20 000:745.285 JLINK_ClrBPEx(BPHandle = 0x0000000C)
T8C20 000:745.297 - 0.017ms returns 0x00
T8C20 000:745.311 JLINK_ReadReg(R0)
T8C20 000:745.324 - 0.018ms returns 0x00000001
T8C20 000:745.338 JLINK_HasError()
T8C20 000:745.353 JLINK_WriteReg(R0, 0x0002B000)
T8C20 000:745.365 - 0.017ms returns 0
T8C20 000:745.379 JLINK_WriteReg(R1, 0x00001000)
T8C20 000:745.391 - 0.017ms returns 0
T8C20 000:745.404 JLINK_WriteReg(R2, 0x000000FF)
T8C20 000:745.416 - 0.017ms returns 0
T8C20 000:745.429 JLINK_WriteReg(R3, 0x00000000)
T8C20 000:745.441 - 0.017ms returns 0
T8C20 000:745.455 JLINK_WriteReg(R4, 0x00000000)
T8C20 000:745.467 - 0.017ms returns 0
T8C20 000:745.480 JLINK_WriteReg(R5, 0x00000000)
T8C20 000:745.492 - 0.017ms returns 0
T8C20 000:745.506 JLINK_WriteReg(R6, 0x00000000)
T8C20 000:745.529 - 0.030ms returns 0
T8C20 000:745.544 JLINK_WriteReg(R7, 0x00000000)
T8C20 000:745.556 - 0.017ms returns 0
T8C20 000:745.569 JLINK_WriteReg(R8, 0x00000000)
T8C20 000:745.582 - 0.018ms returns 0
T8C20 000:745.595 JLINK_WriteReg(R9, 0x200005B4)
T8C20 000:745.607 - 0.017ms returns 0
T8C20 000:745.621 JLINK_WriteReg(R10, 0x00000000)
T8C20 000:745.633 - 0.017ms returns 0
T8C20 000:745.645 JLINK_WriteReg(R11, 0x00000000)
T8C20 000:745.656 - 0.016ms returns 0
T8C20 000:745.668 JLINK_WriteReg(R12, 0x00000000)
T8C20 000:745.679 - 0.016ms returns 0
T8C20 000:745.727 JLINK_WriteReg(R13 (SP), 0x20002000)
T8C20 000:745.740 - 0.018ms returns 0
T8C20 000:745.753 JLINK_WriteReg(R14, 0x20000001)
T8C20 000:745.764 - 0.016ms returns 0
T8C20 000:745.776 JLINK_WriteReg(R15 (PC), 0x200000D4)
T8C20 000:745.787 - 0.016ms returns 0
T8C20 000:745.799 JLINK_WriteReg(XPSR, 0x01000000)
T8C20 000:745.810 - 0.016ms returns 0
T8C20 000:745.822 JLINK_WriteReg(MSP, 0x20002000)
T8C20 000:745.834 - 0.018ms returns 0
T8C20 000:745.846 JLINK_WriteReg(PSP, 0x20002000)
T8C20 000:745.858 - 0.016ms returns 0
T8C20 000:745.869 JLINK_WriteReg(CFBP, 0x00000000)
T8C20 000:745.880 - 0.016ms returns 0
T8C20 000:745.893 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T8C20 000:745.905 - 0.018ms returns 0x0000000D
T8C20 000:745.917 JLINK_Go()
T8C20 000:745.935   CPU_ReadMem(4 bytes @ 0xE0001000)
T8C20 000:751.442 - 5.540ms
T8C20 000:751.466 JLINK_IsHalted()
T8C20 000:752.116 - 0.656ms returns FALSE
T8C20 000:752.129 JLINK_HasError()
T8C20 000:756.594 JLINK_IsHalted()
T8C20 000:761.486   CPU_ReadMem(2 bytes @ 0x20000000)
T8C20 000:762.166 - 5.588ms returns TRUE
T8C20 000:762.193 JLINK_ReadReg(R15 (PC))
T8C20 000:762.208 - 0.020ms returns 0x20000000
T8C20 000:762.220 JLINK_ClrBPEx(BPHandle = 0x0000000D)
T8C20 000:762.232 - 0.017ms returns 0x00
T8C20 000:762.244 JLINK_ReadReg(R0)
T8C20 000:762.256 - 0.016ms returns 0x00000000
T8C20 000:762.750 JLINK_HasError()
T8C20 000:762.776 JLINK_WriteReg(R0, 0x0002C000)
T8C20 000:762.791 - 0.020ms returns 0
T8C20 000:762.804 JLINK_WriteReg(R1, 0x00001000)
T8C20 000:762.816 - 0.017ms returns 0
T8C20 000:762.828 JLINK_WriteReg(R2, 0x000000FF)
T8C20 000:762.839 - 0.016ms returns 0
T8C20 000:762.850 JLINK_WriteReg(R3, 0x00000000)
T8C20 000:762.861 - 0.016ms returns 0
T8C20 000:762.873 JLINK_WriteReg(R4, 0x00000000)
T8C20 000:762.884 - 0.016ms returns 0
T8C20 000:762.896 JLINK_WriteReg(R5, 0x00000000)
T8C20 000:762.907 - 0.016ms returns 0
T8C20 000:762.919 JLINK_WriteReg(R6, 0x00000000)
T8C20 000:762.937 - 0.023ms returns 0
T8C20 000:762.949 JLINK_WriteReg(R7, 0x00000000)
T8C20 000:762.960 - 0.017ms returns 0
T8C20 000:762.972 JLINK_WriteReg(R8, 0x00000000)
T8C20 000:762.983 - 0.016ms returns 0
T8C20 000:762.994 JLINK_WriteReg(R9, 0x200005B4)
T8C20 000:763.005 - 0.016ms returns 0
T8C20 000:763.017 JLINK_WriteReg(R10, 0x00000000)
T8C20 000:763.028 - 0.016ms returns 0
T8C20 000:763.040 JLINK_WriteReg(R11, 0x00000000)
T8C20 000:763.051 - 0.016ms returns 0
T8C20 000:763.063 JLINK_WriteReg(R12, 0x00000000)
T8C20 000:763.074 - 0.016ms returns 0
T8C20 000:763.085 JLINK_WriteReg(R13 (SP), 0x20002000)
T8C20 000:763.097 - 0.017ms returns 0
T8C20 000:763.109 JLINK_WriteReg(R14, 0x20000001)
T8C20 000:763.120 - 0.016ms returns 0
T8C20 000:763.132 JLINK_WriteReg(R15 (PC), 0x20000288)
T8C20 000:763.143 - 0.016ms returns 0
T8C20 000:763.155 JLINK_WriteReg(XPSR, 0x01000000)
T8C20 000:763.166 - 0.016ms returns 0
T8C20 000:763.178 JLINK_WriteReg(MSP, 0x20002000)
T8C20 000:763.189 - 0.016ms returns 0
T8C20 000:763.200 JLINK_WriteReg(PSP, 0x20002000)
T8C20 000:763.212 - 0.016ms returns 0
T8C20 000:763.223 JLINK_WriteReg(CFBP, 0x00000000)
T8C20 000:763.234 - 0.016ms returns 0
T8C20 000:763.246 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T8C20 000:763.258 - 0.017ms returns 0x0000000E
T8C20 000:763.270 JLINK_Go()
T8C20 000:763.288   CPU_ReadMem(4 bytes @ 0xE0001000)
T8C20 000:768.953 - 5.702ms
T8C20 000:768.982 JLINK_IsHalted()
T8C20 000:773.945   CPU_ReadMem(2 bytes @ 0x20000000)
T8C20 000:774.679 - 5.704ms returns TRUE
T8C20 000:774.701 JLINK_ReadReg(R15 (PC))
T8C20 000:774.716 - 0.020ms returns 0x20000000
T8C20 000:774.729 JLINK_ClrBPEx(BPHandle = 0x0000000E)
T8C20 000:774.741 - 0.017ms returns 0x00
T8C20 000:774.753 JLINK_ReadReg(R0)
T8C20 000:774.764 - 0.017ms returns 0x00000001
T8C20 000:774.777 JLINK_HasError()
T8C20 000:774.789 JLINK_WriteReg(R0, 0x0002C000)
T8C20 000:774.801 - 0.017ms returns 0
T8C20 000:774.816 JLINK_WriteReg(R1, 0x00001000)
T8C20 000:774.828 - 0.017ms returns 0
T8C20 000:774.840 JLINK_WriteReg(R2, 0x000000FF)
T8C20 000:774.851 - 0.016ms returns 0
T8C20 000:774.863 JLINK_WriteReg(R3, 0x00000000)
T8C20 000:774.875 - 0.017ms returns 0
T8C20 000:774.888 JLINK_WriteReg(R4, 0x00000000)
T8C20 000:774.900 - 0.017ms returns 0
T8C20 000:774.912 JLINK_WriteReg(R5, 0x00000000)
T8C20 000:774.923 - 0.016ms returns 0
T8C20 000:774.935 JLINK_WriteReg(R6, 0x00000000)
T8C20 000:774.946 - 0.016ms returns 0
T8C20 000:774.959 JLINK_WriteReg(R7, 0x00000000)
T8C20 000:774.971 - 0.017ms returns 0
T8C20 000:774.983 JLINK_WriteReg(R8, 0x00000000)
T8C20 000:774.994 - 0.016ms returns 0
T8C20 000:775.006 JLINK_WriteReg(R9, 0x200005B4)
T8C20 000:775.017 - 0.016ms returns 0
T8C20 000:775.029 JLINK_WriteReg(R10, 0x00000000)
T8C20 000:775.040 - 0.017ms returns 0
T8C20 000:775.053 JLINK_WriteReg(R11, 0x00000000)
T8C20 000:775.065 - 0.017ms returns 0
T8C20 000:775.077 JLINK_WriteReg(R12, 0x00000000)
T8C20 000:775.087 - 0.016ms returns 0
T8C20 000:775.099 JLINK_WriteReg(R13 (SP), 0x20002000)
T8C20 000:775.110 - 0.017ms returns 0
T8C20 000:775.122 JLINK_WriteReg(R14, 0x20000001)
T8C20 000:775.133 - 0.016ms returns 0
T8C20 000:775.147 JLINK_WriteReg(R15 (PC), 0x200000D4)
T8C20 000:775.159 - 0.017ms returns 0
T8C20 000:775.172 JLINK_WriteReg(XPSR, 0x01000000)
T8C20 000:775.184 - 0.017ms returns 0
T8C20 000:775.197 JLINK_WriteReg(MSP, 0x20002000)
T8C20 000:775.209 - 0.017ms returns 0
T8C20 000:775.223 JLINK_WriteReg(PSP, 0x20002000)
T8C20 000:775.235 - 0.017ms returns 0
T8C20 000:775.248 JLINK_WriteReg(CFBP, 0x00000000)
T8C20 000:775.260 - 0.017ms returns 0
T8C20 000:775.273 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T8C20 000:775.286 - 0.018ms returns 0x0000000F
T8C20 000:775.300 JLINK_Go()
T8C20 000:775.318   CPU_ReadMem(4 bytes @ 0xE0001000)
T8C20 000:780.904 - 5.626ms
T8C20 000:780.941 JLINK_IsHalted()
T8C20 000:781.633 - 0.699ms returns FALSE
T8C20 000:781.653 JLINK_HasError()
T8C20 000:786.414 JLINK_IsHalted()
T8C20 000:791.296   CPU_ReadMem(2 bytes @ 0x20000000)
T8C20 000:792.030 - 5.623ms returns TRUE
T8C20 000:792.047 JLINK_ReadReg(R15 (PC))
T8C20 000:792.061 - 0.020ms returns 0x20000000
T8C20 000:792.074 JLINK_ClrBPEx(BPHandle = 0x0000000F)
T8C20 000:792.086 - 0.017ms returns 0x00
T8C20 000:792.098 JLINK_ReadReg(R0)
T8C20 000:792.110 - 0.017ms returns 0x00000000
T8C20 000:792.487 JLINK_HasError()
T8C20 000:792.513 JLINK_WriteReg(R0, 0x00000001)
T8C20 000:792.528 - 0.020ms returns 0
T8C20 000:792.540 JLINK_WriteReg(R1, 0x00001000)
T8C20 000:792.552 - 0.021ms returns 0
T8C20 000:792.569 JLINK_WriteReg(R2, 0x000000FF)
T8C20 000:792.580 - 0.017ms returns 0
T8C20 000:792.592 JLINK_WriteReg(R3, 0x00000000)
T8C20 000:792.603 - 0.017ms returns 0
T8C20 000:792.615 JLINK_WriteReg(R4, 0x00000000)
T8C20 000:792.627 - 0.017ms returns 0
T8C20 000:792.639 JLINK_WriteReg(R5, 0x00000000)
T8C20 000:792.651 - 0.017ms returns 0
T8C20 000:792.662 JLINK_WriteReg(R6, 0x00000000)
T8C20 000:792.674 - 0.017ms returns 0
T8C20 000:792.686 JLINK_WriteReg(R7, 0x00000000)
T8C20 000:792.697 - 0.016ms returns 0
T8C20 000:792.709 JLINK_WriteReg(R8, 0x00000000)
T8C20 000:792.721 - 0.017ms returns 0
T8C20 000:792.733 JLINK_WriteReg(R9, 0x200005B4)
T8C20 000:792.744 - 0.016ms returns 0
T8C20 000:792.756 JLINK_WriteReg(R10, 0x00000000)
T8C20 000:792.870 - 0.119ms returns 0
T8C20 000:792.882 JLINK_WriteReg(R11, 0x00000000)
T8C20 000:792.903 - 0.027ms returns 0
T8C20 000:792.925 JLINK_WriteReg(R12, 0x00000000)
T8C20 000:792.936 - 0.016ms returns 0
T8C20 000:792.948 JLINK_WriteReg(R13 (SP), 0x20002000)
T8C20 000:792.960 - 0.017ms returns 0
T8C20 000:792.972 JLINK_WriteReg(R14, 0x20000001)
T8C20 000:792.983 - 0.016ms returns 0
T8C20 000:792.994 JLINK_WriteReg(R15 (PC), 0x20000060)
T8C20 000:793.006 - 0.017ms returns 0
T8C20 000:793.017 JLINK_WriteReg(XPSR, 0x01000000)
T8C20 000:793.029 - 0.017ms returns 0
T8C20 000:793.041 JLINK_WriteReg(MSP, 0x20002000)
T8C20 000:793.052 - 0.016ms returns 0
T8C20 000:793.064 JLINK_WriteReg(PSP, 0x20002000)
T8C20 000:793.075 - 0.017ms returns 0
T8C20 000:793.087 JLINK_WriteReg(CFBP, 0x00000000)
T8C20 000:793.098 - 0.017ms returns 0
T8C20 000:793.111 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T8C20 000:793.123 - 0.018ms returns 0x00000010
T8C20 000:793.135 JLINK_Go()
T8C20 000:793.154   CPU_ReadMem(4 bytes @ 0xE0001000)
T8C20 000:798.749 - 5.629ms
T8C20 000:798.774 JLINK_IsHalted()
T8C20 000:803.602   CPU_ReadMem(2 bytes @ 0x20000000)
T8C20 000:804.323 - 5.555ms returns TRUE
T8C20 000:804.337 JLINK_ReadReg(R15 (PC))
T8C20 000:804.349 - 0.016ms returns 0x20000000
T8C20 000:804.359 JLINK_ClrBPEx(BPHandle = 0x00000010)
T8C20 000:804.370 - 0.015ms returns 0x00
T8C20 000:804.380 JLINK_ReadReg(R0)
T8C20 000:804.390 - 0.014ms returns 0x00000000
T8C20 000:867.874 JLINK_WriteMem(0x20000000, 0x5B8 Bytes, ...)
T8C20 000:867.899   Data:  00 BE 0A E0 0D 78 2D 06 68 40 08 24 40 00 00 D3 ...
T8C20 000:867.929   CPU_WriteMem(1464 bytes @ 0x20000000)
T8C20 000:882.490 - 14.643ms returns 0x5B8
T8C20 000:882.565 JLINK_HasError()
T8C20 000:882.580 JLINK_WriteReg(R0, 0x00000000)
T8C20 000:882.596 - 0.021ms returns 0
T8C20 000:882.612 JLINK_WriteReg(R1, 0x03D09000)
T8C20 000:882.623 - 0.015ms returns 0
T8C20 000:882.634 JLINK_WriteReg(R2, 0x00000002)
T8C20 000:882.644 - 0.015ms returns 0
T8C20 000:882.655 JLINK_WriteReg(R3, 0x00000000)
T8C20 000:882.666 - 0.015ms returns 0
T8C20 000:882.676 JLINK_WriteReg(R4, 0x00000000)
T8C20 000:882.687 - 0.015ms returns 0
T8C20 000:882.698 JLINK_WriteReg(R5, 0x00000000)
T8C20 000:882.708 - 0.015ms returns 0
T8C20 000:882.719 JLINK_WriteReg(R6, 0x00000000)
T8C20 000:882.729 - 0.015ms returns 0
T8C20 000:882.740 JLINK_WriteReg(R7, 0x00000000)
T8C20 000:882.751 - 0.015ms returns 0
T8C20 000:882.762 JLINK_WriteReg(R8, 0x00000000)
T8C20 000:882.772 - 0.015ms returns 0
T8C20 000:882.783 JLINK_WriteReg(R9, 0x200005B4)
T8C20 000:882.793 - 0.015ms returns 0
T8C20 000:882.804 JLINK_WriteReg(R10, 0x00000000)
T8C20 000:882.818 - 0.021ms returns 0
T8C20 000:882.831 JLINK_WriteReg(R11, 0x00000000)
T8C20 000:882.841 - 0.015ms returns 0
T8C20 000:882.852 JLINK_WriteReg(R12, 0x00000000)
T8C20 000:882.863 - 0.015ms returns 0
T8C20 000:882.874 JLINK_WriteReg(R13 (SP), 0x20002000)
T8C20 000:882.885 - 0.016ms returns 0
T8C20 000:882.896 JLINK_WriteReg(R14, 0x20000001)
T8C20 000:882.907 - 0.015ms returns 0
T8C20 000:882.918 JLINK_WriteReg(R15 (PC), 0x20000020)
T8C20 000:882.929 - 0.016ms returns 0
T8C20 000:882.940 JLINK_WriteReg(XPSR, 0x01000000)
T8C20 000:882.950 - 0.016ms returns 0
T8C20 000:882.962 JLINK_WriteReg(MSP, 0x20002000)
T8C20 000:882.974 - 0.017ms returns 0
T8C20 000:882.985 JLINK_WriteReg(PSP, 0x20002000)
T8C20 000:882.996 - 0.015ms returns 0
T8C20 000:883.007 JLINK_WriteReg(CFBP, 0x00000000)
T8C20 000:883.026 - 0.024ms returns 0
T8C20 000:883.041 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T8C20 000:883.070   CPU_ReadMem(2 bytes @ 0x20000000)
T8C20 000:883.803 - 0.772ms returns 0x00000011
T8C20 000:883.825 JLINK_Go()
T8C20 000:883.840   CPU_WriteMem(2 bytes @ 0x20000000)
T8C20 000:884.490   CPU_ReadMem(4 bytes @ 0xE0001000)
T8C20 000:890.028 - 6.220ms
T8C20 000:890.060 JLINK_IsHalted()
T8C20 000:895.314   CPU_ReadMem(2 bytes @ 0x20000000)
T8C20 000:895.960 - 5.918ms returns TRUE
T8C20 000:895.994 JLINK_ReadReg(R15 (PC))
T8C20 000:896.011 - 0.022ms returns 0x20000000
T8C20 000:896.025 JLINK_ClrBPEx(BPHandle = 0x00000011)
T8C20 000:896.038 - 0.018ms returns 0x00
T8C20 000:896.052 JLINK_ReadReg(R0)
T8C20 000:896.064 - 0.017ms returns 0x00000000
T8C20 000:896.813 JLINK_WriteMem(0x200005C8, 0x238 Bytes, ...)
T8C20 000:896.839   Data:  08 60 00 20 C5 63 02 00 CD 63 02 00 CF 63 02 00 ...
T8C20 000:896.866   CPU_WriteMem(568 bytes @ 0x200005C8)
T8C20 000:903.314 - 6.514ms returns 0x238
T8C20 000:903.337 JLINK_WriteMem(0x20000800, 0x400 Bytes, ...)
T8C20 000:903.349   Data:  07 E0 C3 EA 02 03 40 E8 01 34 00 2C E9 D1 4F F0 ...
T8C20 000:903.373   CPU_WriteMem(1024 bytes @ 0x20000800)
T8C20 000:913.906 - 10.592ms returns 0x400
T8C20 000:913.939 JLINK_WriteMem(0x20000C00, 0x400 Bytes, ...)
T8C20 000:913.951   Data:  0F 21 20 48 DD F8 2C B0 FF F7 3F FF 1D 48 0F 21 ...
T8C20 000:913.977   CPU_WriteMem(1024 bytes @ 0x20000C00)
T8C20 000:924.669 - 10.757ms returns 0x400
T8C20 000:924.712 JLINK_WriteMem(0x20001000, 0x400 Bytes, ...)
T8C20 000:924.724   Data:  34 21 45 1C C1 F3 03 11 07 D0 84 46 16 46 01 27 ...
T8C20 000:924.756   CPU_WriteMem(1024 bytes @ 0x20001000)
T8C20 000:935.262 - 10.568ms returns 0x400
T8C20 000:935.293 JLINK_WriteMem(0x20001400, 0x1C8 Bytes, ...)
T8C20 000:935.305   Data:  0B A1 A1 64 E0 62 04 F5 1E 71 E1 64 10 21 A0 65 ...
T8C20 000:935.328   CPU_WriteMem(456 bytes @ 0x20001400)
T8C20 000:940.617 - 14.503ms returns 0x1C8
T8C20 000:949.911 JLINK_HasError()
T8C20 000:949.939 JLINK_WriteReg(R0, 0x00026000)
T8C20 000:949.955 - 0.021ms returns 0
T8C20 000:949.968 JLINK_WriteReg(R1, 0x00001000)
T8C20 000:949.979 - 0.016ms returns 0
T8C20 000:949.990 JLINK_WriteReg(R2, 0x200005C8)
T8C20 000:950.001 - 0.015ms returns 0
T8C20 000:950.014 JLINK_WriteReg(R3, 0x00000000)
T8C20 000:950.025 - 0.016ms returns 0
T8C20 000:950.038 JLINK_WriteReg(R4, 0x00000000)
T8C20 000:950.049 - 0.016ms returns 0
T8C20 000:950.059 JLINK_WriteReg(R5, 0x00000000)
T8C20 000:950.070 - 0.015ms returns 0
T8C20 000:950.081 JLINK_WriteReg(R6, 0x00000000)
T8C20 000:950.091 - 0.015ms returns 0
T8C20 000:950.104 JLINK_WriteReg(R7, 0x00000000)
T8C20 000:950.115 - 0.016ms returns 0
T8C20 000:950.128 JLINK_WriteReg(R8, 0x00000000)
T8C20 000:950.139 - 0.016ms returns 0
T8C20 000:950.150 JLINK_WriteReg(R9, 0x200005B4)
T8C20 000:950.161 - 0.015ms returns 0
T8C20 000:950.173 JLINK_WriteReg(R10, 0x00000000)
T8C20 000:950.184 - 0.016ms returns 0
T8C20 000:950.197 JLINK_WriteReg(R11, 0x00000000)
T8C20 000:950.207 - 0.015ms returns 0
T8C20 000:950.221 JLINK_WriteReg(R12, 0x00000000)
T8C20 000:950.232 - 0.016ms returns 0
T8C20 000:950.244 JLINK_WriteReg(R13 (SP), 0x20002000)
T8C20 000:950.259 - 0.022ms returns 0
T8C20 000:950.274 JLINK_WriteReg(R14, 0x20000001)
T8C20 000:950.285 - 0.016ms returns 0
T8C20 000:950.298 JLINK_WriteReg(R15 (PC), 0x20000154)
T8C20 000:950.309 - 0.016ms returns 0
T8C20 000:950.320 JLINK_WriteReg(XPSR, 0x01000000)
T8C20 000:950.331 - 0.015ms returns 0
T8C20 000:950.344 JLINK_WriteReg(MSP, 0x20002000)
T8C20 000:950.355 - 0.016ms returns 0
T8C20 000:950.367 JLINK_WriteReg(PSP, 0x20002000)
T8C20 000:950.377 - 0.015ms returns 0
T8C20 000:950.398 JLINK_WriteReg(CFBP, 0x00000000)
T8C20 000:950.411 - 0.018ms returns 0
T8C20 000:950.425 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T8C20 000:950.438 - 0.018ms returns 0x00000012
T8C20 000:950.452 JLINK_Go()
T8C20 000:950.472   CPU_ReadMem(4 bytes @ 0xE0001000)
T8C20 000:955.946 - 5.511ms
T8C20 000:955.979 JLINK_IsHalted()
T8C20 000:956.643 - 0.679ms returns FALSE
T8C20 000:956.672 JLINK_HasError()
T8C20 000:966.706 JLINK_IsHalted()
T8C20 000:967.506 - 0.806ms returns FALSE
T8C20 000:967.520 JLINK_HasError()
T8C20 000:969.827 JLINK_IsHalted()
T8C20 000:970.702 - 0.890ms returns FALSE
T8C20 000:970.725 JLINK_HasError()
T8C20 000:978.195 JLINK_IsHalted()
T8C20 000:978.991 - 0.811ms returns FALSE
T8C20 000:979.014 JLINK_HasError()
T8C20 000:980.247 JLINK_IsHalted()
T8C20 000:981.001 - 0.760ms returns FALSE
T8C20 000:981.015 JLINK_HasError()
T8C20 000:982.249 JLINK_IsHalted()
T8C20 000:983.005 - 0.763ms returns FALSE
T8C20 000:983.021 JLINK_HasError()
T8C20 000:984.627 JLINK_IsHalted()
T8C20 000:985.342 - 0.722ms returns FALSE
T8C20 000:985.357 JLINK_HasError()
T8C20 000:996.935 JLINK_IsHalted()
T8C20 000:997.724 - 0.806ms returns FALSE
T8C20 000:997.751 JLINK_HasError()
T8C20 000:998.983 JLINK_IsHalted()
T8C20 000:999.715 - 0.739ms returns FALSE
T8C20 000:999.729 JLINK_HasError()
T8C20 001:000.992 JLINK_IsHalted()
T8C20 001:002.101 - 1.123ms returns FALSE
T8C20 001:002.125 JLINK_HasError()
T8C20 001:003.460 JLINK_IsHalted()
T8C20 001:004.210 - 0.755ms returns FALSE
T8C20 001:004.223 JLINK_HasError()
T8C20 001:005.546 JLINK_IsHalted()
T8C20 001:006.267 - 0.727ms returns FALSE
T8C20 001:006.279 JLINK_HasError()
T8C20 001:007.553 JLINK_IsHalted()
T8C20 001:008.269 - 0.722ms returns FALSE
T8C20 001:008.282 JLINK_HasError()
T8C20 001:009.552 JLINK_IsHalted()
T8C20 001:010.263 - 0.717ms returns FALSE
T8C20 001:010.276 JLINK_HasError()
T8C20 001:011.475 JLINK_IsHalted()
T8C20 001:012.147 - 0.688ms returns FALSE
T8C20 001:012.172 JLINK_HasError()
T8C20 001:013.540 JLINK_IsHalted()
T8C20 001:014.308 - 0.774ms returns FALSE
T8C20 001:014.321 JLINK_HasError()
T8C20 001:015.615 JLINK_IsHalted()
T8C20 001:016.318 - 0.709ms returns FALSE
T8C20 001:016.331 JLINK_HasError()
T8C20 001:017.686 JLINK_IsHalted()
T8C20 001:018.355 - 0.676ms returns FALSE
T8C20 001:018.370 JLINK_HasError()
T8C20 001:026.307 JLINK_IsHalted()
T8C20 001:031.163   CPU_ReadMem(2 bytes @ 0x20000000)
T8C20 001:031.905 - 5.603ms returns TRUE
T8C20 001:031.921 JLINK_ReadReg(R15 (PC))
T8C20 001:031.937 - 0.021ms returns 0x20000000
T8C20 001:031.950 JLINK_ClrBPEx(BPHandle = 0x00000012)
T8C20 001:031.964 - 0.019ms returns 0x00
T8C20 001:031.978 JLINK_ReadReg(R0)
T8C20 001:031.991 - 0.019ms returns 0x00000000
T8C20 001:033.556 JLINK_WriteMem(0x200005C8, 0x238 Bytes, ...)
T8C20 001:033.578   Data:  68 60 0A 20 A5 F5 A1 65 03 F0 0A FD 11 20 FF F7 ...
T8C20 001:033.602   CPU_WriteMem(568 bytes @ 0x200005C8)
T8C20 001:039.957 - 6.412ms returns 0x238
T8C20 001:039.977 JLINK_WriteMem(0x20000800, 0x400 Bytes, ...)
T8C20 001:039.989   Data:  01 29 42 61 00 D0 00 21 01 61 00 20 70 47 07 20 ...
T8C20 001:040.010   CPU_WriteMem(1024 bytes @ 0x20000800)
T8C20 001:050.592 - 10.633ms returns 0x400
T8C20 001:050.620 JLINK_WriteMem(0x20000C00, 0x400 Bytes, ...)
T8C20 001:050.632   Data:  A4 00 60 63 D5 F8 A8 00 A0 63 4F F6 FF 70 A4 F8 ...
T8C20 001:050.655   CPU_WriteMem(1024 bytes @ 0x20000C00)
T8C20 001:061.144 - 10.549ms returns 0x400
T8C20 001:061.185 JLINK_WriteMem(0x20001000, 0x400 Bytes, ...)
T8C20 001:061.203   Data:  20 88 40 1C 80 B2 20 80 28 44 DD F8 01 10 01 60 ...
T8C20 001:061.227   CPU_WriteMem(1024 bytes @ 0x20001000)
T8C20 001:071.587 - 10.422ms returns 0x400
T8C20 001:071.622 JLINK_WriteMem(0x20001400, 0x1C8 Bytes, ...)
T8C20 001:071.633   Data:  04 00 8E B0 2D CD 08 AE 86 E8 2D 00 5B D0 E9 B3 ...
T8C20 001:071.657   CPU_WriteMem(456 bytes @ 0x20001400)
T8C20 001:076.936 - 5.329ms returns 0x1C8
T8C20 001:076.964 JLINK_HasError()
T8C20 001:076.979 JLINK_WriteReg(R0, 0x00027000)
T8C20 001:076.993 - 0.019ms returns 0
T8C20 001:077.007 JLINK_WriteReg(R1, 0x00001000)
T8C20 001:077.018 - 0.016ms returns 0
T8C20 001:077.030 JLINK_WriteReg(R2, 0x200005C8)
T8C20 001:077.041 - 0.016ms returns 0
T8C20 001:077.054 JLINK_WriteReg(R3, 0x00000000)
T8C20 001:077.065 - 0.016ms returns 0
T8C20 001:077.078 JLINK_WriteReg(R4, 0x00000000)
T8C20 001:077.090 - 0.017ms returns 0
T8C20 001:077.103 JLINK_WriteReg(R5, 0x00000000)
T8C20 001:077.114 - 0.017ms returns 0
T8C20 001:077.127 JLINK_WriteReg(R6, 0x00000000)
T8C20 001:077.138 - 0.016ms returns 0
T8C20 001:077.151 JLINK_WriteReg(R7, 0x00000000)
T8C20 001:077.165 - 0.019ms returns 0
T8C20 001:077.178 JLINK_WriteReg(R8, 0x00000000)
T8C20 001:077.189 - 0.016ms returns 0
T8C20 001:077.201 JLINK_WriteReg(R9, 0x200005B4)
T8C20 001:077.212 - 0.015ms returns 0
T8C20 001:077.225 JLINK_WriteReg(R10, 0x00000000)
T8C20 001:077.235 - 0.016ms returns 0
T8C20 001:077.248 JLINK_WriteReg(R11, 0x00000000)
T8C20 001:077.259 - 0.016ms returns 0
T8C20 001:077.271 JLINK_WriteReg(R12, 0x00000000)
T8C20 001:077.282 - 0.015ms returns 0
T8C20 001:077.295 JLINK_WriteReg(R13 (SP), 0x20002000)
T8C20 001:077.306 - 0.017ms returns 0
T8C20 001:077.319 JLINK_WriteReg(R14, 0x20000001)
T8C20 001:077.330 - 0.016ms returns 0
T8C20 001:077.342 JLINK_WriteReg(R15 (PC), 0x20000154)
T8C20 001:077.353 - 0.016ms returns 0
T8C20 001:077.366 JLINK_WriteReg(XPSR, 0x01000000)
T8C20 001:077.377 - 0.016ms returns 0
T8C20 001:077.389 JLINK_WriteReg(MSP, 0x20002000)
T8C20 001:077.400 - 0.016ms returns 0
T8C20 001:077.413 JLINK_WriteReg(PSP, 0x20002000)
T8C20 001:077.424 - 0.015ms returns 0
T8C20 001:077.436 JLINK_WriteReg(CFBP, 0x00000000)
T8C20 001:077.447 - 0.016ms returns 0
T8C20 001:077.460 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T8C20 001:077.472 - 0.016ms returns 0x00000013
T8C20 001:077.484 JLINK_Go()
T8C20 001:077.501   CPU_ReadMem(4 bytes @ 0xE0001000)
T8C20 001:083.203 - 5.738ms
T8C20 001:083.236 JLINK_IsHalted()
T8C20 001:083.850 - 0.629ms returns FALSE
T8C20 001:083.880 JLINK_HasError()
T8C20 001:087.705 JLINK_IsHalted()
T8C20 001:088.334 - 0.643ms returns FALSE
T8C20 001:088.357 JLINK_HasError()
T8C20 001:089.708 JLINK_IsHalted()
T8C20 001:090.442 - 0.744ms returns FALSE
T8C20 001:090.461 JLINK_HasError()
T8C20 001:092.283 JLINK_IsHalted()
T8C20 001:093.018 - 0.747ms returns FALSE
T8C20 001:093.039 JLINK_HasError()
T8C20 001:095.100 JLINK_IsHalted()
T8C20 001:096.002 - 0.937ms returns FALSE
T8C20 001:096.046 JLINK_HasError()
T8C20 001:097.145 JLINK_IsHalted()
T8C20 001:098.050 - 0.913ms returns FALSE
T8C20 001:098.066 JLINK_HasError()
T8C20 001:100.255 JLINK_IsHalted()
T8C20 001:100.887 - 0.647ms returns FALSE
T8C20 001:100.911 JLINK_HasError()
T8C20 001:102.260 JLINK_IsHalted()
T8C20 001:102.895 - 0.654ms returns FALSE
T8C20 001:102.923 JLINK_HasError()
T8C20 001:104.738 JLINK_IsHalted()
T8C20 001:105.382 - 0.661ms returns FALSE
T8C20 001:105.409 JLINK_HasError()
T8C20 001:109.731 JLINK_IsHalted()
T8C20 001:110.470 - 0.753ms returns FALSE
T8C20 001:110.493 JLINK_HasError()
T8C20 001:112.061 JLINK_IsHalted()
T8C20 001:112.808 - 0.757ms returns FALSE
T8C20 001:112.827 JLINK_HasError()
T8C20 001:114.807 JLINK_IsHalted()
T8C20 001:115.481 - 0.687ms returns FALSE
T8C20 001:115.503 JLINK_HasError()
T8C20 001:116.820 JLINK_IsHalted()
T8C20 001:117.553 - 0.745ms returns FALSE
T8C20 001:117.574 JLINK_HasError()
T8C20 001:118.752 JLINK_IsHalted()
T8C20 001:119.535 - 0.798ms returns FALSE
T8C20 001:119.559 JLINK_HasError()
T8C20 001:120.836 JLINK_IsHalted()
T8C20 001:121.527 - 0.698ms returns FALSE
T8C20 001:121.541 JLINK_HasError()
T8C20 001:123.384 JLINK_IsHalted()
T8C20 001:124.114 - 0.737ms returns FALSE
T8C20 001:124.128 JLINK_HasError()
T8C20 001:125.435 JLINK_IsHalted()
T8C20 001:126.348 - 0.925ms returns FALSE
T8C20 001:126.366 JLINK_HasError()
T8C20 001:128.469 JLINK_IsHalted()
T8C20 001:129.229 - 0.766ms returns FALSE
T8C20 001:129.242 JLINK_HasError()
T8C20 001:130.385 JLINK_IsHalted()
T8C20 001:131.090 - 0.711ms returns FALSE
T8C20 001:131.104 JLINK_HasError()
T8C20 001:132.497 JLINK_IsHalted()
T8C20 001:133.290 - 0.799ms returns FALSE
T8C20 001:133.303 JLINK_HasError()
T8C20 001:134.546 JLINK_IsHalted()
T8C20 001:135.225 - 0.710ms returns FALSE
T8C20 001:135.276 JLINK_HasError()
T8C20 001:136.535 JLINK_IsHalted()
T8C20 001:137.315 - 0.785ms returns FALSE
T8C20 001:137.327 JLINK_HasError()
T8C20 001:138.522 JLINK_IsHalted()
T8C20 001:139.209 - 0.694ms returns FALSE
T8C20 001:139.224 JLINK_HasError()
T8C20 001:140.537 JLINK_IsHalted()
T8C20 001:141.388 - 0.858ms returns FALSE
T8C20 001:141.402 JLINK_HasError()
T8C20 001:142.787 JLINK_IsHalted()
T8C20 001:143.645 - 0.871ms returns FALSE
T8C20 001:143.667 JLINK_HasError()
T8C20 001:144.993 JLINK_IsHalted()
T8C20 001:145.725 - 0.746ms returns FALSE
T8C20 001:145.748 JLINK_HasError()
T8C20 001:147.077 JLINK_IsHalted()
T8C20 001:147.836 - 0.777ms returns FALSE
T8C20 001:147.863 JLINK_HasError()
T8C20 001:149.047 JLINK_IsHalted()
T8C20 001:149.777 - 0.749ms returns FALSE
T8C20 001:149.803 JLINK_HasError()
T8C20 001:151.042 JLINK_IsHalted()
T8C20 001:151.965 - 0.947ms returns FALSE
T8C20 001:152.000 JLINK_HasError()
T8C20 001:153.488 JLINK_IsHalted()
T8C20 001:158.773   CPU_ReadMem(2 bytes @ 0x20000000)
T8C20 001:160.057 - 6.584ms returns TRUE
T8C20 001:160.087 JLINK_ReadReg(R15 (PC))
T8C20 001:160.102 - 0.021ms returns 0x20000000
T8C20 001:160.116 JLINK_ClrBPEx(BPHandle = 0x00000013)
T8C20 001:160.129 - 0.018ms returns 0x00
T8C20 001:160.144 JLINK_ReadReg(R0)
T8C20 001:160.156 - 0.017ms returns 0x00000000
T8C20 001:160.788 JLINK_WriteMem(0x200005C8, 0x238 Bytes, ...)
T8C20 001:160.812   Data:  DB FF 64 1C 04 2C F9 D3 10 BD 10 B5 00 24 20 46 ...
T8C20 001:160.837   CPU_WriteMem(568 bytes @ 0x200005C8)
T8C20 001:167.086 - 6.313ms returns 0x238
T8C20 001:167.112 JLINK_WriteMem(0x20000800, 0x400 Bytes, ...)
T8C20 001:167.123   Data:  C7 FE 25 70 40 F6 CD 41 00 22 38 68 FF F7 30 F8 ...
T8C20 001:167.148   CPU_WriteMem(1024 bytes @ 0x20000800)
T8C20 001:177.677 - 10.584ms returns 0x400
T8C20 001:177.706 JLINK_WriteMem(0x20000C00, 0x400 Bytes, ...)
T8C20 001:177.718   Data:  FA E7 70 47 EC 35 00 20 10 B5 D0 E9 02 34 D1 E9 ...
T8C20 001:177.742   CPU_WriteMem(1024 bytes @ 0x20000C00)
T8C20 001:188.131 - 10.446ms returns 0x400
T8C20 001:188.166 JLINK_WriteMem(0x20001000, 0x400 Bytes, ...)
T8C20 001:188.179   Data:  43 F8 31 00 03 20 00 EB 04 40 0E A1 01 F0 58 FB ...
T8C20 001:188.203   CPU_WriteMem(1024 bytes @ 0x20001000)
T8C20 001:198.614 - 10.468ms returns 0x400
T8C20 001:198.650 JLINK_WriteMem(0x20001400, 0x1C8 Bytes, ...)
T8C20 001:198.663   Data:  5C 02 1E 28 01 D3 01 20 60 70 9D F8 00 00 FE F7 ...
T8C20 001:198.688   CPU_WriteMem(456 bytes @ 0x20001400)
T8C20 001:203.949 - 5.318ms returns 0x1C8
T8C20 001:203.983 JLINK_HasError()
T8C20 001:203.997 JLINK_WriteReg(R0, 0x00028000)
T8C20 001:204.011 - 0.020ms returns 0
T8C20 001:204.048 JLINK_WriteReg(R1, 0x00001000)
T8C20 001:204.061 - 0.018ms returns 0
T8C20 001:204.073 JLINK_WriteReg(R2, 0x200005C8)
T8C20 001:204.084 - 0.016ms returns 0
T8C20 001:204.096 JLINK_WriteReg(R3, 0x00000000)
T8C20 001:204.107 - 0.016ms returns 0
T8C20 001:204.118 JLINK_WriteReg(R4, 0x00000000)
T8C20 001:204.130 - 0.017ms returns 0
T8C20 001:204.141 JLINK_WriteReg(R5, 0x00000000)
T8C20 001:204.152 - 0.016ms returns 0
T8C20 001:204.164 JLINK_WriteReg(R6, 0x00000000)
T8C20 001:204.175 - 0.016ms returns 0
T8C20 001:204.187 JLINK_WriteReg(R7, 0x00000000)
T8C20 001:204.198 - 0.021ms returns 0
T8C20 001:204.217 JLINK_WriteReg(R8, 0x00000000)
T8C20 001:204.228 - 0.016ms returns 0
T8C20 001:204.239 JLINK_WriteReg(R9, 0x200005B4)
T8C20 001:204.250 - 0.016ms returns 0
T8C20 001:204.262 JLINK_WriteReg(R10, 0x00000000)
T8C20 001:204.273 - 0.016ms returns 0
T8C20 001:204.285 JLINK_WriteReg(R11, 0x00000000)
T8C20 001:204.296 - 0.016ms returns 0
T8C20 001:204.307 JLINK_WriteReg(R12, 0x00000000)
T8C20 001:204.318 - 0.016ms returns 0
T8C20 001:204.330 JLINK_WriteReg(R13 (SP), 0x20002000)
T8C20 001:204.342 - 0.017ms returns 0
T8C20 001:204.353 JLINK_WriteReg(R14, 0x20000001)
T8C20 001:204.364 - 0.016ms returns 0
T8C20 001:204.376 JLINK_WriteReg(R15 (PC), 0x20000154)
T8C20 001:204.387 - 0.016ms returns 0
T8C20 001:204.399 JLINK_WriteReg(XPSR, 0x01000000)
T8C20 001:204.410 - 0.016ms returns 0
T8C20 001:204.422 JLINK_WriteReg(MSP, 0x20002000)
T8C20 001:204.433 - 0.016ms returns 0
T8C20 001:204.444 JLINK_WriteReg(PSP, 0x20002000)
T8C20 001:204.455 - 0.016ms returns 0
T8C20 001:204.467 JLINK_WriteReg(CFBP, 0x00000000)
T8C20 001:204.478 - 0.016ms returns 0
T8C20 001:204.490 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T8C20 001:204.503 - 0.017ms returns 0x00000014
T8C20 001:204.514 JLINK_Go()
T8C20 001:204.532   CPU_ReadMem(4 bytes @ 0xE0001000)
T8C20 001:210.655 - 6.158ms
T8C20 001:210.681 JLINK_IsHalted()
T8C20 001:211.387 - 0.716ms returns FALSE
T8C20 001:211.407 JLINK_HasError()
T8C20 001:217.045 JLINK_IsHalted()
T8C20 001:217.718 - 0.689ms returns FALSE
T8C20 001:217.744 JLINK_HasError()
T8C20 001:219.041 JLINK_IsHalted()
T8C20 001:219.673 - 0.642ms returns FALSE
T8C20 001:219.695 JLINK_HasError()
T8C20 001:221.117 JLINK_IsHalted()
T8C20 001:222.058 - 0.954ms returns FALSE
T8C20 001:222.081 JLINK_HasError()
T8C20 001:226.719 JLINK_IsHalted()
T8C20 001:227.396 - 0.693ms returns FALSE
T8C20 001:227.421 JLINK_HasError()
T8C20 001:229.131 JLINK_IsHalted()
T8C20 001:229.870 - 0.750ms returns FALSE
T8C20 001:229.890 JLINK_HasError()
T8C20 001:231.037 JLINK_IsHalted()
T8C20 001:231.734 - 0.702ms returns FALSE
T8C20 001:231.747 JLINK_HasError()
T8C20 001:233.388 JLINK_IsHalted()
T8C20 001:234.139 - 0.764ms returns FALSE
T8C20 001:234.161 JLINK_HasError()
T8C20 001:235.493 JLINK_IsHalted()
T8C20 001:236.221 - 0.735ms returns FALSE
T8C20 001:236.234 JLINK_HasError()
T8C20 001:238.074 JLINK_IsHalted()
T8C20 001:238.794 - 0.732ms returns FALSE
T8C20 001:238.815 JLINK_HasError()
T8C20 001:240.119 JLINK_IsHalted()
T8C20 001:240.896 - 0.783ms returns FALSE
T8C20 001:240.909 JLINK_HasError()
T8C20 001:242.178 JLINK_IsHalted()
T8C20 001:242.844 - 0.681ms returns FALSE
T8C20 001:242.869 JLINK_HasError()
T8C20 001:244.446 JLINK_IsHalted()
T8C20 001:245.089 - 0.658ms returns FALSE
T8C20 001:245.113 JLINK_HasError()
T8C20 001:246.436 JLINK_IsHalted()
T8C20 001:247.126 - 0.702ms returns FALSE
T8C20 001:247.147 JLINK_HasError()
T8C20 001:248.447 JLINK_IsHalted()
T8C20 001:249.222 - 0.781ms returns FALSE
T8C20 001:249.235 JLINK_HasError()
T8C20 001:250.460 JLINK_IsHalted()
T8C20 001:251.168 - 0.714ms returns FALSE
T8C20 001:251.181 JLINK_HasError()
T8C20 001:252.448 JLINK_IsHalted()
T8C20 001:253.422 - 0.986ms returns FALSE
T8C20 001:253.442 JLINK_HasError()
T8C20 001:254.915 JLINK_IsHalted()
T8C20 001:255.712 - 0.811ms returns FALSE
T8C20 001:255.738 JLINK_HasError()
T8C20 001:256.938 JLINK_IsHalted()
T8C20 001:257.727 - 0.799ms returns FALSE
T8C20 001:257.745 JLINK_HasError()
T8C20 001:258.954 JLINK_IsHalted()
T8C20 001:259.735 - 0.787ms returns FALSE
T8C20 001:259.748 JLINK_HasError()
T8C20 001:260.888 JLINK_IsHalted()
T8C20 001:261.519 - 0.639ms returns FALSE
T8C20 001:261.534 JLINK_HasError()
T8C20 001:262.941 JLINK_IsHalted()
T8C20 001:263.667 - 0.741ms returns FALSE
T8C20 001:263.690 JLINK_HasError()
T8C20 001:264.999 JLINK_IsHalted()
T8C20 001:265.706 - 0.718ms returns FALSE
T8C20 001:265.727 JLINK_HasError()
T8C20 001:267.029 JLINK_IsHalted()
T8C20 001:267.765 - 0.757ms returns FALSE
T8C20 001:267.793 JLINK_HasError()
T8C20 001:269.360 JLINK_IsHalted()
T8C20 001:270.032 - 0.737ms returns FALSE
T8C20 001:270.111 JLINK_HasError()
T8C20 001:275.474 JLINK_IsHalted()
T8C20 001:276.174 - 0.717ms returns FALSE
T8C20 001:276.201 JLINK_HasError()
T8C20 001:277.426 JLINK_IsHalted()
T8C20 001:278.211 - 0.802ms returns FALSE
T8C20 001:278.237 JLINK_HasError()
T8C20 001:279.602 JLINK_IsHalted()
T8C20 001:280.290 - 0.711ms returns FALSE
T8C20 001:280.328 JLINK_HasError()
T8C20 001:281.830 JLINK_IsHalted()
T8C20 001:286.668   CPU_ReadMem(2 bytes @ 0x20000000)
T8C20 001:287.301 - 5.480ms returns TRUE
T8C20 001:287.343 JLINK_ReadReg(R15 (PC))
T8C20 001:287.357 - 0.020ms returns 0x20000000
T8C20 001:287.369 JLINK_ClrBPEx(BPHandle = 0x00000014)
T8C20 001:287.381 - 0.017ms returns 0x00
T8C20 001:287.393 JLINK_ReadReg(R0)
T8C20 001:287.405 - 0.016ms returns 0x00000000
T8C20 001:288.145 JLINK_WriteMem(0x200005C8, 0x238 Bytes, ...)
T8C20 001:288.166   Data:  0C 30 FF F7 86 FF 00 28 F5 D1 01 AA 00 99 01 EA ...
T8C20 001:288.190   CPU_WriteMem(568 bytes @ 0x200005C8)
T8C20 001:294.385 - 6.269ms returns 0x238
T8C20 001:294.425 JLINK_WriteMem(0x20000800, 0x400 Bytes, ...)
T8C20 001:294.457   Data:  0C 00 00 50 70 B5 14 46 82 88 0E 46 05 46 20 88 ...
T8C20 001:294.482   CPU_WriteMem(1024 bytes @ 0x20000800)
T8C20 001:304.956 - 10.560ms returns 0x400
T8C20 001:304.997 JLINK_WriteMem(0x20000C00, 0x400 Bytes, ...)
T8C20 001:305.009   Data:  40 89 34 F8 06 1F 81 42 00 D8 08 46 60 80 17 28 ...
T8C20 001:305.036   CPU_WriteMem(1024 bytes @ 0x20000C00)
T8C20 001:315.503 - 10.536ms returns 0x400
T8C20 001:315.552 JLINK_WriteMem(0x20001000, 0x400 Bytes, ...)
T8C20 001:315.565   Data:  21 78 A1 F1 30 02 0A 2A 11 D2 05 EB 85 02 01 EB ...
T8C20 001:315.593   CPU_WriteMem(1024 bytes @ 0x20001000)
T8C20 001:326.144 - 10.620ms returns 0x400
T8C20 001:326.191 JLINK_WriteMem(0x20001400, 0x1C8 Bytes, ...)
T8C20 001:326.204   Data:  C1 F3 C2 00 8C 0D 8D F8 20 00 08 2C 01 D2 27 46 ...
T8C20 001:326.231   CPU_WriteMem(456 bytes @ 0x20001400)
T8C20 001:331.458 - 5.297ms returns 0x1C8
T8C20 001:331.506 JLINK_HasError()
T8C20 001:331.523 JLINK_WriteReg(R0, 0x00029000)
T8C20 001:331.540 - 0.023ms returns 0
T8C20 001:331.554 JLINK_WriteReg(R1, 0x00001000)
T8C20 001:331.567 - 0.018ms returns 0
T8C20 001:331.581 JLINK_WriteReg(R2, 0x200005C8)
T8C20 001:331.592 - 0.017ms returns 0
T8C20 001:331.606 JLINK_WriteReg(R3, 0x00000000)
T8C20 001:331.617 - 0.017ms returns 0
T8C20 001:331.631 JLINK_WriteReg(R4, 0x00000000)
T8C20 001:331.643 - 0.017ms returns 0
T8C20 001:331.656 JLINK_WriteReg(R5, 0x00000000)
T8C20 001:331.668 - 0.017ms returns 0
T8C20 001:331.681 JLINK_WriteReg(R6, 0x00000000)
T8C20 001:331.693 - 0.017ms returns 0
T8C20 001:331.707 JLINK_WriteReg(R7, 0x00000000)
T8C20 001:331.718 - 0.016ms returns 0
T8C20 001:331.731 JLINK_WriteReg(R8, 0x00000000)
T8C20 001:331.743 - 0.017ms returns 0
T8C20 001:331.757 JLINK_WriteReg(R9, 0x200005B4)
T8C20 001:331.768 - 0.017ms returns 0
T8C20 001:331.782 JLINK_WriteReg(R10, 0x00000000)
T8C20 001:331.793 - 0.017ms returns 0
T8C20 001:331.807 JLINK_WriteReg(R11, 0x00000000)
T8C20 001:331.818 - 0.017ms returns 0
T8C20 001:331.832 JLINK_WriteReg(R12, 0x00000000)
T8C20 001:331.844 - 0.017ms returns 0
T8C20 001:331.857 JLINK_WriteReg(R13 (SP), 0x20002000)
T8C20 001:331.870 - 0.018ms returns 0
T8C20 001:331.884 JLINK_WriteReg(R14, 0x20000001)
T8C20 001:331.895 - 0.017ms returns 0
T8C20 001:331.909 JLINK_WriteReg(R15 (PC), 0x20000154)
T8C20 001:331.921 - 0.017ms returns 0
T8C20 001:331.934 JLINK_WriteReg(XPSR, 0x01000000)
T8C20 001:331.946 - 0.017ms returns 0
T8C20 001:331.960 JLINK_WriteReg(MSP, 0x20002000)
T8C20 001:331.971 - 0.017ms returns 0
T8C20 001:331.985 JLINK_WriteReg(PSP, 0x20002000)
T8C20 001:331.997 - 0.017ms returns 0
T8C20 001:332.010 JLINK_WriteReg(CFBP, 0x00000000)
T8C20 001:332.022 - 0.020ms returns 0
T8C20 001:332.040 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T8C20 001:332.053 - 0.018ms returns 0x00000015
T8C20 001:332.066 JLINK_Go()
T8C20 001:332.094   CPU_ReadMem(4 bytes @ 0xE0001000)
T8C20 001:337.720 - 5.673ms
T8C20 001:337.755 JLINK_IsHalted()
T8C20 001:338.424 - 0.678ms returns FALSE
T8C20 001:338.445 JLINK_HasError()
T8C20 001:340.061 JLINK_IsHalted()
T8C20 001:340.818 - 0.769ms returns FALSE
T8C20 001:340.838 JLINK_HasError()
T8C20 001:342.089 JLINK_IsHalted()
T8C20 001:342.861 - 0.779ms returns FALSE
T8C20 001:342.876 JLINK_HasError()
T8C20 001:344.153 JLINK_IsHalted()
T8C20 001:344.854 - 0.719ms returns FALSE
T8C20 001:344.882 JLINK_HasError()
T8C20 001:346.167 JLINK_IsHalted()
T8C20 001:346.911 - 0.759ms returns FALSE
T8C20 001:346.936 JLINK_HasError()
T8C20 001:348.161 JLINK_IsHalted()
T8C20 001:348.929 - 0.783ms returns FALSE
T8C20 001:348.954 JLINK_HasError()
T8C20 001:350.793 JLINK_IsHalted()
T8C20 001:351.512 - 0.726ms returns FALSE
T8C20 001:351.527 JLINK_HasError()
T8C20 001:353.290 JLINK_IsHalted()
T8C20 001:354.174 - 0.897ms returns FALSE
T8C20 001:354.196 JLINK_HasError()
T8C20 001:355.349 JLINK_IsHalted()
T8C20 001:356.072 - 0.735ms returns FALSE
T8C20 001:356.093 JLINK_HasError()
T8C20 001:357.301 JLINK_IsHalted()
T8C20 001:357.998 - 0.705ms returns FALSE
T8C20 001:358.014 JLINK_HasError()
T8C20 001:360.476 JLINK_IsHalted()
T8C20 001:361.235 - 0.775ms returns FALSE
T8C20 001:361.260 JLINK_HasError()
T8C20 001:363.044 JLINK_IsHalted()
T8C20 001:363.821 - 0.786ms returns FALSE
T8C20 001:363.839 JLINK_HasError()
T8C20 001:365.123 JLINK_IsHalted()
T8C20 001:365.900 - 0.789ms returns FALSE
T8C20 001:365.919 JLINK_HasError()
T8C20 001:367.049 JLINK_IsHalted()
T8C20 001:367.752 - 0.711ms returns FALSE
T8C20 001:367.768 JLINK_HasError()
T8C20 001:369.042 JLINK_IsHalted()
T8C20 001:369.763 - 0.730ms returns FALSE
T8C20 001:369.780 JLINK_HasError()
T8C20 001:372.220 JLINK_IsHalted()
T8C20 001:372.859 - 0.648ms returns FALSE
T8C20 001:372.875 JLINK_HasError()
T8C20 001:374.244 JLINK_IsHalted()
T8C20 001:374.900 - 0.682ms returns FALSE
T8C20 001:374.939 JLINK_HasError()
T8C20 001:376.192 JLINK_IsHalted()
T8C20 001:376.809 - 0.629ms returns FALSE
T8C20 001:376.831 JLINK_HasError()
T8C20 001:382.215 JLINK_IsHalted()
T8C20 001:382.901 - 0.703ms returns FALSE
T8C20 001:382.927 JLINK_HasError()
T8C20 001:384.246 JLINK_IsHalted()
T8C20 001:384.998 - 0.758ms returns FALSE
T8C20 001:385.013 JLINK_HasError()
T8C20 001:386.293 JLINK_IsHalted()
T8C20 001:386.993 - 0.707ms returns FALSE
T8C20 001:387.007 JLINK_HasError()
T8C20 001:388.235 JLINK_IsHalted()
T8C20 001:388.923 - 0.695ms returns FALSE
T8C20 001:388.938 JLINK_HasError()
T8C20 001:390.286 JLINK_IsHalted()
T8C20 001:390.988 - 0.708ms returns FALSE
T8C20 001:391.002 JLINK_HasError()
T8C20 001:392.407 JLINK_IsHalted()
T8C20 001:393.183 - 0.789ms returns FALSE
T8C20 001:393.205 JLINK_HasError()
T8C20 001:395.391 JLINK_IsHalted()
T8C20 001:396.183 - 0.809ms returns FALSE
T8C20 001:396.209 JLINK_HasError()
T8C20 001:397.386 JLINK_IsHalted()
T8C20 001:398.144 - 0.764ms returns FALSE
T8C20 001:398.157 JLINK_HasError()
T8C20 001:399.331 JLINK_IsHalted()
T8C20 001:400.096 - 0.770ms returns FALSE
T8C20 001:400.108 JLINK_HasError()
T8C20 001:401.331 JLINK_IsHalted()
T8C20 001:402.070 - 0.745ms returns FALSE
T8C20 001:402.084 JLINK_HasError()
T8C20 001:403.490 JLINK_IsHalted()
T8C20 001:404.267 - 0.783ms returns FALSE
T8C20 001:404.280 JLINK_HasError()
T8C20 001:405.487 JLINK_IsHalted()
T8C20 001:406.509 - 1.028ms returns FALSE
T8C20 001:406.522 JLINK_HasError()
T8C20 001:408.014 JLINK_IsHalted()
T8C20 001:412.952   CPU_ReadMem(2 bytes @ 0x20000000)
T8C20 001:413.721 - 5.721ms returns TRUE
T8C20 001:413.744 JLINK_ReadReg(R15 (PC))
T8C20 001:413.758 - 0.019ms returns 0x20000000
T8C20 001:413.769 JLINK_ClrBPEx(BPHandle = 0x00000015)
T8C20 001:413.780 - 0.016ms returns 0x00
T8C20 001:413.792 JLINK_ReadReg(R0)
T8C20 001:413.802 - 0.015ms returns 0x00000000
T8C20 001:414.245 JLINK_WriteMem(0x200005C8, 0x238 Bytes, ...)
T8C20 001:414.262   Data:  82 F9 07 00 4A D0 00 F0 C7 F9 43 46 08 22 09 A9 ...
T8C20 001:414.292   CPU_WriteMem(568 bytes @ 0x200005C8)
T8C20 001:420.732 - 6.499ms returns 0x238
T8C20 001:420.757 JLINK_WriteMem(0x20000800, 0x400 Bytes, ...)
T8C20 001:420.770   Data:  45 71 21 68 20 46 49 68 88 47 E0 68 04 68 00 2C ...
T8C20 001:420.791   CPU_WriteMem(1024 bytes @ 0x20000800)
T8C20 001:431.268 - 10.527ms returns 0x400
T8C20 001:431.300 JLINK_WriteMem(0x20000C00, 0x400 Bytes, ...)
T8C20 001:431.312   Data:  42 1A 20 46 25 A1 FF F7 5F FD B8 F1 00 00 4F F0 ...
T8C20 001:431.333   CPU_WriteMem(1024 bytes @ 0x20000C00)
T8C20 001:441.961 - 10.678ms returns 0x400
T8C20 001:441.993 JLINK_WriteMem(0x20001000, 0x400 Bytes, ...)
T8C20 001:442.006   Data:  08 47 70 47 31 C1 02 00 2D E9 F0 41 0E 46 05 46 ...
T8C20 001:442.030   CPU_WriteMem(1024 bytes @ 0x20001000)
T8C20 001:452.651 - 10.686ms returns 0x400
T8C20 001:452.705 JLINK_WriteMem(0x20001400, 0x1C8 Bytes, ...)
T8C20 001:452.728   Data:  3A 46 21 46 28 68 BD E8 F0 41 00 F0 41 BE 00 00 ...
T8C20 001:452.768   CPU_WriteMem(456 bytes @ 0x20001400)
T8C20 001:458.322 - 5.639ms returns 0x1C8
T8C20 001:458.361 JLINK_HasError()
T8C20 001:458.379 JLINK_WriteReg(R0, 0x0002A000)
T8C20 001:458.397 - 0.024ms returns 0
T8C20 001:458.413 JLINK_WriteReg(R1, 0x00001000)
T8C20 001:458.426 - 0.019ms returns 0
T8C20 001:458.441 JLINK_WriteReg(R2, 0x200005C8)
T8C20 001:458.455 - 0.019ms returns 0
T8C20 001:458.470 JLINK_WriteReg(R3, 0x00000000)
T8C20 001:458.483 - 0.019ms returns 0
T8C20 001:458.499 JLINK_WriteReg(R4, 0x00000000)
T8C20 001:458.512 - 0.019ms returns 0
T8C20 001:458.527 JLINK_WriteReg(R5, 0x00000000)
T8C20 001:458.540 - 0.019ms returns 0
T8C20 001:458.556 JLINK_WriteReg(R6, 0x00000000)
T8C20 001:458.569 - 0.019ms returns 0
T8C20 001:458.584 JLINK_WriteReg(R7, 0x00000000)
T8C20 001:458.597 - 0.019ms returns 0
T8C20 001:458.612 JLINK_WriteReg(R8, 0x00000000)
T8C20 001:458.625 - 0.019ms returns 0
T8C20 001:458.640 JLINK_WriteReg(R9, 0x200005B4)
T8C20 001:458.654 - 0.019ms returns 0
T8C20 001:458.669 JLINK_WriteReg(R10, 0x00000000)
T8C20 001:458.682 - 0.019ms returns 0
T8C20 001:458.697 JLINK_WriteReg(R11, 0x00000000)
T8C20 001:458.710 - 0.019ms returns 0
T8C20 001:458.726 JLINK_WriteReg(R12, 0x00000000)
T8C20 001:458.737 - 0.017ms returns 0
T8C20 001:458.750 JLINK_WriteReg(R13 (SP), 0x20002000)
T8C20 001:458.763 - 0.017ms returns 0
T8C20 001:458.776 JLINK_WriteReg(R14, 0x20000001)
T8C20 001:458.788 - 0.017ms returns 0
T8C20 001:458.801 JLINK_WriteReg(R15 (PC), 0x20000154)
T8C20 001:458.812 - 0.017ms returns 0
T8C20 001:458.826 JLINK_WriteReg(XPSR, 0x01000000)
T8C20 001:458.838 - 0.017ms returns 0
T8C20 001:458.850 JLINK_WriteReg(MSP, 0x20002000)
T8C20 001:458.862 - 0.016ms returns 0
T8C20 001:458.875 JLINK_WriteReg(PSP, 0x20002000)
T8C20 001:458.887 - 0.016ms returns 0
T8C20 001:458.900 JLINK_WriteReg(CFBP, 0x00000000)
T8C20 001:458.911 - 0.017ms returns 0
T8C20 001:458.925 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T8C20 001:458.937 - 0.022ms returns 0x00000016
T8C20 001:458.955 JLINK_Go()
T8C20 001:458.974   CPU_ReadMem(4 bytes @ 0xE0001000)
T8C20 001:465.267 - 6.327ms
T8C20 001:465.294 JLINK_IsHalted()
T8C20 001:466.323 - 1.042ms returns FALSE
T8C20 001:466.349 JLINK_HasError()
T8C20 001:467.792 JLINK_IsHalted()
T8C20 001:468.545 - 0.783ms returns FALSE
T8C20 001:468.586 JLINK_HasError()
T8C20 001:469.699 JLINK_IsHalted()
T8C20 001:470.871 - 1.185ms returns FALSE
T8C20 001:470.893 JLINK_HasError()
T8C20 001:472.270 JLINK_IsHalted()
T8C20 001:473.128 - 0.867ms returns FALSE
T8C20 001:473.146 JLINK_HasError()
T8C20 001:474.587 JLINK_IsHalted()
T8C20 001:475.360 - 0.780ms returns FALSE
T8C20 001:475.377 JLINK_HasError()
T8C20 001:476.553 JLINK_IsHalted()
T8C20 001:477.412 - 0.871ms returns FALSE
T8C20 001:477.434 JLINK_HasError()
T8C20 001:478.573 JLINK_IsHalted()
T8C20 001:479.338 - 0.782ms returns FALSE
T8C20 001:479.367 JLINK_HasError()
T8C20 001:480.594 JLINK_IsHalted()
T8C20 001:481.531 - 0.945ms returns FALSE
T8C20 001:481.549 JLINK_HasError()
T8C20 001:483.136 JLINK_IsHalted()
T8C20 001:483.941 - 0.817ms returns FALSE
T8C20 001:483.965 JLINK_HasError()
T8C20 001:485.280 JLINK_IsHalted()
T8C20 001:486.058 - 0.794ms returns FALSE
T8C20 001:486.087 JLINK_HasError()
T8C20 001:490.434 JLINK_IsHalted()
T8C20 001:491.233 - 0.805ms returns FALSE
T8C20 001:491.247 JLINK_HasError()
T8C20 001:492.428 JLINK_IsHalted()
T8C20 001:493.216 - 0.794ms returns FALSE
T8C20 001:493.228 JLINK_HasError()
T8C20 001:494.580 JLINK_IsHalted()
T8C20 001:495.273 - 0.707ms returns FALSE
T8C20 001:495.297 JLINK_HasError()
T8C20 001:496.683 JLINK_IsHalted()
T8C20 001:497.445 - 0.768ms returns FALSE
T8C20 001:497.459 JLINK_HasError()
T8C20 001:498.681 JLINK_IsHalted()
T8C20 001:499.409 - 0.733ms returns FALSE
T8C20 001:499.421 JLINK_HasError()
T8C20 001:500.792 JLINK_IsHalted()
T8C20 001:501.724 - 0.941ms returns FALSE
T8C20 001:501.741 JLINK_HasError()
T8C20 001:503.464 JLINK_IsHalted()
T8C20 001:504.231 - 0.774ms returns FALSE
T8C20 001:504.245 JLINK_HasError()
T8C20 001:505.512 JLINK_IsHalted()
T8C20 001:506.293 - 0.788ms returns FALSE
T8C20 001:506.307 JLINK_HasError()
T8C20 001:507.514 JLINK_IsHalted()
T8C20 001:508.262 - 0.755ms returns FALSE
T8C20 001:508.277 JLINK_HasError()
T8C20 001:509.517 JLINK_IsHalted()
T8C20 001:510.263 - 0.753ms returns FALSE
T8C20 001:510.279 JLINK_HasError()
T8C20 001:512.623 JLINK_IsHalted()
T8C20 001:513.293 - 0.685ms returns FALSE
T8C20 001:513.319 JLINK_HasError()
T8C20 001:514.593 JLINK_IsHalted()
T8C20 001:515.486 - 0.901ms returns FALSE
T8C20 001:515.504 JLINK_HasError()
T8C20 001:516.703 JLINK_IsHalted()
T8C20 001:517.478 - 0.786ms returns FALSE
T8C20 001:517.500 JLINK_HasError()
T8C20 001:525.693 JLINK_IsHalted()
T8C20 001:526.425 - 0.739ms returns FALSE
T8C20 001:526.440 JLINK_HasError()
T8C20 001:528.547 JLINK_IsHalted()
T8C20 001:529.186 - 0.654ms returns FALSE
T8C20 001:529.209 JLINK_HasError()
T8C20 001:530.595 JLINK_IsHalted()
T8C20 001:531.359 - 0.772ms returns FALSE
T8C20 001:531.375 JLINK_HasError()
T8C20 001:532.910 JLINK_IsHalted()
T8C20 001:533.725 - 0.824ms returns FALSE
T8C20 001:533.743 JLINK_HasError()
T8C20 001:534.981 JLINK_IsHalted()
T8C20 001:539.866   CPU_ReadMem(2 bytes @ 0x20000000)
T8C20 001:540.606 - 5.630ms returns TRUE
T8C20 001:540.619 JLINK_ReadReg(R15 (PC))
T8C20 001:540.633 - 0.018ms returns 0x20000000
T8C20 001:540.644 JLINK_ClrBPEx(BPHandle = 0x00000016)
T8C20 001:540.655 - 0.017ms returns 0x00
T8C20 001:540.667 JLINK_ReadReg(R0)
T8C20 001:540.678 - 0.015ms returns 0x00000000
T8C20 001:541.144 JLINK_WriteMem(0x200005C8, 0x238 Bytes, ...)
T8C20 001:541.161   Data:  B3 FC 00 28 F9 D0 28 68 C7 60 C4 F8 14 90 30 46 ...
T8C20 001:541.184   CPU_WriteMem(568 bytes @ 0x200005C8)
T8C20 001:547.560 - 6.430ms returns 0x238
T8C20 001:547.598 JLINK_WriteMem(0x20000800, 0x400 Bytes, ...)
T8C20 001:547.647   Data:  E0 2A 00 20 DC 2C 00 20 84 34 00 20 6F 6E 01 00 ...
T8C20 001:547.681   CPU_WriteMem(1024 bytes @ 0x20000800)
T8C20 001:558.305 - 10.721ms returns 0x400
T8C20 001:558.332 JLINK_WriteMem(0x20000C00, 0x400 Bytes, ...)
T8C20 001:558.343   Data:  33 B1 14 A0 00 90 15 A2 16 A1 28 46 FE F7 B0 F9 ...
T8C20 001:558.364   CPU_WriteMem(1024 bytes @ 0x20000C00)
T8C20 001:568.873 - 10.562ms returns 0x400
T8C20 001:568.911 JLINK_WriteMem(0x20001000, 0x400 Bytes, ...)
T8C20 001:568.926   Data:  81 40 11 F0 EC 0F 0C D0 0C 48 82 68 4F F4 80 01 ...
T8C20 001:568.953   CPU_WriteMem(1024 bytes @ 0x20001000)
T8C20 001:579.278 - 10.393ms returns 0x400
T8C20 001:579.327 JLINK_WriteMem(0x20001400, 0x1C8 Bytes, ...)
T8C20 001:579.346   Data:  5F F0 01 06 B9 F1 01 0F 02 D9 A9 F1 01 09 03 E0 ...
T8C20 001:579.382   CPU_WriteMem(456 bytes @ 0x20001400)
T8C20 001:584.830 - 5.519ms returns 0x1C8
T8C20 001:584.859 JLINK_HasError()
T8C20 001:584.876 JLINK_WriteReg(R0, 0x0002B000)
T8C20 001:584.892 - 0.020ms returns 0
T8C20 001:584.905 JLINK_WriteReg(R1, 0x00001000)
T8C20 001:584.917 - 0.017ms returns 0
T8C20 001:584.929 JLINK_WriteReg(R2, 0x200005C8)
T8C20 001:584.941 - 0.016ms returns 0
T8C20 001:584.958 JLINK_WriteReg(R3, 0x00000000)
T8C20 001:584.972 - 0.018ms returns 0
T8C20 001:584.984 JLINK_WriteReg(R4, 0x00000000)
T8C20 001:584.996 - 0.016ms returns 0
T8C20 001:585.009 JLINK_WriteReg(R5, 0x00000000)
T8C20 001:585.020 - 0.016ms returns 0
T8C20 001:585.033 JLINK_WriteReg(R6, 0x00000000)
T8C20 001:585.044 - 0.016ms returns 0
T8C20 001:585.057 JLINK_WriteReg(R7, 0x00000000)
T8C20 001:585.068 - 0.016ms returns 0
T8C20 001:585.081 JLINK_WriteReg(R8, 0x00000000)
T8C20 001:585.092 - 0.016ms returns 0
T8C20 001:585.105 JLINK_WriteReg(R9, 0x200005B4)
T8C20 001:585.116 - 0.016ms returns 0
T8C20 001:585.128 JLINK_WriteReg(R10, 0x00000000)
T8C20 001:585.140 - 0.016ms returns 0
T8C20 001:585.153 JLINK_WriteReg(R11, 0x00000000)
T8C20 001:585.164 - 0.016ms returns 0
T8C20 001:585.176 JLINK_WriteReg(R12, 0x00000000)
T8C20 001:585.188 - 0.016ms returns 0
T8C20 001:585.201 JLINK_WriteReg(R13 (SP), 0x20002000)
T8C20 001:585.213 - 0.017ms returns 0
T8C20 001:585.225 JLINK_WriteReg(R14, 0x20000001)
T8C20 001:585.236 - 0.016ms returns 0
T8C20 001:585.249 JLINK_WriteReg(R15 (PC), 0x20000154)
T8C20 001:585.260 - 0.016ms returns 0
T8C20 001:585.273 JLINK_WriteReg(XPSR, 0x01000000)
T8C20 001:585.284 - 0.016ms returns 0
T8C20 001:585.296 JLINK_WriteReg(MSP, 0x20002000)
T8C20 001:585.308 - 0.016ms returns 0
T8C20 001:585.320 JLINK_WriteReg(PSP, 0x20002000)
T8C20 001:585.331 - 0.016ms returns 0
T8C20 001:585.343 JLINK_WriteReg(CFBP, 0x00000000)
T8C20 001:585.354 - 0.015ms returns 0
T8C20 001:585.367 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T8C20 001:585.379 - 0.017ms returns 0x00000017
T8C20 001:585.391 JLINK_Go()
T8C20 001:585.408   CPU_ReadMem(4 bytes @ 0xE0001000)
T8C20 001:591.117 - 5.737ms
T8C20 001:591.140 JLINK_IsHalted()
T8C20 001:591.782 - 0.705ms returns FALSE
T8C20 001:591.857 JLINK_HasError()
T8C20 001:593.292 JLINK_IsHalted()
T8C20 001:594.051 - 0.796ms returns FALSE
T8C20 001:594.097 JLINK_HasError()
T8C20 001:598.351 JLINK_IsHalted()
T8C20 001:599.105 - 0.760ms returns FALSE
T8C20 001:599.119 JLINK_HasError()
T8C20 001:600.353 JLINK_IsHalted()
T8C20 001:601.004 - 0.656ms returns FALSE
T8C20 001:601.016 JLINK_HasError()
T8C20 001:602.351 JLINK_IsHalted()
T8C20 001:603.120 - 0.775ms returns FALSE
T8C20 001:603.133 JLINK_HasError()
T8C20 001:604.467 JLINK_IsHalted()
T8C20 001:605.267 - 0.813ms returns FALSE
T8C20 001:605.289 JLINK_HasError()
T8C20 001:606.526 JLINK_IsHalted()
T8C20 001:607.266 - 0.751ms returns FALSE
T8C20 001:607.285 JLINK_HasError()
T8C20 001:608.550 JLINK_IsHalted()
T8C20 001:609.377 - 0.843ms returns FALSE
T8C20 001:609.403 JLINK_HasError()
T8C20 001:610.925 JLINK_IsHalted()
T8C20 001:611.639 - 0.729ms returns FALSE
T8C20 001:611.663 JLINK_HasError()
T8C20 001:613.187 JLINK_IsHalted()
T8C20 001:613.927 - 0.745ms returns FALSE
T8C20 001:613.940 JLINK_HasError()
T8C20 001:615.269 JLINK_IsHalted()
T8C20 001:616.072 - 0.814ms returns FALSE
T8C20 001:616.091 JLINK_HasError()
T8C20 001:617.289 JLINK_IsHalted()
T8C20 001:618.030 - 0.750ms returns FALSE
T8C20 001:618.047 JLINK_HasError()
T8C20 001:619.780 JLINK_IsHalted()
T8C20 001:620.487 - 0.712ms returns FALSE
T8C20 001:620.499 JLINK_HasError()
T8C20 001:622.331 JLINK_IsHalted()
T8C20 001:623.065 - 0.754ms returns FALSE
T8C20 001:623.096 JLINK_HasError()
T8C20 001:624.402 JLINK_IsHalted()
T8C20 001:625.295 - 0.906ms returns FALSE
T8C20 001:625.317 JLINK_HasError()
T8C20 001:626.557 JLINK_IsHalted()
T8C20 001:627.275 - 0.724ms returns FALSE
T8C20 001:627.289 JLINK_HasError()
T8C20 001:628.554 JLINK_IsHalted()
T8C20 001:629.221 - 0.673ms returns FALSE
T8C20 001:629.234 JLINK_HasError()
T8C20 001:630.615 JLINK_IsHalted()
T8C20 001:631.330 - 0.725ms returns FALSE
T8C20 001:631.348 JLINK_HasError()
T8C20 001:633.136 JLINK_IsHalted()
T8C20 001:633.861 - 0.733ms returns FALSE
T8C20 001:633.876 JLINK_HasError()
T8C20 001:635.070 JLINK_IsHalted()
T8C20 001:635.771 - 0.706ms returns FALSE
T8C20 001:635.782 JLINK_HasError()
T8C20 001:637.126 JLINK_IsHalted()
T8C20 001:637.858 - 0.738ms returns FALSE
T8C20 001:637.869 JLINK_HasError()
T8C20 001:639.147 JLINK_IsHalted()
T8C20 001:639.851 - 0.709ms returns FALSE
T8C20 001:639.863 JLINK_HasError()
T8C20 001:641.738 JLINK_IsHalted()
T8C20 001:642.465 - 0.742ms returns FALSE
T8C20 001:642.495 JLINK_HasError()
T8C20 001:643.795 JLINK_IsHalted()
T8C20 001:644.727 - 0.961ms returns FALSE
T8C20 001:644.774 JLINK_HasError()
T8C20 001:646.509 JLINK_IsHalted()
T8C20 001:647.255 - 0.755ms returns FALSE
T8C20 001:647.268 JLINK_HasError()
T8C20 001:649.915 JLINK_IsHalted()
T8C20 001:650.804 - 0.897ms returns FALSE
T8C20 001:650.821 JLINK_HasError()
T8C20 001:652.365 JLINK_IsHalted()
T8C20 001:653.107 - 0.747ms returns FALSE
T8C20 001:653.119 JLINK_HasError()
T8C20 001:654.417 JLINK_IsHalted()
T8C20 001:655.173 - 0.769ms returns FALSE
T8C20 001:655.195 JLINK_HasError()
T8C20 001:656.432 JLINK_IsHalted()
T8C20 001:657.174 - 0.758ms returns FALSE
T8C20 001:657.200 JLINK_HasError()
T8C20 001:659.321 JLINK_IsHalted()
T8C20 001:660.088 - 0.784ms returns FALSE
T8C20 001:660.114 JLINK_HasError()
T8C20 001:662.470 JLINK_IsHalted()
T8C20 001:667.295   CPU_ReadMem(2 bytes @ 0x20000000)
T8C20 001:668.039 - 5.577ms returns TRUE
T8C20 001:668.060 JLINK_ReadReg(R15 (PC))
T8C20 001:668.073 - 0.018ms returns 0x20000000
T8C20 001:668.086 JLINK_ClrBPEx(BPHandle = 0x00000017)
T8C20 001:668.096 - 0.015ms returns 0x00
T8C20 001:668.108 JLINK_ReadReg(R0)
T8C20 001:668.119 - 0.015ms returns 0x00000000
T8C20 001:668.656 JLINK_WriteMem(0x200005C8, 0x238 Bytes, ...)
T8C20 001:668.673   Data:  FC 9F 09 F8 07 00 00 20 F9 E7 10 B5 04 28 17 D2 ...
T8C20 001:668.700   CPU_WriteMem(568 bytes @ 0x200005C8)
T8C20 001:675.180 - 6.538ms returns 0x238
T8C20 001:675.202 JLINK_WriteMem(0x20000800, 0x400 Bytes, ...)
T8C20 001:675.212   Data:  83 00 00 00 00 C5 02 00 84 00 00 00 3C C5 02 00 ...
T8C20 001:675.233   CPU_WriteMem(1024 bytes @ 0x20000800)
T8C20 001:685.724 - 10.538ms returns 0x400
T8C20 001:685.748 JLINK_WriteMem(0x20000C00, 0x400 Bytes, ...)
T8C20 001:685.758   Data:  5F 49 4E 49 54 49 41 4C 49 5A 45 44 00 00 00 00 ...
T8C20 001:685.778   CPU_WriteMem(1024 bytes @ 0x20000C00)
T8C20 001:696.215 - 10.482ms returns 0x400
T8C20 001:696.244 JLINK_WriteMem(0x20001000, 0x400 Bytes, ...)
T8C20 001:696.255   Data:  6C 65 5F 63 66 67 5F 73 65 74 28 29 20 72 65 74 ...
T8C20 001:696.276   CPU_WriteMem(1024 bytes @ 0x20001000)
T8C20 001:706.769 - 10.543ms returns 0x400
T8C20 001:706.801 JLINK_WriteMem(0x20001400, 0x1C8 Bytes, ...)
T8C20 001:706.813   Data:  FF FF FF FF FF FF FF FF FF FF FF FF FF FF FF FF ...
T8C20 001:706.835   CPU_WriteMem(456 bytes @ 0x20001400)
T8C20 001:711.954 - 5.166ms returns 0x1C8
T8C20 001:711.981 JLINK_HasError()
T8C20 001:711.995 JLINK_WriteReg(R0, 0x0002C000)
T8C20 001:712.009 - 0.019ms returns 0
T8C20 001:712.049 JLINK_WriteReg(R1, 0x00000D2C)
T8C20 001:712.062 - 0.018ms returns 0
T8C20 001:712.074 JLINK_WriteReg(R2, 0x200005C8)
T8C20 001:712.085 - 0.016ms returns 0
T8C20 001:712.096 JLINK_WriteReg(R3, 0x00000000)
T8C20 001:712.107 - 0.015ms returns 0
T8C20 001:712.118 JLINK_WriteReg(R4, 0x00000000)
T8C20 001:712.128 - 0.015ms returns 0
T8C20 001:712.139 JLINK_WriteReg(R5, 0x00000000)
T8C20 001:712.150 - 0.016ms returns 0
T8C20 001:712.162 JLINK_WriteReg(R6, 0x00000000)
T8C20 001:712.173 - 0.016ms returns 0
T8C20 001:712.184 JLINK_WriteReg(R7, 0x00000000)
T8C20 001:712.195 - 0.016ms returns 0
T8C20 001:712.206 JLINK_WriteReg(R8, 0x00000000)
T8C20 001:712.216 - 0.015ms returns 0
T8C20 001:712.227 JLINK_WriteReg(R9, 0x200005B4)
T8C20 001:712.237 - 0.015ms returns 0
T8C20 001:712.248 JLINK_WriteReg(R10, 0x00000000)
T8C20 001:712.261 - 0.017ms returns 0
T8C20 001:712.272 JLINK_WriteReg(R11, 0x00000000)
T8C20 001:712.282 - 0.015ms returns 0
T8C20 001:712.293 JLINK_WriteReg(R12, 0x00000000)
T8C20 001:712.304 - 0.015ms returns 0
T8C20 001:712.314 JLINK_WriteReg(R13 (SP), 0x20002000)
T8C20 001:712.325 - 0.016ms returns 0
T8C20 001:712.336 JLINK_WriteReg(R14, 0x20000001)
T8C20 001:712.354 - 0.026ms returns 0
T8C20 001:712.368 JLINK_WriteReg(R15 (PC), 0x20000154)
T8C20 001:712.379 - 0.015ms returns 0
T8C20 001:712.390 JLINK_WriteReg(XPSR, 0x01000000)
T8C20 001:712.400 - 0.015ms returns 0
T8C20 001:712.411 JLINK_WriteReg(MSP, 0x20002000)
T8C20 001:712.422 - 0.015ms returns 0
T8C20 001:712.432 JLINK_WriteReg(PSP, 0x20002000)
T8C20 001:712.443 - 0.015ms returns 0
T8C20 001:712.454 JLINK_WriteReg(CFBP, 0x00000000)
T8C20 001:712.464 - 0.015ms returns 0
T8C20 001:712.475 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T8C20 001:712.499 - 0.029ms returns 0x00000018
T8C20 001:712.510 JLINK_Go()
T8C20 001:712.527   CPU_ReadMem(4 bytes @ 0xE0001000)
T8C20 001:718.059 - 5.572ms
T8C20 001:718.097 JLINK_IsHalted()
T8C20 001:718.775 - 0.692ms returns FALSE
T8C20 001:718.798 JLINK_HasError()
T8C20 001:724.703 JLINK_IsHalted()
T8C20 001:725.415 - 0.740ms returns FALSE
T8C20 001:725.453 JLINK_HasError()
T8C20 001:726.949 JLINK_IsHalted()
T8C20 001:727.636 - 0.692ms returns FALSE
T8C20 001:727.648 JLINK_HasError()
T8C20 001:728.927 JLINK_IsHalted()
T8C20 001:729.643 - 0.721ms returns FALSE
T8C20 001:729.655 JLINK_HasError()
T8C20 001:731.012 JLINK_IsHalted()
T8C20 001:731.634 - 0.631ms returns FALSE
T8C20 001:731.649 JLINK_HasError()
T8C20 001:733.511 JLINK_IsHalted()
T8C20 001:734.306 - 0.811ms returns FALSE
T8C20 001:734.331 JLINK_HasError()
T8C20 001:735.481 JLINK_IsHalted()
T8C20 001:736.268 - 0.793ms returns FALSE
T8C20 001:736.281 JLINK_HasError()
T8C20 001:737.485 JLINK_IsHalted()
T8C20 001:738.301 - 0.828ms returns FALSE
T8C20 001:738.325 JLINK_HasError()
T8C20 001:739.480 JLINK_IsHalted()
T8C20 001:740.164 - 0.689ms returns FALSE
T8C20 001:740.175 JLINK_HasError()
T8C20 001:741.477 JLINK_IsHalted()
T8C20 001:742.174 - 0.703ms returns FALSE
T8C20 001:742.186 JLINK_HasError()
T8C20 001:744.008 JLINK_IsHalted()
T8C20 001:744.810 - 0.811ms returns FALSE
T8C20 001:744.831 JLINK_HasError()
T8C20 001:745.949 JLINK_IsHalted()
T8C20 001:746.638 - 0.696ms returns FALSE
T8C20 001:746.652 JLINK_HasError()
T8C20 001:748.931 JLINK_IsHalted()
T8C20 001:749.741 - 0.852ms returns FALSE
T8C20 001:749.792 JLINK_HasError()
T8C20 001:752.256 JLINK_IsHalted()
T8C20 001:752.993 - 0.751ms returns FALSE
T8C20 001:753.015 JLINK_HasError()
T8C20 001:754.327 JLINK_IsHalted()
T8C20 001:755.002 - 0.689ms returns FALSE
T8C20 001:755.024 JLINK_HasError()
T8C20 001:756.161 JLINK_IsHalted()
T8C20 001:756.897 - 0.742ms returns FALSE
T8C20 001:756.909 JLINK_HasError()
T8C20 001:758.214 JLINK_IsHalted()
T8C20 001:758.930 - 0.721ms returns FALSE
T8C20 001:758.942 JLINK_HasError()
T8C20 001:760.207 JLINK_IsHalted()
T8C20 001:760.914 - 0.723ms returns FALSE
T8C20 001:760.939 JLINK_HasError()
T8C20 001:762.149 JLINK_IsHalted()
T8C20 001:762.854 - 0.711ms returns FALSE
T8C20 001:762.867 JLINK_HasError()
T8C20 001:764.263 JLINK_IsHalted()
T8C20 001:765.009 - 0.762ms returns FALSE
T8C20 001:765.034 JLINK_HasError()
T8C20 001:766.537 JLINK_IsHalted()
T8C20 001:767.269 - 0.747ms returns FALSE
T8C20 001:767.293 JLINK_HasError()
T8C20 001:768.623 JLINK_IsHalted()
T8C20 001:769.322 - 0.711ms returns FALSE
T8C20 001:769.342 JLINK_HasError()
T8C20 001:770.607 JLINK_IsHalted()
T8C20 001:771.232 - 0.631ms returns FALSE
T8C20 001:771.245 JLINK_HasError()
T8C20 001:773.224 JLINK_IsHalted()
T8C20 001:773.996 - 0.777ms returns FALSE
T8C20 001:774.009 JLINK_HasError()
T8C20 001:775.708 JLINK_IsHalted()
T8C20 001:780.568   CPU_ReadMem(2 bytes @ 0x20000000)
T8C20 001:781.343 - 5.649ms returns TRUE
T8C20 001:781.366 JLINK_ReadReg(R15 (PC))
T8C20 001:781.380 - 0.019ms returns 0x20000000
T8C20 001:781.392 JLINK_ClrBPEx(BPHandle = 0x00000018)
T8C20 001:781.403 - 0.016ms returns 0x00
T8C20 001:781.414 JLINK_ReadReg(R0)
T8C20 001:781.425 - 0.015ms returns 0x00000000
T8C20 001:781.437 JLINK_HasError()
T8C20 001:781.449 JLINK_WriteReg(R0, 0x00000002)
T8C20 001:781.460 - 0.016ms returns 0
T8C20 001:781.471 JLINK_WriteReg(R1, 0x00000D2C)
T8C20 001:781.481 - 0.020ms returns 0
T8C20 001:781.499 JLINK_WriteReg(R2, 0x200005C8)
T8C20 001:781.510 - 0.015ms returns 0
T8C20 001:781.520 JLINK_WriteReg(R3, 0x00000000)
T8C20 001:781.531 - 0.015ms returns 0
T8C20 001:781.542 JLINK_WriteReg(R4, 0x00000000)
T8C20 001:781.552 - 0.015ms returns 0
T8C20 001:781.563 JLINK_WriteReg(R5, 0x00000000)
T8C20 001:781.573 - 0.015ms returns 0
T8C20 001:781.584 JLINK_WriteReg(R6, 0x00000000)
T8C20 001:781.595 - 0.015ms returns 0
T8C20 001:781.606 JLINK_WriteReg(R7, 0x00000000)
T8C20 001:781.616 - 0.015ms returns 0
T8C20 001:781.627 JLINK_WriteReg(R8, 0x00000000)
T8C20 001:781.638 - 0.015ms returns 0
T8C20 001:781.649 JLINK_WriteReg(R9, 0x200005B4)
T8C20 001:781.659 - 0.016ms returns 0
T8C20 001:781.670 JLINK_WriteReg(R10, 0x00000000)
T8C20 001:781.681 - 0.015ms returns 0
T8C20 001:781.692 JLINK_WriteReg(R11, 0x00000000)
T8C20 001:781.702 - 0.015ms returns 0
T8C20 001:781.713 JLINK_WriteReg(R12, 0x00000000)
T8C20 001:781.724 - 0.015ms returns 0
T8C20 001:781.735 JLINK_WriteReg(R13 (SP), 0x20002000)
T8C20 001:781.746 - 0.016ms returns 0
T8C20 001:781.757 JLINK_WriteReg(R14, 0x20000001)
T8C20 001:781.767 - 0.015ms returns 0
T8C20 001:781.778 JLINK_WriteReg(R15 (PC), 0x20000060)
T8C20 001:781.788 - 0.015ms returns 0
T8C20 001:781.799 JLINK_WriteReg(XPSR, 0x01000000)
T8C20 001:781.810 - 0.015ms returns 0
T8C20 001:781.820 JLINK_WriteReg(MSP, 0x20002000)
T8C20 001:781.831 - 0.015ms returns 0
T8C20 001:781.842 JLINK_WriteReg(PSP, 0x20002000)
T8C20 001:781.852 - 0.015ms returns 0
T8C20 001:781.863 JLINK_WriteReg(CFBP, 0x00000000)
T8C20 001:781.873 - 0.015ms returns 0
T8C20 001:781.884 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T8C20 001:781.895 - 0.016ms returns 0x00000019
T8C20 001:781.906 JLINK_Go()
T8C20 001:781.922   CPU_ReadMem(4 bytes @ 0xE0001000)
T8C20 001:787.466 - 5.571ms
T8C20 001:787.485 JLINK_IsHalted()
T8C20 001:792.272   CPU_ReadMem(2 bytes @ 0x20000000)
T8C20 001:792.979 - 5.500ms returns TRUE
T8C20 001:792.998 JLINK_ReadReg(R15 (PC))
T8C20 001:793.011 - 0.018ms returns 0x20000000
T8C20 001:793.024 JLINK_ClrBPEx(BPHandle = 0x00000019)
T8C20 001:793.036 - 0.016ms returns 0x00
T8C20 001:793.048 JLINK_ReadReg(R0)
T8C20 001:793.059 - 0.016ms returns 0x00000000
T8C20 001:852.901 JLINK_WriteMem(0x20000000, 0x5B8 Bytes, ...)
T8C20 001:852.922   Data:  00 BE 0A E0 0D 78 2D 06 68 40 08 24 40 00 00 D3 ...
T8C20 001:852.950   CPU_WriteMem(1464 bytes @ 0x20000000)
T8C20 001:867.626 - 14.765ms returns 0x5B8
T8C20 001:867.715 JLINK_HasError()
T8C20 001:867.729 JLINK_WriteReg(R0, 0x00000000)
T8C20 001:867.744 - 0.020ms returns 0
T8C20 001:867.755 JLINK_WriteReg(R1, 0x03D09000)
T8C20 001:867.766 - 0.016ms returns 0
T8C20 001:867.777 JLINK_WriteReg(R2, 0x00000003)
T8C20 001:867.787 - 0.016ms returns 0
T8C20 001:867.798 JLINK_WriteReg(R3, 0x00000000)
T8C20 001:867.808 - 0.015ms returns 0
T8C20 001:867.819 JLINK_WriteReg(R4, 0x00000000)
T8C20 001:867.830 - 0.015ms returns 0
T8C20 001:867.840 JLINK_WriteReg(R5, 0x00000000)
T8C20 001:867.851 - 0.015ms returns 0
T8C20 001:867.861 JLINK_WriteReg(R6, 0x00000000)
T8C20 001:867.872 - 0.015ms returns 0
T8C20 001:867.882 JLINK_WriteReg(R7, 0x00000000)
T8C20 001:867.893 - 0.015ms returns 0
T8C20 001:867.903 JLINK_WriteReg(R8, 0x00000000)
T8C20 001:867.914 - 0.015ms returns 0
T8C20 001:867.925 JLINK_WriteReg(R9, 0x200005B4)
T8C20 001:867.935 - 0.015ms returns 0
T8C20 001:867.947 JLINK_WriteReg(R10, 0x00000000)
T8C20 001:867.965 - 0.026ms returns 0
T8C20 001:867.983 JLINK_WriteReg(R11, 0x00000000)
T8C20 001:868.001 - 0.025ms returns 0
T8C20 001:868.019 JLINK_WriteReg(R12, 0x00000000)
T8C20 001:868.036 - 0.025ms returns 0
T8C20 001:868.052 JLINK_WriteReg(R13 (SP), 0x20002000)
T8C20 001:868.068 - 0.021ms returns 0
T8C20 001:868.080 JLINK_WriteReg(R14, 0x20000001)
T8C20 001:868.090 - 0.015ms returns 0
T8C20 001:868.101 JLINK_WriteReg(R15 (PC), 0x20000020)
T8C20 001:868.112 - 0.015ms returns 0
T8C20 001:868.123 JLINK_WriteReg(XPSR, 0x01000000)
T8C20 001:868.133 - 0.015ms returns 0
T8C20 001:868.149 JLINK_WriteReg(MSP, 0x20002000)
T8C20 001:868.161 - 0.016ms returns 0
T8C20 001:868.172 JLINK_WriteReg(PSP, 0x20002000)
T8C20 001:868.182 - 0.015ms returns 0
T8C20 001:868.193 JLINK_WriteReg(CFBP, 0x00000000)
T8C20 001:868.203 - 0.015ms returns 0
T8C20 001:868.215 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T8C20 001:868.231   CPU_ReadMem(2 bytes @ 0x20000000)
T8C20 001:868.988 - 0.781ms returns 0x0000001A
T8C20 001:869.009 JLINK_Go()
T8C20 001:869.023   CPU_WriteMem(2 bytes @ 0x20000000)
T8C20 001:869.695   CPU_ReadMem(4 bytes @ 0xE0001000)
T8C20 001:875.297 - 6.334ms
T8C20 001:875.363 JLINK_IsHalted()
T8C20 001:880.427   CPU_ReadMem(2 bytes @ 0x20000000)
T8C20 001:881.168 - 5.843ms returns TRUE
T8C20 001:881.226 JLINK_ReadReg(R15 (PC))
T8C20 001:881.242 - 0.020ms returns 0x20000000
T8C20 001:881.255 JLINK_ClrBPEx(BPHandle = 0x0000001A)
T8C20 001:881.267 - 0.017ms returns 0x00
T8C20 001:881.281 JLINK_ReadReg(R0)
T8C20 001:881.292 - 0.016ms returns 0x00000000
T8C20 001:881.305 JLINK_WriteMem(0x200005C8, 0x238 Bytes, ...)
T8C20 001:881.317   Data:  08 60 00 20 C5 63 02 00 CD 63 02 00 CF 63 02 00 ...
T8C20 001:881.337   CPU_WriteMem(568 bytes @ 0x200005C8)
T8C20 001:887.580 - 6.328ms returns 0x238
T8C20 001:887.647 JLINK_WriteMem(0x20000800, 0x400 Bytes, ...)
T8C20 001:887.659   Data:  07 E0 C3 EA 02 03 40 E8 01 34 00 2C E9 D1 4F F0 ...
T8C20 001:887.682   CPU_WriteMem(1024 bytes @ 0x20000800)
T8C20 001:898.100 - 10.472ms returns 0x400
T8C20 001:898.138 JLINK_WriteMem(0x20000C00, 0x400 Bytes, ...)
T8C20 001:898.149   Data:  0F 21 20 48 DD F8 2C B0 FF F7 3F FF 1D 48 0F 21 ...
T8C20 001:898.176   CPU_WriteMem(1024 bytes @ 0x20000C00)
T8C20 001:908.676 - 10.564ms returns 0x400
T8C20 001:908.719 JLINK_WriteMem(0x20001000, 0x400 Bytes, ...)
T8C20 001:908.730   Data:  34 21 45 1C C1 F3 03 11 07 D0 84 46 16 46 01 27 ...
T8C20 001:908.759   CPU_WriteMem(1024 bytes @ 0x20001000)
T8C20 001:919.234 - 10.547ms returns 0x400
T8C20 001:919.286 JLINK_WriteMem(0x20001400, 0x1C8 Bytes, ...)
T8C20 001:919.300   Data:  0B A1 A1 64 E0 62 04 F5 1E 71 E1 64 10 21 A0 65 ...
T8C20 001:919.330   CPU_WriteMem(456 bytes @ 0x20001400)
T8C20 001:924.567 - 5.313ms returns 0x1C8
T8C20 001:924.620 JLINK_HasError()
T8C20 001:924.635 JLINK_WriteReg(R0, 0x00026000)
T8C20 001:924.652 - 0.022ms returns 0
T8C20 001:924.664 JLINK_WriteReg(R1, 0x00001000)
T8C20 001:924.676 - 0.017ms returns 0
T8C20 001:924.689 JLINK_WriteReg(R2, 0x200005C8)
T8C20 001:924.700 - 0.016ms returns 0
T8C20 001:924.714 JLINK_WriteReg(R3, 0x00000000)
T8C20 001:924.725 - 0.017ms returns 0
T8C20 001:924.826 JLINK_WriteReg(R4, 0x00000000)
T8C20 001:924.862 - 0.042ms returns 0
T8C20 001:924.876 JLINK_WriteReg(R5, 0x00000000)
T8C20 001:924.887 - 0.017ms returns 0
T8C20 001:924.899 JLINK_WriteReg(R6, 0x00000000)
T8C20 001:924.910 - 0.016ms returns 0
T8C20 001:924.922 JLINK_WriteReg(R7, 0x00000000)
T8C20 001:924.933 - 0.016ms returns 0
T8C20 001:924.945 JLINK_WriteReg(R8, 0x00000000)
T8C20 001:924.956 - 0.016ms returns 0
T8C20 001:924.968 JLINK_WriteReg(R9, 0x200005B4)
T8C20 001:924.979 - 0.016ms returns 0
T8C20 001:924.991 JLINK_WriteReg(R10, 0x00000000)
T8C20 001:925.003 - 0.017ms returns 0
T8C20 001:925.014 JLINK_WriteReg(R11, 0x00000000)
T8C20 001:925.025 - 0.016ms returns 0
T8C20 001:925.037 JLINK_WriteReg(R12, 0x00000000)
T8C20 001:925.048 - 0.016ms returns 0
T8C20 001:925.059 JLINK_WriteReg(R13 (SP), 0x20002000)
T8C20 001:925.071 - 0.017ms returns 0
T8C20 001:925.083 JLINK_WriteReg(R14, 0x20000001)
T8C20 001:925.094 - 0.016ms returns 0
T8C20 001:925.106 JLINK_WriteReg(R15 (PC), 0x20000348)
T8C20 001:925.117 - 0.016ms returns 0
T8C20 001:925.129 JLINK_WriteReg(XPSR, 0x01000000)
T8C20 001:925.140 - 0.016ms returns 0
T8C20 001:925.152 JLINK_WriteReg(MSP, 0x20002000)
T8C20 001:925.163 - 0.016ms returns 0
T8C20 001:925.175 JLINK_WriteReg(PSP, 0x20002000)
T8C20 001:925.186 - 0.016ms returns 0
T8C20 001:925.197 JLINK_WriteReg(CFBP, 0x00000000)
T8C20 001:925.209 - 0.016ms returns 0
T8C20 001:925.226 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T8C20 001:925.241 - 0.021ms returns 0x0000001B
T8C20 001:925.253 JLINK_Go()
T8C20 001:925.274   CPU_ReadMem(4 bytes @ 0xE0001000)
T8C20 001:931.189 - 5.982ms
T8C20 001:931.251 JLINK_IsHalted()
T8C20 001:936.099   CPU_ReadMem(2 bytes @ 0x20000000)
T8C20 001:936.748 - 5.524ms returns TRUE
T8C20 001:936.791 JLINK_ReadReg(R15 (PC))
T8C20 001:936.808 - 0.022ms returns 0x20000000
T8C20 001:936.821 JLINK_ClrBPEx(BPHandle = 0x0000001B)
T8C20 001:936.833 - 0.017ms returns 0x00
T8C20 001:936.845 JLINK_ReadReg(R0)
T8C20 001:936.857 - 0.017ms returns 0x00027000
T8C20 001:937.493 JLINK_WriteMem(0x200005C8, 0x238 Bytes, ...)
T8C20 001:937.520   Data:  68 60 0A 20 A5 F5 A1 65 03 F0 0A FD 11 20 FF F7 ...
T8C20 001:937.549   CPU_WriteMem(568 bytes @ 0x200005C8)
T8C20 001:943.827 - 6.353ms returns 0x238
T8C20 001:943.858 JLINK_WriteMem(0x20000800, 0x400 Bytes, ...)
T8C20 001:943.871   Data:  01 29 42 61 00 D0 00 21 01 61 00 20 70 47 07 20 ...
T8C20 001:943.898   CPU_WriteMem(1024 bytes @ 0x20000800)
T8C20 001:954.327 - 10.499ms returns 0x400
T8C20 001:954.376 JLINK_WriteMem(0x20000C00, 0x400 Bytes, ...)
T8C20 001:954.388   Data:  A4 00 60 63 D5 F8 A8 00 A0 63 4F F6 FF 70 A4 F8 ...
T8C20 001:954.417   CPU_WriteMem(1024 bytes @ 0x20000C00)
T8C20 001:964.931 - 10.586ms returns 0x400
T8C20 001:964.981 JLINK_WriteMem(0x20001000, 0x400 Bytes, ...)
T8C20 001:964.994   Data:  20 88 40 1C 80 B2 20 80 28 44 DD F8 01 10 01 60 ...
T8C20 001:965.024   CPU_WriteMem(1024 bytes @ 0x20001000)
T8C20 001:975.656 - 10.713ms returns 0x400
T8C20 001:975.713 JLINK_WriteMem(0x20001400, 0x1C8 Bytes, ...)
T8C20 001:975.725   Data:  04 00 8E B0 2D CD 08 AE 86 E8 2D 00 5B D0 E9 B3 ...
T8C20 001:975.754   CPU_WriteMem(456 bytes @ 0x20001400)
T8C20 001:981.305 - 5.621ms returns 0x1C8
T8C20 001:981.354 JLINK_HasError()
T8C20 001:981.370 JLINK_WriteReg(R0, 0x00027000)
T8C20 001:981.389 - 0.024ms returns 0
T8C20 001:981.403 JLINK_WriteReg(R1, 0x00001000)
T8C20 001:981.414 - 0.017ms returns 0
T8C20 001:981.428 JLINK_WriteReg(R2, 0x200005C8)
T8C20 001:981.440 - 0.017ms returns 0
T8C20 001:981.454 JLINK_WriteReg(R3, 0x00000000)
T8C20 001:981.465 - 0.017ms returns 0
T8C20 001:981.479 JLINK_WriteReg(R4, 0x00000000)
T8C20 001:981.491 - 0.017ms returns 0
T8C20 001:981.504 JLINK_WriteReg(R5, 0x00000000)
T8C20 001:981.516 - 0.017ms returns 0
T8C20 001:981.533 JLINK_WriteReg(R6, 0x00000000)
T8C20 001:981.546 - 0.040ms returns 0
T8C20 001:981.582 JLINK_WriteReg(R7, 0x00000000)
T8C20 001:981.594 - 0.017ms returns 0
T8C20 001:981.607 JLINK_WriteReg(R8, 0x00000000)
T8C20 001:981.619 - 0.017ms returns 0
T8C20 001:981.632 JLINK_WriteReg(R9, 0x200005B4)
T8C20 001:981.644 - 0.017ms returns 0
T8C20 001:981.657 JLINK_WriteReg(R10, 0x00000000)
T8C20 001:981.669 - 0.017ms returns 0
T8C20 001:981.682 JLINK_WriteReg(R11, 0x00000000)
T8C20 001:981.694 - 0.017ms returns 0
T8C20 001:981.708 JLINK_WriteReg(R12, 0x00000000)
T8C20 001:981.719 - 0.017ms returns 0
T8C20 001:981.733 JLINK_WriteReg(R13 (SP), 0x20002000)
T8C20 001:981.746 - 0.018ms returns 0
T8C20 001:981.759 JLINK_WriteReg(R14, 0x20000001)
T8C20 001:981.771 - 0.017ms returns 0
T8C20 001:981.784 JLINK_WriteReg(R15 (PC), 0x20000348)
T8C20 001:981.796 - 0.017ms returns 0
T8C20 001:981.809 JLINK_WriteReg(XPSR, 0x01000000)
T8C20 001:981.821 - 0.017ms returns 0
T8C20 001:981.834 JLINK_WriteReg(MSP, 0x20002000)
T8C20 001:981.846 - 0.017ms returns 0
T8C20 001:981.859 JLINK_WriteReg(PSP, 0x20002000)
T8C20 001:981.871 - 0.017ms returns 0
T8C20 001:981.885 JLINK_WriteReg(CFBP, 0x00000000)
T8C20 001:981.896 - 0.017ms returns 0
T8C20 001:981.910 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T8C20 001:981.923 - 0.018ms returns 0x0000001C
T8C20 001:981.936 JLINK_Go()
T8C20 001:981.957   CPU_ReadMem(4 bytes @ 0xE0001000)
T8C20 001:988.183 - 6.272ms
T8C20 001:988.226 JLINK_IsHalted()
T8C20 001:993.631   CPU_ReadMem(2 bytes @ 0x20000000)
T8C20 001:994.407 - 6.189ms returns TRUE
T8C20 001:994.431 JLINK_ReadReg(R15 (PC))
T8C20 001:994.455 - 0.029ms returns 0x20000000
T8C20 001:994.468 JLINK_ClrBPEx(BPHandle = 0x0000001C)
T8C20 001:994.480 - 0.016ms returns 0x00
T8C20 001:994.493 JLINK_ReadReg(R0)
T8C20 001:994.504 - 0.016ms returns 0x00028000
T8C20 001:995.141 JLINK_WriteMem(0x200005C8, 0x238 Bytes, ...)
T8C20 001:995.173   Data:  DB FF 64 1C 04 2C F9 D3 10 BD 10 B5 00 24 20 46 ...
T8C20 001:995.201   CPU_WriteMem(568 bytes @ 0x200005C8)
T8C20 002:001.462 - 6.352ms returns 0x238
T8C20 002:001.505 JLINK_WriteMem(0x20000800, 0x400 Bytes, ...)
T8C20 002:001.516   Data:  C7 FE 25 70 40 F6 CD 41 00 22 38 68 FF F7 30 F8 ...
T8C20 002:001.543   CPU_WriteMem(1024 bytes @ 0x20000800)
T8C20 002:012.122 - 10.649ms returns 0x400
T8C20 002:012.166 JLINK_WriteMem(0x20000C00, 0x400 Bytes, ...)
T8C20 002:012.178   Data:  FA E7 70 47 EC 35 00 20 10 B5 D0 E9 02 34 D1 E9 ...
T8C20 002:012.207   CPU_WriteMem(1024 bytes @ 0x20000C00)
T8C20 002:022.936 - 10.808ms returns 0x400
T8C20 002:022.995 JLINK_WriteMem(0x20001000, 0x400 Bytes, ...)
T8C20 002:023.010   Data:  43 F8 31 00 03 20 00 EB 04 40 0E A1 01 F0 58 FB ...
T8C20 002:023.048   CPU_WriteMem(1024 bytes @ 0x20001000)
T8C20 002:033.530 - 10.565ms returns 0x400
T8C20 002:033.581 JLINK_WriteMem(0x20001400, 0x1C8 Bytes, ...)
T8C20 002:033.595   Data:  5C 02 1E 28 01 D3 01 20 60 70 9D F8 00 00 FE F7 ...
T8C20 002:033.623   CPU_WriteMem(456 bytes @ 0x20001400)
T8C20 002:039.061 - 5.509ms returns 0x1C8
T8C20 002:039.109 JLINK_HasError()
T8C20 002:039.126 JLINK_WriteReg(R0, 0x00028000)
T8C20 002:039.147 - 0.026ms returns 0
T8C20 002:039.161 JLINK_WriteReg(R1, 0x00001000)
T8C20 002:039.178 - 0.024ms returns 0
T8C20 002:039.193 JLINK_WriteReg(R2, 0x200005C8)
T8C20 002:039.204 - 0.017ms returns 0
T8C20 002:039.216 JLINK_WriteReg(R3, 0x00000000)
T8C20 002:039.228 - 0.017ms returns 0
T8C20 002:039.279 JLINK_WriteReg(R4, 0x00000000)
T8C20 002:039.292 - 0.018ms returns 0
T8C20 002:039.303 JLINK_WriteReg(R5, 0x00000000)
T8C20 002:039.315 - 0.018ms returns 0
T8C20 002:039.327 JLINK_WriteReg(R6, 0x00000000)
T8C20 002:039.338 - 0.015ms returns 0
T8C20 002:039.349 JLINK_WriteReg(R7, 0x00000000)
T8C20 002:039.359 - 0.015ms returns 0
T8C20 002:039.370 JLINK_WriteReg(R8, 0x00000000)
T8C20 002:039.380 - 0.015ms returns 0
T8C20 002:039.391 JLINK_WriteReg(R9, 0x200005B4)
T8C20 002:039.401 - 0.015ms returns 0
T8C20 002:039.412 JLINK_WriteReg(R10, 0x00000000)
T8C20 002:039.423 - 0.015ms returns 0
T8C20 002:039.434 JLINK_WriteReg(R11, 0x00000000)
T8C20 002:039.444 - 0.015ms returns 0
T8C20 002:039.455 JLINK_WriteReg(R12, 0x00000000)
T8C20 002:039.465 - 0.015ms returns 0
T8C20 002:039.476 JLINK_WriteReg(R13 (SP), 0x20002000)
T8C20 002:039.487 - 0.016ms returns 0
T8C20 002:039.498 JLINK_WriteReg(R14, 0x20000001)
T8C20 002:039.509 - 0.015ms returns 0
T8C20 002:039.519 JLINK_WriteReg(R15 (PC), 0x20000348)
T8C20 002:039.530 - 0.015ms returns 0
T8C20 002:039.541 JLINK_WriteReg(XPSR, 0x01000000)
T8C20 002:039.552 - 0.015ms returns 0
T8C20 002:039.563 JLINK_WriteReg(MSP, 0x20002000)
T8C20 002:039.573 - 0.015ms returns 0
T8C20 002:039.584 JLINK_WriteReg(PSP, 0x20002000)
T8C20 002:039.595 - 0.015ms returns 0
T8C20 002:039.605 JLINK_WriteReg(CFBP, 0x00000000)
T8C20 002:039.616 - 0.015ms returns 0
T8C20 002:039.628 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T8C20 002:039.641 - 0.019ms returns 0x0000001D
T8C20 002:039.654 JLINK_Go()
T8C20 002:039.675   CPU_ReadMem(4 bytes @ 0xE0001000)
T8C20 002:046.055 - 6.435ms
T8C20 002:046.104 JLINK_IsHalted()
T8C20 002:051.025   CPU_ReadMem(2 bytes @ 0x20000000)
T8C20 002:051.901 - 5.804ms returns TRUE
T8C20 002:051.918 JLINK_ReadReg(R15 (PC))
T8C20 002:051.932 - 0.019ms returns 0x20000000
T8C20 002:051.944 JLINK_ClrBPEx(BPHandle = 0x0000001D)
T8C20 002:051.955 - 0.016ms returns 0x00
T8C20 002:051.968 JLINK_ReadReg(R0)
T8C20 002:051.979 - 0.015ms returns 0x00029000
T8C20 002:052.466 JLINK_WriteMem(0x200005C8, 0x238 Bytes, ...)
T8C20 002:052.488   Data:  0C 30 FF F7 86 FF 00 28 F5 D1 01 AA 00 99 01 EA ...
T8C20 002:052.551   CPU_WriteMem(568 bytes @ 0x200005C8)
T8C20 002:058.904 - 6.466ms returns 0x238
T8C20 002:058.944 JLINK_WriteMem(0x20000800, 0x400 Bytes, ...)
T8C20 002:058.955   Data:  0C 00 00 50 70 B5 14 46 82 88 0E 46 05 46 20 88 ...
T8C20 002:058.996   CPU_WriteMem(1024 bytes @ 0x20000800)
T8C20 002:069.584 - 10.673ms returns 0x400
T8C20 002:069.637 JLINK_WriteMem(0x20000C00, 0x400 Bytes, ...)
T8C20 002:069.649   Data:  40 89 34 F8 06 1F 81 42 00 D8 08 46 60 80 17 28 ...
T8C20 002:069.684   CPU_WriteMem(1024 bytes @ 0x20000C00)
T8C20 002:080.220 - 10.612ms returns 0x400
T8C20 002:080.269 JLINK_WriteMem(0x20001000, 0x400 Bytes, ...)
T8C20 002:080.281   Data:  21 78 A1 F1 30 02 0A 2A 11 D2 05 EB 85 02 01 EB ...
T8C20 002:080.309   CPU_WriteMem(1024 bytes @ 0x20001000)
T8C20 002:090.797 - 10.555ms returns 0x400
T8C20 002:090.842 JLINK_WriteMem(0x20001400, 0x1C8 Bytes, ...)
T8C20 002:090.855   Data:  C1 F3 C2 00 8C 0D 8D F8 20 00 08 2C 01 D2 27 46 ...
T8C20 002:090.882   CPU_WriteMem(456 bytes @ 0x20001400)
T8C20 002:096.133 - 5.311ms returns 0x1C8
T8C20 002:096.169 JLINK_HasError()
T8C20 002:096.184 JLINK_WriteReg(R0, 0x00029000)
T8C20 002:096.200 - 0.022ms returns 0
T8C20 002:096.869 JLINK_WriteReg(R1, 0x00001000)
T8C20 002:096.882 - 0.019ms returns 0
T8C20 002:096.894 JLINK_WriteReg(R2, 0x200005C8)
T8C20 002:096.905 - 0.016ms returns 0
T8C20 002:096.917 JLINK_WriteReg(R3, 0x00000000)
T8C20 002:096.928 - 0.016ms returns 0
T8C20 002:096.940 JLINK_WriteReg(R4, 0x00000000)
T8C20 002:096.951 - 0.016ms returns 0
T8C20 002:096.963 JLINK_WriteReg(R5, 0x00000000)
T8C20 002:096.974 - 0.016ms returns 0
T8C20 002:096.985 JLINK_WriteReg(R6, 0x00000000)
T8C20 002:096.996 - 0.016ms returns 0
T8C20 002:097.008 JLINK_WriteReg(R7, 0x00000000)
T8C20 002:097.019 - 0.016ms returns 0
T8C20 002:097.030 JLINK_WriteReg(R8, 0x00000000)
T8C20 002:097.041 - 0.016ms returns 0
T8C20 002:097.053 JLINK_WriteReg(R9, 0x200005B4)
T8C20 002:097.064 - 0.016ms returns 0
T8C20 002:097.075 JLINK_WriteReg(R10, 0x00000000)
T8C20 002:097.087 - 0.016ms returns 0
T8C20 002:097.098 JLINK_WriteReg(R11, 0x00000000)
T8C20 002:097.109 - 0.016ms returns 0
T8C20 002:097.124 JLINK_WriteReg(R12, 0x00000000)
T8C20 002:097.136 - 0.017ms returns 0
T8C20 002:097.147 JLINK_WriteReg(R13 (SP), 0x20002000)
T8C20 002:097.160 - 0.018ms returns 0
T8C20 002:097.172 JLINK_WriteReg(R14, 0x20000001)
T8C20 002:097.183 - 0.016ms returns 0
T8C20 002:097.194 JLINK_WriteReg(R15 (PC), 0x20000348)
T8C20 002:097.205 - 0.016ms returns 0
T8C20 002:097.217 JLINK_WriteReg(XPSR, 0x01000000)
T8C20 002:097.228 - 0.016ms returns 0
T8C20 002:097.240 JLINK_WriteReg(MSP, 0x20002000)
T8C20 002:097.251 - 0.016ms returns 0
T8C20 002:097.262 JLINK_WriteReg(PSP, 0x20002000)
T8C20 002:097.274 - 0.016ms returns 0
T8C20 002:097.285 JLINK_WriteReg(CFBP, 0x00000000)
T8C20 002:097.297 - 0.016ms returns 0
T8C20 002:097.308 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T8C20 002:097.321 - 0.018ms returns 0x0000001E
T8C20 002:097.333 JLINK_Go()
T8C20 002:097.351   CPU_ReadMem(4 bytes @ 0xE0001000)
T8C20 002:102.860 - 5.545ms
T8C20 002:102.887 JLINK_IsHalted()
T8C20 002:107.835   CPU_ReadMem(2 bytes @ 0x20000000)
T8C20 002:108.585 - 5.722ms returns TRUE
T8C20 002:108.625 JLINK_ReadReg(R15 (PC))
T8C20 002:108.647 - 0.031ms returns 0x20000000
T8C20 002:108.681 JLINK_ClrBPEx(BPHandle = 0x0000001E)
T8C20 002:108.701 - 0.028ms returns 0x00
T8C20 002:108.720 JLINK_ReadReg(R0)
T8C20 002:108.738 - 0.027ms returns 0x0002A000
T8C20 002:109.597 JLINK_WriteMem(0x200005C8, 0x238 Bytes, ...)
T8C20 002:109.627   Data:  82 F9 07 00 4A D0 00 F0 C7 F9 43 46 08 22 09 A9 ...
T8C20 002:109.652   CPU_WriteMem(568 bytes @ 0x200005C8)
T8C20 002:115.985 - 6.406ms returns 0x238
T8C20 002:116.014 JLINK_WriteMem(0x20000800, 0x400 Bytes, ...)
T8C20 002:116.026   Data:  45 71 21 68 20 46 49 68 88 47 E0 68 04 68 00 2C ...
T8C20 002:116.050   CPU_WriteMem(1024 bytes @ 0x20000800)
T8C20 002:126.618 - 10.625ms returns 0x400
T8C20 002:126.654 JLINK_WriteMem(0x20000C00, 0x400 Bytes, ...)
T8C20 002:126.680   Data:  42 1A 20 46 25 A1 FF F7 5F FD B8 F1 00 00 4F F0 ...
T8C20 002:126.706   CPU_WriteMem(1024 bytes @ 0x20000C00)
T8C20 002:137.201 - 10.567ms returns 0x400
T8C20 002:137.238 JLINK_WriteMem(0x20001000, 0x400 Bytes, ...)
T8C20 002:137.253   Data:  08 47 70 47 31 C1 02 00 2D E9 F0 41 0E 46 05 46 ...
T8C20 002:137.280   CPU_WriteMem(1024 bytes @ 0x20001000)
T8C20 002:147.846 - 10.632ms returns 0x400
T8C20 002:147.886 JLINK_WriteMem(0x20001400, 0x1C8 Bytes, ...)
T8C20 002:147.899   Data:  3A 46 21 46 28 68 BD E8 F0 41 00 F0 41 BE 00 00 ...
T8C20 002:147.927   CPU_WriteMem(456 bytes @ 0x20001400)
T8C20 002:153.174 - 5.304ms returns 0x1C8
T8C20 002:153.208 JLINK_HasError()
T8C20 002:153.225 JLINK_WriteReg(R0, 0x0002A000)
T8C20 002:153.244 - 0.025ms returns 0
T8C20 002:153.260 JLINK_WriteReg(R1, 0x00001000)
T8C20 002:153.275 - 0.023ms returns 0
T8C20 002:153.293 JLINK_WriteReg(R2, 0x200005C8)
T8C20 002:153.305 - 0.017ms returns 0
T8C20 002:153.317 JLINK_WriteReg(R3, 0x00000000)
T8C20 002:153.328 - 0.016ms returns 0
T8C20 002:153.341 JLINK_WriteReg(R4, 0x00000000)
T8C20 002:153.353 - 0.017ms returns 0
T8C20 002:153.367 JLINK_WriteReg(R5, 0x00000000)
T8C20 002:153.379 - 0.017ms returns 0
T8C20 002:153.392 JLINK_WriteReg(R6, 0x00000000)
T8C20 002:153.404 - 0.018ms returns 0
T8C20 002:153.418 JLINK_WriteReg(R7, 0x00000000)
T8C20 002:153.430 - 0.018ms returns 0
T8C20 002:153.444 JLINK_WriteReg(R8, 0x00000000)
T8C20 002:153.457 - 0.018ms returns 0
T8C20 002:153.470 JLINK_WriteReg(R9, 0x200005B4)
T8C20 002:153.482 - 0.017ms returns 0
T8C20 002:153.496 JLINK_WriteReg(R10, 0x00000000)
T8C20 002:153.510 - 0.019ms returns 0
T8C20 002:153.523 JLINK_WriteReg(R11, 0x00000000)
T8C20 002:153.536 - 0.018ms returns 0
T8C20 002:153.550 JLINK_WriteReg(R12, 0x00000000)
T8C20 002:153.562 - 0.018ms returns 0
T8C20 002:153.576 JLINK_WriteReg(R13 (SP), 0x20002000)
T8C20 002:153.589 - 0.018ms returns 0
T8C20 002:153.603 JLINK_WriteReg(R14, 0x20000001)
T8C20 002:153.615 - 0.017ms returns 0
T8C20 002:153.629 JLINK_WriteReg(R15 (PC), 0x20000348)
T8C20 002:153.640 - 0.017ms returns 0
T8C20 002:153.654 JLINK_WriteReg(XPSR, 0x01000000)
T8C20 002:153.666 - 0.017ms returns 0
T8C20 002:153.679 JLINK_WriteReg(MSP, 0x20002000)
T8C20 002:153.691 - 0.017ms returns 0
T8C20 002:153.714 JLINK_WriteReg(PSP, 0x20002000)
T8C20 002:153.726 - 0.017ms returns 0
T8C20 002:153.738 JLINK_WriteReg(CFBP, 0x00000000)
T8C20 002:153.749 - 0.016ms returns 0
T8C20 002:153.810 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T8C20 002:153.823 - 0.019ms returns 0x0000001F
T8C20 002:153.836 JLINK_Go()
T8C20 002:153.853   CPU_ReadMem(4 bytes @ 0xE0001000)
T8C20 002:159.639 - 5.827ms
T8C20 002:159.673 JLINK_IsHalted()
T8C20 002:164.725   CPU_ReadMem(2 bytes @ 0x20000000)
T8C20 002:165.451 - 5.786ms returns TRUE
T8C20 002:165.467 JLINK_ReadReg(R15 (PC))
T8C20 002:165.481 - 0.019ms returns 0x20000000
T8C20 002:165.493 JLINK_ClrBPEx(BPHandle = 0x0000001F)
T8C20 002:165.505 - 0.017ms returns 0x00
T8C20 002:165.517 JLINK_ReadReg(R0)
T8C20 002:165.528 - 0.016ms returns 0x0002B000
T8C20 002:165.957 JLINK_WriteMem(0x200005C8, 0x238 Bytes, ...)
T8C20 002:165.976   Data:  B3 FC 00 28 F9 D0 28 68 C7 60 C4 F8 14 90 30 46 ...
T8C20 002:166.000   CPU_WriteMem(568 bytes @ 0x200005C8)
T8C20 002:172.348 - 6.410ms returns 0x238
T8C20 002:172.377 JLINK_WriteMem(0x20000800, 0x400 Bytes, ...)
T8C20 002:172.389   Data:  E0 2A 00 20 DC 2C 00 20 84 34 00 20 6F 6E 01 00 ...
T8C20 002:172.411   CPU_WriteMem(1024 bytes @ 0x20000800)
T8C20 002:182.887 - 10.541ms returns 0x400
T8C20 002:182.937 JLINK_WriteMem(0x20000C00, 0x400 Bytes, ...)
T8C20 002:182.950   Data:  33 B1 14 A0 00 90 15 A2 16 A1 28 46 FE F7 B0 F9 ...
T8C20 002:182.979   CPU_WriteMem(1024 bytes @ 0x20000C00)
T8C20 002:193.708 - 10.798ms returns 0x400
T8C20 002:193.753 JLINK_WriteMem(0x20001000, 0x400 Bytes, ...)
T8C20 002:193.765   Data:  81 40 11 F0 EC 0F 0C D0 0C 48 82 68 4F F4 80 01 ...
T8C20 002:193.793   CPU_WriteMem(1024 bytes @ 0x20001000)
T8C20 002:204.258 - 10.526ms returns 0x400
T8C20 002:204.296 JLINK_WriteMem(0x20001400, 0x1C8 Bytes, ...)
T8C20 002:204.309   Data:  5F F0 01 06 B9 F1 01 0F 02 D9 A9 F1 01 09 03 E0 ...
T8C20 002:204.334   CPU_WriteMem(456 bytes @ 0x20001400)
T8C20 002:209.581 - 5.307ms returns 0x1C8
T8C20 002:209.621 JLINK_HasError()
T8C20 002:209.639 JLINK_WriteReg(R0, 0x0002B000)
T8C20 002:209.654 - 0.021ms returns 0
T8C20 002:209.668 JLINK_WriteReg(R1, 0x00001000)
T8C20 002:209.680 - 0.017ms returns 0
T8C20 002:209.694 JLINK_WriteReg(R2, 0x200005C8)
T8C20 002:209.706 - 0.017ms returns 0
T8C20 002:209.719 JLINK_WriteReg(R3, 0x00000000)
T8C20 002:209.731 - 0.017ms returns 0
T8C20 002:209.745 JLINK_WriteReg(R4, 0x00000000)
T8C20 002:209.757 - 0.017ms returns 0
T8C20 002:209.770 JLINK_WriteReg(R5, 0x00000000)
T8C20 002:209.782 - 0.017ms returns 0
T8C20 002:209.795 JLINK_WriteReg(R6, 0x00000000)
T8C20 002:209.807 - 0.017ms returns 0
T8C20 002:209.821 JLINK_WriteReg(R7, 0x00000000)
T8C20 002:209.832 - 0.017ms returns 0
T8C20 002:209.846 JLINK_WriteReg(R8, 0x00000000)
T8C20 002:209.858 - 0.017ms returns 0
T8C20 002:209.871 JLINK_WriteReg(R9, 0x200005B4)
T8C20 002:209.883 - 0.017ms returns 0
T8C20 002:209.897 JLINK_WriteReg(R10, 0x00000000)
T8C20 002:209.910 - 0.018ms returns 0
T8C20 002:209.923 JLINK_WriteReg(R11, 0x00000000)
T8C20 002:209.935 - 0.017ms returns 0
T8C20 002:209.949 JLINK_WriteReg(R12, 0x00000000)
T8C20 002:209.961 - 0.018ms returns 0
T8C20 002:209.974 JLINK_WriteReg(R13 (SP), 0x20002000)
T8C20 002:209.987 - 0.018ms returns 0
T8C20 002:209.999 JLINK_WriteReg(R14, 0x20000001)
T8C20 002:210.011 - 0.017ms returns 0
T8C20 002:210.023 JLINK_WriteReg(R15 (PC), 0x20000348)
T8C20 002:210.034 - 0.016ms returns 0
T8C20 002:210.046 JLINK_WriteReg(XPSR, 0x01000000)
T8C20 002:210.057 - 0.016ms returns 0
T8C20 002:210.071 JLINK_WriteReg(MSP, 0x20002000)
T8C20 002:210.083 - 0.017ms returns 0
T8C20 002:210.099 JLINK_WriteReg(PSP, 0x20002000)
T8C20 002:210.111 - 0.017ms returns 0
T8C20 002:210.125 JLINK_WriteReg(CFBP, 0x00000000)
T8C20 002:210.137 - 0.017ms returns 0
T8C20 002:210.150 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T8C20 002:210.163 - 0.018ms returns 0x00000020
T8C20 002:210.177 JLINK_Go()
T8C20 002:210.196   CPU_ReadMem(4 bytes @ 0xE0001000)
T8C20 002:215.706 - 5.565ms
T8C20 002:215.760 JLINK_IsHalted()
T8C20 002:220.768   CPU_ReadMem(2 bytes @ 0x20000000)
T8C20 002:221.435 - 5.691ms returns TRUE
T8C20 002:221.465 JLINK_ReadReg(R15 (PC))
T8C20 002:221.480 - 0.020ms returns 0x20000000
T8C20 002:221.494 JLINK_ClrBPEx(BPHandle = 0x00000020)
T8C20 002:221.506 - 0.017ms returns 0x00
T8C20 002:221.520 JLINK_ReadReg(R0)
T8C20 002:221.532 - 0.017ms returns 0x0002C000
T8C20 002:221.951 JLINK_WriteMem(0x200005C8, 0x238 Bytes, ...)
T8C20 002:221.969   Data:  FC 9F 09 F8 07 00 00 20 F9 E7 10 B5 04 28 17 D2 ...
T8C20 002:221.992   CPU_WriteMem(568 bytes @ 0x200005C8)
T8C20 002:228.306 - 6.417ms returns 0x238
T8C20 002:228.377 JLINK_WriteMem(0x20000800, 0x400 Bytes, ...)
T8C20 002:228.389   Data:  83 00 00 00 00 C5 02 00 84 00 00 00 3C C5 02 00 ...
T8C20 002:228.412   CPU_WriteMem(1024 bytes @ 0x20000800)
T8C20 002:239.027 - 10.668ms returns 0x400
T8C20 002:239.055 JLINK_WriteMem(0x20000C00, 0x400 Bytes, ...)
T8C20 002:239.068   Data:  5F 49 4E 49 54 49 41 4C 49 5A 45 44 00 00 00 00 ...
T8C20 002:239.091   CPU_WriteMem(1024 bytes @ 0x20000C00)
T8C20 002:249.543 - 10.510ms returns 0x400
T8C20 002:249.583 JLINK_WriteMem(0x20001000, 0x400 Bytes, ...)
T8C20 002:249.597   Data:  6C 65 5F 63 66 67 5F 73 65 74 28 29 20 72 65 74 ...
T8C20 002:249.624   CPU_WriteMem(1024 bytes @ 0x20001000)
T8C20 002:260.150 - 10.609ms returns 0x400
T8C20 002:260.208 JLINK_WriteMem(0x20001400, 0x1C8 Bytes, ...)
T8C20 002:260.221   Data:  00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 ...
T8C20 002:260.247   CPU_WriteMem(456 bytes @ 0x20001400)
T8C20 002:265.513 - 5.326ms returns 0x1C8
T8C20 002:265.552 JLINK_HasError()
T8C20 002:265.626 JLINK_WriteReg(R0, 0x0002C000)
T8C20 002:265.721 - 0.102ms returns 0
T8C20 002:265.739 JLINK_WriteReg(R1, 0x00000D2C)
T8C20 002:265.755 - 0.023ms returns 0
T8C20 002:265.773 JLINK_WriteReg(R2, 0x200005C8)
T8C20 002:265.841 - 0.075ms returns 0
T8C20 002:265.858 JLINK_WriteReg(R3, 0x00000000)
T8C20 002:265.873 - 0.022ms returns 0
T8C20 002:265.889 JLINK_WriteReg(R4, 0x00000000)
T8C20 002:265.904 - 0.022ms returns 0
T8C20 002:265.920 JLINK_WriteReg(R5, 0x00000000)
T8C20 002:265.936 - 0.023ms returns 0
T8C20 002:265.951 JLINK_WriteReg(R6, 0x00000000)
T8C20 002:265.967 - 0.022ms returns 0
T8C20 002:265.984 JLINK_WriteReg(R7, 0x00000000)
T8C20 002:265.999 - 0.023ms returns 0
T8C20 002:266.015 JLINK_WriteReg(R8, 0x00000000)
T8C20 002:266.030 - 0.022ms returns 0
T8C20 002:266.046 JLINK_WriteReg(R9, 0x200005B4)
T8C20 002:266.062 - 0.024ms returns 0
T8C20 002:266.079 JLINK_WriteReg(R10, 0x00000000)
T8C20 002:266.094 - 0.023ms returns 0
T8C20 002:266.110 JLINK_WriteReg(R11, 0x00000000)
T8C20 002:266.125 - 0.022ms returns 0
T8C20 002:266.141 JLINK_WriteReg(R12, 0x00000000)
T8C20 002:266.156 - 0.022ms returns 0
T8C20 002:266.172 JLINK_WriteReg(R13 (SP), 0x20002000)
T8C20 002:266.189 - 0.024ms returns 0
T8C20 002:266.206 JLINK_WriteReg(R14, 0x20000001)
T8C20 002:266.221 - 0.023ms returns 0
T8C20 002:266.237 JLINK_WriteReg(R15 (PC), 0x20000348)
T8C20 002:266.253 - 0.023ms returns 0
T8C20 002:266.269 JLINK_WriteReg(XPSR, 0x01000000)
T8C20 002:266.284 - 0.022ms returns 0
T8C20 002:266.300 JLINK_WriteReg(MSP, 0x20002000)
T8C20 002:266.314 - 0.022ms returns 0
T8C20 002:266.329 JLINK_WriteReg(PSP, 0x20002000)
T8C20 002:266.343 - 0.020ms returns 0
T8C20 002:266.358 JLINK_WriteReg(CFBP, 0x00000000)
T8C20 002:266.375 - 0.024ms returns 0
T8C20 002:266.391 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T8C20 002:266.409 - 0.025ms returns 0x00000021
T8C20 002:266.425 JLINK_Go()
T8C20 002:266.449   CPU_ReadMem(4 bytes @ 0xE0001000)
T8C20 002:272.301 - 5.895ms
T8C20 002:272.330 JLINK_IsHalted()
T8C20 002:277.428   CPU_ReadMem(2 bytes @ 0x20000000)
T8C20 002:278.169 - 5.846ms returns TRUE
T8C20 002:278.185 JLINK_ReadReg(R15 (PC))
T8C20 002:278.199 - 0.019ms returns 0x20000000
T8C20 002:278.211 JLINK_ClrBPEx(BPHandle = 0x00000021)
T8C20 002:278.222 - 0.017ms returns 0x00
T8C20 002:278.235 JLINK_ReadReg(R0)
T8C20 002:278.246 - 0.016ms returns 0x0002CD2C
T8C20 002:278.790 JLINK_HasError()
T8C20 002:278.815 JLINK_WriteReg(R0, 0x00000003)
T8C20 002:278.830 - 0.021ms returns 0
T8C20 002:278.842 JLINK_WriteReg(R1, 0x00000D2C)
T8C20 002:278.854 - 0.017ms returns 0
T8C20 002:278.866 JLINK_WriteReg(R2, 0x200005C8)
T8C20 002:278.877 - 0.016ms returns 0
T8C20 002:278.889 JLINK_WriteReg(R3, 0x00000000)
T8C20 002:278.900 - 0.016ms returns 0
T8C20 002:278.911 JLINK_WriteReg(R4, 0x00000000)
T8C20 002:278.923 - 0.016ms returns 0
T8C20 002:278.934 JLINK_WriteReg(R5, 0x00000000)
T8C20 002:278.945 - 0.016ms returns 0
T8C20 002:278.957 JLINK_WriteReg(R6, 0x00000000)
T8C20 002:278.968 - 0.016ms returns 0
T8C20 002:278.980 JLINK_WriteReg(R7, 0x00000000)
T8C20 002:278.991 - 0.016ms returns 0
T8C20 002:279.003 JLINK_WriteReg(R8, 0x00000000)
T8C20 002:279.014 - 0.019ms returns 0
T8C20 002:279.028 JLINK_WriteReg(R9, 0x200005B4)
T8C20 002:279.039 - 0.016ms returns 0
T8C20 002:279.051 JLINK_WriteReg(R10, 0x00000000)
T8C20 002:279.062 - 0.016ms returns 0
T8C20 002:279.074 JLINK_WriteReg(R11, 0x00000000)
T8C20 002:279.085 - 0.016ms returns 0
T8C20 002:279.097 JLINK_WriteReg(R12, 0x00000000)
T8C20 002:279.108 - 0.016ms returns 0
T8C20 002:279.120 JLINK_WriteReg(R13 (SP), 0x20002000)
T8C20 002:279.131 - 0.017ms returns 0
T8C20 002:279.143 JLINK_WriteReg(R14, 0x20000001)
T8C20 002:279.154 - 0.017ms returns 0
T8C20 002:279.167 JLINK_WriteReg(R15 (PC), 0x20000060)
T8C20 002:279.189 - 0.027ms returns 0
T8C20 002:279.200 JLINK_WriteReg(XPSR, 0x01000000)
T8C20 002:279.212 - 0.016ms returns 0
T8C20 002:279.223 JLINK_WriteReg(MSP, 0x20002000)
T8C20 002:279.234 - 0.016ms returns 0
T8C20 002:279.246 JLINK_WriteReg(PSP, 0x20002000)
T8C20 002:279.264 - 0.023ms returns 0
T8C20 002:279.280 JLINK_WriteReg(CFBP, 0x00000000)
T8C20 002:279.292 - 0.017ms returns 0
T8C20 002:279.304 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T8C20 002:279.321 - 0.022ms returns 0x00000022
T8C20 002:279.333 JLINK_Go()
T8C20 002:279.351   CPU_ReadMem(4 bytes @ 0xE0001000)
T8C20 002:285.114 - 5.856ms
T8C20 002:285.198 JLINK_IsHalted()
T8C20 002:290.147   CPU_ReadMem(2 bytes @ 0x20000000)
T8C20 002:290.990 - 5.808ms returns TRUE
T8C20 002:291.024 JLINK_ReadReg(R15 (PC))
T8C20 002:291.038 - 0.020ms returns 0x20000000
T8C20 002:291.053 JLINK_ClrBPEx(BPHandle = 0x00000022)
T8C20 002:291.066 - 0.019ms returns 0x00
T8C20 002:291.080 JLINK_ReadReg(R0)
T8C20 002:291.093 - 0.018ms returns 0x00000000
T8C20 002:355.557 JLINK_WriteMemEx(0x20000000, 0x00000002 Bytes, Flags = 0x02000000)
T8C20 002:355.591   Data:  FE E7
T8C20 002:355.624   CPU_WriteMem(2 bytes @ 0x20000000)
T8C20 002:356.389 - 0.851ms returns 0x2
T8C20 002:356.419 JLINK_HasError()
T8C20 002:356.433 JLINK_HasError()
T8C20 002:356.445 JLINK_SetResetType(JLINKARM_CM3_RESET_TYPE_NORMAL)
T8C20 002:356.456 - 0.017ms returns JLINKARM_CM3_RESET_TYPE_NORMAL
T8C20 002:356.469 JLINK_Reset()
T8C20 002:356.487   CPU_WriteMem(4 bytes @ 0xE000EDF0)
T8C20 002:357.278   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T8C20 002:365.517   Reset: Halt core after reset via DEMCR.VC_CORERESET.
T8C20 002:376.518   Reset: Reset device via AIRCR.SYSRESETREQ.
T8C20 002:376.566   CPU_WriteMem(4 bytes @ 0xE000ED0C)
T8C20 002:430.643   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T8C20 002:431.278   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T8C20 002:431.985   CPU_WriteMem(4 bytes @ 0xE000EDF0)
T8C20 002:432.674   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T8C20 002:438.934   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T8C20 002:444.556   CPU_WriteMem(4 bytes @ 0xE0002000)
T8C20 002:445.300   CPU_ReadMem(4 bytes @ 0xE000EDFC)
T8C20 002:446.001   CPU_ReadMem(4 bytes @ 0xE0001000)
T8C20 002:446.703 - 90.241ms
T8C20 002:446.725 JLINK_Go()
T8C20 002:446.744   CPU_ReadMem(4 bytes @ 0xE0001000)
T8C20 002:447.366   CPU_WriteMem(4 bytes @ 0xE0002008)
T8C20 002:447.387   CPU_WriteMem(4 bytes @ 0xE000200C)
T8C20 002:447.408   CPU_WriteMem(4 bytes @ 0xE0002010)
T8C20 002:447.423   CPU_WriteMem(4 bytes @ 0xE0002014)
T8C20 002:447.440   CPU_WriteMem(4 bytes @ 0xE0002018)
T8C20 002:447.457   CPU_WriteMem(4 bytes @ 0xE000201C)
T8C20 002:450.260   CPU_WriteMem(4 bytes @ 0xE0001004)
T8C20 002:451.813 - 5.105ms
T8C20 002:464.637 JLINK_Close()
T8C20 002:465.106   CPU is running
T8C20 002:465.141   CPU_WriteMem(4 bytes @ 0xE0002008)
T8C20 002:465.951   CPU is running
T8C20 002:465.973   CPU_WriteMem(4 bytes @ 0xE000200C)
T8C20 002:466.680   CPU is running
T8C20 002:466.696   CPU_WriteMem(4 bytes @ 0xE0002010)
T8C20 002:467.387   CPU is running
T8C20 002:467.402   CPU_WriteMem(4 bytes @ 0xE0002014)
T8C20 002:468.080   CPU is running
T8C20 002:468.095   CPU_WriteMem(4 bytes @ 0xE0002018)
T8C20 002:468.724   CPU is running
T8C20 002:468.743   CPU_WriteMem(4 bytes @ 0xE000201C)
T8C20 002:488.896 - 24.287ms
T8C20 002:488.932   
T8C20 002:488.943   Closed
