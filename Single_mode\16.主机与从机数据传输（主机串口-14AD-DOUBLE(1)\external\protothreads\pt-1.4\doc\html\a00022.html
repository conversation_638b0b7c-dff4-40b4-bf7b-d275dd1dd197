<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=iso-8859-1">
<title>The Protothreads Library 1.4: pt.h Source File</title>
<link href="doxygen.css" rel="stylesheet" type="text/css">
<link href="tabs.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.4.6 -->
<div class="tabs">
  <ul>
    <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
    <li><a href="modules.html"><span>Modules</span></a></li>
    <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    <li id="current"><a href="files.html"><span>Files</span></a></li>
  </ul></div>
<div class="tabs">
  <ul>
    <li><a href="files.html"><span>File&nbsp;List</span></a></li>
    <li><a href="globals.html"><span>Globals</span></a></li>
  </ul></div>
<h1>pt.h</h1><a href="a00013.html">Go to the documentation of this file.</a><div class="fragment"><pre class="fragment"><a name="l00001"></a>00001 <span class="comment">/*</span>
<a name="l00002"></a>00002 <span class="comment"> * Copyright (c) 2004-2005, Swedish Institute of Computer Science.</span>
<a name="l00003"></a>00003 <span class="comment"> * All rights reserved.</span>
<a name="l00004"></a>00004 <span class="comment"> *</span>
<a name="l00005"></a>00005 <span class="comment"> * Redistribution and use in source and binary forms, with or without</span>
<a name="l00006"></a>00006 <span class="comment"> * modification, are permitted provided that the following conditions</span>
<a name="l00007"></a>00007 <span class="comment"> * are met:</span>
<a name="l00008"></a>00008 <span class="comment"> * 1. Redistributions of source code must retain the above copyright</span>
<a name="l00009"></a>00009 <span class="comment"> *    notice, this list of conditions and the following disclaimer.</span>
<a name="l00010"></a>00010 <span class="comment"> * 2. Redistributions in binary form must reproduce the above copyright</span>
<a name="l00011"></a>00011 <span class="comment"> *    notice, this list of conditions and the following disclaimer in the</span>
<a name="l00012"></a>00012 <span class="comment"> *    documentation and/or other materials provided with the distribution.</span>
<a name="l00013"></a>00013 <span class="comment"> * 3. Neither the name of the Institute nor the names of its contributors</span>
<a name="l00014"></a>00014 <span class="comment"> *    may be used to endorse or promote products derived from this software</span>
<a name="l00015"></a>00015 <span class="comment"> *    without specific prior written permission.</span>
<a name="l00016"></a>00016 <span class="comment"> *</span>
<a name="l00017"></a>00017 <span class="comment"> * THIS SOFTWARE IS PROVIDED BY THE INSTITUTE AND CONTRIBUTORS ``AS IS'' AND</span>
<a name="l00018"></a>00018 <span class="comment"> * ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE</span>
<a name="l00019"></a>00019 <span class="comment"> * IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE</span>
<a name="l00020"></a>00020 <span class="comment"> * ARE DISCLAIMED.  IN NO EVENT SHALL THE INSTITUTE OR CONTRIBUTORS BE LIABLE</span>
<a name="l00021"></a>00021 <span class="comment"> * FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL</span>
<a name="l00022"></a>00022 <span class="comment"> * DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS</span>
<a name="l00023"></a>00023 <span class="comment"> * OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)</span>
<a name="l00024"></a>00024 <span class="comment"> * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT</span>
<a name="l00025"></a>00025 <span class="comment"> * LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY</span>
<a name="l00026"></a>00026 <span class="comment"> * OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF</span>
<a name="l00027"></a>00027 <span class="comment"> * SUCH DAMAGE.</span>
<a name="l00028"></a>00028 <span class="comment"> *</span>
<a name="l00029"></a>00029 <span class="comment"> * This file is part of the Contiki operating system.</span>
<a name="l00030"></a>00030 <span class="comment"> *</span>
<a name="l00031"></a>00031 <span class="comment"> * Author: Adam Dunkels &lt;<EMAIL>&gt;</span>
<a name="l00032"></a>00032 <span class="comment"> *</span>
<a name="l00033"></a>00033 <span class="comment"> * $Id: pt.h,v 1.7 2006/10/02 07:52:56 adam Exp $</span>
<a name="l00034"></a>00034 <span class="comment"> */</span>
<a name="l00035"></a>00035 <span class="comment"></span>
<a name="l00036"></a>00036 <span class="comment">/**</span>
<a name="l00037"></a>00037 <span class="comment"> * \addtogroup pt</span>
<a name="l00038"></a>00038 <span class="comment"> * @{</span>
<a name="l00039"></a>00039 <span class="comment"> */</span>
<a name="l00040"></a>00040 <span class="comment"></span>
<a name="l00041"></a>00041 <span class="comment">/**</span>
<a name="l00042"></a>00042 <span class="comment"> * \file</span>
<a name="l00043"></a>00043 <span class="comment"> * Protothreads implementation.</span>
<a name="l00044"></a>00044 <span class="comment"> * \author</span>
<a name="l00045"></a>00045 <span class="comment"> * Adam Dunkels &lt;<EMAIL>&gt;</span>
<a name="l00046"></a>00046 <span class="comment"> *</span>
<a name="l00047"></a>00047 <span class="comment"> */</span>
<a name="l00048"></a>00048 
<a name="l00049"></a>00049 <span class="preprocessor">#ifndef __PT_H__</span>
<a name="l00050"></a>00050 <span class="preprocessor"></span><span class="preprocessor">#define __PT_H__</span>
<a name="l00051"></a>00051 <span class="preprocessor"></span>
<a name="l00052"></a>00052 <span class="preprocessor">#include "<a class="code" href="a00011.html">lc.h</a>"</span>
<a name="l00053"></a>00053 
<a name="l00054"></a><a class="code" href="a00005.html">00054</a> <span class="keyword">struct </span><a class="code" href="a00005.html">pt</a> {
<a name="l00055"></a><a class="code" href="a00005.html#c3fa0fa86689e3e7c039a16c16861dbe">00055</a>   <a class="code" href="a00017.html#gfad6704adb116cc16edb80f744e7239d">lc_t</a> <a class="code" href="a00005.html#c3fa0fa86689e3e7c039a16c16861dbe">lc</a>;
<a name="l00056"></a>00056 };
<a name="l00057"></a>00057 
<a name="l00058"></a><a class="code" href="a00014.html#g7b5319b5b65761a845fcd1500fde4cdc">00058</a> <span class="preprocessor">#define PT_WAITING 0</span>
<a name="l00059"></a><a class="code" href="a00014.html#ge469332907e0617d72d5e2dd4297119d">00059</a> <span class="preprocessor"></span><span class="preprocessor">#define PT_YIELDED 1</span>
<a name="l00060"></a><a class="code" href="a00014.html#gcfae9053e5c107a1aed6b228c917d2ea">00060</a> <span class="preprocessor"></span><span class="preprocessor">#define PT_EXITED  2</span>
<a name="l00061"></a><a class="code" href="a00014.html#g9ff1e8936a8a26bff54c05f8a989b93b">00061</a> <span class="preprocessor"></span><span class="preprocessor">#define PT_ENDED   3</span>
<a name="l00062"></a>00062 <span class="preprocessor"></span><span class="comment"></span>
<a name="l00063"></a>00063 <span class="comment">/**</span>
<a name="l00064"></a>00064 <span class="comment"> * \name Initialization</span>
<a name="l00065"></a>00065 <span class="comment"> * @{</span>
<a name="l00066"></a>00066 <span class="comment"> */</span>
<a name="l00067"></a>00067 <span class="comment"></span>
<a name="l00068"></a>00068 <span class="comment">/**</span>
<a name="l00069"></a>00069 <span class="comment"> * Initialize a protothread.</span>
<a name="l00070"></a>00070 <span class="comment"> *</span>
<a name="l00071"></a>00071 <span class="comment"> * Initializes a protothread. Initialization must be done prior to</span>
<a name="l00072"></a>00072 <span class="comment"> * starting to execute the protothread.</span>
<a name="l00073"></a>00073 <span class="comment"> *</span>
<a name="l00074"></a>00074 <span class="comment"> * \param pt A pointer to the protothread control structure.</span>
<a name="l00075"></a>00075 <span class="comment"> *</span>
<a name="l00076"></a>00076 <span class="comment"> * \sa PT_SPAWN()</span>
<a name="l00077"></a>00077 <span class="comment"> *</span>
<a name="l00078"></a>00078 <span class="comment"> * \hideinitializer</span>
<a name="l00079"></a>00079 <span class="comment"> */</span>
<a name="l00080"></a><a class="code" href="a00014.html#ge6bae7dc0225468c8a5ac269df549892">00080</a> <span class="preprocessor">#define PT_INIT(pt)   LC_INIT((pt)-&gt;lc)</span>
<a name="l00081"></a>00081 <span class="preprocessor"></span><span class="comment"></span>
<a name="l00082"></a>00082 <span class="comment">/** @} */</span>
<a name="l00083"></a>00083 <span class="comment"></span>
<a name="l00084"></a>00084 <span class="comment">/**</span>
<a name="l00085"></a>00085 <span class="comment"> * \name Declaration and definition</span>
<a name="l00086"></a>00086 <span class="comment"> * @{</span>
<a name="l00087"></a>00087 <span class="comment"> */</span>
<a name="l00088"></a>00088 <span class="comment"></span>
<a name="l00089"></a>00089 <span class="comment">/**</span>
<a name="l00090"></a>00090 <span class="comment"> * Declaration of a protothread.</span>
<a name="l00091"></a>00091 <span class="comment"> *</span>
<a name="l00092"></a>00092 <span class="comment"> * This macro is used to declare a protothread. All protothreads must</span>
<a name="l00093"></a>00093 <span class="comment"> * be declared with this macro.</span>
<a name="l00094"></a>00094 <span class="comment"> *</span>
<a name="l00095"></a>00095 <span class="comment"> * \param name_args The name and arguments of the C function</span>
<a name="l00096"></a>00096 <span class="comment"> * implementing the protothread.</span>
<a name="l00097"></a>00097 <span class="comment"> *</span>
<a name="l00098"></a>00098 <span class="comment"> * \hideinitializer</span>
<a name="l00099"></a>00099 <span class="comment"> */</span>
<a name="l00100"></a><a class="code" href="a00014.html#g3d4c8bd4aada659eb34f5d2ffd3e7901">00100</a> <span class="preprocessor">#define PT_THREAD(name_args) char name_args</span>
<a name="l00101"></a>00101 <span class="preprocessor"></span><span class="comment"></span>
<a name="l00102"></a>00102 <span class="comment">/**</span>
<a name="l00103"></a>00103 <span class="comment"> * Declare the start of a protothread inside the C function</span>
<a name="l00104"></a>00104 <span class="comment"> * implementing the protothread.</span>
<a name="l00105"></a>00105 <span class="comment"> *</span>
<a name="l00106"></a>00106 <span class="comment"> * This macro is used to declare the starting point of a</span>
<a name="l00107"></a>00107 <span class="comment"> * protothread. It should be placed at the start of the function in</span>
<a name="l00108"></a>00108 <span class="comment"> * which the protothread runs. All C statements above the PT_BEGIN()</span>
<a name="l00109"></a>00109 <span class="comment"> * invokation will be executed each time the protothread is scheduled.</span>
<a name="l00110"></a>00110 <span class="comment"> *</span>
<a name="l00111"></a>00111 <span class="comment"> * \param pt A pointer to the protothread control structure.</span>
<a name="l00112"></a>00112 <span class="comment"> *</span>
<a name="l00113"></a>00113 <span class="comment"> * \hideinitializer</span>
<a name="l00114"></a>00114 <span class="comment"> */</span>
<a name="l00115"></a><a class="code" href="a00014.html#g2ffbb9e554e08a343ae2f9de4bedfdfc">00115</a> <span class="preprocessor">#define PT_BEGIN(pt) { char PT_YIELD_FLAG = 1; LC_RESUME((pt)-&gt;lc)</span>
<a name="l00116"></a>00116 <span class="preprocessor"></span><span class="comment"></span>
<a name="l00117"></a>00117 <span class="comment">/**</span>
<a name="l00118"></a>00118 <span class="comment"> * Declare the end of a protothread.</span>
<a name="l00119"></a>00119 <span class="comment"> *</span>
<a name="l00120"></a>00120 <span class="comment"> * This macro is used for declaring that a protothread ends. It must</span>
<a name="l00121"></a>00121 <span class="comment"> * always be used together with a matching PT_BEGIN() macro.</span>
<a name="l00122"></a>00122 <span class="comment"> *</span>
<a name="l00123"></a>00123 <span class="comment"> * \param pt A pointer to the protothread control structure.</span>
<a name="l00124"></a>00124 <span class="comment"> *</span>
<a name="l00125"></a>00125 <span class="comment"> * \hideinitializer</span>
<a name="l00126"></a>00126 <span class="comment"> */</span>
<a name="l00127"></a><a class="code" href="a00014.html#g7b04a0035bef29d905496c23bae066d2">00127</a> <span class="preprocessor">#define PT_END(pt) LC_END((pt)-&gt;lc); PT_YIELD_FLAG = 0; \</span>
<a name="l00128"></a>00128 <span class="preprocessor">                   PT_INIT(pt); return PT_ENDED; }</span>
<a name="l00129"></a>00129 <span class="preprocessor"></span><span class="comment"></span>
<a name="l00130"></a>00130 <span class="comment">/** @} */</span>
<a name="l00131"></a>00131 <span class="comment"></span>
<a name="l00132"></a>00132 <span class="comment">/**</span>
<a name="l00133"></a>00133 <span class="comment"> * \name Blocked wait</span>
<a name="l00134"></a>00134 <span class="comment"> * @{</span>
<a name="l00135"></a>00135 <span class="comment"> */</span>
<a name="l00136"></a>00136 <span class="comment"></span>
<a name="l00137"></a>00137 <span class="comment">/**</span>
<a name="l00138"></a>00138 <span class="comment"> * Block and wait until condition is true.</span>
<a name="l00139"></a>00139 <span class="comment"> *</span>
<a name="l00140"></a>00140 <span class="comment"> * This macro blocks the protothread until the specified condition is</span>
<a name="l00141"></a>00141 <span class="comment"> * true.</span>
<a name="l00142"></a>00142 <span class="comment"> *</span>
<a name="l00143"></a>00143 <span class="comment"> * \param pt A pointer to the protothread control structure.</span>
<a name="l00144"></a>00144 <span class="comment"> * \param condition The condition.</span>
<a name="l00145"></a>00145 <span class="comment"> *</span>
<a name="l00146"></a>00146 <span class="comment"> * \hideinitializer</span>
<a name="l00147"></a>00147 <span class="comment"> */</span>
<a name="l00148"></a><a class="code" href="a00014.html#g99e43010ec61327164466aa2d902de45">00148</a> <span class="preprocessor">#define PT_WAIT_UNTIL(pt, condition)            \</span>
<a name="l00149"></a>00149 <span class="preprocessor">  do {                                          \</span>
<a name="l00150"></a>00150 <span class="preprocessor">    LC_SET((pt)-&gt;lc);                           \</span>
<a name="l00151"></a>00151 <span class="preprocessor">    if(!(condition)) {                          \</span>
<a name="l00152"></a>00152 <span class="preprocessor">      return PT_WAITING;                        \</span>
<a name="l00153"></a>00153 <span class="preprocessor">    }                                           \</span>
<a name="l00154"></a>00154 <span class="preprocessor">  } while(0)</span>
<a name="l00155"></a>00155 <span class="preprocessor"></span><span class="comment"></span>
<a name="l00156"></a>00156 <span class="comment">/**</span>
<a name="l00157"></a>00157 <span class="comment"> * Block and wait while condition is true.</span>
<a name="l00158"></a>00158 <span class="comment"> *</span>
<a name="l00159"></a>00159 <span class="comment"> * This function blocks and waits while condition is true. See</span>
<a name="l00160"></a>00160 <span class="comment"> * PT_WAIT_UNTIL().</span>
<a name="l00161"></a>00161 <span class="comment"> *</span>
<a name="l00162"></a>00162 <span class="comment"> * \param pt A pointer to the protothread control structure.</span>
<a name="l00163"></a>00163 <span class="comment"> * \param cond The condition.</span>
<a name="l00164"></a>00164 <span class="comment"> *</span>
<a name="l00165"></a>00165 <span class="comment"> * \hideinitializer</span>
<a name="l00166"></a>00166 <span class="comment"> */</span>
<a name="l00167"></a><a class="code" href="a00014.html#gad14bbbf092b90aa0a5a4f9169504a8d">00167</a> <span class="preprocessor">#define PT_WAIT_WHILE(pt, cond)  PT_WAIT_UNTIL((pt), !(cond))</span>
<a name="l00168"></a>00168 <span class="preprocessor"></span><span class="comment"></span>
<a name="l00169"></a>00169 <span class="comment">/** @} */</span>
<a name="l00170"></a>00170 <span class="comment"></span>
<a name="l00171"></a>00171 <span class="comment">/**</span>
<a name="l00172"></a>00172 <span class="comment"> * \name Hierarchical protothreads</span>
<a name="l00173"></a>00173 <span class="comment"> * @{</span>
<a name="l00174"></a>00174 <span class="comment"> */</span>
<a name="l00175"></a>00175 <span class="comment"></span>
<a name="l00176"></a>00176 <span class="comment">/**</span>
<a name="l00177"></a>00177 <span class="comment"> * Block and wait until a child protothread completes.</span>
<a name="l00178"></a>00178 <span class="comment"> *</span>
<a name="l00179"></a>00179 <span class="comment"> * This macro schedules a child protothread. The current protothread</span>
<a name="l00180"></a>00180 <span class="comment"> * will block until the child protothread completes.</span>
<a name="l00181"></a>00181 <span class="comment"> *</span>
<a name="l00182"></a>00182 <span class="comment"> * \note The child protothread must be manually initialized with the</span>
<a name="l00183"></a>00183 <span class="comment"> * PT_INIT() function before this function is used.</span>
<a name="l00184"></a>00184 <span class="comment"> *</span>
<a name="l00185"></a>00185 <span class="comment"> * \param pt A pointer to the protothread control structure.</span>
<a name="l00186"></a>00186 <span class="comment"> * \param thread The child protothread with arguments</span>
<a name="l00187"></a>00187 <span class="comment"> *</span>
<a name="l00188"></a>00188 <span class="comment"> * \sa PT_SPAWN()</span>
<a name="l00189"></a>00189 <span class="comment"> *</span>
<a name="l00190"></a>00190 <span class="comment"> * \hideinitializer</span>
<a name="l00191"></a>00191 <span class="comment"> */</span>
<a name="l00192"></a><a class="code" href="a00014.html#g2f8f70c30b9ee08a103fbd69a4365c4c">00192</a> <span class="preprocessor">#define PT_WAIT_THREAD(pt, thread) PT_WAIT_WHILE((pt), PT_SCHEDULE(thread))</span>
<a name="l00193"></a>00193 <span class="preprocessor"></span><span class="comment"></span>
<a name="l00194"></a>00194 <span class="comment">/**</span>
<a name="l00195"></a>00195 <span class="comment"> * Spawn a child protothread and wait until it exits.</span>
<a name="l00196"></a>00196 <span class="comment"> *</span>
<a name="l00197"></a>00197 <span class="comment"> * This macro spawns a child protothread and waits until it exits. The</span>
<a name="l00198"></a>00198 <span class="comment"> * macro can only be used within a protothread.</span>
<a name="l00199"></a>00199 <span class="comment"> *</span>
<a name="l00200"></a>00200 <span class="comment"> * \param pt A pointer to the protothread control structure.</span>
<a name="l00201"></a>00201 <span class="comment"> * \param child A pointer to the child protothread's control structure.</span>
<a name="l00202"></a>00202 <span class="comment"> * \param thread The child protothread with arguments</span>
<a name="l00203"></a>00203 <span class="comment"> *</span>
<a name="l00204"></a>00204 <span class="comment"> * \hideinitializer</span>
<a name="l00205"></a>00205 <span class="comment"> */</span>
<a name="l00206"></a><a class="code" href="a00014.html#g9e97a0b4d5cc7764d8e19758f5da53ae">00206</a> <span class="preprocessor">#define PT_SPAWN(pt, child, thread)             \</span>
<a name="l00207"></a>00207 <span class="preprocessor">  do {                                          \</span>
<a name="l00208"></a>00208 <span class="preprocessor">    PT_INIT((child));                           \</span>
<a name="l00209"></a>00209 <span class="preprocessor">    PT_WAIT_THREAD((pt), (thread));             \</span>
<a name="l00210"></a>00210 <span class="preprocessor">  } while(0)</span>
<a name="l00211"></a>00211 <span class="preprocessor"></span><span class="comment"></span>
<a name="l00212"></a>00212 <span class="comment">/** @} */</span>
<a name="l00213"></a>00213 <span class="comment"></span>
<a name="l00214"></a>00214 <span class="comment">/**</span>
<a name="l00215"></a>00215 <span class="comment"> * \name Exiting and restarting</span>
<a name="l00216"></a>00216 <span class="comment"> * @{</span>
<a name="l00217"></a>00217 <span class="comment"> */</span>
<a name="l00218"></a>00218 <span class="comment"></span>
<a name="l00219"></a>00219 <span class="comment">/**</span>
<a name="l00220"></a>00220 <span class="comment"> * Restart the protothread.</span>
<a name="l00221"></a>00221 <span class="comment"> *</span>
<a name="l00222"></a>00222 <span class="comment"> * This macro will block and cause the running protothread to restart</span>
<a name="l00223"></a>00223 <span class="comment"> * its execution at the place of the PT_BEGIN() call.</span>
<a name="l00224"></a>00224 <span class="comment"> *</span>
<a name="l00225"></a>00225 <span class="comment"> * \param pt A pointer to the protothread control structure.</span>
<a name="l00226"></a>00226 <span class="comment"> *</span>
<a name="l00227"></a>00227 <span class="comment"> * \hideinitializer</span>
<a name="l00228"></a>00228 <span class="comment"> */</span>
<a name="l00229"></a><a class="code" href="a00014.html#gcd3ac045f0a4ae63412e3b3d8780e8ab">00229</a> <span class="preprocessor">#define PT_RESTART(pt)                          \</span>
<a name="l00230"></a>00230 <span class="preprocessor">  do {                                          \</span>
<a name="l00231"></a>00231 <span class="preprocessor">    PT_INIT(pt);                                \</span>
<a name="l00232"></a>00232 <span class="preprocessor">    return PT_WAITING;                  \</span>
<a name="l00233"></a>00233 <span class="preprocessor">  } while(0)</span>
<a name="l00234"></a>00234 <span class="preprocessor"></span><span class="comment"></span>
<a name="l00235"></a>00235 <span class="comment">/**</span>
<a name="l00236"></a>00236 <span class="comment"> * Exit the protothread.</span>
<a name="l00237"></a>00237 <span class="comment"> *</span>
<a name="l00238"></a>00238 <span class="comment"> * This macro causes the protothread to exit. If the protothread was</span>
<a name="l00239"></a>00239 <span class="comment"> * spawned by another protothread, the parent protothread will become</span>
<a name="l00240"></a>00240 <span class="comment"> * unblocked and can continue to run.</span>
<a name="l00241"></a>00241 <span class="comment"> *</span>
<a name="l00242"></a>00242 <span class="comment"> * \param pt A pointer to the protothread control structure.</span>
<a name="l00243"></a>00243 <span class="comment"> *</span>
<a name="l00244"></a>00244 <span class="comment"> * \hideinitializer</span>
<a name="l00245"></a>00245 <span class="comment"> */</span>
<a name="l00246"></a><a class="code" href="a00014.html#g905451249dca72ce0385bf2a9ff178ee">00246</a> <span class="preprocessor">#define PT_EXIT(pt)                             \</span>
<a name="l00247"></a>00247 <span class="preprocessor">  do {                                          \</span>
<a name="l00248"></a>00248 <span class="preprocessor">    PT_INIT(pt);                                \</span>
<a name="l00249"></a>00249 <span class="preprocessor">    return PT_EXITED;                   \</span>
<a name="l00250"></a>00250 <span class="preprocessor">  } while(0)</span>
<a name="l00251"></a>00251 <span class="preprocessor"></span><span class="comment"></span>
<a name="l00252"></a>00252 <span class="comment">/** @} */</span>
<a name="l00253"></a>00253 <span class="comment"></span>
<a name="l00254"></a>00254 <span class="comment">/**</span>
<a name="l00255"></a>00255 <span class="comment"> * \name Calling a protothread</span>
<a name="l00256"></a>00256 <span class="comment"> * @{</span>
<a name="l00257"></a>00257 <span class="comment"> */</span>
<a name="l00258"></a>00258 <span class="comment"></span>
<a name="l00259"></a>00259 <span class="comment">/**</span>
<a name="l00260"></a>00260 <span class="comment"> * Schedule a protothread.</span>
<a name="l00261"></a>00261 <span class="comment"> *</span>
<a name="l00262"></a>00262 <span class="comment"> * This function shedules a protothread. The return value of the</span>
<a name="l00263"></a>00263 <span class="comment"> * function is non-zero if the protothread is running or zero if the</span>
<a name="l00264"></a>00264 <span class="comment"> * protothread has exited.</span>
<a name="l00265"></a>00265 <span class="comment"> *</span>
<a name="l00266"></a>00266 <span class="comment"> * \param f The call to the C function implementing the protothread to</span>
<a name="l00267"></a>00267 <span class="comment"> * be scheduled</span>
<a name="l00268"></a>00268 <span class="comment"> *</span>
<a name="l00269"></a>00269 <span class="comment"> * \hideinitializer</span>
<a name="l00270"></a>00270 <span class="comment"> */</span>
<a name="l00271"></a><a class="code" href="a00014.html#gfa82b860a64b67d25ab3abc21811896f">00271</a> <span class="preprocessor">#define PT_SCHEDULE(f) ((f) &lt; PT_EXITED)</span>
<a name="l00272"></a>00272 <span class="preprocessor"></span><span class="comment"></span>
<a name="l00273"></a>00273 <span class="comment">/** @} */</span>
<a name="l00274"></a>00274 <span class="comment"></span>
<a name="l00275"></a>00275 <span class="comment">/**</span>
<a name="l00276"></a>00276 <span class="comment"> * \name Yielding from a protothread</span>
<a name="l00277"></a>00277 <span class="comment"> * @{</span>
<a name="l00278"></a>00278 <span class="comment"> */</span>
<a name="l00279"></a>00279 <span class="comment"></span>
<a name="l00280"></a>00280 <span class="comment">/**</span>
<a name="l00281"></a>00281 <span class="comment"> * Yield from the current protothread.</span>
<a name="l00282"></a>00282 <span class="comment"> *</span>
<a name="l00283"></a>00283 <span class="comment"> * This function will yield the protothread, thereby allowing other</span>
<a name="l00284"></a>00284 <span class="comment"> * processing to take place in the system.</span>
<a name="l00285"></a>00285 <span class="comment"> *</span>
<a name="l00286"></a>00286 <span class="comment"> * \param pt A pointer to the protothread control structure.</span>
<a name="l00287"></a>00287 <span class="comment"> *</span>
<a name="l00288"></a>00288 <span class="comment"> * \hideinitializer</span>
<a name="l00289"></a>00289 <span class="comment"> */</span>
<a name="l00290"></a><a class="code" href="a00014.html#g155cba6121323726d02c00284428fed6">00290</a> <span class="preprocessor">#define PT_YIELD(pt)                            \</span>
<a name="l00291"></a>00291 <span class="preprocessor">  do {                                          \</span>
<a name="l00292"></a>00292 <span class="preprocessor">    PT_YIELD_FLAG = 0;                          \</span>
<a name="l00293"></a>00293 <span class="preprocessor">    LC_SET((pt)-&gt;lc);                           \</span>
<a name="l00294"></a>00294 <span class="preprocessor">    if(PT_YIELD_FLAG == 0) {                    \</span>
<a name="l00295"></a>00295 <span class="preprocessor">      return PT_YIELDED;                        \</span>
<a name="l00296"></a>00296 <span class="preprocessor">    }                                           \</span>
<a name="l00297"></a>00297 <span class="preprocessor">  } while(0)</span>
<a name="l00298"></a>00298 <span class="preprocessor"></span><span class="comment"></span>
<a name="l00299"></a>00299 <span class="comment">/**</span>
<a name="l00300"></a>00300 <span class="comment"> * \brief      Yield from the protothread until a condition occurs.</span>
<a name="l00301"></a>00301 <span class="comment"> * \param pt   A pointer to the protothread control structure.</span>
<a name="l00302"></a>00302 <span class="comment"> * \param cond The condition.</span>
<a name="l00303"></a>00303 <span class="comment"> *</span>
<a name="l00304"></a>00304 <span class="comment"> *             This function will yield the protothread, until the</span>
<a name="l00305"></a>00305 <span class="comment"> *             specified condition evaluates to true.</span>
<a name="l00306"></a>00306 <span class="comment"> *</span>
<a name="l00307"></a>00307 <span class="comment"> *</span>
<a name="l00308"></a>00308 <span class="comment"> * \hideinitializer</span>
<a name="l00309"></a>00309 <span class="comment"> */</span>
<a name="l00310"></a><a class="code" href="a00014.html#ge3c821e3a388615528efda9d23c7d115">00310</a> <span class="preprocessor">#define PT_YIELD_UNTIL(pt, cond)                \</span>
<a name="l00311"></a>00311 <span class="preprocessor">  do {                                          \</span>
<a name="l00312"></a>00312 <span class="preprocessor">    PT_YIELD_FLAG = 0;                          \</span>
<a name="l00313"></a>00313 <span class="preprocessor">    LC_SET((pt)-&gt;lc);                           \</span>
<a name="l00314"></a>00314 <span class="preprocessor">    if((PT_YIELD_FLAG == 0) || !(cond)) {       \</span>
<a name="l00315"></a>00315 <span class="preprocessor">      return PT_YIELDED;                        \</span>
<a name="l00316"></a>00316 <span class="preprocessor">    }                                           \</span>
<a name="l00317"></a>00317 <span class="preprocessor">  } while(0)</span>
<a name="l00318"></a>00318 <span class="preprocessor"></span><span class="comment"></span>
<a name="l00319"></a>00319 <span class="comment">/** @} */</span>
<a name="l00320"></a>00320 
<a name="l00321"></a>00321 <span class="preprocessor">#endif </span><span class="comment">/* __PT_H__ */</span>
<a name="l00322"></a>00322 <span class="comment"></span>
<a name="l00323"></a>00323 <span class="comment">/** @} */</span>
</pre></div><hr size="1"><address style="align: right;"><small>Generated on Mon Oct 2 10:06:29 2006 for The Protothreads Library 1.4 by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.4.6 </small></address>
</body>
</html>
