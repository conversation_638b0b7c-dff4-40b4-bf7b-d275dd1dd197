<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html lang="ja">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta http-equiv="Content-Style-Type" content="text/css">
<link rel="up" title="FatFs" href="../00index_j.html">
<link rel="alternate" hreflang="en" title="English" href="../en/chdir.html">
<link rel="stylesheet" href="../css_j.css" type="text/css" media="screen" title="ELM Default">
<title>FatFs - f_chdir</title>
</head>

<body>

<div class="para func">
<h2>f_chdir</h2>
<p>カレント ディレクトリを変更します。</p>
<pre>
FRESULT f_chdir (
  const TCHAR* <span class="arg">path</span> <span class="c">/* [IN] ディレクトリ名へのポインタ */</span>
);
</pre>
</div>

<div class="para arg">
<h4>引数</h4>
<dl class="par">
<dt>path</dt>
<dd>移動対象のディレクトリの<a href="filename.html">パス名</a>を示すヌル文字<tt>'\0'</tt>終端の文字列を指定します。</dd>
</dl>
</div>


<div class="para ret">
<h4>戻り値</h4>
<p>
<a href="rc.html#ok">FR_OK</a>,
<a href="rc.html#de">FR_DISK_ERR</a>,
<a href="rc.html#ie">FR_INT_ERR</a>,
<a href="rc.html#nr">FR_NOT_READY</a>,
<a href="rc.html#ok">FR_NO_FILE</a>,
<a href="rc.html#np">FR_NO_PATH</a>,
<a href="rc.html#in">FR_INVALID_NAME</a>,
<a href="rc.html#id">FR_INVALID_DRIVE</a>,
<a href="rc.html#ne">FR_NOT_ENABLED</a>,
<a href="rc.html#ns">FR_NO_FILESYSTEM</a>,
<a href="rc.html#nc">FR_NOT_ENOUGH_CORE</a>
</p>
</div>


<div class="para desc">
<h4>解説</h4>
<p>各ボリュームのカレント ディレクトリを変更します。カレント ディレクトリは、そのボリュームのマウント動作が行われたとき、ルート ディレクトリに初期設定されます。カレント ディレクトリは、ファイル システム オブジェクトに保持されるため、そのボリュームを使用する全てのタスクに対して影響を与えます。</p>
</div>


<div class="para comp">
<h4>対応情報</h4>
<p><tt>_FS_RPATH == 1</tt>のときに使用可能となります。</p>
</div>


<div class="para exa">
<h4>使用例</h4>
<pre>
    <span class="c">/* カレント ドライブのカレント ディレクトリを変更 (ルート下のdir1へ) */</span>
    f_chdir("/dir1");

    <span class="c">/* ドライブ2のカレント ディレクトリを変更 (親ディレクトリへ) */</span>
    f_chdir("2:..");
</pre>
</div>

<div class="para ref">
<h4>参照</h4>
<p><tt><a href="chdrive.html">f_chdrive</a></tt></p>
</div>

<p class="foot"><a href="../00index_j.html">戻る</a></p>
</body>
</html>
