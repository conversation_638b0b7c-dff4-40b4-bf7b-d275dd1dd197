/* AUTOGENERATED FILE. DO NOT EDIT. */
#ifndef _MOCK_PB_DECODE_H
#define _MOCK_PB_DECODE_H

#ifndef __STATIC_INLINE
#define __STATIC_INLINE
#else
#undef __STATIC_INLINE
#define __STATIC_INLINE
#endif
#define SUPPRESS_INLINE_IMPLEMENTATION

#include <pb_decode.h>
#undef SUPPRESS_INLINE_IMPLEMENTATION
#undef __STATIC_INLINE
#define __STATIC_INLINE __STATIC_INLINE1
#include <cmock_typedefs.h>

void mock_pb_decode_Init(void);
void mock_pb_decode_Destroy(void);
void mock_pb_decode_Verify(void);




#define pb_decode_IgnoreAndReturn(cmock_retval) pb_decode_CMockIgnoreAndReturn(__LINE__, cmock_retval)
void pb_decode_CMockIgnoreAndReturn(UNITY_LINE_TYPE cmock_line, bool cmock_to_return);
#define pb_decode_ExpectAndReturn(stream, fields, dest_struct, cmock_retval) pb_decode_CMockExpectAndReturn(__LINE__, stream, fields, dest_struct, cmock_retval)
void pb_decode_CMockExpectAndReturn(UNITY_LINE_TYPE cmock_line, pb_istream_t* stream, const pb_field_t* fields, void* dest_struct, bool cmock_to_return);
typedef bool (* CMOCK_pb_decode_CALLBACK)(pb_istream_t* stream, const pb_field_t* fields, void* dest_struct, int cmock_num_calls);
void pb_decode_StubWithCallback(CMOCK_pb_decode_CALLBACK Callback);
#define pb_decode_ExpectWithArrayAndReturn(stream, stream_Depth, fields, fields_Depth, dest_struct, dest_struct_Depth, cmock_retval) pb_decode_CMockExpectWithArrayAndReturn(__LINE__, stream, stream_Depth, fields, fields_Depth, dest_struct, dest_struct_Depth, cmock_retval)
void pb_decode_CMockExpectWithArrayAndReturn(UNITY_LINE_TYPE cmock_line, pb_istream_t* stream, int stream_Depth, pb_field_t* fields, int fields_Depth, void* dest_struct, int dest_struct_Depth, bool cmock_to_return);
#define pb_decode_ReturnThruPtr_stream(stream) pb_decode_CMockReturnMemThruPtr_stream(__LINE__, stream, sizeof(*stream))
#define pb_decode_ReturnArrayThruPtr_stream(stream, cmock_len) pb_decode_CMockReturnMemThruPtr_stream(__LINE__, stream, cmock_len * sizeof(*stream))
#define pb_decode_ReturnMemThruPtr_stream(stream, cmock_size) pb_decode_CMockReturnMemThruPtr_stream(__LINE__, stream, cmock_size)
void pb_decode_CMockReturnMemThruPtr_stream(UNITY_LINE_TYPE cmock_line, pb_istream_t* stream, int cmock_size);
#define pb_decode_ReturnThruPtr_dest_struct(dest_struct) pb_decode_CMockReturnMemThruPtr_dest_struct(__LINE__, dest_struct, sizeof(*dest_struct))
#define pb_decode_ReturnArrayThruPtr_dest_struct(dest_struct, cmock_len) pb_decode_CMockReturnMemThruPtr_dest_struct(__LINE__, dest_struct, cmock_len * sizeof(*dest_struct))
#define pb_decode_ReturnMemThruPtr_dest_struct(dest_struct, cmock_size) pb_decode_CMockReturnMemThruPtr_dest_struct(__LINE__, dest_struct, cmock_size)
void pb_decode_CMockReturnMemThruPtr_dest_struct(UNITY_LINE_TYPE cmock_line, void* dest_struct, int cmock_size);
#define pb_decode_IgnoreArg_stream() pb_decode_CMockIgnoreArg_stream(__LINE__)
void pb_decode_CMockIgnoreArg_stream(UNITY_LINE_TYPE cmock_line);
#define pb_decode_IgnoreArg_fields() pb_decode_CMockIgnoreArg_fields(__LINE__)
void pb_decode_CMockIgnoreArg_fields(UNITY_LINE_TYPE cmock_line);
#define pb_decode_IgnoreArg_dest_struct() pb_decode_CMockIgnoreArg_dest_struct(__LINE__)
void pb_decode_CMockIgnoreArg_dest_struct(UNITY_LINE_TYPE cmock_line);
#define pb_decode_noinit_IgnoreAndReturn(cmock_retval) pb_decode_noinit_CMockIgnoreAndReturn(__LINE__, cmock_retval)
void pb_decode_noinit_CMockIgnoreAndReturn(UNITY_LINE_TYPE cmock_line, bool cmock_to_return);
#define pb_decode_noinit_ExpectAndReturn(stream, fields, dest_struct, cmock_retval) pb_decode_noinit_CMockExpectAndReturn(__LINE__, stream, fields, dest_struct, cmock_retval)
void pb_decode_noinit_CMockExpectAndReturn(UNITY_LINE_TYPE cmock_line, pb_istream_t* stream, const pb_field_t* fields, void* dest_struct, bool cmock_to_return);
typedef bool (* CMOCK_pb_decode_noinit_CALLBACK)(pb_istream_t* stream, const pb_field_t* fields, void* dest_struct, int cmock_num_calls);
void pb_decode_noinit_StubWithCallback(CMOCK_pb_decode_noinit_CALLBACK Callback);
#define pb_decode_noinit_ExpectWithArrayAndReturn(stream, stream_Depth, fields, fields_Depth, dest_struct, dest_struct_Depth, cmock_retval) pb_decode_noinit_CMockExpectWithArrayAndReturn(__LINE__, stream, stream_Depth, fields, fields_Depth, dest_struct, dest_struct_Depth, cmock_retval)
void pb_decode_noinit_CMockExpectWithArrayAndReturn(UNITY_LINE_TYPE cmock_line, pb_istream_t* stream, int stream_Depth, pb_field_t* fields, int fields_Depth, void* dest_struct, int dest_struct_Depth, bool cmock_to_return);
#define pb_decode_noinit_ReturnThruPtr_stream(stream) pb_decode_noinit_CMockReturnMemThruPtr_stream(__LINE__, stream, sizeof(*stream))
#define pb_decode_noinit_ReturnArrayThruPtr_stream(stream, cmock_len) pb_decode_noinit_CMockReturnMemThruPtr_stream(__LINE__, stream, cmock_len * sizeof(*stream))
#define pb_decode_noinit_ReturnMemThruPtr_stream(stream, cmock_size) pb_decode_noinit_CMockReturnMemThruPtr_stream(__LINE__, stream, cmock_size)
void pb_decode_noinit_CMockReturnMemThruPtr_stream(UNITY_LINE_TYPE cmock_line, pb_istream_t* stream, int cmock_size);
#define pb_decode_noinit_ReturnThruPtr_dest_struct(dest_struct) pb_decode_noinit_CMockReturnMemThruPtr_dest_struct(__LINE__, dest_struct, sizeof(*dest_struct))
#define pb_decode_noinit_ReturnArrayThruPtr_dest_struct(dest_struct, cmock_len) pb_decode_noinit_CMockReturnMemThruPtr_dest_struct(__LINE__, dest_struct, cmock_len * sizeof(*dest_struct))
#define pb_decode_noinit_ReturnMemThruPtr_dest_struct(dest_struct, cmock_size) pb_decode_noinit_CMockReturnMemThruPtr_dest_struct(__LINE__, dest_struct, cmock_size)
void pb_decode_noinit_CMockReturnMemThruPtr_dest_struct(UNITY_LINE_TYPE cmock_line, void* dest_struct, int cmock_size);
#define pb_decode_noinit_IgnoreArg_stream() pb_decode_noinit_CMockIgnoreArg_stream(__LINE__)
void pb_decode_noinit_CMockIgnoreArg_stream(UNITY_LINE_TYPE cmock_line);
#define pb_decode_noinit_IgnoreArg_fields() pb_decode_noinit_CMockIgnoreArg_fields(__LINE__)
void pb_decode_noinit_CMockIgnoreArg_fields(UNITY_LINE_TYPE cmock_line);
#define pb_decode_noinit_IgnoreArg_dest_struct() pb_decode_noinit_CMockIgnoreArg_dest_struct(__LINE__)
void pb_decode_noinit_CMockIgnoreArg_dest_struct(UNITY_LINE_TYPE cmock_line);
#define pb_decode_delimited_IgnoreAndReturn(cmock_retval) pb_decode_delimited_CMockIgnoreAndReturn(__LINE__, cmock_retval)
void pb_decode_delimited_CMockIgnoreAndReturn(UNITY_LINE_TYPE cmock_line, bool cmock_to_return);
#define pb_decode_delimited_ExpectAndReturn(stream, fields, dest_struct, cmock_retval) pb_decode_delimited_CMockExpectAndReturn(__LINE__, stream, fields, dest_struct, cmock_retval)
void pb_decode_delimited_CMockExpectAndReturn(UNITY_LINE_TYPE cmock_line, pb_istream_t* stream, const pb_field_t* fields, void* dest_struct, bool cmock_to_return);
typedef bool (* CMOCK_pb_decode_delimited_CALLBACK)(pb_istream_t* stream, const pb_field_t* fields, void* dest_struct, int cmock_num_calls);
void pb_decode_delimited_StubWithCallback(CMOCK_pb_decode_delimited_CALLBACK Callback);
#define pb_decode_delimited_ExpectWithArrayAndReturn(stream, stream_Depth, fields, fields_Depth, dest_struct, dest_struct_Depth, cmock_retval) pb_decode_delimited_CMockExpectWithArrayAndReturn(__LINE__, stream, stream_Depth, fields, fields_Depth, dest_struct, dest_struct_Depth, cmock_retval)
void pb_decode_delimited_CMockExpectWithArrayAndReturn(UNITY_LINE_TYPE cmock_line, pb_istream_t* stream, int stream_Depth, pb_field_t* fields, int fields_Depth, void* dest_struct, int dest_struct_Depth, bool cmock_to_return);
#define pb_decode_delimited_ReturnThruPtr_stream(stream) pb_decode_delimited_CMockReturnMemThruPtr_stream(__LINE__, stream, sizeof(*stream))
#define pb_decode_delimited_ReturnArrayThruPtr_stream(stream, cmock_len) pb_decode_delimited_CMockReturnMemThruPtr_stream(__LINE__, stream, cmock_len * sizeof(*stream))
#define pb_decode_delimited_ReturnMemThruPtr_stream(stream, cmock_size) pb_decode_delimited_CMockReturnMemThruPtr_stream(__LINE__, stream, cmock_size)
void pb_decode_delimited_CMockReturnMemThruPtr_stream(UNITY_LINE_TYPE cmock_line, pb_istream_t* stream, int cmock_size);
#define pb_decode_delimited_ReturnThruPtr_dest_struct(dest_struct) pb_decode_delimited_CMockReturnMemThruPtr_dest_struct(__LINE__, dest_struct, sizeof(*dest_struct))
#define pb_decode_delimited_ReturnArrayThruPtr_dest_struct(dest_struct, cmock_len) pb_decode_delimited_CMockReturnMemThruPtr_dest_struct(__LINE__, dest_struct, cmock_len * sizeof(*dest_struct))
#define pb_decode_delimited_ReturnMemThruPtr_dest_struct(dest_struct, cmock_size) pb_decode_delimited_CMockReturnMemThruPtr_dest_struct(__LINE__, dest_struct, cmock_size)
void pb_decode_delimited_CMockReturnMemThruPtr_dest_struct(UNITY_LINE_TYPE cmock_line, void* dest_struct, int cmock_size);
#define pb_decode_delimited_IgnoreArg_stream() pb_decode_delimited_CMockIgnoreArg_stream(__LINE__)
void pb_decode_delimited_CMockIgnoreArg_stream(UNITY_LINE_TYPE cmock_line);
#define pb_decode_delimited_IgnoreArg_fields() pb_decode_delimited_CMockIgnoreArg_fields(__LINE__)
void pb_decode_delimited_CMockIgnoreArg_fields(UNITY_LINE_TYPE cmock_line);
#define pb_decode_delimited_IgnoreArg_dest_struct() pb_decode_delimited_CMockIgnoreArg_dest_struct(__LINE__)
void pb_decode_delimited_CMockIgnoreArg_dest_struct(UNITY_LINE_TYPE cmock_line);
#define pb_release_Ignore() pb_release_CMockIgnore()
void pb_release_CMockIgnore(void);
#define pb_release_Expect(fields, dest_struct) pb_release_CMockExpect(__LINE__, fields, dest_struct)
void pb_release_CMockExpect(UNITY_LINE_TYPE cmock_line, const pb_field_t* fields, void* dest_struct);
typedef void (* CMOCK_pb_release_CALLBACK)(const pb_field_t* fields, void* dest_struct, int cmock_num_calls);
void pb_release_StubWithCallback(CMOCK_pb_release_CALLBACK Callback);
#define pb_release_ExpectWithArray(fields, fields_Depth, dest_struct, dest_struct_Depth) pb_release_CMockExpectWithArray(__LINE__, fields, fields_Depth, dest_struct, dest_struct_Depth)
void pb_release_CMockExpectWithArray(UNITY_LINE_TYPE cmock_line, pb_field_t* fields, int fields_Depth, void* dest_struct, int dest_struct_Depth);
#define pb_release_ReturnThruPtr_dest_struct(dest_struct) pb_release_CMockReturnMemThruPtr_dest_struct(__LINE__, dest_struct, sizeof(*dest_struct))
#define pb_release_ReturnArrayThruPtr_dest_struct(dest_struct, cmock_len) pb_release_CMockReturnMemThruPtr_dest_struct(__LINE__, dest_struct, cmock_len * sizeof(*dest_struct))
#define pb_release_ReturnMemThruPtr_dest_struct(dest_struct, cmock_size) pb_release_CMockReturnMemThruPtr_dest_struct(__LINE__, dest_struct, cmock_size)
void pb_release_CMockReturnMemThruPtr_dest_struct(UNITY_LINE_TYPE cmock_line, void* dest_struct, int cmock_size);
#define pb_release_IgnoreArg_fields() pb_release_CMockIgnoreArg_fields(__LINE__)
void pb_release_CMockIgnoreArg_fields(UNITY_LINE_TYPE cmock_line);
#define pb_release_IgnoreArg_dest_struct() pb_release_CMockIgnoreArg_dest_struct(__LINE__)
void pb_release_CMockIgnoreArg_dest_struct(UNITY_LINE_TYPE cmock_line);
#define pb_istream_from_buffer_IgnoreAndReturn(cmock_retval) pb_istream_from_buffer_CMockIgnoreAndReturn(__LINE__, cmock_retval)
void pb_istream_from_buffer_CMockIgnoreAndReturn(UNITY_LINE_TYPE cmock_line, pb_istream_t cmock_to_return);
#define pb_istream_from_buffer_ExpectAndReturn(buf, bufsize, cmock_retval) pb_istream_from_buffer_CMockExpectAndReturn(__LINE__, buf, bufsize, cmock_retval)
void pb_istream_from_buffer_CMockExpectAndReturn(UNITY_LINE_TYPE cmock_line, const pb_byte_t* buf, size_t bufsize, pb_istream_t cmock_to_return);
typedef pb_istream_t (* CMOCK_pb_istream_from_buffer_CALLBACK)(const pb_byte_t* buf, size_t bufsize, int cmock_num_calls);
void pb_istream_from_buffer_StubWithCallback(CMOCK_pb_istream_from_buffer_CALLBACK Callback);
#define pb_istream_from_buffer_ExpectWithArrayAndReturn(buf, buf_Depth, bufsize, cmock_retval) pb_istream_from_buffer_CMockExpectWithArrayAndReturn(__LINE__, buf, buf_Depth, bufsize, cmock_retval)
void pb_istream_from_buffer_CMockExpectWithArrayAndReturn(UNITY_LINE_TYPE cmock_line, pb_byte_t* buf, int buf_Depth, size_t bufsize, pb_istream_t cmock_to_return);
#define pb_istream_from_buffer_IgnoreArg_buf() pb_istream_from_buffer_CMockIgnoreArg_buf(__LINE__)
void pb_istream_from_buffer_CMockIgnoreArg_buf(UNITY_LINE_TYPE cmock_line);
#define pb_istream_from_buffer_IgnoreArg_bufsize() pb_istream_from_buffer_CMockIgnoreArg_bufsize(__LINE__)
void pb_istream_from_buffer_CMockIgnoreArg_bufsize(UNITY_LINE_TYPE cmock_line);
#define pb_read_IgnoreAndReturn(cmock_retval) pb_read_CMockIgnoreAndReturn(__LINE__, cmock_retval)
void pb_read_CMockIgnoreAndReturn(UNITY_LINE_TYPE cmock_line, bool cmock_to_return);
#define pb_read_ExpectAndReturn(stream, buf, count, cmock_retval) pb_read_CMockExpectAndReturn(__LINE__, stream, buf, count, cmock_retval)
void pb_read_CMockExpectAndReturn(UNITY_LINE_TYPE cmock_line, pb_istream_t* stream, pb_byte_t* buf, size_t count, bool cmock_to_return);
typedef bool (* CMOCK_pb_read_CALLBACK)(pb_istream_t* stream, pb_byte_t* buf, size_t count, int cmock_num_calls);
void pb_read_StubWithCallback(CMOCK_pb_read_CALLBACK Callback);
#define pb_read_ExpectWithArrayAndReturn(stream, stream_Depth, buf, buf_Depth, count, cmock_retval) pb_read_CMockExpectWithArrayAndReturn(__LINE__, stream, stream_Depth, buf, buf_Depth, count, cmock_retval)
void pb_read_CMockExpectWithArrayAndReturn(UNITY_LINE_TYPE cmock_line, pb_istream_t* stream, int stream_Depth, pb_byte_t* buf, int buf_Depth, size_t count, bool cmock_to_return);
#define pb_read_ReturnThruPtr_stream(stream) pb_read_CMockReturnMemThruPtr_stream(__LINE__, stream, sizeof(*stream))
#define pb_read_ReturnArrayThruPtr_stream(stream, cmock_len) pb_read_CMockReturnMemThruPtr_stream(__LINE__, stream, cmock_len * sizeof(*stream))
#define pb_read_ReturnMemThruPtr_stream(stream, cmock_size) pb_read_CMockReturnMemThruPtr_stream(__LINE__, stream, cmock_size)
void pb_read_CMockReturnMemThruPtr_stream(UNITY_LINE_TYPE cmock_line, pb_istream_t* stream, int cmock_size);
#define pb_read_ReturnThruPtr_buf(buf) pb_read_CMockReturnMemThruPtr_buf(__LINE__, buf, sizeof(*buf))
#define pb_read_ReturnArrayThruPtr_buf(buf, cmock_len) pb_read_CMockReturnMemThruPtr_buf(__LINE__, buf, cmock_len * sizeof(*buf))
#define pb_read_ReturnMemThruPtr_buf(buf, cmock_size) pb_read_CMockReturnMemThruPtr_buf(__LINE__, buf, cmock_size)
void pb_read_CMockReturnMemThruPtr_buf(UNITY_LINE_TYPE cmock_line, pb_byte_t* buf, int cmock_size);
#define pb_read_IgnoreArg_stream() pb_read_CMockIgnoreArg_stream(__LINE__)
void pb_read_CMockIgnoreArg_stream(UNITY_LINE_TYPE cmock_line);
#define pb_read_IgnoreArg_buf() pb_read_CMockIgnoreArg_buf(__LINE__)
void pb_read_CMockIgnoreArg_buf(UNITY_LINE_TYPE cmock_line);
#define pb_read_IgnoreArg_count() pb_read_CMockIgnoreArg_count(__LINE__)
void pb_read_CMockIgnoreArg_count(UNITY_LINE_TYPE cmock_line);
#define pb_decode_tag_IgnoreAndReturn(cmock_retval) pb_decode_tag_CMockIgnoreAndReturn(__LINE__, cmock_retval)
void pb_decode_tag_CMockIgnoreAndReturn(UNITY_LINE_TYPE cmock_line, bool cmock_to_return);
#define pb_decode_tag_ExpectAndReturn(stream, wire_type, tag, eof, cmock_retval) pb_decode_tag_CMockExpectAndReturn(__LINE__, stream, wire_type, tag, eof, cmock_retval)
void pb_decode_tag_CMockExpectAndReturn(UNITY_LINE_TYPE cmock_line, pb_istream_t* stream, pb_wire_type_t* wire_type, uint32_t* tag, bool* eof, bool cmock_to_return);
typedef bool (* CMOCK_pb_decode_tag_CALLBACK)(pb_istream_t* stream, pb_wire_type_t* wire_type, uint32_t* tag, bool* eof, int cmock_num_calls);
void pb_decode_tag_StubWithCallback(CMOCK_pb_decode_tag_CALLBACK Callback);
#define pb_decode_tag_ExpectWithArrayAndReturn(stream, stream_Depth, wire_type, wire_type_Depth, tag, tag_Depth, eof, eof_Depth, cmock_retval) pb_decode_tag_CMockExpectWithArrayAndReturn(__LINE__, stream, stream_Depth, wire_type, wire_type_Depth, tag, tag_Depth, eof, eof_Depth, cmock_retval)
void pb_decode_tag_CMockExpectWithArrayAndReturn(UNITY_LINE_TYPE cmock_line, pb_istream_t* stream, int stream_Depth, pb_wire_type_t* wire_type, int wire_type_Depth, uint32_t* tag, int tag_Depth, bool* eof, int eof_Depth, bool cmock_to_return);
#define pb_decode_tag_ReturnThruPtr_stream(stream) pb_decode_tag_CMockReturnMemThruPtr_stream(__LINE__, stream, sizeof(*stream))
#define pb_decode_tag_ReturnArrayThruPtr_stream(stream, cmock_len) pb_decode_tag_CMockReturnMemThruPtr_stream(__LINE__, stream, cmock_len * sizeof(*stream))
#define pb_decode_tag_ReturnMemThruPtr_stream(stream, cmock_size) pb_decode_tag_CMockReturnMemThruPtr_stream(__LINE__, stream, cmock_size)
void pb_decode_tag_CMockReturnMemThruPtr_stream(UNITY_LINE_TYPE cmock_line, pb_istream_t* stream, int cmock_size);
#define pb_decode_tag_ReturnThruPtr_wire_type(wire_type) pb_decode_tag_CMockReturnMemThruPtr_wire_type(__LINE__, wire_type, sizeof(*wire_type))
#define pb_decode_tag_ReturnArrayThruPtr_wire_type(wire_type, cmock_len) pb_decode_tag_CMockReturnMemThruPtr_wire_type(__LINE__, wire_type, cmock_len * sizeof(*wire_type))
#define pb_decode_tag_ReturnMemThruPtr_wire_type(wire_type, cmock_size) pb_decode_tag_CMockReturnMemThruPtr_wire_type(__LINE__, wire_type, cmock_size)
void pb_decode_tag_CMockReturnMemThruPtr_wire_type(UNITY_LINE_TYPE cmock_line, pb_wire_type_t* wire_type, int cmock_size);
#define pb_decode_tag_ReturnThruPtr_tag(tag) pb_decode_tag_CMockReturnMemThruPtr_tag(__LINE__, tag, sizeof(*tag))
#define pb_decode_tag_ReturnArrayThruPtr_tag(tag, cmock_len) pb_decode_tag_CMockReturnMemThruPtr_tag(__LINE__, tag, cmock_len * sizeof(*tag))
#define pb_decode_tag_ReturnMemThruPtr_tag(tag, cmock_size) pb_decode_tag_CMockReturnMemThruPtr_tag(__LINE__, tag, cmock_size)
void pb_decode_tag_CMockReturnMemThruPtr_tag(UNITY_LINE_TYPE cmock_line, uint32_t* tag, int cmock_size);
#define pb_decode_tag_ReturnThruPtr_eof(eof) pb_decode_tag_CMockReturnMemThruPtr_eof(__LINE__, eof, sizeof(*eof))
#define pb_decode_tag_ReturnArrayThruPtr_eof(eof, cmock_len) pb_decode_tag_CMockReturnMemThruPtr_eof(__LINE__, eof, cmock_len * sizeof(*eof))
#define pb_decode_tag_ReturnMemThruPtr_eof(eof, cmock_size) pb_decode_tag_CMockReturnMemThruPtr_eof(__LINE__, eof, cmock_size)
void pb_decode_tag_CMockReturnMemThruPtr_eof(UNITY_LINE_TYPE cmock_line, bool* eof, int cmock_size);
#define pb_decode_tag_IgnoreArg_stream() pb_decode_tag_CMockIgnoreArg_stream(__LINE__)
void pb_decode_tag_CMockIgnoreArg_stream(UNITY_LINE_TYPE cmock_line);
#define pb_decode_tag_IgnoreArg_wire_type() pb_decode_tag_CMockIgnoreArg_wire_type(__LINE__)
void pb_decode_tag_CMockIgnoreArg_wire_type(UNITY_LINE_TYPE cmock_line);
#define pb_decode_tag_IgnoreArg_tag() pb_decode_tag_CMockIgnoreArg_tag(__LINE__)
void pb_decode_tag_CMockIgnoreArg_tag(UNITY_LINE_TYPE cmock_line);
#define pb_decode_tag_IgnoreArg_eof() pb_decode_tag_CMockIgnoreArg_eof(__LINE__)
void pb_decode_tag_CMockIgnoreArg_eof(UNITY_LINE_TYPE cmock_line);
#define pb_skip_field_IgnoreAndReturn(cmock_retval) pb_skip_field_CMockIgnoreAndReturn(__LINE__, cmock_retval)
void pb_skip_field_CMockIgnoreAndReturn(UNITY_LINE_TYPE cmock_line, bool cmock_to_return);
#define pb_skip_field_ExpectAndReturn(stream, wire_type, cmock_retval) pb_skip_field_CMockExpectAndReturn(__LINE__, stream, wire_type, cmock_retval)
void pb_skip_field_CMockExpectAndReturn(UNITY_LINE_TYPE cmock_line, pb_istream_t* stream, pb_wire_type_t wire_type, bool cmock_to_return);
typedef bool (* CMOCK_pb_skip_field_CALLBACK)(pb_istream_t* stream, pb_wire_type_t wire_type, int cmock_num_calls);
void pb_skip_field_StubWithCallback(CMOCK_pb_skip_field_CALLBACK Callback);
#define pb_skip_field_ExpectWithArrayAndReturn(stream, stream_Depth, wire_type, cmock_retval) pb_skip_field_CMockExpectWithArrayAndReturn(__LINE__, stream, stream_Depth, wire_type, cmock_retval)
void pb_skip_field_CMockExpectWithArrayAndReturn(UNITY_LINE_TYPE cmock_line, pb_istream_t* stream, int stream_Depth, pb_wire_type_t wire_type, bool cmock_to_return);
#define pb_skip_field_ReturnThruPtr_stream(stream) pb_skip_field_CMockReturnMemThruPtr_stream(__LINE__, stream, sizeof(*stream))
#define pb_skip_field_ReturnArrayThruPtr_stream(stream, cmock_len) pb_skip_field_CMockReturnMemThruPtr_stream(__LINE__, stream, cmock_len * sizeof(*stream))
#define pb_skip_field_ReturnMemThruPtr_stream(stream, cmock_size) pb_skip_field_CMockReturnMemThruPtr_stream(__LINE__, stream, cmock_size)
void pb_skip_field_CMockReturnMemThruPtr_stream(UNITY_LINE_TYPE cmock_line, pb_istream_t* stream, int cmock_size);
#define pb_skip_field_IgnoreArg_stream() pb_skip_field_CMockIgnoreArg_stream(__LINE__)
void pb_skip_field_CMockIgnoreArg_stream(UNITY_LINE_TYPE cmock_line);
#define pb_skip_field_IgnoreArg_wire_type() pb_skip_field_CMockIgnoreArg_wire_type(__LINE__)
void pb_skip_field_CMockIgnoreArg_wire_type(UNITY_LINE_TYPE cmock_line);
#define pb_decode_varint_IgnoreAndReturn(cmock_retval) pb_decode_varint_CMockIgnoreAndReturn(__LINE__, cmock_retval)
void pb_decode_varint_CMockIgnoreAndReturn(UNITY_LINE_TYPE cmock_line, bool cmock_to_return);
#define pb_decode_varint_ExpectAndReturn(stream, dest, cmock_retval) pb_decode_varint_CMockExpectAndReturn(__LINE__, stream, dest, cmock_retval)
void pb_decode_varint_CMockExpectAndReturn(UNITY_LINE_TYPE cmock_line, pb_istream_t* stream, uint64_t* dest, bool cmock_to_return);
typedef bool (* CMOCK_pb_decode_varint_CALLBACK)(pb_istream_t* stream, uint64_t* dest, int cmock_num_calls);
void pb_decode_varint_StubWithCallback(CMOCK_pb_decode_varint_CALLBACK Callback);
#define pb_decode_varint_ExpectWithArrayAndReturn(stream, stream_Depth, dest, dest_Depth, cmock_retval) pb_decode_varint_CMockExpectWithArrayAndReturn(__LINE__, stream, stream_Depth, dest, dest_Depth, cmock_retval)
void pb_decode_varint_CMockExpectWithArrayAndReturn(UNITY_LINE_TYPE cmock_line, pb_istream_t* stream, int stream_Depth, uint64_t* dest, int dest_Depth, bool cmock_to_return);
#define pb_decode_varint_ReturnThruPtr_stream(stream) pb_decode_varint_CMockReturnMemThruPtr_stream(__LINE__, stream, sizeof(*stream))
#define pb_decode_varint_ReturnArrayThruPtr_stream(stream, cmock_len) pb_decode_varint_CMockReturnMemThruPtr_stream(__LINE__, stream, cmock_len * sizeof(*stream))
#define pb_decode_varint_ReturnMemThruPtr_stream(stream, cmock_size) pb_decode_varint_CMockReturnMemThruPtr_stream(__LINE__, stream, cmock_size)
void pb_decode_varint_CMockReturnMemThruPtr_stream(UNITY_LINE_TYPE cmock_line, pb_istream_t* stream, int cmock_size);
#define pb_decode_varint_ReturnThruPtr_dest(dest) pb_decode_varint_CMockReturnMemThruPtr_dest(__LINE__, dest, sizeof(*dest))
#define pb_decode_varint_ReturnArrayThruPtr_dest(dest, cmock_len) pb_decode_varint_CMockReturnMemThruPtr_dest(__LINE__, dest, cmock_len * sizeof(*dest))
#define pb_decode_varint_ReturnMemThruPtr_dest(dest, cmock_size) pb_decode_varint_CMockReturnMemThruPtr_dest(__LINE__, dest, cmock_size)
void pb_decode_varint_CMockReturnMemThruPtr_dest(UNITY_LINE_TYPE cmock_line, uint64_t* dest, int cmock_size);
#define pb_decode_varint_IgnoreArg_stream() pb_decode_varint_CMockIgnoreArg_stream(__LINE__)
void pb_decode_varint_CMockIgnoreArg_stream(UNITY_LINE_TYPE cmock_line);
#define pb_decode_varint_IgnoreArg_dest() pb_decode_varint_CMockIgnoreArg_dest(__LINE__)
void pb_decode_varint_CMockIgnoreArg_dest(UNITY_LINE_TYPE cmock_line);
#define pb_decode_svarint_IgnoreAndReturn(cmock_retval) pb_decode_svarint_CMockIgnoreAndReturn(__LINE__, cmock_retval)
void pb_decode_svarint_CMockIgnoreAndReturn(UNITY_LINE_TYPE cmock_line, bool cmock_to_return);
#define pb_decode_svarint_ExpectAndReturn(stream, dest, cmock_retval) pb_decode_svarint_CMockExpectAndReturn(__LINE__, stream, dest, cmock_retval)
void pb_decode_svarint_CMockExpectAndReturn(UNITY_LINE_TYPE cmock_line, pb_istream_t* stream, int64_t* dest, bool cmock_to_return);
typedef bool (* CMOCK_pb_decode_svarint_CALLBACK)(pb_istream_t* stream, int64_t* dest, int cmock_num_calls);
void pb_decode_svarint_StubWithCallback(CMOCK_pb_decode_svarint_CALLBACK Callback);
#define pb_decode_svarint_ExpectWithArrayAndReturn(stream, stream_Depth, dest, dest_Depth, cmock_retval) pb_decode_svarint_CMockExpectWithArrayAndReturn(__LINE__, stream, stream_Depth, dest, dest_Depth, cmock_retval)
void pb_decode_svarint_CMockExpectWithArrayAndReturn(UNITY_LINE_TYPE cmock_line, pb_istream_t* stream, int stream_Depth, int64_t* dest, int dest_Depth, bool cmock_to_return);
#define pb_decode_svarint_ReturnThruPtr_stream(stream) pb_decode_svarint_CMockReturnMemThruPtr_stream(__LINE__, stream, sizeof(*stream))
#define pb_decode_svarint_ReturnArrayThruPtr_stream(stream, cmock_len) pb_decode_svarint_CMockReturnMemThruPtr_stream(__LINE__, stream, cmock_len * sizeof(*stream))
#define pb_decode_svarint_ReturnMemThruPtr_stream(stream, cmock_size) pb_decode_svarint_CMockReturnMemThruPtr_stream(__LINE__, stream, cmock_size)
void pb_decode_svarint_CMockReturnMemThruPtr_stream(UNITY_LINE_TYPE cmock_line, pb_istream_t* stream, int cmock_size);
#define pb_decode_svarint_ReturnThruPtr_dest(dest) pb_decode_svarint_CMockReturnMemThruPtr_dest(__LINE__, dest, sizeof(*dest))
#define pb_decode_svarint_ReturnArrayThruPtr_dest(dest, cmock_len) pb_decode_svarint_CMockReturnMemThruPtr_dest(__LINE__, dest, cmock_len * sizeof(*dest))
#define pb_decode_svarint_ReturnMemThruPtr_dest(dest, cmock_size) pb_decode_svarint_CMockReturnMemThruPtr_dest(__LINE__, dest, cmock_size)
void pb_decode_svarint_CMockReturnMemThruPtr_dest(UNITY_LINE_TYPE cmock_line, int64_t* dest, int cmock_size);
#define pb_decode_svarint_IgnoreArg_stream() pb_decode_svarint_CMockIgnoreArg_stream(__LINE__)
void pb_decode_svarint_CMockIgnoreArg_stream(UNITY_LINE_TYPE cmock_line);
#define pb_decode_svarint_IgnoreArg_dest() pb_decode_svarint_CMockIgnoreArg_dest(__LINE__)
void pb_decode_svarint_CMockIgnoreArg_dest(UNITY_LINE_TYPE cmock_line);
#define pb_decode_fixed32_IgnoreAndReturn(cmock_retval) pb_decode_fixed32_CMockIgnoreAndReturn(__LINE__, cmock_retval)
void pb_decode_fixed32_CMockIgnoreAndReturn(UNITY_LINE_TYPE cmock_line, bool cmock_to_return);
#define pb_decode_fixed32_ExpectAndReturn(stream, dest, cmock_retval) pb_decode_fixed32_CMockExpectAndReturn(__LINE__, stream, dest, cmock_retval)
void pb_decode_fixed32_CMockExpectAndReturn(UNITY_LINE_TYPE cmock_line, pb_istream_t* stream, void* dest, bool cmock_to_return);
typedef bool (* CMOCK_pb_decode_fixed32_CALLBACK)(pb_istream_t* stream, void* dest, int cmock_num_calls);
void pb_decode_fixed32_StubWithCallback(CMOCK_pb_decode_fixed32_CALLBACK Callback);
#define pb_decode_fixed32_ExpectWithArrayAndReturn(stream, stream_Depth, dest, dest_Depth, cmock_retval) pb_decode_fixed32_CMockExpectWithArrayAndReturn(__LINE__, stream, stream_Depth, dest, dest_Depth, cmock_retval)
void pb_decode_fixed32_CMockExpectWithArrayAndReturn(UNITY_LINE_TYPE cmock_line, pb_istream_t* stream, int stream_Depth, void* dest, int dest_Depth, bool cmock_to_return);
#define pb_decode_fixed32_ReturnThruPtr_stream(stream) pb_decode_fixed32_CMockReturnMemThruPtr_stream(__LINE__, stream, sizeof(*stream))
#define pb_decode_fixed32_ReturnArrayThruPtr_stream(stream, cmock_len) pb_decode_fixed32_CMockReturnMemThruPtr_stream(__LINE__, stream, cmock_len * sizeof(*stream))
#define pb_decode_fixed32_ReturnMemThruPtr_stream(stream, cmock_size) pb_decode_fixed32_CMockReturnMemThruPtr_stream(__LINE__, stream, cmock_size)
void pb_decode_fixed32_CMockReturnMemThruPtr_stream(UNITY_LINE_TYPE cmock_line, pb_istream_t* stream, int cmock_size);
#define pb_decode_fixed32_ReturnThruPtr_dest(dest) pb_decode_fixed32_CMockReturnMemThruPtr_dest(__LINE__, dest, sizeof(*dest))
#define pb_decode_fixed32_ReturnArrayThruPtr_dest(dest, cmock_len) pb_decode_fixed32_CMockReturnMemThruPtr_dest(__LINE__, dest, cmock_len * sizeof(*dest))
#define pb_decode_fixed32_ReturnMemThruPtr_dest(dest, cmock_size) pb_decode_fixed32_CMockReturnMemThruPtr_dest(__LINE__, dest, cmock_size)
void pb_decode_fixed32_CMockReturnMemThruPtr_dest(UNITY_LINE_TYPE cmock_line, void* dest, int cmock_size);
#define pb_decode_fixed32_IgnoreArg_stream() pb_decode_fixed32_CMockIgnoreArg_stream(__LINE__)
void pb_decode_fixed32_CMockIgnoreArg_stream(UNITY_LINE_TYPE cmock_line);
#define pb_decode_fixed32_IgnoreArg_dest() pb_decode_fixed32_CMockIgnoreArg_dest(__LINE__)
void pb_decode_fixed32_CMockIgnoreArg_dest(UNITY_LINE_TYPE cmock_line);
#define pb_decode_fixed64_IgnoreAndReturn(cmock_retval) pb_decode_fixed64_CMockIgnoreAndReturn(__LINE__, cmock_retval)
void pb_decode_fixed64_CMockIgnoreAndReturn(UNITY_LINE_TYPE cmock_line, bool cmock_to_return);
#define pb_decode_fixed64_ExpectAndReturn(stream, dest, cmock_retval) pb_decode_fixed64_CMockExpectAndReturn(__LINE__, stream, dest, cmock_retval)
void pb_decode_fixed64_CMockExpectAndReturn(UNITY_LINE_TYPE cmock_line, pb_istream_t* stream, void* dest, bool cmock_to_return);
typedef bool (* CMOCK_pb_decode_fixed64_CALLBACK)(pb_istream_t* stream, void* dest, int cmock_num_calls);
void pb_decode_fixed64_StubWithCallback(CMOCK_pb_decode_fixed64_CALLBACK Callback);
#define pb_decode_fixed64_ExpectWithArrayAndReturn(stream, stream_Depth, dest, dest_Depth, cmock_retval) pb_decode_fixed64_CMockExpectWithArrayAndReturn(__LINE__, stream, stream_Depth, dest, dest_Depth, cmock_retval)
void pb_decode_fixed64_CMockExpectWithArrayAndReturn(UNITY_LINE_TYPE cmock_line, pb_istream_t* stream, int stream_Depth, void* dest, int dest_Depth, bool cmock_to_return);
#define pb_decode_fixed64_ReturnThruPtr_stream(stream) pb_decode_fixed64_CMockReturnMemThruPtr_stream(__LINE__, stream, sizeof(*stream))
#define pb_decode_fixed64_ReturnArrayThruPtr_stream(stream, cmock_len) pb_decode_fixed64_CMockReturnMemThruPtr_stream(__LINE__, stream, cmock_len * sizeof(*stream))
#define pb_decode_fixed64_ReturnMemThruPtr_stream(stream, cmock_size) pb_decode_fixed64_CMockReturnMemThruPtr_stream(__LINE__, stream, cmock_size)
void pb_decode_fixed64_CMockReturnMemThruPtr_stream(UNITY_LINE_TYPE cmock_line, pb_istream_t* stream, int cmock_size);
#define pb_decode_fixed64_ReturnThruPtr_dest(dest) pb_decode_fixed64_CMockReturnMemThruPtr_dest(__LINE__, dest, sizeof(*dest))
#define pb_decode_fixed64_ReturnArrayThruPtr_dest(dest, cmock_len) pb_decode_fixed64_CMockReturnMemThruPtr_dest(__LINE__, dest, cmock_len * sizeof(*dest))
#define pb_decode_fixed64_ReturnMemThruPtr_dest(dest, cmock_size) pb_decode_fixed64_CMockReturnMemThruPtr_dest(__LINE__, dest, cmock_size)
void pb_decode_fixed64_CMockReturnMemThruPtr_dest(UNITY_LINE_TYPE cmock_line, void* dest, int cmock_size);
#define pb_decode_fixed64_IgnoreArg_stream() pb_decode_fixed64_CMockIgnoreArg_stream(__LINE__)
void pb_decode_fixed64_CMockIgnoreArg_stream(UNITY_LINE_TYPE cmock_line);
#define pb_decode_fixed64_IgnoreArg_dest() pb_decode_fixed64_CMockIgnoreArg_dest(__LINE__)
void pb_decode_fixed64_CMockIgnoreArg_dest(UNITY_LINE_TYPE cmock_line);
#define pb_make_string_substream_IgnoreAndReturn(cmock_retval) pb_make_string_substream_CMockIgnoreAndReturn(__LINE__, cmock_retval)
void pb_make_string_substream_CMockIgnoreAndReturn(UNITY_LINE_TYPE cmock_line, bool cmock_to_return);
#define pb_make_string_substream_ExpectAndReturn(stream, substream, cmock_retval) pb_make_string_substream_CMockExpectAndReturn(__LINE__, stream, substream, cmock_retval)
void pb_make_string_substream_CMockExpectAndReturn(UNITY_LINE_TYPE cmock_line, pb_istream_t* stream, pb_istream_t* substream, bool cmock_to_return);
typedef bool (* CMOCK_pb_make_string_substream_CALLBACK)(pb_istream_t* stream, pb_istream_t* substream, int cmock_num_calls);
void pb_make_string_substream_StubWithCallback(CMOCK_pb_make_string_substream_CALLBACK Callback);
#define pb_make_string_substream_ExpectWithArrayAndReturn(stream, stream_Depth, substream, substream_Depth, cmock_retval) pb_make_string_substream_CMockExpectWithArrayAndReturn(__LINE__, stream, stream_Depth, substream, substream_Depth, cmock_retval)
void pb_make_string_substream_CMockExpectWithArrayAndReturn(UNITY_LINE_TYPE cmock_line, pb_istream_t* stream, int stream_Depth, pb_istream_t* substream, int substream_Depth, bool cmock_to_return);
#define pb_make_string_substream_ReturnThruPtr_stream(stream) pb_make_string_substream_CMockReturnMemThruPtr_stream(__LINE__, stream, sizeof(*stream))
#define pb_make_string_substream_ReturnArrayThruPtr_stream(stream, cmock_len) pb_make_string_substream_CMockReturnMemThruPtr_stream(__LINE__, stream, cmock_len * sizeof(*stream))
#define pb_make_string_substream_ReturnMemThruPtr_stream(stream, cmock_size) pb_make_string_substream_CMockReturnMemThruPtr_stream(__LINE__, stream, cmock_size)
void pb_make_string_substream_CMockReturnMemThruPtr_stream(UNITY_LINE_TYPE cmock_line, pb_istream_t* stream, int cmock_size);
#define pb_make_string_substream_ReturnThruPtr_substream(substream) pb_make_string_substream_CMockReturnMemThruPtr_substream(__LINE__, substream, sizeof(*substream))
#define pb_make_string_substream_ReturnArrayThruPtr_substream(substream, cmock_len) pb_make_string_substream_CMockReturnMemThruPtr_substream(__LINE__, substream, cmock_len * sizeof(*substream))
#define pb_make_string_substream_ReturnMemThruPtr_substream(substream, cmock_size) pb_make_string_substream_CMockReturnMemThruPtr_substream(__LINE__, substream, cmock_size)
void pb_make_string_substream_CMockReturnMemThruPtr_substream(UNITY_LINE_TYPE cmock_line, pb_istream_t* substream, int cmock_size);
#define pb_make_string_substream_IgnoreArg_stream() pb_make_string_substream_CMockIgnoreArg_stream(__LINE__)
void pb_make_string_substream_CMockIgnoreArg_stream(UNITY_LINE_TYPE cmock_line);
#define pb_make_string_substream_IgnoreArg_substream() pb_make_string_substream_CMockIgnoreArg_substream(__LINE__)
void pb_make_string_substream_CMockIgnoreArg_substream(UNITY_LINE_TYPE cmock_line);
#define pb_close_string_substream_Ignore() pb_close_string_substream_CMockIgnore()
void pb_close_string_substream_CMockIgnore(void);
#define pb_close_string_substream_Expect(stream, substream) pb_close_string_substream_CMockExpect(__LINE__, stream, substream)
void pb_close_string_substream_CMockExpect(UNITY_LINE_TYPE cmock_line, pb_istream_t* stream, pb_istream_t* substream);
typedef void (* CMOCK_pb_close_string_substream_CALLBACK)(pb_istream_t* stream, pb_istream_t* substream, int cmock_num_calls);
void pb_close_string_substream_StubWithCallback(CMOCK_pb_close_string_substream_CALLBACK Callback);
#define pb_close_string_substream_ExpectWithArray(stream, stream_Depth, substream, substream_Depth) pb_close_string_substream_CMockExpectWithArray(__LINE__, stream, stream_Depth, substream, substream_Depth)
void pb_close_string_substream_CMockExpectWithArray(UNITY_LINE_TYPE cmock_line, pb_istream_t* stream, int stream_Depth, pb_istream_t* substream, int substream_Depth);
#define pb_close_string_substream_ReturnThruPtr_stream(stream) pb_close_string_substream_CMockReturnMemThruPtr_stream(__LINE__, stream, sizeof(*stream))
#define pb_close_string_substream_ReturnArrayThruPtr_stream(stream, cmock_len) pb_close_string_substream_CMockReturnMemThruPtr_stream(__LINE__, stream, cmock_len * sizeof(*stream))
#define pb_close_string_substream_ReturnMemThruPtr_stream(stream, cmock_size) pb_close_string_substream_CMockReturnMemThruPtr_stream(__LINE__, stream, cmock_size)
void pb_close_string_substream_CMockReturnMemThruPtr_stream(UNITY_LINE_TYPE cmock_line, pb_istream_t* stream, int cmock_size);
#define pb_close_string_substream_ReturnThruPtr_substream(substream) pb_close_string_substream_CMockReturnMemThruPtr_substream(__LINE__, substream, sizeof(*substream))
#define pb_close_string_substream_ReturnArrayThruPtr_substream(substream, cmock_len) pb_close_string_substream_CMockReturnMemThruPtr_substream(__LINE__, substream, cmock_len * sizeof(*substream))
#define pb_close_string_substream_ReturnMemThruPtr_substream(substream, cmock_size) pb_close_string_substream_CMockReturnMemThruPtr_substream(__LINE__, substream, cmock_size)
void pb_close_string_substream_CMockReturnMemThruPtr_substream(UNITY_LINE_TYPE cmock_line, pb_istream_t* substream, int cmock_size);
#define pb_close_string_substream_IgnoreArg_stream() pb_close_string_substream_CMockIgnoreArg_stream(__LINE__)
void pb_close_string_substream_CMockIgnoreArg_stream(UNITY_LINE_TYPE cmock_line);
#define pb_close_string_substream_IgnoreArg_substream() pb_close_string_substream_CMockIgnoreArg_substream(__LINE__)
void pb_close_string_substream_CMockIgnoreArg_substream(UNITY_LINE_TYPE cmock_line);

#endif
