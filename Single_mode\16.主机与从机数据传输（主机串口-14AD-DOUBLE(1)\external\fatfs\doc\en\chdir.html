<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html lang="en">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<meta http-equiv="Content-Style-Type" content="text/css">
<link rel="up" title="FatFs" href="../00index_e.html">
<link rel="alternate" hreflang="ja" title="Japanese" href="../ja/chdir.html">
<link rel="stylesheet" href="../css_e.css" type="text/css" media="screen" title="ELM Default">
<title>FatFs - f_chdir</title>
</head>

<body>

<div class="para func">
<h2>f_chdir</h2>
<p>The f_chdir function changes the current directory of a drive.</p>
<pre>
FRESULT f_chdir (
  const TCHAR* <span class="arg">path</span> <span class="c">/* [IN] Path name */</span>
);
</pre>
</div>

<div class="para arg">
<h4>Parameters</h4>
<dl class="par">
<dt>path</dt>
<dd>Pointer to the null-terminated string that specifies the <a href="filename.html">directory</a> to go.</dd>
</dl>
</div>


<div class="para ret">
<h4>Return Values</h4>
<p>
<a href="rc.html#ok">FR_OK</a>,
<a href="rc.html#de">FR_DISK_ERR</a>,
<a href="rc.html#ie">FR_INT_ERR</a>,
<a href="rc.html#nr">FR_NOT_READY</a>,
<a href="rc.html#np">FR_NO_PATH</a>,
<a href="rc.html#in">FR_INVALID_NAME</a>,
<a href="rc.html#id">FR_INVALID_DRIVE</a>,
<a href="rc.html#ne">FR_NOT_ENABLED</a>,
<a href="rc.html#ns">FR_NO_FILESYSTEM</a>,
<a href="rc.html#tm">FR_TIMEOUT</a>,
<a href="rc.html#nc">FR_NOT_ENOUGH_CORE</a>
</p>
</div>


<div class="para desc">
<h4>Description</h4>
<p>The <tt>f_chdir</tt> function changes the current directory of the logical drive. The current directory of a drive is set to the root directory when the drive is mounted. Note that the current directory is retained in the each file system object, so that it also affects other tasks that use the volume.</p>
</div>


<div class="para comp">
<h4>QuickInfo</h4>
<p>Available when <tt>_FS_RPATH >= 1</tt>.</p>
</div>


<div class="para use">
<h4>Example</h4>
<pre>
    <span class="c">/* Change current direcoty of the current drive ('dir1' under root directory) */</span>
    f_chdir("/dir1");

    <span class="c">/* Change current direcoty of drive 2 (parent directory) */</span>
    f_chdir("2:..");
</pre>
</div>

<div class="para ref">
<h4>See Also</h4>
<p><tt><a href="chdrive.html">f_chdrive</a>, <a href="getcwd.html">f_getcwd</a></tt></p>
</div>

<p class="foot"><a href="../00index_e.html">Return</a></p>
</body>
</html>
