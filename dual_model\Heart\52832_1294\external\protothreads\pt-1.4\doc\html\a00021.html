<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=iso-8859-1">
<title>The Protothreads Library 1.4: pt-sem.h Source File</title>
<link href="doxygen.css" rel="stylesheet" type="text/css">
<link href="tabs.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.4.6 -->
<div class="tabs">
  <ul>
    <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
    <li><a href="modules.html"><span>Modules</span></a></li>
    <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    <li id="current"><a href="files.html"><span>Files</span></a></li>
  </ul></div>
<div class="tabs">
  <ul>
    <li><a href="files.html"><span>File&nbsp;List</span></a></li>
    <li><a href="globals.html"><span>Globals</span></a></li>
  </ul></div>
<h1>pt-sem.h</h1><a href="a00012.html">Go to the documentation of this file.</a><div class="fragment"><pre class="fragment"><a name="l00001"></a>00001 <span class="comment">/*</span>
<a name="l00002"></a>00002 <span class="comment"> * Copyright (c) 2004, Swedish Institute of Computer Science.</span>
<a name="l00003"></a>00003 <span class="comment"> * All rights reserved. </span>
<a name="l00004"></a>00004 <span class="comment"> *</span>
<a name="l00005"></a>00005 <span class="comment"> * Redistribution and use in source and binary forms, with or without </span>
<a name="l00006"></a>00006 <span class="comment"> * modification, are permitted provided that the following conditions </span>
<a name="l00007"></a>00007 <span class="comment"> * are met: </span>
<a name="l00008"></a>00008 <span class="comment"> * 1. Redistributions of source code must retain the above copyright </span>
<a name="l00009"></a>00009 <span class="comment"> *    notice, this list of conditions and the following disclaimer. </span>
<a name="l00010"></a>00010 <span class="comment"> * 2. Redistributions in binary form must reproduce the above copyright </span>
<a name="l00011"></a>00011 <span class="comment"> *    notice, this list of conditions and the following disclaimer in the </span>
<a name="l00012"></a>00012 <span class="comment"> *    documentation and/or other materials provided with the distribution. </span>
<a name="l00013"></a>00013 <span class="comment"> * 3. Neither the name of the Institute nor the names of its contributors </span>
<a name="l00014"></a>00014 <span class="comment"> *    may be used to endorse or promote products derived from this software </span>
<a name="l00015"></a>00015 <span class="comment"> *    without specific prior written permission. </span>
<a name="l00016"></a>00016 <span class="comment"> *</span>
<a name="l00017"></a>00017 <span class="comment"> * THIS SOFTWARE IS PROVIDED BY THE INSTITUTE AND CONTRIBUTORS ``AS IS'' AND </span>
<a name="l00018"></a>00018 <span class="comment"> * ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE </span>
<a name="l00019"></a>00019 <span class="comment"> * IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE </span>
<a name="l00020"></a>00020 <span class="comment"> * ARE DISCLAIMED.  IN NO EVENT SHALL THE INSTITUTE OR CONTRIBUTORS BE LIABLE </span>
<a name="l00021"></a>00021 <span class="comment"> * FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL </span>
<a name="l00022"></a>00022 <span class="comment"> * DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS </span>
<a name="l00023"></a>00023 <span class="comment"> * OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) </span>
<a name="l00024"></a>00024 <span class="comment"> * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT </span>
<a name="l00025"></a>00025 <span class="comment"> * LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY </span>
<a name="l00026"></a>00026 <span class="comment"> * OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF </span>
<a name="l00027"></a>00027 <span class="comment"> * SUCH DAMAGE. </span>
<a name="l00028"></a>00028 <span class="comment"> *</span>
<a name="l00029"></a>00029 <span class="comment"> * This file is part of the protothreads library.</span>
<a name="l00030"></a>00030 <span class="comment"> * </span>
<a name="l00031"></a>00031 <span class="comment"> * Author: Adam Dunkels &lt;<EMAIL>&gt;</span>
<a name="l00032"></a>00032 <span class="comment"> *</span>
<a name="l00033"></a>00033 <span class="comment"> * $Id: pt-sem.h,v 1.2 2005/02/24 10:36:59 adam Exp $</span>
<a name="l00034"></a>00034 <span class="comment"> */</span>
<a name="l00035"></a>00035 <span class="comment"></span>
<a name="l00036"></a>00036 <span class="comment">/**</span>
<a name="l00037"></a>00037 <span class="comment"> * \addtogroup pt</span>
<a name="l00038"></a>00038 <span class="comment"> * @{</span>
<a name="l00039"></a>00039 <span class="comment"> */</span>
<a name="l00040"></a>00040 <span class="comment"></span>
<a name="l00041"></a>00041 <span class="comment">/**</span>
<a name="l00042"></a>00042 <span class="comment"> * \defgroup ptsem Protothread semaphores</span>
<a name="l00043"></a>00043 <span class="comment"> * @{</span>
<a name="l00044"></a>00044 <span class="comment"> *</span>
<a name="l00045"></a>00045 <span class="comment"> * This module implements counting semaphores on top of</span>
<a name="l00046"></a>00046 <span class="comment"> * protothreads. Semaphores are a synchronization primitive that</span>
<a name="l00047"></a>00047 <span class="comment"> * provide two operations: "wait" and "signal". The "wait" operation</span>
<a name="l00048"></a>00048 <span class="comment"> * checks the semaphore counter and blocks the thread if the counter</span>
<a name="l00049"></a>00049 <span class="comment"> * is zero. The "signal" operation increases the semaphore counter but</span>
<a name="l00050"></a>00050 <span class="comment"> * does not block. If another thread has blocked waiting for the</span>
<a name="l00051"></a>00051 <span class="comment"> * semaphore that is signalled, the blocked thread will become</span>
<a name="l00052"></a>00052 <span class="comment"> * runnable again.</span>
<a name="l00053"></a>00053 <span class="comment"> *</span>
<a name="l00054"></a>00054 <span class="comment"> * Semaphores can be used to implement other, more structured,</span>
<a name="l00055"></a>00055 <span class="comment"> * synchronization primitives such as monitors and message</span>
<a name="l00056"></a>00056 <span class="comment"> * queues/bounded buffers (see below).</span>
<a name="l00057"></a>00057 <span class="comment"> *</span>
<a name="l00058"></a>00058 <span class="comment"> * The following example shows how the producer-consumer problem, also</span>
<a name="l00059"></a>00059 <span class="comment"> * known as the bounded buffer problem, can be solved using</span>
<a name="l00060"></a>00060 <span class="comment"> * protothreads and semaphores. Notes on the program follow after the</span>
<a name="l00061"></a>00061 <span class="comment"> * example.</span>
<a name="l00062"></a>00062 <span class="comment"> *</span>
<a name="l00063"></a>00063 <span class="comment"> \code</span>
<a name="l00064"></a>00064 <span class="comment">#include "pt-sem.h"</span>
<a name="l00065"></a>00065 <span class="comment"></span>
<a name="l00066"></a>00066 <span class="comment">#define NUM_ITEMS 32</span>
<a name="l00067"></a>00067 <span class="comment">#define BUFSIZE 8</span>
<a name="l00068"></a>00068 <span class="comment"></span>
<a name="l00069"></a>00069 <span class="comment">static struct pt_sem mutex, full, empty;</span>
<a name="l00070"></a>00070 <span class="comment"></span>
<a name="l00071"></a>00071 <span class="comment">PT_THREAD(producer(struct pt *pt))</span>
<a name="l00072"></a>00072 <span class="comment">{</span>
<a name="l00073"></a>00073 <span class="comment">  static int produced;</span>
<a name="l00074"></a>00074 <span class="comment">  </span>
<a name="l00075"></a>00075 <span class="comment">  PT_BEGIN(pt);</span>
<a name="l00076"></a>00076 <span class="comment">  </span>
<a name="l00077"></a>00077 <span class="comment">  for(produced = 0; produced &lt; NUM_ITEMS; ++produced) {</span>
<a name="l00078"></a>00078 <span class="comment">  </span>
<a name="l00079"></a>00079 <span class="comment">    PT_SEM_WAIT(pt, &amp;full);</span>
<a name="l00080"></a>00080 <span class="comment">    </span>
<a name="l00081"></a>00081 <span class="comment">    PT_SEM_WAIT(pt, &amp;mutex);</span>
<a name="l00082"></a>00082 <span class="comment">    add_to_buffer(produce_item());    </span>
<a name="l00083"></a>00083 <span class="comment">    PT_SEM_SIGNAL(pt, &amp;mutex);</span>
<a name="l00084"></a>00084 <span class="comment">    </span>
<a name="l00085"></a>00085 <span class="comment">    PT_SEM_SIGNAL(pt, &amp;empty);</span>
<a name="l00086"></a>00086 <span class="comment">  }</span>
<a name="l00087"></a>00087 <span class="comment"></span>
<a name="l00088"></a>00088 <span class="comment">  PT_END(pt);</span>
<a name="l00089"></a>00089 <span class="comment">}</span>
<a name="l00090"></a>00090 <span class="comment"></span>
<a name="l00091"></a>00091 <span class="comment">PT_THREAD(consumer(struct pt *pt))</span>
<a name="l00092"></a>00092 <span class="comment">{</span>
<a name="l00093"></a>00093 <span class="comment">  static int consumed;</span>
<a name="l00094"></a>00094 <span class="comment">  </span>
<a name="l00095"></a>00095 <span class="comment">  PT_BEGIN(pt);</span>
<a name="l00096"></a>00096 <span class="comment"></span>
<a name="l00097"></a>00097 <span class="comment">  for(consumed = 0; consumed &lt; NUM_ITEMS; ++consumed) {</span>
<a name="l00098"></a>00098 <span class="comment">    </span>
<a name="l00099"></a>00099 <span class="comment">    PT_SEM_WAIT(pt, &amp;empty);</span>
<a name="l00100"></a>00100 <span class="comment">    </span>
<a name="l00101"></a>00101 <span class="comment">    PT_SEM_WAIT(pt, &amp;mutex);    </span>
<a name="l00102"></a>00102 <span class="comment">    consume_item(get_from_buffer());    </span>
<a name="l00103"></a>00103 <span class="comment">    PT_SEM_SIGNAL(pt, &amp;mutex);</span>
<a name="l00104"></a>00104 <span class="comment">    </span>
<a name="l00105"></a>00105 <span class="comment">    PT_SEM_SIGNAL(pt, &amp;full);</span>
<a name="l00106"></a>00106 <span class="comment">  }</span>
<a name="l00107"></a>00107 <span class="comment"></span>
<a name="l00108"></a>00108 <span class="comment">  PT_END(pt);</span>
<a name="l00109"></a>00109 <span class="comment">}</span>
<a name="l00110"></a>00110 <span class="comment"></span>
<a name="l00111"></a>00111 <span class="comment">PT_THREAD(driver_thread(struct pt *pt))</span>
<a name="l00112"></a>00112 <span class="comment">{</span>
<a name="l00113"></a>00113 <span class="comment">  static struct pt pt_producer, pt_consumer;</span>
<a name="l00114"></a>00114 <span class="comment"></span>
<a name="l00115"></a>00115 <span class="comment">  PT_BEGIN(pt);</span>
<a name="l00116"></a>00116 <span class="comment">  </span>
<a name="l00117"></a>00117 <span class="comment">  PT_SEM_INIT(&amp;empty, 0);</span>
<a name="l00118"></a>00118 <span class="comment">  PT_SEM_INIT(&amp;full, BUFSIZE);</span>
<a name="l00119"></a>00119 <span class="comment">  PT_SEM_INIT(&amp;mutex, 1);</span>
<a name="l00120"></a>00120 <span class="comment"></span>
<a name="l00121"></a>00121 <span class="comment">  PT_INIT(&amp;pt_producer);</span>
<a name="l00122"></a>00122 <span class="comment">  PT_INIT(&amp;pt_consumer);</span>
<a name="l00123"></a>00123 <span class="comment"></span>
<a name="l00124"></a>00124 <span class="comment">  PT_WAIT_THREAD(pt, producer(&amp;pt_producer) &amp;</span>
<a name="l00125"></a>00125 <span class="comment">                     consumer(&amp;pt_consumer));</span>
<a name="l00126"></a>00126 <span class="comment"></span>
<a name="l00127"></a>00127 <span class="comment">  PT_END(pt);</span>
<a name="l00128"></a>00128 <span class="comment">}</span>
<a name="l00129"></a>00129 <span class="comment"> \endcode</span>
<a name="l00130"></a>00130 <span class="comment"> *</span>
<a name="l00131"></a>00131 <span class="comment"> * The program uses three protothreads: one protothread that</span>
<a name="l00132"></a>00132 <span class="comment"> * implements the consumer, one thread that implements the producer,</span>
<a name="l00133"></a>00133 <span class="comment"> * and one protothread that drives the two other protothreads. The</span>
<a name="l00134"></a>00134 <span class="comment"> * program uses three semaphores: "full", "empty" and "mutex". The</span>
<a name="l00135"></a>00135 <span class="comment"> * "mutex" semaphore is used to provide mutual exclusion for the</span>
<a name="l00136"></a>00136 <span class="comment"> * buffer, the "empty" semaphore is used to block the consumer is the</span>
<a name="l00137"></a>00137 <span class="comment"> * buffer is empty, and the "full" semaphore is used to block the</span>
<a name="l00138"></a>00138 <span class="comment"> * producer is the buffer is full.</span>
<a name="l00139"></a>00139 <span class="comment"> *</span>
<a name="l00140"></a>00140 <span class="comment"> * The "driver_thread" holds two protothread state variables,</span>
<a name="l00141"></a>00141 <span class="comment"> * "pt_producer" and "pt_consumer". It is important to note that both</span>
<a name="l00142"></a>00142 <span class="comment"> * these variables are declared as &lt;i&gt;static&lt;/i&gt;. If the static</span>
<a name="l00143"></a>00143 <span class="comment"> * keyword is not used, both variables are stored on the stack. Since</span>
<a name="l00144"></a>00144 <span class="comment"> * protothreads do not store the stack, these variables may be</span>
<a name="l00145"></a>00145 <span class="comment"> * overwritten during a protothread wait operation. Similarly, both</span>
<a name="l00146"></a>00146 <span class="comment"> * the "consumer" and "producer" protothreads declare their local</span>
<a name="l00147"></a>00147 <span class="comment"> * variables as static, to avoid them being stored on the stack.</span>
<a name="l00148"></a>00148 <span class="comment"> * </span>
<a name="l00149"></a>00149 <span class="comment"> *</span>
<a name="l00150"></a>00150 <span class="comment"> */</span>
<a name="l00151"></a>00151    <span class="comment"></span>
<a name="l00152"></a>00152 <span class="comment">/**</span>
<a name="l00153"></a>00153 <span class="comment"> * \file</span>
<a name="l00154"></a>00154 <span class="comment"> * Couting semaphores implemented on protothreads</span>
<a name="l00155"></a>00155 <span class="comment"> * \author</span>
<a name="l00156"></a>00156 <span class="comment"> * Adam Dunkels &lt;<EMAIL>&gt;</span>
<a name="l00157"></a>00157 <span class="comment"> *</span>
<a name="l00158"></a>00158 <span class="comment"> */</span>
<a name="l00159"></a>00159 
<a name="l00160"></a>00160 <span class="preprocessor">#ifndef __PT_SEM_H__</span>
<a name="l00161"></a>00161 <span class="preprocessor"></span><span class="preprocessor">#define __PT_SEM_H__</span>
<a name="l00162"></a>00162 <span class="preprocessor"></span>
<a name="l00163"></a>00163 <span class="preprocessor">#include "<a class="code" href="a00013.html">pt.h</a>"</span>
<a name="l00164"></a>00164 
<a name="l00165"></a><a class="code" href="a00006.html">00165</a> <span class="keyword">struct </span><a class="code" href="a00006.html">pt_sem</a> {
<a name="l00166"></a><a class="code" href="a00006.html#6f341120f42d5fd9f329ff1119594743">00166</a>   <span class="keywordtype">unsigned</span> <span class="keywordtype">int</span> <a class="code" href="a00006.html#6f341120f42d5fd9f329ff1119594743">count</a>;
<a name="l00167"></a>00167 };
<a name="l00168"></a>00168 <span class="comment"></span>
<a name="l00169"></a>00169 <span class="comment">/**</span>
<a name="l00170"></a>00170 <span class="comment"> * Initialize a semaphore</span>
<a name="l00171"></a>00171 <span class="comment"> *</span>
<a name="l00172"></a>00172 <span class="comment"> * This macro initializes a semaphore with a value for the</span>
<a name="l00173"></a>00173 <span class="comment"> * counter. Internally, the semaphores use an "unsigned int" to</span>
<a name="l00174"></a>00174 <span class="comment"> * represent the counter, and therefore the "count" argument should be</span>
<a name="l00175"></a>00175 <span class="comment"> * within range of an unsigned int.</span>
<a name="l00176"></a>00176 <span class="comment"> *</span>
<a name="l00177"></a>00177 <span class="comment"> * \param s (struct pt_sem *) A pointer to the pt_sem struct</span>
<a name="l00178"></a>00178 <span class="comment"> * representing the semaphore</span>
<a name="l00179"></a>00179 <span class="comment"> *</span>
<a name="l00180"></a>00180 <span class="comment"> * \param c (unsigned int) The initial count of the semaphore.</span>
<a name="l00181"></a>00181 <span class="comment"> * \hideinitializer</span>
<a name="l00182"></a>00182 <span class="comment"> */</span>
<a name="l00183"></a><a class="code" href="a00016.html#gd7089c5dc86f12019f0361d82a75b04b">00183</a> <span class="preprocessor">#define PT_SEM_INIT(s, c) (s)-&gt;count = c</span>
<a name="l00184"></a>00184 <span class="preprocessor"></span><span class="comment"></span>
<a name="l00185"></a>00185 <span class="comment">/**</span>
<a name="l00186"></a>00186 <span class="comment"> * Wait for a semaphore</span>
<a name="l00187"></a>00187 <span class="comment"> *</span>
<a name="l00188"></a>00188 <span class="comment"> * This macro carries out the "wait" operation on the semaphore. The</span>
<a name="l00189"></a>00189 <span class="comment"> * wait operation causes the protothread to block while the counter is</span>
<a name="l00190"></a>00190 <span class="comment"> * zero. When the counter reaches a value larger than zero, the</span>
<a name="l00191"></a>00191 <span class="comment"> * protothread will continue.</span>
<a name="l00192"></a>00192 <span class="comment"> *</span>
<a name="l00193"></a>00193 <span class="comment"> * \param pt (struct pt *) A pointer to the protothread (struct pt) in</span>
<a name="l00194"></a>00194 <span class="comment"> * which the operation is executed.</span>
<a name="l00195"></a>00195 <span class="comment"> *</span>
<a name="l00196"></a>00196 <span class="comment"> * \param s (struct pt_sem *) A pointer to the pt_sem struct</span>
<a name="l00197"></a>00197 <span class="comment"> * representing the semaphore</span>
<a name="l00198"></a>00198 <span class="comment"> *</span>
<a name="l00199"></a>00199 <span class="comment"> * \hideinitializer</span>
<a name="l00200"></a>00200 <span class="comment"> */</span>
<a name="l00201"></a><a class="code" href="a00016.html#g386ff87a52a840512906f2940e229e2e">00201</a> <span class="preprocessor">#define PT_SEM_WAIT(pt, s)      \</span>
<a name="l00202"></a>00202 <span class="preprocessor">  do {                                          \</span>
<a name="l00203"></a>00203 <span class="preprocessor">    PT_WAIT_UNTIL(pt, (s)-&gt;count &gt; 0);          \</span>
<a name="l00204"></a>00204 <span class="preprocessor">    --(s)-&gt;count;                               \</span>
<a name="l00205"></a>00205 <span class="preprocessor">  } while(0)</span>
<a name="l00206"></a>00206 <span class="preprocessor"></span><span class="comment"></span>
<a name="l00207"></a>00207 <span class="comment">/**</span>
<a name="l00208"></a>00208 <span class="comment"> * Signal a semaphore</span>
<a name="l00209"></a>00209 <span class="comment"> *</span>
<a name="l00210"></a>00210 <span class="comment"> * This macro carries out the "signal" operation on the semaphore. The</span>
<a name="l00211"></a>00211 <span class="comment"> * signal operation increments the counter inside the semaphore, which</span>
<a name="l00212"></a>00212 <span class="comment"> * eventually will cause waiting protothreads to continue executing.</span>
<a name="l00213"></a>00213 <span class="comment"> *</span>
<a name="l00214"></a>00214 <span class="comment"> * \param pt (struct pt *) A pointer to the protothread (struct pt) in</span>
<a name="l00215"></a>00215 <span class="comment"> * which the operation is executed.</span>
<a name="l00216"></a>00216 <span class="comment"> *</span>
<a name="l00217"></a>00217 <span class="comment"> * \param s (struct pt_sem *) A pointer to the pt_sem struct</span>
<a name="l00218"></a>00218 <span class="comment"> * representing the semaphore</span>
<a name="l00219"></a>00219 <span class="comment"> *</span>
<a name="l00220"></a>00220 <span class="comment"> * \hideinitializer</span>
<a name="l00221"></a>00221 <span class="comment"> */</span>
<a name="l00222"></a><a class="code" href="a00016.html#g1eaaf4d9d75e24582acc6440d7085f19">00222</a> <span class="preprocessor">#define PT_SEM_SIGNAL(pt, s) ++(s)-&gt;count</span>
<a name="l00223"></a>00223 <span class="preprocessor"></span>
<a name="l00224"></a>00224 <span class="preprocessor">#endif </span><span class="comment">/* __PT_SEM_H__ */</span>
<a name="l00225"></a>00225 <span class="comment"></span>
<a name="l00226"></a>00226 <span class="comment">/** @} */</span><span class="comment"></span>
<a name="l00227"></a>00227 <span class="comment">/** @} */</span>
<a name="l00228"></a>00228    
</pre></div><hr size="1"><address style="align: right;"><small>Generated on Mon Oct 2 10:06:29 2006 for The Protothreads Library 1.4 by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.4.6 </small></address>
</body>
</html>
