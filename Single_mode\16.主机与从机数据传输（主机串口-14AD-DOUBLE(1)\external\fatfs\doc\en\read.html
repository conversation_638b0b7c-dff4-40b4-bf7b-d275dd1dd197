<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html lang="en">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<meta http-equiv="Content-Style-Type" content="text/css">
<link rel="up" title="FatFs" href="../00index_e.html">
<link rel="alternate" hreflang="ja" title="Japanese" href="../ja/read.html">
<link rel="stylesheet" href="../css_e.css" type="text/css" media="screen" title="ELM Default">
<title>FatFs - f_read</title>
</head>

<body>

<div class="para func">
<h2>f_read</h2>
<p>The f_read function reads data from a file.</p>
<pre>
FRESULT f_read (
  FIL* <span class="arg">fp</span>,     <span class="c">/* [IN] File object */</span>
  void* <span class="arg">buff</span>,  <span class="c">/* [OUT] Buffer to store read data */</span>
  UINT <span class="arg">btr</span>,    <span class="c">/* [IN] Number of bytes to read */</span>
  UINT* <span class="arg">br</span>     <span class="c">/* [OUT] Number of bytes read */</span>
);
</pre>
</div>

<div class="para arg">
<h4>Parameters</h4>
<dl class="par">
<dt>fp</dt>
<dd>Pointer to the open file object.</dd>
<dt>buff</dt>
<dd>Pointer to the buffer to store read data.</dd>
<dt>btr</dt>
<dd>Number of bytes to read in range of <tt>UINT</tt> type.</dd>
<dt>br</dt>
<dd>Pointer to the <tt>UINT</tt> variable to return number of bytes read. The value is always valid after the function call regardless of the result code.</dd>
</dl>
</div>


<div class="para ret">
<h4>Return Values</h4>
<p>
<a href="rc.html#ok">FR_OK</a>,
<a href="rc.html#de">FR_DISK_ERR</a>,
<a href="rc.html#ie">FR_INT_ERR</a>,
<a href="rc.html#io">FR_INVALID_OBJECT</a>,
<a href="rc.html#tm">FR_TIMEOUT</a>
</p>
</div>


<div class="para desc">
<h4>Description</h4>
<p>The function starts to read data from the file at the position pointed by the read/write pointer. The read/write pointer advances as number of bytes read. After the function succeeded, <tt class="arg">*br</tt> should be checked to detect end of the file. In case of <tt class="arg">*br</tt> &lt; <tt class="arg">btr</tt>, it means the read/write pointer reached end of the file during read operation.</p>
</div>


<div class="para comp">
<h4>QuickInfo</h4>
<p>Always available.</p>
</div>


<div class="para ref">
<h4>See Also</h4>
<p><tt><a href="open.html">f_open</a>, <a href="gets.html">fgets</a>, <a href="write.html">f_write</a>, <a href="close.html">f_close</a>, <a href="sfile.html">FIL</a></tt></p>
</div>

<p class="foot"><a href="../00index_e.html">Return</a></p>
</body>
</html>
