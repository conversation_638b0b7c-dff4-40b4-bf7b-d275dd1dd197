[BASIC]
# We're ok with short funtion argument names.
# [invalid-name]
argument-rgx=[a-z_][a-z0-9_]*$

# Allow filter and map.
# [bad-builtin]
bad-functions=input

# We prefer docstrings, but we don't require them on all functions.
# Require them only on long functions (for some value of long).
# [missing-docstring]
docstring-min-length=10

# Allow longer methods than the default.
# [invalid-name]
method-rgx=[a-z_][a-z0-9_]{2,35}$

# Allow module names containing a dash (but no underscore or uppercase letter).
# They are whole programs, not meant to be included by another module.
# [invalid-name]
module-rgx=(([a-z_][a-z0-9_]*)|([A-Z][a-zA-Z0-9]+)|[a-z][-0-9a-z]+)$

# Some functions don't need docstrings.
# [missing-docstring]
no-docstring-rgx=(run_)main$

# We're ok with short local or global variable names.
# [invalid-name]
variable-rgx=[a-z_][a-z0-9_]*$

[DESIGN]
# Allow more than the default 7 attributes.
# [too-many-instance-attributes]
max-attributes=15

[FORMAT]
# Allow longer modules than the default recommended maximum.
# [too-many-lines]
max-module-lines=2000

[MESSAGES CONTROL]
disable=

[REPORTS]
# Don't diplay statistics. Just the facts.
reports=no

[VARIABLES]
# Allow unused variables if their name starts with an underscore.
# [unused-argument]
dummy-variables-rgx=_.*
