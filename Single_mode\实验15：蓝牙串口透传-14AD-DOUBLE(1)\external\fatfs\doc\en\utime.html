<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html lang="en">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<meta http-equiv="Content-Style-Type" content="text/css">
<link rel="up" title="FatFs" href="../00index_e.html">
<link rel="alternate" hreflang="ja" title="Japanese" href="../ja/utime.html">
<link rel="stylesheet" href="../css_e.css" type="text/css" media="screen" title="ELM Default">
<title>FatFs - f_utime</title>
</head>

<body>

<div class="para func">
<h2>f_utime</h2>
<p>The f_utime function changes the timestamp of a file or sub-directory.</p>
<pre>
FRESULT f_utime (
  const TCHAR* <span class="arg">path</span>,  <span class="c">/* [IN] Object name */</span>
  const FILINFO* <span class="arg">fno</span>  <span class="c">/* [IN] Time and data to be set */</span>
);
</pre>
</div>

<div class="para arg">
<h4>Parameters</h4>
<dl class="par">
<dt>path</dt>
<dd>Pointer to the null-terminated string that specifies an <a href="filename.html">object</a> to be changed.</dd>
<dt>fno</dt>
<dd>Pointer to the file information structure that has a timestamp to be set in member fdate and ftime. Do not care any other members.</dd>
</dl>
</div>


<div class="para ret">
<h4>Return Values</h4>
<p>
<a href="rc.html#ok">FR_OK</a>,
<a href="rc.html#de">FR_DISK_ERR</a>,
<a href="rc.html#ie">FR_INT_ERR</a>,
<a href="rc.html#nr">FR_NOT_READY</a>,
<a href="rc.html#ok">FR_NO_FILE</a>,
<a href="rc.html#np">FR_NO_PATH</a>,
<a href="rc.html#in">FR_INVALID_NAME</a>,
<a href="rc.html#wp">FR_WRITE_PROTECTED</a>,
<a href="rc.html#id">FR_INVALID_DRIVE</a>,
<a href="rc.html#ne">FR_NOT_ENABLED</a>,
<a href="rc.html#ns">FR_NO_FILESYSTEM</a>,
<a href="rc.html#tm">FR_TIMEOUT</a>,
<a href="rc.html#nc">FR_NOT_ENOUGH_CORE</a>
</p>
</div>


<div class="para desc">
<h4>Description</h4>
<p>The <tt>f_utime</tt> function changes the timestamp of a file or sub-directory</p>
</div>


<div class="para use">
<h4>Example</h4>
<pre>
FRESULT set_timestamp (
    char *obj,     <span class="c">/* Pointer to the file name */</span>
    int year,
    int month,
    int mday,
    int hour,
    int min,
    int sec
)
{
    FILINFO fno;

    fno.fdate = (WORD)(((year - 1980) * 512U) | month * 32U | mday);
    fno.ftime = (WORD)(hour * 2048U | min * 32U | sec / 2U);

    return f_utime(obj, &amp;fno);
}
</pre>
</div>


<div class="para comp">
<h4>QuickInfo</h4>
<p>Available when <tt>_FS_READONLY == 0</tt> and <tt>_USE_CHMOD == 1</tt>.</p>
</div>


<div class="para ref">
<h4>See Also</h4>
<p><tt><a href="stat.html">f_stat</a>, <a href="sfileinfo.html">FILINFO</a></tt></p>
</div>

<p class="foot"><a href="../00index_e.html">Return</a></p>
</body>
</html>
