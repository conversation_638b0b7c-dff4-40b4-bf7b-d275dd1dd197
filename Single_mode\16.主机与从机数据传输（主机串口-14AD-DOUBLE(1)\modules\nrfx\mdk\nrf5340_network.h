/*
 * Copyright (c) 2010 - 2020, Nordic Semiconductor ASAAll rights reserved.Redistribution and use in sou
 * rce and binary forms, with or without modification,are permitted provided that the following conditi
 * ons are met:1. Redistributions of source code must retain the above copyright notice, this list of c
 * onditions and the following disclaimer.2. Redistributions in binary form, except as embedded into a 
 * Nordic Semiconductor ASA integrated circuit in a product or a software update for such product, must
 * reproduce the above copyright notice, this list of conditions and the following disclaimer in the d
 * ocumentation and/or other materials provided with the distribution.3. Neither the name of Nordic Sem
 * iconductor ASA nor the names of its contributors may be used to endorse or promote products derived 
 * from this software without specific prior written permission.4. This software, with or without modif
 * ication, must only be used with a Nordic Semiconductor ASA integrated circuit.5. Any software provid
 * ed in binary form under this license must not be reverse engineered, decompiled, modified and/or dis
 * assembled.THIS SOFTWARE IS PROVIDED BY NORDIC SEMICONDUCTOR ASA "AS IS" AND ANY EXPRESSOR IMPLIED WA
 * RRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIESOF MERCHANTABILITY, NONINFRINGEMENT, 
 * AND FITNESS FOR A PARTICULAR PURPOSE AREDISCLAIMED. IN NO EVENT SHALL NORDIC SEMICONDUCTOR ASA OR CO
 * NTRIBUTORS BELIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, ORCONSEQUENTIAL DAMAGE
 * S (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTEGOODS OR SERVICES; LOSS OF USE, DATA, OR 
 * PROFITS; OR BUSINESS INTERRUPTION)HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT
 * , STRICTLIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUTOF THE USE OF T
 * HIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 * @file     nrf5340_network.h
 * @brief    CMSIS HeaderFile
 * @version  1
 * @date     04. March 2020
 * @note     Generated by SVDConv V3.3.25 on Wednesday, 04.03.2020 14:59:25
 *           from File 'nrf5340_network.svd',
 *           last modified on Wednesday, 04.03.2020 13:59:18
 */



/** @addtogroup Nordic Semiconductor
  * @{
  */


/** @addtogroup nrf5340_network
  * @{
  */


#ifndef NRF5340_NETWORK_H
#define NRF5340_NETWORK_H

#ifdef __cplusplus
extern "C" {
#endif


/** @addtogroup Configuration_of_CMSIS
  * @{
  */



/* =========================================================================================================================== */
/* ================                                Interrupt Number Definition                                ================ */
/* =========================================================================================================================== */

typedef enum {
/* =======================================  ARM Cortex-M33 Specific Interrupt Numbers  ======================================= */
  Reset_IRQn                = -15,              /*!< -15  Reset Vector, invoked on Power up and warm reset                     */
  NonMaskableInt_IRQn       = -14,              /*!< -14  Non maskable Interrupt, cannot be stopped or preempted               */
  HardFault_IRQn            = -13,              /*!< -13  Hard Fault, all classes of Fault                                     */
  MemoryManagement_IRQn     = -12,              /*!< -12  Memory Management, MPU mismatch, including Access Violation
                                                     and No Match                                                              */
  BusFault_IRQn             = -11,              /*!< -11  Bus Fault, Pre-Fetch-, Memory Access Fault, other address/memory
                                                     related Fault                                                             */
  UsageFault_IRQn           = -10,              /*!< -10  Usage Fault, i.e. Undef Instruction, Illegal State Transition        */
  SecureFault_IRQn          =  -9,              /*!< -9 Secure Fault Handler                                                   */
  SVCall_IRQn               =  -5,              /*!< -5 System Service Call via SVC instruction                                */
  DebugMonitor_IRQn         =  -4,              /*!< -4 Debug Monitor                                                          */
  PendSV_IRQn               =  -2,              /*!< -2 Pendable request for system service                                    */
  SysTick_IRQn              =  -1,              /*!< -1 System Tick Timer                                                      */
/* ======================================  nrf5340_network Specific Interrupt Numbers  ======================================= */
  CLOCK_POWER_IRQn          =   5,              /*!< 5  CLOCK_POWER                                                            */
  RADIO_IRQn                =   8,              /*!< 8  RADIO                                                                  */
  RNG_IRQn                  =   9,              /*!< 9  RNG                                                                    */
  GPIOTE_IRQn               =  10,              /*!< 10 GPIOTE                                                                 */
  WDT_IRQn                  =  11,              /*!< 11 WDT                                                                    */
  TIMER0_IRQn               =  12,              /*!< 12 TIMER0                                                                 */
  ECB_IRQn                  =  13,              /*!< 13 ECB                                                                    */
  AAR_CCM_IRQn              =  14,              /*!< 14 AAR_CCM                                                                */
  TEMP_IRQn                 =  16,              /*!< 16 TEMP                                                                   */
  RTC0_IRQn                 =  17,              /*!< 17 RTC0                                                                   */
  IPC_IRQn                  =  18,              /*!< 18 IPC                                                                    */
  SPIM0_SPIS0_TWIM0_TWIS0_UARTE0_IRQn=  19,     /*!< 19 SPIM0_SPIS0_TWIM0_TWIS0_UARTE0                                         */
  EGU0_IRQn                 =  20,              /*!< 20 EGU0                                                                   */
  RTC1_IRQn                 =  22,              /*!< 22 RTC1                                                                   */
  TIMER1_IRQn               =  24,              /*!< 24 TIMER1                                                                 */
  TIMER2_IRQn               =  25,              /*!< 25 TIMER2                                                                 */
  SWI0_IRQn                 =  26,              /*!< 26 SWI0                                                                   */
  SWI1_IRQn                 =  27,              /*!< 27 SWI1                                                                   */
  SWI2_IRQn                 =  28,              /*!< 28 SWI2                                                                   */
  SWI3_IRQn                 =  29               /*!< 29 SWI3                                                                   */
} IRQn_Type;



/* =========================================================================================================================== */
/* ================                           Processor and Core Peripheral Section                           ================ */
/* =========================================================================================================================== */

/* ==========================  Configuration of the ARM Cortex-M33 Processor and Core Peripherals  =========================== */
#define __CM33_REV                 0x0004U      /*!< CM33 Core Revision                                                        */
#define __DSP_PRESENT                  0        /*!< DSP present or not                                                        */
#define __NVIC_PRIO_BITS               3        /*!< Number of Bits used for Priority Levels                                   */
#define __Vendor_SysTickConfig         0        /*!< Set to 1 if different SysTick Config is used                              */
#define __VTOR_PRESENT                 1        /*!< Set to 1 if CPU supports Vector Table Offset Register                     */
#define __MPU_PRESENT                  1        /*!< MPU present                                                               */
#define __FPU_PRESENT                  0        /*!< FPU present                                                               */
#define __FPU_DP                       0        /*!< unused, Device has no FPU                                                 */
#define __SAU_REGION_PRESENT           0        /*!< SAU present                                                               */


/** @} */ /* End of group Configuration_of_CMSIS */

#include "core_cm33.h"                          /*!< ARM Cortex-M33 processor and core peripherals                             */
#include "system_nrf5340_network.h"             /*!< nrf5340_network System                                                    */

#ifndef __IM                                    /*!< Fallback for older CMSIS versions                                         */
  #define __IM   __I
#endif
#ifndef __OM                                    /*!< Fallback for older CMSIS versions                                         */
  #define __OM   __O
#endif
#ifndef __IOM                                   /*!< Fallback for older CMSIS versions                                         */
  #define __IOM  __IO
#endif


/* =========================================================================================================================== */
/* ================                              Device Specific Cluster Section                              ================ */
/* =========================================================================================================================== */


/** @addtogroup Device_Peripheral_clusters
  * @{
  */


/**
  * @brief FICR_INFO [INFO] (Device info)
  */
typedef struct {
  __IM  uint32_t  CONFIGID;                     /*!< (@ 0x00000000) Configuration identifier                                   */
  __IM  uint32_t  DEVICEID[2];                  /*!< (@ 0x00000004) Description collection: Device identifier                  */
  __IM  uint32_t  PART;                         /*!< (@ 0x0000000C) Part code                                                  */
  __IM  uint32_t  VARIANT;                      /*!< (@ 0x00000010) Part Variant, Hardware version and Production
                                                                    configuration                                              */
  __IM  uint32_t  PACKAGE;                      /*!< (@ 0x00000014) Package option                                             */
  __IM  uint32_t  RAM;                          /*!< (@ 0x00000018) RAM variant                                                */
  __IM  uint32_t  FLASH;                        /*!< (@ 0x0000001C) Flash variant                                              */
  __IM  uint32_t  CODEPAGESIZE;                 /*!< (@ 0x00000020) Code memory page size in bytes                             */
  __IM  uint32_t  CODESIZE;                     /*!< (@ 0x00000024) Code memory size                                           */
  __IM  uint32_t  DEVICETYPE;                   /*!< (@ 0x00000028) Device type                                                */
} FICR_INFO_Type;                               /*!< Size = 44 (0x2c)                                                          */


/**
  * @brief FICR_TRIMCNF [TRIMCNF] (Unspecified)
  */
typedef struct {
  __IOM uint32_t* ADDR;                         /*!< (@ 0x00000000) Description cluster: Address                               */
  __IM  uint32_t  DATA;                         /*!< (@ 0x00000004) Description cluster: Data                                  */
} FICR_TRIMCNF_Type;                            /*!< Size = 8 (0x8)                                                            */


/**
  * @brief VREQCTRL_VREGRADIO [VREGRADIO] (Unspecified)
  */
typedef struct {
  __IOM uint32_t  VREQH;                        /*!< (@ 0x00000000) Request high voltage on RADIO After requesting
                                                                    high voltage, the user must wait until VREQHREADY
                                                                    is set to Ready                                            */
  __IM  uint32_t  RESERVED;
  __IM  uint32_t  VREQHREADY;                   /*!< (@ 0x00000008) High voltage on RADIO is ready                             */
} VREQCTRL_VREGRADIO_Type;                      /*!< Size = 12 (0xc)                                                           */


/**
  * @brief CLOCK_HFCLKAUDIO [HFCLKAUDIO] (Unspecified)
  */
typedef struct {
  __IOM uint32_t  FREQUENCY;                    /*!< (@ 0x00000000) Audio PLL frequency in 11.176 MHz - 11.402 MHz
                                                                    or 12.165 MHz - 12.411 MHz frequency bands                 */
} CLOCK_HFCLKAUDIO_Type;                        /*!< Size = 4 (0x4)                                                            */


/**
  * @brief RESET_NETWORK [NETWORK] (ULP network core control)
  */
typedef struct {
  __IM  uint32_t  RESERVED;
  __IOM uint32_t  FORCEOFF;                     /*!< (@ 0x00000004) Force off power and clock in network core                  */
} RESET_NETWORK_Type;                           /*!< Size = 8 (0x8)                                                            */


/**
  * @brief CTRLAPPERI_MAILBOX [MAILBOX] (Unspecified)
  */
typedef struct {
  __IM  uint32_t  RXDATA;                       /*!< (@ 0x00000000) Data sent from the debugger to the CPU                     */
  __IM  uint32_t  RXSTATUS;                     /*!< (@ 0x00000004) Status to indicate if data sent from the debugger
                                                                    to the CPU has been read                                   */
  __IM  uint32_t  RESERVED[30];
  __IOM uint32_t  TXDATA;                       /*!< (@ 0x00000080) Data sent from the CPU to the debugger                     */
  __IM  uint32_t  TXSTATUS;                     /*!< (@ 0x00000084) Status to indicate if data sent from the CPU
                                                                    to the debugger has been read                              */
} CTRLAPPERI_MAILBOX_Type;                      /*!< Size = 136 (0x88)                                                         */


/**
  * @brief CTRLAPPERI_ERASEPROTECT [ERASEPROTECT] (Unspecified)
  */
typedef struct {
  __IOM uint32_t  LOCK;                         /*!< (@ 0x00000000) Lock register ERASEPROTECT.DISABLE from being
                                                                    written until next reset                                   */
  __IOM uint32_t  DISABLE;                      /*!< (@ 0x00000004) Disable ERASEPROTECT and perform ERASEALL                  */
} CTRLAPPERI_ERASEPROTECT_Type;                 /*!< Size = 8 (0x8)                                                            */


/**
  * @brief CTRLAPPERI_APPROTECT [APPROTECT] (Unspecified)
  */
typedef struct {
  __IOM uint32_t  LOCK;                         /*!< (@ 0x00000000) Lock register APPROTECT.DISABLE from being written
                                                                    to until next reset                                        */
  __IOM uint32_t  DISABLE;                      /*!< (@ 0x00000004) Disable APPROTECT and enable debug access to
                                                                    non-secure mode                                            */
} CTRLAPPERI_APPROTECT_Type;                    /*!< Size = 8 (0x8)                                                            */


/**
  * @brief CTRLAPPERI_SECUREAPPROTECT [SECUREAPPROTECT] (Unspecified)
  */
typedef struct {
  __IOM uint32_t  LOCK;                         /*!< (@ 0x00000000) Lock register SECUREAPPROTECT.DISABLE from being
                                                                    written until next reset                                   */
  __IOM uint32_t  DISABLE;                      /*!< (@ 0x00000004) Disable SECUREAPPROTECT and enable debug access
                                                                    to secure mode                                             */
} CTRLAPPERI_SECUREAPPROTECT_Type;              /*!< Size = 8 (0x8)                                                            */


/**
  * @brief RADIO_PSEL [PSEL] (Unspecified)
  */
typedef struct {
  __IOM uint32_t  DFEGPIO[8];                   /*!< (@ 0x00000000) Description collection: Pin select for DFE pin
                                                                    n                                                          */
} RADIO_PSEL_Type;                              /*!< Size = 32 (0x20)                                                          */


/**
  * @brief RADIO_DFEPACKET [DFEPACKET] (DFE packet EasyDMA channel)
  */
typedef struct {
  __IOM uint32_t  PTR;                          /*!< (@ 0x00000000) Data pointer                                               */
  __IOM uint32_t  MAXCNT;                       /*!< (@ 0x00000004) Maximum number of buffer words to transfer                 */
  __IM  uint32_t  AMOUNT;                       /*!< (@ 0x00000008) Number of samples transferred in the last transaction      */
} RADIO_DFEPACKET_Type;                         /*!< Size = 12 (0xc)                                                           */


/**
  * @brief DPPIC_TASKS_CHG [TASKS_CHG] (Channel group tasks)
  */
typedef struct {
  __OM  uint32_t  EN;                           /*!< (@ 0x00000000) Description cluster: Enable channel group n                */
  __OM  uint32_t  DIS;                          /*!< (@ 0x00000004) Description cluster: Disable channel group n               */
} DPPIC_TASKS_CHG_Type;                         /*!< Size = 8 (0x8)                                                            */


/**
  * @brief DPPIC_SUBSCRIBE_CHG [SUBSCRIBE_CHG] (Subscribe configuration for tasks)
  */
typedef struct {
  __IOM uint32_t  EN;                           /*!< (@ 0x00000000) Description cluster: Subscribe configuration
                                                                    for task CHG[n].EN                                         */
  __IOM uint32_t  DIS;                          /*!< (@ 0x00000004) Description cluster: Subscribe configuration
                                                                    for task CHG[n].DIS                                        */
} DPPIC_SUBSCRIBE_CHG_Type;                     /*!< Size = 8 (0x8)                                                            */


/**
  * @brief SPIM_PSEL [PSEL] (Unspecified)
  */
typedef struct {
  __IOM uint32_t  SCK;                          /*!< (@ 0x00000000) Pin select for SCK                                         */
  __IOM uint32_t  MOSI;                         /*!< (@ 0x00000004) Pin select for MOSI signal                                 */
  __IOM uint32_t  MISO;                         /*!< (@ 0x00000008) Pin select for MISO signal                                 */
  __IOM uint32_t  CSN;                          /*!< (@ 0x0000000C) Pin select for CSN                                         */
} SPIM_PSEL_Type;                               /*!< Size = 16 (0x10)                                                          */


/**
  * @brief SPIM_RXD [RXD] (RXD EasyDMA channel)
  */
typedef struct {
  __IOM uint32_t  PTR;                          /*!< (@ 0x00000000) Data pointer                                               */
  __IOM uint32_t  MAXCNT;                       /*!< (@ 0x00000004) Maximum number of bytes in receive buffer                  */
  __IM  uint32_t  AMOUNT;                       /*!< (@ 0x00000008) Number of bytes transferred in the last transaction        */
  __IOM uint32_t  LIST;                         /*!< (@ 0x0000000C) EasyDMA list type                                          */
} SPIM_RXD_Type;                                /*!< Size = 16 (0x10)                                                          */


/**
  * @brief SPIM_TXD [TXD] (TXD EasyDMA channel)
  */
typedef struct {
  __IOM uint32_t  PTR;                          /*!< (@ 0x00000000) Data pointer                                               */
  __IOM uint32_t  MAXCNT;                       /*!< (@ 0x00000004) Number of bytes in transmit buffer                         */
  __IM  uint32_t  AMOUNT;                       /*!< (@ 0x00000008) Number of bytes transferred in the last transaction        */
  __IOM uint32_t  LIST;                         /*!< (@ 0x0000000C) EasyDMA list type                                          */
} SPIM_TXD_Type;                                /*!< Size = 16 (0x10)                                                          */


/**
  * @brief SPIM_IFTIMING [IFTIMING] (Unspecified)
  */
typedef struct {
  __IOM uint32_t  RXDELAY;                      /*!< (@ 0x00000000) Sample delay for input serial data on MISO                 */
  __IOM uint32_t  CSNDUR;                       /*!< (@ 0x00000004) Minimum duration between edge of CSN and edge
                                                                    of SCK and minimum duration CSN must stay
                                                                    high between transactions                                  */
} SPIM_IFTIMING_Type;                           /*!< Size = 8 (0x8)                                                            */


/**
  * @brief SPIS_PSEL [PSEL] (Unspecified)
  */
typedef struct {
  __IOM uint32_t  SCK;                          /*!< (@ 0x00000000) Pin select for SCK                                         */
  __IOM uint32_t  MISO;                         /*!< (@ 0x00000004) Pin select for MISO signal                                 */
  __IOM uint32_t  MOSI;                         /*!< (@ 0x00000008) Pin select for MOSI signal                                 */
  __IOM uint32_t  CSN;                          /*!< (@ 0x0000000C) Pin select for CSN signal                                  */
} SPIS_PSEL_Type;                               /*!< Size = 16 (0x10)                                                          */


/**
  * @brief SPIS_RXD [RXD] (Unspecified)
  */
typedef struct {
  __IOM uint32_t  PTR;                          /*!< (@ 0x00000000) RXD data pointer                                           */
  __IOM uint32_t  MAXCNT;                       /*!< (@ 0x00000004) Maximum number of bytes in receive buffer                  */
  __IM  uint32_t  AMOUNT;                       /*!< (@ 0x00000008) Number of bytes received in last granted transaction       */
  __IOM uint32_t  LIST;                         /*!< (@ 0x0000000C) EasyDMA list type                                          */
} SPIS_RXD_Type;                                /*!< Size = 16 (0x10)                                                          */


/**
  * @brief SPIS_TXD [TXD] (Unspecified)
  */
typedef struct {
  __IOM uint32_t  PTR;                          /*!< (@ 0x00000000) TXD data pointer                                           */
  __IOM uint32_t  MAXCNT;                       /*!< (@ 0x00000004) Maximum number of bytes in transmit buffer                 */
  __IM  uint32_t  AMOUNT;                       /*!< (@ 0x00000008) Number of bytes transmitted in last granted transaction    */
  __IOM uint32_t  LIST;                         /*!< (@ 0x0000000C) EasyDMA list type                                          */
} SPIS_TXD_Type;                                /*!< Size = 16 (0x10)                                                          */


/**
  * @brief TWIM_PSEL [PSEL] (Unspecified)
  */
typedef struct {
  __IOM uint32_t  SCL;                          /*!< (@ 0x00000000) Pin select for SCL signal                                  */
  __IOM uint32_t  SDA;                          /*!< (@ 0x00000004) Pin select for SDA signal                                  */
} TWIM_PSEL_Type;                               /*!< Size = 8 (0x8)                                                            */


/**
  * @brief TWIM_RXD [RXD] (RXD EasyDMA channel)
  */
typedef struct {
  __IOM uint32_t  PTR;                          /*!< (@ 0x00000000) Data pointer                                               */
  __IOM uint32_t  MAXCNT;                       /*!< (@ 0x00000004) Maximum number of bytes in receive buffer                  */
  __IM  uint32_t  AMOUNT;                       /*!< (@ 0x00000008) Number of bytes transferred in the last transaction        */
  __IOM uint32_t  LIST;                         /*!< (@ 0x0000000C) EasyDMA list type                                          */
} TWIM_RXD_Type;                                /*!< Size = 16 (0x10)                                                          */


/**
  * @brief TWIM_TXD [TXD] (TXD EasyDMA channel)
  */
typedef struct {
  __IOM uint32_t  PTR;                          /*!< (@ 0x00000000) Data pointer                                               */
  __IOM uint32_t  MAXCNT;                       /*!< (@ 0x00000004) Maximum number of bytes in transmit buffer                 */
  __IM  uint32_t  AMOUNT;                       /*!< (@ 0x00000008) Number of bytes transferred in the last transaction        */
  __IOM uint32_t  LIST;                         /*!< (@ 0x0000000C) EasyDMA list type                                          */
} TWIM_TXD_Type;                                /*!< Size = 16 (0x10)                                                          */


/**
  * @brief TWIS_PSEL [PSEL] (Unspecified)
  */
typedef struct {
  __IOM uint32_t  SCL;                          /*!< (@ 0x00000000) Pin select for SCL signal                                  */
  __IOM uint32_t  SDA;                          /*!< (@ 0x00000004) Pin select for SDA signal                                  */
} TWIS_PSEL_Type;                               /*!< Size = 8 (0x8)                                                            */


/**
  * @brief TWIS_RXD [RXD] (RXD EasyDMA channel)
  */
typedef struct {
  __IOM uint32_t  PTR;                          /*!< (@ 0x00000000) RXD Data pointer                                           */
  __IOM uint32_t  MAXCNT;                       /*!< (@ 0x00000004) Maximum number of bytes in RXD buffer                      */
  __IM  uint32_t  AMOUNT;                       /*!< (@ 0x00000008) Number of bytes transferred in the last RXD transaction    */
  __IOM uint32_t  LIST;                         /*!< (@ 0x0000000C) EasyDMA list type                                          */
} TWIS_RXD_Type;                                /*!< Size = 16 (0x10)                                                          */


/**
  * @brief TWIS_TXD [TXD] (TXD EasyDMA channel)
  */
typedef struct {
  __IOM uint32_t  PTR;                          /*!< (@ 0x00000000) TXD Data pointer                                           */
  __IOM uint32_t  MAXCNT;                       /*!< (@ 0x00000004) Maximum number of bytes in TXD buffer                      */
  __IM  uint32_t  AMOUNT;                       /*!< (@ 0x00000008) Number of bytes transferred in the last TXD transaction    */
  __IOM uint32_t  LIST;                         /*!< (@ 0x0000000C) EasyDMA list type                                          */
} TWIS_TXD_Type;                                /*!< Size = 16 (0x10)                                                          */


/**
  * @brief UARTE_PSEL [PSEL] (Unspecified)
  */
typedef struct {
  __IOM uint32_t  RTS;                          /*!< (@ 0x00000000) Pin select for RTS signal                                  */
  __IOM uint32_t  TXD;                          /*!< (@ 0x00000004) Pin select for TXD signal                                  */
  __IOM uint32_t  CTS;                          /*!< (@ 0x00000008) Pin select for CTS signal                                  */
  __IOM uint32_t  RXD;                          /*!< (@ 0x0000000C) Pin select for RXD signal                                  */
} UARTE_PSEL_Type;                              /*!< Size = 16 (0x10)                                                          */


/**
  * @brief UARTE_RXD [RXD] (RXD EasyDMA channel)
  */
typedef struct {
  __IOM uint32_t  PTR;                          /*!< (@ 0x00000000) Data pointer                                               */
  __IOM uint32_t  MAXCNT;                       /*!< (@ 0x00000004) Maximum number of bytes in receive buffer                  */
  __IM  uint32_t  AMOUNT;                       /*!< (@ 0x00000008) Number of bytes transferred in the last transaction        */
} UARTE_RXD_Type;                               /*!< Size = 12 (0xc)                                                           */


/**
  * @brief UARTE_TXD [TXD] (TXD EasyDMA channel)
  */
typedef struct {
  __IOM uint32_t  PTR;                          /*!< (@ 0x00000000) Data pointer                                               */
  __IOM uint32_t  MAXCNT;                       /*!< (@ 0x00000004) Maximum number of bytes in transmit buffer                 */
  __IM  uint32_t  AMOUNT;                       /*!< (@ 0x00000008) Number of bytes transferred in the last transaction        */
} UARTE_TXD_Type;                               /*!< Size = 12 (0xc)                                                           */


/**
  * @brief ACL_ACL [ACL] (Unspecified)
  */
typedef struct {
  __IOM uint32_t  ADDR;                         /*!< (@ 0x00000000) Description cluster: Configure the word-aligned
                                                                    start address of region n to protect                       */
  __IOM uint32_t  SIZE;                         /*!< (@ 0x00000004) Description cluster: Size of region to protect
                                                                    counting from address ACL[n].ADDR. Write
                                                                    '0' as no effect.                                          */
  __IOM uint32_t  PERM;                         /*!< (@ 0x00000008) Description cluster: Access permissions for region
                                                                    n as defined by start address ACL[n].ADDR
                                                                    and size ACL[n].SIZE                                       */
  __IM  uint32_t  RESERVED;
} ACL_ACL_Type;                                 /*!< Size = 16 (0x10)                                                          */


/**
  * @brief VMC_RAM [RAM] (Unspecified)
  */
typedef struct {
  __IOM uint32_t  POWER;                        /*!< (@ 0x00000000) Description cluster: RAM[n] power control register         */
  __IOM uint32_t  POWERSET;                     /*!< (@ 0x00000004) Description cluster: RAM[n] power control set
                                                                    register                                                   */
  __IOM uint32_t  POWERCLR;                     /*!< (@ 0x00000008) Description cluster: RAM[n] power control clear
                                                                    register                                                   */
  __IM  uint32_t  RESERVED;
} VMC_RAM_Type;                                 /*!< Size = 16 (0x10)                                                          */


/** @} */ /* End of group Device_Peripheral_clusters */


/* =========================================================================================================================== */
/* ================                            Device Specific Peripheral Section                             ================ */
/* =========================================================================================================================== */


/** @addtogroup Device_Peripheral_peripherals
  * @{
  */



/* =========================================================================================================================== */
/* ================                                          FICR_NS                                          ================ */
/* =========================================================================================================================== */


/**
  * @brief Factory Information Configuration Registers (FICR_NS)
  */

typedef struct {                                /*!< (@ 0x01FF0000) FICR_NS Structure                                          */
  __IM  uint32_t  RESERVED[128];
  __IOM FICR_INFO_Type INFO;                    /*!< (@ 0x00000200) Device info                                                */
  __IM  uint32_t  RESERVED1[21];
  __IM  uint32_t  ER[4];                        /*!< (@ 0x00000280) Description collection: Encryption Root, word
                                                                    n                                                          */
  __IM  uint32_t  IR[4];                        /*!< (@ 0x00000290) Description collection: Identity Root, word n              */
  __IM  uint32_t  DEVICEADDRTYPE;               /*!< (@ 0x000002A0) Device address type                                        */
  __IM  uint32_t  DEVICEADDR[2];                /*!< (@ 0x000002A4) Description collection: Device address n                   */
  __IM  uint32_t  RESERVED2[21];
  __IOM FICR_TRIMCNF_Type TRIMCNF[32];          /*!< (@ 0x00000300) Unspecified                                                */
} NRF_FICR_Type;                                /*!< Size = 1024 (0x400)                                                       */



/* =========================================================================================================================== */
/* ================                                          UICR_NS                                          ================ */
/* =========================================================================================================================== */


/**
  * @brief User Information Configuration Registers (UICR_NS)
  */

typedef struct {                                /*!< (@ 0x01FF8000) UICR_NS Structure                                          */
  __IOM uint32_t  APPROTECT;                    /*!< (@ 0x00000000) Access port protection                                     */
  __IOM uint32_t  ERASEPROTECT;                 /*!< (@ 0x00000004) Erase protection                                           */
  __IM  uint32_t  RESERVED[126];
  __IOM uint32_t  NRFFW[32];                    /*!< (@ 0x00000200) Description collection: Reserved for Nordic firmware
                                                                    design                                                     */
  __IM  uint32_t  RESERVED1[32];
  __IOM uint32_t  CUSTOMER[32];                 /*!< (@ 0x00000300) Description collection: Reserved for customer              */
} NRF_UICR_Type;                                /*!< Size = 896 (0x380)                                                        */



/* =========================================================================================================================== */
/* ================                                          CTI_NS                                           ================ */
/* =========================================================================================================================== */


/**
  * @brief Cross-Trigger Interface control. NOTE: this is not a separate peripheral, but describes CM33 functionality. (CTI_NS)
  */

typedef struct {                                /*!< (@ 0xE0042000) CTI_NS Structure                                           */
  __IOM uint32_t  CTICONTROL;                   /*!< (@ 0x00000000) CTI Control register                                       */
  __IM  uint32_t  RESERVED[3];
  __OM  uint32_t  CTIINTACK;                    /*!< (@ 0x00000010) CTI Interrupt Acknowledge register                         */
  __IOM uint32_t  CTIAPPSET;                    /*!< (@ 0x00000014) CTI Application Trigger Set register                       */
  __OM  uint32_t  CTIAPPCLEAR;                  /*!< (@ 0x00000018) CTI Application Trigger Clear register                     */
  __OM  uint32_t  CTIAPPPULSE;                  /*!< (@ 0x0000001C) CTI Application Pulse register                             */
  __IOM uint32_t  CTIINEN[8];                   /*!< (@ 0x00000020) Description collection: CTI Trigger input                  */
  __IM  uint32_t  RESERVED1[24];
  __IOM uint32_t  CTIOUTEN[8];                  /*!< (@ 0x000000A0) Description collection: CTI Trigger output                 */
  __IM  uint32_t  RESERVED2[28];
  __IM  uint32_t  CTITRIGINSTATUS;              /*!< (@ 0x00000130) CTI Trigger In Status register                             */
  __IM  uint32_t  CTITRIGOUTSTATUS;             /*!< (@ 0x00000134) CTI Trigger Out Status register                            */
  __IM  uint32_t  CTICHINSTATUS;                /*!< (@ 0x00000138) CTI Channel In Status register                             */
  __IM  uint32_t  RESERVED3;
  __IOM uint32_t  CTIGATE;                      /*!< (@ 0x00000140) Enable CTI Channel Gate register                           */
  __IM  uint32_t  RESERVED4[926];
  __IM  uint32_t  DEVARCH;                      /*!< (@ 0x00000FBC) Device Architecture register                               */
  __IM  uint32_t  RESERVED5[2];
  __IM  uint32_t  DEVID;                        /*!< (@ 0x00000FC8) Device Configuration register                              */
  __IM  uint32_t  DEVTYPE;                      /*!< (@ 0x00000FCC) Device Type Identifier register                            */
  __IM  uint32_t  PIDR4;                        /*!< (@ 0x00000FD0) Peripheral ID4 Register                                    */
  __IM  uint32_t  PIDR5;                        /*!< (@ 0x00000FD4) Peripheral ID5 register                                    */
  __IM  uint32_t  PIDR6;                        /*!< (@ 0x00000FD8) Peripheral ID6 register                                    */
  __IM  uint32_t  PIDR7;                        /*!< (@ 0x00000FDC) Peripheral ID7 register                                    */
  __IM  uint32_t  PIDR0;                        /*!< (@ 0x00000FE0) Peripheral ID0 Register                                    */
  __IM  uint32_t  PIDR1;                        /*!< (@ 0x00000FE4) Peripheral ID1 Register                                    */
  __IM  uint32_t  PIDR2;                        /*!< (@ 0x00000FE8) Peripheral ID2 Register                                    */
  __IM  uint32_t  PIDR3;                        /*!< (@ 0x00000FEC) Peripheral ID3 Register                                    */
  __IM  uint32_t  CIDR0;                        /*!< (@ 0x00000FF0) Component ID0 Register                                     */
  __IM  uint32_t  CIDR1;                        /*!< (@ 0x00000FF4) Component ID1 Register                                     */
  __IM  uint32_t  CIDR2;                        /*!< (@ 0x00000FF8) Component ID2 Register                                     */
  __IM  uint32_t  CIDR3;                        /*!< (@ 0x00000FFC) Component ID3 Register                                     */
} NRF_CTI_Type;                                 /*!< Size = 4096 (0x1000)                                                      */



/* =========================================================================================================================== */
/* ================                                          DCNF_NS                                          ================ */
/* =========================================================================================================================== */


/**
  * @brief Domain configuration management (DCNF_NS)
  */

typedef struct {                                /*!< (@ 0x41000000) DCNF_NS Structure                                          */
  __IM  uint32_t  RESERVED[264];
  __IM  uint32_t  CPUID;                        /*!< (@ 0x00000420) CPU ID of this subsystem                                   */
} NRF_DCNF_Type;                                /*!< Size = 1060 (0x424)                                                       */



/* =========================================================================================================================== */
/* ================                                        VREQCTRL_NS                                        ================ */
/* =========================================================================================================================== */


/**
  * @brief Voltage request control (VREQCTRL_NS)
  */

typedef struct {                                /*!< (@ 0x41004000) VREQCTRL_NS Structure                                      */
  __IM  uint32_t  RESERVED[320];
  __IOM VREQCTRL_VREGRADIO_Type VREGRADIO;      /*!< (@ 0x00000500) Unspecified                                                */
} NRF_VREQCTRL_Type;                            /*!< Size = 1292 (0x50c)                                                       */



/* =========================================================================================================================== */
/* ================                                         CLOCK_NS                                          ================ */
/* =========================================================================================================================== */


/**
  * @brief Clock management (CLOCK_NS)
  */

typedef struct {                                /*!< (@ 0x41005000) CLOCK_NS Structure                                         */
  __OM  uint32_t  TASKS_HFCLKSTART;             /*!< (@ 0x00000000) Start HFCLK128M/HFCLK64M source as selected in
                                                                    HFCLKSRC                                                   */
  __OM  uint32_t  TASKS_HFCLKSTOP;              /*!< (@ 0x00000004) Stop HFCLK128M/HFCLK64M source                             */
  __OM  uint32_t  TASKS_LFCLKSTART;             /*!< (@ 0x00000008) Start LFCLK source as selected in LFCLKSRC                 */
  __OM  uint32_t  TASKS_LFCLKSTOP;              /*!< (@ 0x0000000C) Stop LFCLK source                                          */
  __OM  uint32_t  TASKS_CAL;                    /*!< (@ 0x00000010) Start calibration of LFRC oscillator                       */
  __IM  uint32_t  RESERVED;
  __OM  uint32_t  TASKS_HFCLKAUDIOSTART;        /*!< (@ 0x00000018) Start HFCLKAUDIO source                                    */
  __OM  uint32_t  TASKS_HFCLKAUDIOSTOP;         /*!< (@ 0x0000001C) Stop HFCLKAUDIO source                                     */
  __OM  uint32_t  TASKS_HFCLK192MSTART;         /*!< (@ 0x00000020) Start HFCLK192M source as selected in HFCLK192MSRC         */
  __OM  uint32_t  TASKS_HFCLK192MSTOP;          /*!< (@ 0x00000024) Stop HFCLK192M source                                      */
  __IM  uint32_t  RESERVED1[22];
  __IOM uint32_t  SUBSCRIBE_HFCLKSTART;         /*!< (@ 0x00000080) Subscribe configuration for task HFCLKSTART                */
  __IOM uint32_t  SUBSCRIBE_HFCLKSTOP;          /*!< (@ 0x00000084) Subscribe configuration for task HFCLKSTOP                 */
  __IOM uint32_t  SUBSCRIBE_LFCLKSTART;         /*!< (@ 0x00000088) Subscribe configuration for task LFCLKSTART                */
  __IOM uint32_t  SUBSCRIBE_LFCLKSTOP;          /*!< (@ 0x0000008C) Subscribe configuration for task LFCLKSTOP                 */
  __IOM uint32_t  SUBSCRIBE_CAL;                /*!< (@ 0x00000090) Subscribe configuration for task CAL                       */
  __IM  uint32_t  RESERVED2;
  __IOM uint32_t  SUBSCRIBE_HFCLKAUDIOSTART;    /*!< (@ 0x00000098) Subscribe configuration for task HFCLKAUDIOSTART           */
  __IOM uint32_t  SUBSCRIBE_HFCLKAUDIOSTOP;     /*!< (@ 0x0000009C) Subscribe configuration for task HFCLKAUDIOSTOP            */
  __IOM uint32_t  SUBSCRIBE_HFCLK192MSTART;     /*!< (@ 0x000000A0) Subscribe configuration for task HFCLK192MSTART            */
  __IOM uint32_t  SUBSCRIBE_HFCLK192MSTOP;      /*!< (@ 0x000000A4) Subscribe configuration for task HFCLK192MSTOP             */
  __IM  uint32_t  RESERVED3[22];
  __IOM uint32_t  EVENTS_HFCLKSTARTED;          /*!< (@ 0x00000100) HFCLK128M/HFCLK64M source started                          */
  __IOM uint32_t  EVENTS_LFCLKSTARTED;          /*!< (@ 0x00000104) LFCLK source started                                       */
  __IM  uint32_t  RESERVED4[5];
  __IOM uint32_t  EVENTS_DONE;                  /*!< (@ 0x0000011C) Calibration of LFRC oscillator complete event              */
  __IOM uint32_t  EVENTS_HFCLKAUDIOSTARTED;     /*!< (@ 0x00000120) HFCLKAUDIO source started                                  */
  __IOM uint32_t  EVENTS_HFCLK192MSTARTED;      /*!< (@ 0x00000124) HFCLK192M source started                                   */
  __IM  uint32_t  RESERVED5[22];
  __IOM uint32_t  PUBLISH_HFCLKSTARTED;         /*!< (@ 0x00000180) Publish configuration for event HFCLKSTARTED               */
  __IOM uint32_t  PUBLISH_LFCLKSTARTED;         /*!< (@ 0x00000184) Publish configuration for event LFCLKSTARTED               */
  __IM  uint32_t  RESERVED6[5];
  __IOM uint32_t  PUBLISH_DONE;                 /*!< (@ 0x0000019C) Publish configuration for event DONE                       */
  __IOM uint32_t  PUBLISH_HFCLKAUDIOSTARTED;    /*!< (@ 0x000001A0) Publish configuration for event HFCLKAUDIOSTARTED          */
  __IOM uint32_t  PUBLISH_HFCLK192MSTARTED;     /*!< (@ 0x000001A4) Publish configuration for event HFCLK192MSTARTED           */
  __IM  uint32_t  RESERVED7[86];
  __IOM uint32_t  INTEN;                        /*!< (@ 0x00000300) Enable or disable interrupt                                */
  __IOM uint32_t  INTENSET;                     /*!< (@ 0x00000304) Enable interrupt                                           */
  __IOM uint32_t  INTENCLR;                     /*!< (@ 0x00000308) Disable interrupt                                          */
  __IM  uint32_t  INTPEND;                      /*!< (@ 0x0000030C) Pending interrupts                                         */
  __IM  uint32_t  RESERVED8[62];
  __IM  uint32_t  HFCLKRUN;                     /*!< (@ 0x00000408) Status indicating that HFCLKSTART task has been
                                                                    triggered                                                  */
  __IM  uint32_t  HFCLKSTAT;                    /*!< (@ 0x0000040C) Status indicating which HFCLK128M/HFCLK64M source
                                                                    is running Note: Value of this register
                                                                    in any CLOCK instance reflects status only
                                                                    due to configurations/actions in that CLOCK
                                                                    instance.                                                  */
  __IM  uint32_t  RESERVED9;
  __IM  uint32_t  LFCLKRUN;                     /*!< (@ 0x00000414) Status indicating that LFCLKSTART task has been
                                                                    triggered                                                  */
  __IM  uint32_t  LFCLKSTAT;                    /*!< (@ 0x00000418) Status indicating which LFCLK source is running
                                                                    Note: Value of this register in any CLOCK
                                                                    instance reflects status only due to configurations/action
                                                                    in that CLOCK instance.                                    */
  __IM  uint32_t  LFCLKSRCCOPY;                 /*!< (@ 0x0000041C) Copy of LFCLKSRC register, set when LFCLKSTART
                                                                    task was triggered                                         */
  __IM  uint32_t  RESERVED10[12];
  __IM  uint32_t  HFCLKAUDIORUN;                /*!< (@ 0x00000450) Status indicating that HFCLKAUDIOSTART task has
                                                                    been triggered                                             */
  __IM  uint32_t  HFCLKAUDIOSTAT;               /*!< (@ 0x00000454) Status indicating which HFCLKAUDIO source is
                                                                    running                                                    */
  __IM  uint32_t  HFCLK192MRUN;                 /*!< (@ 0x00000458) Status indicating that HFCLK192MSTART task has
                                                                    been triggered                                             */
  __IM  uint32_t  HFCLK192MSTAT;                /*!< (@ 0x0000045C) Status indicating which HFCLK192M source is running        */
  __IM  uint32_t  RESERVED11[45];
  __IOM uint32_t  HFCLKSRC;                     /*!< (@ 0x00000514) Clock source for HFCLK128M/HFCLK64M                        */
  __IOM uint32_t  LFCLKSRC;                     /*!< (@ 0x00000518) Clock source for LFCLK                                     */
  __IM  uint32_t  RESERVED12[15];
  __IOM uint32_t  HFCLKCTRL;                    /*!< (@ 0x00000558) HFCLK128M frequency configuration                          */
  __IOM CLOCK_HFCLKAUDIO_Type HFCLKAUDIO;       /*!< (@ 0x0000055C) Unspecified                                                */
  __IM  uint32_t  RESERVED13[4];
  __IM  uint32_t  HFCLKALWAYSRUN;               /*!< (@ 0x00000570) Automatic or manual control of HFCLK128M/HFCLK64M          */
  __IM  uint32_t  LFCLKALWAYSRUN;               /*!< (@ 0x00000574) Automatic or manual control of LFCLK                       */
  __IM  uint32_t  RESERVED14;
  __IM  uint32_t  HFCLKAUDIOALWAYSRUN;          /*!< (@ 0x0000057C) Automatic or manual control of HFCLKAUDIO                  */
  __IOM uint32_t  HFCLK192MSRC;                 /*!< (@ 0x00000580) Clock source for HFCLK192M                                 */
  __IM  uint32_t  HFCLK192MALWAYSRUN;           /*!< (@ 0x00000584) Automatic or manual control of HFCLK192M                   */
  __IM  uint32_t  RESERVED15[12];
  __IOM uint32_t  HFCLK192MCTRL;                /*!< (@ 0x000005B8) HFCLK192M frequency configuration                          */
} NRF_CLOCK_Type;                               /*!< Size = 1468 (0x5bc)                                                       */



/* =========================================================================================================================== */
/* ================                                         POWER_NS                                          ================ */
/* =========================================================================================================================== */


/**
  * @brief Power control (POWER_NS)
  */

typedef struct {                                /*!< (@ 0x41005000) POWER_NS Structure                                         */
  __IM  uint32_t  RESERVED[30];
  __OM  uint32_t  TASKS_CONSTLAT;               /*!< (@ 0x00000078) Enable constant latency mode                               */
  __OM  uint32_t  TASKS_LOWPWR;                 /*!< (@ 0x0000007C) Enable low power mode (variable latency)                   */
  __IM  uint32_t  RESERVED1[30];
  __IOM uint32_t  SUBSCRIBE_CONSTLAT;           /*!< (@ 0x000000F8) Subscribe configuration for task CONSTLAT                  */
  __IOM uint32_t  SUBSCRIBE_LOWPWR;             /*!< (@ 0x000000FC) Subscribe configuration for task LOWPWR                    */
  __IM  uint32_t  RESERVED2[2];
  __IOM uint32_t  EVENTS_POFWARN;               /*!< (@ 0x00000108) Power failure warning                                      */
  __IM  uint32_t  RESERVED3[2];
  __IOM uint32_t  EVENTS_SLEEPENTER;            /*!< (@ 0x00000114) CPU entered WFI/WFE sleep                                  */
  __IOM uint32_t  EVENTS_SLEEPEXIT;             /*!< (@ 0x00000118) CPU exited WFI/WFE sleep                                   */
  __IM  uint32_t  RESERVED4[27];
  __IOM uint32_t  PUBLISH_POFWARN;              /*!< (@ 0x00000188) Publish configuration for event POFWARN                    */
  __IM  uint32_t  RESERVED5[2];
  __IOM uint32_t  PUBLISH_SLEEPENTER;           /*!< (@ 0x00000194) Publish configuration for event SLEEPENTER                 */
  __IOM uint32_t  PUBLISH_SLEEPEXIT;            /*!< (@ 0x00000198) Publish configuration for event SLEEPEXIT                  */
  __IM  uint32_t  RESERVED6[89];
  __IOM uint32_t  INTEN;                        /*!< (@ 0x00000300) Enable or disable interrupt                                */
  __IOM uint32_t  INTENSET;                     /*!< (@ 0x00000304) Enable interrupt                                           */
  __IOM uint32_t  INTENCLR;                     /*!< (@ 0x00000308) Disable interrupt                                          */
  __IM  uint32_t  RESERVED7[132];
  __IOM uint32_t  GPREGRET[2];                  /*!< (@ 0x0000051C) Description collection: General purpose retention
                                                                    register                                                   */
} NRF_POWER_Type;                               /*!< Size = 1316 (0x524)                                                       */



/* =========================================================================================================================== */
/* ================                                         RESET_NS                                          ================ */
/* =========================================================================================================================== */


/**
  * @brief Reset control (RESET_NS)
  */

typedef struct {                                /*!< (@ 0x41005000) RESET_NS Structure                                         */
  __IM  uint32_t  RESERVED[256];
  __IOM uint32_t  RESETREAS;                    /*!< (@ 0x00000400) Reset reason                                               */
  __IM  uint32_t  RESERVED1[131];
  __IOM RESET_NETWORK_Type NETWORK;             /*!< (@ 0x00000610) ULP network core control                                   */
} NRF_RESET_Type;                               /*!< Size = 1560 (0x618)                                                       */



/* =========================================================================================================================== */
/* ================                                         CTRLAP_NS                                         ================ */
/* =========================================================================================================================== */


/**
  * @brief Control access port (CTRLAP_NS)
  */

typedef struct {                                /*!< (@ 0x41006000) CTRLAP_NS Structure                                        */
  __IM  uint32_t  RESERVED[256];
  __IOM CTRLAPPERI_MAILBOX_Type MAILBOX;        /*!< (@ 0x00000400) Unspecified                                                */
  __IM  uint32_t  RESERVED1[30];
  __IOM CTRLAPPERI_ERASEPROTECT_Type ERASEPROTECT;/*!< (@ 0x00000500) Unspecified                                              */
  __IM  uint32_t  RESERVED2[14];
  __IOM CTRLAPPERI_APPROTECT_Type APPROTECT;    /*!< (@ 0x00000540) Unspecified                                                */
  __IOM CTRLAPPERI_SECUREAPPROTECT_Type SECUREAPPROTECT;/*!< (@ 0x00000548) Unspecified                                        */
  __IM  uint32_t  RESERVED3[44];
  __IM  uint32_t  STATUS;                       /*!< (@ 0x00000600) Status bits for CTRL-AP peripheral                         */
} NRF_CTRLAPPERI_Type;                          /*!< Size = 1540 (0x604)                                                       */



/* =========================================================================================================================== */
/* ================                                         RADIO_NS                                          ================ */
/* =========================================================================================================================== */


/**
  * @brief 2.4 GHz radio (RADIO_NS)
  */

typedef struct {                                /*!< (@ 0x41008000) RADIO_NS Structure                                         */
  __OM  uint32_t  TASKS_TXEN;                   /*!< (@ 0x00000000) Enable RADIO in TX mode                                    */
  __OM  uint32_t  TASKS_RXEN;                   /*!< (@ 0x00000004) Enable RADIO in RX mode                                    */
  __OM  uint32_t  TASKS_START;                  /*!< (@ 0x00000008) Start RADIO                                                */
  __OM  uint32_t  TASKS_STOP;                   /*!< (@ 0x0000000C) Stop RADIO                                                 */
  __OM  uint32_t  TASKS_DISABLE;                /*!< (@ 0x00000010) Disable RADIO                                              */
  __OM  uint32_t  TASKS_RSSISTART;              /*!< (@ 0x00000014) Start the RSSI and take one single sample of
                                                                    the receive signal strength                                */
  __OM  uint32_t  TASKS_RSSISTOP;               /*!< (@ 0x00000018) Stop the RSSI measurement                                  */
  __OM  uint32_t  TASKS_BCSTART;                /*!< (@ 0x0000001C) Start the bit counter                                      */
  __OM  uint32_t  TASKS_BCSTOP;                 /*!< (@ 0x00000020) Stop the bit counter                                       */
  __OM  uint32_t  TASKS_EDSTART;                /*!< (@ 0x00000024) Start the energy detect measurement used in IEEE
                                                                    802.15.4 mode                                              */
  __OM  uint32_t  TASKS_EDSTOP;                 /*!< (@ 0x00000028) Stop the energy detect measurement                         */
  __OM  uint32_t  TASKS_CCASTART;               /*!< (@ 0x0000002C) Start the clear channel assessment used in IEEE
                                                                    802.15.4 mode                                              */
  __OM  uint32_t  TASKS_CCASTOP;                /*!< (@ 0x00000030) Stop the clear channel assessment                          */
  __IM  uint32_t  RESERVED[19];
  __IOM uint32_t  SUBSCRIBE_TXEN;               /*!< (@ 0x00000080) Subscribe configuration for task TXEN                      */
  __IOM uint32_t  SUBSCRIBE_RXEN;               /*!< (@ 0x00000084) Subscribe configuration for task RXEN                      */
  __IOM uint32_t  SUBSCRIBE_START;              /*!< (@ 0x00000088) Subscribe configuration for task START                     */
  __IOM uint32_t  SUBSCRIBE_STOP;               /*!< (@ 0x0000008C) Subscribe configuration for task STOP                      */
  __IOM uint32_t  SUBSCRIBE_DISABLE;            /*!< (@ 0x00000090) Subscribe configuration for task DISABLE                   */
  __IOM uint32_t  SUBSCRIBE_RSSISTART;          /*!< (@ 0x00000094) Subscribe configuration for task RSSISTART                 */
  __IOM uint32_t  SUBSCRIBE_RSSISTOP;           /*!< (@ 0x00000098) Subscribe configuration for task RSSISTOP                  */
  __IOM uint32_t  SUBSCRIBE_BCSTART;            /*!< (@ 0x0000009C) Subscribe configuration for task BCSTART                   */
  __IOM uint32_t  SUBSCRIBE_BCSTOP;             /*!< (@ 0x000000A0) Subscribe configuration for task BCSTOP                    */
  __IOM uint32_t  SUBSCRIBE_EDSTART;            /*!< (@ 0x000000A4) Subscribe configuration for task EDSTART                   */
  __IOM uint32_t  SUBSCRIBE_EDSTOP;             /*!< (@ 0x000000A8) Subscribe configuration for task EDSTOP                    */
  __IOM uint32_t  SUBSCRIBE_CCASTART;           /*!< (@ 0x000000AC) Subscribe configuration for task CCASTART                  */
  __IOM uint32_t  SUBSCRIBE_CCASTOP;            /*!< (@ 0x000000B0) Subscribe configuration for task CCASTOP                   */
  __IM  uint32_t  RESERVED1[19];
  __IOM uint32_t  EVENTS_READY;                 /*!< (@ 0x00000100) RADIO has ramped up and is ready to be started             */
  __IOM uint32_t  EVENTS_ADDRESS;               /*!< (@ 0x00000104) Address sent or received                                   */
  __IOM uint32_t  EVENTS_PAYLOAD;               /*!< (@ 0x00000108) Packet payload sent or received                            */
  __IOM uint32_t  EVENTS_END;                   /*!< (@ 0x0000010C) Packet sent or received                                    */
  __IOM uint32_t  EVENTS_DISABLED;              /*!< (@ 0x00000110) RADIO has been disabled                                    */
  __IOM uint32_t  EVENTS_DEVMATCH;              /*!< (@ 0x00000114) A device address match occurred on the last received
                                                                    packet                                                     */
  __IOM uint32_t  EVENTS_DEVMISS;               /*!< (@ 0x00000118) No device address match occurred on the last
                                                                    received packet                                            */
  __IOM uint32_t  EVENTS_RSSIEND;               /*!< (@ 0x0000011C) Sampling of receive signal strength complete               */
  __IM  uint32_t  RESERVED2[2];
  __IOM uint32_t  EVENTS_BCMATCH;               /*!< (@ 0x00000128) Bit counter reached bit count value                        */
  __IM  uint32_t  RESERVED3;
  __IOM uint32_t  EVENTS_CRCOK;                 /*!< (@ 0x00000130) Packet received with CRC ok                                */
  __IOM uint32_t  EVENTS_CRCERROR;              /*!< (@ 0x00000134) Packet received with CRC error                             */
  __IOM uint32_t  EVENTS_FRAMESTART;            /*!< (@ 0x00000138) IEEE 802.15.4 length field received                        */
  __IOM uint32_t  EVENTS_EDEND;                 /*!< (@ 0x0000013C) Sampling of energy detection complete. A new
                                                                    ED sample is ready for readout from the
                                                                    RADIO.EDSAMPLE register                                    */
  __IOM uint32_t  EVENTS_EDSTOPPED;             /*!< (@ 0x00000140) The sampling of energy detection has stopped               */
  __IOM uint32_t  EVENTS_CCAIDLE;               /*!< (@ 0x00000144) Wireless medium in idle - clear to send                    */
  __IOM uint32_t  EVENTS_CCABUSY;               /*!< (@ 0x00000148) Wireless medium busy - do not send                         */
  __IOM uint32_t  EVENTS_CCASTOPPED;            /*!< (@ 0x0000014C) The CCA has stopped                                        */
  __IOM uint32_t  EVENTS_RATEBOOST;             /*!< (@ 0x00000150) Ble_LR CI field received, receive mode is changed
                                                                    from Ble_LR125Kbit to Ble_LR500Kbit.                       */
  __IOM uint32_t  EVENTS_TXREADY;               /*!< (@ 0x00000154) RADIO has ramped up and is ready to be started
                                                                    TX path                                                    */
  __IOM uint32_t  EVENTS_RXREADY;               /*!< (@ 0x00000158) RADIO has ramped up and is ready to be started
                                                                    RX path                                                    */
  __IOM uint32_t  EVENTS_MHRMATCH;              /*!< (@ 0x0000015C) MAC header match found                                     */
  __IM  uint32_t  RESERVED4[3];
  __IOM uint32_t  EVENTS_PHYEND;                /*!< (@ 0x0000016C) Generated when last bit is sent on air                     */
  __IOM uint32_t  EVENTS_CTEPRESENT;            /*!< (@ 0x00000170) CTE is present (early warning right after receiving
                                                                    CTEInfo byte)                                              */
  __IM  uint32_t  RESERVED5[3];
  __IOM uint32_t  PUBLISH_READY;                /*!< (@ 0x00000180) Publish configuration for event READY                      */
  __IOM uint32_t  PUBLISH_ADDRESS;              /*!< (@ 0x00000184) Publish configuration for event ADDRESS                    */
  __IOM uint32_t  PUBLISH_PAYLOAD;              /*!< (@ 0x00000188) Publish configuration for event PAYLOAD                    */
  __IOM uint32_t  PUBLISH_END;                  /*!< (@ 0x0000018C) Publish configuration for event END                        */
  __IOM uint32_t  PUBLISH_DISABLED;             /*!< (@ 0x00000190) Publish configuration for event DISABLED                   */
  __IOM uint32_t  PUBLISH_DEVMATCH;             /*!< (@ 0x00000194) Publish configuration for event DEVMATCH                   */
  __IOM uint32_t  PUBLISH_DEVMISS;              /*!< (@ 0x00000198) Publish configuration for event DEVMISS                    */
  __IOM uint32_t  PUBLISH_RSSIEND;              /*!< (@ 0x0000019C) Publish configuration for event RSSIEND                    */
  __IM  uint32_t  RESERVED6[2];
  __IOM uint32_t  PUBLISH_BCMATCH;              /*!< (@ 0x000001A8) Publish configuration for event BCMATCH                    */
  __IM  uint32_t  RESERVED7;
  __IOM uint32_t  PUBLISH_CRCOK;                /*!< (@ 0x000001B0) Publish configuration for event CRCOK                      */
  __IOM uint32_t  PUBLISH_CRCERROR;             /*!< (@ 0x000001B4) Publish configuration for event CRCERROR                   */
  __IOM uint32_t  PUBLISH_FRAMESTART;           /*!< (@ 0x000001B8) Publish configuration for event FRAMESTART                 */
  __IOM uint32_t  PUBLISH_EDEND;                /*!< (@ 0x000001BC) Publish configuration for event EDEND                      */
  __IOM uint32_t  PUBLISH_EDSTOPPED;            /*!< (@ 0x000001C0) Publish configuration for event EDSTOPPED                  */
  __IOM uint32_t  PUBLISH_CCAIDLE;              /*!< (@ 0x000001C4) Publish configuration for event CCAIDLE                    */
  __IOM uint32_t  PUBLISH_CCABUSY;              /*!< (@ 0x000001C8) Publish configuration for event CCABUSY                    */
  __IOM uint32_t  PUBLISH_CCASTOPPED;           /*!< (@ 0x000001CC) Publish configuration for event CCASTOPPED                 */
  __IOM uint32_t  PUBLISH_RATEBOOST;            /*!< (@ 0x000001D0) Publish configuration for event RATEBOOST                  */
  __IOM uint32_t  PUBLISH_TXREADY;              /*!< (@ 0x000001D4) Publish configuration for event TXREADY                    */
  __IOM uint32_t  PUBLISH_RXREADY;              /*!< (@ 0x000001D8) Publish configuration for event RXREADY                    */
  __IOM uint32_t  PUBLISH_MHRMATCH;             /*!< (@ 0x000001DC) Publish configuration for event MHRMATCH                   */
  __IM  uint32_t  RESERVED8[3];
  __IOM uint32_t  PUBLISH_PHYEND;               /*!< (@ 0x000001EC) Publish configuration for event PHYEND                     */
  __IOM uint32_t  PUBLISH_CTEPRESENT;           /*!< (@ 0x000001F0) Publish configuration for event CTEPRESENT                 */
  __IM  uint32_t  RESERVED9[3];
  __IOM uint32_t  SHORTS;                       /*!< (@ 0x00000200) Shortcuts between local events and tasks                   */
  __IM  uint32_t  RESERVED10[64];
  __IOM uint32_t  INTENSET;                     /*!< (@ 0x00000304) Enable interrupt                                           */
  __IOM uint32_t  INTENCLR;                     /*!< (@ 0x00000308) Disable interrupt                                          */
  __IM  uint32_t  RESERVED11[61];
  __IM  uint32_t  CRCSTATUS;                    /*!< (@ 0x00000400) CRC status                                                 */
  __IM  uint32_t  RESERVED12;
  __IM  uint32_t  RXMATCH;                      /*!< (@ 0x00000408) Received address                                           */
  __IM  uint32_t  RXCRC;                        /*!< (@ 0x0000040C) CRC field of previously received packet                    */
  __IM  uint32_t  DAI;                          /*!< (@ 0x00000410) Device address match index                                 */
  __IM  uint32_t  PDUSTAT;                      /*!< (@ 0x00000414) Payload status                                             */
  __IM  uint32_t  RESERVED13[13];
  __IM  uint32_t  CTESTATUS;                    /*!< (@ 0x0000044C) CTEInfo parsed from received packet                        */
  __IM  uint32_t  RESERVED14[2];
  __IM  uint32_t  DFESTATUS;                    /*!< (@ 0x00000458) DFE status information                                     */
  __IM  uint32_t  RESERVED15[42];
  __IOM uint32_t  PACKETPTR;                    /*!< (@ 0x00000504) Packet pointer                                             */
  __IOM uint32_t  FREQUENCY;                    /*!< (@ 0x00000508) Frequency                                                  */
  __IOM uint32_t  TXPOWER;                      /*!< (@ 0x0000050C) Output power                                               */
  __IOM uint32_t  MODE;                         /*!< (@ 0x00000510) Data rate and modulation                                   */
  __IOM uint32_t  PCNF0;                        /*!< (@ 0x00000514) Packet configuration register 0                            */
  __IOM uint32_t  PCNF1;                        /*!< (@ 0x00000518) Packet configuration register 1                            */
  __IOM uint32_t  BASE0;                        /*!< (@ 0x0000051C) Base address 0                                             */
  __IOM uint32_t  BASE1;                        /*!< (@ 0x00000520) Base address 1                                             */
  __IOM uint32_t  PREFIX0;                      /*!< (@ 0x00000524) Prefixes bytes for logical addresses 0-3                   */
  __IOM uint32_t  PREFIX1;                      /*!< (@ 0x00000528) Prefixes bytes for logical addresses 4-7                   */
  __IOM uint32_t  TXADDRESS;                    /*!< (@ 0x0000052C) Transmit address select                                    */
  __IOM uint32_t  RXADDRESSES;                  /*!< (@ 0x00000530) Receive address select                                     */
  __IOM uint32_t  CRCCNF;                       /*!< (@ 0x00000534) CRC configuration                                          */
  __IOM uint32_t  CRCPOLY;                      /*!< (@ 0x00000538) CRC polynomial                                             */
  __IOM uint32_t  CRCINIT;                      /*!< (@ 0x0000053C) CRC initial value                                          */
  __IM  uint32_t  RESERVED16;
  __IOM uint32_t  TIFS;                         /*!< (@ 0x00000544) Interframe spacing in us                                   */
  __IM  uint32_t  RSSISAMPLE;                   /*!< (@ 0x00000548) RSSI sample                                                */
  __IM  uint32_t  RESERVED17;
  __IM  uint32_t  STATE;                        /*!< (@ 0x00000550) Current radio state                                        */
  __IOM uint32_t  DATAWHITEIV;                  /*!< (@ 0x00000554) Data whitening initial value                               */
  __IM  uint32_t  RESERVED18[2];
  __IOM uint32_t  BCC;                          /*!< (@ 0x00000560) Bit counter compare                                        */
  __IM  uint32_t  RESERVED19[39];
  __IOM uint32_t  DAB[8];                       /*!< (@ 0x00000600) Description collection: Device address base segment
                                                                    n                                                          */
  __IOM uint32_t  DAP[8];                       /*!< (@ 0x00000620) Description collection: Device address prefix
                                                                    n                                                          */
  __IOM uint32_t  DACNF;                        /*!< (@ 0x00000640) Device address match configuration                         */
  __IOM uint32_t  MHRMATCHCONF;                 /*!< (@ 0x00000644) Search pattern configuration                               */
  __IOM uint32_t  MHRMATCHMAS;                  /*!< (@ 0x00000648) Pattern mask                                               */
  __IM  uint32_t  RESERVED20;
  __IOM uint32_t  MODECNF0;                     /*!< (@ 0x00000650) Radio mode configuration register 0                        */
  __IM  uint32_t  RESERVED21[3];
  __IOM uint32_t  SFD;                          /*!< (@ 0x00000660) IEEE 802.15.4 start of frame delimiter                     */
  __IOM uint32_t  EDCNT;                        /*!< (@ 0x00000664) IEEE 802.15.4 energy detect loop count                     */
  __IM  uint32_t  EDSAMPLE;                     /*!< (@ 0x00000668) IEEE 802.15.4 energy detect level                          */
  __IOM uint32_t  CCACTRL;                      /*!< (@ 0x0000066C) IEEE 802.15.4 clear channel assessment control             */
  __IM  uint32_t  RESERVED22[164];
  __IOM uint32_t  DFEMODE;                      /*!< (@ 0x00000900) Whether to use Angle-of-Arrival (AOA) or Angle-of-Departure
                                                                    (AOD)                                                      */
  __IOM uint32_t  CTEINLINECONF;                /*!< (@ 0x00000904) Configuration for CTE inline mode                          */
  __IM  uint32_t  RESERVED23[2];
  __IOM uint32_t  DFECTRL1;                     /*!< (@ 0x00000910) Various configuration for Direction finding                */
  __IOM uint32_t  DFECTRL2;                     /*!< (@ 0x00000914) Start offset for Direction finding                         */
  __IM  uint32_t  RESERVED24[4];
  __IOM uint32_t  SWITCHPATTERN;                /*!< (@ 0x00000928) GPIO patterns to be used for each antenna                  */
  __IOM uint32_t  CLEARPATTERN;                 /*!< (@ 0x0000092C) Clear the GPIO pattern array for antenna control           */
  __IOM RADIO_PSEL_Type PSEL;                   /*!< (@ 0x00000930) Unspecified                                                */
  __IOM RADIO_DFEPACKET_Type DFEPACKET;         /*!< (@ 0x00000950) DFE packet EasyDMA channel                                 */
  __IM  uint32_t  RESERVED25[424];
  __IOM uint32_t  POWER;                        /*!< (@ 0x00000FFC) Peripheral power control                                   */
} NRF_RADIO_Type;                               /*!< Size = 4096 (0x1000)                                                      */



/* =========================================================================================================================== */
/* ================                                          RNG_NS                                           ================ */
/* =========================================================================================================================== */


/**
  * @brief Random Number Generator (RNG_NS)
  */

typedef struct {                                /*!< (@ 0x41009000) RNG_NS Structure                                           */
  __OM  uint32_t  TASKS_START;                  /*!< (@ 0x00000000) Task starting the random number generator                  */
  __OM  uint32_t  TASKS_STOP;                   /*!< (@ 0x00000004) Task stopping the random number generator                  */
  __IM  uint32_t  RESERVED[30];
  __IOM uint32_t  SUBSCRIBE_START;              /*!< (@ 0x00000080) Subscribe configuration for task START                     */
  __IOM uint32_t  SUBSCRIBE_STOP;               /*!< (@ 0x00000084) Subscribe configuration for task STOP                      */
  __IM  uint32_t  RESERVED1[30];
  __IOM uint32_t  EVENTS_VALRDY;                /*!< (@ 0x00000100) Event being generated for every new random number
                                                                    written to the VALUE register                              */
  __IM  uint32_t  RESERVED2[31];
  __IOM uint32_t  PUBLISH_VALRDY;               /*!< (@ 0x00000180) Publish configuration for event VALRDY                     */
  __IM  uint32_t  RESERVED3[31];
  __IOM uint32_t  SHORTS;                       /*!< (@ 0x00000200) Shortcuts between local events and tasks                   */
  __IM  uint32_t  RESERVED4[64];
  __IOM uint32_t  INTENSET;                     /*!< (@ 0x00000304) Enable interrupt                                           */
  __IOM uint32_t  INTENCLR;                     /*!< (@ 0x00000308) Disable interrupt                                          */
  __IM  uint32_t  RESERVED5[126];
  __IOM uint32_t  CONFIG;                       /*!< (@ 0x00000504) Configuration register                                     */
  __IM  uint32_t  VALUE;                        /*!< (@ 0x00000508) Output random number                                       */
} NRF_RNG_Type;                                 /*!< Size = 1292 (0x50c)                                                       */



/* =========================================================================================================================== */
/* ================                                         GPIOTE_NS                                         ================ */
/* =========================================================================================================================== */


/**
  * @brief GPIO Tasks and Events (GPIOTE_NS)
  */

typedef struct {                                /*!< (@ 0x4100A000) GPIOTE_NS Structure                                        */
  __OM  uint32_t  TASKS_OUT[8];                 /*!< (@ 0x00000000) Description collection: Task for writing to pin
                                                                    specified in CONFIG[n].PSEL. Action on pin
                                                                    is configured in CONFIG[n].POLARITY.                       */
  __IM  uint32_t  RESERVED[4];
  __OM  uint32_t  TASKS_SET[8];                 /*!< (@ 0x00000030) Description collection: Task for writing to pin
                                                                    specified in CONFIG[n].PSEL. Action on pin
                                                                    is to set it high.                                         */
  __IM  uint32_t  RESERVED1[4];
  __OM  uint32_t  TASKS_CLR[8];                 /*!< (@ 0x00000060) Description collection: Task for writing to pin
                                                                    specified in CONFIG[n].PSEL. Action on pin
                                                                    is to set it low.                                          */
  __IOM uint32_t  SUBSCRIBE_OUT[8];             /*!< (@ 0x00000080) Description collection: Subscribe configuration
                                                                    for task OUT[n]                                            */
  __IM  uint32_t  RESERVED2[4];
  __IOM uint32_t  SUBSCRIBE_SET[8];             /*!< (@ 0x000000B0) Description collection: Subscribe configuration
                                                                    for task SET[n]                                            */
  __IM  uint32_t  RESERVED3[4];
  __IOM uint32_t  SUBSCRIBE_CLR[8];             /*!< (@ 0x000000E0) Description collection: Subscribe configuration
                                                                    for task CLR[n]                                            */
  __IOM uint32_t  EVENTS_IN[8];                 /*!< (@ 0x00000100) Description collection: Event generated from
                                                                    pin specified in CONFIG[n].PSEL                            */
  __IM  uint32_t  RESERVED4[23];
  __IOM uint32_t  EVENTS_PORT;                  /*!< (@ 0x0000017C) Event generated from multiple input GPIO pins
                                                                    with SENSE mechanism enabled                               */
  __IOM uint32_t  PUBLISH_IN[8];                /*!< (@ 0x00000180) Description collection: Publish configuration
                                                                    for event IN[n]                                            */
  __IM  uint32_t  RESERVED5[23];
  __IOM uint32_t  PUBLISH_PORT;                 /*!< (@ 0x000001FC) Publish configuration for event PORT                       */
  __IM  uint32_t  RESERVED6[65];
  __IOM uint32_t  INTENSET;                     /*!< (@ 0x00000304) Enable interrupt                                           */
  __IOM uint32_t  INTENCLR;                     /*!< (@ 0x00000308) Disable interrupt                                          */
  __IM  uint32_t  RESERVED7[129];
  __IOM uint32_t  CONFIG[8];                    /*!< (@ 0x00000510) Description collection: Configuration for OUT[n],
                                                                    SET[n], and CLR[n] tasks and IN[n] event                   */
} NRF_GPIOTE_Type;                              /*!< Size = 1328 (0x530)                                                       */



/* =========================================================================================================================== */
/* ================                                          WDT_NS                                           ================ */
/* =========================================================================================================================== */


/**
  * @brief Watchdog Timer (WDT_NS)
  */

typedef struct {                                /*!< (@ 0x4100B000) WDT_NS Structure                                           */
  __OM  uint32_t  TASKS_START;                  /*!< (@ 0x00000000) Start the watchdog                                         */
  __OM  uint32_t  TASKS_STOP;                   /*!< (@ 0x00000004) Stop the watchdog timer.                                   */
  __IM  uint32_t  RESERVED[30];
  __IOM uint32_t  SUBSCRIBE_START;              /*!< (@ 0x00000080) Subscribe configuration for task START                     */
  __IOM uint32_t  SUBSCRIBE_STOP;               /*!< (@ 0x00000084) Subscribe configuration for task STOP                      */
  __IM  uint32_t  RESERVED1[30];
  __IOM uint32_t  EVENTS_TIMEOUT;               /*!< (@ 0x00000100) Watchdog timeout                                           */
  __IOM uint32_t  EVENTS_STOPPED;               /*!< (@ 0x00000104) Watchdog stopped                                           */
  __IM  uint32_t  RESERVED2[30];
  __IOM uint32_t  PUBLISH_TIMEOUT;              /*!< (@ 0x00000180) Publish configuration for event TIMEOUT                    */
  __IOM uint32_t  PUBLISH_STOPPED;              /*!< (@ 0x00000184) Publish configuration for event STOPPED                    */
  __IM  uint32_t  RESERVED3[95];
  __IOM uint32_t  INTENSET;                     /*!< (@ 0x00000304) Enable interrupt                                           */
  __IOM uint32_t  INTENCLR;                     /*!< (@ 0x00000308) Disable interrupt                                          */
  __IM  uint32_t  RESERVED4[6];
  __IOM uint32_t  NMIENSET;                     /*!< (@ 0x00000324) Enable interrupt                                           */
  __IOM uint32_t  NMIENCLR;                     /*!< (@ 0x00000328) Disable interrupt                                          */
  __IM  uint32_t  RESERVED5[53];
  __IM  uint32_t  RUNSTATUS;                    /*!< (@ 0x00000400) Run status                                                 */
  __IM  uint32_t  REQSTATUS;                    /*!< (@ 0x00000404) Request status                                             */
  __IM  uint32_t  RESERVED6[63];
  __IOM uint32_t  CRV;                          /*!< (@ 0x00000504) Counter reload value                                       */
  __IOM uint32_t  RREN;                         /*!< (@ 0x00000508) Enable register for reload request registers               */
  __IOM uint32_t  CONFIG;                       /*!< (@ 0x0000050C) Configuration register                                     */
  __IM  uint32_t  RESERVED7[4];
  __OM  uint32_t  TSEN;                         /*!< (@ 0x00000520) Task Stop Enable                                           */
  __IM  uint32_t  RESERVED8[55];
  __OM  uint32_t  RR[8];                        /*!< (@ 0x00000600) Description collection: Reload request n                   */
} NRF_WDT_Type;                                 /*!< Size = 1568 (0x620)                                                       */



/* =========================================================================================================================== */
/* ================                                         TIMER0_NS                                         ================ */
/* =========================================================================================================================== */


/**
  * @brief Timer/Counter 0 (TIMER0_NS)
  */

typedef struct {                                /*!< (@ 0x4100C000) TIMER0_NS Structure                                        */
  __OM  uint32_t  TASKS_START;                  /*!< (@ 0x00000000) Start Timer                                                */
  __OM  uint32_t  TASKS_STOP;                   /*!< (@ 0x00000004) Stop Timer                                                 */
  __OM  uint32_t  TASKS_COUNT;                  /*!< (@ 0x00000008) Increment Timer (Counter mode only)                        */
  __OM  uint32_t  TASKS_CLEAR;                  /*!< (@ 0x0000000C) Clear time                                                 */
  __OM  uint32_t  TASKS_SHUTDOWN;               /*!< (@ 0x00000010) Deprecated register - Shut down timer                      */
  __IM  uint32_t  RESERVED[11];
  __OM  uint32_t  TASKS_CAPTURE[8];             /*!< (@ 0x00000040) Description collection: Capture Timer value to
                                                                    CC[n] register                                             */
  __IM  uint32_t  RESERVED1[8];
  __IOM uint32_t  SUBSCRIBE_START;              /*!< (@ 0x00000080) Subscribe configuration for task START                     */
  __IOM uint32_t  SUBSCRIBE_STOP;               /*!< (@ 0x00000084) Subscribe configuration for task STOP                      */
  __IOM uint32_t  SUBSCRIBE_COUNT;              /*!< (@ 0x00000088) Subscribe configuration for task COUNT                     */
  __IOM uint32_t  SUBSCRIBE_CLEAR;              /*!< (@ 0x0000008C) Subscribe configuration for task CLEAR                     */
  __IOM uint32_t  SUBSCRIBE_SHUTDOWN;           /*!< (@ 0x00000090) Deprecated register - Subscribe configuration
                                                                    for task SHUTDOWN                                          */
  __IM  uint32_t  RESERVED2[11];
  __IOM uint32_t  SUBSCRIBE_CAPTURE[8];         /*!< (@ 0x000000C0) Description collection: Subscribe configuration
                                                                    for task CAPTURE[n]                                        */
  __IM  uint32_t  RESERVED3[24];
  __IOM uint32_t  EVENTS_COMPARE[8];            /*!< (@ 0x00000140) Description collection: Compare event on CC[n]
                                                                    match                                                      */
  __IM  uint32_t  RESERVED4[24];
  __IOM uint32_t  PUBLISH_COMPARE[8];           /*!< (@ 0x000001C0) Description collection: Publish configuration
                                                                    for event COMPARE[n]                                       */
  __IM  uint32_t  RESERVED5[8];
  __IOM uint32_t  SHORTS;                       /*!< (@ 0x00000200) Shortcuts between local events and tasks                   */
  __IM  uint32_t  RESERVED6[63];
  __IOM uint32_t  INTEN;                        /*!< (@ 0x00000300) Enable or disable interrupt                                */
  __IOM uint32_t  INTENSET;                     /*!< (@ 0x00000304) Enable interrupt                                           */
  __IOM uint32_t  INTENCLR;                     /*!< (@ 0x00000308) Disable interrupt                                          */
  __IM  uint32_t  RESERVED7[126];
  __IOM uint32_t  MODE;                         /*!< (@ 0x00000504) Timer mode selection                                       */
  __IOM uint32_t  BITMODE;                      /*!< (@ 0x00000508) Configure the number of bits used by the TIMER             */
  __IM  uint32_t  RESERVED8;
  __IOM uint32_t  PRESCALER;                    /*!< (@ 0x00000510) Timer prescaler register                                   */
  __IM  uint32_t  RESERVED9[11];
  __IOM uint32_t  CC[8];                        /*!< (@ 0x00000540) Description collection: Capture/Compare register
                                                                    n                                                          */
  __IM  uint32_t  RESERVED10[8];
  __IOM uint32_t  ONESHOTEN[8];                 /*!< (@ 0x00000580) Description collection: Enable one-shot operation
                                                                    for Capture/Compare channel n                              */
} NRF_TIMER_Type;                               /*!< Size = 1440 (0x5a0)                                                       */



/* =========================================================================================================================== */
/* ================                                          ECB_NS                                           ================ */
/* =========================================================================================================================== */


/**
  * @brief AES ECB Mode Encryption (ECB_NS)
  */

typedef struct {                                /*!< (@ 0x4100D000) ECB_NS Structure                                           */
  __OM  uint32_t  TASKS_STARTECB;               /*!< (@ 0x00000000) Start ECB block encrypt                                    */
  __OM  uint32_t  TASKS_STOPECB;                /*!< (@ 0x00000004) Abort a possible executing ECB operation                   */
  __IM  uint32_t  RESERVED[30];
  __IOM uint32_t  SUBSCRIBE_STARTECB;           /*!< (@ 0x00000080) Subscribe configuration for task STARTECB                  */
  __IOM uint32_t  SUBSCRIBE_STOPECB;            /*!< (@ 0x00000084) Subscribe configuration for task STOPECB                   */
  __IM  uint32_t  RESERVED1[30];
  __IOM uint32_t  EVENTS_ENDECB;                /*!< (@ 0x00000100) ECB block encrypt complete                                 */
  __IOM uint32_t  EVENTS_ERRORECB;              /*!< (@ 0x00000104) ECB block encrypt aborted because of a STOPECB
                                                                    task or due to an error                                    */
  __IM  uint32_t  RESERVED2[30];
  __IOM uint32_t  PUBLISH_ENDECB;               /*!< (@ 0x00000180) Publish configuration for event ENDECB                     */
  __IOM uint32_t  PUBLISH_ERRORECB;             /*!< (@ 0x00000184) Publish configuration for event ERRORECB                   */
  __IM  uint32_t  RESERVED3[95];
  __IOM uint32_t  INTENSET;                     /*!< (@ 0x00000304) Enable interrupt                                           */
  __IOM uint32_t  INTENCLR;                     /*!< (@ 0x00000308) Disable interrupt                                          */
  __IM  uint32_t  RESERVED4[126];
  __IOM uint32_t  ECBDATAPTR;                   /*!< (@ 0x00000504) ECB block encrypt memory pointers                          */
} NRF_ECB_Type;                                 /*!< Size = 1288 (0x508)                                                       */



/* =========================================================================================================================== */
/* ================                                          AAR_NS                                           ================ */
/* =========================================================================================================================== */


/**
  * @brief Accelerated Address Resolver (AAR_NS)
  */

typedef struct {                                /*!< (@ 0x4100E000) AAR_NS Structure                                           */
  __OM  uint32_t  TASKS_START;                  /*!< (@ 0x00000000) Start resolving addresses based on IRKs specified
                                                                    in the IRK data structure                                  */
  __IM  uint32_t  RESERVED;
  __OM  uint32_t  TASKS_STOP;                   /*!< (@ 0x00000008) Stop resolving addresses                                   */
  __IM  uint32_t  RESERVED1[29];
  __IOM uint32_t  SUBSCRIBE_START;              /*!< (@ 0x00000080) Subscribe configuration for task START                     */
  __IM  uint32_t  RESERVED2;
  __IOM uint32_t  SUBSCRIBE_STOP;               /*!< (@ 0x00000088) Subscribe configuration for task STOP                      */
  __IM  uint32_t  RESERVED3[29];
  __IOM uint32_t  EVENTS_END;                   /*!< (@ 0x00000100) Address resolution procedure complete                      */
  __IOM uint32_t  EVENTS_RESOLVED;              /*!< (@ 0x00000104) Address resolved                                           */
  __IOM uint32_t  EVENTS_NOTRESOLVED;           /*!< (@ 0x00000108) Address not resolved                                       */
  __IM  uint32_t  RESERVED4[29];
  __IOM uint32_t  PUBLISH_END;                  /*!< (@ 0x00000180) Publish configuration for event END                        */
  __IOM uint32_t  PUBLISH_RESOLVED;             /*!< (@ 0x00000184) Publish configuration for event RESOLVED                   */
  __IOM uint32_t  PUBLISH_NOTRESOLVED;          /*!< (@ 0x00000188) Publish configuration for event NOTRESOLVED                */
  __IM  uint32_t  RESERVED5[94];
  __IOM uint32_t  INTENSET;                     /*!< (@ 0x00000304) Enable interrupt                                           */
  __IOM uint32_t  INTENCLR;                     /*!< (@ 0x00000308) Disable interrupt                                          */
  __IM  uint32_t  RESERVED6[61];
  __IM  uint32_t  STATUS;                       /*!< (@ 0x00000400) Resolution status                                          */
  __IM  uint32_t  RESERVED7[63];
  __IOM uint32_t  ENABLE;                       /*!< (@ 0x00000500) Enable AAR                                                 */
  __IOM uint32_t  NIRK;                         /*!< (@ 0x00000504) Number of IRKs                                             */
  __IOM uint32_t  IRKPTR;                       /*!< (@ 0x00000508) Pointer to IRK data structure                              */
  __IM  uint32_t  RESERVED8;
  __IOM uint32_t  ADDRPTR;                      /*!< (@ 0x00000510) Pointer to the resolvable address                          */
  __IOM uint32_t  SCRATCHPTR;                   /*!< (@ 0x00000514) Pointer to data area used for temporary storage            */
} NRF_AAR_Type;                                 /*!< Size = 1304 (0x518)                                                       */



/* =========================================================================================================================== */
/* ================                                          CCM_NS                                           ================ */
/* =========================================================================================================================== */


/**
  * @brief AES CCM mode encryption (CCM_NS)
  */

typedef struct {                                /*!< (@ 0x4100E000) CCM_NS Structure                                           */
  __OM  uint32_t  TASKS_KSGEN;                  /*!< (@ 0x00000000) Start generation of keystream. This operation
                                                                    will stop by itself when completed.                        */
  __OM  uint32_t  TASKS_CRYPT;                  /*!< (@ 0x00000004) Start encryption/decryption. This operation will
                                                                    stop by itself when completed.                             */
  __OM  uint32_t  TASKS_STOP;                   /*!< (@ 0x00000008) Stop encryption/decryption                                 */
  __OM  uint32_t  TASKS_RATEOVERRIDE;           /*!< (@ 0x0000000C) Override DATARATE setting in MODE register with
                                                                    the contents of the RATEOVERRIDE register
                                                                    for any ongoing encryption/decryption                      */
  __IM  uint32_t  RESERVED[28];
  __IOM uint32_t  SUBSCRIBE_KSGEN;              /*!< (@ 0x00000080) Subscribe configuration for task KSGEN                     */
  __IOM uint32_t  SUBSCRIBE_CRYPT;              /*!< (@ 0x00000084) Subscribe configuration for task CRYPT                     */
  __IOM uint32_t  SUBSCRIBE_STOP;               /*!< (@ 0x00000088) Subscribe configuration for task STOP                      */
  __IOM uint32_t  SUBSCRIBE_RATEOVERRIDE;       /*!< (@ 0x0000008C) Subscribe configuration for task RATEOVERRIDE              */
  __IM  uint32_t  RESERVED1[28];
  __IOM uint32_t  EVENTS_ENDKSGEN;              /*!< (@ 0x00000100) Keystream generation complete                              */
  __IOM uint32_t  EVENTS_ENDCRYPT;              /*!< (@ 0x00000104) Encrypt/decrypt complete                                   */
  __IOM uint32_t  EVENTS_ERROR;                 /*!< (@ 0x00000108) Deprecated register - CCM error event                      */
  __IM  uint32_t  RESERVED2[29];
  __IOM uint32_t  PUBLISH_ENDKSGEN;             /*!< (@ 0x00000180) Publish configuration for event ENDKSGEN                   */
  __IOM uint32_t  PUBLISH_ENDCRYPT;             /*!< (@ 0x00000184) Publish configuration for event ENDCRYPT                   */
  __IOM uint32_t  PUBLISH_ERROR;                /*!< (@ 0x00000188) Deprecated register - Publish configuration for
                                                                    event ERROR                                                */
  __IM  uint32_t  RESERVED3[29];
  __IOM uint32_t  SHORTS;                       /*!< (@ 0x00000200) Shortcuts between local events and tasks                   */
  __IM  uint32_t  RESERVED4[64];
  __IOM uint32_t  INTENSET;                     /*!< (@ 0x00000304) Enable interrupt                                           */
  __IOM uint32_t  INTENCLR;                     /*!< (@ 0x00000308) Disable interrupt                                          */
  __IM  uint32_t  RESERVED5[61];
  __IM  uint32_t  MICSTATUS;                    /*!< (@ 0x00000400) MIC check result                                           */
  __IM  uint32_t  RESERVED6[63];
  __IOM uint32_t  ENABLE;                       /*!< (@ 0x00000500) Enable                                                     */
  __IOM uint32_t  MODE;                         /*!< (@ 0x00000504) Operation mode                                             */
  __IOM uint32_t  CNFPTR;                       /*!< (@ 0x00000508) Pointer to data structure holding the AES key
                                                                    and the NONCE vector                                       */
  __IOM uint32_t  INPTR;                        /*!< (@ 0x0000050C) Input pointer                                              */
  __IOM uint32_t  OUTPTR;                       /*!< (@ 0x00000510) Output pointer                                             */
  __IOM uint32_t  SCRATCHPTR;                   /*!< (@ 0x00000514) Pointer to data area used for temporary storage            */
  __IOM uint32_t  MAXPACKETSIZE;                /*!< (@ 0x00000518) Length of keystream generated when MODE.LENGTH
                                                                    = Extended                                                 */
  __IOM uint32_t  RATEOVERRIDE;                 /*!< (@ 0x0000051C) Data rate override setting.                                */
  __IOM uint32_t  HEADERMASK;                   /*!< (@ 0x00000520) Header (S0) mask.                                          */
} NRF_CCM_Type;                                 /*!< Size = 1316 (0x524)                                                       */



/* =========================================================================================================================== */
/* ================                                         DPPIC_NS                                          ================ */
/* =========================================================================================================================== */


/**
  * @brief Distributed programmable peripheral interconnect controller (DPPIC_NS)
  */

typedef struct {                                /*!< (@ 0x4100F000) DPPIC_NS Structure                                         */
  __OM  DPPIC_TASKS_CHG_Type TASKS_CHG[6];      /*!< (@ 0x00000000) Channel group tasks                                        */
  __IM  uint32_t  RESERVED[20];
  __IOM DPPIC_SUBSCRIBE_CHG_Type SUBSCRIBE_CHG[6];/*!< (@ 0x00000080) Subscribe configuration for tasks                        */
  __IM  uint32_t  RESERVED1[276];
  __IOM uint32_t  CHEN;                         /*!< (@ 0x00000500) Channel enable register                                    */
  __IOM uint32_t  CHENSET;                      /*!< (@ 0x00000504) Channel enable set register                                */
  __IOM uint32_t  CHENCLR;                      /*!< (@ 0x00000508) Channel enable clear register                              */
  __IM  uint32_t  RESERVED2[189];
  __IOM uint32_t  CHG[6];                       /*!< (@ 0x00000800) Description collection: Channel group n Note:
                                                                    Writes to this register are ignored if either
                                                                    SUBSCRIBE_CHG[n].EN or SUBSCRIBE_CHG[n].DIS
                                                                    is enabled                                                 */
} NRF_DPPIC_Type;                               /*!< Size = 2072 (0x818)                                                       */



/* =========================================================================================================================== */
/* ================                                          TEMP_NS                                          ================ */
/* =========================================================================================================================== */


/**
  * @brief Temperature Sensor (TEMP_NS)
  */

typedef struct {                                /*!< (@ 0x41010000) TEMP_NS Structure                                          */
  __OM  uint32_t  TASKS_START;                  /*!< (@ 0x00000000) Start temperature measurement                              */
  __OM  uint32_t  TASKS_STOP;                   /*!< (@ 0x00000004) Stop temperature measurement                               */
  __IM  uint32_t  RESERVED[30];
  __IOM uint32_t  SUBSCRIBE_START;              /*!< (@ 0x00000080) Subscribe configuration for task START                     */
  __IOM uint32_t  SUBSCRIBE_STOP;               /*!< (@ 0x00000084) Subscribe configuration for task STOP                      */
  __IM  uint32_t  RESERVED1[30];
  __IOM uint32_t  EVENTS_DATARDY;               /*!< (@ 0x00000100) Temperature measurement complete, data ready               */
  __IM  uint32_t  RESERVED2[31];
  __IOM uint32_t  PUBLISH_DATARDY;              /*!< (@ 0x00000180) Publish configuration for event DATARDY                    */
  __IM  uint32_t  RESERVED3[96];
  __IOM uint32_t  INTENSET;                     /*!< (@ 0x00000304) Enable interrupt                                           */
  __IOM uint32_t  INTENCLR;                     /*!< (@ 0x00000308) Disable interrupt                                          */
  __IM  uint32_t  RESERVED4[127];
  __IM  int32_t   TEMP;                         /*!< (@ 0x00000508) Temperature in degC (0.25deg steps)                        */
  __IM  uint32_t  RESERVED5[5];
  __IOM uint32_t  A0;                           /*!< (@ 0x00000520) Slope of 1st piece wise linear function                    */
  __IOM uint32_t  A1;                           /*!< (@ 0x00000524) Slope of 2nd piece wise linear function                    */
  __IOM uint32_t  A2;                           /*!< (@ 0x00000528) Slope of 3rd piece wise linear function                    */
  __IOM uint32_t  A3;                           /*!< (@ 0x0000052C) Slope of 4th piece wise linear function                    */
  __IOM uint32_t  A4;                           /*!< (@ 0x00000530) Slope of 5th piece wise linear function                    */
  __IOM uint32_t  A5;                           /*!< (@ 0x00000534) Slope of 6th piece wise linear function                    */
  __IM  uint32_t  RESERVED6[2];
  __IOM uint32_t  B0;                           /*!< (@ 0x00000540) y-intercept of 1st piece wise linear function              */
  __IOM uint32_t  B1;                           /*!< (@ 0x00000544) y-intercept of 2nd piece wise linear function              */
  __IOM uint32_t  B2;                           /*!< (@ 0x00000548) y-intercept of 3rd piece wise linear function              */
  __IOM uint32_t  B3;                           /*!< (@ 0x0000054C) y-intercept of 4th piece wise linear function              */
  __IOM uint32_t  B4;                           /*!< (@ 0x00000550) y-intercept of 5th piece wise linear function              */
  __IOM uint32_t  B5;                           /*!< (@ 0x00000554) y-intercept of 6th piece wise linear function              */
  __IM  uint32_t  RESERVED7[2];
  __IOM uint32_t  T0;                           /*!< (@ 0x00000560) End point of 1st piece wise linear function                */
  __IOM uint32_t  T1;                           /*!< (@ 0x00000564) End point of 2nd piece wise linear function                */
  __IOM uint32_t  T2;                           /*!< (@ 0x00000568) End point of 3rd piece wise linear function                */
  __IOM uint32_t  T3;                           /*!< (@ 0x0000056C) End point of 4th piece wise linear function                */
  __IOM uint32_t  T4;                           /*!< (@ 0x00000570) End point of 5th piece wise linear function                */
} NRF_TEMP_Type;                                /*!< Size = 1396 (0x574)                                                       */



/* =========================================================================================================================== */
/* ================                                          RTC0_NS                                          ================ */
/* =========================================================================================================================== */


/**
  * @brief Real-time counter 0 (RTC0_NS)
  */

typedef struct {                                /*!< (@ 0x41011000) RTC0_NS Structure                                          */
  __OM  uint32_t  TASKS_START;                  /*!< (@ 0x00000000) Start RTC counter                                          */
  __OM  uint32_t  TASKS_STOP;                   /*!< (@ 0x00000004) Stop RTC counter                                           */
  __OM  uint32_t  TASKS_CLEAR;                  /*!< (@ 0x00000008) Clear RTC counter                                          */
  __OM  uint32_t  TASKS_TRIGOVRFLW;             /*!< (@ 0x0000000C) Set counter to 0xFFFFF0                                    */
  __IM  uint32_t  RESERVED[12];
  __OM  uint32_t  TASKS_CAPTURE[4];             /*!< (@ 0x00000040) Description collection: Capture RTC counter to
                                                                    CC[n] register                                             */
  __IM  uint32_t  RESERVED1[12];
  __IOM uint32_t  SUBSCRIBE_START;              /*!< (@ 0x00000080) Subscribe configuration for task START                     */
  __IOM uint32_t  SUBSCRIBE_STOP;               /*!< (@ 0x00000084) Subscribe configuration for task STOP                      */
  __IOM uint32_t  SUBSCRIBE_CLEAR;              /*!< (@ 0x00000088) Subscribe configuration for task CLEAR                     */
  __IOM uint32_t  SUBSCRIBE_TRIGOVRFLW;         /*!< (@ 0x0000008C) Subscribe configuration for task TRIGOVRFLW                */
  __IM  uint32_t  RESERVED2[12];
  __IOM uint32_t  SUBSCRIBE_CAPTURE[4];         /*!< (@ 0x000000C0) Description collection: Subscribe configuration
                                                                    for task CAPTURE[n]                                        */
  __IM  uint32_t  RESERVED3[12];
  __IOM uint32_t  EVENTS_TICK;                  /*!< (@ 0x00000100) Event on counter increment                                 */
  __IOM uint32_t  EVENTS_OVRFLW;                /*!< (@ 0x00000104) Event on counter overflow                                  */
  __IM  uint32_t  RESERVED4[14];
  __IOM uint32_t  EVENTS_COMPARE[4];            /*!< (@ 0x00000140) Description collection: Compare event on CC[n]
                                                                    match                                                      */
  __IM  uint32_t  RESERVED5[12];
  __IOM uint32_t  PUBLISH_TICK;                 /*!< (@ 0x00000180) Publish configuration for event TICK                       */
  __IOM uint32_t  PUBLISH_OVRFLW;               /*!< (@ 0x00000184) Publish configuration for event OVRFLW                     */
  __IM  uint32_t  RESERVED6[14];
  __IOM uint32_t  PUBLISH_COMPARE[4];           /*!< (@ 0x000001C0) Description collection: Publish configuration
                                                                    for event COMPARE[n]                                       */
  __IM  uint32_t  RESERVED7[12];
  __IOM uint32_t  SHORTS;                       /*!< (@ 0x00000200) Shortcuts between local events and tasks                   */
  __IM  uint32_t  RESERVED8[64];
  __IOM uint32_t  INTENSET;                     /*!< (@ 0x00000304) Enable interrupt                                           */
  __IOM uint32_t  INTENCLR;                     /*!< (@ 0x00000308) Disable interrupt                                          */
  __IM  uint32_t  RESERVED9[13];
  __IOM uint32_t  EVTEN;                        /*!< (@ 0x00000340) Enable or disable event routing                            */
  __IOM uint32_t  EVTENSET;                     /*!< (@ 0x00000344) Enable event routing                                       */
  __IOM uint32_t  EVTENCLR;                     /*!< (@ 0x00000348) Disable event routing                                      */
  __IM  uint32_t  RESERVED10[110];
  __IM  uint32_t  COUNTER;                      /*!< (@ 0x00000504) Current counter value                                      */
  __IOM uint32_t  PRESCALER;                    /*!< (@ 0x00000508) 12-bit prescaler for counter frequency (32768/(PRESCALER+1)).
                                                                    Must be written when RTC is stopped.                       */
  __IM  uint32_t  RESERVED11[13];
  __IOM uint32_t  CC[4];                        /*!< (@ 0x00000540) Description collection: Compare register n                 */
} NRF_RTC_Type;                                 /*!< Size = 1360 (0x550)                                                       */



/* =========================================================================================================================== */
/* ================                                          IPC_NS                                           ================ */
/* =========================================================================================================================== */


/**
  * @brief Interprocessor communication (IPC_NS)
  */

typedef struct {                                /*!< (@ 0x41012000) IPC_NS Structure                                           */
  __OM  uint32_t  TASKS_SEND[16];               /*!< (@ 0x00000000) Description collection: Trigger events on IPC
                                                                    channel enabled in SEND_CNF[n]                             */
  __IM  uint32_t  RESERVED[16];
  __IOM uint32_t  SUBSCRIBE_SEND[16];           /*!< (@ 0x00000080) Description collection: Subscribe configuration
                                                                    for task SEND[n]                                           */
  __IM  uint32_t  RESERVED1[16];
  __IOM uint32_t  EVENTS_RECEIVE[16];           /*!< (@ 0x00000100) Description collection: Event received on one
                                                                    or more of the enabled IPC channels in RECEIVE_CNF[n]      */
  __IM  uint32_t  RESERVED2[16];
  __IOM uint32_t  PUBLISH_RECEIVE[16];          /*!< (@ 0x00000180) Description collection: Publish configuration
                                                                    for event RECEIVE[n]                                       */
  __IM  uint32_t  RESERVED3[80];
  __IOM uint32_t  INTEN;                        /*!< (@ 0x00000300) Enable or disable interrupt                                */
  __IOM uint32_t  INTENSET;                     /*!< (@ 0x00000304) Enable interrupt                                           */
  __IOM uint32_t  INTENCLR;                     /*!< (@ 0x00000308) Disable interrupt                                          */
  __IM  uint32_t  INTPEND;                      /*!< (@ 0x0000030C) Pending interrupts                                         */
  __IM  uint32_t  RESERVED4[128];
  __IOM uint32_t  SEND_CNF[16];                 /*!< (@ 0x00000510) Description collection: Send event configuration
                                                                    for TASKS_SEND[n]                                          */
  __IM  uint32_t  RESERVED5[16];
  __IOM uint32_t  RECEIVE_CNF[16];              /*!< (@ 0x00000590) Description collection: Receive event configuration
                                                                    for EVENTS_RECEIVE[n]                                      */
  __IM  uint32_t  RESERVED6[16];
  __IOM uint32_t  GPMEM[2];                     /*!< (@ 0x00000610) Description collection: General purpose memory             */
} NRF_IPC_Type;                                 /*!< Size = 1560 (0x618)                                                       */



/* =========================================================================================================================== */
/* ================                                         SPIM0_NS                                          ================ */
/* =========================================================================================================================== */


/**
  * @brief Serial Peripheral Interface Master with EasyDMA (SPIM0_NS)
  */

typedef struct {                                /*!< (@ 0x41013000) SPIM0_NS Structure                                         */
  __IM  uint32_t  RESERVED[4];
  __OM  uint32_t  TASKS_START;                  /*!< (@ 0x00000010) Start SPI transaction                                      */
  __OM  uint32_t  TASKS_STOP;                   /*!< (@ 0x00000014) Stop SPI transaction                                       */
  __IM  uint32_t  RESERVED1;
  __OM  uint32_t  TASKS_SUSPEND;                /*!< (@ 0x0000001C) Suspend SPI transaction                                    */
  __OM  uint32_t  TASKS_RESUME;                 /*!< (@ 0x00000020) Resume SPI transaction                                     */
  __IM  uint32_t  RESERVED2[27];
  __IOM uint32_t  SUBSCRIBE_START;              /*!< (@ 0x00000090) Subscribe configuration for task START                     */
  __IOM uint32_t  SUBSCRIBE_STOP;               /*!< (@ 0x00000094) Subscribe configuration for task STOP                      */
  __IM  uint32_t  RESERVED3;
  __IOM uint32_t  SUBSCRIBE_SUSPEND;            /*!< (@ 0x0000009C) Subscribe configuration for task SUSPEND                   */
  __IOM uint32_t  SUBSCRIBE_RESUME;             /*!< (@ 0x000000A0) Subscribe configuration for task RESUME                    */
  __IM  uint32_t  RESERVED4[24];
  __IOM uint32_t  EVENTS_STOPPED;               /*!< (@ 0x00000104) SPI transaction has stopped                                */
  __IM  uint32_t  RESERVED5[2];
  __IOM uint32_t  EVENTS_ENDRX;                 /*!< (@ 0x00000110) End of RXD buffer reached                                  */
  __IM  uint32_t  RESERVED6;
  __IOM uint32_t  EVENTS_END;                   /*!< (@ 0x00000118) End of RXD buffer and TXD buffer reached                   */
  __IM  uint32_t  RESERVED7;
  __IOM uint32_t  EVENTS_ENDTX;                 /*!< (@ 0x00000120) End of TXD buffer reached                                  */
  __IM  uint32_t  RESERVED8[10];
  __IOM uint32_t  EVENTS_STARTED;               /*!< (@ 0x0000014C) Transaction started                                        */
  __IM  uint32_t  RESERVED9[13];
  __IOM uint32_t  PUBLISH_STOPPED;              /*!< (@ 0x00000184) Publish configuration for event STOPPED                    */
  __IM  uint32_t  RESERVED10[2];
  __IOM uint32_t  PUBLISH_ENDRX;                /*!< (@ 0x00000190) Publish configuration for event ENDRX                      */
  __IM  uint32_t  RESERVED11;
  __IOM uint32_t  PUBLISH_END;                  /*!< (@ 0x00000198) Publish configuration for event END                        */
  __IM  uint32_t  RESERVED12;
  __IOM uint32_t  PUBLISH_ENDTX;                /*!< (@ 0x000001A0) Publish configuration for event ENDTX                      */
  __IM  uint32_t  RESERVED13[10];
  __IOM uint32_t  PUBLISH_STARTED;              /*!< (@ 0x000001CC) Publish configuration for event STARTED                    */
  __IM  uint32_t  RESERVED14[12];
  __IOM uint32_t  SHORTS;                       /*!< (@ 0x00000200) Shortcuts between local events and tasks                   */
  __IM  uint32_t  RESERVED15[64];
  __IOM uint32_t  INTENSET;                     /*!< (@ 0x00000304) Enable interrupt                                           */
  __IOM uint32_t  INTENCLR;                     /*!< (@ 0x00000308) Disable interrupt                                          */
  __IM  uint32_t  RESERVED16[61];
  __IOM uint32_t  STALLSTAT;                    /*!< (@ 0x00000400) Stall status for EasyDMA RAM accesses. The fields
                                                                    in this register is set to STALL by hardware
                                                                    whenever a stall occurres and can be cleared
                                                                    (set to NOSTALL) by the CPU.                               */
  __IM  uint32_t  RESERVED17[63];
  __IOM uint32_t  ENABLE;                       /*!< (@ 0x00000500) Enable SPIM                                                */
  __IM  uint32_t  RESERVED18;
  __IOM SPIM_PSEL_Type PSEL;                    /*!< (@ 0x00000508) Unspecified                                                */
  __IM  uint32_t  RESERVED19[3];
  __IOM uint32_t  FREQUENCY;                    /*!< (@ 0x00000524) SPI frequency. Accuracy depends on the HFCLK
                                                                    source selected.                                           */
  __IM  uint32_t  RESERVED20[3];
  __IOM SPIM_RXD_Type RXD;                      /*!< (@ 0x00000534) RXD EasyDMA channel                                        */
  __IOM SPIM_TXD_Type TXD;                      /*!< (@ 0x00000544) TXD EasyDMA channel                                        */
  __IOM uint32_t  CONFIG;                       /*!< (@ 0x00000554) Configuration register                                     */
  __IM  uint32_t  RESERVED21[2];
  __IOM SPIM_IFTIMING_Type IFTIMING;            /*!< (@ 0x00000560) Unspecified                                                */
  __IOM uint32_t  CSNPOL;                       /*!< (@ 0x00000568) Polarity of CSN output                                     */
  __IOM uint32_t  PSELDCX;                      /*!< (@ 0x0000056C) Pin select for DCX signal                                  */
  __IOM uint32_t  DCXCNT;                       /*!< (@ 0x00000570) DCX configuration                                          */
  __IM  uint32_t  RESERVED22[19];
  __IOM uint32_t  ORC;                          /*!< (@ 0x000005C0) Byte transmitted after TXD.MAXCNT bytes have
                                                                    been transmitted in the case when RXD.MAXCNT
                                                                    is greater than TXD.MAXCNT                                 */
} NRF_SPIM_Type;                                /*!< Size = 1476 (0x5c4)                                                       */



/* =========================================================================================================================== */
/* ================                                         SPIS0_NS                                          ================ */
/* =========================================================================================================================== */


/**
  * @brief SPI Slave (SPIS0_NS)
  */

typedef struct {                                /*!< (@ 0x41013000) SPIS0_NS Structure                                         */
  __IM  uint32_t  RESERVED[9];
  __OM  uint32_t  TASKS_ACQUIRE;                /*!< (@ 0x00000024) Acquire SPI semaphore                                      */
  __OM  uint32_t  TASKS_RELEASE;                /*!< (@ 0x00000028) Release SPI semaphore, enabling the SPI slave
                                                                    to acquire it                                              */
  __IM  uint32_t  RESERVED1[30];
  __IOM uint32_t  SUBSCRIBE_ACQUIRE;            /*!< (@ 0x000000A4) Subscribe configuration for task ACQUIRE                   */
  __IOM uint32_t  SUBSCRIBE_RELEASE;            /*!< (@ 0x000000A8) Subscribe configuration for task RELEASE                   */
  __IM  uint32_t  RESERVED2[22];
  __IOM uint32_t  EVENTS_END;                   /*!< (@ 0x00000104) Granted transaction completed                              */
  __IM  uint32_t  RESERVED3[2];
  __IOM uint32_t  EVENTS_ENDRX;                 /*!< (@ 0x00000110) End of RXD buffer reached                                  */
  __IM  uint32_t  RESERVED4[5];
  __IOM uint32_t  EVENTS_ACQUIRED;              /*!< (@ 0x00000128) Semaphore acquired                                         */
  __IM  uint32_t  RESERVED5[22];
  __IOM uint32_t  PUBLISH_END;                  /*!< (@ 0x00000184) Publish configuration for event END                        */
  __IM  uint32_t  RESERVED6[2];
  __IOM uint32_t  PUBLISH_ENDRX;                /*!< (@ 0x00000190) Publish configuration for event ENDRX                      */
  __IM  uint32_t  RESERVED7[5];
  __IOM uint32_t  PUBLISH_ACQUIRED;             /*!< (@ 0x000001A8) Publish configuration for event ACQUIRED                   */
  __IM  uint32_t  RESERVED8[21];
  __IOM uint32_t  SHORTS;                       /*!< (@ 0x00000200) Shortcuts between local events and tasks                   */
  __IM  uint32_t  RESERVED9[64];
  __IOM uint32_t  INTENSET;                     /*!< (@ 0x00000304) Enable interrupt                                           */
  __IOM uint32_t  INTENCLR;                     /*!< (@ 0x00000308) Disable interrupt                                          */
  __IM  uint32_t  RESERVED10[61];
  __IM  uint32_t  SEMSTAT;                      /*!< (@ 0x00000400) Semaphore status register                                  */
  __IM  uint32_t  RESERVED11[15];
  __IOM uint32_t  STATUS;                       /*!< (@ 0x00000440) Status from last transaction                               */
  __IM  uint32_t  RESERVED12[47];
  __IOM uint32_t  ENABLE;                       /*!< (@ 0x00000500) Enable SPI slave                                           */
  __IM  uint32_t  RESERVED13;
  __IOM SPIS_PSEL_Type PSEL;                    /*!< (@ 0x00000508) Unspecified                                                */
  __IM  uint32_t  RESERVED14[7];
  __IOM SPIS_RXD_Type RXD;                      /*!< (@ 0x00000534) Unspecified                                                */
  __IOM SPIS_TXD_Type TXD;                      /*!< (@ 0x00000544) Unspecified                                                */
  __IOM uint32_t  CONFIG;                       /*!< (@ 0x00000554) Configuration register                                     */
  __IM  uint32_t  RESERVED15;
  __IOM uint32_t  DEF;                          /*!< (@ 0x0000055C) Default character. Character clocked out in case
                                                                    of an ignored transaction.                                 */
  __IM  uint32_t  RESERVED16[24];
  __IOM uint32_t  ORC;                          /*!< (@ 0x000005C0) Over-read character                                        */
} NRF_SPIS_Type;                                /*!< Size = 1476 (0x5c4)                                                       */



/* =========================================================================================================================== */
/* ================                                         TWIM0_NS                                          ================ */
/* =========================================================================================================================== */


/**
  * @brief I2C compatible Two-Wire Master Interface with EasyDMA (TWIM0_NS)
  */

typedef struct {                                /*!< (@ 0x41013000) TWIM0_NS Structure                                         */
  __OM  uint32_t  TASKS_STARTRX;                /*!< (@ 0x00000000) Start TWI receive sequence                                 */
  __IM  uint32_t  RESERVED;
  __OM  uint32_t  TASKS_STARTTX;                /*!< (@ 0x00000008) Start TWI transmit sequence                                */
  __IM  uint32_t  RESERVED1[2];
  __OM  uint32_t  TASKS_STOP;                   /*!< (@ 0x00000014) Stop TWI transaction. Must be issued while the
                                                                    TWI master is not suspended.                               */
  __IM  uint32_t  RESERVED2;
  __OM  uint32_t  TASKS_SUSPEND;                /*!< (@ 0x0000001C) Suspend TWI transaction                                    */
  __OM  uint32_t  TASKS_RESUME;                 /*!< (@ 0x00000020) Resume TWI transaction                                     */
  __IM  uint32_t  RESERVED3[23];
  __IOM uint32_t  SUBSCRIBE_STARTRX;            /*!< (@ 0x00000080) Subscribe configuration for task STARTRX                   */
  __IM  uint32_t  RESERVED4;
  __IOM uint32_t  SUBSCRIBE_STARTTX;            /*!< (@ 0x00000088) Subscribe configuration for task STARTTX                   */
  __IM  uint32_t  RESERVED5[2];
  __IOM uint32_t  SUBSCRIBE_STOP;               /*!< (@ 0x00000094) Subscribe configuration for task STOP                      */
  __IM  uint32_t  RESERVED6;
  __IOM uint32_t  SUBSCRIBE_SUSPEND;            /*!< (@ 0x0000009C) Subscribe configuration for task SUSPEND                   */
  __IOM uint32_t  SUBSCRIBE_RESUME;             /*!< (@ 0x000000A0) Subscribe configuration for task RESUME                    */
  __IM  uint32_t  RESERVED7[24];
  __IOM uint32_t  EVENTS_STOPPED;               /*!< (@ 0x00000104) TWI stopped                                                */
  __IM  uint32_t  RESERVED8[7];
  __IOM uint32_t  EVENTS_ERROR;                 /*!< (@ 0x00000124) TWI error                                                  */
  __IM  uint32_t  RESERVED9[8];
  __IOM uint32_t  EVENTS_SUSPENDED;             /*!< (@ 0x00000148) Last byte has been sent out after the SUSPEND
                                                                    task has been issued, TWI traffic is now
                                                                    suspended.                                                 */
  __IOM uint32_t  EVENTS_RXSTARTED;             /*!< (@ 0x0000014C) Receive sequence started                                   */
  __IOM uint32_t  EVENTS_TXSTARTED;             /*!< (@ 0x00000150) Transmit sequence started                                  */
  __IM  uint32_t  RESERVED10[2];
  __IOM uint32_t  EVENTS_LASTRX;                /*!< (@ 0x0000015C) Byte boundary, starting to receive the last byte           */
  __IOM uint32_t  EVENTS_LASTTX;                /*!< (@ 0x00000160) Byte boundary, starting to transmit the last
                                                                    byte                                                       */
  __IM  uint32_t  RESERVED11[8];
  __IOM uint32_t  PUBLISH_STOPPED;              /*!< (@ 0x00000184) Publish configuration for event STOPPED                    */
  __IM  uint32_t  RESERVED12[7];
  __IOM uint32_t  PUBLISH_ERROR;                /*!< (@ 0x000001A4) Publish configuration for event ERROR                      */
  __IM  uint32_t  RESERVED13[8];
  __IOM uint32_t  PUBLISH_SUSPENDED;            /*!< (@ 0x000001C8) Publish configuration for event SUSPENDED                  */
  __IOM uint32_t  PUBLISH_RXSTARTED;            /*!< (@ 0x000001CC) Publish configuration for event RXSTARTED                  */
  __IOM uint32_t  PUBLISH_TXSTARTED;            /*!< (@ 0x000001D0) Publish configuration for event TXSTARTED                  */
  __IM  uint32_t  RESERVED14[2];
  __IOM uint32_t  PUBLISH_LASTRX;               /*!< (@ 0x000001DC) Publish configuration for event LASTRX                     */
  __IOM uint32_t  PUBLISH_LASTTX;               /*!< (@ 0x000001E0) Publish configuration for event LASTTX                     */
  __IM  uint32_t  RESERVED15[7];
  __IOM uint32_t  SHORTS;                       /*!< (@ 0x00000200) Shortcuts between local events and tasks                   */
  __IM  uint32_t  RESERVED16[63];
  __IOM uint32_t  INTEN;                        /*!< (@ 0x00000300) Enable or disable interrupt                                */
  __IOM uint32_t  INTENSET;                     /*!< (@ 0x00000304) Enable interrupt                                           */
  __IOM uint32_t  INTENCLR;                     /*!< (@ 0x00000308) Disable interrupt                                          */
  __IM  uint32_t  RESERVED17[110];
  __IOM uint32_t  ERRORSRC;                     /*!< (@ 0x000004C4) Error source                                               */
  __IM  uint32_t  RESERVED18[14];
  __IOM uint32_t  ENABLE;                       /*!< (@ 0x00000500) Enable TWIM                                                */
  __IM  uint32_t  RESERVED19;
  __IOM TWIM_PSEL_Type PSEL;                    /*!< (@ 0x00000508) Unspecified                                                */
  __IM  uint32_t  RESERVED20[5];
  __IOM uint32_t  FREQUENCY;                    /*!< (@ 0x00000524) TWI frequency. Accuracy depends on the HFCLK
                                                                    source selected.                                           */
  __IM  uint32_t  RESERVED21[3];
  __IOM TWIM_RXD_Type RXD;                      /*!< (@ 0x00000534) RXD EasyDMA channel                                        */
  __IOM TWIM_TXD_Type TXD;                      /*!< (@ 0x00000544) TXD EasyDMA channel                                        */
  __IM  uint32_t  RESERVED22[13];
  __IOM uint32_t  ADDRESS;                      /*!< (@ 0x00000588) Address used in the TWI transfer                           */
} NRF_TWIM_Type;                                /*!< Size = 1420 (0x58c)                                                       */



/* =========================================================================================================================== */
/* ================                                         TWIS0_NS                                          ================ */
/* =========================================================================================================================== */


/**
  * @brief I2C compatible Two-Wire Slave Interface with EasyDMA (TWIS0_NS)
  */

typedef struct {                                /*!< (@ 0x41013000) TWIS0_NS Structure                                         */
  __IM  uint32_t  RESERVED[5];
  __OM  uint32_t  TASKS_STOP;                   /*!< (@ 0x00000014) Stop TWI transaction                                       */
  __IM  uint32_t  RESERVED1;
  __OM  uint32_t  TASKS_SUSPEND;                /*!< (@ 0x0000001C) Suspend TWI transaction                                    */
  __OM  uint32_t  TASKS_RESUME;                 /*!< (@ 0x00000020) Resume TWI transaction                                     */
  __IM  uint32_t  RESERVED2[3];
  __OM  uint32_t  TASKS_PREPARERX;              /*!< (@ 0x00000030) Prepare the TWI slave to respond to a write command        */
  __OM  uint32_t  TASKS_PREPARETX;              /*!< (@ 0x00000034) Prepare the TWI slave to respond to a read command         */
  __IM  uint32_t  RESERVED3[23];
  __IOM uint32_t  SUBSCRIBE_STOP;               /*!< (@ 0x00000094) Subscribe configuration for task STOP                      */
  __IM  uint32_t  RESERVED4;
  __IOM uint32_t  SUBSCRIBE_SUSPEND;            /*!< (@ 0x0000009C) Subscribe configuration for task SUSPEND                   */
  __IOM uint32_t  SUBSCRIBE_RESUME;             /*!< (@ 0x000000A0) Subscribe configuration for task RESUME                    */
  __IM  uint32_t  RESERVED5[3];
  __IOM uint32_t  SUBSCRIBE_PREPARERX;          /*!< (@ 0x000000B0) Subscribe configuration for task PREPARERX                 */
  __IOM uint32_t  SUBSCRIBE_PREPARETX;          /*!< (@ 0x000000B4) Subscribe configuration for task PREPARETX                 */
  __IM  uint32_t  RESERVED6[19];
  __IOM uint32_t  EVENTS_STOPPED;               /*!< (@ 0x00000104) TWI stopped                                                */
  __IM  uint32_t  RESERVED7[7];
  __IOM uint32_t  EVENTS_ERROR;                 /*!< (@ 0x00000124) TWI error                                                  */
  __IM  uint32_t  RESERVED8[9];
  __IOM uint32_t  EVENTS_RXSTARTED;             /*!< (@ 0x0000014C) Receive sequence started                                   */
  __IOM uint32_t  EVENTS_TXSTARTED;             /*!< (@ 0x00000150) Transmit sequence started                                  */
  __IM  uint32_t  RESERVED9[4];
  __IOM uint32_t  EVENTS_WRITE;                 /*!< (@ 0x00000164) Write command received                                     */
  __IOM uint32_t  EVENTS_READ;                  /*!< (@ 0x00000168) Read command received                                      */
  __IM  uint32_t  RESERVED10[6];
  __IOM uint32_t  PUBLISH_STOPPED;              /*!< (@ 0x00000184) Publish configuration for event STOPPED                    */
  __IM  uint32_t  RESERVED11[7];
  __IOM uint32_t  PUBLISH_ERROR;                /*!< (@ 0x000001A4) Publish configuration for event ERROR                      */
  __IM  uint32_t  RESERVED12[9];
  __IOM uint32_t  PUBLISH_RXSTARTED;            /*!< (@ 0x000001CC) Publish configuration for event RXSTARTED                  */
  __IOM uint32_t  PUBLISH_TXSTARTED;            /*!< (@ 0x000001D0) Publish configuration for event TXSTARTED                  */
  __IM  uint32_t  RESERVED13[4];
  __IOM uint32_t  PUBLISH_WRITE;                /*!< (@ 0x000001E4) Publish configuration for event WRITE                      */
  __IOM uint32_t  PUBLISH_READ;                 /*!< (@ 0x000001E8) Publish configuration for event READ                       */
  __IM  uint32_t  RESERVED14[5];
  __IOM uint32_t  SHORTS;                       /*!< (@ 0x00000200) Shortcuts between local events and tasks                   */
  __IM  uint32_t  RESERVED15[63];
  __IOM uint32_t  INTEN;                        /*!< (@ 0x00000300) Enable or disable interrupt                                */
  __IOM uint32_t  INTENSET;                     /*!< (@ 0x00000304) Enable interrupt                                           */
  __IOM uint32_t  INTENCLR;                     /*!< (@ 0x00000308) Disable interrupt                                          */
  __IM  uint32_t  RESERVED16[113];
  __IOM uint32_t  ERRORSRC;                     /*!< (@ 0x000004D0) Error source                                               */
  __IM  uint32_t  MATCH;                        /*!< (@ 0x000004D4) Status register indicating which address had
                                                                    a match                                                    */
  __IM  uint32_t  RESERVED17[10];
  __IOM uint32_t  ENABLE;                       /*!< (@ 0x00000500) Enable TWIS                                                */
  __IM  uint32_t  RESERVED18;
  __IOM TWIS_PSEL_Type PSEL;                    /*!< (@ 0x00000508) Unspecified                                                */
  __IM  uint32_t  RESERVED19[9];
  __IOM TWIS_RXD_Type RXD;                      /*!< (@ 0x00000534) RXD EasyDMA channel                                        */
  __IOM TWIS_TXD_Type TXD;                      /*!< (@ 0x00000544) TXD EasyDMA channel                                        */
  __IM  uint32_t  RESERVED20[13];
  __IOM uint32_t  ADDRESS[2];                   /*!< (@ 0x00000588) Description collection: TWI slave address n                */
  __IM  uint32_t  RESERVED21;
  __IOM uint32_t  CONFIG;                       /*!< (@ 0x00000594) Configuration register for the address match
                                                                    mechanism                                                  */
  __IM  uint32_t  RESERVED22[10];
  __IOM uint32_t  ORC;                          /*!< (@ 0x000005C0) Over-read character. Character sent out in case
                                                                    of an over-read of the transmit buffer.                    */
} NRF_TWIS_Type;                                /*!< Size = 1476 (0x5c4)                                                       */



/* =========================================================================================================================== */
/* ================                                         UARTE0_NS                                         ================ */
/* =========================================================================================================================== */


/**
  * @brief UART with EasyDMA (UARTE0_NS)
  */

typedef struct {                                /*!< (@ 0x41013000) UARTE0_NS Structure                                        */
  __OM  uint32_t  TASKS_STARTRX;                /*!< (@ 0x00000000) Start UART receiver                                        */
  __OM  uint32_t  TASKS_STOPRX;                 /*!< (@ 0x00000004) Stop UART receiver                                         */
  __OM  uint32_t  TASKS_STARTTX;                /*!< (@ 0x00000008) Start UART transmitter                                     */
  __OM  uint32_t  TASKS_STOPTX;                 /*!< (@ 0x0000000C) Stop UART transmitter                                      */
  __IM  uint32_t  RESERVED[7];
  __OM  uint32_t  TASKS_FLUSHRX;                /*!< (@ 0x0000002C) Flush RX FIFO into RX buffer                               */
  __IM  uint32_t  RESERVED1[20];
  __IOM uint32_t  SUBSCRIBE_STARTRX;            /*!< (@ 0x00000080) Subscribe configuration for task STARTRX                   */
  __IOM uint32_t  SUBSCRIBE_STOPRX;             /*!< (@ 0x00000084) Subscribe configuration for task STOPRX                    */
  __IOM uint32_t  SUBSCRIBE_STARTTX;            /*!< (@ 0x00000088) Subscribe configuration for task STARTTX                   */
  __IOM uint32_t  SUBSCRIBE_STOPTX;             /*!< (@ 0x0000008C) Subscribe configuration for task STOPTX                    */
  __IM  uint32_t  RESERVED2[7];
  __IOM uint32_t  SUBSCRIBE_FLUSHRX;            /*!< (@ 0x000000AC) Subscribe configuration for task FLUSHRX                   */
  __IM  uint32_t  RESERVED3[20];
  __IOM uint32_t  EVENTS_CTS;                   /*!< (@ 0x00000100) CTS is activated (set low). Clear To Send.                 */
  __IOM uint32_t  EVENTS_NCTS;                  /*!< (@ 0x00000104) CTS is deactivated (set high). Not Clear To Send.          */
  __IOM uint32_t  EVENTS_RXDRDY;                /*!< (@ 0x00000108) Data received in RXD (but potentially not yet
                                                                    transferred to Data RAM)                                   */
  __IM  uint32_t  RESERVED4;
  __IOM uint32_t  EVENTS_ENDRX;                 /*!< (@ 0x00000110) Receive buffer is filled up                                */
  __IM  uint32_t  RESERVED5[2];
  __IOM uint32_t  EVENTS_TXDRDY;                /*!< (@ 0x0000011C) Data sent from TXD                                         */
  __IOM uint32_t  EVENTS_ENDTX;                 /*!< (@ 0x00000120) Last TX byte transmitted                                   */
  __IOM uint32_t  EVENTS_ERROR;                 /*!< (@ 0x00000124) Error detected                                             */
  __IM  uint32_t  RESERVED6[7];
  __IOM uint32_t  EVENTS_RXTO;                  /*!< (@ 0x00000144) Receiver timeout                                           */
  __IM  uint32_t  RESERVED7;
  __IOM uint32_t  EVENTS_RXSTARTED;             /*!< (@ 0x0000014C) UART receiver has started                                  */
  __IOM uint32_t  EVENTS_TXSTARTED;             /*!< (@ 0x00000150) UART transmitter has started                               */
  __IM  uint32_t  RESERVED8;
  __IOM uint32_t  EVENTS_TXSTOPPED;             /*!< (@ 0x00000158) Transmitter stopped                                        */
  __IM  uint32_t  RESERVED9[9];
  __IOM uint32_t  PUBLISH_CTS;                  /*!< (@ 0x00000180) Publish configuration for event CTS                        */
  __IOM uint32_t  PUBLISH_NCTS;                 /*!< (@ 0x00000184) Publish configuration for event NCTS                       */
  __IOM uint32_t  PUBLISH_RXDRDY;               /*!< (@ 0x00000188) Publish configuration for event RXDRDY                     */
  __IM  uint32_t  RESERVED10;
  __IOM uint32_t  PUBLISH_ENDRX;                /*!< (@ 0x00000190) Publish configuration for event ENDRX                      */
  __IM  uint32_t  RESERVED11[2];
  __IOM uint32_t  PUBLISH_TXDRDY;               /*!< (@ 0x0000019C) Publish configuration for event TXDRDY                     */
  __IOM uint32_t  PUBLISH_ENDTX;                /*!< (@ 0x000001A0) Publish configuration for event ENDTX                      */
  __IOM uint32_t  PUBLISH_ERROR;                /*!< (@ 0x000001A4) Publish configuration for event ERROR                      */
  __IM  uint32_t  RESERVED12[7];
  __IOM uint32_t  PUBLISH_RXTO;                 /*!< (@ 0x000001C4) Publish configuration for event RXTO                       */
  __IM  uint32_t  RESERVED13;
  __IOM uint32_t  PUBLISH_RXSTARTED;            /*!< (@ 0x000001CC) Publish configuration for event RXSTARTED                  */
  __IOM uint32_t  PUBLISH_TXSTARTED;            /*!< (@ 0x000001D0) Publish configuration for event TXSTARTED                  */
  __IM  uint32_t  RESERVED14;
  __IOM uint32_t  PUBLISH_TXSTOPPED;            /*!< (@ 0x000001D8) Publish configuration for event TXSTOPPED                  */
  __IM  uint32_t  RESERVED15[9];
  __IOM uint32_t  SHORTS;                       /*!< (@ 0x00000200) Shortcuts between local events and tasks                   */
  __IM  uint32_t  RESERVED16[63];
  __IOM uint32_t  INTEN;                        /*!< (@ 0x00000300) Enable or disable interrupt                                */
  __IOM uint32_t  INTENSET;                     /*!< (@ 0x00000304) Enable interrupt                                           */
  __IOM uint32_t  INTENCLR;                     /*!< (@ 0x00000308) Disable interrupt                                          */
  __IM  uint32_t  RESERVED17[93];
  __IOM uint32_t  ERRORSRC;                     /*!< (@ 0x00000480) Error source Note : this register is read / write
                                                                    one to clear.                                              */
  __IM  uint32_t  RESERVED18[31];
  __IOM uint32_t  ENABLE;                       /*!< (@ 0x00000500) Enable UART                                                */
  __IM  uint32_t  RESERVED19;
  __IOM UARTE_PSEL_Type PSEL;                   /*!< (@ 0x00000508) Unspecified                                                */
  __IM  uint32_t  RESERVED20[3];
  __IOM uint32_t  BAUDRATE;                     /*!< (@ 0x00000524) Baud rate. Accuracy depends on the HFCLK source
                                                                    selected.                                                  */
  __IM  uint32_t  RESERVED21[3];
  __IOM UARTE_RXD_Type RXD;                     /*!< (@ 0x00000534) RXD EasyDMA channel                                        */
  __IM  uint32_t  RESERVED22;
  __IOM UARTE_TXD_Type TXD;                     /*!< (@ 0x00000544) TXD EasyDMA channel                                        */
  __IM  uint32_t  RESERVED23[7];
  __IOM uint32_t  CONFIG;                       /*!< (@ 0x0000056C) Configuration of parity and hardware flow control          */
} NRF_UARTE_Type;                               /*!< Size = 1392 (0x570)                                                       */



/* =========================================================================================================================== */
/* ================                                          EGU0_NS                                          ================ */
/* =========================================================================================================================== */


/**
  * @brief Event generator unit (EGU0_NS)
  */

typedef struct {                                /*!< (@ 0x41014000) EGU0_NS Structure                                          */
  __OM  uint32_t  TASKS_TRIGGER[16];            /*!< (@ 0x00000000) Description collection: Trigger n for triggering
                                                                    the corresponding TRIGGERED[n] event                       */
  __IM  uint32_t  RESERVED[16];
  __IOM uint32_t  SUBSCRIBE_TRIGGER[16];        /*!< (@ 0x00000080) Description collection: Subscribe configuration
                                                                    for task TRIGGER[n]                                        */
  __IM  uint32_t  RESERVED1[16];
  __IOM uint32_t  EVENTS_TRIGGERED[16];         /*!< (@ 0x00000100) Description collection: Event number n generated
                                                                    by triggering the corresponding TRIGGER[n]
                                                                    task                                                       */
  __IM  uint32_t  RESERVED2[16];
  __IOM uint32_t  PUBLISH_TRIGGERED[16];        /*!< (@ 0x00000180) Description collection: Publish configuration
                                                                    for event TRIGGERED[n]                                     */
  __IM  uint32_t  RESERVED3[80];
  __IOM uint32_t  INTEN;                        /*!< (@ 0x00000300) Enable or disable interrupt                                */
  __IOM uint32_t  INTENSET;                     /*!< (@ 0x00000304) Enable interrupt                                           */
  __IOM uint32_t  INTENCLR;                     /*!< (@ 0x00000308) Disable interrupt                                          */
} NRF_EGU_Type;                                 /*!< Size = 780 (0x30c)                                                        */



/* =========================================================================================================================== */
/* ================                                          SWI0_NS                                          ================ */
/* =========================================================================================================================== */


/**
  * @brief Software interrupt 0 (SWI0_NS)
  */

typedef struct {                                /*!< (@ 0x4101A000) SWI0_NS Structure                                          */
  __IM  uint32_t  UNUSED;                       /*!< (@ 0x00000000) Unused.                                                    */
} NRF_SWI_Type;                                 /*!< Size = 4 (0x4)                                                            */



/* =========================================================================================================================== */
/* ================                                        APPMUTEX_NS                                        ================ */
/* =========================================================================================================================== */


/**
  * @brief MUTEX 0 (APPMUTEX_NS)
  */

typedef struct {                                /*!< (@ 0x40030000) APPMUTEX_NS Structure                                      */
  __IM  uint32_t  RESERVED[256];
  __IOM uint32_t  MUTEX[16];                    /*!< (@ 0x00000400) Description collection: Mutex register                     */
} NRF_MUTEX_Type;                               /*!< Size = 1088 (0x440)                                                       */



/* =========================================================================================================================== */
/* ================                                          ACL_NS                                           ================ */
/* =========================================================================================================================== */


/**
  * @brief Access control lists (ACL_NS)
  */

typedef struct {                                /*!< (@ 0x41080000) ACL_NS Structure                                           */
  __IM  uint32_t  RESERVED[512];
  __IOM ACL_ACL_Type ACL[8];                    /*!< (@ 0x00000800) Unspecified                                                */
} NRF_ACL_Type;                                 /*!< Size = 2176 (0x880)                                                       */



/* =========================================================================================================================== */
/* ================                                          NVMC_NS                                          ================ */
/* =========================================================================================================================== */


/**
  * @brief Non-volatile memory controller (NVMC_NS)
  */

typedef struct {                                /*!< (@ 0x41080000) NVMC_NS Structure                                          */
  __IM  uint32_t  RESERVED[256];
  __IM  uint32_t  READY;                        /*!< (@ 0x00000400) Ready flag                                                 */
  __IM  uint32_t  RESERVED1;
  __IM  uint32_t  READYNEXT;                    /*!< (@ 0x00000408) Ready flag                                                 */
  __IM  uint32_t  RESERVED2[62];
  __IOM uint32_t  CONFIG;                       /*!< (@ 0x00000504) Configuration register                                     */
  __IM  uint32_t  RESERVED3;
  __OM  uint32_t  ERASEALL;                     /*!< (@ 0x0000050C) Register for erasing all non-volatile user memory          */
  __IM  uint32_t  RESERVED4[3];
  __IOM uint32_t  ERASEPAGEPARTIALCFG;          /*!< (@ 0x0000051C) Register for partial erase configuration                   */
  __IM  uint32_t  RESERVED5[8];
  __IOM uint32_t  ICACHECNF;                    /*!< (@ 0x00000540) I-code cache configuration register                        */
  __IM  uint32_t  RESERVED6;
  __IOM uint32_t  IHIT;                         /*!< (@ 0x00000548) I-code cache hit counter                                   */
  __IOM uint32_t  IMISS;                        /*!< (@ 0x0000054C) I-code cache miss counter                                  */
  __IM  uint32_t  RESERVED7[13];
  __IOM uint32_t  CONFIGNS;                     /*!< (@ 0x00000584) Unspecified                                                */
  __OM  uint32_t  WRITEUICRNS;                  /*!< (@ 0x00000588) Non-secure APPROTECT enable register                       */
} NRF_NVMC_Type;                                /*!< Size = 1420 (0x58c)                                                       */



/* =========================================================================================================================== */
/* ================                                          VMC_NS                                           ================ */
/* =========================================================================================================================== */


/**
  * @brief Volatile Memory controller (VMC_NS)
  */

typedef struct {                                /*!< (@ 0x41081000) VMC_NS Structure                                           */
  __IM  uint32_t  RESERVED[384];
  __IOM VMC_RAM_Type RAM[4];                    /*!< (@ 0x00000600) Unspecified                                                */
} NRF_VMC_Type;                                 /*!< Size = 1600 (0x640)                                                       */



/* =========================================================================================================================== */
/* ================                                           P0_NS                                           ================ */
/* =========================================================================================================================== */


/**
  * @brief GPIO Port 0 (P0_NS)
  */

typedef struct {                                /*!< (@ 0x418C0500) P0_NS Structure                                            */
  __IM  uint32_t  RESERVED;
  __IOM uint32_t  OUT;                          /*!< (@ 0x00000004) Write GPIO port                                            */
  __IOM uint32_t  OUTSET;                       /*!< (@ 0x00000008) Set individual bits in GPIO port                           */
  __IOM uint32_t  OUTCLR;                       /*!< (@ 0x0000000C) Clear individual bits in GPIO port                         */
  __IM  uint32_t  IN;                           /*!< (@ 0x00000010) Read GPIO port                                             */
  __IOM uint32_t  DIR;                          /*!< (@ 0x00000014) Direction of GPIO pins                                     */
  __IOM uint32_t  DIRSET;                       /*!< (@ 0x00000018) DIR set register                                           */
  __IOM uint32_t  DIRCLR;                       /*!< (@ 0x0000001C) DIR clear register                                         */
  __IOM uint32_t  LATCH;                        /*!< (@ 0x00000020) Latch register indicating what GPIO pins that
                                                                    have met the criteria set in the PIN_CNF[n].SENSE
                                                                    registers                                                  */
  __IOM uint32_t  DETECTMODE;                   /*!< (@ 0x00000024) Select between default DETECT signal behavior
                                                                    and LDETECT mode (For non-secure pin only)                 */
  __IOM uint32_t  DETECTMODE_SEC;               /*!< (@ 0x00000028) Select between default DETECT signal behavior
                                                                    and LDETECT mode (For secure pin only)                     */
  __IM  uint32_t  RESERVED1[117];
  __IOM uint32_t  PIN_CNF[32];                  /*!< (@ 0x00000200) Description collection: Configuration of GPIO
                                                                    pins                                                       */
} NRF_GPIO_Type;                                /*!< Size = 640 (0x280)                                                        */


/** @} */ /* End of group Device_Peripheral_peripherals */


/* =========================================================================================================================== */
/* ================                          Device Specific Peripheral Address Map                           ================ */
/* =========================================================================================================================== */


/** @addtogroup Device_Peripheral_peripheralAddr
  * @{
  */

#define NRF_FICR_NS_BASE            0x01FF0000UL
#define NRF_UICR_NS_BASE            0x01FF8000UL
#define NRF_CTI_NS_BASE             0xE0042000UL
#define NRF_DCNF_NS_BASE            0x41000000UL
#define NRF_VREQCTRL_NS_BASE        0x41004000UL
#define NRF_CLOCK_NS_BASE           0x41005000UL
#define NRF_POWER_NS_BASE           0x41005000UL
#define NRF_RESET_NS_BASE           0x41005000UL
#define NRF_CTRLAP_NS_BASE          0x41006000UL
#define NRF_RADIO_NS_BASE           0x41008000UL
#define NRF_RNG_NS_BASE             0x41009000UL
#define NRF_GPIOTE_NS_BASE          0x4100A000UL
#define NRF_WDT_NS_BASE             0x4100B000UL
#define NRF_TIMER0_NS_BASE          0x4100C000UL
#define NRF_ECB_NS_BASE             0x4100D000UL
#define NRF_AAR_NS_BASE             0x4100E000UL
#define NRF_CCM_NS_BASE             0x4100E000UL
#define NRF_DPPIC_NS_BASE           0x4100F000UL
#define NRF_TEMP_NS_BASE            0x41010000UL
#define NRF_RTC0_NS_BASE            0x41011000UL
#define NRF_IPC_NS_BASE             0x41012000UL
#define NRF_SPIM0_NS_BASE           0x41013000UL
#define NRF_SPIS0_NS_BASE           0x41013000UL
#define NRF_TWIM0_NS_BASE           0x41013000UL
#define NRF_TWIS0_NS_BASE           0x41013000UL
#define NRF_UARTE0_NS_BASE          0x41013000UL
#define NRF_EGU0_NS_BASE            0x41014000UL
#define NRF_RTC1_NS_BASE            0x41016000UL
#define NRF_TIMER1_NS_BASE          0x41018000UL
#define NRF_TIMER2_NS_BASE          0x41019000UL
#define NRF_SWI0_NS_BASE            0x4101A000UL
#define NRF_SWI1_NS_BASE            0x4101B000UL
#define NRF_SWI2_NS_BASE            0x4101C000UL
#define NRF_SWI3_NS_BASE            0x4101D000UL
#define NRF_APPMUTEX_NS_BASE        0x40030000UL
#define NRF_APPMUTEX_S_BASE         0x50030000UL
#define NRF_ACL_NS_BASE             0x41080000UL
#define NRF_NVMC_NS_BASE            0x41080000UL
#define NRF_VMC_NS_BASE             0x41081000UL
#define NRF_P0_NS_BASE              0x418C0500UL
#define NRF_P1_NS_BASE              0x418C0800UL

/** @} */ /* End of group Device_Peripheral_peripheralAddr */


/* =========================================================================================================================== */
/* ================                                  Peripheral declaration                                   ================ */
/* =========================================================================================================================== */


/** @addtogroup Device_Peripheral_declaration
  * @{
  */

#define NRF_FICR_NS                 ((NRF_FICR_Type*)          NRF_FICR_NS_BASE)
#define NRF_UICR_NS                 ((NRF_UICR_Type*)          NRF_UICR_NS_BASE)
#define NRF_CTI_NS                  ((NRF_CTI_Type*)           NRF_CTI_NS_BASE)
#define NRF_DCNF_NS                 ((NRF_DCNF_Type*)          NRF_DCNF_NS_BASE)
#define NRF_VREQCTRL_NS             ((NRF_VREQCTRL_Type*)      NRF_VREQCTRL_NS_BASE)
#define NRF_CLOCK_NS                ((NRF_CLOCK_Type*)         NRF_CLOCK_NS_BASE)
#define NRF_POWER_NS                ((NRF_POWER_Type*)         NRF_POWER_NS_BASE)
#define NRF_RESET_NS                ((NRF_RESET_Type*)         NRF_RESET_NS_BASE)
#define NRF_CTRLAP_NS               ((NRF_CTRLAPPERI_Type*)    NRF_CTRLAP_NS_BASE)
#define NRF_RADIO_NS                ((NRF_RADIO_Type*)         NRF_RADIO_NS_BASE)
#define NRF_RNG_NS                  ((NRF_RNG_Type*)           NRF_RNG_NS_BASE)
#define NRF_GPIOTE_NS               ((NRF_GPIOTE_Type*)        NRF_GPIOTE_NS_BASE)
#define NRF_WDT_NS                  ((NRF_WDT_Type*)           NRF_WDT_NS_BASE)
#define NRF_TIMER0_NS               ((NRF_TIMER_Type*)         NRF_TIMER0_NS_BASE)
#define NRF_ECB_NS                  ((NRF_ECB_Type*)           NRF_ECB_NS_BASE)
#define NRF_AAR_NS                  ((NRF_AAR_Type*)           NRF_AAR_NS_BASE)
#define NRF_CCM_NS                  ((NRF_CCM_Type*)           NRF_CCM_NS_BASE)
#define NRF_DPPIC_NS                ((NRF_DPPIC_Type*)         NRF_DPPIC_NS_BASE)
#define NRF_TEMP_NS                 ((NRF_TEMP_Type*)          NRF_TEMP_NS_BASE)
#define NRF_RTC0_NS                 ((NRF_RTC_Type*)           NRF_RTC0_NS_BASE)
#define NRF_IPC_NS                  ((NRF_IPC_Type*)           NRF_IPC_NS_BASE)
#define NRF_SPIM0_NS                ((NRF_SPIM_Type*)          NRF_SPIM0_NS_BASE)
#define NRF_SPIS0_NS                ((NRF_SPIS_Type*)          NRF_SPIS0_NS_BASE)
#define NRF_TWIM0_NS                ((NRF_TWIM_Type*)          NRF_TWIM0_NS_BASE)
#define NRF_TWIS0_NS                ((NRF_TWIS_Type*)          NRF_TWIS0_NS_BASE)
#define NRF_UARTE0_NS               ((NRF_UARTE_Type*)         NRF_UARTE0_NS_BASE)
#define NRF_EGU0_NS                 ((NRF_EGU_Type*)           NRF_EGU0_NS_BASE)
#define NRF_RTC1_NS                 ((NRF_RTC_Type*)           NRF_RTC1_NS_BASE)
#define NRF_TIMER1_NS               ((NRF_TIMER_Type*)         NRF_TIMER1_NS_BASE)
#define NRF_TIMER2_NS               ((NRF_TIMER_Type*)         NRF_TIMER2_NS_BASE)
#define NRF_SWI0_NS                 ((NRF_SWI_Type*)           NRF_SWI0_NS_BASE)
#define NRF_SWI1_NS                 ((NRF_SWI_Type*)           NRF_SWI1_NS_BASE)
#define NRF_SWI2_NS                 ((NRF_SWI_Type*)           NRF_SWI2_NS_BASE)
#define NRF_SWI3_NS                 ((NRF_SWI_Type*)           NRF_SWI3_NS_BASE)
#define NRF_APPMUTEX_NS             ((NRF_MUTEX_Type*)         NRF_APPMUTEX_NS_BASE)
#define NRF_APPMUTEX_S              ((NRF_MUTEX_Type*)         NRF_APPMUTEX_S_BASE)
#define NRF_ACL_NS                  ((NRF_ACL_Type*)           NRF_ACL_NS_BASE)
#define NRF_NVMC_NS                 ((NRF_NVMC_Type*)          NRF_NVMC_NS_BASE)
#define NRF_VMC_NS                  ((NRF_VMC_Type*)           NRF_VMC_NS_BASE)
#define NRF_P0_NS                   ((NRF_GPIO_Type*)          NRF_P0_NS_BASE)
#define NRF_P1_NS                   ((NRF_GPIO_Type*)          NRF_P1_NS_BASE)

/** @} */ /* End of group Device_Peripheral_declaration */


#ifdef __cplusplus
}
#endif

#endif /* NRF5340_NETWORK_H */


/** @} */ /* End of group nrf5340_network */

/** @} */ /* End of group Nordic Semiconductor */
