<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html lang="en">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta http-equiv="Content-Style-Type" content="text/css">
<link rel="up" title="FatFs" href="../00index_e.html">
<link rel="alternate" hreflang="ja" title="Japanese" href="../ja/findnext.html">
<link rel="stylesheet" href="../css_e.css" type="text/css" media="screen" title="ELM Default">
<title>FatFs - f_findnext</title>
</head>

<body>

<div class="para func">
<h2>f_findnext</h2>
<p>The f_findnext function searches for a next matched object</p>
<pre>
FRESULT f_findnext (
  DIR* <span class="arg">dp</span>,              <span class="c">/* [IN] Poninter to the directory object */</span>
  FILINFO* <span class="arg">fno</span>          <span class="c">/* [OUT] Pointer to the file information structure */</span>
);
</pre>
</div>

<div class="para arg">
<h4>Parameters</h4>
<dl class="par">
<dt>dp</dt>
<dd>Pointer to the valid directory object created by <tt>f_findfirst</tt> function.</dd>
<dt>fno</dt>
<dd>Pointer to the file information structure to store the information about the found directory item.</dd>
</dl>
</div>


<div class="para ret">
<h4>Return Values</h4>
<p>
<a href="rc.html#ok">FR_OK</a>,
<a href="rc.html#de">FR_DISK_ERR</a>,
<a href="rc.html#ie">FR_INT_ERR</a>,
<a href="rc.html#nr">FR_NOT_READY</a>,
<a href="rc.html#io">FR_INVALID_OBJECT</a>,
<a href="rc.html#tm">FR_TIMEOUT</a>,
<a href="rc.html#nc">FR_NOT_ENOUGH_CORE</a>
</p>
</div>


<div class="para desc">
<h4>Description</h4>
<p>It continues the search from a previous call to the <tt>f_findfirst</tt> or <tt>f_findnext</tt> function. If found, the information about the object is stored into the file information structure. If no item to be read, a null string will be returned into <tt>fno-&gt;fname[]</tt>.</p>
</div>


<div class="para comp">
<h4>QuickInfo</h4>
<p>This is a wrapper function of <a href="readdir.html"><tt>f_readdir</tt></a> function. Available when <tt>_USE_FIND == 1</tt> and <tt>_FS_MINIMIZE &lt;= 1</tt>.</p>
</div>


<div class="para ref">
<h4>See Also</h4>
<p><tt><a href="findfirst.html">f_findfirst</a>, <a href="closedir.html">f_closedir</a>, <a href="sdir.html">DIR</a>, <a href="sfileinfo.html">FILINFO</a></tt></p>
</div>

<p class="foot"><a href="../00index_e.html">Return</a></p>
</body>
</html>
