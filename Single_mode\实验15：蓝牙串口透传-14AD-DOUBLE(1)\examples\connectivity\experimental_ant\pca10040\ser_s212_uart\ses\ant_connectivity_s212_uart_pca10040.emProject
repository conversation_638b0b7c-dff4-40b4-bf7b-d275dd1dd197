<!DOCTYPE CrossStudio_Project_File>
<solution Name="ant_connectivity_s212_uart_pca10040" target="8" version="2">
  <project Name="ant_connectivity_s212_uart_pca10040">
    <configuration
      Name="Common"
      arm_architecture="v7EM"
      arm_core_type="Cortex-M4"
      arm_endian="Little"
      arm_fp_abi="Hard"
      arm_fpu_type="FPv4-SP-D16"
      arm_linker_heap_size="1024"
      arm_linker_process_stack_size="0"
      arm_linker_stack_size="2048"
      arm_linker_treat_warnings_as_errors="No"
      arm_simulator_memory_simulation_parameter="RWX 00000000,00100000,FFFFFFFF;RWX 20000000,00010000,CDCDCDCD"
      arm_target_device_name="nRF52832_xxAA"
      arm_target_interface_type="SWD"
      c_user_include_directories="../../../config;../../../../../../components;../../../../../../components/ble/ble_dtm;../../../../../../components/boards;../../../../../../components/libraries/atomic;../../../../../../components/libraries/atomic_fifo;../../../../../../components/libraries/balloc;../../../../../../components/libraries/bsp;../../../../../../components/libraries/delay;../../../../../../components/libraries/experimental_section_vars;../../../../../../components/libraries/log;../../../../../../components/libraries/log/src;../../../../../../components/libraries/memobj;../../../../../../components/libraries/ringbuf;../../../../../../components/libraries/scheduler;../../../../../../components/libraries/sortlist;../../../../../../components/libraries/strerror;../../../../../../components/libraries/timer;../../../../../../components/libraries/util;../../../../../../components/serialization/common;../../../../../../components/serialization/common/struct_ser/ant;../../../../../../components/serialization/common/struct_ser/ble;../../../../../../components/serialization/common/transport;../../../../../../components/serialization/common/transport/ser_phy;../../../../../../components/serialization/common/transport/ser_phy/config;../../../../../../components/serialization/connectivity;../../../../../../components/serialization/connectivity/codecs/ant/middleware;../../../../../../components/serialization/connectivity/codecs/ant/serializers;../../../../../../components/serialization/connectivity/codecs/ble/serializers;../../../../../../components/serialization/connectivity/codecs/common;../../../../../../components/serialization/connectivity/hal;../../../../../../components/softdevice/common;../../../../../../components/softdevice/s212/headers;../../../../../../components/softdevice/s212/headers/nrf52;../../../../../../components/toolchain/cmsis/include;../../../../../../external/fprintf;../../../../../../external/segger_rtt;../../../../../../integration/nrfx;../../../../../../integration/nrfx/legacy;../../../../../../modules/nrfx;../../../../../../modules/nrfx/drivers/include;../../../../../../modules/nrfx/hal;../../../../../../modules/nrfx/mdk;../config;"
      c_preprocessor_definitions="ANT_STACK_SUPPORT_REQD;APP_TIMER_V2;APP_TIMER_V2_RTC1_ENABLED;BOARD_PCA10040;BSP_DEFINES_ONLY;CONFIG_GPIO_AS_PINRESET;FLOAT_ABI_HARD;INITIALIZE_USER_SECTIONS;NO_VTOR_CONFIG;NRF52;NRF52832_XXAA;NRF52_PAN_74;S212;SER_CONNECTIVITY;SOFTDEVICE_PRESENT;"
      debug_target_connection="J-Link"
      gcc_entry_point="Reset_Handler"
      macros="CMSIS_CONFIG_TOOL=../../../../../../external_tools/cmsisconfig/CMSIS_Configuration_Wizard.jar"
      debug_register_definition_file="../../../../../../modules/nrfx/mdk/nrf52.svd"
      debug_start_from_entry_point_symbol="No"
      gcc_debugging_level="Level 3"      linker_output_format="hex"
      linker_printf_width_precision_supported="Yes"
      linker_printf_fmt_level="long"
      linker_scanf_fmt_level="long"
      linker_section_placement_file="flash_placement.xml"
      linker_section_placement_macros="FLASH_PH_START=0x0;FLASH_PH_SIZE=0x80000;RAM_PH_START=0x20000000;RAM_PH_SIZE=0x10000;FLASH_START=0x12000;FLASH_SIZE=0x6e000;RAM_START=0x20006b80;RAM_SIZE=0x9480"
      
      linker_section_placements_segments="FLASH RX 0x0 0x80000;RAM RWX 0x20000000 0x10000"
      project_directory=""
      project_type="Executable" />
      <folder Name="Segger Startup Files">
        <file file_name="$(StudioDir)/source/thumb_crt0.s" />
      </folder>
    <folder Name="nRF_Log">
      <file file_name="../../../../../../components/libraries/log/src/nrf_log_backend_rtt.c" />
      <file file_name="../../../../../../components/libraries/log/src/nrf_log_backend_serial.c" />
      <file file_name="../../../../../../components/libraries/log/src/nrf_log_backend_uart.c" />
      <file file_name="../../../../../../components/libraries/log/src/nrf_log_default_backends.c" />
      <file file_name="../../../../../../components/libraries/log/src/nrf_log_frontend.c" />
      <file file_name="../../../../../../components/libraries/log/src/nrf_log_str_formatter.c" />
    </folder>
    <folder Name="nRF_Libraries">
      <file file_name="../../../../../../components/libraries/util/app_error.c" />
      <file file_name="../../../../../../components/libraries/util/app_error_handler_gcc.c" />
      <file file_name="../../../../../../components/libraries/util/app_error_weak.c" />
      <file file_name="../../../../../../components/libraries/scheduler/app_scheduler.c" />
      <file file_name="../../../../../../components/libraries/timer/app_timer2.c" />
      <file file_name="../../../../../../components/libraries/util/app_util_platform.c" />
      <file file_name="../../../../../../components/libraries/timer/drv_rtc.c" />
      <file file_name="../../../../../../components/libraries/util/nrf_assert.c" />
      <file file_name="../../../../../../components/libraries/atomic_fifo/nrf_atfifo.c" />
      <file file_name="../../../../../../components/libraries/atomic/nrf_atomic.c" />
      <file file_name="../../../../../../components/libraries/balloc/nrf_balloc.c" />
      <file file_name="../../../../../../external/fprintf/nrf_fprintf.c" />
      <file file_name="../../../../../../external/fprintf/nrf_fprintf_format.c" />
      <file file_name="../../../../../../components/libraries/memobj/nrf_memobj.c" />
      <file file_name="../../../../../../components/libraries/ringbuf/nrf_ringbuf.c" />
      <file file_name="../../../../../../components/libraries/experimental_section_vars/nrf_section_iter.c" />
      <file file_name="../../../../../../components/libraries/sortlist/nrf_sortlist.c" />
      <file file_name="../../../../../../components/libraries/strerror/nrf_strerror.c" />
    </folder>
    <folder Name="None">
      <file file_name="../../../../../../modules/nrfx/mdk/ses_startup_nrf52.s" />
      <file file_name="../../../../../../modules/nrfx/mdk/ses_startup_nrf_common.s" />
      <file file_name="../../../../../../modules/nrfx/mdk/system_nrf52.c" />
    </folder>
    <folder Name="Board Definition">
      <file file_name="../../../../../../components/boards/boards.c" />
    </folder>
    <folder Name="nRF_Serialization">
      <file file_name="../../../../../../components/serialization/connectivity/codecs/ant/serializers/ant_acknowledge_message_tx.c" />
      <file file_name="../../../../../../components/serialization/connectivity/codecs/ant/serializers/ant_active_search_sharing_cycles_get.c" />
      <file file_name="../../../../../../components/serialization/connectivity/codecs/ant/serializers/ant_active_search_sharing_cycles_set.c" />
      <file file_name="../../../../../../components/serialization/connectivity/codecs/ant/serializers/ant_adv_burst_config_set.c" />
      <file file_name="../../../../../../components/serialization/connectivity/codecs/ant/serializers/ant_broadcast_message_tx.c" />
      <file file_name="../../../../../../components/serialization/connectivity/codecs/ant/serializers/ant_capabilities_get.c" />
      <file file_name="../../../../../../components/serialization/connectivity/codecs/ant/serializers/ant_channel_assign.c" />
      <file file_name="../../../../../../components/serialization/connectivity/codecs/ant/serializers/ant_channel_close.c" />
      <file file_name="../../../../../../components/serialization/connectivity/codecs/ant/serializers/ant_channel_id_get.c" />
      <file file_name="../../../../../../components/serialization/connectivity/codecs/ant/serializers/ant_channel_id_set.c" />
      <file file_name="../../../../../../components/serialization/connectivity/codecs/ant/serializers/ant_channel_low_priority_rx_search_timout_set.c" />
      <file file_name="../../../../../../components/serialization/connectivity/codecs/ant/serializers/ant_channel_open_with_offset.c" />
      <file file_name="../../../../../../components/serialization/connectivity/codecs/ant/serializers/ant_channel_period_get.c" />
      <file file_name="../../../../../../components/serialization/connectivity/codecs/ant/serializers/ant_channel_period_set.c" />
      <file file_name="../../../../../../components/serialization/connectivity/codecs/ant/serializers/ant_channel_radio_freq_get.c" />
      <file file_name="../../../../../../components/serialization/connectivity/codecs/ant/serializers/ant_channel_radio_freq_set.c" />
      <file file_name="../../../../../../components/serialization/connectivity/codecs/ant/serializers/ant_channel_radio_tx_power_set.c" />
      <file file_name="../../../../../../components/serialization/connectivity/codecs/ant/serializers/ant_channel_rx_search_timeout_set.c" />
      <file file_name="../../../../../../components/serialization/connectivity/codecs/ant/serializers/ant_channel_status_get.c" />
      <file file_name="../../../../../../components/serialization/connectivity/codecs/ant/serializers/ant_channel_unassign.c" />
      <file file_name="../../../../../../components/serialization/connectivity/codecs/ant/serializers/ant_coex_config_get.c" />
      <file file_name="../../../../../../components/serialization/connectivity/codecs/ant/serializers/ant_coex_config_set.c" />
      <file file_name="../../../../../../components/serialization/connectivity/codecs/ant/serializers/ant_crypto_channel_enable.c" />
      <file file_name="../../../../../../components/serialization/connectivity/codecs/ant/serializers/ant_crypto_info_get.c" />
      <file file_name="../../../../../../components/serialization/connectivity/codecs/ant/serializers/ant_crypto_info_set.c" />
      <file file_name="../../../../../../components/serialization/connectivity/codecs/ant/serializers/ant_crypto_key_set.c" />
      <file file_name="../../../../../../components/serialization/connectivity/codecs/ant/serializers/ant_cw_test_mode.c" />
      <file file_name="../../../../../../components/serialization/connectivity/codecs/ant/serializers/ant_cw_test_mode_init.c" />
      <file file_name="../../../../../../components/serialization/connectivity/codecs/ant/serializers/ant_enable.c" />
      <file file_name="../../../../../../components/serialization/connectivity/codecs/ant/serializers/ant_event.c" />
      <file file_name="../../../../../../components/serialization/connectivity/codecs/ant/serializers/ant_event_rx.c" />
      <file file_name="../../../../../../components/serialization/connectivity/codecs/ant/serializers/ant_id_list_add.c" />
      <file file_name="../../../../../../components/serialization/connectivity/codecs/ant/serializers/ant_id_list_config.c" />
      <file file_name="../../../../../../components/serialization/connectivity/codecs/ant/serializers/ant_lib_config_clear.c" />
      <file file_name="../../../../../../components/serialization/connectivity/codecs/ant/serializers/ant_lib_config_get.c" />
      <file file_name="../../../../../../components/serialization/connectivity/codecs/ant/serializers/ant_lib_config_set.c" />
      <file file_name="../../../../../../components/serialization/connectivity/codecs/ant/serializers/ant_network_address_set.c" />
      <file file_name="../../../../../../components/serialization/connectivity/codecs/ant/serializers/ant_prox_search_set.c" />
      <file file_name="../../../../../../components/serialization/connectivity/codecs/ant/serializers/ant_rx_scan_mode_start.c" />
      <file file_name="../../../../../../components/serialization/connectivity/codecs/ant/serializers/ant_search_channel_priority_set.c" />
      <file file_name="../../../../../../components/serialization/connectivity/codecs/ant/serializers/ant_search_waveform_set.c" />
      <file file_name="../../../../../../components/serialization/connectivity/codecs/ant/serializers/ant_stack_reset.c" />
      <file file_name="../../../../../../components/serialization/common/struct_ser/ant/ant_struct_serialization.c" />
      <file file_name="../../../../../../components/serialization/connectivity/codecs/ant/serializers/ant_version_get.c" />
      <file file_name="../../../../../../components/serialization/connectivity/codecs/common/ble_dtm_init.c" />
      <file file_name="../../../../../../components/serialization/common/ble_serialization.c" />
      <file file_name="../../../../../../components/serialization/common/cond_field_serialization.c" />
      <file file_name="../../../../../../components/serialization/connectivity/codecs/common/conn_mw.c" />
      <file file_name="../../../../../../components/serialization/connectivity/codecs/ant/middleware/conn_mw_ant.c" />
      <file file_name="../../../../../../components/serialization/connectivity/codecs/common/conn_mw_nrf_soc.c" />
      <file file_name="../../../../../../components/serialization/connectivity/hal/dtm_uart.c" />
      <file file_name="../../../../../../components/serialization/connectivity/codecs/ble/serializers/nrf_soc_conn.c" />
      <file file_name="../../../../../../components/serialization/common/struct_ser/ble/nrf_soc_struct_serialization.c" />
      <file file_name="../../../../../../components/serialization/connectivity/ser_conn_cmd_decoder.c" />
      <file file_name="../../../../../../components/serialization/connectivity/ser_conn_dtm_cmd_decoder.c" />
      <file file_name="../../../../../../components/serialization/connectivity/ser_conn_error_handling.c" />
      <file file_name="../../../../../../components/serialization/connectivity/ser_conn_event_encoder.c" />
      <file file_name="../../../../../../components/serialization/connectivity/ser_conn_handlers.c" />
      <file file_name="../../../../../../components/serialization/connectivity/ser_conn_pkt_decoder.c" />
      <file file_name="../../../../../../components/serialization/connectivity/ser_conn_reset_cmd_decoder.c" />
      <file file_name="../../../../../../components/serialization/common/ser_dbg_sd_str.c" />
      <file file_name="../../../../../../components/serialization/common/transport/ser_hal_transport.c" />
      <file file_name="../../../../../../components/serialization/common/transport/ser_phy/ser_phy_uart.c" />
    </folder>
    <folder Name="nRF_Drivers">
      <file file_name="../../../../../../integration/nrfx/legacy/nrf_drv_clock.c" />
      <file file_name="../../../../../../integration/nrfx/legacy/nrf_drv_uart.c" />
      <file file_name="../../../../../../modules/nrfx/soc/nrfx_atomic.c" />
      <file file_name="../../../../../../modules/nrfx/drivers/src/nrfx_clock.c" />
      <file file_name="../../../../../../modules/nrfx/drivers/src/prs/nrfx_prs.c" />
      <file file_name="../../../../../../modules/nrfx/drivers/src/nrfx_uart.c" />
      <file file_name="../../../../../../modules/nrfx/drivers/src/nrfx_uarte.c" />
    </folder>
    <folder Name="Application">
      <file file_name="../../../main.c" />
      <file file_name="../config/sdk_config.h" />
    </folder>
    <folder Name="nRF_Segger_RTT">
      <file file_name="../../../../../../external/segger_rtt/SEGGER_RTT.c" />
      <file file_name="../../../../../../external/segger_rtt/SEGGER_RTT_Syscalls_SES.c" />
      <file file_name="../../../../../../external/segger_rtt/SEGGER_RTT_printf.c" />
    </folder>
    <folder Name="nRF_BLE">
      <file file_name="../../../../../../components/ble/ble_dtm/ble_dtm.c" />
      <file file_name="../../../../../../components/ble/ble_dtm/ble_dtm_hw_nrf52.c" />
    </folder>
    <folder Name="nRF_SoftDevice">
      <file file_name="../../../../../../components/softdevice/common/nrf_sdh.c" />
      <file file_name="../../../../../../components/softdevice/common/nrf_sdh_ant.c" />
      <file file_name="../../../../../../components/softdevice/common/nrf_sdh_soc.c" />
    </folder>
  </project>
  <configuration Name="Release"
    c_preprocessor_definitions="NDEBUG"
    link_time_optimization="No"    gcc_optimization_level="Optimize For Size" />
  <configuration Name="Debug"
    c_preprocessor_definitions="DEBUG; DEBUG_NRF"
    gcc_optimization_level="None"/>

</solution>
