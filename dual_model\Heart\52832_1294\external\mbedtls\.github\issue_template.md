Note: This is just a template, so feel free to use/remove the unnecessary things

### Description
- Type: Bug | Enhancement\Feature Request | Question
- Priority: Blocker | Major | Minor

---------------------------------------------------------------
## Bug

**OS**  
Mbed OS|linux|windows|

**mbed TLS build:**  
Version: x.x.x or git commit id  
OS version: x.x.x  
Configuration: please attach config.h file where possible  
Compiler and options (if you used a pre-built binary, please indicate how you obtained it):  
Additional environment information:  

**Peer device TLS stack and version**  
OpenSSL|GnuTls|Chrome|NSS(Firefox)|SecureChannel (IIS/Internet Explorer/Edge)|Other  
Version:  

**Expected behavior**   

**Actual behavior**  

**Steps to reproduce**  

----------------------------------------------------------------
## Enhancement\Feature Request

**Justification - why does the library need this feature?**  

**Suggested enhancement**  

-----------------------------------------------------------------

## Question

**Please first check for answers in the [Mbed TLS knowledge Base](https://tls.mbed.org/kb), and preferably file an issue in the [Mbed TLS support forum](https://forums.mbed.com/c/mbed-tls)**  
