package dfu;

// Version 0.1

enum FwType {
    APPLICATION             = 0;
    SOFTDEVICE              = 1;
    BOOTLOADER              = 2;
    SOFTDEVICE_BOOTLOADER   = 3;
    EXTERNAL_APPLICATION    = 4;
}

enum HashType {
    NO_HASH = 0;
    CRC     = 1;
    SHA128  = 2;
    SHA256  = 3;
    SHA512  = 4;
}

enum OpCode {
    INIT = 1;
}

enum ValidationType {
    NO_VALIDATION                   = 0;
    VALIDATE_GENERATED_CRC          = 1;
    VALIDATE_SHA256                 = 2;
    VALIDATE_ECDSA_P256_SHA256      = 3;
}

message Hash {
    required HashType   hash_type   = 1;
    required bytes      hash        = 2;
}

message BootValidation {

    required ValidationType type    = 1;
    required bytes          bytes   = 2;
}

// Commands data
message InitCommand {
    optional uint32             fw_version      = 1;
    optional uint32             hw_version      = 2;
    repeated uint32             sd_req          = 3 [packed = true];
    optional FwType             type            = 4;

    optional uint32             sd_size         = 5;
    optional uint32             bl_size         = 6;
    optional uint32             app_size        = 7;

    optional Hash               hash            = 8;

    optional bool               is_debug        = 9 [default = false];
    repeated BootValidation     boot_validation = 10;
}

// Command type
message Command {
    optional OpCode         op_code = 1;
    optional InitCommand    init    = 2;
}

// Signed command types
enum SignatureType {
    ECDSA_P256_SHA256   = 0;
    ED25519             = 1;
}

message SignedCommand {
    required Command        command         = 1;
    required SignatureType  signature_type  = 2;
    required bytes          signature       = 3;
}

// Parent packet type
message Packet {
    optional Command        command         = 1;
    optional SignedCommand  signed_command  = 2;
}
