<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=iso-8859-1">
<title>The Protothreads Library 1.4: The Protothreads Library</title>
<link href="doxygen.css" rel="stylesheet" type="text/css">
<link href="tabs.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.4.6 -->
<div class="tabs">
  <ul>
    <li id="current"><a href="main.html"><span>Main&nbsp;Page</span></a></li>
    <li><a href="modules.html"><span>Modules</span></a></li>
    <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    <li><a href="files.html"><span>Files</span></a></li>
  </ul></div>
<h1>The Protothreads Library</h1>
<p>
<dl compact><dt><b>Author:</b></dt><dd>Adam Dunkels &lt;<a href="mailto:<EMAIL>"><EMAIL></a>&gt;</dd></dl>
Protothreads are a type of lightweight stackless threads designed for severly memory constrained systems such as deeply embedded systems or sensor network nodes. Protothreads provides linear code execution for event-driven systems implemented in C. Protothreads can be used with or without an RTOS.<p>
Protothreads are a extremely lightweight, stackless type of threads that provides a blocking context on top of an event-driven system, without the overhead of per-thread stacks. The purpose of protothreads is to implement sequential flow of control without complex state machines or full multi-threading. Protothreads provides conditional blocking inside C functions.<p>
Main features:<p>
<ul>
<li>No machine specific code - the protothreads library is pure C</li></ul>
<p>
<ul>
<li>Does not use error-prone functions such as longjmp()</li></ul>
<p>
<ul>
<li>Very small RAM overhead - only two bytes per protothread</li></ul>
<p>
<ul>
<li>Can be used with or without an OS</li></ul>
<p>
<ul>
<li>Provides blocking wait without full multi-threading or stack-switching</li></ul>
<p>
Examples applications:<p>
<ul>
<li>Memory constrained systems</li></ul>
<p>
<ul>
<li>Event-driven protocol stacks</li></ul>
<p>
<ul>
<li>Deeply embedded systems</li></ul>
<p>
<ul>
<li>Sensor network nodes</li></ul>
<p>
<dl compact><dt><b>See also:</b></dt><dd><a class="el" href="a00015.html">Example programs</a> <p>
<a class="el" href="a00014.html">Protothreads API documentation</a></dd></dl>
The protothreads library is released under a BSD-style license that allows for both non-commercial and commercial usage. The only requirement is that credit is given.<p>
More information and new version of the code can be found at the Protothreads homepage:<p>
<a href="http://www.sics.se/~adam/pt/">http://www.sics.se/~adam/pt/</a><h2><a class="anchor" name="authors">
Authors</a></h2>
The protothreads library was written by Adam Dunkels &lt;<a href="mailto:<EMAIL>"><EMAIL></a>&gt; with support from Oliver Schmidt &lt;<a href="mailto:<EMAIL>"><EMAIL></a>&gt;.<h2><a class="anchor" name="using">
Using protothreads</a></h2>
Using protothreads in a project is easy: simply copy the files <a class="el" href="a00013.html">pt.h</a>, <a class="el" href="a00011.html">lc.h</a> and lc-switch.h into the include files directory of the project, and #include "pt.h" in all files that should use protothreads.<h2><a class="anchor" name="pt-desc">
Protothreads</a></h2>
Protothreads are a extremely lightweight, stackless threads that provides a blocking context on top of an event-driven system, without the overhead of per-thread stacks. The purpose of protothreads is to implement sequential flow of control without using complex state machines or full multi-threading. Protothreads provides conditional blocking inside a C function.<p>
In memory constrained systems, such as deeply embedded systems, traditional multi-threading may have a too large memory overhead. In traditional multi-threading, each thread requires its own stack, that typically is over-provisioned. The stacks may use large parts of the available memory.<p>
The main advantage of protothreads over ordinary threads is that protothreads are very lightweight: a protothread does not require its own stack. Rather, all protothreads run on the same stack and context switching is done by stack rewinding. This is advantageous in memory constrained systems, where a stack for a thread might use a large part of the available memory. A protothread only requires only two bytes of memory per protothread. Moreover, protothreads are implemented in pure C and do not require any machine-specific assembler code.<p>
A protothread runs within a single C function and cannot span over other functions. A protothread may call normal C functions, but cannot block inside a called function. Blocking inside nested function calls is instead made by spawning a separate protothread for each potentially blocking function. The advantage of this approach is that blocking is explicit: the programmer knows exactly which functions that block that which functions the never blocks.<p>
Protothreads are similar to asymmetric co-routines. The main difference is that co-routines uses a separate stack for each co-routine, whereas protothreads are stackless. The most similar mechanism to protothreads are Python generators. These are also stackless constructs, but have a different purpose. Protothreads provides blocking contexts inside a C function, whereas Python generators provide multiple exit points from a generator function.<h2><a class="anchor" name="pt-autovars">
Local variables</a></h2>
<dl compact><dt><b>Note:</b></dt><dd>Because protothreads do not save the stack context across a blocking call, local variables are not preserved when the protothread blocks. This means that local variables should be used with utmost care - if in doubt, do not use local variables inside a protothread!</dd></dl>
<h2><a class="anchor" name="pt-scheduling">
Scheduling</a></h2>
A protothread is driven by repeated calls to the function in which the protothread is running. Each time the function is called, the protothread will run until it blocks or exits. Thus the scheduling of protothreads is done by the application that uses protothreads.<h2><a class="anchor" name="pt-impl">
Implementation</a></h2>
Protothreads are implemented using local continuations. A local continuation represents the current state of execution at a particular place in the program, but does not provide any call history or local variables. A local continuation can be set in a specific function to capture the state of the function. After a local continuation has been set can be resumed in order to restore the state of the function at the point where the local continuation was set.<p>
Local continuations can be implemented in a variety of ways:<p>
<ol type=1>
<li>by using machine specific assembler code,</li><li>by using standard C constructs, or</li><li>by using compiler extensions.</li></ol>
<p>
The first way works by saving and restoring the processor state, except for stack pointers, and requires between 16 and 32 bytes of memory per protothread. The exact amount of memory required depends on the architecture.<p>
The standard C implementation requires only two bytes of state per protothread and utilizes the C switch() statement in a non-obvious way that is similar to Duff's device. This implementation does, however, impose a slight restriction to the code that uses protothreads: a protothread cannot perform a blocking wait (<a class="el" href="a00014.html#g99e43010ec61327164466aa2d902de45">PT_WAIT_UNTIL()</a> or <a class="el" href="a00014.html#g155cba6121323726d02c00284428fed6">PT_YIELD()</a>) inside a switch() statement.<p>
Certain compilers has C extensions that can be used to implement protothreads. GCC supports label pointers that can be used for this purpose. With this implementation, protothreads require 4 bytes of RAM per protothread. <hr size="1"><address style="align: right;"><small>Generated on Mon Oct 2 10:06:29 2006 for The Protothreads Library 1.4 by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.4.6 </small></address>
</body>
</html>
