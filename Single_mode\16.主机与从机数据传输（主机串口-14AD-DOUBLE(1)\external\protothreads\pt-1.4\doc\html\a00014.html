<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=iso-8859-1">
<title>The Protothreads Library 1.4: Protothreads</title>
<link href="doxygen.css" rel="stylesheet" type="text/css">
<link href="tabs.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.4.6 -->
<div class="tabs">
  <ul>
    <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
    <li><a href="modules.html"><span>Modules</span></a></li>
    <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    <li><a href="files.html"><span>Files</span></a></li>
  </ul></div>
<h1>Protothreads</h1><hr><a name="_details"></a><h2>Detailed Description</h2>
Protothreads are implemented in a single header file, <a class="el" href="a00013.html">pt.h</a>, which includes the local continuations header file, <a class="el" href="a00011.html">lc.h</a>. 
<p>
This file in turn includes the actual implementation of local continuations, which typically also is contained in a single header file. 
<p>
<table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Files</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">file &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00013.html">pt.h</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Protothreads implementation. <br></td></tr>

<p>
<tr><td colspan="2"><br><h2>Modules</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00016.html">Protothread semaphores</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">This module implements counting semaphores on top of protothreads. <br></td></tr>

<p>
<tr><td class="memItemLeft" nowrap align="right" valign="top">&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00017.html">Local continuations</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Local continuations form the basis for implementing protothreads. <br></td></tr>

<p>
<tr><td colspan="2"><br><h2>Data Structures</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00005.html">pt</a></td></tr>

<tr><td colspan="2"><br><h2>Initialization</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">#define&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00014.html#ge6bae7dc0225468c8a5ac269df549892">PT_INIT</a>(<a class="el" href="a00005.html">pt</a>)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Initialize a protothread.  <a href="#ge6bae7dc0225468c8a5ac269df549892"></a><br></td></tr>
<tr><td colspan="2"><br><h2>Declaration and definition</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">#define&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00014.html#g3d4c8bd4aada659eb34f5d2ffd3e7901">PT_THREAD</a>(name_args)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Declaration of a protothread.  <a href="#g3d4c8bd4aada659eb34f5d2ffd3e7901"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">#define&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00014.html#g2ffbb9e554e08a343ae2f9de4bedfdfc">PT_BEGIN</a>(<a class="el" href="a00005.html">pt</a>)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Declare the start of a protothread inside the C function implementing the protothread.  <a href="#g2ffbb9e554e08a343ae2f9de4bedfdfc"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">#define&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00014.html#g7b04a0035bef29d905496c23bae066d2">PT_END</a>(<a class="el" href="a00005.html">pt</a>)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Declare the end of a protothread.  <a href="#g7b04a0035bef29d905496c23bae066d2"></a><br></td></tr>
<tr><td colspan="2"><br><h2>Blocked wait</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">#define&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00014.html#g99e43010ec61327164466aa2d902de45">PT_WAIT_UNTIL</a>(<a class="el" href="a00005.html">pt</a>, condition)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Block and wait until condition is true.  <a href="#g99e43010ec61327164466aa2d902de45"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">#define&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00014.html#gad14bbbf092b90aa0a5a4f9169504a8d">PT_WAIT_WHILE</a>(<a class="el" href="a00005.html">pt</a>, cond)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Block and wait while condition is true.  <a href="#gad14bbbf092b90aa0a5a4f9169504a8d"></a><br></td></tr>
<tr><td colspan="2"><br><h2>Hierarchical protothreads</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">#define&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00014.html#g2f8f70c30b9ee08a103fbd69a4365c4c">PT_WAIT_THREAD</a>(<a class="el" href="a00005.html">pt</a>, thread)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Block and wait until a child protothread completes.  <a href="#g2f8f70c30b9ee08a103fbd69a4365c4c"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">#define&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00014.html#g9e97a0b4d5cc7764d8e19758f5da53ae">PT_SPAWN</a>(<a class="el" href="a00005.html">pt</a>, child, thread)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Spawn a child protothread and wait until it exits.  <a href="#g9e97a0b4d5cc7764d8e19758f5da53ae"></a><br></td></tr>
<tr><td colspan="2"><br><h2>Exiting and restarting</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">#define&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00014.html#gcd3ac045f0a4ae63412e3b3d8780e8ab">PT_RESTART</a>(<a class="el" href="a00005.html">pt</a>)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Restart the protothread.  <a href="#gcd3ac045f0a4ae63412e3b3d8780e8ab"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">#define&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00014.html#g905451249dca72ce0385bf2a9ff178ee">PT_EXIT</a>(<a class="el" href="a00005.html">pt</a>)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Exit the protothread.  <a href="#g905451249dca72ce0385bf2a9ff178ee"></a><br></td></tr>
<tr><td colspan="2"><br><h2>Calling a protothread</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">#define&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00014.html#gfa82b860a64b67d25ab3abc21811896f">PT_SCHEDULE</a>(f)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Schedule a protothread.  <a href="#gfa82b860a64b67d25ab3abc21811896f"></a><br></td></tr>
<tr><td colspan="2"><br><h2>Yielding from a protothread</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">#define&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00014.html#g155cba6121323726d02c00284428fed6">PT_YIELD</a>(<a class="el" href="a00005.html">pt</a>)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Yield from the current protothread.  <a href="#g155cba6121323726d02c00284428fed6"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">#define&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00014.html#ge3c821e3a388615528efda9d23c7d115">PT_YIELD_UNTIL</a>(<a class="el" href="a00005.html">pt</a>, cond)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Yield from the protothread until a condition occurs.  <a href="#ge3c821e3a388615528efda9d23c7d115"></a><br></td></tr>
<tr><td colspan="2"><br><h2>Defines</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="anchor" name="g7b5319b5b65761a845fcd1500fde4cdc"></a><!-- doxytag: member="pt::PT_WAITING" ref="g7b5319b5b65761a845fcd1500fde4cdc" args="" -->
#define&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00014.html#g7b5319b5b65761a845fcd1500fde4cdc">PT_WAITING</a>&nbsp;&nbsp;&nbsp;0</td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="anchor" name="ge469332907e0617d72d5e2dd4297119d"></a><!-- doxytag: member="pt::PT_YIELDED" ref="ge469332907e0617d72d5e2dd4297119d" args="" -->
#define&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00014.html#ge469332907e0617d72d5e2dd4297119d">PT_YIELDED</a>&nbsp;&nbsp;&nbsp;1</td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="anchor" name="gcfae9053e5c107a1aed6b228c917d2ea"></a><!-- doxytag: member="pt::PT_EXITED" ref="gcfae9053e5c107a1aed6b228c917d2ea" args="" -->
#define&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00014.html#gcfae9053e5c107a1aed6b228c917d2ea">PT_EXITED</a>&nbsp;&nbsp;&nbsp;2</td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="anchor" name="g9ff1e8936a8a26bff54c05f8a989b93b"></a><!-- doxytag: member="pt::PT_ENDED" ref="g9ff1e8936a8a26bff54c05f8a989b93b" args="" -->
#define&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00014.html#g9ff1e8936a8a26bff54c05f8a989b93b">PT_ENDED</a>&nbsp;&nbsp;&nbsp;3</td></tr>

</table>
<hr><h2>Define Documentation</h2>
<a class="anchor" name="g2ffbb9e554e08a343ae2f9de4bedfdfc"></a><!-- doxytag: member="pt.h::PT_BEGIN" ref="g2ffbb9e554e08a343ae2f9de4bedfdfc" args="(pt)" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top">#define PT_BEGIN          </td>
          <td class="md" valign="top">(&nbsp;</td>
          <td class="md" nowrap valign="top"><a class="el" href="a00005.html">pt</a>&nbsp;</td>
          <td class="mdname1" valign="top" nowrap>          </td>
          <td class="md" valign="top">&nbsp;)&nbsp;</td>
          <td class="md" nowrap></td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
Declare the start of a protothread inside the C function implementing the protothread. 
<p>
This macro is used to declare the starting point of a protothread. It should be placed at the start of the function in which the protothread runs. All C statements above the <a class="el" href="a00014.html#g2ffbb9e554e08a343ae2f9de4bedfdfc">PT_BEGIN()</a> invokation will be executed each time the protothread is scheduled.<p>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>pt</em>&nbsp;</td><td>A pointer to the protothread control structure. </td></tr>
  </table>
</dl>

<p>
Definition at line <a class="el" href="a00022.html#l00115">115</a> of file <a class="el" href="a00022.html">pt.h</a>.    </td>
  </tr>
</table>
<a class="anchor" name="g7b04a0035bef29d905496c23bae066d2"></a><!-- doxytag: member="pt.h::PT_END" ref="g7b04a0035bef29d905496c23bae066d2" args="(pt)" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top">#define PT_END          </td>
          <td class="md" valign="top">(&nbsp;</td>
          <td class="md" nowrap valign="top"><a class="el" href="a00005.html">pt</a>&nbsp;</td>
          <td class="mdname1" valign="top" nowrap>          </td>
          <td class="md" valign="top">&nbsp;)&nbsp;</td>
          <td class="md" nowrap></td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
Declare the end of a protothread. 
<p>
This macro is used for declaring that a protothread ends. It must always be used together with a matching <a class="el" href="a00014.html#g2ffbb9e554e08a343ae2f9de4bedfdfc">PT_BEGIN()</a> macro.<p>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>pt</em>&nbsp;</td><td>A pointer to the protothread control structure. </td></tr>
  </table>
</dl>

<p>
Definition at line <a class="el" href="a00022.html#l00127">127</a> of file <a class="el" href="a00022.html">pt.h</a>.    </td>
  </tr>
</table>
<a class="anchor" name="g905451249dca72ce0385bf2a9ff178ee"></a><!-- doxytag: member="pt.h::PT_EXIT" ref="g905451249dca72ce0385bf2a9ff178ee" args="(pt)" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top">#define PT_EXIT          </td>
          <td class="md" valign="top">(&nbsp;</td>
          <td class="md" nowrap valign="top"><a class="el" href="a00005.html">pt</a>&nbsp;</td>
          <td class="mdname1" valign="top" nowrap>          </td>
          <td class="md" valign="top">&nbsp;)&nbsp;</td>
          <td class="md" nowrap></td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
Exit the protothread. 
<p>
This macro causes the protothread to exit. If the protothread was spawned by another protothread, the parent protothread will become unblocked and can continue to run.<p>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>pt</em>&nbsp;</td><td>A pointer to the protothread control structure. </td></tr>
  </table>
</dl>

<p>
Definition at line <a class="el" href="a00022.html#l00246">246</a> of file <a class="el" href="a00022.html">pt.h</a>.    </td>
  </tr>
</table>
<a class="anchor" name="ge6bae7dc0225468c8a5ac269df549892"></a><!-- doxytag: member="pt.h::PT_INIT" ref="ge6bae7dc0225468c8a5ac269df549892" args="(pt)" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top">#define PT_INIT          </td>
          <td class="md" valign="top">(&nbsp;</td>
          <td class="md" nowrap valign="top"><a class="el" href="a00005.html">pt</a>&nbsp;</td>
          <td class="mdname1" valign="top" nowrap>          </td>
          <td class="md" valign="top">&nbsp;)&nbsp;</td>
          <td class="md" nowrap></td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
Initialize a protothread. 
<p>
Initializes a protothread. Initialization must be done prior to starting to execute the protothread.<p>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>pt</em>&nbsp;</td><td>A pointer to the protothread control structure.</td></tr>
  </table>
</dl>
<dl compact><dt><b>See also:</b></dt><dd><a class="el" href="a00014.html#g9e97a0b4d5cc7764d8e19758f5da53ae">PT_SPAWN()</a> </dd></dl>

<p>
Definition at line <a class="el" href="a00022.html#l00080">80</a> of file <a class="el" href="a00022.html">pt.h</a>.    </td>
  </tr>
</table>
<a class="anchor" name="gcd3ac045f0a4ae63412e3b3d8780e8ab"></a><!-- doxytag: member="pt.h::PT_RESTART" ref="gcd3ac045f0a4ae63412e3b3d8780e8ab" args="(pt)" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top">#define PT_RESTART          </td>
          <td class="md" valign="top">(&nbsp;</td>
          <td class="md" nowrap valign="top"><a class="el" href="a00005.html">pt</a>&nbsp;</td>
          <td class="mdname1" valign="top" nowrap>          </td>
          <td class="md" valign="top">&nbsp;)&nbsp;</td>
          <td class="md" nowrap></td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
Restart the protothread. 
<p>
This macro will block and cause the running protothread to restart its execution at the place of the <a class="el" href="a00014.html#g2ffbb9e554e08a343ae2f9de4bedfdfc">PT_BEGIN()</a> call.<p>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>pt</em>&nbsp;</td><td>A pointer to the protothread control structure. </td></tr>
  </table>
</dl>

<p>
Definition at line <a class="el" href="a00022.html#l00229">229</a> of file <a class="el" href="a00022.html">pt.h</a>.    </td>
  </tr>
</table>
<a class="anchor" name="gfa82b860a64b67d25ab3abc21811896f"></a><!-- doxytag: member="pt.h::PT_SCHEDULE" ref="gfa82b860a64b67d25ab3abc21811896f" args="(f)" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top">#define PT_SCHEDULE          </td>
          <td class="md" valign="top">(&nbsp;</td>
          <td class="md" nowrap valign="top">f&nbsp;</td>
          <td class="mdname1" valign="top" nowrap>          </td>
          <td class="md" valign="top">&nbsp;)&nbsp;</td>
          <td class="md" nowrap></td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
Schedule a protothread. 
<p>
This function shedules a protothread. The return value of the function is non-zero if the protothread is running or zero if the protothread has exited.<p>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>f</em>&nbsp;</td><td>The call to the C function implementing the protothread to be scheduled </td></tr>
  </table>
</dl>

<p>
Definition at line <a class="el" href="a00022.html#l00271">271</a> of file <a class="el" href="a00022.html">pt.h</a>.    </td>
  </tr>
</table>
<a class="anchor" name="g9e97a0b4d5cc7764d8e19758f5da53ae"></a><!-- doxytag: member="pt.h::PT_SPAWN" ref="g9e97a0b4d5cc7764d8e19758f5da53ae" args="(pt, child, thread)" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top">#define PT_SPAWN          </td>
          <td class="md" valign="top">(&nbsp;</td>
          <td class="md" nowrap valign="top"><a class="el" href="a00005.html">pt</a>,         <tr>
          <td class="md" nowrap align="right"></td>
          <td class="md"></td>
          <td class="md" nowrap>child,         <tr>
          <td class="md" nowrap align="right"></td>
          <td class="md"></td>
          <td class="md" nowrap>thread&nbsp;</td>
          <td class="mdname1" valign="top" nowrap>          </td>
          <td class="md" valign="top">&nbsp;)&nbsp;</td>
          <td class="md" nowrap></td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
Spawn a child protothread and wait until it exits. 
<p>
This macro spawns a child protothread and waits until it exits. The macro can only be used within a protothread.<p>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>pt</em>&nbsp;</td><td>A pointer to the protothread control structure. </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>child</em>&nbsp;</td><td>A pointer to the child protothread's control structure. </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>thread</em>&nbsp;</td><td>The child protothread with arguments </td></tr>
  </table>
</dl>

<p>
Definition at line <a class="el" href="a00022.html#l00206">206</a> of file <a class="el" href="a00022.html">pt.h</a>.    </td>
  </tr>
</table>
<a class="anchor" name="g3d4c8bd4aada659eb34f5d2ffd3e7901"></a><!-- doxytag: member="pt.h::PT_THREAD" ref="g3d4c8bd4aada659eb34f5d2ffd3e7901" args="(name_args)" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top">#define PT_THREAD          </td>
          <td class="md" valign="top">(&nbsp;</td>
          <td class="md" nowrap valign="top">name_args&nbsp;</td>
          <td class="mdname1" valign="top" nowrap>          </td>
          <td class="md" valign="top">&nbsp;)&nbsp;</td>
          <td class="md" nowrap></td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
Declaration of a protothread. 
<p>
This macro is used to declare a protothread. All protothreads must be declared with this macro.<p>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>name_args</em>&nbsp;</td><td>The name and arguments of the C function implementing the protothread. </td></tr>
  </table>
</dl>

<p>
Definition at line <a class="el" href="a00022.html#l00100">100</a> of file <a class="el" href="a00022.html">pt.h</a>.    </td>
  </tr>
</table>
<a class="anchor" name="g2f8f70c30b9ee08a103fbd69a4365c4c"></a><!-- doxytag: member="pt.h::PT_WAIT_THREAD" ref="g2f8f70c30b9ee08a103fbd69a4365c4c" args="(pt, thread)" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top">#define PT_WAIT_THREAD          </td>
          <td class="md" valign="top">(&nbsp;</td>
          <td class="md" nowrap valign="top"><a class="el" href="a00005.html">pt</a>,         <tr>
          <td class="md" nowrap align="right"></td>
          <td class="md"></td>
          <td class="md" nowrap>thread&nbsp;</td>
          <td class="mdname1" valign="top" nowrap>          </td>
          <td class="md" valign="top">&nbsp;)&nbsp;</td>
          <td class="md" nowrap></td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
Block and wait until a child protothread completes. 
<p>
This macro schedules a child protothread. The current protothread will block until the child protothread completes.<p>
<dl compact><dt><b>Note:</b></dt><dd>The child protothread must be manually initialized with the <a class="el" href="a00014.html#ge6bae7dc0225468c8a5ac269df549892">PT_INIT()</a> function before this function is used.</dd></dl>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>pt</em>&nbsp;</td><td>A pointer to the protothread control structure. </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>thread</em>&nbsp;</td><td>The child protothread with arguments</td></tr>
  </table>
</dl>
<dl compact><dt><b>See also:</b></dt><dd><a class="el" href="a00014.html#g9e97a0b4d5cc7764d8e19758f5da53ae">PT_SPAWN()</a> </dd></dl>

<p>
Definition at line <a class="el" href="a00022.html#l00192">192</a> of file <a class="el" href="a00022.html">pt.h</a>.    </td>
  </tr>
</table>
<a class="anchor" name="g99e43010ec61327164466aa2d902de45"></a><!-- doxytag: member="pt.h::PT_WAIT_UNTIL" ref="g99e43010ec61327164466aa2d902de45" args="(pt, condition)" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top">#define PT_WAIT_UNTIL          </td>
          <td class="md" valign="top">(&nbsp;</td>
          <td class="md" nowrap valign="top"><a class="el" href="a00005.html">pt</a>,         <tr>
          <td class="md" nowrap align="right"></td>
          <td class="md"></td>
          <td class="md" nowrap>condition&nbsp;</td>
          <td class="mdname1" valign="top" nowrap>          </td>
          <td class="md" valign="top">&nbsp;)&nbsp;</td>
          <td class="md" nowrap></td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
Block and wait until condition is true. 
<p>
This macro blocks the protothread until the specified condition is true.<p>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>pt</em>&nbsp;</td><td>A pointer to the protothread control structure. </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>condition</em>&nbsp;</td><td>The condition. </td></tr>
  </table>
</dl>

<p>
Definition at line <a class="el" href="a00022.html#l00148">148</a> of file <a class="el" href="a00022.html">pt.h</a>.    </td>
  </tr>
</table>
<a class="anchor" name="gad14bbbf092b90aa0a5a4f9169504a8d"></a><!-- doxytag: member="pt.h::PT_WAIT_WHILE" ref="gad14bbbf092b90aa0a5a4f9169504a8d" args="(pt, cond)" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top">#define PT_WAIT_WHILE          </td>
          <td class="md" valign="top">(&nbsp;</td>
          <td class="md" nowrap valign="top"><a class="el" href="a00005.html">pt</a>,         <tr>
          <td class="md" nowrap align="right"></td>
          <td class="md"></td>
          <td class="md" nowrap>cond&nbsp;</td>
          <td class="mdname1" valign="top" nowrap>          </td>
          <td class="md" valign="top">&nbsp;)&nbsp;</td>
          <td class="md" nowrap></td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
Block and wait while condition is true. 
<p>
This function blocks and waits while condition is true. See <a class="el" href="a00014.html#g99e43010ec61327164466aa2d902de45">PT_WAIT_UNTIL()</a>.<p>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>pt</em>&nbsp;</td><td>A pointer to the protothread control structure. </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>cond</em>&nbsp;</td><td>The condition. </td></tr>
  </table>
</dl>

<p>
Definition at line <a class="el" href="a00022.html#l00167">167</a> of file <a class="el" href="a00022.html">pt.h</a>.    </td>
  </tr>
</table>
<a class="anchor" name="g155cba6121323726d02c00284428fed6"></a><!-- doxytag: member="pt.h::PT_YIELD" ref="g155cba6121323726d02c00284428fed6" args="(pt)" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top">#define PT_YIELD          </td>
          <td class="md" valign="top">(&nbsp;</td>
          <td class="md" nowrap valign="top"><a class="el" href="a00005.html">pt</a>&nbsp;</td>
          <td class="mdname1" valign="top" nowrap>          </td>
          <td class="md" valign="top">&nbsp;)&nbsp;</td>
          <td class="md" nowrap></td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
Yield from the current protothread. 
<p>
This function will yield the protothread, thereby allowing other processing to take place in the system.<p>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>pt</em>&nbsp;</td><td>A pointer to the protothread control structure. </td></tr>
  </table>
</dl>

<p>
Definition at line <a class="el" href="a00022.html#l00290">290</a> of file <a class="el" href="a00022.html">pt.h</a>.    </td>
  </tr>
</table>
<a class="anchor" name="ge3c821e3a388615528efda9d23c7d115"></a><!-- doxytag: member="pt.h::PT_YIELD_UNTIL" ref="ge3c821e3a388615528efda9d23c7d115" args="(pt, cond)" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top">#define PT_YIELD_UNTIL          </td>
          <td class="md" valign="top">(&nbsp;</td>
          <td class="md" nowrap valign="top"><a class="el" href="a00005.html">pt</a>,         <tr>
          <td class="md" nowrap align="right"></td>
          <td class="md"></td>
          <td class="md" nowrap>cond&nbsp;</td>
          <td class="mdname1" valign="top" nowrap>          </td>
          <td class="md" valign="top">&nbsp;)&nbsp;</td>
          <td class="md" nowrap></td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
Yield from the protothread until a condition occurs. 
<p>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>pt</em>&nbsp;</td><td>A pointer to the protothread control structure. </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>cond</em>&nbsp;</td><td>The condition.</td></tr>
  </table>
</dl>
This function will yield the protothread, until the specified condition evaluates to true. 
<p>
Definition at line <a class="el" href="a00022.html#l00310">310</a> of file <a class="el" href="a00022.html">pt.h</a>.    </td>
  </tr>
</table>
<hr size="1"><address style="align: right;"><small>Generated on Mon Oct 2 10:06:29 2006 for The Protothreads Library 1.4 by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.4.6 </small></address>
</body>
</html>
