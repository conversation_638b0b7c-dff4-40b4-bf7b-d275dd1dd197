<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html lang="ja">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta http-equiv="Content-Style-Type" content="text/css">
<link rel="up" title="FatFs" href="../00index_j.html">
<link rel="alternate" hreflang="en" title="English" href="../en/close.html">
<link rel="stylesheet" href="../css_j.css" type="text/css" media="screen" title="ELM Default">
<title>FatFs - f_closedir</title>
</head>

<body>

<div class="para func">
<h2>f_closedir</h2>
<p>ディレクトリを閉じます。</p>
<pre>
FRESULT f_closedir (
  DIR* <span class="arg">dp</span>     <span class="c">/* [IN] ディレクトリ オブジェクトへのポインタ */</span>
);
</pre>
</div>

<div class="para arg">
<h4>引数</h4>
<dl class="par">
<dt>dp</dt>
<dd>閉じようとするディレクトリのディレクトリ オブジェクト構造体へのポインタを指定します。</dd>
</dl>
</div>


<div class="para ret">
<h4>戻り値</h4>
<p>
<a href="rc.html#ok">FR_OK</a>,
<a href="rc.html#ie">FR_INT_ERR</a>,
<a href="rc.html#io">FR_INVALID_OBJECT</a>,
<a href="rc.html#tm">FR_TIMEOUT</a>
</p>
</div>


<div class="para desc">
<h4>解説</h4>
<p>ディレクトリを閉じます。関数が正常終了すると、そのディレクトリ オブジェクトは無効になり、そのメモリも解放できます。</p>
<p><tt>_FS_LOCK</tt>オプションが選択されていない場合は、この処理を行わずにディレクトリ オブジェクトを破棄することもできます。しかし、これは将来の互換性の点で推奨はされません。</p>
</div>


<div class="para comp">
<h4>対応情報</h4>
<p><tt>_FS_MINIMIZE &lt;= 1</tt>のとき使用可能になります。</p>
</div>


<div class="para ref">
<h4>参照</h4>
<tt><a href="opendir.html">f_opendir</a>, <a href="readdir.html">f_readdir</a>, <a href="sdir.html">DIR</a></tt>
</div>

<p class="foot"><a href="../00index_j.html">戻る</a></p>
</body>
</html>
