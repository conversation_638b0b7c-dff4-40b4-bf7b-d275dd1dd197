<!doctype html public "-//w3c//dtd html 4.0 transitional//en">
<html><head>
<title>Static Call Graph - [.\_build\nrf52832_xxaa.axf]</title></head>
<body><HR>
<H1>Static Call Graph for image .\_build\nrf52832_xxaa.axf</H1><HR>
<BR><P>#&#060CALLGRAPH&#062# ARM Linker, 5060960: Last Updated: Sun Jan 07 20:33:28 2024
<BR><P>
<H3>Maximum Stack Usage =        544 bytes + Unknown(Cycles, Untraceable Function Pointers)</H3><H3>
Call chain for Maximum Stack Depth:</H3>
nrf_sdh_ble_evts_poll &rArr; app_error_handler_bare
<P>
<H3>
Mutually Recursive functions
</H3> <LI><a href="#[1]">NMI_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[1]">NMI_Handler</a><BR>
 <LI><a href="#[2]">HardFault_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[2]">HardFault_Handler</a><BR>
 <LI><a href="#[3]">MemoryManagement_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[3]">MemoryManagement_Handler</a><BR>
 <LI><a href="#[4]">BusFault_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[4]">BusFault_Handler</a><BR>
 <LI><a href="#[5]">UsageFault_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[5]">UsageFault_Handler</a><BR>
 <LI><a href="#[6]">SVC_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[6]">SVC_Handler</a><BR>
 <LI><a href="#[7]">DebugMon_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[7]">DebugMon_Handler</a><BR>
 <LI><a href="#[8]">PendSV_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[8]">PendSV_Handler</a><BR>
 <LI><a href="#[9]">SysTick_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[9]">SysTick_Handler</a><BR>
 <LI><a href="#[19]">CCM_AAR_IRQHandler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[19]">CCM_AAR_IRQHandler</a><BR>
 <LI><a href="#[54]">__asm___12_nrf_atfifo_c_51f461e1__nrf_atfifo_wspace_close</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[54]">__asm___12_nrf_atfifo_c_51f461e1__nrf_atfifo_wspace_close</a><BR>
 <LI><a href="#[55]">__asm___12_nrf_atfifo_c_51f461e1__nrf_atfifo_rspace_close</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[55]">__asm___12_nrf_atfifo_c_51f461e1__nrf_atfifo_rspace_close</a><BR>
</UL>
<P>
<H3>
Function Pointers
</H3><UL>
 <LI><a href="#[4]">BusFault_Handler</a> from arm_startup_nrf52.o(.text) referenced from arm_startup_nrf52.o(RESET)
 <LI><a href="#[19]">CCM_AAR_IRQHandler</a> from arm_startup_nrf52.o(.text) referenced from arm_startup_nrf52.o(RESET)
 <LI><a href="#[1d]">COMP_LPCOMP_IRQHandler</a> from arm_startup_nrf52.o(.text) referenced from arm_startup_nrf52.o(RESET)
 <LI><a href="#[7]">DebugMon_Handler</a> from arm_startup_nrf52.o(.text) referenced from arm_startup_nrf52.o(RESET)
 <LI><a href="#[18]">ECB_IRQHandler</a> from arm_startup_nrf52.o(.text) referenced from arm_startup_nrf52.o(RESET)
 <LI><a href="#[2e]">FPU_IRQHandler</a> from arm_startup_nrf52.o(.text) referenced from arm_startup_nrf52.o(RESET)
 <LI><a href="#[10]">GPIOTE_IRQHandler</a> from nrfx_gpiote.o(i.GPIOTE_IRQHandler) referenced from arm_startup_nrf52.o(RESET)
 <LI><a href="#[2]">HardFault_Handler</a> from arm_startup_nrf52.o(.text) referenced from arm_startup_nrf52.o(RESET)
 <LI><a href="#[2d]">I2S_IRQHandler</a> from arm_startup_nrf52.o(.text) referenced from arm_startup_nrf52.o(RESET)
 <LI><a href="#[28]">MWU_IRQHandler</a> from arm_startup_nrf52.o(.text) referenced from arm_startup_nrf52.o(RESET)
 <LI><a href="#[3]">MemoryManagement_Handler</a> from arm_startup_nrf52.o(.text) referenced from arm_startup_nrf52.o(RESET)
 <LI><a href="#[f]">NFCT_IRQHandler</a> from arm_startup_nrf52.o(.text) referenced from arm_startup_nrf52.o(RESET)
 <LI><a href="#[1]">NMI_Handler</a> from arm_startup_nrf52.o(.text) referenced from arm_startup_nrf52.o(RESET)
 <LI><a href="#[27]">PDM_IRQHandler</a> from arm_startup_nrf52.o(.text) referenced from arm_startup_nrf52.o(RESET)
 <LI><a href="#[a]">POWER_CLOCK_IRQHandler</a> from nrfx_clock.o(i.POWER_CLOCK_IRQHandler) referenced from arm_startup_nrf52.o(RESET)
 <LI><a href="#[26]">PWM0_IRQHandler</a> from arm_startup_nrf52.o(.text) referenced from arm_startup_nrf52.o(RESET)
 <LI><a href="#[29]">PWM1_IRQHandler</a> from arm_startup_nrf52.o(.text) referenced from arm_startup_nrf52.o(RESET)
 <LI><a href="#[2a]">PWM2_IRQHandler</a> from arm_startup_nrf52.o(.text) referenced from arm_startup_nrf52.o(RESET)
 <LI><a href="#[8]">PendSV_Handler</a> from arm_startup_nrf52.o(.text) referenced from arm_startup_nrf52.o(RESET)
 <LI><a href="#[1c]">QDEC_IRQHandler</a> from arm_startup_nrf52.o(.text) referenced from arm_startup_nrf52.o(RESET)
 <LI><a href="#[b]">RADIO_IRQHandler</a> from arm_startup_nrf52.o(.text) referenced from arm_startup_nrf52.o(RESET)
 <LI><a href="#[17]">RNG_IRQHandler</a> from arm_startup_nrf52.o(.text) referenced from arm_startup_nrf52.o(RESET)
 <LI><a href="#[15]">RTC0_IRQHandler</a> from arm_startup_nrf52.o(.text) referenced from arm_startup_nrf52.o(RESET)
 <LI><a href="#[1b]">RTC1_IRQHandler</a> from drv_rtc.o(i.RTC1_IRQHandler) referenced from arm_startup_nrf52.o(RESET)
 <LI><a href="#[2c]">RTC2_IRQHandler</a> from arm_startup_nrf52.o(.text) referenced from arm_startup_nrf52.o(RESET)
 <LI><a href="#[0]">Reset_Handler</a> from arm_startup_nrf52.o(.text) referenced from arm_startup_nrf52.o(RESET)
 <LI><a href="#[11]">SAADC_IRQHandler</a> from arm_startup_nrf52.o(.text) referenced from arm_startup_nrf52.o(RESET)
 <LI><a href="#[d]">SPIM0_SPIS0_TWIM0_TWIS0_SPI0_TWI0_IRQHandler</a> from arm_startup_nrf52.o(.text) referenced from arm_startup_nrf52.o(RESET)
 <LI><a href="#[e]">SPIM1_SPIS1_TWIM1_TWIS1_SPI1_TWI1_IRQHandler</a> from arm_startup_nrf52.o(.text) referenced from arm_startup_nrf52.o(RESET)
 <LI><a href="#[2b]">SPIM2_SPIS2_SPI2_IRQHandler</a> from arm_startup_nrf52.o(.text) referenced from arm_startup_nrf52.o(RESET)
 <LI><a href="#[6]">SVC_Handler</a> from arm_startup_nrf52.o(.text) referenced from arm_startup_nrf52.o(RESET)
 <LI><a href="#[1e]">SWI0_EGU0_IRQHandler</a> from arm_startup_nrf52.o(.text) referenced from arm_startup_nrf52.o(RESET)
 <LI><a href="#[1f]">SWI1_EGU1_IRQHandler</a> from arm_startup_nrf52.o(.text) referenced from arm_startup_nrf52.o(RESET)
 <LI><a href="#[20]">SWI2_EGU2_IRQHandler</a> from nrf_sdh.o(i.SWI2_EGU2_IRQHandler) referenced from arm_startup_nrf52.o(RESET)
 <LI><a href="#[21]">SWI3_EGU3_IRQHandler</a> from arm_startup_nrf52.o(.text) referenced from arm_startup_nrf52.o(RESET)
 <LI><a href="#[22]">SWI4_EGU4_IRQHandler</a> from arm_startup_nrf52.o(.text) referenced from arm_startup_nrf52.o(RESET)
 <LI><a href="#[23]">SWI5_EGU5_IRQHandler</a> from arm_startup_nrf52.o(.text) referenced from arm_startup_nrf52.o(RESET)
 <LI><a href="#[9]">SysTick_Handler</a> from arm_startup_nrf52.o(.text) referenced from arm_startup_nrf52.o(RESET)
 <LI><a href="#[30]">SystemInit</a> from system_nrf52.o(i.SystemInit) referenced from arm_startup_nrf52.o(.text)
 <LI><a href="#[16]">TEMP_IRQHandler</a> from arm_startup_nrf52.o(.text) referenced from arm_startup_nrf52.o(RESET)
 <LI><a href="#[12]">TIMER0_IRQHandler</a> from arm_startup_nrf52.o(.text) referenced from arm_startup_nrf52.o(RESET)
 <LI><a href="#[13]">TIMER1_IRQHandler</a> from arm_startup_nrf52.o(.text) referenced from arm_startup_nrf52.o(RESET)
 <LI><a href="#[14]">TIMER2_IRQHandler</a> from arm_startup_nrf52.o(.text) referenced from arm_startup_nrf52.o(RESET)
 <LI><a href="#[24]">TIMER3_IRQHandler</a> from arm_startup_nrf52.o(.text) referenced from arm_startup_nrf52.o(RESET)
 <LI><a href="#[25]">TIMER4_IRQHandler</a> from arm_startup_nrf52.o(.text) referenced from arm_startup_nrf52.o(RESET)
 <LI><a href="#[c]">UARTE0_UART0_IRQHandler</a> from nrfx_prs.o(i.UARTE0_UART0_IRQHandler) referenced from arm_startup_nrf52.o(RESET)
 <LI><a href="#[5]">UsageFault_Handler</a> from arm_startup_nrf52.o(.text) referenced from arm_startup_nrf52.o(RESET)
 <LI><a href="#[1a]">WDT_IRQHandler</a> from arm_startup_nrf52.o(.text) referenced from arm_startup_nrf52.o(RESET)
 <LI><a href="#[31]">__main</a> from entry.o(.ARM.Collect$$$$00000000) referenced from arm_startup_nrf52.o(.text)
 <LI><a href="#[3e]">app_error_fault_handler</a> from app_error_weak.o(i.app_error_fault_handler) referenced from nrf_sdh.o(i.nrf_sdh_enable_request)
 <LI><a href="#[48]">ble_db_discovery_on_ble_evt</a> from ble_db_discovery.o(i.ble_db_discovery_on_ble_evt) referenced from main.o(sdh_ble_observers1)
 <LI><a href="#[4b]">ble_evt_handler</a> from bsp_btn_ble.o(i.ble_evt_handler) referenced from bsp_btn_ble.o(sdh_ble_observers1)
 <LI><a href="#[4d]">ble_evt_handler</a> from main.o(i.ble_evt_handler) referenced from main.o(sdh_ble_observers3)
 <LI><a href="#[39]">ble_nus_c_evt_handler</a> from main.o(i.ble_nus_c_evt_handler) referenced from main.o(i.main)
 <LI><a href="#[4c]">ble_nus_c_on_ble_evt</a> from ble_nus_c.o(i.ble_nus_c_on_ble_evt) referenced from main.o(sdh_ble_observers2)
 <LI><a href="#[40]">bsp_button_event_handler</a> from bsp.o(i.bsp_button_event_handler) referenced 4 times from bsp.o(.constdata)
 <LI><a href="#[3b]">clock_irq_handler</a> from nrf_drv_clock.o(i.clock_irq_handler) referenced from nrf_drv_clock.o(i.nrf_drv_clock_init)
 <LI><a href="#[45]">compare_func</a> from app_timer2.o(i.compare_func) referenced from app_timer2.o(.constdata)
 <LI><a href="#[38]">db_disc_handler</a> from main.o(i.db_disc_handler) referenced from main.o(i.main)
 <LI><a href="#[35]">discovery_error_handler</a> from ble_db_discovery.o(i.discovery_error_handler) referenced from ble_db_discovery.o(i.characteristics_discover)
 <LI><a href="#[35]">discovery_error_handler</a> from ble_db_discovery.o(i.discovery_error_handler) referenced from ble_db_discovery.o(i.descriptors_discover)
 <LI><a href="#[35]">discovery_error_handler</a> from ble_db_discovery.o(i.discovery_error_handler) referenced from ble_db_discovery.o(i.discovery_start)
 <LI><a href="#[35]">discovery_error_handler</a> from ble_db_discovery.o(i.discovery_error_handler) referenced from ble_db_discovery.o(i.on_srv_disc_completion)
 <LI><a href="#[34]">gatt_error_handler</a> from ble_nus_c.o(i.gatt_error_handler) referenced from ble_nus_c.o(i.ble_nus_c_tx_notif_enable)
 <LI><a href="#[36]">gatt_evt_handler</a> from main.o(i.gatt_evt_handler) referenced from main.o(i.gatt_init)
 <LI><a href="#[41]">gattc_write_alloc</a> from nrf_ble_gq.o(i.gattc_write_alloc) referenced from nrf_ble_gq.o(.constdata)
 <LI><a href="#[42]">gatts_hvx_alloc</a> from nrf_ble_gq.o(i.gatts_hvx_alloc) referenced from nrf_ble_gq.o(.constdata)
 <LI><a href="#[2f]">main</a> from main.o(i.main) referenced from entry9a.o(.ARM.Collect$$$$0000000B)
 <LI><a href="#[47]">nrf_ble_gatt_on_ble_evt</a> from nrf_ble_gatt.o(i.nrf_ble_gatt_on_ble_evt) referenced from main.o(sdh_ble_observers1)
 <LI><a href="#[4a]">nrf_ble_gq_on_ble_evt</a> from nrf_ble_gq.o(i.nrf_ble_gq_on_ble_evt) referenced from main.o(sdh_ble_observers1)
 <LI><a href="#[49]">nrf_ble_scan_on_ble_evt</a> from nrf_ble_scan.o(i.nrf_ble_scan_on_ble_evt) referenced from main.o(sdh_ble_observers1)
 <LI><a href="#[4f]">nrf_sdh_ble_evts_poll</a> from nrf_sdh_ble.o(i.nrf_sdh_ble_evts_poll) referenced from nrf_sdh_ble.o(sdh_stack_observers0)
 <LI><a href="#[50]">nrf_sdh_soc_evts_poll</a> from nrf_sdh_soc.o(i.nrf_sdh_soc_evts_poll) referenced from nrf_sdh_soc.o(sdh_stack_observers0)
 <LI><a href="#[43]">nrfx_uart_0_irq_handler</a> from nrfx_uart.o(i.nrfx_uart_0_irq_handler) referenced from nrfx_uart.o(.constdata)
 <LI><a href="#[44]">nrfx_uarte_0_irq_handler</a> from nrfx_uarte.o(i.nrfx_uarte_0_irq_handler) referenced from nrfx_uarte.o(.constdata)
 <LI><a href="#[3a]">nus_error_handler</a> from main.o(i.nus_error_handler) referenced from main.o(i.main)
 <LI><a href="#[32]">rtc_irq</a> from app_timer2.o(i.rtc_irq) referenced from app_timer2.o(i.app_timer_init)
 <LI><a href="#[3f]">scan_evt_handler</a> from main.o(i.scan_evt_handler) referenced from main.o(i.scan_init)
 <LI><a href="#[51]">sd_state_evt_handler</a> from nrf_drv_clock.o(i.sd_state_evt_handler) referenced from nrf_drv_clock.o(sdh_state_observers0)
 <LI><a href="#[46]">shutdown_handler</a> from main.o(i.shutdown_handler) referenced from main.o(pwr_mgmt_data1)
 <LI><a href="#[4e]">soc_evt_handler</a> from nrf_drv_clock.o(i.soc_evt_handler) referenced from nrf_drv_clock.o(sdh_soc_observers0)
 <LI><a href="#[37]">uart_event_handle</a> from main.o(i.uart_event_handle) referenced from main.o(i.main)
 <LI><a href="#[33]">uart_event_handler</a> from app_uart_fifo.o(i.uart_event_handler) referenced from app_uart_fifo.o(i.app_uart_init)
 <LI><a href="#[3d]">uart_evt_handler</a> from nrf_drv_uart.o(i.uart_evt_handler) referenced from nrf_drv_uart.o(i.nrf_drv_uart_init)
 <LI><a href="#[3c]">uarte_evt_handler</a> from nrf_drv_uart.o(i.uarte_evt_handler) referenced from nrf_drv_uart.o(i.nrf_drv_uart_init)
</UL>
<P>
<H3>
Global Symbols
</H3>
<P><STRONG><a name="[31]"></a>__main</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry.o(.ARM.Collect$$$$00000000))
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52.o(.text)
</UL>
<P><STRONG><a name="[121]"></a>_main_stk</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry2.o(.ARM.Collect$$$$00000001))

<P><STRONG><a name="[52]"></a>_main_scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry5.o(.ARM.Collect$$$$00000004))
<BR><BR>[Calls]<UL><LI><a href="#[53]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
</UL>

<P><STRONG><a name="[5c]"></a>__main_after_scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry5.o(.ARM.Collect$$$$00000004))
<BR><BR>[Called By]<UL><LI><a href="#[53]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
</UL>

<P><STRONG><a name="[122]"></a>_main_clock</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry7b.o(.ARM.Collect$$$$00000008))

<P><STRONG><a name="[123]"></a>_main_cpp_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry8b.o(.ARM.Collect$$$$0000000A))

<P><STRONG><a name="[124]"></a>_main_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry9a.o(.ARM.Collect$$$$0000000B))

<P><STRONG><a name="[125]"></a>__rt_lib_shutdown_fini</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry12b.o(.ARM.Collect$$$$0000000E))

<P><STRONG><a name="[126]"></a>__rt_final_cpp</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry10a.o(.ARM.Collect$$$$0000000F))

<P><STRONG><a name="[127]"></a>__rt_final_exit</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry11a.o(.ARM.Collect$$$$00000011))

<P><STRONG><a name="[c3]"></a>__asm___12_nrf_atfifo_c_51f461e1__nrf_atfifo_wspace_req</STRONG> (Thumb, 56 bytes, Stack size 0 bytes, nrf_atfifo.o(.emb_text))
<BR><BR>[Called By]<UL><LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_atfifo_item_alloc
</UL>

<P><STRONG><a name="[54]"></a>__asm___12_nrf_atfifo_c_51f461e1__nrf_atfifo_wspace_close</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, nrf_atfifo.o(.emb_text))
<BR><BR>[Calls]<UL><LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__asm___12_nrf_atfifo_c_51f461e1__nrf_atfifo_wspace_close
</UL>
<BR>[Called By]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_atfifo_item_put
<LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__asm___12_nrf_atfifo_c_51f461e1__nrf_atfifo_wspace_close
</UL>

<P><STRONG><a name="[c6]"></a>__asm___12_nrf_atfifo_c_51f461e1__nrf_atfifo_rspace_req</STRONG> (Thumb, 58 bytes, Stack size 0 bytes, nrf_atfifo.o(.emb_text))
<BR><BR>[Called By]<UL><LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_atfifo_item_get
</UL>

<P><STRONG><a name="[55]"></a>__asm___12_nrf_atfifo_c_51f461e1__nrf_atfifo_rspace_close</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, nrf_atfifo.o(.emb_text))
<BR><BR>[Calls]<UL><LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__asm___12_nrf_atfifo_c_51f461e1__nrf_atfifo_rspace_close
</UL>
<BR>[Called By]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_atfifo_item_free
<LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__asm___12_nrf_atfifo_c_51f461e1__nrf_atfifo_rspace_close
</UL>

<P><STRONG><a name="[128]"></a>__asm___12_nrf_atfifo_c_51f461e1__nrf_atfifo_space_clear</STRONG> (Thumb, 50 bytes, Stack size 0 bytes, nrf_atfifo.o(.emb_text), UNUSED)

<P><STRONG><a name="[0]"></a>Reset_Handler</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, arm_startup_nrf52.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52.o(RESET)
</UL>
<P><STRONG><a name="[1]"></a>NMI_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, arm_startup_nrf52.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NMI_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NMI_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52.o(RESET)
</UL>
<P><STRONG><a name="[2]"></a>HardFault_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, arm_startup_nrf52.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HardFault_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HardFault_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52.o(RESET)
</UL>
<P><STRONG><a name="[3]"></a>MemoryManagement_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, arm_startup_nrf52.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MemoryManagement_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MemoryManagement_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52.o(RESET)
</UL>
<P><STRONG><a name="[4]"></a>BusFault_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, arm_startup_nrf52.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BusFault_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BusFault_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52.o(RESET)
</UL>
<P><STRONG><a name="[5]"></a>UsageFault_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, arm_startup_nrf52.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsageFault_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsageFault_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52.o(RESET)
</UL>
<P><STRONG><a name="[6]"></a>SVC_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, arm_startup_nrf52.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SVC_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SVC_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52.o(RESET)
</UL>
<P><STRONG><a name="[7]"></a>DebugMon_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, arm_startup_nrf52.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DebugMon_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DebugMon_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52.o(RESET)
</UL>
<P><STRONG><a name="[8]"></a>PendSV_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, arm_startup_nrf52.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PendSV_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PendSV_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52.o(RESET)
</UL>
<P><STRONG><a name="[9]"></a>SysTick_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, arm_startup_nrf52.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52.o(RESET)
</UL>
<P><STRONG><a name="[19]"></a>CCM_AAR_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, arm_startup_nrf52.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[19]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CCM_AAR_IRQHandler
</UL>
<BR>[Called By]<UL><LI><a href="#[19]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CCM_AAR_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52.o(RESET)
</UL>
<P><STRONG><a name="[1d]"></a>COMP_LPCOMP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, arm_startup_nrf52.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52.o(RESET)
</UL>
<P><STRONG><a name="[18]"></a>ECB_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, arm_startup_nrf52.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52.o(RESET)
</UL>
<P><STRONG><a name="[2e]"></a>FPU_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, arm_startup_nrf52.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52.o(RESET)
</UL>
<P><STRONG><a name="[2d]"></a>I2S_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, arm_startup_nrf52.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52.o(RESET)
</UL>
<P><STRONG><a name="[28]"></a>MWU_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, arm_startup_nrf52.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52.o(RESET)
</UL>
<P><STRONG><a name="[f]"></a>NFCT_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, arm_startup_nrf52.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52.o(RESET)
</UL>
<P><STRONG><a name="[27]"></a>PDM_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, arm_startup_nrf52.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52.o(RESET)
</UL>
<P><STRONG><a name="[26]"></a>PWM0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, arm_startup_nrf52.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52.o(RESET)
</UL>
<P><STRONG><a name="[29]"></a>PWM1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, arm_startup_nrf52.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52.o(RESET)
</UL>
<P><STRONG><a name="[2a]"></a>PWM2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, arm_startup_nrf52.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52.o(RESET)
</UL>
<P><STRONG><a name="[1c]"></a>QDEC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, arm_startup_nrf52.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52.o(RESET)
</UL>
<P><STRONG><a name="[b]"></a>RADIO_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, arm_startup_nrf52.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52.o(RESET)
</UL>
<P><STRONG><a name="[17]"></a>RNG_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, arm_startup_nrf52.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52.o(RESET)
</UL>
<P><STRONG><a name="[15]"></a>RTC0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, arm_startup_nrf52.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52.o(RESET)
</UL>
<P><STRONG><a name="[2c]"></a>RTC2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, arm_startup_nrf52.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52.o(RESET)
</UL>
<P><STRONG><a name="[11]"></a>SAADC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, arm_startup_nrf52.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52.o(RESET)
</UL>
<P><STRONG><a name="[d]"></a>SPIM0_SPIS0_TWIM0_TWIS0_SPI0_TWI0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, arm_startup_nrf52.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52.o(RESET)
</UL>
<P><STRONG><a name="[e]"></a>SPIM1_SPIS1_TWIM1_TWIS1_SPI1_TWI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, arm_startup_nrf52.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52.o(RESET)
</UL>
<P><STRONG><a name="[2b]"></a>SPIM2_SPIS2_SPI2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, arm_startup_nrf52.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52.o(RESET)
</UL>
<P><STRONG><a name="[1e]"></a>SWI0_EGU0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, arm_startup_nrf52.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52.o(RESET)
</UL>
<P><STRONG><a name="[1f]"></a>SWI1_EGU1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, arm_startup_nrf52.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52.o(RESET)
</UL>
<P><STRONG><a name="[21]"></a>SWI3_EGU3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, arm_startup_nrf52.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52.o(RESET)
</UL>
<P><STRONG><a name="[22]"></a>SWI4_EGU4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, arm_startup_nrf52.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52.o(RESET)
</UL>
<P><STRONG><a name="[23]"></a>SWI5_EGU5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, arm_startup_nrf52.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52.o(RESET)
</UL>
<P><STRONG><a name="[16]"></a>TEMP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, arm_startup_nrf52.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52.o(RESET)
</UL>
<P><STRONG><a name="[12]"></a>TIMER0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, arm_startup_nrf52.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52.o(RESET)
</UL>
<P><STRONG><a name="[13]"></a>TIMER1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, arm_startup_nrf52.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52.o(RESET)
</UL>
<P><STRONG><a name="[14]"></a>TIMER2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, arm_startup_nrf52.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52.o(RESET)
</UL>
<P><STRONG><a name="[24]"></a>TIMER3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, arm_startup_nrf52.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52.o(RESET)
</UL>
<P><STRONG><a name="[25]"></a>TIMER4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, arm_startup_nrf52.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52.o(RESET)
</UL>
<P><STRONG><a name="[1a]"></a>WDT_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, arm_startup_nrf52.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52.o(RESET)
</UL>
<P><STRONG><a name="[56]"></a>__aeabi_uldivmod</STRONG> (Thumb, 98 bytes, Stack size 40 bytes, uldiv.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = __aeabi_uldivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
<LI><a href="#[58]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
</UL>
<BR>[Called By]<UL><LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_led_indication
</UL>

<P><STRONG><a name="[58]"></a>__aeabi_llsl</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, llshl.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
</UL>

<P><STRONG><a name="[129]"></a>_ll_shift_l</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, llshl.o(.text), UNUSED)

<P><STRONG><a name="[93]"></a>__aeabi_memcpy</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, memcpya.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_queue_push
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_queue_generic_pop
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;discovery_complete_evt_trigger
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_ble_scan_init
<LI><a href="#[39]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_nus_c_evt_handler
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;memobj_op
</UL>

<P><STRONG><a name="[78]"></a>__aeabi_memcpy4</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memcpya.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[35]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;discovery_error_handler
<LI><a href="#[47]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_ble_gatt_on_ble_evt
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_uart_init
<LI><a href="#[2f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_drv_uart_init
</UL>

<P><STRONG><a name="[12a]"></a>__aeabi_memcpy8</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memcpya.o(.text), UNUSED)

<P><STRONG><a name="[5a]"></a>__aeabi_memset</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_memset$wrapper
<LI><a href="#[59]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
</UL>

<P><STRONG><a name="[12b]"></a>__aeabi_memset4</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)

<P><STRONG><a name="[12c]"></a>__aeabi_memset8</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)

<P><STRONG><a name="[59]"></a>__aeabi_memclr</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memset
</UL>

<P><STRONG><a name="[97]"></a>__aeabi_memclr4</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_ble_scan_on_adv_report
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_ble_scan_connect_with_target
<LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;on_srv_disc_completion
<LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;discovery_start
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;discovery_available_evt_trigger
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;descriptors_discover
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;characteristics_discover
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_ble_scan_start
<LI><a href="#[49]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_ble_scan_on_ble_evt
<LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_nus_c_tx_notif_enable
<LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_nus_c_on_db_disc_evt
</UL>

<P><STRONG><a name="[12d]"></a>__aeabi_memclr8</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)

<P><STRONG><a name="[5b]"></a>_memset$wrapper</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, memseta.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memset
</UL>

<P><STRONG><a name="[87]"></a>memcmp</STRONG> (Thumb, 26 bytes, Stack size 12 bytes, memcmp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = memcmp
</UL>
<BR>[Called By]<UL><LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_advdata_uuid_find
</UL>

<P><STRONG><a name="[57]"></a>__aeabi_llsr</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, llushr.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
</UL>

<P><STRONG><a name="[12e]"></a>_ll_ushift_r</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, llushr.o(.text), UNUSED)

<P><STRONG><a name="[53]"></a>__scatterload</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, init.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__main_after_scatterload
</UL>
<BR>[Called By]<UL><LI><a href="#[52]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_main_scatterload
</UL>

<P><STRONG><a name="[12f]"></a>__scatterload_rt2</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, init.o(.text), UNUSED)

<P><STRONG><a name="[130]"></a>__decompress</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __dczerorl.o(.text), UNUSED)

<P><STRONG><a name="[131]"></a>__decompress0</STRONG> (Thumb, 58 bytes, Stack size unknown bytes, __dczerorl.o(.text), UNUSED)

<P><STRONG><a name="[10]"></a>GPIOTE_IRQHandler</STRONG> (Thumb, 154 bytes, Stack size 32 bytes, nrfx_gpiote.o(i.GPIOTE_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = GPIOTE_IRQHandler &rArr; port_event_handle &rArr; nrf_gpio_latches_read_and_clear
</UL>
<BR>[Calls]<UL><LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;port_event_handle
<LI><a href="#[5d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_gpiote_event_is_set
<LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_gpiote_event_clear
<LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_gpio_latches_read_and_clear
</UL>
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52.o(RESET)
</UL>
<P><STRONG><a name="[a]"></a>POWER_CLOCK_IRQHandler</STRONG> (Thumb, 82 bytes, Stack size 16 bytes, nrfx_clock.o(i.POWER_CLOCK_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = POWER_CLOCK_IRQHandler &rArr; nrf_clock_event_clear
</UL>
<BR>[Calls]<UL><LI><a href="#[62]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_clock_event_clear
<LI><a href="#[61]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_clock_event_check
</UL>
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52.o(RESET)
</UL>
<P><STRONG><a name="[1b]"></a>RTC1_IRQHandler</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, drv_rtc.o(i.RTC1_IRQHandler))
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52.o(RESET)
</UL>
<P><STRONG><a name="[20]"></a>SWI2_EGU2_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, nrf_sdh.o(i.SWI2_EGU2_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = SWI2_EGU2_IRQHandler &rArr; nrf_sdh_evts_poll &rArr; nrf_section_iter_next &rArr; nrf_section_iter_item_set
</UL>
<BR>[Calls]<UL><LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_sdh_evts_poll
</UL>
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52.o(RESET)
</UL>
<P><STRONG><a name="[30]"></a>SystemInit</STRONG> (Thumb, 726 bytes, Stack size 20 bytes, system_nrf52.o(i.SystemInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = SystemInit
</UL>
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52.o(.text)
</UL>
<P><STRONG><a name="[c]"></a>UARTE0_UART0_IRQHandler</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, nrfx_prs.o(i.UARTE0_UART0_IRQHandler))
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52.o(RESET)
</UL>
<P><STRONG><a name="[132]"></a>__scatterload_copy</STRONG> (Thumb, 14 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_copy), UNUSED)

<P><STRONG><a name="[133]"></a>__scatterload_null</STRONG> (Thumb, 2 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_null), UNUSED)

<P><STRONG><a name="[134]"></a>__scatterload_zeroinit</STRONG> (Thumb, 14 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_zeroinit), UNUSED)

<P><STRONG><a name="[3e]"></a>app_error_fault_handler</STRONG> (Thumb, 40 bytes, Stack size 0 bytes, app_error_weak.o(i.app_error_fault_handler))
<BR><BR>[Called By]<UL><LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_error_handler_bare
</UL>
<BR>[Address Reference Count : 1]<UL><LI> nrf_sdh.o(i.nrf_sdh_enable_request)
</UL>
<P><STRONG><a name="[66]"></a>app_error_handler_bare</STRONG> (Thumb, 22 bytes, Stack size 16 bytes, app_error.o(i.app_error_handler_bare))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = app_error_handler_bare
</UL>
<BR>[Calls]<UL><LI><a href="#[3e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_error_fault_handler
</UL>
<BR>[Called By]<UL><LI><a href="#[2f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gatt_init
<LI><a href="#[46]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;shutdown_handler
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;scan_start
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;scan_init
<LI><a href="#[3f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;scan_evt_handler
<LI><a href="#[3a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nus_error_handler
<LI><a href="#[39]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_nus_c_evt_handler
<LI><a href="#[4d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_evt_handler
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;softdevices_evt_irq_enable
<LI><a href="#[50]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_sdh_soc_evts_poll
<LI><a href="#[4f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_sdh_ble_evts_poll
</UL>

<P><STRONG><a name="[67]"></a>app_fifo_get</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, app_fifo.o(i.app_fifo_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = app_fifo_get
</UL>
<BR>[Calls]<UL><LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fifo_get
</UL>
<BR>[Called By]<UL><LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_uart_put
<LI><a href="#[33]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_event_handler
</UL>

<P><STRONG><a name="[77]"></a>app_fifo_init</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, app_fifo.o(i.app_fifo_init))
<BR><BR>[Called By]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_uart_init
</UL>

<P><STRONG><a name="[69]"></a>app_fifo_put</STRONG> (Thumb, 26 bytes, Stack size 8 bytes, app_fifo.o(i.app_fifo_put))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = app_fifo_put
</UL>
<BR>[Calls]<UL><LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fifo_put
</UL>
<BR>[Called By]<UL><LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_uart_put
<LI><a href="#[33]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_event_handler
</UL>

<P><STRONG><a name="[6b]"></a>app_timer_cnt_get</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, app_timer2.o(i.app_timer_cnt_get))
<BR><BR>[Calls]<UL><LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;drv_rtc_counter_get
</UL>
<BR>[Called By]<UL><LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_schedule
</UL>

<P><STRONG><a name="[6d]"></a>app_timer_init</STRONG> (Thumb, 70 bytes, Stack size 16 bytes, app_timer2.o(i.app_timer_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = app_timer_init &rArr; drv_rtc_compare_set &rArr; nrf_rtc_event_clear
</UL>
<BR>[Calls]<UL><LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_atfifo_init
<LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;drv_rtc_overflow_enable
<LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;drv_rtc_init
<LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;drv_rtc_compare_set
</UL>
<BR>[Called By]<UL><LI><a href="#[2f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[72]"></a>app_timer_start</STRONG> (Thumb, 48 bytes, Stack size 16 bytes, app_timer2.o(i.app_timer_start))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = app_timer_start &rArr; timer_req_schedule &rArr; nrf_atfifo_item_alloc
</UL>
<BR>[Calls]<UL><LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_req_schedule
<LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_now
</UL>
<BR>[Called By]<UL><LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_led_indication
<LI><a href="#[40]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_button_event_handler
</UL>

<P><STRONG><a name="[75]"></a>app_timer_stop</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, app_timer2.o(i.app_timer_stop))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = app_timer_stop &rArr; timer_req_schedule &rArr; nrf_atfifo_item_alloc
</UL>
<BR>[Calls]<UL><LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_req_schedule
</UL>
<BR>[Called By]<UL><LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_led_indication
<LI><a href="#[40]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_button_event_handler
</UL>

<P><STRONG><a name="[76]"></a>app_uart_init</STRONG> (Thumb, 148 bytes, Stack size 56 bytes, app_uart_fifo.o(i.app_uart_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 192<LI>Call Chain = app_uart_init &rArr; nrf_drv_uart_init &rArr; nrfx_uarte_init &rArr; apply_config &rArr; nrf_gpio_cfg_output &rArr; nrf_gpio_cfg
</UL>
<BR>[Calls]<UL><LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_drv_uart_init
<LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_drv_uart_rx
<LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_fifo_init
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>
<BR>[Called By]<UL><LI><a href="#[2f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[7b]"></a>app_uart_put</STRONG> (Thumb, 70 bytes, Stack size 8 bytes, app_uart_fifo.o(i.app_uart_put))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = app_uart_put &rArr; nrf_drv_uart_tx &rArr; nrfx_uart_tx &rArr; tx_byte &rArr; nrf_uart_event_clear
</UL>
<BR>[Calls]<UL><LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_drv_uart_tx
<LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_fifo_put
<LI><a href="#[67]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_fifo_get
<LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrfx_uarte_tx_in_progress
<LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrfx_uart_tx_in_progress
</UL>
<BR>[Called By]<UL><LI><a href="#[2f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[c9]"></a>app_util_critical_region_enter</STRONG> (Thumb, 64 bytes, Stack size 12 bytes, app_util_platform.o(i.app_util_critical_region_enter))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = app_util_critical_region_enter
</UL>
<BR>[Called By]<UL><LI><a href="#[51]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_state_evt_handler
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_queue_push
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_queue_generic_pop
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_sdh_enable_request
<LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_pwr_mgmt_run
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrfx_prs_acquire
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_drv_clock_lfclk_release
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_balloc_free
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_balloc_alloc
</UL>

<P><STRONG><a name="[ca]"></a>app_util_critical_region_exit</STRONG> (Thumb, 46 bytes, Stack size 0 bytes, app_util_platform.o(i.app_util_critical_region_exit))
<BR><BR>[Called By]<UL><LI><a href="#[51]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_state_evt_handler
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_queue_push
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_queue_generic_pop
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_sdh_enable_request
<LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_pwr_mgmt_run
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrfx_prs_acquire
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_drv_clock_lfclk_release
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_balloc_free
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_balloc_alloc
</UL>

<P><STRONG><a name="[86]"></a>ble_advdata_search</STRONG> (Thumb, 70 bytes, Stack size 16 bytes, ble_advdata.o(i.ble_advdata_search))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = ble_advdata_search
</UL>
<BR>[Called By]<UL><LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_advdata_uuid_find
</UL>

<P><STRONG><a name="[85]"></a>ble_advdata_uuid_find</STRONG> (Thumb, 182 bytes, Stack size 64 bytes, ble_advdata.o(i.ble_advdata_uuid_find))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = ble_advdata_uuid_find &rArr; ble_advdata_search
</UL>
<BR>[Calls]<UL><LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_advdata_search
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;memcmp
</UL>
<BR>[Called By]<UL><LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_ble_scan_on_adv_report
</UL>

<P><STRONG><a name="[88]"></a>ble_db_discovery_evt_register</STRONG> (Thumb, 56 bytes, Stack size 16 bytes, ble_db_discovery.o(i.ble_db_discovery_evt_register))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = ble_db_discovery_evt_register &rArr; registered_handler_get
</UL>
<BR>[Calls]<UL><LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;registered_handler_get
</UL>
<BR>[Called By]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_nus_c_init
</UL>

<P><STRONG><a name="[ba]"></a>ble_db_discovery_init</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, ble_db_discovery.o(i.ble_db_discovery_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = ble_db_discovery_init
</UL>
<BR>[Called By]<UL><LI><a href="#[2f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[48]"></a>ble_db_discovery_on_ble_evt</STRONG> (Thumb, 86 bytes, Stack size 0 bytes, ble_db_discovery.o(i.ble_db_discovery_on_ble_evt))
<BR><BR>[Stack]<UL><LI>Max Depth = 352<LI>Call Chain = ble_db_discovery_on_ble_evt &rArr; on_descriptor_discovery_rsp &rArr; on_srv_disc_completion &rArr; discovery_error_handler &rArr; discovery_available_evt_trigger
</UL>
<BR>[Calls]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;on_primary_srv_discovery_rsp
<LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;on_descriptor_discovery_rsp
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;on_characteristic_discovery_rsp
</UL>
<BR>[Address Reference Count : 1]<UL><LI> main.o(sdh_ble_observers1)
</UL>
<P><STRONG><a name="[8d]"></a>ble_db_discovery_start</STRONG> (Thumb, 34 bytes, Stack size 0 bytes, ble_db_discovery.o(i.ble_db_discovery_start))
<BR><BR>[Stack]<UL><LI>Max Depth = 228<LI>Call Chain = ble_db_discovery_start &rArr; discovery_start &rArr; nrf_ble_gq_item_add &rArr; queue_process &rArr; nrf_memobj_free &rArr; nrf_balloc_free &rArr; app_util_critical_region_enter
</UL>
<BR>[Calls]<UL><LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;discovery_start
</UL>
<BR>[Called By]<UL><LI><a href="#[4d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_evt_handler
</UL>

<P><STRONG><a name="[8f]"></a>ble_nus_c_handles_assign</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, ble_nus_c.o(i.ble_nus_c_handles_assign))
<BR><BR>[Stack]<UL><LI>Max Depth = 140<LI>Call Chain = ble_nus_c_handles_assign &rArr; nrf_ble_gq_conn_handle_register &rArr; queues_purge &rArr; nrf_memobj_free &rArr; nrf_balloc_free &rArr; app_util_critical_region_enter
</UL>
<BR>[Calls]<UL><LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_ble_gq_conn_handle_register
</UL>
<BR>[Called By]<UL><LI><a href="#[39]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_nus_c_evt_handler
<LI><a href="#[4d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_evt_handler
</UL>

<P><STRONG><a name="[95]"></a>ble_nus_c_init</STRONG> (Thumb, 84 bytes, Stack size 32 bytes, ble_nus_c.o(i.ble_nus_c_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = ble_nus_c_init &rArr; ble_db_discovery_evt_register &rArr; registered_handler_get
</UL>
<BR>[Calls]<UL><LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_db_discovery_evt_register
</UL>
<BR>[Called By]<UL><LI><a href="#[2f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[4c]"></a>ble_nus_c_on_ble_evt</STRONG> (Thumb, 106 bytes, Stack size 32 bytes, ble_nus_c.o(i.ble_nus_c_on_ble_evt))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = ble_nus_c_on_ble_evt
</UL>
<BR>[Address Reference Count : 1]<UL><LI> main.o(sdh_ble_observers2)
</UL>
<P><STRONG><a name="[96]"></a>ble_nus_c_on_db_disc_evt</STRONG> (Thumb, 128 bytes, Stack size 32 bytes, ble_nus_c.o(i.ble_nus_c_on_db_disc_evt))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = ble_nus_c_on_db_disc_evt
</UL>
<BR>[Calls]<UL><LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[38]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;db_disc_handler
</UL>

<P><STRONG><a name="[92]"></a>ble_nus_c_tx_notif_enable</STRONG> (Thumb, 102 bytes, Stack size 48 bytes, ble_nus_c.o(i.ble_nus_c_tx_notif_enable))
<BR><BR>[Stack]<UL><LI>Max Depth = 220<LI>Call Chain = ble_nus_c_tx_notif_enable &rArr; nrf_ble_gq_item_add &rArr; queue_process &rArr; nrf_memobj_free &rArr; nrf_balloc_free &rArr; app_util_critical_region_enter
</UL>
<BR>[Calls]<UL><LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_ble_gq_item_add
<LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[39]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_nus_c_evt_handler
</UL>

<P><STRONG><a name="[120]"></a>bsp_board_button_idx_to_pin</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, boards.o(i.bsp_board_button_idx_to_pin))
<BR><BR>[Called By]<UL><LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wakeup_button_cfg
</UL>

<P><STRONG><a name="[a4]"></a>bsp_board_led_invert</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, boards.o(i.bsp_board_led_invert))
<BR><BR>[Called By]<UL><LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_led_indication
</UL>

<P><STRONG><a name="[99]"></a>bsp_board_led_off</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, boards.o(i.bsp_board_led_off))
<BR><BR>[Calls]<UL><LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_gpio_pin_write
</UL>
<BR>[Called By]<UL><LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;leds_off
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_led_indication
<LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_board_leds_off
</UL>

<P><STRONG><a name="[9b]"></a>bsp_board_led_on</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, boards.o(i.bsp_board_led_on))
<BR><BR>[Calls]<UL><LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_gpio_pin_write
</UL>
<BR>[Called By]<UL><LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_led_indication
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_board_leds_on
</UL>

<P><STRONG><a name="[a3]"></a>bsp_board_led_state_get</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, boards.o(i.bsp_board_led_state_get))
<BR><BR>[Called By]<UL><LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_led_indication
</UL>

<P><STRONG><a name="[9c]"></a>bsp_board_leds_off</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, boards.o(i.bsp_board_leds_off))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = bsp_board_leds_off
</UL>
<BR>[Calls]<UL><LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_board_led_off
</UL>
<BR>[Called By]<UL><LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;leds_off
</UL>

<P><STRONG><a name="[9d]"></a>bsp_board_leds_on</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, boards.o(i.bsp_board_leds_on))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = bsp_board_leds_on
</UL>
<BR>[Calls]<UL><LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_board_led_on
</UL>
<BR>[Called By]<UL><LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_led_indication
</UL>

<P><STRONG><a name="[a0]"></a>bsp_board_pin_to_button_idx</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, boards.o(i.bsp_board_pin_to_button_idx))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = bsp_board_pin_to_button_idx
</UL>
<BR>[Called By]<UL><LI><a href="#[40]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_button_event_handler
</UL>

<P><STRONG><a name="[9e]"></a>bsp_btn_ble_sleep_mode_prepare</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, bsp_btn_ble.o(i.bsp_btn_ble_sleep_mode_prepare))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = bsp_btn_ble_sleep_mode_prepare &rArr; bsp_wakeup_button_enable &rArr; wakeup_button_cfg
</UL>
<BR>[Calls]<UL><LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_wakeup_button_enable
</UL>
<BR>[Called By]<UL><LI><a href="#[46]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;shutdown_handler
</UL>

<P><STRONG><a name="[65]"></a>bsp_event_to_button_action_assign</STRONG> (Thumb, 64 bytes, Stack size 16 bytes, bsp.o(i.bsp_event_to_button_action_assign))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = bsp_event_to_button_action_assign
</UL>
<BR>[Called By]<UL><LI><a href="#[4b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_evt_handler
<LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;advertising_buttons_configure
</UL>

<P><STRONG><a name="[90]"></a>bsp_indication_set</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, bsp.o(i.bsp_indication_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = bsp_indication_set &rArr; bsp_led_indication &rArr; app_timer_start &rArr; timer_req_schedule &rArr; nrf_atfifo_item_alloc
</UL>
<BR>[Calls]<UL><LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_led_indication
</UL>
<BR>[Called By]<UL><LI><a href="#[46]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;shutdown_handler
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;scan_start
<LI><a href="#[4d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_evt_handler
</UL>

<P><STRONG><a name="[9f]"></a>bsp_wakeup_button_enable</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, bsp.o(i.bsp_wakeup_button_enable))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = bsp_wakeup_button_enable &rArr; wakeup_button_cfg
</UL>
<BR>[Calls]<UL><LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wakeup_button_cfg
</UL>
<BR>[Called By]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_btn_ble_sleep_mode_prepare
</UL>

<P><STRONG><a name="[110]"></a>drv_rtc_compare_disable</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, drv_rtc.o(i.drv_rtc_compare_disable))
<BR><BR>[Called By]<UL><LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_schedule
</UL>

<P><STRONG><a name="[ac]"></a>drv_rtc_compare_pending</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, drv_rtc.o(i.drv_rtc_compare_pending))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = drv_rtc_compare_pending &rArr; evt_pending &rArr; nrf_rtc_event_clear
</UL>
<BR>[Calls]<UL><LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;evt_pending
</UL>
<BR>[Called By]<UL><LI><a href="#[32]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_irq
</UL>

<P><STRONG><a name="[71]"></a>drv_rtc_compare_set</STRONG> (Thumb, 80 bytes, Stack size 24 bytes, drv_rtc.o(i.drv_rtc_compare_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = drv_rtc_compare_set &rArr; nrf_rtc_event_clear
</UL>
<BR>[Calls]<UL><LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_rtc_event_clear
</UL>
<BR>[Called By]<UL><LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_timer_init
</UL>

<P><STRONG><a name="[6c]"></a>drv_rtc_counter_get</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, drv_rtc.o(i.drv_rtc_counter_get))
<BR><BR>[Called By]<UL><LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_timer_cnt_get
<LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_now
</UL>

<P><STRONG><a name="[6f]"></a>drv_rtc_init</STRONG> (Thumb, 118 bytes, Stack size 8 bytes, drv_rtc.o(i.drv_rtc_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = drv_rtc_init
</UL>
<BR>[Called By]<UL><LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_timer_init
</UL>

<P><STRONG><a name="[11a]"></a>drv_rtc_irq_trigger</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, drv_rtc.o(i.drv_rtc_irq_trigger))
<BR><BR>[Called By]<UL><LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_req_schedule
</UL>

<P><STRONG><a name="[70]"></a>drv_rtc_overflow_enable</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, drv_rtc.o(i.drv_rtc_overflow_enable))
<BR><BR>[Calls]<UL><LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;evt_enable
</UL>
<BR>[Called By]<UL><LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_timer_init
</UL>

<P><STRONG><a name="[b0]"></a>drv_rtc_overflow_pending</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, drv_rtc.o(i.drv_rtc_overflow_pending))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = drv_rtc_overflow_pending &rArr; evt_pending &rArr; nrf_rtc_event_clear
</UL>
<BR>[Calls]<UL><LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;evt_pending
</UL>
<BR>[Called By]<UL><LI><a href="#[32]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_irq
</UL>

<P><STRONG><a name="[115]"></a>drv_rtc_start</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, drv_rtc.o(i.drv_rtc_start))
<BR><BR>[Called By]<UL><LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_update
</UL>

<P><STRONG><a name="[114]"></a>drv_rtc_stop</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, drv_rtc.o(i.drv_rtc_stop))
<BR><BR>[Called By]<UL><LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_update
</UL>

<P><STRONG><a name="[b1]"></a>drv_rtc_windowed_compare_set</STRONG> (Thumb, 222 bytes, Stack size 40 bytes, drv_rtc.o(i.drv_rtc_windowed_compare_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = drv_rtc_windowed_compare_set &rArr; nrf_rtc_event_clear
</UL>
<BR>[Calls]<UL><LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrfx_coredep_delay_us
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_rtc_event_clear
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;evt_enable
</UL>
<BR>[Called By]<UL><LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_schedule
</UL>

<P><STRONG><a name="[36]"></a>gatt_evt_handler</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, main.o(i.gatt_evt_handler))
<BR>[Address Reference Count : 1]<UL><LI> main.o(i.gatt_init)
</UL>
<P><STRONG><a name="[b3]"></a>gatt_init</STRONG> (Thumb, 38 bytes, Stack size 8 bytes, main.o(i.gatt_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = gatt_init &rArr; app_error_handler_bare
</UL>
<BR>[Calls]<UL><LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_ble_gatt_init
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_ble_gatt_att_mtu_central_set
<LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_error_handler_bare
</UL>
<BR>[Called By]<UL><LI><a href="#[2f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[dd]"></a>is_whitelist_used</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, nrf_ble_scan.o(i.is_whitelist_used))
<BR><BR>[Called By]<UL><LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_ble_scan_on_adv_report
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_ble_scan_start
</UL>

<P><STRONG><a name="[2f]"></a>main</STRONG> (Thumb, 512 bytes, Stack size 48 bytes, main.o(i.main))
<BR><BR>[Stack]<UL><LI>Max Depth = 240<LI>Call Chain = main &rArr; app_uart_init &rArr; nrf_drv_uart_init &rArr; nrfx_uarte_init &rArr; apply_config &rArr; nrf_gpio_cfg_output &rArr; nrf_gpio_cfg
</UL>
<BR>[Calls]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_sdh_enable_request
<LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_sdh_ble_enable
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_sdh_ble_default_cfg_set
<LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_pwr_mgmt_run
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_pwr_mgmt_init
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_nus_c_init
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_db_discovery_init
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_uart_put
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_uart_init
<LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_timer_init
<LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_error_handler_bare
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gatt_init
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;scan_start
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;scan_init
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>
<BR>[Address Reference Count : 1]<UL><LI> entry9a.o(.ARM.Collect$$$$0000000B)
</UL>
<P><STRONG><a name="[6e]"></a>nrf_atfifo_init</STRONG> (Thumb, 38 bytes, Stack size 8 bytes, nrf_atfifo.o(i.nrf_atfifo_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = nrf_atfifo_init
</UL>
<BR>[Called By]<UL><LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_timer_init
</UL>

<P><STRONG><a name="[c2]"></a>nrf_atfifo_item_alloc</STRONG> (Thumb, 22 bytes, Stack size 16 bytes, nrf_atfifo.o(i.nrf_atfifo_item_alloc))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = nrf_atfifo_item_alloc
</UL>
<BR>[Calls]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__asm___12_nrf_atfifo_c_51f461e1__nrf_atfifo_wspace_req
</UL>
<BR>[Called By]<UL><LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_req_schedule
</UL>

<P><STRONG><a name="[c4]"></a>nrf_atfifo_item_free</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, nrf_atfifo.o(i.nrf_atfifo_item_free))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = nrf_atfifo_item_free
</UL>
<BR>[Calls]<UL><LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__asm___12_nrf_atfifo_c_51f461e1__nrf_atfifo_rspace_close
</UL>
<BR>[Called By]<UL><LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_req_process
</UL>

<P><STRONG><a name="[c5]"></a>nrf_atfifo_item_get</STRONG> (Thumb, 22 bytes, Stack size 16 bytes, nrf_atfifo.o(i.nrf_atfifo_item_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = nrf_atfifo_item_get
</UL>
<BR>[Calls]<UL><LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__asm___12_nrf_atfifo_c_51f461e1__nrf_atfifo_rspace_req
</UL>
<BR>[Called By]<UL><LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_req_process
</UL>

<P><STRONG><a name="[c7]"></a>nrf_atfifo_item_put</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, nrf_atfifo.o(i.nrf_atfifo_item_put))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = nrf_atfifo_item_put
</UL>
<BR>[Calls]<UL><LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__asm___12_nrf_atfifo_c_51f461e1__nrf_atfifo_wspace_close
</UL>
<BR>[Called By]<UL><LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_req_schedule
</UL>

<P><STRONG><a name="[c8]"></a>nrf_balloc_alloc</STRONG> (Thumb, 68 bytes, Stack size 16 bytes, nrf_balloc.o(i.nrf_balloc_alloc))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = nrf_balloc_alloc &rArr; app_util_critical_region_enter
</UL>
<BR>[Calls]<UL><LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_util_critical_region_exit
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_util_critical_region_enter
</UL>
<BR>[Called By]<UL><LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_memobj_alloc
</UL>

<P><STRONG><a name="[cb]"></a>nrf_balloc_free</STRONG> (Thumb, 48 bytes, Stack size 16 bytes, nrf_balloc.o(i.nrf_balloc_free))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = nrf_balloc_free &rArr; app_util_critical_region_enter
</UL>
<BR>[Calls]<UL><LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_util_critical_region_exit
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_util_critical_region_enter
</UL>
<BR>[Called By]<UL><LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_memobj_free
</UL>

<P><STRONG><a name="[ee]"></a>nrf_balloc_init</STRONG> (Thumb, 48 bytes, Stack size 8 bytes, nrf_balloc.o(i.nrf_balloc_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = nrf_balloc_init
</UL>
<BR>[Called By]<UL><LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_memobj_pool_init
</UL>

<P><STRONG><a name="[b5]"></a>nrf_ble_gatt_att_mtu_central_set</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, nrf_ble_gatt.o(i.nrf_ble_gatt_att_mtu_central_set))
<BR><BR>[Called By]<UL><LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gatt_init
</UL>

<P><STRONG><a name="[b4]"></a>nrf_ble_gatt_init</STRONG> (Thumb, 34 bytes, Stack size 8 bytes, nrf_ble_gatt.o(i.nrf_ble_gatt_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = nrf_ble_gatt_init
</UL>
<BR>[Calls]<UL><LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;link_init
</UL>
<BR>[Called By]<UL><LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gatt_init
</UL>

<P><STRONG><a name="[47]"></a>nrf_ble_gatt_on_ble_evt</STRONG> (Thumb, 366 bytes, Stack size 88 bytes, nrf_ble_gatt.o(i.nrf_ble_gatt_on_ble_evt))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = nrf_ble_gatt_on_ble_evt &rArr; data_length_update
</UL>
<BR>[Calls]<UL><LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;link_init
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;data_length_update
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>
<BR>[Address Reference Count : 1]<UL><LI> main.o(sdh_ble_observers1)
</UL>
<P><STRONG><a name="[94]"></a>nrf_ble_gq_conn_handle_register</STRONG> (Thumb, 110 bytes, Stack size 24 bytes, nrf_ble_gq.o(i.nrf_ble_gq_conn_handle_register))
<BR><BR>[Stack]<UL><LI>Max Depth = 140<LI>Call Chain = nrf_ble_gq_conn_handle_register &rArr; queues_purge &rArr; nrf_memobj_free &rArr; nrf_balloc_free &rArr; app_util_critical_region_enter
</UL>
<BR>[Calls]<UL><LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_memobj_pool_init
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;queues_purge
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;conn_handle_id_find
</UL>
<BR>[Called By]<UL><LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;discovery_start
<LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_nus_c_handles_assign
</UL>

<P><STRONG><a name="[98]"></a>nrf_ble_gq_item_add</STRONG> (Thumb, 154 bytes, Stack size 32 bytes, nrf_ble_gq.o(i.nrf_ble_gq_item_add))
<BR><BR>[Stack]<UL><LI>Max Depth = 172<LI>Call Chain = nrf_ble_gq_item_add &rArr; queue_process &rArr; nrf_memobj_free &rArr; nrf_balloc_free &rArr; app_util_critical_region_enter
</UL>
<BR>[Calls]<UL><LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_queue_push
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_queue_is_empty
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_memobj_free
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;request_process
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;queues_purge
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;queue_process
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;conn_handle_id_find
</UL>
<BR>[Called By]<UL><LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;on_srv_disc_completion
<LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;discovery_start
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;descriptors_discover
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;characteristics_discover
<LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_nus_c_tx_notif_enable
</UL>

<P><STRONG><a name="[4a]"></a>nrf_ble_gq_on_ble_evt</STRONG> (Thumb, 96 bytes, Stack size 24 bytes, nrf_ble_gq.o(i.nrf_ble_gq_on_ble_evt))
<BR><BR>[Stack]<UL><LI>Max Depth = 164<LI>Call Chain = nrf_ble_gq_on_ble_evt &rArr; queue_process &rArr; nrf_memobj_free &rArr; nrf_balloc_free &rArr; app_util_critical_region_enter
</UL>
<BR>[Calls]<UL><LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_queue_push
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;queue_process
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;conn_handle_id_find
</UL>
<BR>[Address Reference Count : 1]<UL><LI> main.o(sdh_ble_observers1)
</UL>
<P><STRONG><a name="[116]"></a>nrf_ble_scan_filter_set</STRONG> (Thumb, 66 bytes, Stack size 20 bytes, nrf_ble_scan.o(i.nrf_ble_scan_filter_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = nrf_ble_scan_filter_set
</UL>
<BR>[Called By]<UL><LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;scan_init
</UL>

<P><STRONG><a name="[d8]"></a>nrf_ble_scan_filters_disable</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, nrf_ble_scan.o(i.nrf_ble_scan_filters_disable))
<BR><BR>[Called By]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_ble_scan_filters_enable
</UL>

<P><STRONG><a name="[d7]"></a>nrf_ble_scan_filters_enable</STRONG> (Thumb, 54 bytes, Stack size 8 bytes, nrf_ble_scan.o(i.nrf_ble_scan_filters_enable))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = nrf_ble_scan_filters_enable
</UL>
<BR>[Calls]<UL><LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_ble_scan_filters_disable
</UL>
<BR>[Called By]<UL><LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;scan_init
</UL>

<P><STRONG><a name="[d9]"></a>nrf_ble_scan_init</STRONG> (Thumb, 106 bytes, Stack size 16 bytes, nrf_ble_scan.o(i.nrf_ble_scan_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = nrf_ble_scan_init
</UL>
<BR>[Calls]<UL><LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_ble_scan_default_param_set
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_ble_scan_default_conn_param_set
<LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;scan_init
</UL>

<P><STRONG><a name="[49]"></a>nrf_ble_scan_on_ble_evt</STRONG> (Thumb, 128 bytes, Stack size 40 bytes, nrf_ble_scan.o(i.nrf_ble_scan_on_ble_evt))
<BR><BR>[Stack]<UL><LI>Max Depth = 192<LI>Call Chain = nrf_ble_scan_on_ble_evt &rArr; nrf_ble_scan_on_adv_report &rArr; ble_advdata_uuid_find &rArr; ble_advdata_search
</UL>
<BR>[Calls]<UL><LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_ble_scan_on_adv_report
<LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Address Reference Count : 1]<UL><LI> main.o(sdh_ble_observers1)
</UL>
<P><STRONG><a name="[de]"></a>nrf_ble_scan_start</STRONG> (Thumb, 68 bytes, Stack size 32 bytes, nrf_ble_scan.o(i.nrf_ble_scan_start))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = nrf_ble_scan_start
</UL>
<BR>[Calls]<UL><LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;is_whitelist_used
<LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;scan_start
</UL>

<P><STRONG><a name="[df]"></a>nrf_drv_clock_init</STRONG> (Thumb, 56 bytes, Stack size 16 bytes, nrf_drv_clock.o(i.nrf_drv_clock_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = nrf_drv_clock_init
</UL>
<BR>[Calls]<UL><LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_wdt_started
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrfx_clock_init
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrfx_clock_enable
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_sdh_is_enabled
</UL>
<BR>[Called By]<UL><LI><a href="#[51]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_state_evt_handler
</UL>

<P><STRONG><a name="[e4]"></a>nrf_drv_clock_lfclk_release</STRONG> (Thumb, 48 bytes, Stack size 16 bytes, nrf_drv_clock.o(i.nrf_drv_clock_lfclk_release))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = nrf_drv_clock_lfclk_release &rArr; nrfx_clock_lfclk_stop &rArr; nrf_clock_event_clear
</UL>
<BR>[Calls]<UL><LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_wdt_started
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrfx_clock_lfclk_stop
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_util_critical_region_exit
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_util_critical_region_enter
</UL>
<BR>[Called By]<UL><LI><a href="#[51]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_state_evt_handler
</UL>

<P><STRONG><a name="[79]"></a>nrf_drv_uart_init</STRONG> (Thumb, 90 bytes, Stack size 56 bytes, nrf_drv_uart.o(i.nrf_drv_uart_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = nrf_drv_uart_init &rArr; nrfx_uarte_init &rArr; apply_config &rArr; nrf_gpio_cfg_output &rArr; nrf_gpio_cfg
</UL>
<BR>[Calls]<UL><LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrfx_uarte_init
<LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrfx_uart_init
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>
<BR>[Called By]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_uart_init
</UL>

<P><STRONG><a name="[b6]"></a>nrf_memobj_alloc</STRONG> (Thumb, 96 bytes, Stack size 24 bytes, nrf_memobj.o(i.nrf_memobj_alloc))
<BR><BR>[Stack]<UL><LI>Max Depth = 76<LI>Call Chain = nrf_memobj_alloc &rArr; nrf_memobj_free &rArr; nrf_balloc_free &rArr; app_util_critical_region_enter
</UL>
<BR>[Calls]<UL><LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_memobj_free
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_balloc_alloc
</UL>
<BR>[Called By]<UL><LI><a href="#[42]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gatts_hvx_alloc
<LI><a href="#[41]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gattc_write_alloc
</UL>

<P><STRONG><a name="[d4]"></a>nrf_memobj_free</STRONG> (Thumb, 50 bytes, Stack size 24 bytes, nrf_memobj.o(i.nrf_memobj_free))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = nrf_memobj_free &rArr; nrf_balloc_free &rArr; app_util_critical_region_enter
</UL>
<BR>[Calls]<UL><LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_balloc_free
</UL>
<BR>[Called By]<UL><LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_memobj_alloc
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;queues_purge
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;queue_process
<LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_ble_gq_item_add
</UL>

<P><STRONG><a name="[d0]"></a>nrf_memobj_pool_init</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, nrf_memobj.o(i.nrf_memobj_pool_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = nrf_memobj_pool_init &rArr; nrf_balloc_init
</UL>
<BR>[Calls]<UL><LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_balloc_init
</UL>
<BR>[Called By]<UL><LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_ble_gq_conn_handle_register
</UL>

<P><STRONG><a name="[ef]"></a>nrf_memobj_read</STRONG> (Thumb, 16 bytes, Stack size 16 bytes, nrf_memobj.o(i.nrf_memobj_read))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = nrf_memobj_read &rArr; memobj_op
</UL>
<BR>[Calls]<UL><LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;memobj_op
</UL>
<BR>[Called By]<UL><LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;queue_process
</UL>

<P><STRONG><a name="[b7]"></a>nrf_memobj_write</STRONG> (Thumb, 16 bytes, Stack size 16 bytes, nrf_memobj.o(i.nrf_memobj_write))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = nrf_memobj_write &rArr; memobj_op
</UL>
<BR>[Calls]<UL><LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;memobj_op
</UL>
<BR>[Called By]<UL><LI><a href="#[42]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gatts_hvx_alloc
<LI><a href="#[41]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gattc_write_alloc
</UL>

<P><STRONG><a name="[bb]"></a>nrf_pwr_mgmt_init</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, nrf_pwr_mgmt.o(i.nrf_pwr_mgmt_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = nrf_pwr_mgmt_init
</UL>
<BR>[Calls]<UL><LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_section_iter_init
</UL>
<BR>[Called By]<UL><LI><a href="#[2f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[c0]"></a>nrf_pwr_mgmt_run</STRONG> (Thumb, 64 bytes, Stack size 8 bytes, nrf_pwr_mgmt.o(i.nrf_pwr_mgmt_run))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = nrf_pwr_mgmt_run &rArr; app_util_critical_region_enter
</UL>
<BR>[Calls]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_sdh_is_enabled
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_util_critical_region_exit
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_util_critical_region_enter
</UL>
<BR>[Called By]<UL><LI><a href="#[2f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[f1]"></a>nrf_queue_generic_pop</STRONG> (Thumb, 142 bytes, Stack size 32 bytes, nrf_queue.o(i.nrf_queue_generic_pop))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = nrf_queue_generic_pop &rArr; app_util_critical_region_enter
</UL>
<BR>[Calls]<UL><LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_queue_is_empty
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_util_critical_region_exit
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_util_critical_region_enter
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_queue_next_idx
<LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;queues_purge
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;queue_process
</UL>

<P><STRONG><a name="[d1]"></a>nrf_queue_is_empty</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, nrf_queue.o(i.nrf_queue_is_empty))
<BR><BR>[Called By]<UL><LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_queue_generic_pop
<LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_ble_gq_item_add
</UL>

<P><STRONG><a name="[f3]"></a>nrf_queue_is_full</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, nrf_queue.o(i.nrf_queue_is_full))
<BR><BR>[Called By]<UL><LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_queue_push
</UL>

<P><STRONG><a name="[d3]"></a>nrf_queue_push</STRONG> (Thumb, 178 bytes, Stack size 32 bytes, nrf_queue.o(i.nrf_queue_push))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = nrf_queue_push &rArr; app_util_critical_region_enter
</UL>
<BR>[Calls]<UL><LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_util_critical_region_exit
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_util_critical_region_enter
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_queue_is_full
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;queue_utilization_get
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_queue_next_idx
<LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_ble_gq_item_add
<LI><a href="#[4a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_ble_gq_on_ble_evt
</UL>

<P><STRONG><a name="[f5]"></a>nrf_sdh_ble_app_ram_start_get</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, nrf_sdh_ble.o(i.nrf_sdh_ble_app_ram_start_get))
<BR><BR>[Called By]<UL><LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_sdh_ble_default_cfg_set
</UL>

<P><STRONG><a name="[bd]"></a>nrf_sdh_ble_default_cfg_set</STRONG> (Thumb, 156 bytes, Stack size 32 bytes, nrf_sdh_ble.o(i.nrf_sdh_ble_default_cfg_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = nrf_sdh_ble_default_cfg_set
</UL>
<BR>[Calls]<UL><LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_sdh_ble_app_ram_start_get
</UL>
<BR>[Called By]<UL><LI><a href="#[2f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[be]"></a>nrf_sdh_ble_enable</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, nrf_sdh_ble.o(i.nrf_sdh_ble_enable))
<BR><BR>[Called By]<UL><LI><a href="#[2f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[bc]"></a>nrf_sdh_enable_request</STRONG> (Thumb, 102 bytes, Stack size 24 bytes, nrf_sdh.o(i.nrf_sdh_enable_request))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = nrf_sdh_enable_request &rArr; sdh_state_observer_notify &rArr; nrf_section_iter_next &rArr; nrf_section_iter_item_set
</UL>
<BR>[Calls]<UL><LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_util_critical_region_exit
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_util_critical_region_enter
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;softdevices_evt_irq_enable
<LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdh_state_observer_notify
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdh_request_observer_notify
</UL>
<BR>[Called By]<UL><LI><a href="#[2f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[63]"></a>nrf_sdh_evts_poll</STRONG> (Thumb, 32 bytes, Stack size 16 bytes, nrf_sdh.o(i.nrf_sdh_evts_poll))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = nrf_sdh_evts_poll &rArr; nrf_section_iter_next &rArr; nrf_section_iter_item_set
</UL>
<BR>[Calls]<UL><LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_section_iter_next
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_section_iter_init
</UL>
<BR>[Called By]<UL><LI><a href="#[20]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SWI2_EGU2_IRQHandler
</UL>

<P><STRONG><a name="[e1]"></a>nrf_sdh_is_enabled</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, nrf_sdh.o(i.nrf_sdh_is_enabled))
<BR><BR>[Called By]<UL><LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_pwr_mgmt_run
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_drv_clock_init
</UL>

<P><STRONG><a name="[f0]"></a>nrf_section_iter_init</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, nrf_section_iter.o(i.nrf_section_iter_init))
<BR><BR>[Called By]<UL><LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_pwr_mgmt_init
<LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdh_state_observer_notify
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdh_request_observer_notify
<LI><a href="#[50]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_sdh_soc_evts_poll
<LI><a href="#[4f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_sdh_ble_evts_poll
<LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_sdh_evts_poll
</UL>

<P><STRONG><a name="[f6]"></a>nrf_section_iter_next</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, nrf_section_iter.o(i.nrf_section_iter_next))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = nrf_section_iter_next &rArr; nrf_section_iter_item_set
</UL>
<BR>[Calls]<UL><LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_section_iter_item_set
</UL>
<BR>[Called By]<UL><LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdh_state_observer_notify
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdh_request_observer_notify
<LI><a href="#[50]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_sdh_soc_evts_poll
<LI><a href="#[4f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_sdh_ble_evts_poll
<LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_sdh_evts_poll
</UL>

<P><STRONG><a name="[112]"></a>nrf_sortlist_add</STRONG> (Thumb, 34 bytes, Stack size 16 bytes, nrf_sortlist.o(i.nrf_sortlist_add))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = nrf_sortlist_add
</UL>
<BR>[Called By]<UL><LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_req_process
<LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_expire
<LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_update
</UL>

<P><STRONG><a name="[111]"></a>nrf_sortlist_peek</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, nrf_sortlist.o(i.nrf_sortlist_peek))
<BR><BR>[Called By]<UL><LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_update
</UL>

<P><STRONG><a name="[118]"></a>nrf_sortlist_pop</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, nrf_sortlist.o(i.nrf_sortlist_pop))
<BR><BR>[Called By]<UL><LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sortlist_pop
</UL>

<P><STRONG><a name="[119]"></a>nrf_sortlist_remove</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, nrf_sortlist.o(i.nrf_sortlist_remove))
<BR><BR>[Called By]<UL><LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_req_process
</UL>

<P><STRONG><a name="[e2]"></a>nrfx_clock_enable</STRONG> (Thumb, 34 bytes, Stack size 0 bytes, nrfx_clock.o(i.nrfx_clock_enable))
<BR><BR>[Called By]<UL><LI><a href="#[51]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_state_evt_handler
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_drv_clock_init
</UL>

<P><STRONG><a name="[e0]"></a>nrfx_clock_init</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, nrfx_clock.o(i.nrfx_clock_init))
<BR><BR>[Called By]<UL><LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_drv_clock_init
</UL>

<P><STRONG><a name="[e5]"></a>nrfx_clock_lfclk_stop</STRONG> (Thumb, 38 bytes, Stack size 8 bytes, nrfx_clock.o(i.nrfx_clock_lfclk_stop))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = nrfx_clock_lfclk_stop &rArr; nrf_clock_event_clear
</UL>
<BR>[Calls]<UL><LI><a href="#[62]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_clock_event_clear
</UL>
<BR>[Called By]<UL><LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_drv_clock_lfclk_release
</UL>

<P><STRONG><a name="[fb]"></a>nrfx_prs_acquire</STRONG> (Thumb, 58 bytes, Stack size 16 bytes, nrfx_prs.o(i.nrfx_prs_acquire))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = nrfx_prs_acquire &rArr; app_util_critical_region_enter
</UL>
<BR>[Calls]<UL><LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prs_box_get
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_util_critical_region_exit
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_util_critical_region_enter
</UL>
<BR>[Called By]<UL><LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrfx_uarte_init
<LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrfx_uart_init
</UL>

<P><STRONG><a name="[43]"></a>nrfx_uart_0_irq_handler</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, nrfx_uart.o(i.nrfx_uart_0_irq_handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = nrfx_uart_0_irq_handler &rArr; uart_irq_handler &rArr; tx_done_event
</UL>
<BR>[Calls]<UL><LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_irq_handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> nrfx_uart.o(.constdata)
</UL>
<P><STRONG><a name="[e7]"></a>nrfx_uart_init</STRONG> (Thumb, 200 bytes, Stack size 24 bytes, nrfx_uart.o(i.nrfx_uart_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = nrfx_uart_init &rArr; apply_config &rArr; nrf_gpio_cfg_output &rArr; nrf_gpio_cfg
</UL>
<BR>[Calls]<UL><LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_uart_event_clear
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;apply_config
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrfx_prs_acquire
</UL>
<BR>[Called By]<UL><LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_drv_uart_init
</UL>

<P><STRONG><a name="[e9]"></a>nrfx_uart_rx</STRONG> (Thumb, 230 bytes, Stack size 40 bytes, nrfx_uart.o(i.nrfx_uart_rx))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = nrfx_uart_rx &rArr; rx_byte &rArr; nrf_uart_event_clear
</UL>
<BR>[Calls]<UL><LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rx_enable
<LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rx_byte
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_uart_event_clear
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_uart_event_check
</UL>
<BR>[Called By]<UL><LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_drv_uart_rx
</UL>

<P><STRONG><a name="[eb]"></a>nrfx_uart_tx</STRONG> (Thumb, 162 bytes, Stack size 40 bytes, nrfx_uart.o(i.nrfx_uart_tx))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = nrfx_uart_tx &rArr; tx_byte &rArr; nrf_uart_event_clear
</UL>
<BR>[Calls]<UL><LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tx_byte
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_uart_event_clear
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_uart_event_check
<LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrfx_uart_tx_in_progress
</UL>
<BR>[Called By]<UL><LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_drv_uart_tx
</UL>

<P><STRONG><a name="[7d]"></a>nrfx_uart_tx_in_progress</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, nrfx_uart.o(i.nrfx_uart_tx_in_progress))
<BR><BR>[Called By]<UL><LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_uart_put
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrfx_uart_tx
</UL>

<P><STRONG><a name="[44]"></a>nrfx_uarte_0_irq_handler</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, nrfx_uarte.o(i.nrfx_uarte_0_irq_handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = nrfx_uarte_0_irq_handler &rArr; uarte_irq_handler &rArr; tx_done_event
</UL>
<BR>[Calls]<UL><LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uarte_irq_handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> nrfx_uarte.o(.constdata)
</UL>
<P><STRONG><a name="[e6]"></a>nrfx_uarte_init</STRONG> (Thumb, 100 bytes, Stack size 24 bytes, nrfx_uarte.o(i.nrfx_uarte_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = nrfx_uarte_init &rArr; apply_config &rArr; nrf_gpio_cfg_output &rArr; nrf_gpio_cfg
</UL>
<BR>[Calls]<UL><LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrfx_prs_acquire
<LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;interrupts_enable
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;apply_config
</UL>
<BR>[Called By]<UL><LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_drv_uart_init
</UL>

<P><STRONG><a name="[e8]"></a>nrfx_uarte_rx</STRONG> (Thumb, 256 bytes, Stack size 40 bytes, nrfx_uarte.o(i.nrfx_uarte_rx))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = nrfx_uarte_rx &rArr; nrf_uarte_event_clear
</UL>
<BR>[Calls]<UL><LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrfx_is_in_ram
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_uarte_event_clear
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_uarte_event_check
</UL>
<BR>[Called By]<UL><LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_drv_uart_rx
</UL>

<P><STRONG><a name="[ea]"></a>nrfx_uarte_tx</STRONG> (Thumb, 166 bytes, Stack size 32 bytes, nrfx_uarte.o(i.nrfx_uarte_tx))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = nrfx_uarte_tx &rArr; nrf_uarte_event_clear
</UL>
<BR>[Calls]<UL><LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrfx_uarte_tx_in_progress
<LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrfx_is_in_ram
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_uarte_event_clear
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_uarte_event_check
</UL>
<BR>[Called By]<UL><LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_drv_uart_tx
</UL>

<P><STRONG><a name="[7c]"></a>nrfx_uarte_tx_in_progress</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, nrfx_uarte.o(i.nrfx_uarte_tx_in_progress))
<BR><BR>[Called By]<UL><LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_uart_put
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrfx_uarte_tx
</UL>

<P><STRONG><a name="[37]"></a>uart_event_handle</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, main.o(i.uart_event_handle))
<BR>[Address Reference Count : 1]<UL><LI> main.o(i.main)
</UL><P>
<H3>
Local Symbols
</H3>
<P><STRONG><a name="[4d]"></a>ble_evt_handler</STRONG> (Thumb, 144 bytes, Stack size 16 bytes, main.o(i.ble_evt_handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 244<LI>Call Chain = ble_evt_handler &rArr; ble_db_discovery_start &rArr; discovery_start &rArr; nrf_ble_gq_item_add &rArr; queue_process &rArr; nrf_memobj_free &rArr; nrf_balloc_free &rArr; app_util_critical_region_enter
</UL>
<BR>[Calls]<UL><LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_indication_set
<LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_nus_c_handles_assign
<LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_db_discovery_start
<LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_error_handler_bare
</UL>
<BR>[Address Reference Count : 1]<UL><LI> main.o(sdh_ble_observers3)
</UL>
<P><STRONG><a name="[39]"></a>ble_nus_c_evt_handler</STRONG> (Thumb, 86 bytes, Stack size 16 bytes, main.o(i.ble_nus_c_evt_handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 236<LI>Call Chain = ble_nus_c_evt_handler &rArr; ble_nus_c_tx_notif_enable &rArr; nrf_ble_gq_item_add &rArr; queue_process &rArr; nrf_memobj_free &rArr; nrf_balloc_free &rArr; app_util_critical_region_enter
</UL>
<BR>[Calls]<UL><LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_nus_c_tx_notif_enable
<LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_nus_c_handles_assign
<LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_error_handler_bare
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;scan_start
<LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Address Reference Count : 1]<UL><LI> main.o(i.main)
</UL>
<P><STRONG><a name="[38]"></a>db_disc_handler</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, main.o(i.db_disc_handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = db_disc_handler &rArr; ble_nus_c_on_db_disc_evt
</UL>
<BR>[Calls]<UL><LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_nus_c_on_db_disc_evt
</UL>
<BR>[Address Reference Count : 1]<UL><LI> main.o(i.main)
</UL>
<P><STRONG><a name="[3a]"></a>nus_error_handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, main.o(i.nus_error_handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = nus_error_handler &rArr; app_error_handler_bare
</UL>
<BR>[Calls]<UL><LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_error_handler_bare
</UL>
<BR>[Address Reference Count : 1]<UL><LI> main.o(i.main)
</UL>
<P><STRONG><a name="[3f]"></a>scan_evt_handler</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, main.o(i.scan_evt_handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = scan_evt_handler &rArr; scan_start &rArr; bsp_indication_set &rArr; bsp_led_indication &rArr; app_timer_start &rArr; timer_req_schedule &rArr; nrf_atfifo_item_alloc
</UL>
<BR>[Calls]<UL><LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_error_handler_bare
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;scan_start
</UL>
<BR>[Address Reference Count : 1]<UL><LI> main.o(i.scan_init)
</UL>
<P><STRONG><a name="[bf]"></a>scan_init</STRONG> (Thumb, 74 bytes, Stack size 24 bytes, main.o(i.scan_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = scan_init &rArr; nrf_ble_scan_filter_set
</UL>
<BR>[Calls]<UL><LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_ble_scan_init
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_ble_scan_filters_enable
<LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_ble_scan_filter_set
<LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_error_handler_bare
</UL>
<BR>[Called By]<UL><LI><a href="#[2f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[91]"></a>scan_start</STRONG> (Thumb, 34 bytes, Stack size 8 bytes, main.o(i.scan_start))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = scan_start &rArr; bsp_indication_set &rArr; bsp_led_indication &rArr; app_timer_start &rArr; timer_req_schedule &rArr; nrf_atfifo_item_alloc
</UL>
<BR>[Calls]<UL><LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_ble_scan_start
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_indication_set
<LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_error_handler_bare
</UL>
<BR>[Called By]<UL><LI><a href="#[2f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[3f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;scan_evt_handler
<LI><a href="#[39]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_nus_c_evt_handler
</UL>

<P><STRONG><a name="[46]"></a>shutdown_handler</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, main.o(i.shutdown_handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = shutdown_handler &rArr; bsp_indication_set &rArr; bsp_led_indication &rArr; app_timer_start &rArr; timer_req_schedule &rArr; nrf_atfifo_item_alloc
</UL>
<BR>[Calls]<UL><LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_indication_set
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_btn_ble_sleep_mode_prepare
<LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_error_handler_bare
</UL>
<BR>[Address Reference Count : 1]<UL><LI> main.o(pwr_mgmt_data1)
</UL>
<P><STRONG><a name="[9a]"></a>nrf_gpio_pin_write</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, boards.o(i.nrf_gpio_pin_write))
<BR><BR>[Called By]<UL><LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_board_led_on
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_board_led_off
</UL>

<P><STRONG><a name="[40]"></a>bsp_button_event_handler</STRONG> (Thumb, 132 bytes, Stack size 32 bytes, bsp.o(i.bsp_button_event_handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = bsp_button_event_handler &rArr; app_timer_start &rArr; timer_req_schedule &rArr; nrf_atfifo_item_alloc
</UL>
<BR>[Calls]<UL><LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_timer_stop
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_timer_start
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_board_pin_to_button_idx
</UL>
<BR>[Address Reference Count : 1]<UL><LI> bsp.o(.constdata)
</UL>
<P><STRONG><a name="[a1]"></a>bsp_led_indication</STRONG> (Thumb, 446 bytes, Stack size 40 bytes, bsp.o(i.bsp_led_indication))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = bsp_led_indication &rArr; app_timer_start &rArr; timer_req_schedule &rArr; nrf_atfifo_item_alloc
</UL>
<BR>[Calls]<UL><LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_timer_stop
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_timer_start
<LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;leds_off
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_board_leds_on
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_board_led_state_get
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_board_led_on
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_board_led_off
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_board_led_invert
<LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
</UL>
<BR>[Called By]<UL><LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_indication_set
</UL>

<P><STRONG><a name="[a2]"></a>leds_off</STRONG> (Thumb, 42 bytes, Stack size 8 bytes, bsp.o(i.leds_off))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = leds_off &rArr; bsp_board_leds_off
</UL>
<BR>[Calls]<UL><LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_board_leds_off
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_board_led_off
</UL>
<BR>[Called By]<UL><LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_led_indication
</UL>

<P><STRONG><a name="[a5]"></a>wakeup_button_cfg</STRONG> (Thumb, 58 bytes, Stack size 8 bytes, bsp.o(i.wakeup_button_cfg))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = wakeup_button_cfg
</UL>
<BR>[Calls]<UL><LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_board_button_idx_to_pin
</UL>
<BR>[Called By]<UL><LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_wakeup_button_enable
</UL>

<P><STRONG><a name="[64]"></a>advertising_buttons_configure</STRONG> (Thumb, 54 bytes, Stack size 8 bytes, bsp_btn_ble.o(i.advertising_buttons_configure))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = advertising_buttons_configure &rArr; bsp_event_to_button_action_assign
</UL>
<BR>[Calls]<UL><LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_event_to_button_action_assign
</UL>
<BR>[Called By]<UL><LI><a href="#[4b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_evt_handler
</UL>

<P><STRONG><a name="[4b]"></a>ble_evt_handler</STRONG> (Thumb, 106 bytes, Stack size 8 bytes, bsp_btn_ble.o(i.ble_evt_handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = ble_evt_handler &rArr; advertising_buttons_configure &rArr; bsp_event_to_button_action_assign
</UL>
<BR>[Calls]<UL><LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;advertising_buttons_configure
<LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_event_to_button_action_assign
</UL>
<BR>[Address Reference Count : 1]<UL><LI> bsp_btn_ble.o(sdh_ble_observers1)
</UL>
<P><STRONG><a name="[a6]"></a>characteristics_discover</STRONG> (Thumb, 100 bytes, Stack size 48 bytes, ble_db_discovery.o(i.characteristics_discover))
<BR><BR>[Stack]<UL><LI>Max Depth = 220<LI>Call Chain = characteristics_discover &rArr; nrf_ble_gq_item_add &rArr; queue_process &rArr; nrf_memobj_free &rArr; nrf_balloc_free &rArr; app_util_critical_region_enter
</UL>
<BR>[Calls]<UL><LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_ble_gq_item_add
<LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;on_primary_srv_discovery_rsp
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;on_characteristic_discovery_rsp
</UL>

<P><STRONG><a name="[a8]"></a>descriptors_discover</STRONG> (Thumb, 174 bytes, Stack size 64 bytes, ble_db_discovery.o(i.descriptors_discover))
<BR><BR>[Stack]<UL><LI>Max Depth = 236<LI>Call Chain = descriptors_discover &rArr; nrf_ble_gq_item_add &rArr; queue_process &rArr; nrf_memobj_free &rArr; nrf_balloc_free &rArr; app_util_critical_region_enter
</UL>
<BR>[Calls]<UL><LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_ble_gq_item_add
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;is_desc_discovery_reqd
<LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;on_descriptor_discovery_rsp
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;on_characteristic_discovery_rsp
</UL>

<P><STRONG><a name="[aa]"></a>discovery_available_evt_trigger</STRONG> (Thumb, 44 bytes, Stack size 136 bytes, ble_db_discovery.o(i.discovery_available_evt_trigger))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = discovery_available_evt_trigger
</UL>
<BR>[Calls]<UL><LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;on_srv_disc_completion
<LI><a href="#[35]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;discovery_error_handler
</UL>

<P><STRONG><a name="[ab]"></a>discovery_complete_evt_trigger</STRONG> (Thumb, 174 bytes, Stack size 24 bytes, ble_db_discovery.o(i.discovery_complete_evt_trigger))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = discovery_complete_evt_trigger &rArr; registered_handler_get
</UL>
<BR>[Calls]<UL><LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;registered_handler_get
<LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;on_primary_srv_discovery_rsp
<LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;on_descriptor_discovery_rsp
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;on_characteristic_discovery_rsp
</UL>

<P><STRONG><a name="[35]"></a>discovery_error_handler</STRONG> (Thumb, 74 bytes, Stack size 144 bytes, ble_db_discovery.o(i.discovery_error_handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 280<LI>Call Chain = discovery_error_handler &rArr; discovery_available_evt_trigger
</UL>
<BR>[Calls]<UL><LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;registered_handler_get
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;discovery_available_evt_trigger
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>
<BR>[Called By]<UL><LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;on_srv_disc_completion
<LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;on_primary_srv_discovery_rsp
<LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;on_descriptor_discovery_rsp
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;on_characteristic_discovery_rsp
</UL>
<BR>[Address Reference Count : 4]<UL><LI> ble_db_discovery.o(i.characteristics_discover)
<LI> ble_db_discovery.o(i.descriptors_discover)
<LI> ble_db_discovery.o(i.discovery_start)
<LI> ble_db_discovery.o(i.on_srv_disc_completion)
</UL>
<P><STRONG><a name="[8e]"></a>discovery_start</STRONG> (Thumb, 110 bytes, Stack size 56 bytes, ble_db_discovery.o(i.discovery_start))
<BR><BR>[Stack]<UL><LI>Max Depth = 228<LI>Call Chain = discovery_start &rArr; nrf_ble_gq_item_add &rArr; queue_process &rArr; nrf_memobj_free &rArr; nrf_balloc_free &rArr; app_util_critical_region_enter
</UL>
<BR>[Calls]<UL><LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_ble_gq_item_add
<LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_ble_gq_conn_handle_register
<LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_db_discovery_start
</UL>

<P><STRONG><a name="[a9]"></a>is_desc_discovery_reqd</STRONG> (Thumb, 76 bytes, Stack size 8 bytes, ble_db_discovery.o(i.is_desc_discovery_reqd))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = is_desc_discovery_reqd
</UL>
<BR>[Called By]<UL><LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;descriptors_discover
</UL>

<P><STRONG><a name="[8c]"></a>on_characteristic_discovery_rsp</STRONG> (Thumb, 252 bytes, Stack size 24 bytes, ble_db_discovery.o(i.on_characteristic_discovery_rsp))
<BR><BR>[Stack]<UL><LI>Max Depth = 352<LI>Call Chain = on_characteristic_discovery_rsp &rArr; on_srv_disc_completion &rArr; discovery_error_handler &rArr; discovery_available_evt_trigger
</UL>
<BR>[Calls]<UL><LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;on_srv_disc_completion
<LI><a href="#[35]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;discovery_error_handler
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;discovery_complete_evt_trigger
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;descriptors_discover
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;characteristics_discover
</UL>
<BR>[Called By]<UL><LI><a href="#[48]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_db_discovery_on_ble_evt
</UL>

<P><STRONG><a name="[8a]"></a>on_descriptor_discovery_rsp</STRONG> (Thumb, 218 bytes, Stack size 24 bytes, ble_db_discovery.o(i.on_descriptor_discovery_rsp))
<BR><BR>[Stack]<UL><LI>Max Depth = 352<LI>Call Chain = on_descriptor_discovery_rsp &rArr; on_srv_disc_completion &rArr; discovery_error_handler &rArr; discovery_available_evt_trigger
</UL>
<BR>[Calls]<UL><LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;on_srv_disc_completion
<LI><a href="#[35]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;discovery_error_handler
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;discovery_complete_evt_trigger
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;descriptors_discover
</UL>
<BR>[Called By]<UL><LI><a href="#[48]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_db_discovery_on_ble_evt
</UL>

<P><STRONG><a name="[8b]"></a>on_primary_srv_discovery_rsp</STRONG> (Thumb, 114 bytes, Stack size 16 bytes, ble_db_discovery.o(i.on_primary_srv_discovery_rsp))
<BR><BR>[Stack]<UL><LI>Max Depth = 344<LI>Call Chain = on_primary_srv_discovery_rsp &rArr; on_srv_disc_completion &rArr; discovery_error_handler &rArr; discovery_available_evt_trigger
</UL>
<BR>[Calls]<UL><LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;on_srv_disc_completion
<LI><a href="#[35]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;discovery_error_handler
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;discovery_complete_evt_trigger
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;characteristics_discover
</UL>
<BR>[Called By]<UL><LI><a href="#[48]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_db_discovery_on_ble_evt
</UL>

<P><STRONG><a name="[106]"></a>on_srv_disc_completion</STRONG> (Thumb, 142 bytes, Stack size 48 bytes, ble_db_discovery.o(i.on_srv_disc_completion))
<BR><BR>[Stack]<UL><LI>Max Depth = 328<LI>Call Chain = on_srv_disc_completion &rArr; discovery_error_handler &rArr; discovery_available_evt_trigger
</UL>
<BR>[Calls]<UL><LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_ble_gq_item_add
<LI><a href="#[35]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;discovery_error_handler
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;discovery_available_evt_trigger
<LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;on_primary_srv_discovery_rsp
<LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;on_descriptor_discovery_rsp
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;on_characteristic_discovery_rsp
</UL>

<P><STRONG><a name="[89]"></a>registered_handler_get</STRONG> (Thumb, 48 bytes, Stack size 16 bytes, ble_db_discovery.o(i.registered_handler_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = registered_handler_get
</UL>
<BR>[Called By]<UL><LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_db_discovery_evt_register
<LI><a href="#[35]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;discovery_error_handler
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;discovery_complete_evt_trigger
</UL>

<P><STRONG><a name="[cd]"></a>data_length_update</STRONG> (Thumb, 30 bytes, Stack size 24 bytes, nrf_ble_gatt.o(i.data_length_update))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = data_length_update
</UL>
<BR>[Called By]<UL><LI><a href="#[47]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_ble_gatt_on_ble_evt
</UL>

<P><STRONG><a name="[cc]"></a>link_init</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, nrf_ble_gatt.o(i.link_init))
<BR><BR>[Called By]<UL><LI><a href="#[47]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_ble_gatt_on_ble_evt
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_ble_gatt_init
</UL>

<P><STRONG><a name="[cf]"></a>conn_handle_id_find</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, nrf_ble_gq.o(i.conn_handle_id_find))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = conn_handle_id_find
</UL>
<BR>[Called By]<UL><LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_ble_gq_item_add
<LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_ble_gq_conn_handle_register
<LI><a href="#[4a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_ble_gq_on_ble_evt
</UL>

<P><STRONG><a name="[41]"></a>gattc_write_alloc</STRONG> (Thumb, 44 bytes, Stack size 16 bytes, nrf_ble_gq.o(i.gattc_write_alloc))
<BR><BR>[Stack]<UL><LI>Max Depth = 92<LI>Call Chain = gattc_write_alloc &rArr; nrf_memobj_alloc &rArr; nrf_memobj_free &rArr; nrf_balloc_free &rArr; app_util_critical_region_enter
</UL>
<BR>[Calls]<UL><LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_memobj_write
<LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_memobj_alloc
</UL>
<BR>[Address Reference Count : 1]<UL><LI> nrf_ble_gq.o(.constdata)
</UL>
<P><STRONG><a name="[42]"></a>gatts_hvx_alloc</STRONG> (Thumb, 62 bytes, Stack size 16 bytes, nrf_ble_gq.o(i.gatts_hvx_alloc))
<BR><BR>[Stack]<UL><LI>Max Depth = 92<LI>Call Chain = gatts_hvx_alloc &rArr; nrf_memobj_alloc &rArr; nrf_memobj_free &rArr; nrf_balloc_free &rArr; app_util_critical_region_enter
</UL>
<BR>[Calls]<UL><LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_memobj_write
<LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_memobj_alloc
</UL>
<BR>[Address Reference Count : 1]<UL><LI> nrf_ble_gq.o(.constdata)
</UL>
<P><STRONG><a name="[d5]"></a>queue_process</STRONG> (Thumb, 218 bytes, Stack size 88 bytes, nrf_ble_gq.o(i.queue_process))
<BR><BR>[Stack]<UL><LI>Max Depth = 140<LI>Call Chain = queue_process &rArr; nrf_memobj_free &rArr; nrf_balloc_free &rArr; app_util_critical_region_enter
</UL>
<BR>[Calls]<UL><LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_queue_generic_pop
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_memobj_read
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_memobj_free
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;request_err_code_handle
</UL>
<BR>[Called By]<UL><LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_ble_gq_item_add
<LI><a href="#[4a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_ble_gq_on_ble_evt
</UL>

<P><STRONG><a name="[ce]"></a>queues_purge</STRONG> (Thumb, 84 bytes, Stack size 64 bytes, nrf_ble_gq.o(i.queues_purge))
<BR><BR>[Stack]<UL><LI>Max Depth = 116<LI>Call Chain = queues_purge &rArr; nrf_memobj_free &rArr; nrf_balloc_free &rArr; app_util_critical_region_enter
</UL>
<BR>[Calls]<UL><LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_queue_generic_pop
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_memobj_free
</UL>
<BR>[Called By]<UL><LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_ble_gq_item_add
<LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_ble_gq_conn_handle_register
</UL>

<P><STRONG><a name="[10b]"></a>request_err_code_handle</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, nrf_ble_gq.o(i.request_err_code_handle))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = request_err_code_handle
</UL>
<BR>[Called By]<UL><LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;request_process
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;queue_process
</UL>

<P><STRONG><a name="[d2]"></a>request_process</STRONG> (Thumb, 114 bytes, Stack size 16 bytes, nrf_ble_gq.o(i.request_process))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = request_process &rArr; request_err_code_handle
</UL>
<BR>[Calls]<UL><LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;request_err_code_handle
</UL>
<BR>[Called By]<UL><LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_ble_gq_item_add
</UL>

<P><STRONG><a name="[d6]"></a>nrf_ble_scan_connect_with_target</STRONG> (Thumb, 76 bytes, Stack size 48 bytes, nrf_ble_scan.o(i.nrf_ble_scan_connect_with_target))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = nrf_ble_scan_connect_with_target
</UL>
<BR>[Calls]<UL><LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_ble_scan_on_adv_report
</UL>

<P><STRONG><a name="[db]"></a>nrf_ble_scan_default_conn_param_set</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, nrf_ble_scan.o(i.nrf_ble_scan_default_conn_param_set))
<BR><BR>[Called By]<UL><LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_ble_scan_init
</UL>

<P><STRONG><a name="[da]"></a>nrf_ble_scan_default_param_set</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, nrf_ble_scan.o(i.nrf_ble_scan_default_param_set))
<BR><BR>[Called By]<UL><LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_ble_scan_init
</UL>

<P><STRONG><a name="[dc]"></a>nrf_ble_scan_on_adv_report</STRONG> (Thumb, 252 bytes, Stack size 72 bytes, nrf_ble_scan.o(i.nrf_ble_scan_on_adv_report))
<BR><BR>[Stack]<UL><LI>Max Depth = 152<LI>Call Chain = nrf_ble_scan_on_adv_report &rArr; ble_advdata_uuid_find &rArr; ble_advdata_search
</UL>
<BR>[Calls]<UL><LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;is_whitelist_used
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_ble_scan_connect_with_target
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_advdata_uuid_find
<LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[49]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_ble_scan_on_ble_evt
</UL>

<P><STRONG><a name="[34]"></a>gatt_error_handler</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, ble_nus_c.o(i.gatt_error_handler))
<BR>[Address Reference Count : 1]<UL><LI> ble_nus_c.o(i.ble_nus_c_tx_notif_enable)
</UL>
<P><STRONG><a name="[a7]"></a>clock_clk_started_notify</STRONG> (Thumb, 34 bytes, Stack size 16 bytes, nrf_drv_clock.o(i.clock_clk_started_notify))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = clock_clk_started_notify
</UL>
<BR>[Called By]<UL><LI><a href="#[4e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;soc_evt_handler
<LI><a href="#[3b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;clock_irq_handler
</UL>

<P><STRONG><a name="[3b]"></a>clock_irq_handler</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, nrf_drv_clock.o(i.clock_irq_handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = clock_irq_handler &rArr; clock_clk_started_notify
</UL>
<BR>[Calls]<UL><LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;clock_clk_started_notify
</UL>
<BR>[Address Reference Count : 1]<UL><LI> nrf_drv_clock.o(i.nrf_drv_clock_init)
</UL>
<P><STRONG><a name="[e3]"></a>nrf_wdt_started</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, nrf_drv_clock.o(i.nrf_wdt_started))
<BR><BR>[Called By]<UL><LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_drv_clock_lfclk_release
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_drv_clock_init
</UL>

<P><STRONG><a name="[51]"></a>sd_state_evt_handler</STRONG> (Thumb, 84 bytes, Stack size 16 bytes, nrf_drv_clock.o(i.sd_state_evt_handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = sd_state_evt_handler &rArr; nrf_drv_clock_lfclk_release &rArr; nrfx_clock_lfclk_stop &rArr; nrf_clock_event_clear
</UL>
<BR>[Calls]<UL><LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrfx_clock_enable
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_util_critical_region_exit
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_util_critical_region_enter
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_drv_clock_lfclk_release
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_drv_clock_init
</UL>
<BR>[Address Reference Count : 1]<UL><LI> nrf_drv_clock.o(sdh_state_observers0)
</UL>
<P><STRONG><a name="[4e]"></a>soc_evt_handler</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, nrf_drv_clock.o(i.soc_evt_handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = soc_evt_handler &rArr; clock_clk_started_notify
</UL>
<BR>[Calls]<UL><LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;clock_clk_started_notify
</UL>
<BR>[Address Reference Count : 1]<UL><LI> nrf_drv_clock.o(sdh_soc_observers0)
</UL>
<P><STRONG><a name="[3d]"></a>uart_evt_handler</STRONG> (Thumb, 42 bytes, Stack size 24 bytes, nrf_drv_uart.o(i.uart_evt_handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = uart_evt_handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> nrf_drv_uart.o(i.nrf_drv_uart_init)
</UL>
<P><STRONG><a name="[3c]"></a>uarte_evt_handler</STRONG> (Thumb, 42 bytes, Stack size 24 bytes, nrf_drv_uart.o(i.uarte_evt_handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = uarte_evt_handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> nrf_drv_uart.o(i.nrf_drv_uart_init)
</UL>
<P><STRONG><a name="[61]"></a>nrf_clock_event_check</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, nrfx_clock.o(i.nrf_clock_event_check))
<BR><BR>[Called By]<UL><LI><a href="#[a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;POWER_CLOCK_IRQHandler
</UL>

<P><STRONG><a name="[62]"></a>nrf_clock_event_clear</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, nrfx_clock.o(i.nrf_clock_event_clear))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = nrf_clock_event_clear
</UL>
<BR>[Called By]<UL><LI><a href="#[a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;POWER_CLOCK_IRQHandler
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrfx_clock_lfclk_stop
</UL>

<P><STRONG><a name="[10a]"></a>channel_port_get</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, nrfx_gpiote.o(i.channel_port_get))
<BR><BR>[Called By]<UL><LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;port_event_handle
</UL>

<P><STRONG><a name="[107]"></a>nrf_bitmask_bit_is_set</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, nrfx_gpiote.o(i.nrf_bitmask_bit_is_set))
<BR><BR>[Called By]<UL><LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;port_event_handle
</UL>

<P><STRONG><a name="[109]"></a>nrf_gpio_cfg_sense_set</STRONG> (Thumb, 34 bytes, Stack size 0 bytes, nrfx_gpiote.o(i.nrf_gpio_cfg_sense_set))
<BR><BR>[Called By]<UL><LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;port_event_handle
</UL>

<P><STRONG><a name="[5f]"></a>nrf_gpio_latches_read_and_clear</STRONG> (Thumb, 38 bytes, Stack size 16 bytes, nrfx_gpiote.o(i.nrf_gpio_latches_read_and_clear))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = nrf_gpio_latches_read_and_clear
</UL>
<BR>[Called By]<UL><LI><a href="#[10]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOTE_IRQHandler
<LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;port_event_handle
</UL>

<P><STRONG><a name="[5e]"></a>nrf_gpiote_event_clear</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, nrfx_gpiote.o(i.nrf_gpiote_event_clear))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = nrf_gpiote_event_clear
</UL>
<BR>[Called By]<UL><LI><a href="#[10]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOTE_IRQHandler
</UL>

<P><STRONG><a name="[5d]"></a>nrf_gpiote_event_is_set</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, nrfx_gpiote.o(i.nrf_gpiote_event_is_set))
<BR><BR>[Called By]<UL><LI><a href="#[10]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOTE_IRQHandler
</UL>

<P><STRONG><a name="[60]"></a>port_event_handle</STRONG> (Thumb, 176 bytes, Stack size 40 bytes, nrfx_gpiote.o(i.port_event_handle))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = port_event_handle &rArr; nrf_gpio_latches_read_and_clear
</UL>
<BR>[Calls]<UL><LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;port_handler_polarity_get
<LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_gpio_latches_read_and_clear
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_gpio_cfg_sense_set
<LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_bitmask_bit_is_set
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;channel_port_get
</UL>
<BR>[Called By]<UL><LI><a href="#[10]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOTE_IRQHandler
</UL>

<P><STRONG><a name="[108]"></a>port_handler_polarity_get</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, nrfx_gpiote.o(i.port_handler_polarity_get))
<BR><BR>[Called By]<UL><LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;port_event_handle
</UL>

<P><STRONG><a name="[fc]"></a>prs_box_get</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, nrfx_prs.o(i.prs_box_get))
<BR><BR>[Called By]<UL><LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrfx_prs_acquire
</UL>

<P><STRONG><a name="[7f]"></a>apply_config</STRONG> (Thumb, 136 bytes, Stack size 24 bytes, nrfx_uart.o(i.apply_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = apply_config &rArr; nrf_gpio_cfg_output &rArr; nrf_gpio_cfg
</UL>
<BR>[Calls]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_gpio_cfg_output
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_gpio_cfg_input
</UL>
<BR>[Called By]<UL><LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrfx_uart_init
</UL>

<P><STRONG><a name="[ec]"></a>nrf_gpio_cfg</STRONG> (Thumb, 36 bytes, Stack size 16 bytes, nrfx_uart.o(i.nrf_gpio_cfg))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = nrf_gpio_cfg
</UL>
<BR>[Called By]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_gpio_cfg_output
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_gpio_cfg_input
</UL>

<P><STRONG><a name="[81]"></a>nrf_gpio_cfg_input</STRONG> (Thumb, 18 bytes, Stack size 16 bytes, nrfx_uart.o(i.nrf_gpio_cfg_input))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = nrf_gpio_cfg_input &rArr; nrf_gpio_cfg
</UL>
<BR>[Calls]<UL><LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_gpio_cfg
</UL>
<BR>[Called By]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;apply_config
</UL>

<P><STRONG><a name="[80]"></a>nrf_gpio_cfg_output</STRONG> (Thumb, 20 bytes, Stack size 16 bytes, nrfx_uart.o(i.nrf_gpio_cfg_output))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = nrf_gpio_cfg_output &rArr; nrf_gpio_cfg
</UL>
<BR>[Calls]<UL><LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_gpio_cfg
</UL>
<BR>[Called By]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;apply_config
</UL>

<P><STRONG><a name="[100]"></a>nrf_uart_event_check</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, nrfx_uart.o(i.nrf_uart_event_check))
<BR><BR>[Called By]<UL><LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_irq_handler
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrfx_uart_tx
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrfx_uart_rx
</UL>

<P><STRONG><a name="[fe]"></a>nrf_uart_event_clear</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, nrfx_uart.o(i.nrf_uart_event_clear))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = nrf_uart_event_clear
</UL>
<BR>[Called By]<UL><LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_irq_handler
<LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tx_byte
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rx_enable
<LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rx_byte
<LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrfx_uart_init
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrfx_uart_tx
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrfx_uart_rx
</UL>

<P><STRONG><a name="[11b]"></a>nrf_uart_int_enable_check</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, nrfx_uart.o(i.nrf_uart_int_enable_check))
<BR><BR>[Called By]<UL><LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_irq_handler
</UL>

<P><STRONG><a name="[101]"></a>rx_byte</STRONG> (Thumb, 50 bytes, Stack size 16 bytes, nrfx_uart.o(i.rx_byte))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = rx_byte &rArr; nrf_uart_event_clear
</UL>
<BR>[Calls]<UL><LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_uart_event_clear
</UL>
<BR>[Called By]<UL><LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_irq_handler
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrfx_uart_rx
</UL>

<P><STRONG><a name="[11c]"></a>rx_done_event</STRONG> (Thumb, 22 bytes, Stack size 24 bytes, nrfx_uart.o(i.rx_done_event))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = rx_done_event
</UL>
<BR>[Called By]<UL><LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_irq_handler
</UL>

<P><STRONG><a name="[ff]"></a>rx_enable</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, nrfx_uart.o(i.rx_enable))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = rx_enable &rArr; nrf_uart_event_clear
</UL>
<BR>[Calls]<UL><LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_uart_event_clear
</UL>
<BR>[Called By]<UL><LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrfx_uart_rx
</UL>

<P><STRONG><a name="[102]"></a>tx_byte</STRONG> (Thumb, 32 bytes, Stack size 16 bytes, nrfx_uart.o(i.tx_byte))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = tx_byte &rArr; nrf_uart_event_clear
</UL>
<BR>[Calls]<UL><LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_uart_event_clear
</UL>
<BR>[Called By]<UL><LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_irq_handler
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrfx_uart_tx
</UL>

<P><STRONG><a name="[11d]"></a>tx_done_event</STRONG> (Thumb, 26 bytes, Stack size 24 bytes, nrfx_uart.o(i.tx_done_event))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = tx_done_event
</UL>
<BR>[Called By]<UL><LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_irq_handler
</UL>

<P><STRONG><a name="[fd]"></a>uart_irq_handler</STRONG> (Thumb, 298 bytes, Stack size 48 bytes, nrfx_uart.o(i.uart_irq_handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = uart_irq_handler &rArr; tx_done_event
</UL>
<BR>[Calls]<UL><LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tx_done_event
<LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tx_byte
<LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rx_done_event
<LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rx_byte
<LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_uart_int_enable_check
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_uart_event_clear
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_uart_event_check
</UL>
<BR>[Called By]<UL><LI><a href="#[43]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrfx_uart_0_irq_handler
</UL>

<P><STRONG><a name="[82]"></a>apply_config</STRONG> (Thumb, 136 bytes, Stack size 24 bytes, nrfx_uarte.o(i.apply_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = apply_config &rArr; nrf_gpio_cfg_output &rArr; nrf_gpio_cfg
</UL>
<BR>[Calls]<UL><LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_gpio_cfg_output
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_gpio_cfg_input
</UL>
<BR>[Called By]<UL><LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrfx_uarte_init
</UL>

<P><STRONG><a name="[b8]"></a>interrupts_enable</STRONG> (Thumb, 132 bytes, Stack size 16 bytes, nrfx_uarte.o(i.interrupts_enable))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = interrupts_enable &rArr; nrf_uarte_event_clear
</UL>
<BR>[Calls]<UL><LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_uarte_event_clear
</UL>
<BR>[Called By]<UL><LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrfx_uarte_init
</UL>

<P><STRONG><a name="[ed]"></a>nrf_gpio_cfg</STRONG> (Thumb, 36 bytes, Stack size 16 bytes, nrfx_uarte.o(i.nrf_gpio_cfg))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = nrf_gpio_cfg
</UL>
<BR>[Called By]<UL><LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_gpio_cfg_output
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_gpio_cfg_input
</UL>

<P><STRONG><a name="[84]"></a>nrf_gpio_cfg_input</STRONG> (Thumb, 18 bytes, Stack size 16 bytes, nrfx_uarte.o(i.nrf_gpio_cfg_input))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = nrf_gpio_cfg_input &rArr; nrf_gpio_cfg
</UL>
<BR>[Calls]<UL><LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_gpio_cfg
</UL>
<BR>[Called By]<UL><LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;apply_config
</UL>

<P><STRONG><a name="[83]"></a>nrf_gpio_cfg_output</STRONG> (Thumb, 20 bytes, Stack size 16 bytes, nrfx_uarte.o(i.nrf_gpio_cfg_output))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = nrf_gpio_cfg_output &rArr; nrf_gpio_cfg
</UL>
<BR>[Calls]<UL><LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_gpio_cfg
</UL>
<BR>[Called By]<UL><LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;apply_config
</UL>

<P><STRONG><a name="[105]"></a>nrf_uarte_event_check</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, nrfx_uarte.o(i.nrf_uarte_event_check))
<BR><BR>[Called By]<UL><LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrfx_uarte_tx
<LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrfx_uarte_rx
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uarte_irq_handler
</UL>

<P><STRONG><a name="[b9]"></a>nrf_uarte_event_clear</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, nrfx_uarte.o(i.nrf_uarte_event_clear))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = nrf_uarte_event_clear
</UL>
<BR>[Called By]<UL><LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrfx_uarte_tx
<LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrfx_uarte_rx
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uarte_irq_handler
<LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;interrupts_enable
</UL>

<P><STRONG><a name="[104]"></a>nrfx_is_in_ram</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, nrfx_uarte.o(i.nrfx_is_in_ram))
<BR><BR>[Called By]<UL><LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrfx_uarte_tx
<LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrfx_uarte_rx
</UL>

<P><STRONG><a name="[11e]"></a>rx_done_event</STRONG> (Thumb, 22 bytes, Stack size 24 bytes, nrfx_uarte.o(i.rx_done_event))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = rx_done_event
</UL>
<BR>[Called By]<UL><LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uarte_irq_handler
</UL>

<P><STRONG><a name="[11f]"></a>tx_done_event</STRONG> (Thumb, 26 bytes, Stack size 24 bytes, nrfx_uarte.o(i.tx_done_event))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = tx_done_event
</UL>
<BR>[Called By]<UL><LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uarte_irq_handler
</UL>

<P><STRONG><a name="[103]"></a>uarte_irq_handler</STRONG> (Thumb, 274 bytes, Stack size 40 bytes, nrfx_uarte.o(i.uarte_irq_handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = uarte_irq_handler &rArr; tx_done_event
</UL>
<BR>[Calls]<UL><LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tx_done_event
<LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rx_done_event
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_uarte_event_clear
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_uarte_event_check
</UL>
<BR>[Called By]<UL><LI><a href="#[44]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrfx_uarte_0_irq_handler
</UL>

<P><STRONG><a name="[68]"></a>fifo_get</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, app_fifo.o(i.fifo_get))
<BR><BR>[Called By]<UL><LI><a href="#[67]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_fifo_get
</UL>

<P><STRONG><a name="[6a]"></a>fifo_put</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, app_fifo.o(i.fifo_put))
<BR><BR>[Called By]<UL><LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_fifo_put
</UL>

<P><STRONG><a name="[45]"></a>compare_func</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, app_timer2.o(i.compare_func))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = compare_func
</UL>
<BR>[Address Reference Count : 1]<UL><LI> app_timer2.o(.constdata)
</UL>
<P><STRONG><a name="[73]"></a>get_now</STRONG> (Thumb, 46 bytes, Stack size 8 bytes, app_timer2.o(i.get_now))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = get_now
</UL>
<BR>[Calls]<UL><LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;drv_rtc_counter_get
</UL>
<BR>[Called By]<UL><LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_timer_start
<LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_expire
<LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_schedule
<LI><a href="#[32]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_irq
</UL>

<P><STRONG><a name="[32]"></a>rtc_irq</STRONG> (Thumb, 88 bytes, Stack size 16 bytes, app_timer2.o(i.rtc_irq))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = rtc_irq &rArr; rtc_update &rArr; rtc_schedule &rArr; drv_rtc_windowed_compare_set &rArr; nrf_rtc_event_clear
</UL>
<BR>[Calls]<UL><LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;drv_rtc_overflow_pending
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;drv_rtc_compare_pending
<LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_req_process
<LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_expire
<LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_update
<LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_now
</UL>
<BR>[Address Reference Count : 1]<UL><LI> app_timer2.o(i.app_timer_init)
</UL>
<P><STRONG><a name="[10f]"></a>rtc_schedule</STRONG> (Thumb, 92 bytes, Stack size 16 bytes, app_timer2.o(i.rtc_schedule))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = rtc_schedule &rArr; drv_rtc_windowed_compare_set &rArr; nrf_rtc_event_clear
</UL>
<BR>[Calls]<UL><LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;drv_rtc_windowed_compare_set
<LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;drv_rtc_compare_disable
<LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_timer_cnt_get
<LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_expire
<LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_now
</UL>
<BR>[Called By]<UL><LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_update
</UL>

<P><STRONG><a name="[10e]"></a>rtc_update</STRONG> (Thumb, 108 bytes, Stack size 24 bytes, app_timer2.o(i.rtc_update))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = rtc_update &rArr; rtc_schedule &rArr; drv_rtc_windowed_compare_set &rArr; nrf_rtc_event_clear
</UL>
<BR>[Calls]<UL><LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_sortlist_peek
<LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_sortlist_add
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;drv_rtc_stop
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;drv_rtc_start
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sortlist_pop
<LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_schedule
</UL>
<BR>[Called By]<UL><LI><a href="#[32]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_irq
</UL>

<P><STRONG><a name="[113]"></a>sortlist_pop</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, app_timer2.o(i.sortlist_pop))
<BR><BR>[Calls]<UL><LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_sortlist_pop
</UL>
<BR>[Called By]<UL><LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_req_process
<LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_update
</UL>

<P><STRONG><a name="[10c]"></a>timer_expire</STRONG> (Thumb, 80 bytes, Stack size 16 bytes, app_timer2.o(i.timer_expire))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = timer_expire &rArr; nrf_sortlist_add
</UL>
<BR>[Calls]<UL><LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_sortlist_add
<LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_now
</UL>
<BR>[Called By]<UL><LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_schedule
<LI><a href="#[32]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_irq
</UL>

<P><STRONG><a name="[10d]"></a>timer_req_process</STRONG> (Thumb, 110 bytes, Stack size 24 bytes, app_timer2.o(i.timer_req_process))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = timer_req_process &rArr; nrf_sortlist_add
</UL>
<BR>[Calls]<UL><LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_sortlist_remove
<LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_sortlist_add
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_atfifo_item_get
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_atfifo_item_free
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sortlist_pop
</UL>
<BR>[Called By]<UL><LI><a href="#[32]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_irq
</UL>

<P><STRONG><a name="[74]"></a>timer_req_schedule</STRONG> (Thumb, 46 bytes, Stack size 24 bytes, app_timer2.o(i.timer_req_schedule))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = timer_req_schedule &rArr; nrf_atfifo_item_alloc
</UL>
<BR>[Calls]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_atfifo_item_put
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_atfifo_item_alloc
<LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;drv_rtc_irq_trigger
</UL>
<BR>[Called By]<UL><LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_timer_stop
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_timer_start
</UL>

<P><STRONG><a name="[7a]"></a>nrf_drv_uart_rx</STRONG> (Thumb, 26 bytes, Stack size 8 bytes, app_uart_fifo.o(i.nrf_drv_uart_rx))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = nrf_drv_uart_rx &rArr; nrfx_uart_rx &rArr; rx_byte &rArr; nrf_uart_event_clear
</UL>
<BR>[Calls]<UL><LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrfx_uarte_rx
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrfx_uart_rx
</UL>
<BR>[Called By]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_uart_init
<LI><a href="#[33]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_event_handler
</UL>

<P><STRONG><a name="[7e]"></a>nrf_drv_uart_tx</STRONG> (Thumb, 26 bytes, Stack size 8 bytes, app_uart_fifo.o(i.nrf_drv_uart_tx))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = nrf_drv_uart_tx &rArr; nrfx_uart_tx &rArr; tx_byte &rArr; nrf_uart_event_clear
</UL>
<BR>[Calls]<UL><LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrfx_uarte_tx
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrfx_uart_tx
</UL>
<BR>[Called By]<UL><LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_uart_put
<LI><a href="#[33]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_event_handler
</UL>

<P><STRONG><a name="[33]"></a>uart_event_handler</STRONG> (Thumb, 156 bytes, Stack size 24 bytes, app_uart_fifo.o(i.uart_event_handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = uart_event_handler &rArr; nrf_drv_uart_tx &rArr; nrfx_uart_tx &rArr; tx_byte &rArr; nrf_uart_event_clear
</UL>
<BR>[Calls]<UL><LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_drv_uart_tx
<LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_drv_uart_rx
<LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_fifo_put
<LI><a href="#[67]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_fifo_get
</UL>
<BR>[Address Reference Count : 1]<UL><LI> app_uart_fifo.o(i.app_uart_init)
</UL>
<P><STRONG><a name="[af]"></a>evt_enable</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, drv_rtc.o(i.evt_enable))
<BR><BR>[Called By]<UL><LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;drv_rtc_windowed_compare_set
<LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;drv_rtc_overflow_enable
</UL>

<P><STRONG><a name="[ad]"></a>evt_pending</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, drv_rtc.o(i.evt_pending))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = evt_pending &rArr; nrf_rtc_event_clear
</UL>
<BR>[Calls]<UL><LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_rtc_event_clear
</UL>
<BR>[Called By]<UL><LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;drv_rtc_overflow_pending
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;drv_rtc_compare_pending
</UL>

<P><STRONG><a name="[ae]"></a>nrf_rtc_event_clear</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, drv_rtc.o(i.nrf_rtc_event_clear))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = nrf_rtc_event_clear
</UL>
<BR>[Called By]<UL><LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;drv_rtc_windowed_compare_set
<LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;drv_rtc_compare_set
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;evt_pending
</UL>

<P><STRONG><a name="[b2]"></a>nrfx_coredep_delay_us</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, drv_rtc.o(i.nrfx_coredep_delay_us))
<BR><BR>[Called By]<UL><LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;drv_rtc_windowed_compare_set
</UL>

<P><STRONG><a name="[c1]"></a>memobj_op</STRONG> (Thumb, 126 bytes, Stack size 32 bytes, nrf_memobj.o(i.memobj_op))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = memobj_op
</UL>
<BR>[Calls]<UL><LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_memobj_write
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_memobj_read
</UL>

<P><STRONG><a name="[f2]"></a>nrf_queue_next_idx</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, nrf_queue.o(i.nrf_queue_next_idx))
<BR><BR>[Called By]<UL><LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_queue_push
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_queue_generic_pop
</UL>

<P><STRONG><a name="[f4]"></a>queue_utilization_get</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, nrf_queue.o(i.queue_utilization_get))
<BR><BR>[Called By]<UL><LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_queue_push
</UL>

<P><STRONG><a name="[fa]"></a>nrf_section_iter_item_set</STRONG> (Thumb, 36 bytes, Stack size 8 bytes, nrf_section_iter.o(i.nrf_section_iter_item_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = nrf_section_iter_item_set
</UL>
<BR>[Called By]<UL><LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_section_iter_next
</UL>

<P><STRONG><a name="[117]"></a>__sd_nvic_app_accessible_irq</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, nrf_sdh.o(i.__sd_nvic_app_accessible_irq))
<BR><BR>[Called By]<UL><LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;softdevices_evt_irq_enable
</UL>

<P><STRONG><a name="[f7]"></a>sdh_request_observer_notify</STRONG> (Thumb, 44 bytes, Stack size 24 bytes, nrf_sdh.o(i.sdh_request_observer_notify))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = sdh_request_observer_notify &rArr; nrf_section_iter_next &rArr; nrf_section_iter_item_set
</UL>
<BR>[Calls]<UL><LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_section_iter_next
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_section_iter_init
</UL>
<BR>[Called By]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_sdh_enable_request
</UL>

<P><STRONG><a name="[f8]"></a>sdh_state_observer_notify</STRONG> (Thumb, 38 bytes, Stack size 24 bytes, nrf_sdh.o(i.sdh_state_observer_notify))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = sdh_state_observer_notify &rArr; nrf_section_iter_next &rArr; nrf_section_iter_item_set
</UL>
<BR>[Calls]<UL><LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_section_iter_next
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_section_iter_init
</UL>
<BR>[Called By]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_sdh_enable_request
</UL>

<P><STRONG><a name="[f9]"></a>softdevices_evt_irq_enable</STRONG> (Thumb, 80 bytes, Stack size 8 bytes, nrf_sdh.o(i.softdevices_evt_irq_enable))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = softdevices_evt_irq_enable &rArr; app_error_handler_bare
</UL>
<BR>[Calls]<UL><LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_error_handler_bare
<LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__sd_nvic_app_accessible_irq
</UL>
<BR>[Called By]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_sdh_enable_request
</UL>

<P><STRONG><a name="[4f]"></a>nrf_sdh_ble_evts_poll</STRONG> (Thumb, 88 bytes, Stack size 528 bytes, nrf_sdh_ble.o(i.nrf_sdh_ble_evts_poll))
<BR><BR>[Stack]<UL><LI>Max Depth = 544<LI>Call Chain = nrf_sdh_ble_evts_poll &rArr; app_error_handler_bare
</UL>
<BR>[Calls]<UL><LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_error_handler_bare
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_section_iter_next
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_section_iter_init
</UL>
<BR>[Address Reference Count : 1]<UL><LI> nrf_sdh_ble.o(sdh_stack_observers0)
</UL>
<P><STRONG><a name="[50]"></a>nrf_sdh_soc_evts_poll</STRONG> (Thumb, 56 bytes, Stack size 24 bytes, nrf_sdh_soc.o(i.nrf_sdh_soc_evts_poll))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = nrf_sdh_soc_evts_poll &rArr; app_error_handler_bare
</UL>
<BR>[Calls]<UL><LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_error_handler_bare
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_section_iter_next
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_section_iter_init
</UL>
<BR>[Address Reference Count : 1]<UL><LI> nrf_sdh_soc.o(sdh_stack_observers0)
</UL><P>
<H3>
Undefined Global Symbols
</H3><HR></body></html>
