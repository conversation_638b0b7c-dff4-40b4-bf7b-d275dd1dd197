<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html lang="ja">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta http-equiv="Content-Style-Type" content="text/css">
<link rel="up" title="FatFs" href="../00index_j.html">
<link rel="alternate" hreflang="en" title="English" href="../en/unlink.html">
<link rel="stylesheet" href="../css_j.css" type="text/css" media="screen" title="ELM Default">
<title>FatFs - f_unlink</title>
</head>

<body>

<div class="para func">
<h2>f_unlink</h2>
<p>ファイルまたはサブ ディレクトリを削除します。</p>
<pre>
FRESULT f_unlink (
  const TCHAR* <span class="arg">path</span>  <span class="c">/* [IN] オブジェクト名へのポインタ */</span>
);
</pre>
</div>

<div class="para arg">
<h4>引数</h4>
<dl class="par">
<dt>path</dt>
<dd>削除対象の<a href="filename.html">パス名</a>を示すヌル文字<tt>'\0'</tt>終端の文字列へのポインタを指定します。</dd>
</dl>
</div>


<div class="para ret">
<h4>戻り値</h4>
<p>
<a href="rc.html#ok">FR_OK</a>,
<a href="rc.html#de">FR_DISK_ERR</a>,
<a href="rc.html#ie">FR_INT_ERR</a>,
<a href="rc.html#nr">FR_NOT_READY</a>,
<a href="rc.html#ok">FR_NO_FILE</a>,
<a href="rc.html#np">FR_NO_PATH</a>,
<a href="rc.html#in">FR_INVALID_NAME</a>,
<a href="rc.html#de">FR_DENIED</a>,
<a href="rc.html#wp">FR_WRITE_PROTECTED</a>,
<a href="rc.html#id">FR_INVALID_DRIVE</a>,
<a href="rc.html#ne">FR_NOT_ENABLED</a>,
<a href="rc.html#ns">FR_NO_FILESYSTEM</a>,
<a href="rc.html#tm">FR_TIMEOUT</a>,
<a href="rc.html#lo">FR_LOCKED</a>,
<a href="rc.html#nc">FR_NOT_ENOUGH_CORE</a>
</p>
</div>


<div class="para desc">
<h4>解説</h4>
<p>削除対象のオブジェクトが次の条件に当てはまる場合、そのアクセスは拒否(<tt>FR_DENIED</tt>)され関数は失敗します。</p>
<ul>
<li>リード オンリー属性(<tt>AM_RDO</tt>)を持っている場合。</li>
<li>空でないサブ ディレクトリまたはカレント ディレクトリである場合。</li>
<li>開かれているオブジェクトである場合。<a href="appnote.html#dup">多重アクセス制御</a>が有効のときは安全に拒否されるが、そうでないときは不正な操作となり<em>FAT構造が破壊される可能性</em>がある。</li>
</ul>
</div>

<div class="para comp">
<h4>対応情報</h4>
<p><tt>_FS_READONLY == 0</tt>で、且つ<tt>_FS_MINIMIZE == 0</tt>のときに使用可能です。</p>
</div>


<p class="foot"><a href="../00index_j.html">戻る</a></p>
</body>
</html>
