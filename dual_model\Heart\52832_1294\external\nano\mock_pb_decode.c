/*lint -e???? -save */
/* AUTOGENERATED FILE. DO NOT EDIT. */
#include <string.h>
#include <stdlib.h>
#include <setjmp.h>
#include "unity.h"
#include "cmock.h"
#include "mock_pb_decode.h"

typedef struct _CMOCK_pb_decode_CALL_INSTANCE
{
  UNITY_LINE_TYPE LineNumber;
  bool ReturnVal;
  int CallOrder;
  pb_istream_t* Expected_stream;
  pb_field_t* Expected_fields;
  void* Expected_dest_struct;
  int Expected_stream_Depth;
  int Expected_fields_Depth;
  int Expected_dest_struct_Depth;
  int ReturnThruPtr_stream_Used;
  pb_istream_t* ReturnThruPtr_stream_Val;
  int ReturnThruPtr_stream_Size;
  int ReturnThruPtr_dest_struct_Used;
  void* ReturnThruPtr_dest_struct_Val;
  int ReturnThruPtr_dest_struct_Size;
  int IgnoreArg_stream;
  int IgnoreArg_fields;
  int IgnoreArg_dest_struct;

} CMOCK_pb_decode_CALL_INSTANCE;

typedef struct _CMOCK_pb_decode_noinit_CALL_INSTANCE
{
  UNITY_LINE_TYPE LineNumber;
  bool ReturnVal;
  int CallOrder;
  pb_istream_t* Expected_stream;
  pb_field_t* Expected_fields;
  void* Expected_dest_struct;
  int Expected_stream_Depth;
  int Expected_fields_Depth;
  int Expected_dest_struct_Depth;
  int ReturnThruPtr_stream_Used;
  pb_istream_t* ReturnThruPtr_stream_Val;
  int ReturnThruPtr_stream_Size;
  int ReturnThruPtr_dest_struct_Used;
  void* ReturnThruPtr_dest_struct_Val;
  int ReturnThruPtr_dest_struct_Size;
  int IgnoreArg_stream;
  int IgnoreArg_fields;
  int IgnoreArg_dest_struct;

} CMOCK_pb_decode_noinit_CALL_INSTANCE;

typedef struct _CMOCK_pb_decode_delimited_CALL_INSTANCE
{
  UNITY_LINE_TYPE LineNumber;
  bool ReturnVal;
  int CallOrder;
  pb_istream_t* Expected_stream;
  pb_field_t* Expected_fields;
  void* Expected_dest_struct;
  int Expected_stream_Depth;
  int Expected_fields_Depth;
  int Expected_dest_struct_Depth;
  int ReturnThruPtr_stream_Used;
  pb_istream_t* ReturnThruPtr_stream_Val;
  int ReturnThruPtr_stream_Size;
  int ReturnThruPtr_dest_struct_Used;
  void* ReturnThruPtr_dest_struct_Val;
  int ReturnThruPtr_dest_struct_Size;
  int IgnoreArg_stream;
  int IgnoreArg_fields;
  int IgnoreArg_dest_struct;

} CMOCK_pb_decode_delimited_CALL_INSTANCE;

typedef struct _CMOCK_pb_release_CALL_INSTANCE
{
  UNITY_LINE_TYPE LineNumber;
  int CallOrder;
  pb_field_t* Expected_fields;
  void* Expected_dest_struct;
  int Expected_fields_Depth;
  int Expected_dest_struct_Depth;
  int ReturnThruPtr_dest_struct_Used;
  void* ReturnThruPtr_dest_struct_Val;
  int ReturnThruPtr_dest_struct_Size;
  int IgnoreArg_fields;
  int IgnoreArg_dest_struct;

} CMOCK_pb_release_CALL_INSTANCE;

typedef struct _CMOCK_pb_istream_from_buffer_CALL_INSTANCE
{
  UNITY_LINE_TYPE LineNumber;
  pb_istream_t ReturnVal;
  int CallOrder;
  pb_byte_t* Expected_buf;
  size_t Expected_bufsize;
  int Expected_buf_Depth;
  int IgnoreArg_buf;
  int IgnoreArg_bufsize;

} CMOCK_pb_istream_from_buffer_CALL_INSTANCE;

typedef struct _CMOCK_pb_read_CALL_INSTANCE
{
  UNITY_LINE_TYPE LineNumber;
  bool ReturnVal;
  int CallOrder;
  pb_istream_t* Expected_stream;
  pb_byte_t* Expected_buf;
  size_t Expected_count;
  int Expected_stream_Depth;
  int Expected_buf_Depth;
  int ReturnThruPtr_stream_Used;
  pb_istream_t* ReturnThruPtr_stream_Val;
  int ReturnThruPtr_stream_Size;
  int ReturnThruPtr_buf_Used;
  pb_byte_t* ReturnThruPtr_buf_Val;
  int ReturnThruPtr_buf_Size;
  int IgnoreArg_stream;
  int IgnoreArg_buf;
  int IgnoreArg_count;

} CMOCK_pb_read_CALL_INSTANCE;

typedef struct _CMOCK_pb_decode_tag_CALL_INSTANCE
{
  UNITY_LINE_TYPE LineNumber;
  bool ReturnVal;
  int CallOrder;
  pb_istream_t* Expected_stream;
  pb_wire_type_t* Expected_wire_type;
  uint32_t* Expected_tag;
  bool* Expected_eof;
  int Expected_stream_Depth;
  int Expected_wire_type_Depth;
  int Expected_tag_Depth;
  int Expected_eof_Depth;
  int ReturnThruPtr_stream_Used;
  pb_istream_t* ReturnThruPtr_stream_Val;
  int ReturnThruPtr_stream_Size;
  int ReturnThruPtr_wire_type_Used;
  pb_wire_type_t* ReturnThruPtr_wire_type_Val;
  int ReturnThruPtr_wire_type_Size;
  int ReturnThruPtr_tag_Used;
  uint32_t* ReturnThruPtr_tag_Val;
  int ReturnThruPtr_tag_Size;
  int ReturnThruPtr_eof_Used;
  bool* ReturnThruPtr_eof_Val;
  int ReturnThruPtr_eof_Size;
  int IgnoreArg_stream;
  int IgnoreArg_wire_type;
  int IgnoreArg_tag;
  int IgnoreArg_eof;

} CMOCK_pb_decode_tag_CALL_INSTANCE;

typedef struct _CMOCK_pb_skip_field_CALL_INSTANCE
{
  UNITY_LINE_TYPE LineNumber;
  bool ReturnVal;
  int CallOrder;
  pb_istream_t* Expected_stream;
  pb_wire_type_t Expected_wire_type;
  int Expected_stream_Depth;
  int ReturnThruPtr_stream_Used;
  pb_istream_t* ReturnThruPtr_stream_Val;
  int ReturnThruPtr_stream_Size;
  int IgnoreArg_stream;
  int IgnoreArg_wire_type;

} CMOCK_pb_skip_field_CALL_INSTANCE;

typedef struct _CMOCK_pb_decode_varint_CALL_INSTANCE
{
  UNITY_LINE_TYPE LineNumber;
  bool ReturnVal;
  int CallOrder;
  pb_istream_t* Expected_stream;
  uint64_t* Expected_dest;
  int Expected_stream_Depth;
  int Expected_dest_Depth;
  int ReturnThruPtr_stream_Used;
  pb_istream_t* ReturnThruPtr_stream_Val;
  int ReturnThruPtr_stream_Size;
  int ReturnThruPtr_dest_Used;
  uint64_t* ReturnThruPtr_dest_Val;
  int ReturnThruPtr_dest_Size;
  int IgnoreArg_stream;
  int IgnoreArg_dest;

} CMOCK_pb_decode_varint_CALL_INSTANCE;

typedef struct _CMOCK_pb_decode_svarint_CALL_INSTANCE
{
  UNITY_LINE_TYPE LineNumber;
  bool ReturnVal;
  int CallOrder;
  pb_istream_t* Expected_stream;
  int64_t* Expected_dest;
  int Expected_stream_Depth;
  int Expected_dest_Depth;
  int ReturnThruPtr_stream_Used;
  pb_istream_t* ReturnThruPtr_stream_Val;
  int ReturnThruPtr_stream_Size;
  int ReturnThruPtr_dest_Used;
  int64_t* ReturnThruPtr_dest_Val;
  int ReturnThruPtr_dest_Size;
  int IgnoreArg_stream;
  int IgnoreArg_dest;

} CMOCK_pb_decode_svarint_CALL_INSTANCE;

typedef struct _CMOCK_pb_decode_fixed32_CALL_INSTANCE
{
  UNITY_LINE_TYPE LineNumber;
  bool ReturnVal;
  int CallOrder;
  pb_istream_t* Expected_stream;
  void* Expected_dest;
  int Expected_stream_Depth;
  int Expected_dest_Depth;
  int ReturnThruPtr_stream_Used;
  pb_istream_t* ReturnThruPtr_stream_Val;
  int ReturnThruPtr_stream_Size;
  int ReturnThruPtr_dest_Used;
  void* ReturnThruPtr_dest_Val;
  int ReturnThruPtr_dest_Size;
  int IgnoreArg_stream;
  int IgnoreArg_dest;

} CMOCK_pb_decode_fixed32_CALL_INSTANCE;

typedef struct _CMOCK_pb_decode_fixed64_CALL_INSTANCE
{
  UNITY_LINE_TYPE LineNumber;
  bool ReturnVal;
  int CallOrder;
  pb_istream_t* Expected_stream;
  void* Expected_dest;
  int Expected_stream_Depth;
  int Expected_dest_Depth;
  int ReturnThruPtr_stream_Used;
  pb_istream_t* ReturnThruPtr_stream_Val;
  int ReturnThruPtr_stream_Size;
  int ReturnThruPtr_dest_Used;
  void* ReturnThruPtr_dest_Val;
  int ReturnThruPtr_dest_Size;
  int IgnoreArg_stream;
  int IgnoreArg_dest;

} CMOCK_pb_decode_fixed64_CALL_INSTANCE;

typedef struct _CMOCK_pb_make_string_substream_CALL_INSTANCE
{
  UNITY_LINE_TYPE LineNumber;
  bool ReturnVal;
  int CallOrder;
  pb_istream_t* Expected_stream;
  pb_istream_t* Expected_substream;
  int Expected_stream_Depth;
  int Expected_substream_Depth;
  int ReturnThruPtr_stream_Used;
  pb_istream_t* ReturnThruPtr_stream_Val;
  int ReturnThruPtr_stream_Size;
  int ReturnThruPtr_substream_Used;
  pb_istream_t* ReturnThruPtr_substream_Val;
  int ReturnThruPtr_substream_Size;
  int IgnoreArg_stream;
  int IgnoreArg_substream;

} CMOCK_pb_make_string_substream_CALL_INSTANCE;

typedef struct _CMOCK_pb_close_string_substream_CALL_INSTANCE
{
  UNITY_LINE_TYPE LineNumber;
  int CallOrder;
  pb_istream_t* Expected_stream;
  pb_istream_t* Expected_substream;
  int Expected_stream_Depth;
  int Expected_substream_Depth;
  int ReturnThruPtr_stream_Used;
  pb_istream_t* ReturnThruPtr_stream_Val;
  int ReturnThruPtr_stream_Size;
  int ReturnThruPtr_substream_Used;
  pb_istream_t* ReturnThruPtr_substream_Val;
  int ReturnThruPtr_substream_Size;
  int IgnoreArg_stream;
  int IgnoreArg_substream;

} CMOCK_pb_close_string_substream_CALL_INSTANCE;

static struct mock_pb_decodeInstance
{
  int pb_decode_IgnoreBool;
  bool pb_decode_FinalReturn;
  CMOCK_pb_decode_CALLBACK pb_decode_CallbackFunctionPointer;
  int pb_decode_CallbackCalls;
  CMOCK_MEM_INDEX_TYPE pb_decode_CallInstance;
  int pb_decode_noinit_IgnoreBool;
  bool pb_decode_noinit_FinalReturn;
  CMOCK_pb_decode_noinit_CALLBACK pb_decode_noinit_CallbackFunctionPointer;
  int pb_decode_noinit_CallbackCalls;
  CMOCK_MEM_INDEX_TYPE pb_decode_noinit_CallInstance;
  int pb_decode_delimited_IgnoreBool;
  bool pb_decode_delimited_FinalReturn;
  CMOCK_pb_decode_delimited_CALLBACK pb_decode_delimited_CallbackFunctionPointer;
  int pb_decode_delimited_CallbackCalls;
  CMOCK_MEM_INDEX_TYPE pb_decode_delimited_CallInstance;
  int pb_release_IgnoreBool;
  CMOCK_pb_release_CALLBACK pb_release_CallbackFunctionPointer;
  int pb_release_CallbackCalls;
  CMOCK_MEM_INDEX_TYPE pb_release_CallInstance;
  int pb_istream_from_buffer_IgnoreBool;
  pb_istream_t pb_istream_from_buffer_FinalReturn;
  CMOCK_pb_istream_from_buffer_CALLBACK pb_istream_from_buffer_CallbackFunctionPointer;
  int pb_istream_from_buffer_CallbackCalls;
  CMOCK_MEM_INDEX_TYPE pb_istream_from_buffer_CallInstance;
  int pb_read_IgnoreBool;
  bool pb_read_FinalReturn;
  CMOCK_pb_read_CALLBACK pb_read_CallbackFunctionPointer;
  int pb_read_CallbackCalls;
  CMOCK_MEM_INDEX_TYPE pb_read_CallInstance;
  int pb_decode_tag_IgnoreBool;
  bool pb_decode_tag_FinalReturn;
  CMOCK_pb_decode_tag_CALLBACK pb_decode_tag_CallbackFunctionPointer;
  int pb_decode_tag_CallbackCalls;
  CMOCK_MEM_INDEX_TYPE pb_decode_tag_CallInstance;
  int pb_skip_field_IgnoreBool;
  bool pb_skip_field_FinalReturn;
  CMOCK_pb_skip_field_CALLBACK pb_skip_field_CallbackFunctionPointer;
  int pb_skip_field_CallbackCalls;
  CMOCK_MEM_INDEX_TYPE pb_skip_field_CallInstance;
  int pb_decode_varint_IgnoreBool;
  bool pb_decode_varint_FinalReturn;
  CMOCK_pb_decode_varint_CALLBACK pb_decode_varint_CallbackFunctionPointer;
  int pb_decode_varint_CallbackCalls;
  CMOCK_MEM_INDEX_TYPE pb_decode_varint_CallInstance;
  int pb_decode_svarint_IgnoreBool;
  bool pb_decode_svarint_FinalReturn;
  CMOCK_pb_decode_svarint_CALLBACK pb_decode_svarint_CallbackFunctionPointer;
  int pb_decode_svarint_CallbackCalls;
  CMOCK_MEM_INDEX_TYPE pb_decode_svarint_CallInstance;
  int pb_decode_fixed32_IgnoreBool;
  bool pb_decode_fixed32_FinalReturn;
  CMOCK_pb_decode_fixed32_CALLBACK pb_decode_fixed32_CallbackFunctionPointer;
  int pb_decode_fixed32_CallbackCalls;
  CMOCK_MEM_INDEX_TYPE pb_decode_fixed32_CallInstance;
  int pb_decode_fixed64_IgnoreBool;
  bool pb_decode_fixed64_FinalReturn;
  CMOCK_pb_decode_fixed64_CALLBACK pb_decode_fixed64_CallbackFunctionPointer;
  int pb_decode_fixed64_CallbackCalls;
  CMOCK_MEM_INDEX_TYPE pb_decode_fixed64_CallInstance;
  int pb_make_string_substream_IgnoreBool;
  bool pb_make_string_substream_FinalReturn;
  CMOCK_pb_make_string_substream_CALLBACK pb_make_string_substream_CallbackFunctionPointer;
  int pb_make_string_substream_CallbackCalls;
  CMOCK_MEM_INDEX_TYPE pb_make_string_substream_CallInstance;
  int pb_close_string_substream_IgnoreBool;
  CMOCK_pb_close_string_substream_CALLBACK pb_close_string_substream_CallbackFunctionPointer;
  int pb_close_string_substream_CallbackCalls;
  CMOCK_MEM_INDEX_TYPE pb_close_string_substream_CallInstance;
} Mock;

extern jmp_buf AbortFrame;
extern int GlobalExpectCount;
extern int GlobalVerifyOrder;

void mock_pb_decode_Verify(void)
{
  UNITY_LINE_TYPE cmock_line = TEST_LINE_NUM;
  UNITY_TEST_ASSERT(CMOCK_GUTS_NONE == Mock.pb_decode_CallInstance, cmock_line, "Function 'pb_decode' called less times than expected.");
  if (Mock.pb_decode_CallbackFunctionPointer != NULL)
    Mock.pb_decode_CallInstance = CMOCK_GUTS_NONE;
  UNITY_TEST_ASSERT(CMOCK_GUTS_NONE == Mock.pb_decode_noinit_CallInstance, cmock_line, "Function 'pb_decode_noinit' called less times than expected.");
  if (Mock.pb_decode_noinit_CallbackFunctionPointer != NULL)
    Mock.pb_decode_noinit_CallInstance = CMOCK_GUTS_NONE;
  UNITY_TEST_ASSERT(CMOCK_GUTS_NONE == Mock.pb_decode_delimited_CallInstance, cmock_line, "Function 'pb_decode_delimited' called less times than expected.");
  if (Mock.pb_decode_delimited_CallbackFunctionPointer != NULL)
    Mock.pb_decode_delimited_CallInstance = CMOCK_GUTS_NONE;
  UNITY_TEST_ASSERT(CMOCK_GUTS_NONE == Mock.pb_release_CallInstance, cmock_line, "Function 'pb_release' called less times than expected.");
  if (Mock.pb_release_CallbackFunctionPointer != NULL)
    Mock.pb_release_CallInstance = CMOCK_GUTS_NONE;
  UNITY_TEST_ASSERT(CMOCK_GUTS_NONE == Mock.pb_istream_from_buffer_CallInstance, cmock_line, "Function 'pb_istream_from_buffer' called less times than expected.");
  if (Mock.pb_istream_from_buffer_CallbackFunctionPointer != NULL)
    Mock.pb_istream_from_buffer_CallInstance = CMOCK_GUTS_NONE;
  UNITY_TEST_ASSERT(CMOCK_GUTS_NONE == Mock.pb_read_CallInstance, cmock_line, "Function 'pb_read' called less times than expected.");
  if (Mock.pb_read_CallbackFunctionPointer != NULL)
    Mock.pb_read_CallInstance = CMOCK_GUTS_NONE;
  UNITY_TEST_ASSERT(CMOCK_GUTS_NONE == Mock.pb_decode_tag_CallInstance, cmock_line, "Function 'pb_decode_tag' called less times than expected.");
  if (Mock.pb_decode_tag_CallbackFunctionPointer != NULL)
    Mock.pb_decode_tag_CallInstance = CMOCK_GUTS_NONE;
  UNITY_TEST_ASSERT(CMOCK_GUTS_NONE == Mock.pb_skip_field_CallInstance, cmock_line, "Function 'pb_skip_field' called less times than expected.");
  if (Mock.pb_skip_field_CallbackFunctionPointer != NULL)
    Mock.pb_skip_field_CallInstance = CMOCK_GUTS_NONE;
  UNITY_TEST_ASSERT(CMOCK_GUTS_NONE == Mock.pb_decode_varint_CallInstance, cmock_line, "Function 'pb_decode_varint' called less times than expected.");
  if (Mock.pb_decode_varint_CallbackFunctionPointer != NULL)
    Mock.pb_decode_varint_CallInstance = CMOCK_GUTS_NONE;
  UNITY_TEST_ASSERT(CMOCK_GUTS_NONE == Mock.pb_decode_svarint_CallInstance, cmock_line, "Function 'pb_decode_svarint' called less times than expected.");
  if (Mock.pb_decode_svarint_CallbackFunctionPointer != NULL)
    Mock.pb_decode_svarint_CallInstance = CMOCK_GUTS_NONE;
  UNITY_TEST_ASSERT(CMOCK_GUTS_NONE == Mock.pb_decode_fixed32_CallInstance, cmock_line, "Function 'pb_decode_fixed32' called less times than expected.");
  if (Mock.pb_decode_fixed32_CallbackFunctionPointer != NULL)
    Mock.pb_decode_fixed32_CallInstance = CMOCK_GUTS_NONE;
  UNITY_TEST_ASSERT(CMOCK_GUTS_NONE == Mock.pb_decode_fixed64_CallInstance, cmock_line, "Function 'pb_decode_fixed64' called less times than expected.");
  if (Mock.pb_decode_fixed64_CallbackFunctionPointer != NULL)
    Mock.pb_decode_fixed64_CallInstance = CMOCK_GUTS_NONE;
  UNITY_TEST_ASSERT(CMOCK_GUTS_NONE == Mock.pb_make_string_substream_CallInstance, cmock_line, "Function 'pb_make_string_substream' called less times than expected.");
  if (Mock.pb_make_string_substream_CallbackFunctionPointer != NULL)
    Mock.pb_make_string_substream_CallInstance = CMOCK_GUTS_NONE;
  UNITY_TEST_ASSERT(CMOCK_GUTS_NONE == Mock.pb_close_string_substream_CallInstance, cmock_line, "Function 'pb_close_string_substream' called less times than expected.");
  if (Mock.pb_close_string_substream_CallbackFunctionPointer != NULL)
    Mock.pb_close_string_substream_CallInstance = CMOCK_GUTS_NONE;
}

void mock_pb_decode_Init(void)
{
  mock_pb_decode_Destroy();
}

void mock_pb_decode_Destroy(void)
{
  CMock_Guts_MemFreeAll();
  memset(&Mock, 0, sizeof(Mock));
  Mock.pb_decode_CallbackFunctionPointer = NULL;
  Mock.pb_decode_CallbackCalls = 0;
  Mock.pb_decode_noinit_CallbackFunctionPointer = NULL;
  Mock.pb_decode_noinit_CallbackCalls = 0;
  Mock.pb_decode_delimited_CallbackFunctionPointer = NULL;
  Mock.pb_decode_delimited_CallbackCalls = 0;
  Mock.pb_release_CallbackFunctionPointer = NULL;
  Mock.pb_release_CallbackCalls = 0;
  Mock.pb_istream_from_buffer_CallbackFunctionPointer = NULL;
  Mock.pb_istream_from_buffer_CallbackCalls = 0;
  Mock.pb_read_CallbackFunctionPointer = NULL;
  Mock.pb_read_CallbackCalls = 0;
  Mock.pb_decode_tag_CallbackFunctionPointer = NULL;
  Mock.pb_decode_tag_CallbackCalls = 0;
  Mock.pb_skip_field_CallbackFunctionPointer = NULL;
  Mock.pb_skip_field_CallbackCalls = 0;
  Mock.pb_decode_varint_CallbackFunctionPointer = NULL;
  Mock.pb_decode_varint_CallbackCalls = 0;
  Mock.pb_decode_svarint_CallbackFunctionPointer = NULL;
  Mock.pb_decode_svarint_CallbackCalls = 0;
  Mock.pb_decode_fixed32_CallbackFunctionPointer = NULL;
  Mock.pb_decode_fixed32_CallbackCalls = 0;
  Mock.pb_decode_fixed64_CallbackFunctionPointer = NULL;
  Mock.pb_decode_fixed64_CallbackCalls = 0;
  Mock.pb_make_string_substream_CallbackFunctionPointer = NULL;
  Mock.pb_make_string_substream_CallbackCalls = 0;
  Mock.pb_close_string_substream_CallbackFunctionPointer = NULL;
  Mock.pb_close_string_substream_CallbackCalls = 0;
  GlobalExpectCount = 0;
  GlobalVerifyOrder = 0;
}

bool pb_decode(pb_istream_t* stream, const pb_field_t* fields, void* dest_struct)
{
  UNITY_LINE_TYPE cmock_line = TEST_LINE_NUM;
  CMOCK_pb_decode_CALL_INSTANCE* cmock_call_instance = (CMOCK_pb_decode_CALL_INSTANCE*)CMock_Guts_GetAddressFor(Mock.pb_decode_CallInstance);
  Mock.pb_decode_CallInstance = CMock_Guts_MemNext(Mock.pb_decode_CallInstance);
  if (Mock.pb_decode_IgnoreBool)
  {
    if (cmock_call_instance == NULL)
      return Mock.pb_decode_FinalReturn;
    Mock.pb_decode_FinalReturn = cmock_call_instance->ReturnVal;
    return cmock_call_instance->ReturnVal;
  }
  if (Mock.pb_decode_CallbackFunctionPointer != NULL)
  {
    return Mock.pb_decode_CallbackFunctionPointer(stream, fields, dest_struct, Mock.pb_decode_CallbackCalls++);
  }
  UNITY_TEST_ASSERT_NOT_NULL(cmock_call_instance, cmock_line, "Function 'pb_decode' called more times than expected.");
  cmock_line = cmock_call_instance->LineNumber;
  if (cmock_call_instance->CallOrder > ++GlobalVerifyOrder)
    UNITY_TEST_FAIL(cmock_line, "Function 'pb_decode' called earlier than expected.");
  if (cmock_call_instance->CallOrder < GlobalVerifyOrder)
    UNITY_TEST_FAIL(cmock_line, "Function 'pb_decode' called later than expected.");
  if (!cmock_call_instance->IgnoreArg_stream)
  {
    if (cmock_call_instance->Expected_stream == NULL)
      { UNITY_TEST_ASSERT_NULL(stream, cmock_line, "Expected NULL. Function 'pb_decode' called with unexpected value for argument 'stream'."); }
    else if (cmock_call_instance->Expected_stream_Depth == 0)
      { UNITY_TEST_ASSERT_EQUAL_PTR(cmock_call_instance->Expected_stream, stream, cmock_line, "Function 'pb_decode' called with unexpected value for argument 'stream'."); }
    else
      { UNITY_TEST_ASSERT_EQUAL_MEMORY_ARRAY((void*)(cmock_call_instance->Expected_stream), (void*)(stream), sizeof(pb_istream_t), cmock_call_instance->Expected_stream_Depth, cmock_line, "Function 'pb_decode' called with unexpected value for argument 'stream'."); }
  }
  if (!cmock_call_instance->IgnoreArg_fields)
  {
    if (cmock_call_instance->Expected_fields == NULL)
      { UNITY_TEST_ASSERT_NULL(fields, cmock_line, "Expected NULL. Function 'pb_decode' called with unexpected value for argument 'fields'."); }
    else if (cmock_call_instance->Expected_fields_Depth == 0)
      { UNITY_TEST_ASSERT_EQUAL_PTR(cmock_call_instance->Expected_fields, fields, cmock_line, "Function 'pb_decode' called with unexpected value for argument 'fields'."); }
    else
      { UNITY_TEST_ASSERT_EQUAL_MEMORY_ARRAY((void*)(cmock_call_instance->Expected_fields), (void*)(fields), sizeof(pb_field_t), cmock_call_instance->Expected_fields_Depth, cmock_line, "Function 'pb_decode' called with unexpected value for argument 'fields'."); }
  }
  if (!cmock_call_instance->IgnoreArg_dest_struct)
  {
    UNITY_TEST_ASSERT_EQUAL_PTR(cmock_call_instance->Expected_dest_struct, dest_struct, cmock_line, "Function 'pb_decode' called with unexpected value for argument 'dest_struct'.");
  }
  if (cmock_call_instance->ReturnThruPtr_stream_Used)
  {
    memcpy((void*)stream, (void*)cmock_call_instance->ReturnThruPtr_stream_Val,
      cmock_call_instance->ReturnThruPtr_stream_Size);
  }
  if (cmock_call_instance->ReturnThruPtr_dest_struct_Used)
  {
    memcpy((void*)dest_struct, (void*)cmock_call_instance->ReturnThruPtr_dest_struct_Val,
      cmock_call_instance->ReturnThruPtr_dest_struct_Size);
  }
  return cmock_call_instance->ReturnVal;
}

void CMockExpectParameters_pb_decode(CMOCK_pb_decode_CALL_INSTANCE* cmock_call_instance, pb_istream_t* stream, int stream_Depth, const pb_field_t* fields, int fields_Depth, void* dest_struct, int dest_struct_Depth)
{
  cmock_call_instance->Expected_stream = stream;
  cmock_call_instance->Expected_stream_Depth = stream_Depth;
  cmock_call_instance->IgnoreArg_stream = 0;
  cmock_call_instance->ReturnThruPtr_stream_Used = 0;
  cmock_call_instance->Expected_fields = (pb_field_t*)fields;
  cmock_call_instance->Expected_fields_Depth = fields_Depth;
  cmock_call_instance->IgnoreArg_fields = 0;
  cmock_call_instance->Expected_dest_struct = dest_struct;
  cmock_call_instance->Expected_dest_struct_Depth = dest_struct_Depth;
  cmock_call_instance->IgnoreArg_dest_struct = 0;
  cmock_call_instance->ReturnThruPtr_dest_struct_Used = 0;
}

void pb_decode_CMockIgnoreAndReturn(UNITY_LINE_TYPE cmock_line, bool cmock_to_return)
{
  CMOCK_MEM_INDEX_TYPE cmock_guts_index = CMock_Guts_MemNew(sizeof(CMOCK_pb_decode_CALL_INSTANCE));
  CMOCK_pb_decode_CALL_INSTANCE* cmock_call_instance = (CMOCK_pb_decode_CALL_INSTANCE*)CMock_Guts_GetAddressFor(cmock_guts_index);
  UNITY_TEST_ASSERT_NOT_NULL(cmock_call_instance, cmock_line, "CMock has run out of memory. Please allocate more.");
  Mock.pb_decode_CallInstance = CMock_Guts_MemChain(Mock.pb_decode_CallInstance, cmock_guts_index);
  cmock_call_instance->LineNumber = cmock_line;
  cmock_call_instance->ReturnVal = cmock_to_return;
  Mock.pb_decode_IgnoreBool = (int)1;
}

void pb_decode_CMockExpectAndReturn(UNITY_LINE_TYPE cmock_line, pb_istream_t* stream, const pb_field_t* fields, void* dest_struct, bool cmock_to_return)
{
  CMOCK_MEM_INDEX_TYPE cmock_guts_index = CMock_Guts_MemNew(sizeof(CMOCK_pb_decode_CALL_INSTANCE));
  CMOCK_pb_decode_CALL_INSTANCE* cmock_call_instance = (CMOCK_pb_decode_CALL_INSTANCE*)CMock_Guts_GetAddressFor(cmock_guts_index);
  UNITY_TEST_ASSERT_NOT_NULL(cmock_call_instance, cmock_line, "CMock has run out of memory. Please allocate more.");
  Mock.pb_decode_CallInstance = CMock_Guts_MemChain(Mock.pb_decode_CallInstance, cmock_guts_index);
  cmock_call_instance->LineNumber = cmock_line;
  cmock_call_instance->CallOrder = ++GlobalExpectCount;
  CMockExpectParameters_pb_decode(cmock_call_instance, stream, 0, fields, 0, dest_struct, 0);
  cmock_call_instance->ReturnVal = cmock_to_return;
}

void pb_decode_StubWithCallback(CMOCK_pb_decode_CALLBACK Callback)
{
  Mock.pb_decode_CallbackFunctionPointer = Callback;
}

void pb_decode_CMockExpectWithArrayAndReturn(UNITY_LINE_TYPE cmock_line, pb_istream_t* stream, int stream_Depth, pb_field_t* fields, int fields_Depth, void* dest_struct, int dest_struct_Depth, bool cmock_to_return)
{
  CMOCK_MEM_INDEX_TYPE cmock_guts_index = CMock_Guts_MemNew(sizeof(CMOCK_pb_decode_CALL_INSTANCE));
  CMOCK_pb_decode_CALL_INSTANCE* cmock_call_instance = (CMOCK_pb_decode_CALL_INSTANCE*)CMock_Guts_GetAddressFor(cmock_guts_index);
  UNITY_TEST_ASSERT_NOT_NULL(cmock_call_instance, cmock_line, "CMock has run out of memory. Please allocate more.");
  Mock.pb_decode_CallInstance = CMock_Guts_MemChain(Mock.pb_decode_CallInstance, cmock_guts_index);
  cmock_call_instance->LineNumber = cmock_line;
  cmock_call_instance->CallOrder = ++GlobalExpectCount;
  CMockExpectParameters_pb_decode(cmock_call_instance, stream, stream_Depth, fields, fields_Depth, dest_struct, dest_struct_Depth);
  cmock_call_instance->ReturnVal = cmock_to_return;
}

void pb_decode_CMockReturnMemThruPtr_stream(UNITY_LINE_TYPE cmock_line, pb_istream_t* stream, int cmock_size)
{
  CMOCK_pb_decode_CALL_INSTANCE* cmock_call_instance = cmock_call_instance = (CMOCK_pb_decode_CALL_INSTANCE*)CMock_Guts_GetAddressFor(CMock_Guts_MemEndOfChain(Mock.pb_decode_CallInstance));
  UNITY_TEST_ASSERT_NOT_NULL(cmock_call_instance, cmock_line, "stream ReturnThruPtr called before Expect on 'pb_decode'.");
  cmock_call_instance->ReturnThruPtr_stream_Used = 1;
  cmock_call_instance->ReturnThruPtr_stream_Val = stream;
  cmock_call_instance->ReturnThruPtr_stream_Size = cmock_size;
}

void pb_decode_CMockReturnMemThruPtr_dest_struct(UNITY_LINE_TYPE cmock_line, void* dest_struct, int cmock_size)
{
  CMOCK_pb_decode_CALL_INSTANCE* cmock_call_instance = cmock_call_instance = (CMOCK_pb_decode_CALL_INSTANCE*)CMock_Guts_GetAddressFor(CMock_Guts_MemEndOfChain(Mock.pb_decode_CallInstance));
  UNITY_TEST_ASSERT_NOT_NULL(cmock_call_instance, cmock_line, "dest_struct ReturnThruPtr called before Expect on 'pb_decode'.");
  cmock_call_instance->ReturnThruPtr_dest_struct_Used = 1;
  cmock_call_instance->ReturnThruPtr_dest_struct_Val = dest_struct;
  cmock_call_instance->ReturnThruPtr_dest_struct_Size = cmock_size;
}

void pb_decode_CMockIgnoreArg_stream(UNITY_LINE_TYPE cmock_line)
{
  CMOCK_pb_decode_CALL_INSTANCE* cmock_call_instance = cmock_call_instance = (CMOCK_pb_decode_CALL_INSTANCE*)CMock_Guts_GetAddressFor(CMock_Guts_MemEndOfChain(Mock.pb_decode_CallInstance));
  UNITY_TEST_ASSERT_NOT_NULL(cmock_call_instance, cmock_line, "stream IgnoreArg called before Expect on 'pb_decode'.");
  cmock_call_instance->IgnoreArg_stream = 1;
}

void pb_decode_CMockIgnoreArg_fields(UNITY_LINE_TYPE cmock_line)
{
  CMOCK_pb_decode_CALL_INSTANCE* cmock_call_instance = cmock_call_instance = (CMOCK_pb_decode_CALL_INSTANCE*)CMock_Guts_GetAddressFor(CMock_Guts_MemEndOfChain(Mock.pb_decode_CallInstance));
  UNITY_TEST_ASSERT_NOT_NULL(cmock_call_instance, cmock_line, "fields IgnoreArg called before Expect on 'pb_decode'.");
  cmock_call_instance->IgnoreArg_fields = 1;
}

void pb_decode_CMockIgnoreArg_dest_struct(UNITY_LINE_TYPE cmock_line)
{
  CMOCK_pb_decode_CALL_INSTANCE* cmock_call_instance = cmock_call_instance = (CMOCK_pb_decode_CALL_INSTANCE*)CMock_Guts_GetAddressFor(CMock_Guts_MemEndOfChain(Mock.pb_decode_CallInstance));
  UNITY_TEST_ASSERT_NOT_NULL(cmock_call_instance, cmock_line, "dest_struct IgnoreArg called before Expect on 'pb_decode'.");
  cmock_call_instance->IgnoreArg_dest_struct = 1;
}

bool pb_decode_noinit(pb_istream_t* stream, const pb_field_t* fields, void* dest_struct)
{
  UNITY_LINE_TYPE cmock_line = TEST_LINE_NUM;
  CMOCK_pb_decode_noinit_CALL_INSTANCE* cmock_call_instance = (CMOCK_pb_decode_noinit_CALL_INSTANCE*)CMock_Guts_GetAddressFor(Mock.pb_decode_noinit_CallInstance);
  Mock.pb_decode_noinit_CallInstance = CMock_Guts_MemNext(Mock.pb_decode_noinit_CallInstance);
  if (Mock.pb_decode_noinit_IgnoreBool)
  {
    if (cmock_call_instance == NULL)
      return Mock.pb_decode_noinit_FinalReturn;
    Mock.pb_decode_noinit_FinalReturn = cmock_call_instance->ReturnVal;
    return cmock_call_instance->ReturnVal;
  }
  if (Mock.pb_decode_noinit_CallbackFunctionPointer != NULL)
  {
    return Mock.pb_decode_noinit_CallbackFunctionPointer(stream, fields, dest_struct, Mock.pb_decode_noinit_CallbackCalls++);
  }
  UNITY_TEST_ASSERT_NOT_NULL(cmock_call_instance, cmock_line, "Function 'pb_decode_noinit' called more times than expected.");
  cmock_line = cmock_call_instance->LineNumber;
  if (cmock_call_instance->CallOrder > ++GlobalVerifyOrder)
    UNITY_TEST_FAIL(cmock_line, "Function 'pb_decode_noinit' called earlier than expected.");
  if (cmock_call_instance->CallOrder < GlobalVerifyOrder)
    UNITY_TEST_FAIL(cmock_line, "Function 'pb_decode_noinit' called later than expected.");
  if (!cmock_call_instance->IgnoreArg_stream)
  {
    if (cmock_call_instance->Expected_stream == NULL)
      { UNITY_TEST_ASSERT_NULL(stream, cmock_line, "Expected NULL. Function 'pb_decode_noinit' called with unexpected value for argument 'stream'."); }
    else if (cmock_call_instance->Expected_stream_Depth == 0)
      { UNITY_TEST_ASSERT_EQUAL_PTR(cmock_call_instance->Expected_stream, stream, cmock_line, "Function 'pb_decode_noinit' called with unexpected value for argument 'stream'."); }
    else
      { UNITY_TEST_ASSERT_EQUAL_MEMORY_ARRAY((void*)(cmock_call_instance->Expected_stream), (void*)(stream), sizeof(pb_istream_t), cmock_call_instance->Expected_stream_Depth, cmock_line, "Function 'pb_decode_noinit' called with unexpected value for argument 'stream'."); }
  }
  if (!cmock_call_instance->IgnoreArg_fields)
  {
    if (cmock_call_instance->Expected_fields == NULL)
      { UNITY_TEST_ASSERT_NULL(fields, cmock_line, "Expected NULL. Function 'pb_decode_noinit' called with unexpected value for argument 'fields'."); }
    else if (cmock_call_instance->Expected_fields_Depth == 0)
      { UNITY_TEST_ASSERT_EQUAL_PTR(cmock_call_instance->Expected_fields, fields, cmock_line, "Function 'pb_decode_noinit' called with unexpected value for argument 'fields'."); }
    else
      { UNITY_TEST_ASSERT_EQUAL_MEMORY_ARRAY((void*)(cmock_call_instance->Expected_fields), (void*)(fields), sizeof(pb_field_t), cmock_call_instance->Expected_fields_Depth, cmock_line, "Function 'pb_decode_noinit' called with unexpected value for argument 'fields'."); }
  }
  if (!cmock_call_instance->IgnoreArg_dest_struct)
  {
    UNITY_TEST_ASSERT_EQUAL_PTR(cmock_call_instance->Expected_dest_struct, dest_struct, cmock_line, "Function 'pb_decode_noinit' called with unexpected value for argument 'dest_struct'.");
  }
  if (cmock_call_instance->ReturnThruPtr_stream_Used)
  {
    memcpy((void*)stream, (void*)cmock_call_instance->ReturnThruPtr_stream_Val,
      cmock_call_instance->ReturnThruPtr_stream_Size);
  }
  if (cmock_call_instance->ReturnThruPtr_dest_struct_Used)
  {
    memcpy((void*)dest_struct, (void*)cmock_call_instance->ReturnThruPtr_dest_struct_Val,
      cmock_call_instance->ReturnThruPtr_dest_struct_Size);
  }
  return cmock_call_instance->ReturnVal;
}

void CMockExpectParameters_pb_decode_noinit(CMOCK_pb_decode_noinit_CALL_INSTANCE* cmock_call_instance, pb_istream_t* stream, int stream_Depth, const pb_field_t* fields, int fields_Depth, void* dest_struct, int dest_struct_Depth)
{
  cmock_call_instance->Expected_stream = stream;
  cmock_call_instance->Expected_stream_Depth = stream_Depth;
  cmock_call_instance->IgnoreArg_stream = 0;
  cmock_call_instance->ReturnThruPtr_stream_Used = 0;
  cmock_call_instance->Expected_fields = (pb_field_t*)fields;
  cmock_call_instance->Expected_fields_Depth = fields_Depth;
  cmock_call_instance->IgnoreArg_fields = 0;
  cmock_call_instance->Expected_dest_struct = dest_struct;
  cmock_call_instance->Expected_dest_struct_Depth = dest_struct_Depth;
  cmock_call_instance->IgnoreArg_dest_struct = 0;
  cmock_call_instance->ReturnThruPtr_dest_struct_Used = 0;
}

void pb_decode_noinit_CMockIgnoreAndReturn(UNITY_LINE_TYPE cmock_line, bool cmock_to_return)
{
  CMOCK_MEM_INDEX_TYPE cmock_guts_index = CMock_Guts_MemNew(sizeof(CMOCK_pb_decode_noinit_CALL_INSTANCE));
  CMOCK_pb_decode_noinit_CALL_INSTANCE* cmock_call_instance = (CMOCK_pb_decode_noinit_CALL_INSTANCE*)CMock_Guts_GetAddressFor(cmock_guts_index);
  UNITY_TEST_ASSERT_NOT_NULL(cmock_call_instance, cmock_line, "CMock has run out of memory. Please allocate more.");
  Mock.pb_decode_noinit_CallInstance = CMock_Guts_MemChain(Mock.pb_decode_noinit_CallInstance, cmock_guts_index);
  cmock_call_instance->LineNumber = cmock_line;
  cmock_call_instance->ReturnVal = cmock_to_return;
  Mock.pb_decode_noinit_IgnoreBool = (int)1;
}

void pb_decode_noinit_CMockExpectAndReturn(UNITY_LINE_TYPE cmock_line, pb_istream_t* stream, const pb_field_t* fields, void* dest_struct, bool cmock_to_return)
{
  CMOCK_MEM_INDEX_TYPE cmock_guts_index = CMock_Guts_MemNew(sizeof(CMOCK_pb_decode_noinit_CALL_INSTANCE));
  CMOCK_pb_decode_noinit_CALL_INSTANCE* cmock_call_instance = (CMOCK_pb_decode_noinit_CALL_INSTANCE*)CMock_Guts_GetAddressFor(cmock_guts_index);
  UNITY_TEST_ASSERT_NOT_NULL(cmock_call_instance, cmock_line, "CMock has run out of memory. Please allocate more.");
  Mock.pb_decode_noinit_CallInstance = CMock_Guts_MemChain(Mock.pb_decode_noinit_CallInstance, cmock_guts_index);
  cmock_call_instance->LineNumber = cmock_line;
  cmock_call_instance->CallOrder = ++GlobalExpectCount;
  CMockExpectParameters_pb_decode_noinit(cmock_call_instance, stream, 0, fields, 0, dest_struct, 0);
  cmock_call_instance->ReturnVal = cmock_to_return;
}

void pb_decode_noinit_StubWithCallback(CMOCK_pb_decode_noinit_CALLBACK Callback)
{
  Mock.pb_decode_noinit_CallbackFunctionPointer = Callback;
}

void pb_decode_noinit_CMockExpectWithArrayAndReturn(UNITY_LINE_TYPE cmock_line, pb_istream_t* stream, int stream_Depth, pb_field_t* fields, int fields_Depth, void* dest_struct, int dest_struct_Depth, bool cmock_to_return)
{
  CMOCK_MEM_INDEX_TYPE cmock_guts_index = CMock_Guts_MemNew(sizeof(CMOCK_pb_decode_noinit_CALL_INSTANCE));
  CMOCK_pb_decode_noinit_CALL_INSTANCE* cmock_call_instance = (CMOCK_pb_decode_noinit_CALL_INSTANCE*)CMock_Guts_GetAddressFor(cmock_guts_index);
  UNITY_TEST_ASSERT_NOT_NULL(cmock_call_instance, cmock_line, "CMock has run out of memory. Please allocate more.");
  Mock.pb_decode_noinit_CallInstance = CMock_Guts_MemChain(Mock.pb_decode_noinit_CallInstance, cmock_guts_index);
  cmock_call_instance->LineNumber = cmock_line;
  cmock_call_instance->CallOrder = ++GlobalExpectCount;
  CMockExpectParameters_pb_decode_noinit(cmock_call_instance, stream, stream_Depth, fields, fields_Depth, dest_struct, dest_struct_Depth);
  cmock_call_instance->ReturnVal = cmock_to_return;
}

void pb_decode_noinit_CMockReturnMemThruPtr_stream(UNITY_LINE_TYPE cmock_line, pb_istream_t* stream, int cmock_size)
{
  CMOCK_pb_decode_noinit_CALL_INSTANCE* cmock_call_instance = cmock_call_instance = (CMOCK_pb_decode_noinit_CALL_INSTANCE*)CMock_Guts_GetAddressFor(CMock_Guts_MemEndOfChain(Mock.pb_decode_noinit_CallInstance));
  UNITY_TEST_ASSERT_NOT_NULL(cmock_call_instance, cmock_line, "stream ReturnThruPtr called before Expect on 'pb_decode_noinit'.");
  cmock_call_instance->ReturnThruPtr_stream_Used = 1;
  cmock_call_instance->ReturnThruPtr_stream_Val = stream;
  cmock_call_instance->ReturnThruPtr_stream_Size = cmock_size;
}

void pb_decode_noinit_CMockReturnMemThruPtr_dest_struct(UNITY_LINE_TYPE cmock_line, void* dest_struct, int cmock_size)
{
  CMOCK_pb_decode_noinit_CALL_INSTANCE* cmock_call_instance = cmock_call_instance = (CMOCK_pb_decode_noinit_CALL_INSTANCE*)CMock_Guts_GetAddressFor(CMock_Guts_MemEndOfChain(Mock.pb_decode_noinit_CallInstance));
  UNITY_TEST_ASSERT_NOT_NULL(cmock_call_instance, cmock_line, "dest_struct ReturnThruPtr called before Expect on 'pb_decode_noinit'.");
  cmock_call_instance->ReturnThruPtr_dest_struct_Used = 1;
  cmock_call_instance->ReturnThruPtr_dest_struct_Val = dest_struct;
  cmock_call_instance->ReturnThruPtr_dest_struct_Size = cmock_size;
}

void pb_decode_noinit_CMockIgnoreArg_stream(UNITY_LINE_TYPE cmock_line)
{
  CMOCK_pb_decode_noinit_CALL_INSTANCE* cmock_call_instance = cmock_call_instance = (CMOCK_pb_decode_noinit_CALL_INSTANCE*)CMock_Guts_GetAddressFor(CMock_Guts_MemEndOfChain(Mock.pb_decode_noinit_CallInstance));
  UNITY_TEST_ASSERT_NOT_NULL(cmock_call_instance, cmock_line, "stream IgnoreArg called before Expect on 'pb_decode_noinit'.");
  cmock_call_instance->IgnoreArg_stream = 1;
}

void pb_decode_noinit_CMockIgnoreArg_fields(UNITY_LINE_TYPE cmock_line)
{
  CMOCK_pb_decode_noinit_CALL_INSTANCE* cmock_call_instance = cmock_call_instance = (CMOCK_pb_decode_noinit_CALL_INSTANCE*)CMock_Guts_GetAddressFor(CMock_Guts_MemEndOfChain(Mock.pb_decode_noinit_CallInstance));
  UNITY_TEST_ASSERT_NOT_NULL(cmock_call_instance, cmock_line, "fields IgnoreArg called before Expect on 'pb_decode_noinit'.");
  cmock_call_instance->IgnoreArg_fields = 1;
}

void pb_decode_noinit_CMockIgnoreArg_dest_struct(UNITY_LINE_TYPE cmock_line)
{
  CMOCK_pb_decode_noinit_CALL_INSTANCE* cmock_call_instance = cmock_call_instance = (CMOCK_pb_decode_noinit_CALL_INSTANCE*)CMock_Guts_GetAddressFor(CMock_Guts_MemEndOfChain(Mock.pb_decode_noinit_CallInstance));
  UNITY_TEST_ASSERT_NOT_NULL(cmock_call_instance, cmock_line, "dest_struct IgnoreArg called before Expect on 'pb_decode_noinit'.");
  cmock_call_instance->IgnoreArg_dest_struct = 1;
}

bool pb_decode_delimited(pb_istream_t* stream, const pb_field_t* fields, void* dest_struct)
{
  UNITY_LINE_TYPE cmock_line = TEST_LINE_NUM;
  CMOCK_pb_decode_delimited_CALL_INSTANCE* cmock_call_instance = (CMOCK_pb_decode_delimited_CALL_INSTANCE*)CMock_Guts_GetAddressFor(Mock.pb_decode_delimited_CallInstance);
  Mock.pb_decode_delimited_CallInstance = CMock_Guts_MemNext(Mock.pb_decode_delimited_CallInstance);
  if (Mock.pb_decode_delimited_IgnoreBool)
  {
    if (cmock_call_instance == NULL)
      return Mock.pb_decode_delimited_FinalReturn;
    Mock.pb_decode_delimited_FinalReturn = cmock_call_instance->ReturnVal;
    return cmock_call_instance->ReturnVal;
  }
  if (Mock.pb_decode_delimited_CallbackFunctionPointer != NULL)
  {
    return Mock.pb_decode_delimited_CallbackFunctionPointer(stream, fields, dest_struct, Mock.pb_decode_delimited_CallbackCalls++);
  }
  UNITY_TEST_ASSERT_NOT_NULL(cmock_call_instance, cmock_line, "Function 'pb_decode_delimited' called more times than expected.");
  cmock_line = cmock_call_instance->LineNumber;
  if (cmock_call_instance->CallOrder > ++GlobalVerifyOrder)
    UNITY_TEST_FAIL(cmock_line, "Function 'pb_decode_delimited' called earlier than expected.");
  if (cmock_call_instance->CallOrder < GlobalVerifyOrder)
    UNITY_TEST_FAIL(cmock_line, "Function 'pb_decode_delimited' called later than expected.");
  if (!cmock_call_instance->IgnoreArg_stream)
  {
    if (cmock_call_instance->Expected_stream == NULL)
      { UNITY_TEST_ASSERT_NULL(stream, cmock_line, "Expected NULL. Function 'pb_decode_delimited' called with unexpected value for argument 'stream'."); }
    else if (cmock_call_instance->Expected_stream_Depth == 0)
      { UNITY_TEST_ASSERT_EQUAL_PTR(cmock_call_instance->Expected_stream, stream, cmock_line, "Function 'pb_decode_delimited' called with unexpected value for argument 'stream'."); }
    else
      { UNITY_TEST_ASSERT_EQUAL_MEMORY_ARRAY((void*)(cmock_call_instance->Expected_stream), (void*)(stream), sizeof(pb_istream_t), cmock_call_instance->Expected_stream_Depth, cmock_line, "Function 'pb_decode_delimited' called with unexpected value for argument 'stream'."); }
  }
  if (!cmock_call_instance->IgnoreArg_fields)
  {
    if (cmock_call_instance->Expected_fields == NULL)
      { UNITY_TEST_ASSERT_NULL(fields, cmock_line, "Expected NULL. Function 'pb_decode_delimited' called with unexpected value for argument 'fields'."); }
    else if (cmock_call_instance->Expected_fields_Depth == 0)
      { UNITY_TEST_ASSERT_EQUAL_PTR(cmock_call_instance->Expected_fields, fields, cmock_line, "Function 'pb_decode_delimited' called with unexpected value for argument 'fields'."); }
    else
      { UNITY_TEST_ASSERT_EQUAL_MEMORY_ARRAY((void*)(cmock_call_instance->Expected_fields), (void*)(fields), sizeof(pb_field_t), cmock_call_instance->Expected_fields_Depth, cmock_line, "Function 'pb_decode_delimited' called with unexpected value for argument 'fields'."); }
  }
  if (!cmock_call_instance->IgnoreArg_dest_struct)
  {
    UNITY_TEST_ASSERT_EQUAL_PTR(cmock_call_instance->Expected_dest_struct, dest_struct, cmock_line, "Function 'pb_decode_delimited' called with unexpected value for argument 'dest_struct'.");
  }
  if (cmock_call_instance->ReturnThruPtr_stream_Used)
  {
    memcpy((void*)stream, (void*)cmock_call_instance->ReturnThruPtr_stream_Val,
      cmock_call_instance->ReturnThruPtr_stream_Size);
  }
  if (cmock_call_instance->ReturnThruPtr_dest_struct_Used)
  {
    memcpy((void*)dest_struct, (void*)cmock_call_instance->ReturnThruPtr_dest_struct_Val,
      cmock_call_instance->ReturnThruPtr_dest_struct_Size);
  }
  return cmock_call_instance->ReturnVal;
}

void CMockExpectParameters_pb_decode_delimited(CMOCK_pb_decode_delimited_CALL_INSTANCE* cmock_call_instance, pb_istream_t* stream, int stream_Depth, const pb_field_t* fields, int fields_Depth, void* dest_struct, int dest_struct_Depth)
{
  cmock_call_instance->Expected_stream = stream;
  cmock_call_instance->Expected_stream_Depth = stream_Depth;
  cmock_call_instance->IgnoreArg_stream = 0;
  cmock_call_instance->ReturnThruPtr_stream_Used = 0;
  cmock_call_instance->Expected_fields = (pb_field_t*)fields;
  cmock_call_instance->Expected_fields_Depth = fields_Depth;
  cmock_call_instance->IgnoreArg_fields = 0;
  cmock_call_instance->Expected_dest_struct = dest_struct;
  cmock_call_instance->Expected_dest_struct_Depth = dest_struct_Depth;
  cmock_call_instance->IgnoreArg_dest_struct = 0;
  cmock_call_instance->ReturnThruPtr_dest_struct_Used = 0;
}

void pb_decode_delimited_CMockIgnoreAndReturn(UNITY_LINE_TYPE cmock_line, bool cmock_to_return)
{
  CMOCK_MEM_INDEX_TYPE cmock_guts_index = CMock_Guts_MemNew(sizeof(CMOCK_pb_decode_delimited_CALL_INSTANCE));
  CMOCK_pb_decode_delimited_CALL_INSTANCE* cmock_call_instance = (CMOCK_pb_decode_delimited_CALL_INSTANCE*)CMock_Guts_GetAddressFor(cmock_guts_index);
  UNITY_TEST_ASSERT_NOT_NULL(cmock_call_instance, cmock_line, "CMock has run out of memory. Please allocate more.");
  Mock.pb_decode_delimited_CallInstance = CMock_Guts_MemChain(Mock.pb_decode_delimited_CallInstance, cmock_guts_index);
  cmock_call_instance->LineNumber = cmock_line;
  cmock_call_instance->ReturnVal = cmock_to_return;
  Mock.pb_decode_delimited_IgnoreBool = (int)1;
}

void pb_decode_delimited_CMockExpectAndReturn(UNITY_LINE_TYPE cmock_line, pb_istream_t* stream, const pb_field_t* fields, void* dest_struct, bool cmock_to_return)
{
  CMOCK_MEM_INDEX_TYPE cmock_guts_index = CMock_Guts_MemNew(sizeof(CMOCK_pb_decode_delimited_CALL_INSTANCE));
  CMOCK_pb_decode_delimited_CALL_INSTANCE* cmock_call_instance = (CMOCK_pb_decode_delimited_CALL_INSTANCE*)CMock_Guts_GetAddressFor(cmock_guts_index);
  UNITY_TEST_ASSERT_NOT_NULL(cmock_call_instance, cmock_line, "CMock has run out of memory. Please allocate more.");
  Mock.pb_decode_delimited_CallInstance = CMock_Guts_MemChain(Mock.pb_decode_delimited_CallInstance, cmock_guts_index);
  cmock_call_instance->LineNumber = cmock_line;
  cmock_call_instance->CallOrder = ++GlobalExpectCount;
  CMockExpectParameters_pb_decode_delimited(cmock_call_instance, stream, 0, fields, 0, dest_struct, 0);
  cmock_call_instance->ReturnVal = cmock_to_return;
}

void pb_decode_delimited_StubWithCallback(CMOCK_pb_decode_delimited_CALLBACK Callback)
{
  Mock.pb_decode_delimited_CallbackFunctionPointer = Callback;
}

void pb_decode_delimited_CMockExpectWithArrayAndReturn(UNITY_LINE_TYPE cmock_line, pb_istream_t* stream, int stream_Depth, pb_field_t* fields, int fields_Depth, void* dest_struct, int dest_struct_Depth, bool cmock_to_return)
{
  CMOCK_MEM_INDEX_TYPE cmock_guts_index = CMock_Guts_MemNew(sizeof(CMOCK_pb_decode_delimited_CALL_INSTANCE));
  CMOCK_pb_decode_delimited_CALL_INSTANCE* cmock_call_instance = (CMOCK_pb_decode_delimited_CALL_INSTANCE*)CMock_Guts_GetAddressFor(cmock_guts_index);
  UNITY_TEST_ASSERT_NOT_NULL(cmock_call_instance, cmock_line, "CMock has run out of memory. Please allocate more.");
  Mock.pb_decode_delimited_CallInstance = CMock_Guts_MemChain(Mock.pb_decode_delimited_CallInstance, cmock_guts_index);
  cmock_call_instance->LineNumber = cmock_line;
  cmock_call_instance->CallOrder = ++GlobalExpectCount;
  CMockExpectParameters_pb_decode_delimited(cmock_call_instance, stream, stream_Depth, fields, fields_Depth, dest_struct, dest_struct_Depth);
  cmock_call_instance->ReturnVal = cmock_to_return;
}

void pb_decode_delimited_CMockReturnMemThruPtr_stream(UNITY_LINE_TYPE cmock_line, pb_istream_t* stream, int cmock_size)
{
  CMOCK_pb_decode_delimited_CALL_INSTANCE* cmock_call_instance = cmock_call_instance = (CMOCK_pb_decode_delimited_CALL_INSTANCE*)CMock_Guts_GetAddressFor(CMock_Guts_MemEndOfChain(Mock.pb_decode_delimited_CallInstance));
  UNITY_TEST_ASSERT_NOT_NULL(cmock_call_instance, cmock_line, "stream ReturnThruPtr called before Expect on 'pb_decode_delimited'.");
  cmock_call_instance->ReturnThruPtr_stream_Used = 1;
  cmock_call_instance->ReturnThruPtr_stream_Val = stream;
  cmock_call_instance->ReturnThruPtr_stream_Size = cmock_size;
}

void pb_decode_delimited_CMockReturnMemThruPtr_dest_struct(UNITY_LINE_TYPE cmock_line, void* dest_struct, int cmock_size)
{
  CMOCK_pb_decode_delimited_CALL_INSTANCE* cmock_call_instance = cmock_call_instance = (CMOCK_pb_decode_delimited_CALL_INSTANCE*)CMock_Guts_GetAddressFor(CMock_Guts_MemEndOfChain(Mock.pb_decode_delimited_CallInstance));
  UNITY_TEST_ASSERT_NOT_NULL(cmock_call_instance, cmock_line, "dest_struct ReturnThruPtr called before Expect on 'pb_decode_delimited'.");
  cmock_call_instance->ReturnThruPtr_dest_struct_Used = 1;
  cmock_call_instance->ReturnThruPtr_dest_struct_Val = dest_struct;
  cmock_call_instance->ReturnThruPtr_dest_struct_Size = cmock_size;
}

void pb_decode_delimited_CMockIgnoreArg_stream(UNITY_LINE_TYPE cmock_line)
{
  CMOCK_pb_decode_delimited_CALL_INSTANCE* cmock_call_instance = cmock_call_instance = (CMOCK_pb_decode_delimited_CALL_INSTANCE*)CMock_Guts_GetAddressFor(CMock_Guts_MemEndOfChain(Mock.pb_decode_delimited_CallInstance));
  UNITY_TEST_ASSERT_NOT_NULL(cmock_call_instance, cmock_line, "stream IgnoreArg called before Expect on 'pb_decode_delimited'.");
  cmock_call_instance->IgnoreArg_stream = 1;
}

void pb_decode_delimited_CMockIgnoreArg_fields(UNITY_LINE_TYPE cmock_line)
{
  CMOCK_pb_decode_delimited_CALL_INSTANCE* cmock_call_instance = cmock_call_instance = (CMOCK_pb_decode_delimited_CALL_INSTANCE*)CMock_Guts_GetAddressFor(CMock_Guts_MemEndOfChain(Mock.pb_decode_delimited_CallInstance));
  UNITY_TEST_ASSERT_NOT_NULL(cmock_call_instance, cmock_line, "fields IgnoreArg called before Expect on 'pb_decode_delimited'.");
  cmock_call_instance->IgnoreArg_fields = 1;
}

void pb_decode_delimited_CMockIgnoreArg_dest_struct(UNITY_LINE_TYPE cmock_line)
{
  CMOCK_pb_decode_delimited_CALL_INSTANCE* cmock_call_instance = cmock_call_instance = (CMOCK_pb_decode_delimited_CALL_INSTANCE*)CMock_Guts_GetAddressFor(CMock_Guts_MemEndOfChain(Mock.pb_decode_delimited_CallInstance));
  UNITY_TEST_ASSERT_NOT_NULL(cmock_call_instance, cmock_line, "dest_struct IgnoreArg called before Expect on 'pb_decode_delimited'.");
  cmock_call_instance->IgnoreArg_dest_struct = 1;
}

void pb_release(const pb_field_t* fields, void* dest_struct)
{
  UNITY_LINE_TYPE cmock_line = TEST_LINE_NUM;
  CMOCK_pb_release_CALL_INSTANCE* cmock_call_instance = (CMOCK_pb_release_CALL_INSTANCE*)CMock_Guts_GetAddressFor(Mock.pb_release_CallInstance);
  Mock.pb_release_CallInstance = CMock_Guts_MemNext(Mock.pb_release_CallInstance);
  if (Mock.pb_release_IgnoreBool)
  {
    return;
  }
  if (Mock.pb_release_CallbackFunctionPointer != NULL)
  {
    Mock.pb_release_CallbackFunctionPointer(fields, dest_struct, Mock.pb_release_CallbackCalls++);
    return;
  }
  UNITY_TEST_ASSERT_NOT_NULL(cmock_call_instance, cmock_line, "Function 'pb_release' called more times than expected.");
  cmock_line = cmock_call_instance->LineNumber;
  if (cmock_call_instance->CallOrder > ++GlobalVerifyOrder)
    UNITY_TEST_FAIL(cmock_line, "Function 'pb_release' called earlier than expected.");
  if (cmock_call_instance->CallOrder < GlobalVerifyOrder)
    UNITY_TEST_FAIL(cmock_line, "Function 'pb_release' called later than expected.");
  if (!cmock_call_instance->IgnoreArg_fields)
  {
    if (cmock_call_instance->Expected_fields == NULL)
      { UNITY_TEST_ASSERT_NULL(fields, cmock_line, "Expected NULL. Function 'pb_release' called with unexpected value for argument 'fields'."); }
    else if (cmock_call_instance->Expected_fields_Depth == 0)
      { UNITY_TEST_ASSERT_EQUAL_PTR(cmock_call_instance->Expected_fields, fields, cmock_line, "Function 'pb_release' called with unexpected value for argument 'fields'."); }
    else
      { UNITY_TEST_ASSERT_EQUAL_MEMORY_ARRAY((void*)(cmock_call_instance->Expected_fields), (void*)(fields), sizeof(pb_field_t), cmock_call_instance->Expected_fields_Depth, cmock_line, "Function 'pb_release' called with unexpected value for argument 'fields'."); }
  }
  if (!cmock_call_instance->IgnoreArg_dest_struct)
  {
    UNITY_TEST_ASSERT_EQUAL_PTR(cmock_call_instance->Expected_dest_struct, dest_struct, cmock_line, "Function 'pb_release' called with unexpected value for argument 'dest_struct'.");
  }
  if (cmock_call_instance->ReturnThruPtr_dest_struct_Used)
  {
    memcpy((void*)dest_struct, (void*)cmock_call_instance->ReturnThruPtr_dest_struct_Val,
      cmock_call_instance->ReturnThruPtr_dest_struct_Size);
  }
}

void CMockExpectParameters_pb_release(CMOCK_pb_release_CALL_INSTANCE* cmock_call_instance, const pb_field_t* fields, int fields_Depth, void* dest_struct, int dest_struct_Depth)
{
  cmock_call_instance->Expected_fields = (pb_field_t*)fields;
  cmock_call_instance->Expected_fields_Depth = fields_Depth;
  cmock_call_instance->IgnoreArg_fields = 0;
  cmock_call_instance->Expected_dest_struct = dest_struct;
  cmock_call_instance->Expected_dest_struct_Depth = dest_struct_Depth;
  cmock_call_instance->IgnoreArg_dest_struct = 0;
  cmock_call_instance->ReturnThruPtr_dest_struct_Used = 0;
}

void pb_release_CMockIgnore(void)
{
  Mock.pb_release_IgnoreBool = (int)1;
}

void pb_release_CMockExpect(UNITY_LINE_TYPE cmock_line, const pb_field_t* fields, void* dest_struct)
{
  CMOCK_MEM_INDEX_TYPE cmock_guts_index = CMock_Guts_MemNew(sizeof(CMOCK_pb_release_CALL_INSTANCE));
  CMOCK_pb_release_CALL_INSTANCE* cmock_call_instance = (CMOCK_pb_release_CALL_INSTANCE*)CMock_Guts_GetAddressFor(cmock_guts_index);
  UNITY_TEST_ASSERT_NOT_NULL(cmock_call_instance, cmock_line, "CMock has run out of memory. Please allocate more.");
  Mock.pb_release_CallInstance = CMock_Guts_MemChain(Mock.pb_release_CallInstance, cmock_guts_index);
  cmock_call_instance->LineNumber = cmock_line;
  cmock_call_instance->CallOrder = ++GlobalExpectCount;
  CMockExpectParameters_pb_release(cmock_call_instance, fields, 0, dest_struct, 0);
}

void pb_release_StubWithCallback(CMOCK_pb_release_CALLBACK Callback)
{
  Mock.pb_release_CallbackFunctionPointer = Callback;
}

void pb_release_CMockExpectWithArray(UNITY_LINE_TYPE cmock_line, pb_field_t* fields, int fields_Depth, void* dest_struct, int dest_struct_Depth)
{
  CMOCK_MEM_INDEX_TYPE cmock_guts_index = CMock_Guts_MemNew(sizeof(CMOCK_pb_release_CALL_INSTANCE));
  CMOCK_pb_release_CALL_INSTANCE* cmock_call_instance = (CMOCK_pb_release_CALL_INSTANCE*)CMock_Guts_GetAddressFor(cmock_guts_index);
  UNITY_TEST_ASSERT_NOT_NULL(cmock_call_instance, cmock_line, "CMock has run out of memory. Please allocate more.");
  Mock.pb_release_CallInstance = CMock_Guts_MemChain(Mock.pb_release_CallInstance, cmock_guts_index);
  cmock_call_instance->LineNumber = cmock_line;
  cmock_call_instance->CallOrder = ++GlobalExpectCount;
  CMockExpectParameters_pb_release(cmock_call_instance, fields, fields_Depth, dest_struct, dest_struct_Depth);
}

void pb_release_CMockReturnMemThruPtr_dest_struct(UNITY_LINE_TYPE cmock_line, void* dest_struct, int cmock_size)
{
  CMOCK_pb_release_CALL_INSTANCE* cmock_call_instance = cmock_call_instance = (CMOCK_pb_release_CALL_INSTANCE*)CMock_Guts_GetAddressFor(CMock_Guts_MemEndOfChain(Mock.pb_release_CallInstance));
  UNITY_TEST_ASSERT_NOT_NULL(cmock_call_instance, cmock_line, "dest_struct ReturnThruPtr called before Expect on 'pb_release'.");
  cmock_call_instance->ReturnThruPtr_dest_struct_Used = 1;
  cmock_call_instance->ReturnThruPtr_dest_struct_Val = dest_struct;
  cmock_call_instance->ReturnThruPtr_dest_struct_Size = cmock_size;
}

void pb_release_CMockIgnoreArg_fields(UNITY_LINE_TYPE cmock_line)
{
  CMOCK_pb_release_CALL_INSTANCE* cmock_call_instance = cmock_call_instance = (CMOCK_pb_release_CALL_INSTANCE*)CMock_Guts_GetAddressFor(CMock_Guts_MemEndOfChain(Mock.pb_release_CallInstance));
  UNITY_TEST_ASSERT_NOT_NULL(cmock_call_instance, cmock_line, "fields IgnoreArg called before Expect on 'pb_release'.");
  cmock_call_instance->IgnoreArg_fields = 1;
}

void pb_release_CMockIgnoreArg_dest_struct(UNITY_LINE_TYPE cmock_line)
{
  CMOCK_pb_release_CALL_INSTANCE* cmock_call_instance = cmock_call_instance = (CMOCK_pb_release_CALL_INSTANCE*)CMock_Guts_GetAddressFor(CMock_Guts_MemEndOfChain(Mock.pb_release_CallInstance));
  UNITY_TEST_ASSERT_NOT_NULL(cmock_call_instance, cmock_line, "dest_struct IgnoreArg called before Expect on 'pb_release'.");
  cmock_call_instance->IgnoreArg_dest_struct = 1;
}

pb_istream_t pb_istream_from_buffer(const pb_byte_t* buf, size_t bufsize)
{
  UNITY_LINE_TYPE cmock_line = TEST_LINE_NUM;
  CMOCK_pb_istream_from_buffer_CALL_INSTANCE* cmock_call_instance = (CMOCK_pb_istream_from_buffer_CALL_INSTANCE*)CMock_Guts_GetAddressFor(Mock.pb_istream_from_buffer_CallInstance);
  Mock.pb_istream_from_buffer_CallInstance = CMock_Guts_MemNext(Mock.pb_istream_from_buffer_CallInstance);
  if (Mock.pb_istream_from_buffer_IgnoreBool)
  {
    if (cmock_call_instance == NULL)
      return Mock.pb_istream_from_buffer_FinalReturn;
    memcpy(&Mock.pb_istream_from_buffer_FinalReturn, &cmock_call_instance->ReturnVal, sizeof(pb_istream_t));
    return cmock_call_instance->ReturnVal;
  }
  if (Mock.pb_istream_from_buffer_CallbackFunctionPointer != NULL)
  {
    return Mock.pb_istream_from_buffer_CallbackFunctionPointer(buf, bufsize, Mock.pb_istream_from_buffer_CallbackCalls++);
  }
  UNITY_TEST_ASSERT_NOT_NULL(cmock_call_instance, cmock_line, "Function 'pb_istream_from_buffer' called more times than expected.");
  cmock_line = cmock_call_instance->LineNumber;
  if (cmock_call_instance->CallOrder > ++GlobalVerifyOrder)
    UNITY_TEST_FAIL(cmock_line, "Function 'pb_istream_from_buffer' called earlier than expected.");
  if (cmock_call_instance->CallOrder < GlobalVerifyOrder)
    UNITY_TEST_FAIL(cmock_line, "Function 'pb_istream_from_buffer' called later than expected.");
  if (!cmock_call_instance->IgnoreArg_buf)
  {
    if (cmock_call_instance->Expected_buf == NULL)
      { UNITY_TEST_ASSERT_NULL(buf, cmock_line, "Expected NULL. Function 'pb_istream_from_buffer' called with unexpected value for argument 'buf'."); }
    else if (cmock_call_instance->Expected_buf_Depth == 0)
      { UNITY_TEST_ASSERT_EQUAL_PTR(cmock_call_instance->Expected_buf, buf, cmock_line, "Function 'pb_istream_from_buffer' called with unexpected value for argument 'buf'."); }
    else
      { UNITY_TEST_ASSERT_EQUAL_MEMORY_ARRAY((void*)(cmock_call_instance->Expected_buf), (void*)(buf), sizeof(pb_byte_t), cmock_call_instance->Expected_buf_Depth, cmock_line, "Function 'pb_istream_from_buffer' called with unexpected value for argument 'buf'."); }
  }
  if (!cmock_call_instance->IgnoreArg_bufsize)
  {
    UNITY_TEST_ASSERT_EQUAL_MEMORY((void*)(&cmock_call_instance->Expected_bufsize), (void*)(&bufsize), sizeof(size_t), cmock_line, "Function 'pb_istream_from_buffer' called with unexpected value for argument 'bufsize'.");
  }
  return cmock_call_instance->ReturnVal;
}

void CMockExpectParameters_pb_istream_from_buffer(CMOCK_pb_istream_from_buffer_CALL_INSTANCE* cmock_call_instance, const pb_byte_t* buf, int buf_Depth, size_t bufsize)
{
  cmock_call_instance->Expected_buf = (pb_byte_t*)buf;
  cmock_call_instance->Expected_buf_Depth = buf_Depth;
  cmock_call_instance->IgnoreArg_buf = 0;
  memcpy(&cmock_call_instance->Expected_bufsize, &bufsize, sizeof(size_t));
  cmock_call_instance->IgnoreArg_bufsize = 0;
}

void pb_istream_from_buffer_CMockIgnoreAndReturn(UNITY_LINE_TYPE cmock_line, pb_istream_t cmock_to_return)
{
  CMOCK_MEM_INDEX_TYPE cmock_guts_index = CMock_Guts_MemNew(sizeof(CMOCK_pb_istream_from_buffer_CALL_INSTANCE));
  CMOCK_pb_istream_from_buffer_CALL_INSTANCE* cmock_call_instance = (CMOCK_pb_istream_from_buffer_CALL_INSTANCE*)CMock_Guts_GetAddressFor(cmock_guts_index);
  UNITY_TEST_ASSERT_NOT_NULL(cmock_call_instance, cmock_line, "CMock has run out of memory. Please allocate more.");
  Mock.pb_istream_from_buffer_CallInstance = CMock_Guts_MemChain(Mock.pb_istream_from_buffer_CallInstance, cmock_guts_index);
  cmock_call_instance->LineNumber = cmock_line;
  cmock_call_instance->ReturnVal = cmock_to_return;
  Mock.pb_istream_from_buffer_IgnoreBool = (int)1;
}

void pb_istream_from_buffer_CMockExpectAndReturn(UNITY_LINE_TYPE cmock_line, const pb_byte_t* buf, size_t bufsize, pb_istream_t cmock_to_return)
{
  CMOCK_MEM_INDEX_TYPE cmock_guts_index = CMock_Guts_MemNew(sizeof(CMOCK_pb_istream_from_buffer_CALL_INSTANCE));
  CMOCK_pb_istream_from_buffer_CALL_INSTANCE* cmock_call_instance = (CMOCK_pb_istream_from_buffer_CALL_INSTANCE*)CMock_Guts_GetAddressFor(cmock_guts_index);
  UNITY_TEST_ASSERT_NOT_NULL(cmock_call_instance, cmock_line, "CMock has run out of memory. Please allocate more.");
  Mock.pb_istream_from_buffer_CallInstance = CMock_Guts_MemChain(Mock.pb_istream_from_buffer_CallInstance, cmock_guts_index);
  cmock_call_instance->LineNumber = cmock_line;
  cmock_call_instance->CallOrder = ++GlobalExpectCount;
  CMockExpectParameters_pb_istream_from_buffer(cmock_call_instance, buf, 0, bufsize);
  memcpy(&cmock_call_instance->ReturnVal, &cmock_to_return, sizeof(pb_istream_t));
}

void pb_istream_from_buffer_StubWithCallback(CMOCK_pb_istream_from_buffer_CALLBACK Callback)
{
  Mock.pb_istream_from_buffer_CallbackFunctionPointer = Callback;
}

void pb_istream_from_buffer_CMockExpectWithArrayAndReturn(UNITY_LINE_TYPE cmock_line, pb_byte_t* buf, int buf_Depth, size_t bufsize, pb_istream_t cmock_to_return)
{
  CMOCK_MEM_INDEX_TYPE cmock_guts_index = CMock_Guts_MemNew(sizeof(CMOCK_pb_istream_from_buffer_CALL_INSTANCE));
  CMOCK_pb_istream_from_buffer_CALL_INSTANCE* cmock_call_instance = (CMOCK_pb_istream_from_buffer_CALL_INSTANCE*)CMock_Guts_GetAddressFor(cmock_guts_index);
  UNITY_TEST_ASSERT_NOT_NULL(cmock_call_instance, cmock_line, "CMock has run out of memory. Please allocate more.");
  Mock.pb_istream_from_buffer_CallInstance = CMock_Guts_MemChain(Mock.pb_istream_from_buffer_CallInstance, cmock_guts_index);
  cmock_call_instance->LineNumber = cmock_line;
  cmock_call_instance->CallOrder = ++GlobalExpectCount;
  CMockExpectParameters_pb_istream_from_buffer(cmock_call_instance, buf, buf_Depth, bufsize);
  cmock_call_instance->ReturnVal = cmock_to_return;
}

void pb_istream_from_buffer_CMockIgnoreArg_buf(UNITY_LINE_TYPE cmock_line)
{
  CMOCK_pb_istream_from_buffer_CALL_INSTANCE* cmock_call_instance = cmock_call_instance = (CMOCK_pb_istream_from_buffer_CALL_INSTANCE*)CMock_Guts_GetAddressFor(CMock_Guts_MemEndOfChain(Mock.pb_istream_from_buffer_CallInstance));
  UNITY_TEST_ASSERT_NOT_NULL(cmock_call_instance, cmock_line, "buf IgnoreArg called before Expect on 'pb_istream_from_buffer'.");
  cmock_call_instance->IgnoreArg_buf = 1;
}

void pb_istream_from_buffer_CMockIgnoreArg_bufsize(UNITY_LINE_TYPE cmock_line)
{
  CMOCK_pb_istream_from_buffer_CALL_INSTANCE* cmock_call_instance = cmock_call_instance = (CMOCK_pb_istream_from_buffer_CALL_INSTANCE*)CMock_Guts_GetAddressFor(CMock_Guts_MemEndOfChain(Mock.pb_istream_from_buffer_CallInstance));
  UNITY_TEST_ASSERT_NOT_NULL(cmock_call_instance, cmock_line, "bufsize IgnoreArg called before Expect on 'pb_istream_from_buffer'.");
  cmock_call_instance->IgnoreArg_bufsize = 1;
}

bool pb_read(pb_istream_t* stream, pb_byte_t* buf, size_t count)
{
  UNITY_LINE_TYPE cmock_line = TEST_LINE_NUM;
  CMOCK_pb_read_CALL_INSTANCE* cmock_call_instance = (CMOCK_pb_read_CALL_INSTANCE*)CMock_Guts_GetAddressFor(Mock.pb_read_CallInstance);
  Mock.pb_read_CallInstance = CMock_Guts_MemNext(Mock.pb_read_CallInstance);
  if (Mock.pb_read_IgnoreBool)
  {
    if (cmock_call_instance == NULL)
      return Mock.pb_read_FinalReturn;
    Mock.pb_read_FinalReturn = cmock_call_instance->ReturnVal;
    return cmock_call_instance->ReturnVal;
  }
  if (Mock.pb_read_CallbackFunctionPointer != NULL)
  {
    return Mock.pb_read_CallbackFunctionPointer(stream, buf, count, Mock.pb_read_CallbackCalls++);
  }
  UNITY_TEST_ASSERT_NOT_NULL(cmock_call_instance, cmock_line, "Function 'pb_read' called more times than expected.");
  cmock_line = cmock_call_instance->LineNumber;
  if (cmock_call_instance->CallOrder > ++GlobalVerifyOrder)
    UNITY_TEST_FAIL(cmock_line, "Function 'pb_read' called earlier than expected.");
  if (cmock_call_instance->CallOrder < GlobalVerifyOrder)
    UNITY_TEST_FAIL(cmock_line, "Function 'pb_read' called later than expected.");
  if (!cmock_call_instance->IgnoreArg_stream)
  {
    if (cmock_call_instance->Expected_stream == NULL)
      { UNITY_TEST_ASSERT_NULL(stream, cmock_line, "Expected NULL. Function 'pb_read' called with unexpected value for argument 'stream'."); }
    else if (cmock_call_instance->Expected_stream_Depth == 0)
      { UNITY_TEST_ASSERT_EQUAL_PTR(cmock_call_instance->Expected_stream, stream, cmock_line, "Function 'pb_read' called with unexpected value for argument 'stream'."); }
    else
      { UNITY_TEST_ASSERT_EQUAL_MEMORY_ARRAY((void*)(cmock_call_instance->Expected_stream), (void*)(stream), sizeof(pb_istream_t), cmock_call_instance->Expected_stream_Depth, cmock_line, "Function 'pb_read' called with unexpected value for argument 'stream'."); }
  }
  if (!cmock_call_instance->IgnoreArg_buf)
  {
    if (cmock_call_instance->Expected_buf == NULL)
      { UNITY_TEST_ASSERT_NULL(buf, cmock_line, "Expected NULL. Function 'pb_read' called with unexpected value for argument 'buf'."); }
    else if (cmock_call_instance->Expected_buf_Depth == 0)
      { UNITY_TEST_ASSERT_EQUAL_PTR(cmock_call_instance->Expected_buf, buf, cmock_line, "Function 'pb_read' called with unexpected value for argument 'buf'."); }
    else
      { UNITY_TEST_ASSERT_EQUAL_MEMORY_ARRAY((void*)(cmock_call_instance->Expected_buf), (void*)(buf), sizeof(pb_byte_t), cmock_call_instance->Expected_buf_Depth, cmock_line, "Function 'pb_read' called with unexpected value for argument 'buf'."); }
  }
  if (!cmock_call_instance->IgnoreArg_count)
  {
    UNITY_TEST_ASSERT_EQUAL_MEMORY((void*)(&cmock_call_instance->Expected_count), (void*)(&count), sizeof(size_t), cmock_line, "Function 'pb_read' called with unexpected value for argument 'count'.");
  }
  if (cmock_call_instance->ReturnThruPtr_stream_Used)
  {
    memcpy((void*)stream, (void*)cmock_call_instance->ReturnThruPtr_stream_Val,
      cmock_call_instance->ReturnThruPtr_stream_Size);
  }
  if (cmock_call_instance->ReturnThruPtr_buf_Used)
  {
    memcpy((void*)buf, (void*)cmock_call_instance->ReturnThruPtr_buf_Val,
      cmock_call_instance->ReturnThruPtr_buf_Size);
  }
  return cmock_call_instance->ReturnVal;
}

void CMockExpectParameters_pb_read(CMOCK_pb_read_CALL_INSTANCE* cmock_call_instance, pb_istream_t* stream, int stream_Depth, pb_byte_t* buf, int buf_Depth, size_t count)
{
  cmock_call_instance->Expected_stream = stream;
  cmock_call_instance->Expected_stream_Depth = stream_Depth;
  cmock_call_instance->IgnoreArg_stream = 0;
  cmock_call_instance->ReturnThruPtr_stream_Used = 0;
  cmock_call_instance->Expected_buf = buf;
  cmock_call_instance->Expected_buf_Depth = buf_Depth;
  cmock_call_instance->IgnoreArg_buf = 0;
  cmock_call_instance->ReturnThruPtr_buf_Used = 0;
  memcpy(&cmock_call_instance->Expected_count, &count, sizeof(size_t));
  cmock_call_instance->IgnoreArg_count = 0;
}

void pb_read_CMockIgnoreAndReturn(UNITY_LINE_TYPE cmock_line, bool cmock_to_return)
{
  CMOCK_MEM_INDEX_TYPE cmock_guts_index = CMock_Guts_MemNew(sizeof(CMOCK_pb_read_CALL_INSTANCE));
  CMOCK_pb_read_CALL_INSTANCE* cmock_call_instance = (CMOCK_pb_read_CALL_INSTANCE*)CMock_Guts_GetAddressFor(cmock_guts_index);
  UNITY_TEST_ASSERT_NOT_NULL(cmock_call_instance, cmock_line, "CMock has run out of memory. Please allocate more.");
  Mock.pb_read_CallInstance = CMock_Guts_MemChain(Mock.pb_read_CallInstance, cmock_guts_index);
  cmock_call_instance->LineNumber = cmock_line;
  cmock_call_instance->ReturnVal = cmock_to_return;
  Mock.pb_read_IgnoreBool = (int)1;
}

void pb_read_CMockExpectAndReturn(UNITY_LINE_TYPE cmock_line, pb_istream_t* stream, pb_byte_t* buf, size_t count, bool cmock_to_return)
{
  CMOCK_MEM_INDEX_TYPE cmock_guts_index = CMock_Guts_MemNew(sizeof(CMOCK_pb_read_CALL_INSTANCE));
  CMOCK_pb_read_CALL_INSTANCE* cmock_call_instance = (CMOCK_pb_read_CALL_INSTANCE*)CMock_Guts_GetAddressFor(cmock_guts_index);
  UNITY_TEST_ASSERT_NOT_NULL(cmock_call_instance, cmock_line, "CMock has run out of memory. Please allocate more.");
  Mock.pb_read_CallInstance = CMock_Guts_MemChain(Mock.pb_read_CallInstance, cmock_guts_index);
  cmock_call_instance->LineNumber = cmock_line;
  cmock_call_instance->CallOrder = ++GlobalExpectCount;
  CMockExpectParameters_pb_read(cmock_call_instance, stream, 0, buf, 0, count);
  cmock_call_instance->ReturnVal = cmock_to_return;
}

void pb_read_StubWithCallback(CMOCK_pb_read_CALLBACK Callback)
{
  Mock.pb_read_CallbackFunctionPointer = Callback;
}

void pb_read_CMockExpectWithArrayAndReturn(UNITY_LINE_TYPE cmock_line, pb_istream_t* stream, int stream_Depth, pb_byte_t* buf, int buf_Depth, size_t count, bool cmock_to_return)
{
  CMOCK_MEM_INDEX_TYPE cmock_guts_index = CMock_Guts_MemNew(sizeof(CMOCK_pb_read_CALL_INSTANCE));
  CMOCK_pb_read_CALL_INSTANCE* cmock_call_instance = (CMOCK_pb_read_CALL_INSTANCE*)CMock_Guts_GetAddressFor(cmock_guts_index);
  UNITY_TEST_ASSERT_NOT_NULL(cmock_call_instance, cmock_line, "CMock has run out of memory. Please allocate more.");
  Mock.pb_read_CallInstance = CMock_Guts_MemChain(Mock.pb_read_CallInstance, cmock_guts_index);
  cmock_call_instance->LineNumber = cmock_line;
  cmock_call_instance->CallOrder = ++GlobalExpectCount;
  CMockExpectParameters_pb_read(cmock_call_instance, stream, stream_Depth, buf, buf_Depth, count);
  cmock_call_instance->ReturnVal = cmock_to_return;
}

void pb_read_CMockReturnMemThruPtr_stream(UNITY_LINE_TYPE cmock_line, pb_istream_t* stream, int cmock_size)
{
  CMOCK_pb_read_CALL_INSTANCE* cmock_call_instance = cmock_call_instance = (CMOCK_pb_read_CALL_INSTANCE*)CMock_Guts_GetAddressFor(CMock_Guts_MemEndOfChain(Mock.pb_read_CallInstance));
  UNITY_TEST_ASSERT_NOT_NULL(cmock_call_instance, cmock_line, "stream ReturnThruPtr called before Expect on 'pb_read'.");
  cmock_call_instance->ReturnThruPtr_stream_Used = 1;
  cmock_call_instance->ReturnThruPtr_stream_Val = stream;
  cmock_call_instance->ReturnThruPtr_stream_Size = cmock_size;
}

void pb_read_CMockReturnMemThruPtr_buf(UNITY_LINE_TYPE cmock_line, pb_byte_t* buf, int cmock_size)
{
  CMOCK_pb_read_CALL_INSTANCE* cmock_call_instance = cmock_call_instance = (CMOCK_pb_read_CALL_INSTANCE*)CMock_Guts_GetAddressFor(CMock_Guts_MemEndOfChain(Mock.pb_read_CallInstance));
  UNITY_TEST_ASSERT_NOT_NULL(cmock_call_instance, cmock_line, "buf ReturnThruPtr called before Expect on 'pb_read'.");
  cmock_call_instance->ReturnThruPtr_buf_Used = 1;
  cmock_call_instance->ReturnThruPtr_buf_Val = buf;
  cmock_call_instance->ReturnThruPtr_buf_Size = cmock_size;
}

void pb_read_CMockIgnoreArg_stream(UNITY_LINE_TYPE cmock_line)
{
  CMOCK_pb_read_CALL_INSTANCE* cmock_call_instance = cmock_call_instance = (CMOCK_pb_read_CALL_INSTANCE*)CMock_Guts_GetAddressFor(CMock_Guts_MemEndOfChain(Mock.pb_read_CallInstance));
  UNITY_TEST_ASSERT_NOT_NULL(cmock_call_instance, cmock_line, "stream IgnoreArg called before Expect on 'pb_read'.");
  cmock_call_instance->IgnoreArg_stream = 1;
}

void pb_read_CMockIgnoreArg_buf(UNITY_LINE_TYPE cmock_line)
{
  CMOCK_pb_read_CALL_INSTANCE* cmock_call_instance = cmock_call_instance = (CMOCK_pb_read_CALL_INSTANCE*)CMock_Guts_GetAddressFor(CMock_Guts_MemEndOfChain(Mock.pb_read_CallInstance));
  UNITY_TEST_ASSERT_NOT_NULL(cmock_call_instance, cmock_line, "buf IgnoreArg called before Expect on 'pb_read'.");
  cmock_call_instance->IgnoreArg_buf = 1;
}

void pb_read_CMockIgnoreArg_count(UNITY_LINE_TYPE cmock_line)
{
  CMOCK_pb_read_CALL_INSTANCE* cmock_call_instance = cmock_call_instance = (CMOCK_pb_read_CALL_INSTANCE*)CMock_Guts_GetAddressFor(CMock_Guts_MemEndOfChain(Mock.pb_read_CallInstance));
  UNITY_TEST_ASSERT_NOT_NULL(cmock_call_instance, cmock_line, "count IgnoreArg called before Expect on 'pb_read'.");
  cmock_call_instance->IgnoreArg_count = 1;
}

bool pb_decode_tag(pb_istream_t* stream, pb_wire_type_t* wire_type, uint32_t* tag, bool* eof)
{
  UNITY_LINE_TYPE cmock_line = TEST_LINE_NUM;
  CMOCK_pb_decode_tag_CALL_INSTANCE* cmock_call_instance = (CMOCK_pb_decode_tag_CALL_INSTANCE*)CMock_Guts_GetAddressFor(Mock.pb_decode_tag_CallInstance);
  Mock.pb_decode_tag_CallInstance = CMock_Guts_MemNext(Mock.pb_decode_tag_CallInstance);
  if (Mock.pb_decode_tag_IgnoreBool)
  {
    if (cmock_call_instance == NULL)
      return Mock.pb_decode_tag_FinalReturn;
    Mock.pb_decode_tag_FinalReturn = cmock_call_instance->ReturnVal;
    return cmock_call_instance->ReturnVal;
  }
  if (Mock.pb_decode_tag_CallbackFunctionPointer != NULL)
  {
    return Mock.pb_decode_tag_CallbackFunctionPointer(stream, wire_type, tag, eof, Mock.pb_decode_tag_CallbackCalls++);
  }
  UNITY_TEST_ASSERT_NOT_NULL(cmock_call_instance, cmock_line, "Function 'pb_decode_tag' called more times than expected.");
  cmock_line = cmock_call_instance->LineNumber;
  if (cmock_call_instance->CallOrder > ++GlobalVerifyOrder)
    UNITY_TEST_FAIL(cmock_line, "Function 'pb_decode_tag' called earlier than expected.");
  if (cmock_call_instance->CallOrder < GlobalVerifyOrder)
    UNITY_TEST_FAIL(cmock_line, "Function 'pb_decode_tag' called later than expected.");
  if (!cmock_call_instance->IgnoreArg_stream)
  {
    if (cmock_call_instance->Expected_stream == NULL)
      { UNITY_TEST_ASSERT_NULL(stream, cmock_line, "Expected NULL. Function 'pb_decode_tag' called with unexpected value for argument 'stream'."); }
    else if (cmock_call_instance->Expected_stream_Depth == 0)
      { UNITY_TEST_ASSERT_EQUAL_PTR(cmock_call_instance->Expected_stream, stream, cmock_line, "Function 'pb_decode_tag' called with unexpected value for argument 'stream'."); }
    else
      { UNITY_TEST_ASSERT_EQUAL_MEMORY_ARRAY((void*)(cmock_call_instance->Expected_stream), (void*)(stream), sizeof(pb_istream_t), cmock_call_instance->Expected_stream_Depth, cmock_line, "Function 'pb_decode_tag' called with unexpected value for argument 'stream'."); }
  }
  if (!cmock_call_instance->IgnoreArg_wire_type)
  {
    if (cmock_call_instance->Expected_wire_type == NULL)
      { UNITY_TEST_ASSERT_NULL(wire_type, cmock_line, "Expected NULL. Function 'pb_decode_tag' called with unexpected value for argument 'wire_type'."); }
    else if (cmock_call_instance->Expected_wire_type_Depth == 0)
      { UNITY_TEST_ASSERT_EQUAL_PTR(cmock_call_instance->Expected_wire_type, wire_type, cmock_line, "Function 'pb_decode_tag' called with unexpected value for argument 'wire_type'."); }
    else
      { UNITY_TEST_ASSERT_EQUAL_MEMORY_ARRAY((void*)(cmock_call_instance->Expected_wire_type), (void*)(wire_type), sizeof(pb_wire_type_t), cmock_call_instance->Expected_wire_type_Depth, cmock_line, "Function 'pb_decode_tag' called with unexpected value for argument 'wire_type'."); }
  }
  if (!cmock_call_instance->IgnoreArg_tag)
  {
    if (cmock_call_instance->Expected_tag == NULL)
      { UNITY_TEST_ASSERT_NULL(tag, cmock_line, "Expected NULL. Function 'pb_decode_tag' called with unexpected value for argument 'tag'."); }
    else if (cmock_call_instance->Expected_tag_Depth == 0)
      { UNITY_TEST_ASSERT_EQUAL_PTR(cmock_call_instance->Expected_tag, tag, cmock_line, "Function 'pb_decode_tag' called with unexpected value for argument 'tag'."); }
    else
      { UNITY_TEST_ASSERT_EQUAL_HEX32_ARRAY(cmock_call_instance->Expected_tag, tag, cmock_call_instance->Expected_tag_Depth, cmock_line, "Function 'pb_decode_tag' called with unexpected value for argument 'tag'."); }
  }
  if (!cmock_call_instance->IgnoreArg_eof)
  {
    if (cmock_call_instance->Expected_eof == NULL)
      { UNITY_TEST_ASSERT_NULL(eof, cmock_line, "Expected NULL. Function 'pb_decode_tag' called with unexpected value for argument 'eof'."); }
    else if (cmock_call_instance->Expected_eof_Depth == 0)
      { UNITY_TEST_ASSERT_EQUAL_PTR(cmock_call_instance->Expected_eof, eof, cmock_line, "Function 'pb_decode_tag' called with unexpected value for argument 'eof'."); }
    else
      { UNITY_TEST_ASSERT_EQUAL_INT_ARRAY(cmock_call_instance->Expected_eof, eof, cmock_call_instance->Expected_eof_Depth, cmock_line, "Function 'pb_decode_tag' called with unexpected value for argument 'eof'."); }
  }
  if (cmock_call_instance->ReturnThruPtr_stream_Used)
  {
    memcpy((void*)stream, (void*)cmock_call_instance->ReturnThruPtr_stream_Val,
      cmock_call_instance->ReturnThruPtr_stream_Size);
  }
  if (cmock_call_instance->ReturnThruPtr_wire_type_Used)
  {
    memcpy((void*)wire_type, (void*)cmock_call_instance->ReturnThruPtr_wire_type_Val,
      cmock_call_instance->ReturnThruPtr_wire_type_Size);
  }
  if (cmock_call_instance->ReturnThruPtr_tag_Used)
  {
    memcpy((void*)tag, (void*)cmock_call_instance->ReturnThruPtr_tag_Val,
      cmock_call_instance->ReturnThruPtr_tag_Size);
  }
  if (cmock_call_instance->ReturnThruPtr_eof_Used)
  {
    memcpy((void*)eof, (void*)cmock_call_instance->ReturnThruPtr_eof_Val,
      cmock_call_instance->ReturnThruPtr_eof_Size);
  }
  return cmock_call_instance->ReturnVal;
}

void CMockExpectParameters_pb_decode_tag(CMOCK_pb_decode_tag_CALL_INSTANCE* cmock_call_instance, pb_istream_t* stream, int stream_Depth, pb_wire_type_t* wire_type, int wire_type_Depth, uint32_t* tag, int tag_Depth, bool* eof, int eof_Depth)
{
  cmock_call_instance->Expected_stream = stream;
  cmock_call_instance->Expected_stream_Depth = stream_Depth;
  cmock_call_instance->IgnoreArg_stream = 0;
  cmock_call_instance->ReturnThruPtr_stream_Used = 0;
  cmock_call_instance->Expected_wire_type = wire_type;
  cmock_call_instance->Expected_wire_type_Depth = wire_type_Depth;
  cmock_call_instance->IgnoreArg_wire_type = 0;
  cmock_call_instance->ReturnThruPtr_wire_type_Used = 0;
  cmock_call_instance->Expected_tag = tag;
  cmock_call_instance->Expected_tag_Depth = tag_Depth;
  cmock_call_instance->IgnoreArg_tag = 0;
  cmock_call_instance->ReturnThruPtr_tag_Used = 0;
  cmock_call_instance->Expected_eof = eof;
  cmock_call_instance->Expected_eof_Depth = eof_Depth;
  cmock_call_instance->IgnoreArg_eof = 0;
  cmock_call_instance->ReturnThruPtr_eof_Used = 0;
}

void pb_decode_tag_CMockIgnoreAndReturn(UNITY_LINE_TYPE cmock_line, bool cmock_to_return)
{
  CMOCK_MEM_INDEX_TYPE cmock_guts_index = CMock_Guts_MemNew(sizeof(CMOCK_pb_decode_tag_CALL_INSTANCE));
  CMOCK_pb_decode_tag_CALL_INSTANCE* cmock_call_instance = (CMOCK_pb_decode_tag_CALL_INSTANCE*)CMock_Guts_GetAddressFor(cmock_guts_index);
  UNITY_TEST_ASSERT_NOT_NULL(cmock_call_instance, cmock_line, "CMock has run out of memory. Please allocate more.");
  Mock.pb_decode_tag_CallInstance = CMock_Guts_MemChain(Mock.pb_decode_tag_CallInstance, cmock_guts_index);
  cmock_call_instance->LineNumber = cmock_line;
  cmock_call_instance->ReturnVal = cmock_to_return;
  Mock.pb_decode_tag_IgnoreBool = (int)1;
}

void pb_decode_tag_CMockExpectAndReturn(UNITY_LINE_TYPE cmock_line, pb_istream_t* stream, pb_wire_type_t* wire_type, uint32_t* tag, bool* eof, bool cmock_to_return)
{
  CMOCK_MEM_INDEX_TYPE cmock_guts_index = CMock_Guts_MemNew(sizeof(CMOCK_pb_decode_tag_CALL_INSTANCE));
  CMOCK_pb_decode_tag_CALL_INSTANCE* cmock_call_instance = (CMOCK_pb_decode_tag_CALL_INSTANCE*)CMock_Guts_GetAddressFor(cmock_guts_index);
  UNITY_TEST_ASSERT_NOT_NULL(cmock_call_instance, cmock_line, "CMock has run out of memory. Please allocate more.");
  Mock.pb_decode_tag_CallInstance = CMock_Guts_MemChain(Mock.pb_decode_tag_CallInstance, cmock_guts_index);
  cmock_call_instance->LineNumber = cmock_line;
  cmock_call_instance->CallOrder = ++GlobalExpectCount;
  CMockExpectParameters_pb_decode_tag(cmock_call_instance, stream, 0, wire_type, 0, tag, 0, eof, 0);
  cmock_call_instance->ReturnVal = cmock_to_return;
}

void pb_decode_tag_StubWithCallback(CMOCK_pb_decode_tag_CALLBACK Callback)
{
  Mock.pb_decode_tag_CallbackFunctionPointer = Callback;
}

void pb_decode_tag_CMockExpectWithArrayAndReturn(UNITY_LINE_TYPE cmock_line, pb_istream_t* stream, int stream_Depth, pb_wire_type_t* wire_type, int wire_type_Depth, uint32_t* tag, int tag_Depth, bool* eof, int eof_Depth, bool cmock_to_return)
{
  CMOCK_MEM_INDEX_TYPE cmock_guts_index = CMock_Guts_MemNew(sizeof(CMOCK_pb_decode_tag_CALL_INSTANCE));
  CMOCK_pb_decode_tag_CALL_INSTANCE* cmock_call_instance = (CMOCK_pb_decode_tag_CALL_INSTANCE*)CMock_Guts_GetAddressFor(cmock_guts_index);
  UNITY_TEST_ASSERT_NOT_NULL(cmock_call_instance, cmock_line, "CMock has run out of memory. Please allocate more.");
  Mock.pb_decode_tag_CallInstance = CMock_Guts_MemChain(Mock.pb_decode_tag_CallInstance, cmock_guts_index);
  cmock_call_instance->LineNumber = cmock_line;
  cmock_call_instance->CallOrder = ++GlobalExpectCount;
  CMockExpectParameters_pb_decode_tag(cmock_call_instance, stream, stream_Depth, wire_type, wire_type_Depth, tag, tag_Depth, eof, eof_Depth);
  cmock_call_instance->ReturnVal = cmock_to_return;
}

void pb_decode_tag_CMockReturnMemThruPtr_stream(UNITY_LINE_TYPE cmock_line, pb_istream_t* stream, int cmock_size)
{
  CMOCK_pb_decode_tag_CALL_INSTANCE* cmock_call_instance = cmock_call_instance = (CMOCK_pb_decode_tag_CALL_INSTANCE*)CMock_Guts_GetAddressFor(CMock_Guts_MemEndOfChain(Mock.pb_decode_tag_CallInstance));
  UNITY_TEST_ASSERT_NOT_NULL(cmock_call_instance, cmock_line, "stream ReturnThruPtr called before Expect on 'pb_decode_tag'.");
  cmock_call_instance->ReturnThruPtr_stream_Used = 1;
  cmock_call_instance->ReturnThruPtr_stream_Val = stream;
  cmock_call_instance->ReturnThruPtr_stream_Size = cmock_size;
}

void pb_decode_tag_CMockReturnMemThruPtr_wire_type(UNITY_LINE_TYPE cmock_line, pb_wire_type_t* wire_type, int cmock_size)
{
  CMOCK_pb_decode_tag_CALL_INSTANCE* cmock_call_instance = cmock_call_instance = (CMOCK_pb_decode_tag_CALL_INSTANCE*)CMock_Guts_GetAddressFor(CMock_Guts_MemEndOfChain(Mock.pb_decode_tag_CallInstance));
  UNITY_TEST_ASSERT_NOT_NULL(cmock_call_instance, cmock_line, "wire_type ReturnThruPtr called before Expect on 'pb_decode_tag'.");
  cmock_call_instance->ReturnThruPtr_wire_type_Used = 1;
  cmock_call_instance->ReturnThruPtr_wire_type_Val = wire_type;
  cmock_call_instance->ReturnThruPtr_wire_type_Size = cmock_size;
}

void pb_decode_tag_CMockReturnMemThruPtr_tag(UNITY_LINE_TYPE cmock_line, uint32_t* tag, int cmock_size)
{
  CMOCK_pb_decode_tag_CALL_INSTANCE* cmock_call_instance = cmock_call_instance = (CMOCK_pb_decode_tag_CALL_INSTANCE*)CMock_Guts_GetAddressFor(CMock_Guts_MemEndOfChain(Mock.pb_decode_tag_CallInstance));
  UNITY_TEST_ASSERT_NOT_NULL(cmock_call_instance, cmock_line, "tag ReturnThruPtr called before Expect on 'pb_decode_tag'.");
  cmock_call_instance->ReturnThruPtr_tag_Used = 1;
  cmock_call_instance->ReturnThruPtr_tag_Val = tag;
  cmock_call_instance->ReturnThruPtr_tag_Size = cmock_size;
}

void pb_decode_tag_CMockReturnMemThruPtr_eof(UNITY_LINE_TYPE cmock_line, bool* eof, int cmock_size)
{
  CMOCK_pb_decode_tag_CALL_INSTANCE* cmock_call_instance = cmock_call_instance = (CMOCK_pb_decode_tag_CALL_INSTANCE*)CMock_Guts_GetAddressFor(CMock_Guts_MemEndOfChain(Mock.pb_decode_tag_CallInstance));
  UNITY_TEST_ASSERT_NOT_NULL(cmock_call_instance, cmock_line, "eof ReturnThruPtr called before Expect on 'pb_decode_tag'.");
  cmock_call_instance->ReturnThruPtr_eof_Used = 1;
  cmock_call_instance->ReturnThruPtr_eof_Val = eof;
  cmock_call_instance->ReturnThruPtr_eof_Size = cmock_size;
}

void pb_decode_tag_CMockIgnoreArg_stream(UNITY_LINE_TYPE cmock_line)
{
  CMOCK_pb_decode_tag_CALL_INSTANCE* cmock_call_instance = cmock_call_instance = (CMOCK_pb_decode_tag_CALL_INSTANCE*)CMock_Guts_GetAddressFor(CMock_Guts_MemEndOfChain(Mock.pb_decode_tag_CallInstance));
  UNITY_TEST_ASSERT_NOT_NULL(cmock_call_instance, cmock_line, "stream IgnoreArg called before Expect on 'pb_decode_tag'.");
  cmock_call_instance->IgnoreArg_stream = 1;
}

void pb_decode_tag_CMockIgnoreArg_wire_type(UNITY_LINE_TYPE cmock_line)
{
  CMOCK_pb_decode_tag_CALL_INSTANCE* cmock_call_instance = cmock_call_instance = (CMOCK_pb_decode_tag_CALL_INSTANCE*)CMock_Guts_GetAddressFor(CMock_Guts_MemEndOfChain(Mock.pb_decode_tag_CallInstance));
  UNITY_TEST_ASSERT_NOT_NULL(cmock_call_instance, cmock_line, "wire_type IgnoreArg called before Expect on 'pb_decode_tag'.");
  cmock_call_instance->IgnoreArg_wire_type = 1;
}

void pb_decode_tag_CMockIgnoreArg_tag(UNITY_LINE_TYPE cmock_line)
{
  CMOCK_pb_decode_tag_CALL_INSTANCE* cmock_call_instance = cmock_call_instance = (CMOCK_pb_decode_tag_CALL_INSTANCE*)CMock_Guts_GetAddressFor(CMock_Guts_MemEndOfChain(Mock.pb_decode_tag_CallInstance));
  UNITY_TEST_ASSERT_NOT_NULL(cmock_call_instance, cmock_line, "tag IgnoreArg called before Expect on 'pb_decode_tag'.");
  cmock_call_instance->IgnoreArg_tag = 1;
}

void pb_decode_tag_CMockIgnoreArg_eof(UNITY_LINE_TYPE cmock_line)
{
  CMOCK_pb_decode_tag_CALL_INSTANCE* cmock_call_instance = cmock_call_instance = (CMOCK_pb_decode_tag_CALL_INSTANCE*)CMock_Guts_GetAddressFor(CMock_Guts_MemEndOfChain(Mock.pb_decode_tag_CallInstance));
  UNITY_TEST_ASSERT_NOT_NULL(cmock_call_instance, cmock_line, "eof IgnoreArg called before Expect on 'pb_decode_tag'.");
  cmock_call_instance->IgnoreArg_eof = 1;
}

bool pb_skip_field(pb_istream_t* stream, pb_wire_type_t wire_type)
{
  UNITY_LINE_TYPE cmock_line = TEST_LINE_NUM;
  CMOCK_pb_skip_field_CALL_INSTANCE* cmock_call_instance = (CMOCK_pb_skip_field_CALL_INSTANCE*)CMock_Guts_GetAddressFor(Mock.pb_skip_field_CallInstance);
  Mock.pb_skip_field_CallInstance = CMock_Guts_MemNext(Mock.pb_skip_field_CallInstance);
  if (Mock.pb_skip_field_IgnoreBool)
  {
    if (cmock_call_instance == NULL)
      return Mock.pb_skip_field_FinalReturn;
    Mock.pb_skip_field_FinalReturn = cmock_call_instance->ReturnVal;
    return cmock_call_instance->ReturnVal;
  }
  if (Mock.pb_skip_field_CallbackFunctionPointer != NULL)
  {
    return Mock.pb_skip_field_CallbackFunctionPointer(stream, wire_type, Mock.pb_skip_field_CallbackCalls++);
  }
  UNITY_TEST_ASSERT_NOT_NULL(cmock_call_instance, cmock_line, "Function 'pb_skip_field' called more times than expected.");
  cmock_line = cmock_call_instance->LineNumber;
  if (cmock_call_instance->CallOrder > ++GlobalVerifyOrder)
    UNITY_TEST_FAIL(cmock_line, "Function 'pb_skip_field' called earlier than expected.");
  if (cmock_call_instance->CallOrder < GlobalVerifyOrder)
    UNITY_TEST_FAIL(cmock_line, "Function 'pb_skip_field' called later than expected.");
  if (!cmock_call_instance->IgnoreArg_stream)
  {
    if (cmock_call_instance->Expected_stream == NULL)
      { UNITY_TEST_ASSERT_NULL(stream, cmock_line, "Expected NULL. Function 'pb_skip_field' called with unexpected value for argument 'stream'."); }
    else if (cmock_call_instance->Expected_stream_Depth == 0)
      { UNITY_TEST_ASSERT_EQUAL_PTR(cmock_call_instance->Expected_stream, stream, cmock_line, "Function 'pb_skip_field' called with unexpected value for argument 'stream'."); }
    else
      { UNITY_TEST_ASSERT_EQUAL_MEMORY_ARRAY((void*)(cmock_call_instance->Expected_stream), (void*)(stream), sizeof(pb_istream_t), cmock_call_instance->Expected_stream_Depth, cmock_line, "Function 'pb_skip_field' called with unexpected value for argument 'stream'."); }
  }
  if (!cmock_call_instance->IgnoreArg_wire_type)
  {
    UNITY_TEST_ASSERT_EQUAL_MEMORY((void*)(&cmock_call_instance->Expected_wire_type), (void*)(&wire_type), sizeof(pb_wire_type_t), cmock_line, "Function 'pb_skip_field' called with unexpected value for argument 'wire_type'.");
  }
  if (cmock_call_instance->ReturnThruPtr_stream_Used)
  {
    memcpy((void*)stream, (void*)cmock_call_instance->ReturnThruPtr_stream_Val,
      cmock_call_instance->ReturnThruPtr_stream_Size);
  }
  return cmock_call_instance->ReturnVal;
}

void CMockExpectParameters_pb_skip_field(CMOCK_pb_skip_field_CALL_INSTANCE* cmock_call_instance, pb_istream_t* stream, int stream_Depth, pb_wire_type_t wire_type)
{
  cmock_call_instance->Expected_stream = stream;
  cmock_call_instance->Expected_stream_Depth = stream_Depth;
  cmock_call_instance->IgnoreArg_stream = 0;
  cmock_call_instance->ReturnThruPtr_stream_Used = 0;
  memcpy(&cmock_call_instance->Expected_wire_type, &wire_type, sizeof(pb_wire_type_t));
  cmock_call_instance->IgnoreArg_wire_type = 0;
}

void pb_skip_field_CMockIgnoreAndReturn(UNITY_LINE_TYPE cmock_line, bool cmock_to_return)
{
  CMOCK_MEM_INDEX_TYPE cmock_guts_index = CMock_Guts_MemNew(sizeof(CMOCK_pb_skip_field_CALL_INSTANCE));
  CMOCK_pb_skip_field_CALL_INSTANCE* cmock_call_instance = (CMOCK_pb_skip_field_CALL_INSTANCE*)CMock_Guts_GetAddressFor(cmock_guts_index);
  UNITY_TEST_ASSERT_NOT_NULL(cmock_call_instance, cmock_line, "CMock has run out of memory. Please allocate more.");
  Mock.pb_skip_field_CallInstance = CMock_Guts_MemChain(Mock.pb_skip_field_CallInstance, cmock_guts_index);
  cmock_call_instance->LineNumber = cmock_line;
  cmock_call_instance->ReturnVal = cmock_to_return;
  Mock.pb_skip_field_IgnoreBool = (int)1;
}

void pb_skip_field_CMockExpectAndReturn(UNITY_LINE_TYPE cmock_line, pb_istream_t* stream, pb_wire_type_t wire_type, bool cmock_to_return)
{
  CMOCK_MEM_INDEX_TYPE cmock_guts_index = CMock_Guts_MemNew(sizeof(CMOCK_pb_skip_field_CALL_INSTANCE));
  CMOCK_pb_skip_field_CALL_INSTANCE* cmock_call_instance = (CMOCK_pb_skip_field_CALL_INSTANCE*)CMock_Guts_GetAddressFor(cmock_guts_index);
  UNITY_TEST_ASSERT_NOT_NULL(cmock_call_instance, cmock_line, "CMock has run out of memory. Please allocate more.");
  Mock.pb_skip_field_CallInstance = CMock_Guts_MemChain(Mock.pb_skip_field_CallInstance, cmock_guts_index);
  cmock_call_instance->LineNumber = cmock_line;
  cmock_call_instance->CallOrder = ++GlobalExpectCount;
  CMockExpectParameters_pb_skip_field(cmock_call_instance, stream, 0, wire_type);
  cmock_call_instance->ReturnVal = cmock_to_return;
}

void pb_skip_field_StubWithCallback(CMOCK_pb_skip_field_CALLBACK Callback)
{
  Mock.pb_skip_field_CallbackFunctionPointer = Callback;
}

void pb_skip_field_CMockExpectWithArrayAndReturn(UNITY_LINE_TYPE cmock_line, pb_istream_t* stream, int stream_Depth, pb_wire_type_t wire_type, bool cmock_to_return)
{
  CMOCK_MEM_INDEX_TYPE cmock_guts_index = CMock_Guts_MemNew(sizeof(CMOCK_pb_skip_field_CALL_INSTANCE));
  CMOCK_pb_skip_field_CALL_INSTANCE* cmock_call_instance = (CMOCK_pb_skip_field_CALL_INSTANCE*)CMock_Guts_GetAddressFor(cmock_guts_index);
  UNITY_TEST_ASSERT_NOT_NULL(cmock_call_instance, cmock_line, "CMock has run out of memory. Please allocate more.");
  Mock.pb_skip_field_CallInstance = CMock_Guts_MemChain(Mock.pb_skip_field_CallInstance, cmock_guts_index);
  cmock_call_instance->LineNumber = cmock_line;
  cmock_call_instance->CallOrder = ++GlobalExpectCount;
  CMockExpectParameters_pb_skip_field(cmock_call_instance, stream, stream_Depth, wire_type);
  cmock_call_instance->ReturnVal = cmock_to_return;
}

void pb_skip_field_CMockReturnMemThruPtr_stream(UNITY_LINE_TYPE cmock_line, pb_istream_t* stream, int cmock_size)
{
  CMOCK_pb_skip_field_CALL_INSTANCE* cmock_call_instance = cmock_call_instance = (CMOCK_pb_skip_field_CALL_INSTANCE*)CMock_Guts_GetAddressFor(CMock_Guts_MemEndOfChain(Mock.pb_skip_field_CallInstance));
  UNITY_TEST_ASSERT_NOT_NULL(cmock_call_instance, cmock_line, "stream ReturnThruPtr called before Expect on 'pb_skip_field'.");
  cmock_call_instance->ReturnThruPtr_stream_Used = 1;
  cmock_call_instance->ReturnThruPtr_stream_Val = stream;
  cmock_call_instance->ReturnThruPtr_stream_Size = cmock_size;
}

void pb_skip_field_CMockIgnoreArg_stream(UNITY_LINE_TYPE cmock_line)
{
  CMOCK_pb_skip_field_CALL_INSTANCE* cmock_call_instance = cmock_call_instance = (CMOCK_pb_skip_field_CALL_INSTANCE*)CMock_Guts_GetAddressFor(CMock_Guts_MemEndOfChain(Mock.pb_skip_field_CallInstance));
  UNITY_TEST_ASSERT_NOT_NULL(cmock_call_instance, cmock_line, "stream IgnoreArg called before Expect on 'pb_skip_field'.");
  cmock_call_instance->IgnoreArg_stream = 1;
}

void pb_skip_field_CMockIgnoreArg_wire_type(UNITY_LINE_TYPE cmock_line)
{
  CMOCK_pb_skip_field_CALL_INSTANCE* cmock_call_instance = cmock_call_instance = (CMOCK_pb_skip_field_CALL_INSTANCE*)CMock_Guts_GetAddressFor(CMock_Guts_MemEndOfChain(Mock.pb_skip_field_CallInstance));
  UNITY_TEST_ASSERT_NOT_NULL(cmock_call_instance, cmock_line, "wire_type IgnoreArg called before Expect on 'pb_skip_field'.");
  cmock_call_instance->IgnoreArg_wire_type = 1;
}

bool pb_decode_varint(pb_istream_t* stream, uint64_t* dest)
{
  UNITY_LINE_TYPE cmock_line = TEST_LINE_NUM;
  CMOCK_pb_decode_varint_CALL_INSTANCE* cmock_call_instance = (CMOCK_pb_decode_varint_CALL_INSTANCE*)CMock_Guts_GetAddressFor(Mock.pb_decode_varint_CallInstance);
  Mock.pb_decode_varint_CallInstance = CMock_Guts_MemNext(Mock.pb_decode_varint_CallInstance);
  if (Mock.pb_decode_varint_IgnoreBool)
  {
    if (cmock_call_instance == NULL)
      return Mock.pb_decode_varint_FinalReturn;
    Mock.pb_decode_varint_FinalReturn = cmock_call_instance->ReturnVal;
    return cmock_call_instance->ReturnVal;
  }
  if (Mock.pb_decode_varint_CallbackFunctionPointer != NULL)
  {
    return Mock.pb_decode_varint_CallbackFunctionPointer(stream, dest, Mock.pb_decode_varint_CallbackCalls++);
  }
  UNITY_TEST_ASSERT_NOT_NULL(cmock_call_instance, cmock_line, "Function 'pb_decode_varint' called more times than expected.");
  cmock_line = cmock_call_instance->LineNumber;
  if (cmock_call_instance->CallOrder > ++GlobalVerifyOrder)
    UNITY_TEST_FAIL(cmock_line, "Function 'pb_decode_varint' called earlier than expected.");
  if (cmock_call_instance->CallOrder < GlobalVerifyOrder)
    UNITY_TEST_FAIL(cmock_line, "Function 'pb_decode_varint' called later than expected.");
  if (!cmock_call_instance->IgnoreArg_stream)
  {
    if (cmock_call_instance->Expected_stream == NULL)
      { UNITY_TEST_ASSERT_NULL(stream, cmock_line, "Expected NULL. Function 'pb_decode_varint' called with unexpected value for argument 'stream'."); }
    else if (cmock_call_instance->Expected_stream_Depth == 0)
      { UNITY_TEST_ASSERT_EQUAL_PTR(cmock_call_instance->Expected_stream, stream, cmock_line, "Function 'pb_decode_varint' called with unexpected value for argument 'stream'."); }
    else
      { UNITY_TEST_ASSERT_EQUAL_MEMORY_ARRAY((void*)(cmock_call_instance->Expected_stream), (void*)(stream), sizeof(pb_istream_t), cmock_call_instance->Expected_stream_Depth, cmock_line, "Function 'pb_decode_varint' called with unexpected value for argument 'stream'."); }
  }
  if (!cmock_call_instance->IgnoreArg_dest)
  {
    if (cmock_call_instance->Expected_dest == NULL)
      { UNITY_TEST_ASSERT_NULL(dest, cmock_line, "Expected NULL. Function 'pb_decode_varint' called with unexpected value for argument 'dest'."); }
    else if (cmock_call_instance->Expected_dest_Depth == 0)
      { UNITY_TEST_ASSERT_EQUAL_PTR(cmock_call_instance->Expected_dest, dest, cmock_line, "Function 'pb_decode_varint' called with unexpected value for argument 'dest'."); }
    else
      { UNITY_TEST_ASSERT_EQUAL_MEMORY_ARRAY((void*)(cmock_call_instance->Expected_dest), (void*)(dest), sizeof(uint64_t), cmock_call_instance->Expected_dest_Depth, cmock_line, "Function 'pb_decode_varint' called with unexpected value for argument 'dest'."); }
  }
  if (cmock_call_instance->ReturnThruPtr_stream_Used)
  {
    memcpy((void*)stream, (void*)cmock_call_instance->ReturnThruPtr_stream_Val,
      cmock_call_instance->ReturnThruPtr_stream_Size);
  }
  if (cmock_call_instance->ReturnThruPtr_dest_Used)
  {
    memcpy((void*)dest, (void*)cmock_call_instance->ReturnThruPtr_dest_Val,
      cmock_call_instance->ReturnThruPtr_dest_Size);
  }
  return cmock_call_instance->ReturnVal;
}

void CMockExpectParameters_pb_decode_varint(CMOCK_pb_decode_varint_CALL_INSTANCE* cmock_call_instance, pb_istream_t* stream, int stream_Depth, uint64_t* dest, int dest_Depth)
{
  cmock_call_instance->Expected_stream = stream;
  cmock_call_instance->Expected_stream_Depth = stream_Depth;
  cmock_call_instance->IgnoreArg_stream = 0;
  cmock_call_instance->ReturnThruPtr_stream_Used = 0;
  cmock_call_instance->Expected_dest = dest;
  cmock_call_instance->Expected_dest_Depth = dest_Depth;
  cmock_call_instance->IgnoreArg_dest = 0;
  cmock_call_instance->ReturnThruPtr_dest_Used = 0;
}

void pb_decode_varint_CMockIgnoreAndReturn(UNITY_LINE_TYPE cmock_line, bool cmock_to_return)
{
  CMOCK_MEM_INDEX_TYPE cmock_guts_index = CMock_Guts_MemNew(sizeof(CMOCK_pb_decode_varint_CALL_INSTANCE));
  CMOCK_pb_decode_varint_CALL_INSTANCE* cmock_call_instance = (CMOCK_pb_decode_varint_CALL_INSTANCE*)CMock_Guts_GetAddressFor(cmock_guts_index);
  UNITY_TEST_ASSERT_NOT_NULL(cmock_call_instance, cmock_line, "CMock has run out of memory. Please allocate more.");
  Mock.pb_decode_varint_CallInstance = CMock_Guts_MemChain(Mock.pb_decode_varint_CallInstance, cmock_guts_index);
  cmock_call_instance->LineNumber = cmock_line;
  cmock_call_instance->ReturnVal = cmock_to_return;
  Mock.pb_decode_varint_IgnoreBool = (int)1;
}

void pb_decode_varint_CMockExpectAndReturn(UNITY_LINE_TYPE cmock_line, pb_istream_t* stream, uint64_t* dest, bool cmock_to_return)
{
  CMOCK_MEM_INDEX_TYPE cmock_guts_index = CMock_Guts_MemNew(sizeof(CMOCK_pb_decode_varint_CALL_INSTANCE));
  CMOCK_pb_decode_varint_CALL_INSTANCE* cmock_call_instance = (CMOCK_pb_decode_varint_CALL_INSTANCE*)CMock_Guts_GetAddressFor(cmock_guts_index);
  UNITY_TEST_ASSERT_NOT_NULL(cmock_call_instance, cmock_line, "CMock has run out of memory. Please allocate more.");
  Mock.pb_decode_varint_CallInstance = CMock_Guts_MemChain(Mock.pb_decode_varint_CallInstance, cmock_guts_index);
  cmock_call_instance->LineNumber = cmock_line;
  cmock_call_instance->CallOrder = ++GlobalExpectCount;
  CMockExpectParameters_pb_decode_varint(cmock_call_instance, stream, 0, dest, 0);
  cmock_call_instance->ReturnVal = cmock_to_return;
}

void pb_decode_varint_StubWithCallback(CMOCK_pb_decode_varint_CALLBACK Callback)
{
  Mock.pb_decode_varint_CallbackFunctionPointer = Callback;
}

void pb_decode_varint_CMockExpectWithArrayAndReturn(UNITY_LINE_TYPE cmock_line, pb_istream_t* stream, int stream_Depth, uint64_t* dest, int dest_Depth, bool cmock_to_return)
{
  CMOCK_MEM_INDEX_TYPE cmock_guts_index = CMock_Guts_MemNew(sizeof(CMOCK_pb_decode_varint_CALL_INSTANCE));
  CMOCK_pb_decode_varint_CALL_INSTANCE* cmock_call_instance = (CMOCK_pb_decode_varint_CALL_INSTANCE*)CMock_Guts_GetAddressFor(cmock_guts_index);
  UNITY_TEST_ASSERT_NOT_NULL(cmock_call_instance, cmock_line, "CMock has run out of memory. Please allocate more.");
  Mock.pb_decode_varint_CallInstance = CMock_Guts_MemChain(Mock.pb_decode_varint_CallInstance, cmock_guts_index);
  cmock_call_instance->LineNumber = cmock_line;
  cmock_call_instance->CallOrder = ++GlobalExpectCount;
  CMockExpectParameters_pb_decode_varint(cmock_call_instance, stream, stream_Depth, dest, dest_Depth);
  cmock_call_instance->ReturnVal = cmock_to_return;
}

void pb_decode_varint_CMockReturnMemThruPtr_stream(UNITY_LINE_TYPE cmock_line, pb_istream_t* stream, int cmock_size)
{
  CMOCK_pb_decode_varint_CALL_INSTANCE* cmock_call_instance = cmock_call_instance = (CMOCK_pb_decode_varint_CALL_INSTANCE*)CMock_Guts_GetAddressFor(CMock_Guts_MemEndOfChain(Mock.pb_decode_varint_CallInstance));
  UNITY_TEST_ASSERT_NOT_NULL(cmock_call_instance, cmock_line, "stream ReturnThruPtr called before Expect on 'pb_decode_varint'.");
  cmock_call_instance->ReturnThruPtr_stream_Used = 1;
  cmock_call_instance->ReturnThruPtr_stream_Val = stream;
  cmock_call_instance->ReturnThruPtr_stream_Size = cmock_size;
}

void pb_decode_varint_CMockReturnMemThruPtr_dest(UNITY_LINE_TYPE cmock_line, uint64_t* dest, int cmock_size)
{
  CMOCK_pb_decode_varint_CALL_INSTANCE* cmock_call_instance = cmock_call_instance = (CMOCK_pb_decode_varint_CALL_INSTANCE*)CMock_Guts_GetAddressFor(CMock_Guts_MemEndOfChain(Mock.pb_decode_varint_CallInstance));
  UNITY_TEST_ASSERT_NOT_NULL(cmock_call_instance, cmock_line, "dest ReturnThruPtr called before Expect on 'pb_decode_varint'.");
  cmock_call_instance->ReturnThruPtr_dest_Used = 1;
  cmock_call_instance->ReturnThruPtr_dest_Val = dest;
  cmock_call_instance->ReturnThruPtr_dest_Size = cmock_size;
}

void pb_decode_varint_CMockIgnoreArg_stream(UNITY_LINE_TYPE cmock_line)
{
  CMOCK_pb_decode_varint_CALL_INSTANCE* cmock_call_instance = cmock_call_instance = (CMOCK_pb_decode_varint_CALL_INSTANCE*)CMock_Guts_GetAddressFor(CMock_Guts_MemEndOfChain(Mock.pb_decode_varint_CallInstance));
  UNITY_TEST_ASSERT_NOT_NULL(cmock_call_instance, cmock_line, "stream IgnoreArg called before Expect on 'pb_decode_varint'.");
  cmock_call_instance->IgnoreArg_stream = 1;
}

void pb_decode_varint_CMockIgnoreArg_dest(UNITY_LINE_TYPE cmock_line)
{
  CMOCK_pb_decode_varint_CALL_INSTANCE* cmock_call_instance = cmock_call_instance = (CMOCK_pb_decode_varint_CALL_INSTANCE*)CMock_Guts_GetAddressFor(CMock_Guts_MemEndOfChain(Mock.pb_decode_varint_CallInstance));
  UNITY_TEST_ASSERT_NOT_NULL(cmock_call_instance, cmock_line, "dest IgnoreArg called before Expect on 'pb_decode_varint'.");
  cmock_call_instance->IgnoreArg_dest = 1;
}

bool pb_decode_svarint(pb_istream_t* stream, int64_t* dest)
{
  UNITY_LINE_TYPE cmock_line = TEST_LINE_NUM;
  CMOCK_pb_decode_svarint_CALL_INSTANCE* cmock_call_instance = (CMOCK_pb_decode_svarint_CALL_INSTANCE*)CMock_Guts_GetAddressFor(Mock.pb_decode_svarint_CallInstance);
  Mock.pb_decode_svarint_CallInstance = CMock_Guts_MemNext(Mock.pb_decode_svarint_CallInstance);
  if (Mock.pb_decode_svarint_IgnoreBool)
  {
    if (cmock_call_instance == NULL)
      return Mock.pb_decode_svarint_FinalReturn;
    Mock.pb_decode_svarint_FinalReturn = cmock_call_instance->ReturnVal;
    return cmock_call_instance->ReturnVal;
  }
  if (Mock.pb_decode_svarint_CallbackFunctionPointer != NULL)
  {
    return Mock.pb_decode_svarint_CallbackFunctionPointer(stream, dest, Mock.pb_decode_svarint_CallbackCalls++);
  }
  UNITY_TEST_ASSERT_NOT_NULL(cmock_call_instance, cmock_line, "Function 'pb_decode_svarint' called more times than expected.");
  cmock_line = cmock_call_instance->LineNumber;
  if (cmock_call_instance->CallOrder > ++GlobalVerifyOrder)
    UNITY_TEST_FAIL(cmock_line, "Function 'pb_decode_svarint' called earlier than expected.");
  if (cmock_call_instance->CallOrder < GlobalVerifyOrder)
    UNITY_TEST_FAIL(cmock_line, "Function 'pb_decode_svarint' called later than expected.");
  if (!cmock_call_instance->IgnoreArg_stream)
  {
    if (cmock_call_instance->Expected_stream == NULL)
      { UNITY_TEST_ASSERT_NULL(stream, cmock_line, "Expected NULL. Function 'pb_decode_svarint' called with unexpected value for argument 'stream'."); }
    else if (cmock_call_instance->Expected_stream_Depth == 0)
      { UNITY_TEST_ASSERT_EQUAL_PTR(cmock_call_instance->Expected_stream, stream, cmock_line, "Function 'pb_decode_svarint' called with unexpected value for argument 'stream'."); }
    else
      { UNITY_TEST_ASSERT_EQUAL_MEMORY_ARRAY((void*)(cmock_call_instance->Expected_stream), (void*)(stream), sizeof(pb_istream_t), cmock_call_instance->Expected_stream_Depth, cmock_line, "Function 'pb_decode_svarint' called with unexpected value for argument 'stream'."); }
  }
  if (!cmock_call_instance->IgnoreArg_dest)
  {
    if (cmock_call_instance->Expected_dest == NULL)
      { UNITY_TEST_ASSERT_NULL(dest, cmock_line, "Expected NULL. Function 'pb_decode_svarint' called with unexpected value for argument 'dest'."); }
    else if (cmock_call_instance->Expected_dest_Depth == 0)
      { UNITY_TEST_ASSERT_EQUAL_PTR(cmock_call_instance->Expected_dest, dest, cmock_line, "Function 'pb_decode_svarint' called with unexpected value for argument 'dest'."); }
    else
      { UNITY_TEST_ASSERT_EQUAL_MEMORY_ARRAY((void*)(cmock_call_instance->Expected_dest), (void*)(dest), sizeof(int64_t), cmock_call_instance->Expected_dest_Depth, cmock_line, "Function 'pb_decode_svarint' called with unexpected value for argument 'dest'."); }
  }
  if (cmock_call_instance->ReturnThruPtr_stream_Used)
  {
    memcpy((void*)stream, (void*)cmock_call_instance->ReturnThruPtr_stream_Val,
      cmock_call_instance->ReturnThruPtr_stream_Size);
  }
  if (cmock_call_instance->ReturnThruPtr_dest_Used)
  {
    memcpy((void*)dest, (void*)cmock_call_instance->ReturnThruPtr_dest_Val,
      cmock_call_instance->ReturnThruPtr_dest_Size);
  }
  return cmock_call_instance->ReturnVal;
}

void CMockExpectParameters_pb_decode_svarint(CMOCK_pb_decode_svarint_CALL_INSTANCE* cmock_call_instance, pb_istream_t* stream, int stream_Depth, int64_t* dest, int dest_Depth)
{
  cmock_call_instance->Expected_stream = stream;
  cmock_call_instance->Expected_stream_Depth = stream_Depth;
  cmock_call_instance->IgnoreArg_stream = 0;
  cmock_call_instance->ReturnThruPtr_stream_Used = 0;
  cmock_call_instance->Expected_dest = dest;
  cmock_call_instance->Expected_dest_Depth = dest_Depth;
  cmock_call_instance->IgnoreArg_dest = 0;
  cmock_call_instance->ReturnThruPtr_dest_Used = 0;
}

void pb_decode_svarint_CMockIgnoreAndReturn(UNITY_LINE_TYPE cmock_line, bool cmock_to_return)
{
  CMOCK_MEM_INDEX_TYPE cmock_guts_index = CMock_Guts_MemNew(sizeof(CMOCK_pb_decode_svarint_CALL_INSTANCE));
  CMOCK_pb_decode_svarint_CALL_INSTANCE* cmock_call_instance = (CMOCK_pb_decode_svarint_CALL_INSTANCE*)CMock_Guts_GetAddressFor(cmock_guts_index);
  UNITY_TEST_ASSERT_NOT_NULL(cmock_call_instance, cmock_line, "CMock has run out of memory. Please allocate more.");
  Mock.pb_decode_svarint_CallInstance = CMock_Guts_MemChain(Mock.pb_decode_svarint_CallInstance, cmock_guts_index);
  cmock_call_instance->LineNumber = cmock_line;
  cmock_call_instance->ReturnVal = cmock_to_return;
  Mock.pb_decode_svarint_IgnoreBool = (int)1;
}

void pb_decode_svarint_CMockExpectAndReturn(UNITY_LINE_TYPE cmock_line, pb_istream_t* stream, int64_t* dest, bool cmock_to_return)
{
  CMOCK_MEM_INDEX_TYPE cmock_guts_index = CMock_Guts_MemNew(sizeof(CMOCK_pb_decode_svarint_CALL_INSTANCE));
  CMOCK_pb_decode_svarint_CALL_INSTANCE* cmock_call_instance = (CMOCK_pb_decode_svarint_CALL_INSTANCE*)CMock_Guts_GetAddressFor(cmock_guts_index);
  UNITY_TEST_ASSERT_NOT_NULL(cmock_call_instance, cmock_line, "CMock has run out of memory. Please allocate more.");
  Mock.pb_decode_svarint_CallInstance = CMock_Guts_MemChain(Mock.pb_decode_svarint_CallInstance, cmock_guts_index);
  cmock_call_instance->LineNumber = cmock_line;
  cmock_call_instance->CallOrder = ++GlobalExpectCount;
  CMockExpectParameters_pb_decode_svarint(cmock_call_instance, stream, 0, dest, 0);
  cmock_call_instance->ReturnVal = cmock_to_return;
}

void pb_decode_svarint_StubWithCallback(CMOCK_pb_decode_svarint_CALLBACK Callback)
{
  Mock.pb_decode_svarint_CallbackFunctionPointer = Callback;
}

void pb_decode_svarint_CMockExpectWithArrayAndReturn(UNITY_LINE_TYPE cmock_line, pb_istream_t* stream, int stream_Depth, int64_t* dest, int dest_Depth, bool cmock_to_return)
{
  CMOCK_MEM_INDEX_TYPE cmock_guts_index = CMock_Guts_MemNew(sizeof(CMOCK_pb_decode_svarint_CALL_INSTANCE));
  CMOCK_pb_decode_svarint_CALL_INSTANCE* cmock_call_instance = (CMOCK_pb_decode_svarint_CALL_INSTANCE*)CMock_Guts_GetAddressFor(cmock_guts_index);
  UNITY_TEST_ASSERT_NOT_NULL(cmock_call_instance, cmock_line, "CMock has run out of memory. Please allocate more.");
  Mock.pb_decode_svarint_CallInstance = CMock_Guts_MemChain(Mock.pb_decode_svarint_CallInstance, cmock_guts_index);
  cmock_call_instance->LineNumber = cmock_line;
  cmock_call_instance->CallOrder = ++GlobalExpectCount;
  CMockExpectParameters_pb_decode_svarint(cmock_call_instance, stream, stream_Depth, dest, dest_Depth);
  cmock_call_instance->ReturnVal = cmock_to_return;
}

void pb_decode_svarint_CMockReturnMemThruPtr_stream(UNITY_LINE_TYPE cmock_line, pb_istream_t* stream, int cmock_size)
{
  CMOCK_pb_decode_svarint_CALL_INSTANCE* cmock_call_instance = cmock_call_instance = (CMOCK_pb_decode_svarint_CALL_INSTANCE*)CMock_Guts_GetAddressFor(CMock_Guts_MemEndOfChain(Mock.pb_decode_svarint_CallInstance));
  UNITY_TEST_ASSERT_NOT_NULL(cmock_call_instance, cmock_line, "stream ReturnThruPtr called before Expect on 'pb_decode_svarint'.");
  cmock_call_instance->ReturnThruPtr_stream_Used = 1;
  cmock_call_instance->ReturnThruPtr_stream_Val = stream;
  cmock_call_instance->ReturnThruPtr_stream_Size = cmock_size;
}

void pb_decode_svarint_CMockReturnMemThruPtr_dest(UNITY_LINE_TYPE cmock_line, int64_t* dest, int cmock_size)
{
  CMOCK_pb_decode_svarint_CALL_INSTANCE* cmock_call_instance = cmock_call_instance = (CMOCK_pb_decode_svarint_CALL_INSTANCE*)CMock_Guts_GetAddressFor(CMock_Guts_MemEndOfChain(Mock.pb_decode_svarint_CallInstance));
  UNITY_TEST_ASSERT_NOT_NULL(cmock_call_instance, cmock_line, "dest ReturnThruPtr called before Expect on 'pb_decode_svarint'.");
  cmock_call_instance->ReturnThruPtr_dest_Used = 1;
  cmock_call_instance->ReturnThruPtr_dest_Val = dest;
  cmock_call_instance->ReturnThruPtr_dest_Size = cmock_size;
}

void pb_decode_svarint_CMockIgnoreArg_stream(UNITY_LINE_TYPE cmock_line)
{
  CMOCK_pb_decode_svarint_CALL_INSTANCE* cmock_call_instance = cmock_call_instance = (CMOCK_pb_decode_svarint_CALL_INSTANCE*)CMock_Guts_GetAddressFor(CMock_Guts_MemEndOfChain(Mock.pb_decode_svarint_CallInstance));
  UNITY_TEST_ASSERT_NOT_NULL(cmock_call_instance, cmock_line, "stream IgnoreArg called before Expect on 'pb_decode_svarint'.");
  cmock_call_instance->IgnoreArg_stream = 1;
}

void pb_decode_svarint_CMockIgnoreArg_dest(UNITY_LINE_TYPE cmock_line)
{
  CMOCK_pb_decode_svarint_CALL_INSTANCE* cmock_call_instance = cmock_call_instance = (CMOCK_pb_decode_svarint_CALL_INSTANCE*)CMock_Guts_GetAddressFor(CMock_Guts_MemEndOfChain(Mock.pb_decode_svarint_CallInstance));
  UNITY_TEST_ASSERT_NOT_NULL(cmock_call_instance, cmock_line, "dest IgnoreArg called before Expect on 'pb_decode_svarint'.");
  cmock_call_instance->IgnoreArg_dest = 1;
}

bool pb_decode_fixed32(pb_istream_t* stream, void* dest)
{
  UNITY_LINE_TYPE cmock_line = TEST_LINE_NUM;
  CMOCK_pb_decode_fixed32_CALL_INSTANCE* cmock_call_instance = (CMOCK_pb_decode_fixed32_CALL_INSTANCE*)CMock_Guts_GetAddressFor(Mock.pb_decode_fixed32_CallInstance);
  Mock.pb_decode_fixed32_CallInstance = CMock_Guts_MemNext(Mock.pb_decode_fixed32_CallInstance);
  if (Mock.pb_decode_fixed32_IgnoreBool)
  {
    if (cmock_call_instance == NULL)
      return Mock.pb_decode_fixed32_FinalReturn;
    Mock.pb_decode_fixed32_FinalReturn = cmock_call_instance->ReturnVal;
    return cmock_call_instance->ReturnVal;
  }
  if (Mock.pb_decode_fixed32_CallbackFunctionPointer != NULL)
  {
    return Mock.pb_decode_fixed32_CallbackFunctionPointer(stream, dest, Mock.pb_decode_fixed32_CallbackCalls++);
  }
  UNITY_TEST_ASSERT_NOT_NULL(cmock_call_instance, cmock_line, "Function 'pb_decode_fixed32' called more times than expected.");
  cmock_line = cmock_call_instance->LineNumber;
  if (cmock_call_instance->CallOrder > ++GlobalVerifyOrder)
    UNITY_TEST_FAIL(cmock_line, "Function 'pb_decode_fixed32' called earlier than expected.");
  if (cmock_call_instance->CallOrder < GlobalVerifyOrder)
    UNITY_TEST_FAIL(cmock_line, "Function 'pb_decode_fixed32' called later than expected.");
  if (!cmock_call_instance->IgnoreArg_stream)
  {
    if (cmock_call_instance->Expected_stream == NULL)
      { UNITY_TEST_ASSERT_NULL(stream, cmock_line, "Expected NULL. Function 'pb_decode_fixed32' called with unexpected value for argument 'stream'."); }
    else if (cmock_call_instance->Expected_stream_Depth == 0)
      { UNITY_TEST_ASSERT_EQUAL_PTR(cmock_call_instance->Expected_stream, stream, cmock_line, "Function 'pb_decode_fixed32' called with unexpected value for argument 'stream'."); }
    else
      { UNITY_TEST_ASSERT_EQUAL_MEMORY_ARRAY((void*)(cmock_call_instance->Expected_stream), (void*)(stream), sizeof(pb_istream_t), cmock_call_instance->Expected_stream_Depth, cmock_line, "Function 'pb_decode_fixed32' called with unexpected value for argument 'stream'."); }
  }
  if (!cmock_call_instance->IgnoreArg_dest)
  {
    UNITY_TEST_ASSERT_EQUAL_PTR(cmock_call_instance->Expected_dest, dest, cmock_line, "Function 'pb_decode_fixed32' called with unexpected value for argument 'dest'.");
  }
  if (cmock_call_instance->ReturnThruPtr_stream_Used)
  {
    memcpy((void*)stream, (void*)cmock_call_instance->ReturnThruPtr_stream_Val,
      cmock_call_instance->ReturnThruPtr_stream_Size);
  }
  if (cmock_call_instance->ReturnThruPtr_dest_Used)
  {
    memcpy((void*)dest, (void*)cmock_call_instance->ReturnThruPtr_dest_Val,
      cmock_call_instance->ReturnThruPtr_dest_Size);
  }
  return cmock_call_instance->ReturnVal;
}

void CMockExpectParameters_pb_decode_fixed32(CMOCK_pb_decode_fixed32_CALL_INSTANCE* cmock_call_instance, pb_istream_t* stream, int stream_Depth, void* dest, int dest_Depth)
{
  cmock_call_instance->Expected_stream = stream;
  cmock_call_instance->Expected_stream_Depth = stream_Depth;
  cmock_call_instance->IgnoreArg_stream = 0;
  cmock_call_instance->ReturnThruPtr_stream_Used = 0;
  cmock_call_instance->Expected_dest = dest;
  cmock_call_instance->Expected_dest_Depth = dest_Depth;
  cmock_call_instance->IgnoreArg_dest = 0;
  cmock_call_instance->ReturnThruPtr_dest_Used = 0;
}

void pb_decode_fixed32_CMockIgnoreAndReturn(UNITY_LINE_TYPE cmock_line, bool cmock_to_return)
{
  CMOCK_MEM_INDEX_TYPE cmock_guts_index = CMock_Guts_MemNew(sizeof(CMOCK_pb_decode_fixed32_CALL_INSTANCE));
  CMOCK_pb_decode_fixed32_CALL_INSTANCE* cmock_call_instance = (CMOCK_pb_decode_fixed32_CALL_INSTANCE*)CMock_Guts_GetAddressFor(cmock_guts_index);
  UNITY_TEST_ASSERT_NOT_NULL(cmock_call_instance, cmock_line, "CMock has run out of memory. Please allocate more.");
  Mock.pb_decode_fixed32_CallInstance = CMock_Guts_MemChain(Mock.pb_decode_fixed32_CallInstance, cmock_guts_index);
  cmock_call_instance->LineNumber = cmock_line;
  cmock_call_instance->ReturnVal = cmock_to_return;
  Mock.pb_decode_fixed32_IgnoreBool = (int)1;
}

void pb_decode_fixed32_CMockExpectAndReturn(UNITY_LINE_TYPE cmock_line, pb_istream_t* stream, void* dest, bool cmock_to_return)
{
  CMOCK_MEM_INDEX_TYPE cmock_guts_index = CMock_Guts_MemNew(sizeof(CMOCK_pb_decode_fixed32_CALL_INSTANCE));
  CMOCK_pb_decode_fixed32_CALL_INSTANCE* cmock_call_instance = (CMOCK_pb_decode_fixed32_CALL_INSTANCE*)CMock_Guts_GetAddressFor(cmock_guts_index);
  UNITY_TEST_ASSERT_NOT_NULL(cmock_call_instance, cmock_line, "CMock has run out of memory. Please allocate more.");
  Mock.pb_decode_fixed32_CallInstance = CMock_Guts_MemChain(Mock.pb_decode_fixed32_CallInstance, cmock_guts_index);
  cmock_call_instance->LineNumber = cmock_line;
  cmock_call_instance->CallOrder = ++GlobalExpectCount;
  CMockExpectParameters_pb_decode_fixed32(cmock_call_instance, stream, 0, dest, 0);
  cmock_call_instance->ReturnVal = cmock_to_return;
}

void pb_decode_fixed32_StubWithCallback(CMOCK_pb_decode_fixed32_CALLBACK Callback)
{
  Mock.pb_decode_fixed32_CallbackFunctionPointer = Callback;
}

void pb_decode_fixed32_CMockExpectWithArrayAndReturn(UNITY_LINE_TYPE cmock_line, pb_istream_t* stream, int stream_Depth, void* dest, int dest_Depth, bool cmock_to_return)
{
  CMOCK_MEM_INDEX_TYPE cmock_guts_index = CMock_Guts_MemNew(sizeof(CMOCK_pb_decode_fixed32_CALL_INSTANCE));
  CMOCK_pb_decode_fixed32_CALL_INSTANCE* cmock_call_instance = (CMOCK_pb_decode_fixed32_CALL_INSTANCE*)CMock_Guts_GetAddressFor(cmock_guts_index);
  UNITY_TEST_ASSERT_NOT_NULL(cmock_call_instance, cmock_line, "CMock has run out of memory. Please allocate more.");
  Mock.pb_decode_fixed32_CallInstance = CMock_Guts_MemChain(Mock.pb_decode_fixed32_CallInstance, cmock_guts_index);
  cmock_call_instance->LineNumber = cmock_line;
  cmock_call_instance->CallOrder = ++GlobalExpectCount;
  CMockExpectParameters_pb_decode_fixed32(cmock_call_instance, stream, stream_Depth, dest, dest_Depth);
  cmock_call_instance->ReturnVal = cmock_to_return;
}

void pb_decode_fixed32_CMockReturnMemThruPtr_stream(UNITY_LINE_TYPE cmock_line, pb_istream_t* stream, int cmock_size)
{
  CMOCK_pb_decode_fixed32_CALL_INSTANCE* cmock_call_instance = cmock_call_instance = (CMOCK_pb_decode_fixed32_CALL_INSTANCE*)CMock_Guts_GetAddressFor(CMock_Guts_MemEndOfChain(Mock.pb_decode_fixed32_CallInstance));
  UNITY_TEST_ASSERT_NOT_NULL(cmock_call_instance, cmock_line, "stream ReturnThruPtr called before Expect on 'pb_decode_fixed32'.");
  cmock_call_instance->ReturnThruPtr_stream_Used = 1;
  cmock_call_instance->ReturnThruPtr_stream_Val = stream;
  cmock_call_instance->ReturnThruPtr_stream_Size = cmock_size;
}

void pb_decode_fixed32_CMockReturnMemThruPtr_dest(UNITY_LINE_TYPE cmock_line, void* dest, int cmock_size)
{
  CMOCK_pb_decode_fixed32_CALL_INSTANCE* cmock_call_instance = cmock_call_instance = (CMOCK_pb_decode_fixed32_CALL_INSTANCE*)CMock_Guts_GetAddressFor(CMock_Guts_MemEndOfChain(Mock.pb_decode_fixed32_CallInstance));
  UNITY_TEST_ASSERT_NOT_NULL(cmock_call_instance, cmock_line, "dest ReturnThruPtr called before Expect on 'pb_decode_fixed32'.");
  cmock_call_instance->ReturnThruPtr_dest_Used = 1;
  cmock_call_instance->ReturnThruPtr_dest_Val = dest;
  cmock_call_instance->ReturnThruPtr_dest_Size = cmock_size;
}

void pb_decode_fixed32_CMockIgnoreArg_stream(UNITY_LINE_TYPE cmock_line)
{
  CMOCK_pb_decode_fixed32_CALL_INSTANCE* cmock_call_instance = cmock_call_instance = (CMOCK_pb_decode_fixed32_CALL_INSTANCE*)CMock_Guts_GetAddressFor(CMock_Guts_MemEndOfChain(Mock.pb_decode_fixed32_CallInstance));
  UNITY_TEST_ASSERT_NOT_NULL(cmock_call_instance, cmock_line, "stream IgnoreArg called before Expect on 'pb_decode_fixed32'.");
  cmock_call_instance->IgnoreArg_stream = 1;
}

void pb_decode_fixed32_CMockIgnoreArg_dest(UNITY_LINE_TYPE cmock_line)
{
  CMOCK_pb_decode_fixed32_CALL_INSTANCE* cmock_call_instance = cmock_call_instance = (CMOCK_pb_decode_fixed32_CALL_INSTANCE*)CMock_Guts_GetAddressFor(CMock_Guts_MemEndOfChain(Mock.pb_decode_fixed32_CallInstance));
  UNITY_TEST_ASSERT_NOT_NULL(cmock_call_instance, cmock_line, "dest IgnoreArg called before Expect on 'pb_decode_fixed32'.");
  cmock_call_instance->IgnoreArg_dest = 1;
}

bool pb_decode_fixed64(pb_istream_t* stream, void* dest)
{
  UNITY_LINE_TYPE cmock_line = TEST_LINE_NUM;
  CMOCK_pb_decode_fixed64_CALL_INSTANCE* cmock_call_instance = (CMOCK_pb_decode_fixed64_CALL_INSTANCE*)CMock_Guts_GetAddressFor(Mock.pb_decode_fixed64_CallInstance);
  Mock.pb_decode_fixed64_CallInstance = CMock_Guts_MemNext(Mock.pb_decode_fixed64_CallInstance);
  if (Mock.pb_decode_fixed64_IgnoreBool)
  {
    if (cmock_call_instance == NULL)
      return Mock.pb_decode_fixed64_FinalReturn;
    Mock.pb_decode_fixed64_FinalReturn = cmock_call_instance->ReturnVal;
    return cmock_call_instance->ReturnVal;
  }
  if (Mock.pb_decode_fixed64_CallbackFunctionPointer != NULL)
  {
    return Mock.pb_decode_fixed64_CallbackFunctionPointer(stream, dest, Mock.pb_decode_fixed64_CallbackCalls++);
  }
  UNITY_TEST_ASSERT_NOT_NULL(cmock_call_instance, cmock_line, "Function 'pb_decode_fixed64' called more times than expected.");
  cmock_line = cmock_call_instance->LineNumber;
  if (cmock_call_instance->CallOrder > ++GlobalVerifyOrder)
    UNITY_TEST_FAIL(cmock_line, "Function 'pb_decode_fixed64' called earlier than expected.");
  if (cmock_call_instance->CallOrder < GlobalVerifyOrder)
    UNITY_TEST_FAIL(cmock_line, "Function 'pb_decode_fixed64' called later than expected.");
  if (!cmock_call_instance->IgnoreArg_stream)
  {
    if (cmock_call_instance->Expected_stream == NULL)
      { UNITY_TEST_ASSERT_NULL(stream, cmock_line, "Expected NULL. Function 'pb_decode_fixed64' called with unexpected value for argument 'stream'."); }
    else if (cmock_call_instance->Expected_stream_Depth == 0)
      { UNITY_TEST_ASSERT_EQUAL_PTR(cmock_call_instance->Expected_stream, stream, cmock_line, "Function 'pb_decode_fixed64' called with unexpected value for argument 'stream'."); }
    else
      { UNITY_TEST_ASSERT_EQUAL_MEMORY_ARRAY((void*)(cmock_call_instance->Expected_stream), (void*)(stream), sizeof(pb_istream_t), cmock_call_instance->Expected_stream_Depth, cmock_line, "Function 'pb_decode_fixed64' called with unexpected value for argument 'stream'."); }
  }
  if (!cmock_call_instance->IgnoreArg_dest)
  {
    UNITY_TEST_ASSERT_EQUAL_PTR(cmock_call_instance->Expected_dest, dest, cmock_line, "Function 'pb_decode_fixed64' called with unexpected value for argument 'dest'.");
  }
  if (cmock_call_instance->ReturnThruPtr_stream_Used)
  {
    memcpy((void*)stream, (void*)cmock_call_instance->ReturnThruPtr_stream_Val,
      cmock_call_instance->ReturnThruPtr_stream_Size);
  }
  if (cmock_call_instance->ReturnThruPtr_dest_Used)
  {
    memcpy((void*)dest, (void*)cmock_call_instance->ReturnThruPtr_dest_Val,
      cmock_call_instance->ReturnThruPtr_dest_Size);
  }
  return cmock_call_instance->ReturnVal;
}

void CMockExpectParameters_pb_decode_fixed64(CMOCK_pb_decode_fixed64_CALL_INSTANCE* cmock_call_instance, pb_istream_t* stream, int stream_Depth, void* dest, int dest_Depth)
{
  cmock_call_instance->Expected_stream = stream;
  cmock_call_instance->Expected_stream_Depth = stream_Depth;
  cmock_call_instance->IgnoreArg_stream = 0;
  cmock_call_instance->ReturnThruPtr_stream_Used = 0;
  cmock_call_instance->Expected_dest = dest;
  cmock_call_instance->Expected_dest_Depth = dest_Depth;
  cmock_call_instance->IgnoreArg_dest = 0;
  cmock_call_instance->ReturnThruPtr_dest_Used = 0;
}

void pb_decode_fixed64_CMockIgnoreAndReturn(UNITY_LINE_TYPE cmock_line, bool cmock_to_return)
{
  CMOCK_MEM_INDEX_TYPE cmock_guts_index = CMock_Guts_MemNew(sizeof(CMOCK_pb_decode_fixed64_CALL_INSTANCE));
  CMOCK_pb_decode_fixed64_CALL_INSTANCE* cmock_call_instance = (CMOCK_pb_decode_fixed64_CALL_INSTANCE*)CMock_Guts_GetAddressFor(cmock_guts_index);
  UNITY_TEST_ASSERT_NOT_NULL(cmock_call_instance, cmock_line, "CMock has run out of memory. Please allocate more.");
  Mock.pb_decode_fixed64_CallInstance = CMock_Guts_MemChain(Mock.pb_decode_fixed64_CallInstance, cmock_guts_index);
  cmock_call_instance->LineNumber = cmock_line;
  cmock_call_instance->ReturnVal = cmock_to_return;
  Mock.pb_decode_fixed64_IgnoreBool = (int)1;
}

void pb_decode_fixed64_CMockExpectAndReturn(UNITY_LINE_TYPE cmock_line, pb_istream_t* stream, void* dest, bool cmock_to_return)
{
  CMOCK_MEM_INDEX_TYPE cmock_guts_index = CMock_Guts_MemNew(sizeof(CMOCK_pb_decode_fixed64_CALL_INSTANCE));
  CMOCK_pb_decode_fixed64_CALL_INSTANCE* cmock_call_instance = (CMOCK_pb_decode_fixed64_CALL_INSTANCE*)CMock_Guts_GetAddressFor(cmock_guts_index);
  UNITY_TEST_ASSERT_NOT_NULL(cmock_call_instance, cmock_line, "CMock has run out of memory. Please allocate more.");
  Mock.pb_decode_fixed64_CallInstance = CMock_Guts_MemChain(Mock.pb_decode_fixed64_CallInstance, cmock_guts_index);
  cmock_call_instance->LineNumber = cmock_line;
  cmock_call_instance->CallOrder = ++GlobalExpectCount;
  CMockExpectParameters_pb_decode_fixed64(cmock_call_instance, stream, 0, dest, 0);
  cmock_call_instance->ReturnVal = cmock_to_return;
}

void pb_decode_fixed64_StubWithCallback(CMOCK_pb_decode_fixed64_CALLBACK Callback)
{
  Mock.pb_decode_fixed64_CallbackFunctionPointer = Callback;
}

void pb_decode_fixed64_CMockExpectWithArrayAndReturn(UNITY_LINE_TYPE cmock_line, pb_istream_t* stream, int stream_Depth, void* dest, int dest_Depth, bool cmock_to_return)
{
  CMOCK_MEM_INDEX_TYPE cmock_guts_index = CMock_Guts_MemNew(sizeof(CMOCK_pb_decode_fixed64_CALL_INSTANCE));
  CMOCK_pb_decode_fixed64_CALL_INSTANCE* cmock_call_instance = (CMOCK_pb_decode_fixed64_CALL_INSTANCE*)CMock_Guts_GetAddressFor(cmock_guts_index);
  UNITY_TEST_ASSERT_NOT_NULL(cmock_call_instance, cmock_line, "CMock has run out of memory. Please allocate more.");
  Mock.pb_decode_fixed64_CallInstance = CMock_Guts_MemChain(Mock.pb_decode_fixed64_CallInstance, cmock_guts_index);
  cmock_call_instance->LineNumber = cmock_line;
  cmock_call_instance->CallOrder = ++GlobalExpectCount;
  CMockExpectParameters_pb_decode_fixed64(cmock_call_instance, stream, stream_Depth, dest, dest_Depth);
  cmock_call_instance->ReturnVal = cmock_to_return;
}

void pb_decode_fixed64_CMockReturnMemThruPtr_stream(UNITY_LINE_TYPE cmock_line, pb_istream_t* stream, int cmock_size)
{
  CMOCK_pb_decode_fixed64_CALL_INSTANCE* cmock_call_instance = cmock_call_instance = (CMOCK_pb_decode_fixed64_CALL_INSTANCE*)CMock_Guts_GetAddressFor(CMock_Guts_MemEndOfChain(Mock.pb_decode_fixed64_CallInstance));
  UNITY_TEST_ASSERT_NOT_NULL(cmock_call_instance, cmock_line, "stream ReturnThruPtr called before Expect on 'pb_decode_fixed64'.");
  cmock_call_instance->ReturnThruPtr_stream_Used = 1;
  cmock_call_instance->ReturnThruPtr_stream_Val = stream;
  cmock_call_instance->ReturnThruPtr_stream_Size = cmock_size;
}

void pb_decode_fixed64_CMockReturnMemThruPtr_dest(UNITY_LINE_TYPE cmock_line, void* dest, int cmock_size)
{
  CMOCK_pb_decode_fixed64_CALL_INSTANCE* cmock_call_instance = cmock_call_instance = (CMOCK_pb_decode_fixed64_CALL_INSTANCE*)CMock_Guts_GetAddressFor(CMock_Guts_MemEndOfChain(Mock.pb_decode_fixed64_CallInstance));
  UNITY_TEST_ASSERT_NOT_NULL(cmock_call_instance, cmock_line, "dest ReturnThruPtr called before Expect on 'pb_decode_fixed64'.");
  cmock_call_instance->ReturnThruPtr_dest_Used = 1;
  cmock_call_instance->ReturnThruPtr_dest_Val = dest;
  cmock_call_instance->ReturnThruPtr_dest_Size = cmock_size;
}

void pb_decode_fixed64_CMockIgnoreArg_stream(UNITY_LINE_TYPE cmock_line)
{
  CMOCK_pb_decode_fixed64_CALL_INSTANCE* cmock_call_instance = cmock_call_instance = (CMOCK_pb_decode_fixed64_CALL_INSTANCE*)CMock_Guts_GetAddressFor(CMock_Guts_MemEndOfChain(Mock.pb_decode_fixed64_CallInstance));
  UNITY_TEST_ASSERT_NOT_NULL(cmock_call_instance, cmock_line, "stream IgnoreArg called before Expect on 'pb_decode_fixed64'.");
  cmock_call_instance->IgnoreArg_stream = 1;
}

void pb_decode_fixed64_CMockIgnoreArg_dest(UNITY_LINE_TYPE cmock_line)
{
  CMOCK_pb_decode_fixed64_CALL_INSTANCE* cmock_call_instance = cmock_call_instance = (CMOCK_pb_decode_fixed64_CALL_INSTANCE*)CMock_Guts_GetAddressFor(CMock_Guts_MemEndOfChain(Mock.pb_decode_fixed64_CallInstance));
  UNITY_TEST_ASSERT_NOT_NULL(cmock_call_instance, cmock_line, "dest IgnoreArg called before Expect on 'pb_decode_fixed64'.");
  cmock_call_instance->IgnoreArg_dest = 1;
}

bool pb_make_string_substream(pb_istream_t* stream, pb_istream_t* substream)
{
  UNITY_LINE_TYPE cmock_line = TEST_LINE_NUM;
  CMOCK_pb_make_string_substream_CALL_INSTANCE* cmock_call_instance = (CMOCK_pb_make_string_substream_CALL_INSTANCE*)CMock_Guts_GetAddressFor(Mock.pb_make_string_substream_CallInstance);
  Mock.pb_make_string_substream_CallInstance = CMock_Guts_MemNext(Mock.pb_make_string_substream_CallInstance);
  if (Mock.pb_make_string_substream_IgnoreBool)
  {
    if (cmock_call_instance == NULL)
      return Mock.pb_make_string_substream_FinalReturn;
    Mock.pb_make_string_substream_FinalReturn = cmock_call_instance->ReturnVal;
    return cmock_call_instance->ReturnVal;
  }
  if (Mock.pb_make_string_substream_CallbackFunctionPointer != NULL)
  {
    return Mock.pb_make_string_substream_CallbackFunctionPointer(stream, substream, Mock.pb_make_string_substream_CallbackCalls++);
  }
  UNITY_TEST_ASSERT_NOT_NULL(cmock_call_instance, cmock_line, "Function 'pb_make_string_substream' called more times than expected.");
  cmock_line = cmock_call_instance->LineNumber;
  if (cmock_call_instance->CallOrder > ++GlobalVerifyOrder)
    UNITY_TEST_FAIL(cmock_line, "Function 'pb_make_string_substream' called earlier than expected.");
  if (cmock_call_instance->CallOrder < GlobalVerifyOrder)
    UNITY_TEST_FAIL(cmock_line, "Function 'pb_make_string_substream' called later than expected.");
  if (!cmock_call_instance->IgnoreArg_stream)
  {
    if (cmock_call_instance->Expected_stream == NULL)
      { UNITY_TEST_ASSERT_NULL(stream, cmock_line, "Expected NULL. Function 'pb_make_string_substream' called with unexpected value for argument 'stream'."); }
    else if (cmock_call_instance->Expected_stream_Depth == 0)
      { UNITY_TEST_ASSERT_EQUAL_PTR(cmock_call_instance->Expected_stream, stream, cmock_line, "Function 'pb_make_string_substream' called with unexpected value for argument 'stream'."); }
    else
      { UNITY_TEST_ASSERT_EQUAL_MEMORY_ARRAY((void*)(cmock_call_instance->Expected_stream), (void*)(stream), sizeof(pb_istream_t), cmock_call_instance->Expected_stream_Depth, cmock_line, "Function 'pb_make_string_substream' called with unexpected value for argument 'stream'."); }
  }
  if (!cmock_call_instance->IgnoreArg_substream)
  {
    if (cmock_call_instance->Expected_substream == NULL)
      { UNITY_TEST_ASSERT_NULL(substream, cmock_line, "Expected NULL. Function 'pb_make_string_substream' called with unexpected value for argument 'substream'."); }
    else if (cmock_call_instance->Expected_substream_Depth == 0)
      { UNITY_TEST_ASSERT_EQUAL_PTR(cmock_call_instance->Expected_substream, substream, cmock_line, "Function 'pb_make_string_substream' called with unexpected value for argument 'substream'."); }
    else
      { UNITY_TEST_ASSERT_EQUAL_MEMORY_ARRAY((void*)(cmock_call_instance->Expected_substream), (void*)(substream), sizeof(pb_istream_t), cmock_call_instance->Expected_substream_Depth, cmock_line, "Function 'pb_make_string_substream' called with unexpected value for argument 'substream'."); }
  }
  if (cmock_call_instance->ReturnThruPtr_stream_Used)
  {
    memcpy((void*)stream, (void*)cmock_call_instance->ReturnThruPtr_stream_Val,
      cmock_call_instance->ReturnThruPtr_stream_Size);
  }
  if (cmock_call_instance->ReturnThruPtr_substream_Used)
  {
    memcpy((void*)substream, (void*)cmock_call_instance->ReturnThruPtr_substream_Val,
      cmock_call_instance->ReturnThruPtr_substream_Size);
  }
  return cmock_call_instance->ReturnVal;
}

void CMockExpectParameters_pb_make_string_substream(CMOCK_pb_make_string_substream_CALL_INSTANCE* cmock_call_instance, pb_istream_t* stream, int stream_Depth, pb_istream_t* substream, int substream_Depth)
{
  cmock_call_instance->Expected_stream = stream;
  cmock_call_instance->Expected_stream_Depth = stream_Depth;
  cmock_call_instance->IgnoreArg_stream = 0;
  cmock_call_instance->ReturnThruPtr_stream_Used = 0;
  cmock_call_instance->Expected_substream = substream;
  cmock_call_instance->Expected_substream_Depth = substream_Depth;
  cmock_call_instance->IgnoreArg_substream = 0;
  cmock_call_instance->ReturnThruPtr_substream_Used = 0;
}

void pb_make_string_substream_CMockIgnoreAndReturn(UNITY_LINE_TYPE cmock_line, bool cmock_to_return)
{
  CMOCK_MEM_INDEX_TYPE cmock_guts_index = CMock_Guts_MemNew(sizeof(CMOCK_pb_make_string_substream_CALL_INSTANCE));
  CMOCK_pb_make_string_substream_CALL_INSTANCE* cmock_call_instance = (CMOCK_pb_make_string_substream_CALL_INSTANCE*)CMock_Guts_GetAddressFor(cmock_guts_index);
  UNITY_TEST_ASSERT_NOT_NULL(cmock_call_instance, cmock_line, "CMock has run out of memory. Please allocate more.");
  Mock.pb_make_string_substream_CallInstance = CMock_Guts_MemChain(Mock.pb_make_string_substream_CallInstance, cmock_guts_index);
  cmock_call_instance->LineNumber = cmock_line;
  cmock_call_instance->ReturnVal = cmock_to_return;
  Mock.pb_make_string_substream_IgnoreBool = (int)1;
}

void pb_make_string_substream_CMockExpectAndReturn(UNITY_LINE_TYPE cmock_line, pb_istream_t* stream, pb_istream_t* substream, bool cmock_to_return)
{
  CMOCK_MEM_INDEX_TYPE cmock_guts_index = CMock_Guts_MemNew(sizeof(CMOCK_pb_make_string_substream_CALL_INSTANCE));
  CMOCK_pb_make_string_substream_CALL_INSTANCE* cmock_call_instance = (CMOCK_pb_make_string_substream_CALL_INSTANCE*)CMock_Guts_GetAddressFor(cmock_guts_index);
  UNITY_TEST_ASSERT_NOT_NULL(cmock_call_instance, cmock_line, "CMock has run out of memory. Please allocate more.");
  Mock.pb_make_string_substream_CallInstance = CMock_Guts_MemChain(Mock.pb_make_string_substream_CallInstance, cmock_guts_index);
  cmock_call_instance->LineNumber = cmock_line;
  cmock_call_instance->CallOrder = ++GlobalExpectCount;
  CMockExpectParameters_pb_make_string_substream(cmock_call_instance, stream, 0, substream, 0);
  cmock_call_instance->ReturnVal = cmock_to_return;
}

void pb_make_string_substream_StubWithCallback(CMOCK_pb_make_string_substream_CALLBACK Callback)
{
  Mock.pb_make_string_substream_CallbackFunctionPointer = Callback;
}

void pb_make_string_substream_CMockExpectWithArrayAndReturn(UNITY_LINE_TYPE cmock_line, pb_istream_t* stream, int stream_Depth, pb_istream_t* substream, int substream_Depth, bool cmock_to_return)
{
  CMOCK_MEM_INDEX_TYPE cmock_guts_index = CMock_Guts_MemNew(sizeof(CMOCK_pb_make_string_substream_CALL_INSTANCE));
  CMOCK_pb_make_string_substream_CALL_INSTANCE* cmock_call_instance = (CMOCK_pb_make_string_substream_CALL_INSTANCE*)CMock_Guts_GetAddressFor(cmock_guts_index);
  UNITY_TEST_ASSERT_NOT_NULL(cmock_call_instance, cmock_line, "CMock has run out of memory. Please allocate more.");
  Mock.pb_make_string_substream_CallInstance = CMock_Guts_MemChain(Mock.pb_make_string_substream_CallInstance, cmock_guts_index);
  cmock_call_instance->LineNumber = cmock_line;
  cmock_call_instance->CallOrder = ++GlobalExpectCount;
  CMockExpectParameters_pb_make_string_substream(cmock_call_instance, stream, stream_Depth, substream, substream_Depth);
  cmock_call_instance->ReturnVal = cmock_to_return;
}

void pb_make_string_substream_CMockReturnMemThruPtr_stream(UNITY_LINE_TYPE cmock_line, pb_istream_t* stream, int cmock_size)
{
  CMOCK_pb_make_string_substream_CALL_INSTANCE* cmock_call_instance = cmock_call_instance = (CMOCK_pb_make_string_substream_CALL_INSTANCE*)CMock_Guts_GetAddressFor(CMock_Guts_MemEndOfChain(Mock.pb_make_string_substream_CallInstance));
  UNITY_TEST_ASSERT_NOT_NULL(cmock_call_instance, cmock_line, "stream ReturnThruPtr called before Expect on 'pb_make_string_substream'.");
  cmock_call_instance->ReturnThruPtr_stream_Used = 1;
  cmock_call_instance->ReturnThruPtr_stream_Val = stream;
  cmock_call_instance->ReturnThruPtr_stream_Size = cmock_size;
}

void pb_make_string_substream_CMockReturnMemThruPtr_substream(UNITY_LINE_TYPE cmock_line, pb_istream_t* substream, int cmock_size)
{
  CMOCK_pb_make_string_substream_CALL_INSTANCE* cmock_call_instance = cmock_call_instance = (CMOCK_pb_make_string_substream_CALL_INSTANCE*)CMock_Guts_GetAddressFor(CMock_Guts_MemEndOfChain(Mock.pb_make_string_substream_CallInstance));
  UNITY_TEST_ASSERT_NOT_NULL(cmock_call_instance, cmock_line, "substream ReturnThruPtr called before Expect on 'pb_make_string_substream'.");
  cmock_call_instance->ReturnThruPtr_substream_Used = 1;
  cmock_call_instance->ReturnThruPtr_substream_Val = substream;
  cmock_call_instance->ReturnThruPtr_substream_Size = cmock_size;
}

void pb_make_string_substream_CMockIgnoreArg_stream(UNITY_LINE_TYPE cmock_line)
{
  CMOCK_pb_make_string_substream_CALL_INSTANCE* cmock_call_instance = cmock_call_instance = (CMOCK_pb_make_string_substream_CALL_INSTANCE*)CMock_Guts_GetAddressFor(CMock_Guts_MemEndOfChain(Mock.pb_make_string_substream_CallInstance));
  UNITY_TEST_ASSERT_NOT_NULL(cmock_call_instance, cmock_line, "stream IgnoreArg called before Expect on 'pb_make_string_substream'.");
  cmock_call_instance->IgnoreArg_stream = 1;
}

void pb_make_string_substream_CMockIgnoreArg_substream(UNITY_LINE_TYPE cmock_line)
{
  CMOCK_pb_make_string_substream_CALL_INSTANCE* cmock_call_instance = cmock_call_instance = (CMOCK_pb_make_string_substream_CALL_INSTANCE*)CMock_Guts_GetAddressFor(CMock_Guts_MemEndOfChain(Mock.pb_make_string_substream_CallInstance));
  UNITY_TEST_ASSERT_NOT_NULL(cmock_call_instance, cmock_line, "substream IgnoreArg called before Expect on 'pb_make_string_substream'.");
  cmock_call_instance->IgnoreArg_substream = 1;
}

void pb_close_string_substream(pb_istream_t* stream, pb_istream_t* substream)
{
  UNITY_LINE_TYPE cmock_line = TEST_LINE_NUM;
  CMOCK_pb_close_string_substream_CALL_INSTANCE* cmock_call_instance = (CMOCK_pb_close_string_substream_CALL_INSTANCE*)CMock_Guts_GetAddressFor(Mock.pb_close_string_substream_CallInstance);
  Mock.pb_close_string_substream_CallInstance = CMock_Guts_MemNext(Mock.pb_close_string_substream_CallInstance);
  if (Mock.pb_close_string_substream_IgnoreBool)
  {
    return;
  }
  if (Mock.pb_close_string_substream_CallbackFunctionPointer != NULL)
  {
    Mock.pb_close_string_substream_CallbackFunctionPointer(stream, substream, Mock.pb_close_string_substream_CallbackCalls++);
    return;
  }
  UNITY_TEST_ASSERT_NOT_NULL(cmock_call_instance, cmock_line, "Function 'pb_close_string_substream' called more times than expected.");
  cmock_line = cmock_call_instance->LineNumber;
  if (cmock_call_instance->CallOrder > ++GlobalVerifyOrder)
    UNITY_TEST_FAIL(cmock_line, "Function 'pb_close_string_substream' called earlier than expected.");
  if (cmock_call_instance->CallOrder < GlobalVerifyOrder)
    UNITY_TEST_FAIL(cmock_line, "Function 'pb_close_string_substream' called later than expected.");
  if (!cmock_call_instance->IgnoreArg_stream)
  {
    if (cmock_call_instance->Expected_stream == NULL)
      { UNITY_TEST_ASSERT_NULL(stream, cmock_line, "Expected NULL. Function 'pb_close_string_substream' called with unexpected value for argument 'stream'."); }
    else if (cmock_call_instance->Expected_stream_Depth == 0)
      { UNITY_TEST_ASSERT_EQUAL_PTR(cmock_call_instance->Expected_stream, stream, cmock_line, "Function 'pb_close_string_substream' called with unexpected value for argument 'stream'."); }
    else
      { UNITY_TEST_ASSERT_EQUAL_MEMORY_ARRAY((void*)(cmock_call_instance->Expected_stream), (void*)(stream), sizeof(pb_istream_t), cmock_call_instance->Expected_stream_Depth, cmock_line, "Function 'pb_close_string_substream' called with unexpected value for argument 'stream'."); }
  }
  if (!cmock_call_instance->IgnoreArg_substream)
  {
    if (cmock_call_instance->Expected_substream == NULL)
      { UNITY_TEST_ASSERT_NULL(substream, cmock_line, "Expected NULL. Function 'pb_close_string_substream' called with unexpected value for argument 'substream'."); }
    else if (cmock_call_instance->Expected_substream_Depth == 0)
      { UNITY_TEST_ASSERT_EQUAL_PTR(cmock_call_instance->Expected_substream, substream, cmock_line, "Function 'pb_close_string_substream' called with unexpected value for argument 'substream'."); }
    else
      { UNITY_TEST_ASSERT_EQUAL_MEMORY_ARRAY((void*)(cmock_call_instance->Expected_substream), (void*)(substream), sizeof(pb_istream_t), cmock_call_instance->Expected_substream_Depth, cmock_line, "Function 'pb_close_string_substream' called with unexpected value for argument 'substream'."); }
  }
  if (cmock_call_instance->ReturnThruPtr_stream_Used)
  {
    memcpy((void*)stream, (void*)cmock_call_instance->ReturnThruPtr_stream_Val,
      cmock_call_instance->ReturnThruPtr_stream_Size);
  }
  if (cmock_call_instance->ReturnThruPtr_substream_Used)
  {
    memcpy((void*)substream, (void*)cmock_call_instance->ReturnThruPtr_substream_Val,
      cmock_call_instance->ReturnThruPtr_substream_Size);
  }
}

void CMockExpectParameters_pb_close_string_substream(CMOCK_pb_close_string_substream_CALL_INSTANCE* cmock_call_instance, pb_istream_t* stream, int stream_Depth, pb_istream_t* substream, int substream_Depth)
{
  cmock_call_instance->Expected_stream = stream;
  cmock_call_instance->Expected_stream_Depth = stream_Depth;
  cmock_call_instance->IgnoreArg_stream = 0;
  cmock_call_instance->ReturnThruPtr_stream_Used = 0;
  cmock_call_instance->Expected_substream = substream;
  cmock_call_instance->Expected_substream_Depth = substream_Depth;
  cmock_call_instance->IgnoreArg_substream = 0;
  cmock_call_instance->ReturnThruPtr_substream_Used = 0;
}

void pb_close_string_substream_CMockIgnore(void)
{
  Mock.pb_close_string_substream_IgnoreBool = (int)1;
}

void pb_close_string_substream_CMockExpect(UNITY_LINE_TYPE cmock_line, pb_istream_t* stream, pb_istream_t* substream)
{
  CMOCK_MEM_INDEX_TYPE cmock_guts_index = CMock_Guts_MemNew(sizeof(CMOCK_pb_close_string_substream_CALL_INSTANCE));
  CMOCK_pb_close_string_substream_CALL_INSTANCE* cmock_call_instance = (CMOCK_pb_close_string_substream_CALL_INSTANCE*)CMock_Guts_GetAddressFor(cmock_guts_index);
  UNITY_TEST_ASSERT_NOT_NULL(cmock_call_instance, cmock_line, "CMock has run out of memory. Please allocate more.");
  Mock.pb_close_string_substream_CallInstance = CMock_Guts_MemChain(Mock.pb_close_string_substream_CallInstance, cmock_guts_index);
  cmock_call_instance->LineNumber = cmock_line;
  cmock_call_instance->CallOrder = ++GlobalExpectCount;
  CMockExpectParameters_pb_close_string_substream(cmock_call_instance, stream, 0, substream, 0);
}

void pb_close_string_substream_StubWithCallback(CMOCK_pb_close_string_substream_CALLBACK Callback)
{
  Mock.pb_close_string_substream_CallbackFunctionPointer = Callback;
}

void pb_close_string_substream_CMockExpectWithArray(UNITY_LINE_TYPE cmock_line, pb_istream_t* stream, int stream_Depth, pb_istream_t* substream, int substream_Depth)
{
  CMOCK_MEM_INDEX_TYPE cmock_guts_index = CMock_Guts_MemNew(sizeof(CMOCK_pb_close_string_substream_CALL_INSTANCE));
  CMOCK_pb_close_string_substream_CALL_INSTANCE* cmock_call_instance = (CMOCK_pb_close_string_substream_CALL_INSTANCE*)CMock_Guts_GetAddressFor(cmock_guts_index);
  UNITY_TEST_ASSERT_NOT_NULL(cmock_call_instance, cmock_line, "CMock has run out of memory. Please allocate more.");
  Mock.pb_close_string_substream_CallInstance = CMock_Guts_MemChain(Mock.pb_close_string_substream_CallInstance, cmock_guts_index);
  cmock_call_instance->LineNumber = cmock_line;
  cmock_call_instance->CallOrder = ++GlobalExpectCount;
  CMockExpectParameters_pb_close_string_substream(cmock_call_instance, stream, stream_Depth, substream, substream_Depth);
}

void pb_close_string_substream_CMockReturnMemThruPtr_stream(UNITY_LINE_TYPE cmock_line, pb_istream_t* stream, int cmock_size)
{
  CMOCK_pb_close_string_substream_CALL_INSTANCE* cmock_call_instance = cmock_call_instance = (CMOCK_pb_close_string_substream_CALL_INSTANCE*)CMock_Guts_GetAddressFor(CMock_Guts_MemEndOfChain(Mock.pb_close_string_substream_CallInstance));
  UNITY_TEST_ASSERT_NOT_NULL(cmock_call_instance, cmock_line, "stream ReturnThruPtr called before Expect on 'pb_close_string_substream'.");
  cmock_call_instance->ReturnThruPtr_stream_Used = 1;
  cmock_call_instance->ReturnThruPtr_stream_Val = stream;
  cmock_call_instance->ReturnThruPtr_stream_Size = cmock_size;
}

void pb_close_string_substream_CMockReturnMemThruPtr_substream(UNITY_LINE_TYPE cmock_line, pb_istream_t* substream, int cmock_size)
{
  CMOCK_pb_close_string_substream_CALL_INSTANCE* cmock_call_instance = cmock_call_instance = (CMOCK_pb_close_string_substream_CALL_INSTANCE*)CMock_Guts_GetAddressFor(CMock_Guts_MemEndOfChain(Mock.pb_close_string_substream_CallInstance));
  UNITY_TEST_ASSERT_NOT_NULL(cmock_call_instance, cmock_line, "substream ReturnThruPtr called before Expect on 'pb_close_string_substream'.");
  cmock_call_instance->ReturnThruPtr_substream_Used = 1;
  cmock_call_instance->ReturnThruPtr_substream_Val = substream;
  cmock_call_instance->ReturnThruPtr_substream_Size = cmock_size;
}

void pb_close_string_substream_CMockIgnoreArg_stream(UNITY_LINE_TYPE cmock_line)
{
  CMOCK_pb_close_string_substream_CALL_INSTANCE* cmock_call_instance = cmock_call_instance = (CMOCK_pb_close_string_substream_CALL_INSTANCE*)CMock_Guts_GetAddressFor(CMock_Guts_MemEndOfChain(Mock.pb_close_string_substream_CallInstance));
  UNITY_TEST_ASSERT_NOT_NULL(cmock_call_instance, cmock_line, "stream IgnoreArg called before Expect on 'pb_close_string_substream'.");
  cmock_call_instance->IgnoreArg_stream = 1;
}

void pb_close_string_substream_CMockIgnoreArg_substream(UNITY_LINE_TYPE cmock_line)
{
  CMOCK_pb_close_string_substream_CALL_INSTANCE* cmock_call_instance = cmock_call_instance = (CMOCK_pb_close_string_substream_CALL_INSTANCE*)CMock_Guts_GetAddressFor(CMock_Guts_MemEndOfChain(Mock.pb_close_string_substream_CallInstance));
  UNITY_TEST_ASSERT_NOT_NULL(cmock_call_instance, cmock_line, "substream IgnoreArg called before Expect on 'pb_close_string_substream'.");
  cmock_call_instance->IgnoreArg_substream = 1;
}

/* lint -restore */
