<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=iso-8859-1">
<title>The Protothreads Library 1.4: Protothread semaphores</title>
<link href="doxygen.css" rel="stylesheet" type="text/css">
<link href="tabs.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.4.6 -->
<div class="tabs">
  <ul>
    <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
    <li><a href="modules.html"><span>Modules</span></a></li>
    <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    <li><a href="files.html"><span>Files</span></a></li>
  </ul></div>
<h1>Protothread semaphores<br>
<small>
[<a class="el" href="a00014.html">Protothreads</a>]</small>
</h1><hr><a name="_details"></a><h2>Detailed Description</h2>
This module implements counting semaphores on top of protothreads. 
<p>
Semaphores are a synchronization primitive that provide two operations: "wait" and "signal". The "wait" operation checks the semaphore counter and blocks the thread if the counter is zero. The "signal" operation increases the semaphore counter but does not block. If another thread has blocked waiting for the semaphore that is signalled, the blocked thread will become runnable again.<p>
Semaphores can be used to implement other, more structured, synchronization primitives such as monitors and message queues/bounded buffers (see below).<p>
The following example shows how the producer-consumer problem, also known as the bounded buffer problem, can be solved using protothreads and semaphores. Notes on the program follow after the example.<p>
<div class="fragment"><pre class="fragment"><span class="preprocessor">#include "<a class="code" href="a00012.html">pt-sem.h</a>"</span>

<span class="preprocessor">#define NUM_ITEMS 32</span>
<span class="preprocessor"></span><span class="preprocessor">#define BUFSIZE 8</span>
<span class="preprocessor"></span>
<span class="keyword">static</span> <span class="keyword">struct </span><a class="code" href="a00006.html">pt_sem</a> mutex, full, empty;

<a class="code" href="a00014.html#g3d4c8bd4aada659eb34f5d2ffd3e7901">PT_THREAD</a>(producer(<span class="keyword">struct</span> pt *pt))
{
  <span class="keyword">static</span> <span class="keywordtype">int</span> produced;
  
  <a class="code" href="a00014.html#g2ffbb9e554e08a343ae2f9de4bedfdfc">PT_BEGIN</a>(pt);
  
  <span class="keywordflow">for</span>(produced = 0; produced &lt; NUM_ITEMS; ++produced) {
  
    <a class="code" href="a00016.html#g386ff87a52a840512906f2940e229e2e">PT_SEM_WAIT</a>(pt, &amp;full);
    
    <a class="code" href="a00016.html#g386ff87a52a840512906f2940e229e2e">PT_SEM_WAIT</a>(pt, &amp;mutex);
    add_to_buffer(produce_item());    
    <a class="code" href="a00016.html#g1eaaf4d9d75e24582acc6440d7085f19">PT_SEM_SIGNAL</a>(pt, &amp;mutex);
    
    <a class="code" href="a00016.html#g1eaaf4d9d75e24582acc6440d7085f19">PT_SEM_SIGNAL</a>(pt, &amp;empty);
  }

  <a class="code" href="a00014.html#g7b04a0035bef29d905496c23bae066d2">PT_END</a>(pt);
}

<a class="code" href="a00014.html#g3d4c8bd4aada659eb34f5d2ffd3e7901">PT_THREAD</a>(consumer(<span class="keyword">struct</span> pt *pt))
{
  <span class="keyword">static</span> <span class="keywordtype">int</span> consumed;
  
  <a class="code" href="a00014.html#g2ffbb9e554e08a343ae2f9de4bedfdfc">PT_BEGIN</a>(pt);

  <span class="keywordflow">for</span>(consumed = 0; consumed &lt; NUM_ITEMS; ++consumed) {
    
    <a class="code" href="a00016.html#g386ff87a52a840512906f2940e229e2e">PT_SEM_WAIT</a>(pt, &amp;empty);
    
    <a class="code" href="a00016.html#g386ff87a52a840512906f2940e229e2e">PT_SEM_WAIT</a>(pt, &amp;mutex);    
    consume_item(get_from_buffer());    
    <a class="code" href="a00016.html#g1eaaf4d9d75e24582acc6440d7085f19">PT_SEM_SIGNAL</a>(pt, &amp;mutex);
    
    <a class="code" href="a00016.html#g1eaaf4d9d75e24582acc6440d7085f19">PT_SEM_SIGNAL</a>(pt, &amp;full);
  }

  <a class="code" href="a00014.html#g7b04a0035bef29d905496c23bae066d2">PT_END</a>(pt);
}

<a class="code" href="a00014.html#g3d4c8bd4aada659eb34f5d2ffd3e7901">PT_THREAD</a>(driver_thread(<span class="keyword">struct</span> pt *pt))
{
  <span class="keyword">static</span> <span class="keyword">struct </span>pt pt_producer, pt_consumer;

  <a class="code" href="a00014.html#g2ffbb9e554e08a343ae2f9de4bedfdfc">PT_BEGIN</a>(pt);
  
  <a class="code" href="a00016.html#gd7089c5dc86f12019f0361d82a75b04b">PT_SEM_INIT</a>(&amp;empty, 0);
  <a class="code" href="a00016.html#gd7089c5dc86f12019f0361d82a75b04b">PT_SEM_INIT</a>(&amp;full, BUFSIZE);
  <a class="code" href="a00016.html#gd7089c5dc86f12019f0361d82a75b04b">PT_SEM_INIT</a>(&amp;mutex, 1);

  <a class="code" href="a00014.html#ge6bae7dc0225468c8a5ac269df549892">PT_INIT</a>(&amp;pt_producer);
  <a class="code" href="a00014.html#ge6bae7dc0225468c8a5ac269df549892">PT_INIT</a>(&amp;pt_consumer);

  <a class="code" href="a00014.html#g2f8f70c30b9ee08a103fbd69a4365c4c">PT_WAIT_THREAD</a>(pt, producer(&amp;pt_producer) &amp;
                     consumer(&amp;pt_consumer));

  <a class="code" href="a00014.html#g7b04a0035bef29d905496c23bae066d2">PT_END</a>(pt);
}
</pre></div><p>
The program uses three protothreads: one protothread that implements the consumer, one thread that implements the producer, and one protothread that drives the two other protothreads. The program uses three semaphores: "full", "empty" and "mutex". The "mutex" semaphore is used to provide mutual exclusion for the buffer, the "empty" semaphore is used to block the consumer is the buffer is empty, and the "full" semaphore is used to block the producer is the buffer is full.<p>
The "driver_thread" holds two protothread state variables, "pt_producer" and "pt_consumer". It is important to note that both these variables are declared as <em>static</em>. If the static keyword is not used, both variables are stored on the stack. Since protothreads do not store the stack, these variables may be overwritten during a protothread wait operation. Similarly, both the "consumer" and "producer" protothreads declare their local variables as static, to avoid them being stored on the stack. 
<p>
<table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Files</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">file &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00012.html">pt-sem.h</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Couting semaphores implemented on protothreads. <br></td></tr>

<p>
<tr><td colspan="2"><br><h2>Data Structures</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00006.html">pt_sem</a></td></tr>

<tr><td colspan="2"><br><h2>Defines</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">#define&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00016.html#gd7089c5dc86f12019f0361d82a75b04b">PT_SEM_INIT</a>(s, c)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Initialize a semaphore.  <a href="#gd7089c5dc86f12019f0361d82a75b04b"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">#define&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00016.html#g386ff87a52a840512906f2940e229e2e">PT_SEM_WAIT</a>(<a class="el" href="a00005.html">pt</a>, s)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Wait for a semaphore.  <a href="#g386ff87a52a840512906f2940e229e2e"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">#define&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00016.html#g1eaaf4d9d75e24582acc6440d7085f19">PT_SEM_SIGNAL</a>(<a class="el" href="a00005.html">pt</a>, s)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Signal a semaphore.  <a href="#g1eaaf4d9d75e24582acc6440d7085f19"></a><br></td></tr>
</table>
<hr><h2>Define Documentation</h2>
<a class="anchor" name="gd7089c5dc86f12019f0361d82a75b04b"></a><!-- doxytag: member="pt-sem.h::PT_SEM_INIT" ref="gd7089c5dc86f12019f0361d82a75b04b" args="(s, c)" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top">#define PT_SEM_INIT          </td>
          <td class="md" valign="top">(&nbsp;</td>
          <td class="md" nowrap valign="top">s,         <tr>
          <td class="md" nowrap align="right"></td>
          <td class="md"></td>
          <td class="md" nowrap>c&nbsp;</td>
          <td class="mdname1" valign="top" nowrap>          </td>
          <td class="md" valign="top">&nbsp;)&nbsp;</td>
          <td class="md" nowrap></td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
Initialize a semaphore. 
<p>
This macro initializes a semaphore with a value for the counter. Internally, the semaphores use an "unsigned int" to represent the counter, and therefore the "count" argument should be within range of an unsigned int.<p>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>s</em>&nbsp;</td><td>(struct <a class="el" href="a00006.html">pt_sem</a> *) A pointer to the <a class="el" href="a00006.html">pt_sem</a> struct representing the semaphore</td></tr>
    <tr><td valign="top"></td><td valign="top"><em>c</em>&nbsp;</td><td>(unsigned int) The initial count of the semaphore. </td></tr>
  </table>
</dl>

<p>
Definition at line <a class="el" href="a00021.html#l00183">183</a> of file <a class="el" href="a00021.html">pt-sem.h</a>.    </td>
  </tr>
</table>
<a class="anchor" name="g1eaaf4d9d75e24582acc6440d7085f19"></a><!-- doxytag: member="pt-sem.h::PT_SEM_SIGNAL" ref="g1eaaf4d9d75e24582acc6440d7085f19" args="(pt, s)" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top">#define PT_SEM_SIGNAL          </td>
          <td class="md" valign="top">(&nbsp;</td>
          <td class="md" nowrap valign="top"><a class="el" href="a00005.html">pt</a>,         <tr>
          <td class="md" nowrap align="right"></td>
          <td class="md"></td>
          <td class="md" nowrap>s&nbsp;</td>
          <td class="mdname1" valign="top" nowrap>          </td>
          <td class="md" valign="top">&nbsp;)&nbsp;</td>
          <td class="md" nowrap></td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
Signal a semaphore. 
<p>
This macro carries out the "signal" operation on the semaphore. The signal operation increments the counter inside the semaphore, which eventually will cause waiting protothreads to continue executing.<p>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>pt</em>&nbsp;</td><td>(struct pt *) A pointer to the protothread (struct pt) in which the operation is executed.</td></tr>
    <tr><td valign="top"></td><td valign="top"><em>s</em>&nbsp;</td><td>(struct <a class="el" href="a00006.html">pt_sem</a> *) A pointer to the <a class="el" href="a00006.html">pt_sem</a> struct representing the semaphore </td></tr>
  </table>
</dl>

<p>
Definition at line <a class="el" href="a00021.html#l00222">222</a> of file <a class="el" href="a00021.html">pt-sem.h</a>.    </td>
  </tr>
</table>
<a class="anchor" name="g386ff87a52a840512906f2940e229e2e"></a><!-- doxytag: member="pt-sem.h::PT_SEM_WAIT" ref="g386ff87a52a840512906f2940e229e2e" args="(pt, s)" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top">#define PT_SEM_WAIT          </td>
          <td class="md" valign="top">(&nbsp;</td>
          <td class="md" nowrap valign="top"><a class="el" href="a00005.html">pt</a>,         <tr>
          <td class="md" nowrap align="right"></td>
          <td class="md"></td>
          <td class="md" nowrap>s&nbsp;</td>
          <td class="mdname1" valign="top" nowrap>          </td>
          <td class="md" valign="top">&nbsp;)&nbsp;</td>
          <td class="md" nowrap></td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
Wait for a semaphore. 
<p>
This macro carries out the "wait" operation on the semaphore. The wait operation causes the protothread to block while the counter is zero. When the counter reaches a value larger than zero, the protothread will continue.<p>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>pt</em>&nbsp;</td><td>(struct pt *) A pointer to the protothread (struct pt) in which the operation is executed.</td></tr>
    <tr><td valign="top"></td><td valign="top"><em>s</em>&nbsp;</td><td>(struct <a class="el" href="a00006.html">pt_sem</a> *) A pointer to the <a class="el" href="a00006.html">pt_sem</a> struct representing the semaphore </td></tr>
  </table>
</dl>

<p>
Definition at line <a class="el" href="a00021.html#l00201">201</a> of file <a class="el" href="a00021.html">pt-sem.h</a>.    </td>
  </tr>
</table>
<hr size="1"><address style="align: right;"><small>Generated on Mon Oct 2 10:06:29 2006 for The Protothreads Library 1.4 by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.4.6 </small></address>
</body>
</html>
