<?xml version="1.0" encoding="iso-8859-1"?>


<project>
  <fileVersion>2</fileVersion>  <configuration>
    <name>nrf52811_xxaa_s112</name>
    <toolchain>
      <name>ARM</name>
    </toolchain>
    <debug>0</debug>
    <settings>
      <name>General</name>
      <archiveVersion>3</archiveVersion>
      <data>
        <version>22</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>0</debug>
        <option>
          <name>ExePath</name>
          <state>_build</state>
        </option>
        <option>
          <name>ObjPath</name>
          <state>_build</state>
        </option>
        <option>
          <name>ListPath</name>
          <state>_build</state>
        </option>
        <option>
          <name>Variant</name>
          <version>20</version>
          <state>34</state>
        </option>
        <option>
          <name>GEndianMode</name>
          <state>0</state>
        </option>
        <option>
          <name>Input variant</name>
          <version>3</version>
          <state>1</state>
        </option>
        <option>
          <name>Input description</name>
          <state>Full formatting.</state>
        </option>
        <option>
          <name>Output variant</name>
          <version>2</version>
          <state>1</state>
        </option>
        <option>
          <name>Output description</name>
          <state>Full formatting.</state>
        </option>
        <option>
          <name>GOutputBinary</name>
          <state>0</state>
        </option>
        <option>
          <name>FPU</name>
          <version>2</version>
          <state>5</state>
        </option>
        <option>
          <name>OGCoreOrChip</name>
          <state>1</state>
        </option>
        <option>
          <name>GRuntimeLibSelect</name>
          <version>0</version>
          <state>2</state>
        </option>
        <option>
          <name>GRuntimeLibSelectSlave</name>
          <version>0</version>
          <state>2</state>
        </option>
        <option>
          <name>RTDescription</name>
          <state>Use the full configuration of the C/C++ runtime library. Full locale interface, C locale, file descriptor support, multibytes in printf and scanf, and hex floats in strtod.</state>
        </option>
        <option>
          <name>OGProductVersion</name>
          <state>6.10.3.52260</state>
        </option>
        <option>
          <name>OGLastSavedByProductVersion</name>
          <state>7.20.2.7418</state>
        </option>
        <option>
          <name>GeneralEnableMisra</name>
          <state>0</state>
        </option>
        <option>
          <name>GeneralMisraVerbose</name>
          <state>0</state>
        </option>
        <option>
          <name>OGChipSelectEditMenu</name>
          <state>nRF52811_xxaa	nRF52811_xxAA</state>
        </option>
        <option>
          <name>GenLowLevelInterface</name>
          <state>0</state>
        </option>
        <option>
          <name>GEndianModeBE</name>
          <state>1</state>
        </option>
        <option>
          <name>OGBufferedTerminalOutput</name>
          <state>0</state>
        </option>
        <option>
          <name>GenStdoutInterface</name>
          <state>0</state>
        </option>
        <option>
          <name>GeneralMisraRules98</name>
          <version>0</version>
          <state>1000111110110101101110011100111111101110011011000101110111101101100111111111111100110011111001110111001111111111111111111111111</state>
        </option>
        <option>
          <name>GeneralMisraVer</name>
          <state>0</state>
        </option>
        <option>
          <name>GeneralMisraRules04</name>
          <version>0</version>
          <state>111101110010111111111000110111111111111111111111111110010111101111010101111111111111111111111111101111111011111001111011111011111111111111111</state>
        </option>
        <option>
          <name>RTConfigPath2</name>
          <state>$TOOLKIT_DIR$\INC\c\DLib_Config_Full.h</state>
        </option>
        <option>
          <name>GFPUCoreSlave</name>
          <version>20</version>
          <state>39</state>
        </option>
        <option>
          <name>GBECoreSlave</name>
          <version>20</version>
          <state>39</state>
        </option>
        <option>
          <name>OGUseCmsis</name>
          <state>0</state>
        </option>
        <option>
          <name>OGUseCmsisDspLib</name>
          <state>0</state>
        </option>
        <option>
          <name>GRuntimeLibThreads</name>
          <state>0</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>ICCARM</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>31</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>0</debug>
        <option>
          <name>CCGuardCalls</name>
          <state>1</state>
        </option>
        <option>
          <name>CCOptimizationNoSizeConstraints</name>
          <state>0</state>
        </option>
        <option>
          <name>CCDefines</name>
          <state>BLE_STACK_SUPPORT_REQD</state>
          <state>BOARD_PCA10056</state>
          <state>CONFIG_GPIO_AS_PINRESET</state>
          <state>DEVELOP_IN_NRF52840</state>
          <state>FLOAT_ABI_SOFT</state>
          <state>NRF52811_XXAA</state>
          <state>NRFX_COREDEP_DELAY_US_LOOP_CYCLES=3</state>
          <state>NRF_DFU_SETTINGS_VERSION=2</state>
          <state>NRF_DFU_SVCI_ENABLED</state>
          <state>NRF_SD_BLE_API_VERSION=7</state>
          <state>S112</state>
          <state>SOFTDEVICE_PRESENT</state>
          <state>SVC_INTERFACE_CALL_AS_NORMAL_FUNCTION</state>
          <state>uECC_ENABLE_VLI_API=0</state>
          <state>uECC_OPTIMIZATION_LEVEL=3</state>
          <state>uECC_SQUARE_FUNC=0</state>
          <state>uECC_SUPPORT_COMPRESSED_POINT=0</state>
          <state>uECC_VLI_NATIVE_LITTLE_ENDIAN=1</state>
        </option>
        <option>
          <name>CCPreprocFile</name>
          <state>0</state>
        </option>
        <option>
          <name>CCPreprocComments</name>
          <state>0</state>
        </option>
        <option>
          <name>CCPreprocLine</name>
          <state>0</state>
        </option>
        <option>
          <name>CCListCFile</name>
          <state>0</state>
        </option>
        <option>
          <name>CCListCMnemonics</name>
          <state>0</state>
        </option>
        <option>
          <name>CCListCMessages</name>
          <state>0</state>
        </option>
        <option>
          <name>CCListAssFile</name>
          <state>0</state>
        </option>
        <option>
          <name>CCListAssSource</name>
          <state>0</state>
        </option>
        <option>
          <name>CCEnableRemarks</name>
          <state>0</state>
        </option>
        <option>
          <name>CCDiagSuppress</name>
          <state></state>
        </option>
        <option>
          <name>CCDiagRemark</name>
          <state></state>
        </option>
        <option>
          <name>CCDiagWarning</name>
          <state></state>
        </option>
        <option>
          <name>CCDiagError</name>
          <state></state>
        </option>
        <option>
          <name>CCObjPrefix</name>
          <state>1</state>
        </option>
        <option>
          <name>CCAllowList</name>
          <version>1</version>
          <state>11111110</state>
        </option>
        <option>
          <name>CCDebugInfo</name>
          <state>1</state>
        </option>
        <option>
          <name>IEndianMode</name>
          <state>1</state>
        </option>
        <option>
          <name>IProcessor</name>
          <state>1</state>
        </option>
        <option>
          <name>IExtraOptionsCheck</name>
          <state>1</state>
        </option>
        <option>
          <name>IExtraOptions</name>
          <state>--diag_suppress Pe111</state>
        </option>
        <option>
          <name>CCLangConformance</name>
          <state>0</state>
        </option>
        <option>
          <name>CCSignedPlainChar</name>
          <state>1</state>
        </option>
        <option>
          <name>CCRequirePrototypes</name>
          <state>0</state>
        </option>
        <option>
          <name>CCMultibyteSupport</name>
          <state>0</state>
        </option>
        <option>
          <name>CCDiagWarnAreErr</name>
          <state>1</state>
        </option>
        <option>
          <name>CCCompilerRuntimeInfo</name>
          <state>0</state>
        </option>
        <option>
          <name>IFpuProcessor</name>
          <state>1</state>
        </option>
        <option>
          <name>OutputFile</name>
          <state>$FILE_BNAME$.o</state>
        </option>
        <option>
          <name>CCLibConfigHeader</name>
          <state>1</state>
        </option>
        <option>
          <name>PreInclude</name>
          <state></state>
        </option>
        <option>
          <name>CompilerMisraOverride</name>
          <state>0</state>
        </option>
        <option>
          <name>CCIncludePath2</name>
          <state>$PROJ_DIR$\..\..\config</state>
          <state>$PROJ_DIR$\..\..\..\..\..\components\ble\common</state>
          <state>$PROJ_DIR$\..\..\..\..\..\components\boards</state>
          <state>$PROJ_DIR$\..\..\..\..\..\components\libraries\atomic</state>
          <state>$PROJ_DIR$\..\..\..\..\..\components\libraries\atomic_fifo</state>
          <state>$PROJ_DIR$\..\..\..\..\..\components\libraries\balloc</state>
          <state>$PROJ_DIR$\..\..\..\..\..\components\libraries\bootloader</state>
          <state>$PROJ_DIR$\..\..\..\..\..\components\libraries\bootloader\ble_dfu</state>
          <state>$PROJ_DIR$\..\..\..\..\..\components\libraries\bootloader\dfu</state>
          <state>$PROJ_DIR$\..\..\..\..\..\components\libraries\crc32</state>
          <state>$PROJ_DIR$\..\..\..\..\..\components\libraries\crypto</state>
          <state>$PROJ_DIR$\..\..\..\..\..\components\libraries\crypto\backend\cc310</state>
          <state>$PROJ_DIR$\..\..\..\..\..\components\libraries\crypto\backend\cc310_bl</state>
          <state>$PROJ_DIR$\..\..\..\..\..\components\libraries\crypto\backend\cifra</state>
          <state>$PROJ_DIR$\..\..\..\..\..\components\libraries\crypto\backend\mbedtls</state>
          <state>$PROJ_DIR$\..\..\..\..\..\components\libraries\crypto\backend\micro_ecc</state>
          <state>$PROJ_DIR$\..\..\..\..\..\components\libraries\crypto\backend\nrf_hw</state>
          <state>$PROJ_DIR$\..\..\..\..\..\components\libraries\crypto\backend\nrf_sw</state>
          <state>$PROJ_DIR$\..\..\..\..\..\components\libraries\crypto\backend\oberon</state>
          <state>$PROJ_DIR$\..\..\..\..\..\components\libraries\crypto\backend\optiga</state>
          <state>$PROJ_DIR$\..\..\..\..\..\components\libraries\delay</state>
          <state>$PROJ_DIR$\..\..\..\..\..\components\libraries\experimental_section_vars</state>
          <state>$PROJ_DIR$\..\..\..\..\..\components\libraries\fstorage</state>
          <state>$PROJ_DIR$\..\..\..\..\..\components\libraries\log</state>
          <state>$PROJ_DIR$\..\..\..\..\..\components\libraries\log\src</state>
          <state>$PROJ_DIR$\..\..\..\..\..\components\libraries\mem_manager</state>
          <state>$PROJ_DIR$\..\..\..\..\..\components\libraries\memobj</state>
          <state>$PROJ_DIR$\..\..\..\..\..\components\libraries\queue</state>
          <state>$PROJ_DIR$\..\..\..\..\..\components\libraries\ringbuf</state>
          <state>$PROJ_DIR$\..\..\..\..\..\components\libraries\scheduler</state>
          <state>$PROJ_DIR$\..\..\..\..\..\components\libraries\sha256</state>
          <state>$PROJ_DIR$\..\..\..\..\..\components\libraries\stack_info</state>
          <state>$PROJ_DIR$\..\..\..\..\..\components\libraries\strerror</state>
          <state>$PROJ_DIR$\..\..\..\..\..\components\libraries\svc</state>
          <state>$PROJ_DIR$\..\..\..\..\..\components\libraries\util</state>
          <state>$PROJ_DIR$\..\..\..\..\..\components\softdevice\common</state>
          <state>$PROJ_DIR$\..\..\..\..\..\components\softdevice\s112\headers</state>
          <state>$PROJ_DIR$\..\..\..\..\..\components\softdevice\s112\headers\nrf52</state>
          <state>$PROJ_DIR$\..\..\..\..\..\components\toolchain\cmsis\include</state>
          <state>$PROJ_DIR$\..\..</state>
          <state>$PROJ_DIR$\..\..\..\..\..\external\micro-ecc\micro-ecc</state>
          <state>$PROJ_DIR$\..\..\..\..\..\external\nano-pb</state>
          <state>$PROJ_DIR$\..\..\..\..\..\external\nrf_oberon</state>
          <state>$PROJ_DIR$\..\..\..\..\..\external\nrf_oberon\include</state>
          <state>$PROJ_DIR$\..\..\..\..\..\integration\nrfx</state>
          <state>$PROJ_DIR$\..\..\..\..\..\modules\nrfx</state>
          <state>$PROJ_DIR$\..\..\..\..\..\modules\nrfx\hal</state>
          <state>$PROJ_DIR$\..\..\..\..\..\modules\nrfx\mdk</state>
          <state>$PROJ_DIR$\..\config</state>
        </option>
        <option>
          <name>CCStdIncCheck</name>
          <state>0</state>
        </option>
        <option>
          <name>CCCodeSection</name>
          <state>.text</state>
        </option>
        <option>
          <name>IInterwork2</name>
          <state>0</state>
        </option>
        <option>
          <name>IProcessorMode2</name>
          <state>1</state>
        </option>
        <option>
          <name>CCOptLevel</name>
          <state>3</state>
        </option>
        <option>
          <name>CCOptStrategy</name>
          <version>0</version>
          <state>1</state>
        </option>
        <option>
          <name>CCOptLevelSlave</name>
          <state>3</state>
        </option>
        <option>
          <name>CompilerMisraRules98</name>
          <version>0</version>
          <state>1000111110110101101110011100111111101110011011000101110111101101100111111111111100110011111001110111001111111111111111111111111</state>
        </option>
        <option>
          <name>CompilerMisraRules04</name>
          <version>0</version>
          <state>111101110010111111111000110111111111111111111111111110010111101111010101111111111111111111111111101111111011111001111011111011111111111111111</state>
        </option>
        <option>
          <name>CCPosIndRopi</name>
          <state>0</state>
        </option>
        <option>
          <name>CCPosIndRwpi</name>
          <state>0</state>
        </option>
        <option>
          <name>CCPosIndNoDynInit</name>
          <state>0</state>
        </option>
        <option>
          <name>IccLang</name>
          <state>0</state>
        </option>
        <option>
          <name>IccCDialect</name>
          <state>1</state>
        </option>
        <option>
          <name>IccAllowVLA</name>
          <state>0</state>
        </option>
        <option>
          <name>IccCppDialect</name>
          <state>1</state>
        </option>
        <option>
          <name>IccExceptions</name>
          <state>1</state>
        </option>
        <option>
          <name>IccRTTI</name>
          <state>1</state>
        </option>
        <option>
          <name>IccStaticDestr</name>
          <state>1</state>
        </option>
        <option>
          <name>IccCppInlineSemantics</name>
          <state>0</state>
        </option>
        <option>
          <name>IccCmsis</name>
          <state>1</state>
        </option>
        <option>
          <name>IccFloatSemantics</name>
          <state>0</state>
        </option>
        <option>
          <name>CCNoLiteralPool</name>
          <state>0</state>
        </option>
        <option>
          <name>CCOptStrategySlave</name>
          <version>0</version>
          <state>1</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>AARM</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>9</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>0</debug>
        <option>
          <name>AObjPrefix</name>
          <state>1</state>
        </option>
        <option>
          <name>AEndian</name>
          <state>1</state>
        </option>
        <option>
          <name>ACaseSensitivity</name>
          <state>1</state>
        </option>
        <option>
          <name>MacroChars</name>
          <version>0</version>
          <state>0</state>
        </option>
        <option>
          <name>AWarnEnable</name>
          <state>0</state>
        </option>
        <option>
          <name>AWarnWhat</name>
          <state>0</state>
        </option>
        <option>
          <name>AWarnOne</name>
          <state></state>
        </option>
        <option>
          <name>AWarnRange1</name>
          <state></state>
        </option>
        <option>
          <name>AWarnRange2</name>
          <state></state>
        </option>
        <option>
          <name>ADebug</name>
          <state></state>
        </option>
        <option>
          <name>AltRegisterNames</name>
          <state>0</state>
        </option>
        <option>
      <name>ADefines</name>
          <state>BLE_STACK_SUPPORT_REQD</state>
          <state>BOARD_PCA10056</state>
          <state>CONFIG_GPIO_AS_PINRESET</state>
          <state>DEVELOP_IN_NRF52840</state>
          <state>FLOAT_ABI_SOFT</state>
          <state>NRF52811_XXAA</state>
          <state>NRFX_COREDEP_DELAY_US_LOOP_CYCLES=3</state>
          <state>NRF_DFU_SETTINGS_VERSION=2</state>
          <state>NRF_DFU_SVCI_ENABLED</state>
          <state>NRF_SD_BLE_API_VERSION=7</state>
          <state>S112</state>
          <state>SOFTDEVICE_PRESENT</state>
          <state>SVC_INTERFACE_CALL_AS_NORMAL_FUNCTION</state>
          <state>uECC_ENABLE_VLI_API=0</state>
          <state>uECC_OPTIMIZATION_LEVEL=3</state>
          <state>uECC_SQUARE_FUNC=0</state>
          <state>uECC_SUPPORT_COMPRESSED_POINT=0</state>
          <state>uECC_VLI_NATIVE_LITTLE_ENDIAN=1</state>
        </option>
        <option>
          <name>AList</name>
          <state>0</state>
        </option>
        <option>
          <name>AListHeader</name>
          <state>1</state>
        </option>
        <option>
          <name>AListing</name>
          <state>1</state>
        </option>
        <option>
          <name>Includes</name>
          <state>0</state>
        </option>
        <option>
          <name>MacDefs</name>
          <state>0</state>
        </option>
        <option>
          <name>MacExps</name>
          <state>1</state>
        </option>
        <option>
          <name>MacExec</name>
          <state>0</state>
        </option>
        <option>
          <name>OnlyAssed</name>
          <state>0</state>
        </option>
        <option>
          <name>MultiLine</name>
          <state>0</state>
        </option>
        <option>
          <name>PageLengthCheck</name>
          <state>0</state>
        </option>
        <option>
          <name>PageLength</name>
          <state>80</state>
        </option>
        <option>
          <name>TabSpacing</name>
          <state>8</state>
        </option>
        <option>
          <name>AXRef</name>
          <state>0</state>
        </option>
        <option>
          <name>AXRefDefines</name>
          <state>0</state>
        </option>
        <option>
          <name>AXRefInternal</name>
          <state>0</state>
        </option>
        <option>
          <name>AXRefDual</name>
          <state>0</state>
        </option>
        <option>
          <name>AProcessor</name>
          <state>1</state>
        </option>
        <option>
          <name>AFpuProcessor</name>
          <state>1</state>
        </option>
        <option>
          <name>AOutputFile</name>
          <state>$FILE_BNAME$.o</state>
        </option>
        <option>
          <name>AMultibyteSupport</name>
          <state>0</state>
        </option>
        <option>
          <name>ALimitErrorsCheck</name>
          <state>0</state>
        </option>
        <option>
          <name>ALimitErrorsEdit</name>
          <state>100</state>
        </option>
        <option>
          <name>AIgnoreStdInclude</name>
          <state>0</state>
        </option>
        <option>
          <name>AUserIncludes</name>
          <state>$PROJ_DIR$\..\..\config</state>
          <state>$PROJ_DIR$\..\..\..\..\..\components\ble\common</state>
          <state>$PROJ_DIR$\..\..\..\..\..\components\boards</state>
          <state>$PROJ_DIR$\..\..\..\..\..\components\libraries\atomic</state>
          <state>$PROJ_DIR$\..\..\..\..\..\components\libraries\atomic_fifo</state>
          <state>$PROJ_DIR$\..\..\..\..\..\components\libraries\balloc</state>
          <state>$PROJ_DIR$\..\..\..\..\..\components\libraries\bootloader</state>
          <state>$PROJ_DIR$\..\..\..\..\..\components\libraries\bootloader\ble_dfu</state>
          <state>$PROJ_DIR$\..\..\..\..\..\components\libraries\bootloader\dfu</state>
          <state>$PROJ_DIR$\..\..\..\..\..\components\libraries\crc32</state>
          <state>$PROJ_DIR$\..\..\..\..\..\components\libraries\crypto</state>
          <state>$PROJ_DIR$\..\..\..\..\..\components\libraries\crypto\backend\cc310</state>
          <state>$PROJ_DIR$\..\..\..\..\..\components\libraries\crypto\backend\cc310_bl</state>
          <state>$PROJ_DIR$\..\..\..\..\..\components\libraries\crypto\backend\cifra</state>
          <state>$PROJ_DIR$\..\..\..\..\..\components\libraries\crypto\backend\mbedtls</state>
          <state>$PROJ_DIR$\..\..\..\..\..\components\libraries\crypto\backend\micro_ecc</state>
          <state>$PROJ_DIR$\..\..\..\..\..\components\libraries\crypto\backend\nrf_hw</state>
          <state>$PROJ_DIR$\..\..\..\..\..\components\libraries\crypto\backend\nrf_sw</state>
          <state>$PROJ_DIR$\..\..\..\..\..\components\libraries\crypto\backend\oberon</state>
          <state>$PROJ_DIR$\..\..\..\..\..\components\libraries\crypto\backend\optiga</state>
          <state>$PROJ_DIR$\..\..\..\..\..\components\libraries\delay</state>
          <state>$PROJ_DIR$\..\..\..\..\..\components\libraries\experimental_section_vars</state>
          <state>$PROJ_DIR$\..\..\..\..\..\components\libraries\fstorage</state>
          <state>$PROJ_DIR$\..\..\..\..\..\components\libraries\log</state>
          <state>$PROJ_DIR$\..\..\..\..\..\components\libraries\log\src</state>
          <state>$PROJ_DIR$\..\..\..\..\..\components\libraries\mem_manager</state>
          <state>$PROJ_DIR$\..\..\..\..\..\components\libraries\memobj</state>
          <state>$PROJ_DIR$\..\..\..\..\..\components\libraries\queue</state>
          <state>$PROJ_DIR$\..\..\..\..\..\components\libraries\ringbuf</state>
          <state>$PROJ_DIR$\..\..\..\..\..\components\libraries\scheduler</state>
          <state>$PROJ_DIR$\..\..\..\..\..\components\libraries\sha256</state>
          <state>$PROJ_DIR$\..\..\..\..\..\components\libraries\stack_info</state>
          <state>$PROJ_DIR$\..\..\..\..\..\components\libraries\strerror</state>
          <state>$PROJ_DIR$\..\..\..\..\..\components\libraries\svc</state>
          <state>$PROJ_DIR$\..\..\..\..\..\components\libraries\util</state>
          <state>$PROJ_DIR$\..\..\..\..\..\components\softdevice\common</state>
          <state>$PROJ_DIR$\..\..\..\..\..\components\softdevice\s112\headers</state>
          <state>$PROJ_DIR$\..\..\..\..\..\components\softdevice\s112\headers\nrf52</state>
          <state>$PROJ_DIR$\..\..\..\..\..\components\toolchain\cmsis\include</state>
          <state>$PROJ_DIR$\..\..</state>
          <state>$PROJ_DIR$\..\..\..\..\..\external\micro-ecc\micro-ecc</state>
          <state>$PROJ_DIR$\..\..\..\..\..\external\nano-pb</state>
          <state>$PROJ_DIR$\..\..\..\..\..\external\nrf_oberon</state>
          <state>$PROJ_DIR$\..\..\..\..\..\external\nrf_oberon\include</state>
          <state>$PROJ_DIR$\..\..\..\..\..\integration\nrfx</state>
          <state>$PROJ_DIR$\..\..\..\..\..\modules\nrfx</state>
          <state>$PROJ_DIR$\..\..\..\..\..\modules\nrfx\hal</state>
          <state>$PROJ_DIR$\..\..\..\..\..\modules\nrfx\mdk</state>
          <state>$PROJ_DIR$\..\config</state>
        </option>
        <option>
          <name>AExtraOptionsCheckV2</name>
          <state>0</state>
        </option>
        <option>
          <name>AExtraOptionsV2</name>
          <state></state>
        </option>
        <option>
          <name>AsmNoLiteralPool</name>
          <state>0</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>OBJCOPY</name>
      <archiveVersion>0</archiveVersion>
      <data>
        <version>1</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>0</debug>
        <option>
          <name>OOCOutputFormat</name>
          <version>2</version>
          <state>1</state>
        </option>
        <option>
          <name>OCOutputOverride</name>
          <state>1</state>
        </option>
        <option>
          <name>OOCOutputFile</name>
          <state>secure_bootloader_ble_s112_pca10056e.hex</state>
        </option>
        <option>
          <name>OOCCommandLineProducer</name>
          <state>1</state>
        </option>
        <option>
          <name>OOCObjCopyEnable</name>
          <state>1</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>CUSTOM</name>
      <archiveVersion>3</archiveVersion>
      <data>
        <extensions></extensions>
        <cmdline></cmdline>
      </data>
    </settings>
    <settings>
      <name>BICOMP</name>
      <archiveVersion>0</archiveVersion>
      <data/>
    </settings>
    <settings>
      <name>BUILDACTION</name>
      <archiveVersion>1</archiveVersion>
      <data>
        <prebuild></prebuild>
        <postbuild></postbuild>
      </data>
    </settings>
    <settings>
      <name>ILINK</name>
      <archiveVersion>0</archiveVersion>
      <data>
        <version>16</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>0</debug>
        <option>
          <name>IlinkLibIOConfig</name>
          <state>1</state>
        </option>
        <option>
          <name>XLinkMisraHandler</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkInputFileSlave</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkOutputFile</name>
          <state>secure_bootloader_ble_s112_pca10056e.out</state>
        </option>
        <option>
          <name>IlinkDebugInfoEnable</name>
          <state>1</state>
        </option>
        <option>
          <name>IlinkKeepSymbols</name>
          <state></state>
        </option>
        <option>
          <name>IlinkRawBinaryFile</name>
          <state></state>
        </option>
        <option>
          <name>IlinkRawBinarySymbol</name>
          <state></state>
        </option>
        <option>
          <name>IlinkRawBinarySegment</name>
          <state></state>
        </option>
        <option>
          <name>IlinkRawBinaryAlign</name>
          <state></state>
        </option>
        <option>
          <name>IlinkDefines</name>
          <state></state>
        </option>
        <option>
          <name>IlinkConfigDefines</name>
          <state></state>
        </option>
        <option>
          <name>IlinkMapFile</name>
          <state>1</state>
        </option>
        <option>
          <name>IlinkLogFile</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkLogInitialization</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkLogModule</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkLogSection</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkLogVeneer</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkIcfOverride</name>
          <state>1</state>
        </option>
        <option>
          <name>IlinkIcfFile</name>
      <state>$PROJ_DIR$\secure_bootloader_iar_nRF5x.icf</state>
        </option>
        <option>
          <name>IlinkIcfFileSlave</name>
          <state></state>
        </option>
        <option>
          <name>IlinkEnableRemarks</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkSuppressDiags</name>
          <state></state>
        </option>
        <option>
          <name>IlinkTreatAsRem</name>
          <state></state>
        </option>
        <option>
          <name>IlinkTreatAsWarn</name>
          <state></state>
        </option>
        <option>
          <name>IlinkTreatAsErr</name>
          <state></state>
        </option>
        <option>
          <name>IlinkWarningsAreErrors</name>
          <state>1</state>
        </option>
        <option>
          <name>IlinkUseExtraOptions</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkExtraOptions</name>
          <state></state>
        </option>
        <option>
          <name>IlinkLowLevelInterfaceSlave</name>
          <state>1</state>
        </option>
        <option>
          <name>IlinkAutoLibEnable</name>
          <state>1</state>
        </option>
        <option>
          <name>IlinkAdditionalLibs</name>
          <state></state>
        </option>
        <option>
          <name>IlinkOverrideProgramEntryLabel</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkProgramEntryLabelSelect</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkProgramEntryLabel</name>
          <state>__iar_program_start</state>
        </option>
        <option>
          <name>DoFill</name>
          <state>0</state>
        </option>
        <option>
          <name>FillerByte</name>
          <state>0xFF</state>
        </option>
        <option>
          <name>FillerStart</name>
          <state>0x0</state>
        </option>
        <option>
          <name>FillerEnd</name>
          <state>0x0</state>
        </option>
        <option>
          <name>CrcSize</name>
          <version>0</version>
          <state>1</state>
        </option>
        <option>
          <name>CrcAlign</name>
          <state>1</state>
        </option>
        <option>
          <name>CrcPoly</name>
          <state>0x11021</state>
        </option>
        <option>
          <name>CrcCompl</name>
          <version>0</version>
          <state>0</state>
        </option>
        <option>
          <name>CrcBitOrder</name>
          <version>0</version>
          <state>0</state>
        </option>
        <option>
          <name>CrcInitialValue</name>
          <state>0x0</state>
        </option>
        <option>
          <name>DoCrc</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkBE8Slave</name>
          <state>1</state>
        </option>
        <option>
          <name>IlinkBufferedTerminalOutput</name>
          <state>1</state>
        </option>
        <option>
          <name>IlinkStdoutInterfaceSlave</name>
          <state>1</state>
        </option>
        <option>
          <name>CrcFullSize</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkIElfToolPostProcess</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkLogAutoLibSelect</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkLogRedirSymbols</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkLogUnusedFragments</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkCrcReverseByteOrder</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkCrcUseAsInput</name>
          <state>1</state>
        </option>
        <option>
          <name>IlinkOptInline</name>
          <state>1</state>
        </option>
        <option>
          <name>IlinkOptExceptionsAllow</name>
          <state>1</state>
        </option>
        <option>
          <name>IlinkOptExceptionsForce</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkCmsis</name>
          <state>1</state>
        </option>
        <option>
          <name>IlinkOptMergeDuplSections</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkOptUseVfe</name>
          <state>1</state>
        </option>
        <option>
          <name>IlinkOptForceVfe</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkStackAnalysisEnable</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkStackControlFile</name>
          <state></state>
        </option>
        <option>
          <name>IlinkStackCallGraphFile</name>
          <state></state>
        </option>
        <option>
          <name>CrcAlgorithm</name>
          <version>0</version>
          <state>1</state>
        </option>
        <option>
          <name>CrcUnitSize</name>
          <version>0</version>
          <state>0</state>
        </option>
        <option>
          <name>IlinkThreadsSlave</name>
          <state>1</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>IARCHIVE</name>
      <archiveVersion>0</archiveVersion>
      <data>
        <version>0</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>0</debug>
        <option>
          <name>IarchiveInputs</name>
          <state></state>
        </option>
        <option>
          <name>IarchiveOverride</name>
          <state>0</state>
        </option>
        <option>
          <name>IarchiveOutput</name>
          <state>###Unitialized###</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>BILINK</name>
      <archiveVersion>0</archiveVersion>
      <data/>
    </settings>
  </configuration>  <group>
  <name>nRF_Libraries</name>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\components\libraries\util\app_error_weak.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\components\libraries\scheduler\app_scheduler.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\components\libraries\util\app_util_platform.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\components\libraries\crc32\crc32.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\components\libraries\mem_manager\mem_manager.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\components\libraries\util\nrf_assert.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\components\libraries\atomic_fifo\nrf_atfifo.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\components\libraries\atomic\nrf_atomic.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\components\libraries\balloc\nrf_balloc.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\components\libraries\fstorage\nrf_fstorage.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\components\libraries\fstorage\nrf_fstorage_nvmc.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\components\libraries\fstorage\nrf_fstorage_sd.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\components\libraries\queue\nrf_queue.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\components\libraries\ringbuf\nrf_ringbuf.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\components\libraries\experimental_section_vars\nrf_section_iter.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\components\libraries\strerror\nrf_strerror.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\components\libraries\sha256\sha256.c</name>    </file>  </group>  <group>
  <name>nRF_Crypto backend uECC</name>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\components\libraries\crypto\backend\micro_ecc\micro_ecc_backend_ecc.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\components\libraries\crypto\backend\micro_ecc\micro_ecc_backend_ecdh.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\components\libraries\crypto\backend\micro_ecc\micro_ecc_backend_ecdsa.c</name>    </file>  </group>  <group>
  <name>nano-pb</name>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\external\nano-pb\pb_common.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\external\nano-pb\pb_decode.c</name>    </file>  </group>  <group>
  <name>Board Definition</name>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\components\boards\boards.c</name>    </file>  </group>  <group>
  <name>nRF_Drivers</name>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\modules\nrfx\hal\nrf_nvmc.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\modules\nrfx\soc\nrfx_atomic.c</name>    </file>  </group>  <group>
  <name>nRF_Oberon_Crypto</name>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\external\nrf_oberon\lib\cortex-m4\soft-float\short-wchar\liboberon_3.0.5.a</name>    </file>  </group>  <group>
  <name>nRF_Crypto</name>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\components\libraries\crypto\nrf_crypto_ecc.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\components\libraries\crypto\nrf_crypto_ecdsa.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\components\libraries\crypto\nrf_crypto_hash.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\components\libraries\crypto\nrf_crypto_init.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\components\libraries\crypto\nrf_crypto_shared.c</name>    </file>  </group>  <group>
  <name>Application</name>    <file>
    <name>$PROJ_DIR$\..\..\..\dfu_public_key.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\main.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\config\sdk_config.h</name>    </file>  </group>  <group>
  <name>nRF_micro-ecc</name>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\external\micro-ecc\nrf52nf_iar\armgcc\micro_ecc_lib_nrf52.a</name>    </file>  </group>  <group>
  <name>nRF_BLE</name>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\components\ble\common\ble_srv_common.c</name>    </file>  </group>  <group>
  <name>nRF_Bootloader</name>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\components\libraries\bootloader\nrf_bootloader.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\components\libraries\bootloader\nrf_bootloader_app_start.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\components\libraries\bootloader\nrf_bootloader_app_start_final.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\components\libraries\bootloader\nrf_bootloader_dfu_timers.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\components\libraries\bootloader\nrf_bootloader_fw_activation.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\components\libraries\bootloader\nrf_bootloader_info.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\components\libraries\bootloader\nrf_bootloader_wdt.c</name>    </file>  </group>  <group>
  <name>None</name>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\modules\nrfx\mdk\iar_startup_nrf52811.s</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\modules\nrfx\mdk\system_nrf52811.c</name>    </file>  </group>  <group>
  <name>nRF_Crypto backend nRF sw</name>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\components\libraries\crypto\backend\nrf_sw\nrf_sw_backend_hash.c</name>    </file>  </group>  <group>
  <name>nRF_DFU</name>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\components\libraries\bootloader\dfu\dfu-cc.pb.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\components\libraries\bootloader\dfu\nrf_dfu.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\components\libraries\bootloader\ble_dfu\nrf_dfu_ble.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\components\libraries\bootloader\dfu\nrf_dfu_flash.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\components\libraries\bootloader\dfu\nrf_dfu_handling_error.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\components\libraries\bootloader\dfu\nrf_dfu_mbr.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\components\libraries\bootloader\dfu\nrf_dfu_req_handler.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\components\libraries\bootloader\dfu\nrf_dfu_settings.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\components\libraries\bootloader\dfu\nrf_dfu_settings_svci.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\components\libraries\bootloader\dfu\nrf_dfu_transport.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\components\libraries\bootloader\dfu\nrf_dfu_utils.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\components\libraries\bootloader\dfu\nrf_dfu_validation.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\components\libraries\bootloader\dfu\nrf_dfu_ver_validation.c</name>    </file>  </group>  <group>
  <name>nRF_SVC</name>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\components\libraries\bootloader\dfu\nrf_dfu_svci.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\components\libraries\bootloader\dfu\nrf_dfu_svci_handler.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\components\libraries\svc\nrf_svc_handler.c</name>    </file>  </group>  <group>
  <name>nRF_SoftDevice</name>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\components\softdevice\common\nrf_sdh.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\components\softdevice\common\nrf_sdh_ble.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\components\softdevice\common\nrf_sdh_soc.c</name>    </file>  </group>  <group>
  <name>nRF_Crypto backend Oberon</name>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\components\libraries\crypto\backend\oberon\oberon_backend_chacha_poly_aead.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\components\libraries\crypto\backend\oberon\oberon_backend_ecc.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\components\libraries\crypto\backend\oberon\oberon_backend_ecdh.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\components\libraries\crypto\backend\oberon\oberon_backend_ecdsa.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\components\libraries\crypto\backend\oberon\oberon_backend_eddsa.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\components\libraries\crypto\backend\oberon\oberon_backend_hash.c</name>    </file>    <file>
    <name>$PROJ_DIR$\..\..\..\..\..\components\libraries\crypto\backend\oberon\oberon_backend_hmac.c</name>    </file>  </group></project>


