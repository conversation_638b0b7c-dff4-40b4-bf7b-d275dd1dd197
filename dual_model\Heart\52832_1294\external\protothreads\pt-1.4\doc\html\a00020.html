<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=iso-8859-1">
<title>The Protothreads Library 1.4: lc.h Source File</title>
<link href="doxygen.css" rel="stylesheet" type="text/css">
<link href="tabs.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.4.6 -->
<div class="tabs">
  <ul>
    <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
    <li><a href="modules.html"><span>Modules</span></a></li>
    <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    <li id="current"><a href="files.html"><span>Files</span></a></li>
  </ul></div>
<div class="tabs">
  <ul>
    <li><a href="files.html"><span>File&nbsp;List</span></a></li>
    <li><a href="globals.html"><span>Globals</span></a></li>
  </ul></div>
<h1>lc.h</h1><a href="a00011.html">Go to the documentation of this file.</a><div class="fragment"><pre class="fragment"><a name="l00001"></a>00001 <span class="comment">/*</span>
<a name="l00002"></a>00002 <span class="comment"> * Copyright (c) 2004-2005, Swedish Institute of Computer Science.</span>
<a name="l00003"></a>00003 <span class="comment"> * All rights reserved. </span>
<a name="l00004"></a>00004 <span class="comment"> *</span>
<a name="l00005"></a>00005 <span class="comment"> * Redistribution and use in source and binary forms, with or without </span>
<a name="l00006"></a>00006 <span class="comment"> * modification, are permitted provided that the following conditions </span>
<a name="l00007"></a>00007 <span class="comment"> * are met: </span>
<a name="l00008"></a>00008 <span class="comment"> * 1. Redistributions of source code must retain the above copyright </span>
<a name="l00009"></a>00009 <span class="comment"> *    notice, this list of conditions and the following disclaimer. </span>
<a name="l00010"></a>00010 <span class="comment"> * 2. Redistributions in binary form must reproduce the above copyright </span>
<a name="l00011"></a>00011 <span class="comment"> *    notice, this list of conditions and the following disclaimer in the </span>
<a name="l00012"></a>00012 <span class="comment"> *    documentation and/or other materials provided with the distribution. </span>
<a name="l00013"></a>00013 <span class="comment"> * 3. Neither the name of the Institute nor the names of its contributors </span>
<a name="l00014"></a>00014 <span class="comment"> *    may be used to endorse or promote products derived from this software </span>
<a name="l00015"></a>00015 <span class="comment"> *    without specific prior written permission. </span>
<a name="l00016"></a>00016 <span class="comment"> *</span>
<a name="l00017"></a>00017 <span class="comment"> * THIS SOFTWARE IS PROVIDED BY THE INSTITUTE AND CONTRIBUTORS ``AS IS'' AND </span>
<a name="l00018"></a>00018 <span class="comment"> * ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE </span>
<a name="l00019"></a>00019 <span class="comment"> * IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE </span>
<a name="l00020"></a>00020 <span class="comment"> * ARE DISCLAIMED.  IN NO EVENT SHALL THE INSTITUTE OR CONTRIBUTORS BE LIABLE </span>
<a name="l00021"></a>00021 <span class="comment"> * FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL </span>
<a name="l00022"></a>00022 <span class="comment"> * DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS </span>
<a name="l00023"></a>00023 <span class="comment"> * OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) </span>
<a name="l00024"></a>00024 <span class="comment"> * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT </span>
<a name="l00025"></a>00025 <span class="comment"> * LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY </span>
<a name="l00026"></a>00026 <span class="comment"> * OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF </span>
<a name="l00027"></a>00027 <span class="comment"> * SUCH DAMAGE. </span>
<a name="l00028"></a>00028 <span class="comment"> *</span>
<a name="l00029"></a>00029 <span class="comment"> * This file is part of the protothreads library.</span>
<a name="l00030"></a>00030 <span class="comment"> * </span>
<a name="l00031"></a>00031 <span class="comment"> * Author: Adam Dunkels &lt;<EMAIL>&gt;</span>
<a name="l00032"></a>00032 <span class="comment"> *</span>
<a name="l00033"></a>00033 <span class="comment"> * $Id: lc.h,v 1.2 2005/02/24 10:36:59 adam Exp $</span>
<a name="l00034"></a>00034 <span class="comment"> */</span>
<a name="l00035"></a>00035 <span class="comment"></span>
<a name="l00036"></a>00036 <span class="comment">/**</span>
<a name="l00037"></a>00037 <span class="comment"> * \addtogroup pt</span>
<a name="l00038"></a>00038 <span class="comment"> * @{</span>
<a name="l00039"></a>00039 <span class="comment"> */</span>
<a name="l00040"></a>00040 <span class="comment"></span>
<a name="l00041"></a>00041 <span class="comment">/**</span>
<a name="l00042"></a>00042 <span class="comment"> * \defgroup lc Local continuations</span>
<a name="l00043"></a>00043 <span class="comment"> * @{</span>
<a name="l00044"></a>00044 <span class="comment"> *</span>
<a name="l00045"></a>00045 <span class="comment"> * Local continuations form the basis for implementing protothreads. A</span>
<a name="l00046"></a>00046 <span class="comment"> * local continuation can be &lt;i&gt;set&lt;/i&gt; in a specific function to</span>
<a name="l00047"></a>00047 <span class="comment"> * capture the state of the function. After a local continuation has</span>
<a name="l00048"></a>00048 <span class="comment"> * been set can be &lt;i&gt;resumed&lt;/i&gt; in order to restore the state of the</span>
<a name="l00049"></a>00049 <span class="comment"> * function at the point where the local continuation was set.</span>
<a name="l00050"></a>00050 <span class="comment"> *</span>
<a name="l00051"></a>00051 <span class="comment"> *</span>
<a name="l00052"></a>00052 <span class="comment"> */</span>
<a name="l00053"></a>00053 <span class="comment"></span>
<a name="l00054"></a>00054 <span class="comment">/**</span>
<a name="l00055"></a>00055 <span class="comment"> * \file lc.h</span>
<a name="l00056"></a>00056 <span class="comment"> * Local continuations</span>
<a name="l00057"></a>00057 <span class="comment"> * \author</span>
<a name="l00058"></a>00058 <span class="comment"> * Adam Dunkels &lt;<EMAIL>&gt;</span>
<a name="l00059"></a>00059 <span class="comment"> *</span>
<a name="l00060"></a>00060 <span class="comment"> */</span>
<a name="l00061"></a>00061 
<a name="l00062"></a>00062 <span class="preprocessor">#ifdef DOXYGEN</span>
<a name="l00063"></a>00063 <span class="preprocessor"></span><span class="comment">/**</span>
<a name="l00064"></a>00064 <span class="comment"> * Initialize a local continuation.</span>
<a name="l00065"></a>00065 <span class="comment"> *</span>
<a name="l00066"></a>00066 <span class="comment"> * This operation initializes the local continuation, thereby</span>
<a name="l00067"></a>00067 <span class="comment"> * unsetting any previously set continuation state.</span>
<a name="l00068"></a>00068 <span class="comment"> *</span>
<a name="l00069"></a>00069 <span class="comment"> * \hideinitializer</span>
<a name="l00070"></a>00070 <span class="comment"> */</span>
<a name="l00071"></a><a class="code" href="a00017.html#g9ca9d0fef02b9c5d93bed2834e7aeb76">00071</a> <span class="preprocessor">#define LC_INIT(lc)</span>
<a name="l00072"></a>00072 <span class="preprocessor"></span><span class="comment"></span>
<a name="l00073"></a>00073 <span class="comment">/**</span>
<a name="l00074"></a>00074 <span class="comment"> * Set a local continuation.</span>
<a name="l00075"></a>00075 <span class="comment"> *</span>
<a name="l00076"></a>00076 <span class="comment"> * The set operation saves the state of the function at the point</span>
<a name="l00077"></a>00077 <span class="comment"> * where the operation is executed. As far as the set operation is</span>
<a name="l00078"></a>00078 <span class="comment"> * concerned, the state of the function does &lt;b&gt;not&lt;/b&gt; include the</span>
<a name="l00079"></a>00079 <span class="comment"> * call-stack or local (automatic) variables, but only the program</span>
<a name="l00080"></a>00080 <span class="comment"> * counter and such CPU registers that needs to be saved.</span>
<a name="l00081"></a>00081 <span class="comment"> *</span>
<a name="l00082"></a>00082 <span class="comment"> * \hideinitializer</span>
<a name="l00083"></a>00083 <span class="comment"> */</span>
<a name="l00084"></a><a class="code" href="a00017.html#gfb1d5e671e40464a7a7bda589b5d4341">00084</a> <span class="preprocessor">#define LC_SET(lc)</span>
<a name="l00085"></a>00085 <span class="preprocessor"></span><span class="comment"></span>
<a name="l00086"></a>00086 <span class="comment">/**</span>
<a name="l00087"></a>00087 <span class="comment"> * Resume a local continuation.</span>
<a name="l00088"></a>00088 <span class="comment"> *</span>
<a name="l00089"></a>00089 <span class="comment"> * The resume operation resumes a previously set local continuation, thus</span>
<a name="l00090"></a>00090 <span class="comment"> * restoring the state in which the function was when the local</span>
<a name="l00091"></a>00091 <span class="comment"> * continuation was set. If the local continuation has not been</span>
<a name="l00092"></a>00092 <span class="comment"> * previously set, the resume operation does nothing.</span>
<a name="l00093"></a>00093 <span class="comment"> *</span>
<a name="l00094"></a>00094 <span class="comment"> * \hideinitializer</span>
<a name="l00095"></a>00095 <span class="comment"> */</span>
<a name="l00096"></a><a class="code" href="a00017.html#g33dad6011c98dfeb4e64fee1d6892cb3">00096</a> <span class="preprocessor">#define LC_RESUME(lc)</span>
<a name="l00097"></a>00097 <span class="preprocessor"></span><span class="comment"></span>
<a name="l00098"></a>00098 <span class="comment">/**</span>
<a name="l00099"></a>00099 <span class="comment"> * Mark the end of local continuation usage.</span>
<a name="l00100"></a>00100 <span class="comment"> *</span>
<a name="l00101"></a>00101 <span class="comment"> * The end operation signifies that local continuations should not be</span>
<a name="l00102"></a>00102 <span class="comment"> * used any more in the function. This operation is not needed for</span>
<a name="l00103"></a>00103 <span class="comment"> * most implementations of local continuation, but is required by a</span>
<a name="l00104"></a>00104 <span class="comment"> * few implementations.</span>
<a name="l00105"></a>00105 <span class="comment"> *</span>
<a name="l00106"></a>00106 <span class="comment"> * \hideinitializer </span>
<a name="l00107"></a>00107 <span class="comment"> */</span>
<a name="l00108"></a><a class="code" href="a00017.html#g3d76802e55349cc8bf74f286ced203c3">00108</a> <span class="preprocessor">#define LC_END(lc)</span>
<a name="l00109"></a>00109 <span class="preprocessor"></span><span class="comment"></span>
<a name="l00110"></a>00110 <span class="comment">/**</span>
<a name="l00111"></a>00111 <span class="comment"> * \var typedef lc_t;</span>
<a name="l00112"></a>00112 <span class="comment"> *</span>
<a name="l00113"></a>00113 <span class="comment"> * The local continuation type.</span>
<a name="l00114"></a>00114 <span class="comment"> *</span>
<a name="l00115"></a>00115 <span class="comment"> * \hideinitializer</span>
<a name="l00116"></a>00116 <span class="comment"> */</span>
<a name="l00117"></a>00117 <span class="preprocessor">#endif </span><span class="comment">/* DOXYGEN */</span>
<a name="l00118"></a>00118 
<a name="l00119"></a>00119 <span class="preprocessor">#ifndef __LC_H__</span>
<a name="l00120"></a>00120 <span class="preprocessor"></span><span class="preprocessor">#define __LC_H__</span>
<a name="l00121"></a>00121 <span class="preprocessor"></span>
<a name="l00122"></a>00122 
<a name="l00123"></a>00123 <span class="preprocessor">#ifdef LC_INCLUDE</span>
<a name="l00124"></a>00124 <span class="preprocessor"></span><span class="preprocessor">#include LC_INCLUDE</span>
<a name="l00125"></a>00125 <span class="preprocessor"></span><span class="preprocessor">#else</span>
<a name="l00126"></a>00126 <span class="preprocessor"></span><span class="preprocessor">#include "<a class="code" href="a00010.html">lc-switch.h</a>"</span>
<a name="l00127"></a>00127 <span class="preprocessor">#endif </span><span class="comment">/* LC_INCLUDE */</span>
<a name="l00128"></a>00128 
<a name="l00129"></a>00129 <span class="preprocessor">#endif </span><span class="comment">/* __LC_H__ */</span>
<a name="l00130"></a>00130 <span class="comment"></span>
<a name="l00131"></a>00131 <span class="comment">/** @} */</span><span class="comment"></span>
<a name="l00132"></a>00132 <span class="comment">/** @} */</span>
</pre></div><hr size="1"><address style="align: right;"><small>Generated on Mon Oct 2 10:06:29 2006 for The Protothreads Library 1.4 by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.4.6 </small></address>
</body>
</html>
