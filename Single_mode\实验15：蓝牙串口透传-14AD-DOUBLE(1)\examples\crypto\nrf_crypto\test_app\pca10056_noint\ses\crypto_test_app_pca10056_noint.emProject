<!DOCTYPE CrossStudio_Project_File>
<solution Name="crypto_test_app_pca10056_noint" target="8" version="2">
  <project Name="crypto_test_app_pca10056_noint">
    <configuration
      Name="Common"
      arm_architecture="v7EM"
      arm_core_type="Cortex-M4"
      arm_endian="Little"
      arm_fp_abi="Hard"
      arm_fpu_type="FPv4-SP-D16"
      arm_linker_heap_size="8192"
      arm_linker_process_stack_size="0"
      arm_linker_stack_size="16384"
      arm_linker_treat_warnings_as_errors="No"
      arm_simulator_memory_simulation_parameter="RWX 00000000,00100000,FFFFFFFF;RWX 20000000,00010000,CDCDCDCD"
      arm_target_device_name="nRF52840_xxAA"
      arm_target_interface_type="SWD"
      c_user_include_directories="../../config;../../../../../../components;../../../../../../components/boards;../../../../../../components/drivers_nrf/nrf_soc_nosd;../../../../../../components/libraries/atomic;../../../../../../components/libraries/atomic_fifo;../../../../../../components/libraries/balloc;../../../../../../components/libraries/bsp;../../../../../../components/libraries/crypto;../../../../../../components/libraries/crypto/backend/cc310;../../../../../../components/libraries/crypto/backend/cc310_bl;../../../../../../components/libraries/crypto/backend/cifra;../../../../../../components/libraries/crypto/backend/mbedtls;../../../../../../components/libraries/crypto/backend/micro_ecc;../../../../../../components/libraries/crypto/backend/nrf_hw;../../../../../../components/libraries/crypto/backend/nrf_sw;../../../../../../components/libraries/crypto/backend/oberon;../../../../../../components/libraries/crypto/backend/optiga;../../../../../../components/libraries/delay;../../../../../../components/libraries/experimental_section_vars;../../../../../../components/libraries/log;../../../../../../components/libraries/log/src;../../../../../../components/libraries/mem_manager;../../../../../../components/libraries/memobj;../../../../../../components/libraries/mutex;../../../../../../components/libraries/queue;../../../../../../components/libraries/ringbuf;../../../../../../components/libraries/scheduler;../../../../../../components/libraries/sortlist;../../../../../../components/libraries/stack_info;../../../../../../components/libraries/strerror;../../../../../../components/libraries/timer;../../../../../../components/libraries/util;../../../../../../components/toolchain/cmsis/include;../..;../../common_test;../../test_cases;../../../../../../external/cifra_AES128-EAX;../../../../../../external/fprintf;../../../../../../external/mbedtls/include;../../../../../../external/micro-ecc/micro-ecc;../../../../../../external/nrf_cc310/include;../../../../../../external/nrf_oberon;../../../../../../external/nrf_oberon/include;../../../../../../external/nrf_tls/mbedtls/nrf_crypto/config;../../../../../../external/segger_rtt;../../../../../../integration/nrfx;../../../../../../integration/nrfx/legacy;../../../../../../modules/nrfx;../../../../../../modules/nrfx/drivers/include;../../../../../../modules/nrfx/hal;../../../../../../modules/nrfx/mdk;../config;"
      c_preprocessor_definitions="APP_TIMER_V2;APP_TIMER_V2_RTC1_ENABLED;BOARD_PCA10056;BSP_DEFINES_ONLY;CONFIG_GPIO_AS_PINRESET;FLOAT_ABI_HARD;INITIALIZE_USER_SECTIONS;MBEDTLS_CONFIG_FILE=&quot;nrf_crypto_mbedtls_config.h&quot;;NO_VTOR_CONFIG;NRF52840_XXAA;NRF_CRYPTO_MAX_INSTANCE_COUNT=1;uECC_ENABLE_VLI_API=0;uECC_OPTIMIZATION_LEVEL=3;uECC_SQUARE_FUNC=0;uECC_SUPPORT_COMPRESSED_POINT=0;uECC_VLI_NATIVE_LITTLE_ENDIAN=1;"
      debug_target_connection="J-Link"
      gcc_entry_point="Reset_Handler"
      macros="CMSIS_CONFIG_TOOL=../../../../../../external_tools/cmsisconfig/CMSIS_Configuration_Wizard.jar"
      debug_register_definition_file="../../../../../../modules/nrfx/mdk/nrf52840.svd"
      debug_start_from_entry_point_symbol="No"
      gcc_debugging_level="Level 3"      linker_output_format="hex"
      linker_printf_width_precision_supported="Yes"
      linker_printf_fmt_level="long"
      linker_scanf_fmt_level="long"
      linker_section_placement_file="flash_placement.xml"
      linker_section_placement_macros="FLASH_PH_START=0x0;FLASH_PH_SIZE=0x100000;RAM_PH_START=0x20000000;RAM_PH_SIZE=0x40000;FLASH_START=0x0;FLASH_SIZE=0x100000;RAM_START=0x20000000;RAM_SIZE=0x40000"
      
      linker_section_placements_segments="FLASH RX 0x0 0x100000;RAM RWX 0x20000000 0x40000"
      project_directory=""
      project_type="Executable" />
      <folder Name="Segger Startup Files">
        <file file_name="$(StudioDir)/source/thumb_crt0.s" />
      </folder>
    <folder Name="nrf_cc310">
      <file file_name="../../../../../../external/nrf_cc310/lib/cortex-m4/hard-float/no-interrupts/libnrf_cc310_0.9.13.a" />
    </folder>
    <folder Name="nRF_Crypto">
      <file file_name="../../../../../../components/libraries/crypto/nrf_crypto_aead.c" />
      <file file_name="../../../../../../components/libraries/crypto/nrf_crypto_aes.c" />
      <file file_name="../../../../../../components/libraries/crypto/nrf_crypto_aes_shared.c" />
      <file file_name="../../../../../../components/libraries/crypto/nrf_crypto_ecc.c" />
      <file file_name="../../../../../../components/libraries/crypto/nrf_crypto_ecdh.c" />
      <file file_name="../../../../../../components/libraries/crypto/nrf_crypto_ecdsa.c" />
      <file file_name="../../../../../../components/libraries/crypto/nrf_crypto_eddsa.c" />
      <file file_name="../../../../../../components/libraries/crypto/nrf_crypto_error.c" />
      <file file_name="../../../../../../components/libraries/crypto/nrf_crypto_hash.c" />
      <file file_name="../../../../../../components/libraries/crypto/nrf_crypto_hkdf.c" />
      <file file_name="../../../../../../components/libraries/crypto/nrf_crypto_hmac.c" />
      <file file_name="../../../../../../components/libraries/crypto/nrf_crypto_init.c" />
      <file file_name="../../../../../../components/libraries/crypto/nrf_crypto_rng.c" />
      <file file_name="../../../../../../components/libraries/crypto/nrf_crypto_shared.c" />
    </folder>
    <folder Name="nRF_Crypto backend nRF HW">
      <file file_name="../../../../../../components/libraries/crypto/backend/nrf_hw/nrf_hw_backend_init.c" />
      <file file_name="../../../../../../components/libraries/crypto/backend/nrf_hw/nrf_hw_backend_rng.c" />
      <file file_name="../../../../../../components/libraries/crypto/backend/nrf_hw/nrf_hw_backend_rng_mbedtls.c" />
    </folder>
    <folder Name="Board Definition">
      <file file_name="../../../../../../components/boards/boards.c" />
    </folder>
    <folder Name="nRF_Crypto backend uECC">
      <file file_name="../../../../../../components/libraries/crypto/backend/micro_ecc/micro_ecc_backend_ecc.c" />
      <file file_name="../../../../../../components/libraries/crypto/backend/micro_ecc/micro_ecc_backend_ecdh.c" />
      <file file_name="../../../../../../components/libraries/crypto/backend/micro_ecc/micro_ecc_backend_ecdsa.c" />
    </folder>
    <folder Name="Test Cases">
      <file file_name="../../test_cases/test_aead.c" />
      <file file_name="../../test_cases/test_aes.c" />
      <file file_name="../../test_cases/test_ecdh.c" />
      <file file_name="../../test_cases/test_ecdsa.c" />
      <file file_name="../../test_cases/test_eddsa.c" />
      <file file_name="../../test_cases/test_hkdf.c" />
      <file file_name="../../test_cases/test_hmac.c" />
      <file file_name="../../test_cases/test_sha.c" />
      <file file_name="../../test_cases/test_vectors_aead_ccm.c" />
      <file file_name="../../test_cases/test_vectors_aead_eax.c" />
      <file file_name="../../test_cases/test_vectors_aead_gcm.c" />
      <file file_name="../../test_cases/test_vectors_aes_cbc.c" />
      <file file_name="../../test_cases/test_vectors_aes_cbc_mac.c" />
      <file file_name="../../test_cases/test_vectors_aes_cfb.c" />
      <file file_name="../../test_cases/test_vectors_aes_cmac.c" />
      <file file_name="../../test_cases/test_vectors_aes_ctr.c" />
      <file file_name="../../test_cases/test_vectors_aes_ecb.c" />
      <file file_name="../../test_cases/test_vectors_chacha_poly.c" />
      <file file_name="../../test_cases/test_vectors_ecdh.c" />
      <file file_name="../../test_cases/test_vectors_ecdsa_random.c" />
      <file file_name="../../test_cases/test_vectors_ecdsa_sign.c" />
      <file file_name="../../test_cases/test_vectors_ecdsa_verify.c" />
      <file file_name="../../test_cases/test_vectors_eddsa.c" />
      <file file_name="../../test_cases/test_vectors_hkdf.c" />
      <file file_name="../../test_cases/test_vectors_hmac.c" />
      <file file_name="../../test_cases/test_vectors_sha.c" />
    </folder>
    <folder Name="nRF_Libraries">
      <file file_name="../../../../../../components/libraries/util/app_error.c" />
      <file file_name="../../../../../../components/libraries/util/app_error_handler_gcc.c" />
      <file file_name="../../../../../../components/libraries/util/app_error_weak.c" />
      <file file_name="../../../../../../components/libraries/scheduler/app_scheduler.c" />
      <file file_name="../../../../../../components/libraries/timer/app_timer2.c" />
      <file file_name="../../../../../../components/libraries/util/app_util_platform.c" />
      <file file_name="../../../../../../external/cifra_AES128-EAX/blockwise.c" />
      <file file_name="../../../../../../external/cifra_AES128-EAX/cifra_cmac.c" />
      <file file_name="../../../../../../external/cifra_AES128-EAX/cifra_eax_aes.c" />
      <file file_name="../../../../../../components/libraries/timer/drv_rtc.c" />
      <file file_name="../../../../../../external/cifra_AES128-EAX/eax.c" />
      <file file_name="../../../../../../external/cifra_AES128-EAX/gf128.c" />
      <file file_name="../../../../../../components/libraries/mem_manager/mem_manager.c" />
      <file file_name="../../../../../../external/cifra_AES128-EAX/modes.c" />
      <file file_name="../../../../../../components/libraries/util/nrf_assert.c" />
      <file file_name="../../../../../../components/libraries/atomic_fifo/nrf_atfifo.c" />
      <file file_name="../../../../../../components/libraries/atomic/nrf_atomic.c" />
      <file file_name="../../../../../../components/libraries/balloc/nrf_balloc.c" />
      <file file_name="../../../../../../external/fprintf/nrf_fprintf.c" />
      <file file_name="../../../../../../external/fprintf/nrf_fprintf_format.c" />
      <file file_name="../../../../../../components/libraries/memobj/nrf_memobj.c" />
      <file file_name="../../../../../../components/libraries/queue/nrf_queue.c" />
      <file file_name="../../../../../../components/libraries/ringbuf/nrf_ringbuf.c" />
      <file file_name="../../../../../../components/libraries/sortlist/nrf_sortlist.c" />
      <file file_name="../../../../../../components/libraries/strerror/nrf_strerror.c" />
    </folder>
    <folder Name="nRF_Crypto backend mbed TLS">
      <file file_name="../../../../../../components/libraries/crypto/backend/mbedtls/mbedtls_backend_aes.c" />
      <file file_name="../../../../../../components/libraries/crypto/backend/mbedtls/mbedtls_backend_aes_aead.c" />
      <file file_name="../../../../../../components/libraries/crypto/backend/mbedtls/mbedtls_backend_ecc.c" />
      <file file_name="../../../../../../components/libraries/crypto/backend/mbedtls/mbedtls_backend_ecdh.c" />
      <file file_name="../../../../../../components/libraries/crypto/backend/mbedtls/mbedtls_backend_ecdsa.c" />
      <file file_name="../../../../../../components/libraries/crypto/backend/mbedtls/mbedtls_backend_hash.c" />
      <file file_name="../../../../../../components/libraries/crypto/backend/mbedtls/mbedtls_backend_hmac.c" />
      <file file_name="../../../../../../components/libraries/crypto/backend/mbedtls/mbedtls_backend_init.c" />
    </folder>
    <folder Name="nRF_Drivers">
      <file file_name="../../../../../../integration/nrfx/legacy/nrf_drv_rng.c" />
      <file file_name="../../../../../../modules/nrfx/soc/nrfx_atomic.c" />
      <file file_name="../../../../../../modules/nrfx/drivers/src/nrfx_rng.c" />
    </folder>
    <folder Name="nRF_Crypto backend cifra">
      <file file_name="../../../../../../components/libraries/crypto/backend/cifra/cifra_backend_aes_aead.c" />
    </folder>
    <folder Name="nRF_Log">
      <file file_name="../../../../../../components/libraries/log/src/nrf_log_backend_rtt.c" />
      <file file_name="../../../../../../components/libraries/log/src/nrf_log_backend_serial.c" />
      <file file_name="../../../../../../components/libraries/log/src/nrf_log_default_backends.c" />
      <file file_name="../../../../../../components/libraries/log/src/nrf_log_frontend.c" />
      <file file_name="../../../../../../components/libraries/log/src/nrf_log_str_formatter.c" />
    </folder>
    <folder Name="Application">
      <file file_name="../../common_test/common_test.c" />
      <file file_name="../../main.c" />
      <file file_name="../config/sdk_config.h" />
    </folder>
    <folder Name="nRF_micro-ecc">
      <file file_name="../../../../../../external/micro-ecc/nrf52hf_armgcc/armgcc/micro_ecc_lib_nrf52.a" />
    </folder>
    <folder Name="nRF_TLS">
      <file file_name="../../../../../../external/mbedtls/library/aes.c" />
      <file file_name="../../../../../../external/mbedtls/library/aesni.c" />
      <file file_name="../../../../../../external/mbedtls/library/arc4.c" />
      <file file_name="../../../../../../external/mbedtls/library/aria.c" />
      <file file_name="../../../../../../external/mbedtls/library/asn1parse.c" />
      <file file_name="../../../../../../external/mbedtls/library/asn1write.c" />
      <file file_name="../../../../../../external/mbedtls/library/base64.c" />
      <file file_name="../../../../../../external/mbedtls/library/bignum.c" />
      <file file_name="../../../../../../external/mbedtls/library/blowfish.c" />
      <file file_name="../../../../../../external/mbedtls/library/camellia.c" />
      <file file_name="../../../../../../external/mbedtls/library/ccm.c" />
      <file file_name="../../../../../../external/mbedtls/library/certs.c" />
      <file file_name="../../../../../../external/mbedtls/library/chacha20.c" />
      <file file_name="../../../../../../external/mbedtls/library/chachapoly.c" />
      <file file_name="../../../../../../external/mbedtls/library/cipher.c" />
      <file file_name="../../../../../../external/mbedtls/library/cipher_wrap.c" />
      <file file_name="../../../../../../external/mbedtls/library/cmac.c" />
      <file file_name="../../../../../../external/mbedtls/library/ctr_drbg.c" />
      <file file_name="../../../../../../external/mbedtls/library/debug.c" />
      <file file_name="../../../../../../external/mbedtls/library/des.c" />
      <file file_name="../../../../../../external/mbedtls/library/dhm.c" />
      <file file_name="../../../../../../external/mbedtls/library/ecdh.c" />
      <file file_name="../../../../../../external/mbedtls/library/ecdsa.c" />
      <file file_name="../../../../../../external/mbedtls/library/ecjpake.c" />
      <file file_name="../../../../../../external/mbedtls/library/ecp.c" />
      <file file_name="../../../../../../external/mbedtls/library/ecp_curves.c" />
      <file file_name="../../../../../../external/mbedtls/library/entropy.c" />
      <file file_name="../../../../../../external/mbedtls/library/entropy_poll.c" />
      <file file_name="../../../../../../external/mbedtls/library/error.c" />
      <file file_name="../../../../../../external/mbedtls/library/gcm.c" />
      <file file_name="../../../../../../external/mbedtls/library/havege.c" />
      <file file_name="../../../../../../external/mbedtls/library/hmac_drbg.c" />
      <file file_name="../../../../../../external/mbedtls/library/md.c" />
      <file file_name="../../../../../../external/mbedtls/library/md2.c" />
      <file file_name="../../../../../../external/mbedtls/library/md4.c" />
      <file file_name="../../../../../../external/mbedtls/library/md5.c" />
      <file file_name="../../../../../../external/mbedtls/library/md_wrap.c" />
      <file file_name="../../../../../../external/mbedtls/library/memory_buffer_alloc.c" />
      <file file_name="../../../../../../external/mbedtls/library/net_sockets.c" />
      <file file_name="../../../../../../external/mbedtls/library/nist_kw.c" />
      <file file_name="../../../../../../external/mbedtls/library/oid.c" />
      <file file_name="../../../../../../external/mbedtls/library/padlock.c" />
      <file file_name="../../../../../../external/mbedtls/library/pem.c" />
      <file file_name="../../../../../../external/mbedtls/library/pk.c" />
      <file file_name="../../../../../../external/mbedtls/library/pk_wrap.c" />
      <file file_name="../../../../../../external/mbedtls/library/pkcs11.c" />
      <file file_name="../../../../../../external/mbedtls/library/pkcs12.c" />
      <file file_name="../../../../../../external/mbedtls/library/pkcs5.c" />
      <file file_name="../../../../../../external/mbedtls/library/pkparse.c" />
      <file file_name="../../../../../../external/mbedtls/library/pkwrite.c" />
      <file file_name="../../../../../../external/mbedtls/library/platform.c" />
      <file file_name="../../../../../../external/mbedtls/library/platform_util.c" />
      <file file_name="../../../../../../external/mbedtls/library/poly1305.c" />
      <file file_name="../../../../../../external/mbedtls/library/ripemd160.c" />
      <file file_name="../../../../../../external/mbedtls/library/rsa.c" />
      <file file_name="../../../../../../external/mbedtls/library/rsa_internal.c" />
      <file file_name="../../../../../../external/mbedtls/library/sha1.c" />
      <file file_name="../../../../../../external/mbedtls/library/sha256.c" />
      <file file_name="../../../../../../external/mbedtls/library/sha512.c" />
      <file file_name="../../../../../../external/mbedtls/library/ssl_cache.c" />
      <file file_name="../../../../../../external/mbedtls/library/ssl_ciphersuites.c" />
      <file file_name="../../../../../../external/mbedtls/library/ssl_cli.c" />
      <file file_name="../../../../../../external/mbedtls/library/ssl_cookie.c" />
      <file file_name="../../../../../../external/mbedtls/library/ssl_srv.c" />
      <file file_name="../../../../../../external/mbedtls/library/ssl_ticket.c" />
      <file file_name="../../../../../../external/mbedtls/library/ssl_tls.c" />
      <file file_name="../../../../../../external/mbedtls/library/threading.c" />
      <file file_name="../../../../../../external/mbedtls/library/version.c" />
      <file file_name="../../../../../../external/mbedtls/library/version_features.c" />
      <file file_name="../../../../../../external/mbedtls/library/x509.c" />
      <file file_name="../../../../../../external/mbedtls/library/x509_create.c" />
      <file file_name="../../../../../../external/mbedtls/library/x509_crl.c" />
      <file file_name="../../../../../../external/mbedtls/library/x509_crt.c" />
      <file file_name="../../../../../../external/mbedtls/library/x509_csr.c" />
      <file file_name="../../../../../../external/mbedtls/library/x509write_crt.c" />
      <file file_name="../../../../../../external/mbedtls/library/x509write_csr.c" />
      <file file_name="../../../../../../external/mbedtls/library/xtea.c" />
    </folder>
    <folder Name="nRF_Oberon_Crypto">
      <file file_name="../../../../../../external/nrf_oberon/lib/cortex-m4/hard-float/liboberon_3.0.5.a" />
    </folder>
    <folder Name="None">
      <file file_name="../../../../../../modules/nrfx/mdk/ses_startup_nrf52840.s" />
      <file file_name="../../../../../../modules/nrfx/mdk/ses_startup_nrf_common.s" />
      <file file_name="../../../../../../modules/nrfx/mdk/system_nrf52840.c" />
    </folder>
    <folder Name="nRF_Crypto backend CC310">
      <file file_name="../../../../../../components/libraries/crypto/backend/cc310/cc310_backend_aes.c" />
      <file file_name="../../../../../../components/libraries/crypto/backend/cc310/cc310_backend_aes_aead.c" />
      <file file_name="../../../../../../components/libraries/crypto/backend/cc310/cc310_backend_chacha_poly_aead.c" />
      <file file_name="../../../../../../components/libraries/crypto/backend/cc310/cc310_backend_ecc.c" />
      <file file_name="../../../../../../components/libraries/crypto/backend/cc310/cc310_backend_ecdh.c" />
      <file file_name="../../../../../../components/libraries/crypto/backend/cc310/cc310_backend_ecdsa.c" />
      <file file_name="../../../../../../components/libraries/crypto/backend/cc310/cc310_backend_eddsa.c" />
      <file file_name="../../../../../../components/libraries/crypto/backend/cc310/cc310_backend_hash.c" />
      <file file_name="../../../../../../components/libraries/crypto/backend/cc310/cc310_backend_hmac.c" />
      <file file_name="../../../../../../components/libraries/crypto/backend/cc310/cc310_backend_init.c" />
      <file file_name="../../../../../../components/libraries/crypto/backend/cc310/cc310_backend_mutex.c" />
      <file file_name="../../../../../../components/libraries/crypto/backend/cc310/cc310_backend_rng.c" />
      <file file_name="../../../../../../components/libraries/crypto/backend/cc310/cc310_backend_shared.c" />
    </folder>
    <folder Name="nRF_Segger_RTT">
      <file file_name="../../../../../../external/segger_rtt/SEGGER_RTT.c" />
      <file file_name="../../../../../../external/segger_rtt/SEGGER_RTT_Syscalls_SES.c" />
      <file file_name="../../../../../../external/segger_rtt/SEGGER_RTT_printf.c" />
    </folder>
    <folder Name="nRF_Crypto backend Oberon">
      <file file_name="../../../../../../components/libraries/crypto/backend/oberon/oberon_backend_chacha_poly_aead.c" />
      <file file_name="../../../../../../components/libraries/crypto/backend/oberon/oberon_backend_ecc.c" />
      <file file_name="../../../../../../components/libraries/crypto/backend/oberon/oberon_backend_ecdh.c" />
      <file file_name="../../../../../../components/libraries/crypto/backend/oberon/oberon_backend_ecdsa.c" />
      <file file_name="../../../../../../components/libraries/crypto/backend/oberon/oberon_backend_eddsa.c" />
      <file file_name="../../../../../../components/libraries/crypto/backend/oberon/oberon_backend_hash.c" />
      <file file_name="../../../../../../components/libraries/crypto/backend/oberon/oberon_backend_hmac.c" />
    </folder>
  </project>
  <configuration Name="Release"
    c_preprocessor_definitions="NDEBUG"
    link_time_optimization="No"    gcc_optimization_level="Optimize For Size" />
  <configuration Name="Debug"
    c_preprocessor_definitions="DEBUG; DEBUG_NRF"
    gcc_optimization_level="None"/>

</solution>
