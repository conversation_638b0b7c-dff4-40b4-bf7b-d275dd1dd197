# C嵌入式开发扩展包

专门针对单片机嵌入式C开发的AI辅助工具包，重点支持nrf52832和ads129x系列芯片的软件设计。

## 🎯 认识您的嵌入式开发团队

### 🔧 李工 - C嵌入式开发协调员 (c-embedded-orchestrator)

*拥有15年嵌入式开发经验，专精单片机软件设计，对nrf52832和ads129x芯片有深入理解*

李工是您的嵌入式开发项目协调员，将通过编号选项和结构化工作流指导您完成从芯片配置到功耗优化的完整嵌入式开发过程。他擅长将复杂的嵌入式概念简化解释，注重实际可操作性。

### 💼 专业领域专家

- **🏗️ 张博士** - 硬件抽象层专家 (hardware-abstraction-specialist)
  - *计算机工程博士，专精HAL架构设计和外设驱动开发*
  - 负责硬件抽象层设计、寄存器配置、驱动架构

- **⚡ 王工** - 实时系统专家 (realtime-systems-expert)  
  - *10年实时系统开发经验，对时序分析有深入理解*
  - 负责中断处理设计、任务调度优化、实时性能分析

- **🔋 陈工** - 功耗管理专家 (power-management-specialist)
  - *8年低功耗设计经验，追求极致效率*
  - 负责功耗分析、低功耗模式设计、电源管理策略

- **📡 刘工** - 通信协议专家 (communication-protocol-expert)
  - *12年通信协议开发经验，注重标准规范*
  - 负责SPI/I2C/UART/BLE协议实现和优化

## 🚀 快速开始

### 1. 准备数据文件 (放置在 `bmad-core/data/`)

您需要提供以下技术文档：

#### 📋 必需文件

- **`nrf52832-specs.md`** - nrf52832芯片完整技术规格
  - 寄存器映射表、中断向量表、时钟配置选项
  - 外设寄存器定义和配置示例

- **`ads129x-specs.md`** - ads129x系列芯片配置参数  
  - ADC配置寄存器、采样率设置、SPI通信时序
  - 完整的配置寄存器说明和使用示例

- **`project-requirements.md`** - 项目需求和约束条件
  - 采样频率要求、功耗限制、实时性要求
  - 明确的量化指标和验收标准

- **`hardware-schematic.md`** - 硬件原理图和连接信息
  - GPIO分配表、SPI连接图、电源连接
  - 完整的硬件接口定义和连接关系

- **`power-budget.md`** - 功耗预算和电源管理要求
  - 待机功耗<10μA、工作功耗<5mA、电池容量
  - 明确的功耗指标和测量方法

### 2. 启动协调员

```bash
npm run agent c-embedded-orchestrator
```

### 3. 跟随编号选项

李工将为每个决策点提供编号选择，确保开发过程清晰有序：

```
欢迎！我是李工，您的嵌入式开发协调员。

请选择您的开发需求：
1. 🆕 初始化新的嵌入式项目
2. 🔧 芯片配置和初始化
3. 🚗 外设驱动开发
4. ⚡ 实时系统设计
5. 🔋 功耗优化分析
6. 📡 通信协议实现
7. 🐛 调试策略制定
8. ✅ 代码质量审查
9. 👥 移交给专业专家

请输入数字 1-9 或输入您的具体需求：
```

### 4. 质量保证

多级验证确保开发质量：
- **⭐ 星级评分系统** - 代码质量量化评估
- **✅ 准备就绪框架** - 明确的完成标准
- **🔍 专家审查** - 领域专家深度验证

## 🛠️ 核心功能

### 📝 智能模板系统

- **芯片配置模板** - 支持nrf52832/ads129x的完整配置向导
- **驱动实现模板** - 标准化的驱动开发框架  
- **中断处理模板** - 实时系统中断设计模板
- **功耗分析模板** - 系统化的功耗优化报告

### 🎯 专业任务模块

- **芯片初始化配置** - 时钟、电源、GPIO完整初始化
- **外设驱动开发** - HAL层设计和驱动实现
- **中断处理设计** - 实时系统中断优化
- **功耗优化分析** - 低功耗模式和电源管理
- **通信协议实现** - SPI/I2C/UART/BLE协议栈
- **内存优化分析** - 内存使用和性能优化
- **调试策略制定** - 系统化调试方法
- **代码质量审查** - 嵌入式代码规范检查

### ✅ 多级质量检查

- **嵌入式代码质量检查** - 基础/全面/专家三级验证
- **硬件集成检查** - 硬件兼容性和时序验证
- **功耗检查清单** - 功耗目标达成验证

## 🎯 适用场景

### 🆕 新项目开发 (Greenfield)
- 完整的芯片选型和配置指导
- 系统架构设计和实现
- 从零开始的驱动开发

### 🔄 现有项目优化 (Brownfield)  
- 性能瓶颈分析和优化
- 功耗优化和电源管理改进
- 代码质量提升和重构

### 🎓 学习和培训
- 嵌入式开发最佳实践学习
- 专业技能提升指导
- 实际项目经验积累

## 💡 嵌入式知识库

扩展包内置完整的领域知识：

- **🔧 嵌入式C最佳实践** - 代码组织、内存管理、性能优化
- **📚 嵌入式术语词典** - 专业术语和概念解释  
- **📋 开发标准规范** - 行业标准和质量要求

## 🔄 工作流程

### 典型开发流程

1. **📋 项目初始化** - 需求分析、芯片选择、环境搭建
2. **⚙️ 芯片配置** - 时钟系统、电源管理、基础初始化
3. **🏗️ HAL设计** - 硬件抽象层架构和接口定义
4. **🚗 驱动开发** - 外设驱动实现和测试
5. **⚡ 实时优化** - 中断处理、任务调度、时序分析
6. **📡 通信实现** - 协议栈开发和数据传输
7. **🔋 功耗优化** - 低功耗模式、电源管理策略
8. **🧪 集成测试** - 系统集成、性能验证、质量保证

### 专家协作模式

- **🎯 需求驱动** - 根据具体需求智能推荐专家
- **🔄 无缝移交** - 专家间的工作成果传递
- **📊 进度跟踪** - 实时的开发进度和质量监控
- **✅ 质量把关** - 每个阶段的质量验证和改进建议

## 🎓 最佳实践

### 代码质量
- 分层架构设计原则
- 内存管理和优化策略
- 实时性能保证方法
- 错误处理和调试技巧

### 开发效率  
- 模块化设计和代码复用
- 自动化测试和验证
- 版本控制和配置管理
- 文档化和知识传承

### 系统可靠性
- 硬件故障处理机制
- 软件异常恢复策略
- 系统监控和诊断
- 产品化质量保证

**开始您的嵌入式开发之旅，让专业团队为您提供全方位的技术支持！**
