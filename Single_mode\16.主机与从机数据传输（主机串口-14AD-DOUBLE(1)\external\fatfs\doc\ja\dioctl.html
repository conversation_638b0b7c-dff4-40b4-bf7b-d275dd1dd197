<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html lang="ja">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta http-equiv="Content-Style-Type" content="text/css">
<link rel="up" title="FatFs" href="../00index_j.html">
<link rel="alternate" hreflang="en" title="English" href="../en/dioctl.html">
<link rel="stylesheet" href="../css_j.css" type="text/css" media="screen" title="ELM Default">
<title>FatFs - disk_ioctl</title>
</head>

<body>

<div class="para func">
<h2>disk_ioctl</h2>
<p>一般的なデータ読み書き以外のストレージ デバイス自体に対する様々な制御を行います。</p>
<pre>
DRESULT disk_ioctl (
  BYTE <span class="arg">pdrv</span>,    <span class="c">/* [IN] 物理ドライブ番号 */</span>
  BYTE <span class="arg">cmd</span>,     <span class="c">/* [IN] 制御コマンド */</span>
  void* <span class="arg">buff</span>    <span class="c">/* [I/O] データ受け渡しバッファ */</span>
);
</pre>
</div>

<div class="para arg">
<h4>引数</h4>
<dl class="par">
<dt>pdrv</dt>
<dd>対象のデバイスを識別する物理ドライブ番号(0-9)が指定されます。</dd>
<dt>cmd</dt>
<dd>制御コマンド コードが指定されます。</dd>
<dt>buff</dt>
<dd>制御コマンドに依存したパラメータを授受するバッファを指すポインタが指定されます。パラメータの授受のないコマンドの場合は、値に意味はありません。</dd>
</dl>
</div>

<div class="para ret">
<h4>戻り値</h4>
<dl class="ret">
<dt>RES_OK (0)</dt>
<dd>正常終了。</dd>
<dt>RES_ERROR</dt>
<dd>何らかのエラーが発生した。</dd>
<dt>RES_PARERR</dt>
<dd>コマンドが不正。</dd>
<dt>RES_NOTRDY</dt>
<dd>ドライブが動作可能状態ではない、または初期化されていない。</dd>
</dl>
</div>

<div class="para desc">
<h4>解説</h4>
<p>ストレージ デバイスの種類によりサポートされるコマンドは異なりますが、FatFsモジュール自体は、次の汎用コマンドのみ使用し、特定のデバイスに依存した制御は行いません。</p>
<table class="lst">
<caption>標準ioctlコマンド</caption>
<tr><th>コマンド</th><th>解説</th></tr>
<tr><td>CTRL_SYNC</td><td>デバイスのデータ書き込み処理を完了させます。ドライバがライト バック キャッシュなどを持っている場合は、書き込まれていないデータを即時書き込みます。メディア上への書き込みがそれぞれ<tt>disk_write</tt>関数の中で完了する場合は、このコマンドに対してすることはありません。</td></tr>
<tr><td>GET_SECTOR_COUNT</td><td>総セクタ数の取得。<tt class="arg">buff</tt>の指す<tt>DWORD</tt>型変数にドライブ上の総セクタ数を返します。<tt>f_mkfs, f_fdisk</tt>関数内から呼び出され、作成するボリュームのサイズを決定するために使用されます。</td></tr>
<tr><td>GET_SECTOR_SIZE</td><td>セクタ サイズの取得。セクタ サイズ可変(<tt>_MAX_SS &gt; _MIN_SS</tt>)のとき、<tt>disk_initailize</tt>関数の成功に続き呼び出されるので、<tt class="arg">buff</tt>の指す<tt>WORD</tt>型変数に現在のセクタ サイズを返します。有効値は512、1024、2048または4096です。セクタ サイズ固定(<tt>_MAX_SS == _MIN_SS</tt>)のときはこのコマンドは使われることはなく、デバイスは常にそのセクタ サイズで動作しなければなりません。</td></tr>
<tr><td>GET_BLOCK_SIZE</td><td>消去ブロック サイズの取得。<tt class="arg">buff</tt>の指す<tt>DWORD</tt>型変数にフラッシュ メモリの消去ブロック サイズ(セクタ単位)を返します。1から32768の範囲で2の累乗の値でなければなりません。ブロック サイズ不明またはフラッシュ メモリ以外のデバイスでは1を返します。<tt>f_mkfs</tt>関数内でのみ使用され、作成されるボリュームのデータ領域はこの境界にアライメントされます。</td></tr>
<tr><td>CTRL_TRIM</td><td>不必要セクタの通知。<tt class="arg">buff</tt>の指す<tt>DWORD</tt>型配列には不必要になった領域 {開始セクタ,終了セクタ} を指定して呼び出されます。TRIM機能が有効(<tt>_USE_TRIM == 1</tt>)で、クラスタが解放されるとき、およびフォーマット時に呼び出されます。これは、ATAコマンド セットのTrimコマンドと等価で、この機能をサポートしないデバイスは何もする必要はありません。また、戻り値はチェックされず、結果によってFatFsの動作が影響を受けることはありません。</td></tr>
</table>

<p>FatFs自体はデバイス依存コマンドやユーザ定義コマンドは一切使用しませんが、アプリケーションから何らかのデバイス制御が行えると便利なことがあります。アプリケーション上で標準以外の制御が必要なときは、必要に応じてユーザ定義コマンドを追加して利用するとよいでしょう。次にコマンドの例を示します。</p>
<table class="lst">
<caption>ユーザ定義ioctlコマンドの例</caption>
<tr><th>コマンド</th><th>解説</th></tr>
<tr><td>CTRL_FORMAT</td><td>メディアの物理フォーマットを行います。<tt class="arg">buff</tt>はNULLでないとき、進行表示のためのコールバック関数のアドレスを示します。</td></tr>
<tr><td>CTRL_POWER_IDLE</td><td>デバイスをアイドル状態にします。通常の読み書き要求でアクティブ状態に戻るなら、<tt>STA_NOINIT</tt>フラグをセットする必要はありません。</td></tr>
<tr><td>CTRL_POWER_OFF</td><td>デバイスをシャットダウン状態にします。<tt>STA_NOINIT</tt>はセットされます。デバイスは<tt>disk_initialize</tt>関数でアクティブ状態に戻ります。</td></tr>
<tr><td>CTRL_LOCK</td><td>ユーザによるメディアの取り出しを禁止します。</td></tr>
<tr><td>CTRL_UNLOCK</td><td>ユーザによるメディアの取り出しを許可します。</td></tr>
<tr><td>CTRL_EJECT</td><td>メディアを排出します。完了後、<tt>STA_NOINIT</tt>と<tt>STA_NODISK</tt>フラグはセットされます。</td></tr>
<tr><td>MMC_GET_TYPE</td><td>カード タイプを示すフラグ(b0:MMCv3, b1:SDv1, b2:SDv2+, b3:LBA)を<tt class="arg">buff</tt>の示す<tt>BYTE</tt>変数に読み出します。(MMC/SDカード専用)</td></tr>
<tr><td>MMC_GET_CSD</td><td>CSDレジスタの内容を<tt class="arg">buff</tt>の示す16バイトのバッファに読み出します。(MMC/SDカード専用)</td></tr>
<tr><td>MMC_GET_CID</td><td>CIDレジスタの内容を<tt class="arg">buff</tt>の示す16バイトのバッファに読み出します。(MMC/SDカード専用)</td></tr>
<tr><td>MMC_GET_OCR</td><td>OCRレジスタの内容を<tt class="arg">buff</tt>の示す4バイトのバッファに読み出します。(MMC/SDカード専用)</td></tr>
<tr><td>MMC_GET_SDSTAT</td><td>SD STATUSレジスタの内容を<tt class="arg">buff</tt>の示す64バイトのバッファに読み出します。(SDカード専用)</td></tr>
<tr><td>ATA_GET_REV</td><td>リビジョン コードを<tt class="arg">buff</tt>の示す16バイトのバッファに読み出します。(ATA/CFカード専用)</td></tr>
<tr><td>ATA_GET_MODEL</td><td>モデル コードを<tt class="arg">buff</tt>の示す40バイトのバッファに読み出します。(ATA/CFカード専用)</td></tr>
<tr><td>ATA_GET_SN</td><td>シリアル番号を<tt class="arg">buff</tt>の示す20バイトのバッファに読み出します。(ATA/CFカード専用)</td></tr>
<tr><td>ISDIO_READ</td><td><tt class="arg">buff</tt>の示すコマンド構造体に従いiSDIOレジスタからデータを読み出します。(FlashAir専用)</td></tr>
<tr><td>ISDIO_WRITE</td><td><tt class="arg">buff</tt>の示すコマンド構造体に従いiSDIOレジスタにデータを書き込みます。(FlashAir専用)</td></tr>
<tr><td>ISDIO_MRITE</td><td><tt class="arg">buff</tt>の示すコマンド構造体に従いiSDIOレジスタの一部のビットを書き換えます。(FlashAir専用)</td></tr>
</table>
</div>


<div class="para comp">
<h4>対応情報</h4>
<p>リード オンリー構成で、かつセクタ サイズ固定構成のときは、この関数は必要とされません。</p>
</div>


<p class="foot"><a href="../00index_j.html">戻る</a></p>
</body>
</html>
