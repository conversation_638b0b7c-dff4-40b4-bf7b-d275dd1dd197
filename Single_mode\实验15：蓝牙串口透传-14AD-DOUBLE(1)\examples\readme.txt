Matrix shows which board is supported by given example

Example                                                                                 | pca10028 | pca10040 | pca10040e | pca10056 |
--------------------------------------------------------------------------------------------------------------------------------------
802_15_4\wireless_uart\raw\first                                                        |          |          |           |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
802_15_4\wireless_uart\raw\second                                                       |          |          |           |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
802_15_4\wireless_uart\secure\first                                                     |          |          |           |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
802_15_4\wireless_uart\secure\second                                                    |          |          |           |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
C:\bambuild\HUSS-RWCRWH1-CR\sdk\nrf5\components\libraries\csense_drv\test               |          |    *     |           |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
C:\bambuild\HUSS-RWCRWH1-CR\sdk\nrf5\components\libraries\usbd\test\usb_hosted\example  |          |          |           |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
C:\bambuild\HUSS-RWCRWH1-CR\sdk\nrf5\config                                             |          |          |           |          |
--------------------------------------------------------------------------------------------------------------------------------------
ant\ant_advanced_burst                                                                  |          |    *     |           |          |
--------------------------------------------------------------------------------------------------------------------------------------
ant\ant_async_transmitter                                                               |          |    *     |           |          |
--------------------------------------------------------------------------------------------------------------------------------------
ant\ant_background_scanning                                                             |          |    *     |           |          |
--------------------------------------------------------------------------------------------------------------------------------------
ant\ant_broadcast\rx                                                                    |          |    *     |           |          |
--------------------------------------------------------------------------------------------------------------------------------------
ant\ant_broadcast\tx                                                                    |          |    *     |           |          |
--------------------------------------------------------------------------------------------------------------------------------------
ant\ant_continuous_scanning_controller                                                  |          |    *     |           |          |
--------------------------------------------------------------------------------------------------------------------------------------
ant\ant_cw_mode                                                                         |          |    *     |           |          |
--------------------------------------------------------------------------------------------------------------------------------------
ant\ant_debug                                                                           |          |    *     |           |          |
--------------------------------------------------------------------------------------------------------------------------------------
ant\ant_fs\client                                                                       |          |    *     |           |          |
--------------------------------------------------------------------------------------------------------------------------------------
ant\ant_hd_search_and_bs                                                                |          |    *     |           |          |
--------------------------------------------------------------------------------------------------------------------------------------
ant\ant_io_demo\ant_io_rx                                                               |          |    *     |           |          |
--------------------------------------------------------------------------------------------------------------------------------------
ant\ant_io_demo\ant_io_tx                                                               |          |    *     |           |          |
--------------------------------------------------------------------------------------------------------------------------------------
ant\ant_message_types\master                                                            |          |    *     |     *     |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
ant\ant_multi_channels\rx                                                               |          |    *     |           |          |
--------------------------------------------------------------------------------------------------------------------------------------
ant\ant_multi_channels\tx                                                               |          |    *     |           |          |
--------------------------------------------------------------------------------------------------------------------------------------
ant\ant_multi_channels_encrypted\rx                                                     |          |    *     |           |          |
--------------------------------------------------------------------------------------------------------------------------------------
ant\ant_multi_channels_encrypted\tx                                                     |          |    *     |           |          |
--------------------------------------------------------------------------------------------------------------------------------------
ant\ant_plus\ant_bpwr\bpwr_rx                                                           |          |    *     |           |          |
--------------------------------------------------------------------------------------------------------------------------------------
ant\ant_plus\ant_bpwr\bpwr_tx                                                           |          |    *     |           |          |
--------------------------------------------------------------------------------------------------------------------------------------
ant\ant_plus\ant_bsc\bsc_rx                                                             |          |    *     |           |          |
--------------------------------------------------------------------------------------------------------------------------------------
ant\ant_plus\ant_bsc\bsc_tx                                                             |          |    *     |           |          |
--------------------------------------------------------------------------------------------------------------------------------------
ant\ant_plus\ant_hrm\hrm_rx                                                             |          |    *     |           |          |
--------------------------------------------------------------------------------------------------------------------------------------
ant\ant_plus\ant_hrm\hrm_tx                                                             |          |    *     |           |          |
--------------------------------------------------------------------------------------------------------------------------------------
ant\ant_plus\ant_sdm\sdm_rx                                                             |          |    *     |           |          |
--------------------------------------------------------------------------------------------------------------------------------------
ant\ant_plus\ant_sdm\sdm_tx                                                             |          |    *     |           |          |
--------------------------------------------------------------------------------------------------------------------------------------
ant\ant_relay_demo                                                                      |          |    *     |           |          |
--------------------------------------------------------------------------------------------------------------------------------------
ant\ant_scan_and_forward                                                                |          |    *     |           |          |
--------------------------------------------------------------------------------------------------------------------------------------
ant\ant_search_sharing                                                                  |          |    *     |           |          |
--------------------------------------------------------------------------------------------------------------------------------------
ant\ant_search_uplink                                                                   |          |    *     |           |          |
--------------------------------------------------------------------------------------------------------------------------------------
ant\ant_time_synchronization\rx                                                         |          |    *     |           |          |
--------------------------------------------------------------------------------------------------------------------------------------
ant\ant_time_synchronization\tx                                                         |          |    *     |           |          |
--------------------------------------------------------------------------------------------------------------------------------------
ant\experimental\ant_frequency_agility\ant_frequency_agility_rx                         |          |    *     |           |          |
--------------------------------------------------------------------------------------------------------------------------------------
ant\experimental\ant_frequency_agility\ant_frequency_agility_tx                         |          |    *     |           |          |
--------------------------------------------------------------------------------------------------------------------------------------
ant\experimental\ant_shared_channel\m2m                                                 |          |    *     |     *     |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
ant\experimental\ant_shared_channel\slave                                               |          |    *     |           |          |
--------------------------------------------------------------------------------------------------------------------------------------
ble_central\ble_app_blinky_c                                                            |          |    *     |           |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
ble_central\ble_app_gatts                                                               |          |    *     |           |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
ble_central\ble_app_hrs_c                                                               |          |    *     |           |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
ble_central\ble_app_ias                                                                 |          |    *     |           |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
ble_central\ble_app_ipsp_initiator                                                      |          |    *     |           |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
ble_central\ble_app_multilink_central                                                   |          |    *     |           |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
ble_central\ble_app_rscs_c                                                              |          |    *     |           |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
ble_central\ble_app_uart_c                                                              |          |    *     |           |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
ble_central\experimental\ble_app_hrs_nfc_c                                              |          |    *     |           |          |
--------------------------------------------------------------------------------------------------------------------------------------
ble_central\experimental\ble_app_ots_c                                                  |          |    *     |           |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
ble_central\experimental\ble_nfc_pairing_reference_c                                    |          |    *     |           |          |
--------------------------------------------------------------------------------------------------------------------------------------
ble_central_and_peripheral\experimental\ble_app_att_mtu_throughput                      |          |    *     |           |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
ble_central_and_peripheral\experimental\ble_app_hrs_rscs_relay                          |          |    *     |           |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
ble_central_and_peripheral\experimental\ble_app_interactive                             |          |    *     |           |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
ble_central_and_peripheral\experimental\ble_app_multirole_lesc                          |          |    *     |           |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
ble_peripheral\ble_app_alert_notification                                               |          |    *     |     *     |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
ble_peripheral\ble_app_ancs_c                                                           |          |    *     |     *     |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
ble_peripheral\ble_app_beacon                                                           |          |    *     |     *     |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
ble_peripheral\ble_app_blinky                                                           |          |    *     |     *     |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
ble_peripheral\ble_app_bms                                                              |          |    *     |     *     |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
ble_peripheral\ble_app_bps                                                              |          |    *     |     *     |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
ble_peripheral\ble_app_buttonless_dfu                                                   |          |    *     |     *     |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
ble_peripheral\ble_app_cscs                                                             |          |    *     |     *     |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
ble_peripheral\ble_app_cts_c                                                            |          |    *     |     *     |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
ble_peripheral\ble_app_eddystone                                                        |          |    *     |     *     |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
ble_peripheral\ble_app_gatts_c                                                          |          |    *     |     *     |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
ble_peripheral\ble_app_gls                                                              |          |    *     |           |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
ble_peripheral\ble_app_hids_keyboard                                                    |          |    *     |     *     |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
ble_peripheral\ble_app_hids_mouse                                                       |          |    *     |     *     |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
ble_peripheral\ble_app_hrs                                                              |          |    *     |     *     |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
ble_peripheral\ble_app_hrs_freertos                                                     |          |    *     |           |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
ble_peripheral\ble_app_hts                                                              |          |    *     |     *     |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
ble_peripheral\ble_app_ias_c                                                            |          |    *     |     *     |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
ble_peripheral\ble_app_ipsp_acceptor                                                    |          |    *     |           |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
ble_peripheral\ble_app_proximity                                                        |          |    *     |     *     |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
ble_peripheral\ble_app_pwr_profiling                                                    |          |    *     |     *     |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
ble_peripheral\ble_app_rscs                                                             |          |    *     |     *     |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
ble_peripheral\ble_app_template                                                         |          |    *     |     *     |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
ble_peripheral\ble_app_tile                                                             |          |    *     |           |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
ble_peripheral\ble_app_uart                                                             |          |    *     |     *     |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
ble_peripheral\experimental\ble_app_cgms                                                |          |    *     |           |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
ble_peripheral\experimental\ble_app_cli                                                 |          |    *     |           |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
ble_peripheral\experimental\ble_app_hrs_nfc_pairing                                     |          |    *     |           |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
ble_peripheral\experimental\ble_app_lls                                                 |          |    *     |     *     |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
ble_peripheral\experimental\ble_app_lns                                                 |          |    *     |     *     |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
ble_peripheral\experimental\ble_app_multiperipheral                                     |          |    *     |     *     |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
ble_peripheral\experimental\ble_app_ots                                                 |          |    *     |           |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
ble_peripheral\experimental\ble_app_queued_writes                                       |          |    *     |           |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
ble_peripheral\experimental\ble_hids_keyboard_pair_nfc                                  |          |    *     |           |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
ble_peripheral\experimental\ble_nfc_pairing_reference                                   |          |    *     |           |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
ble_peripheral\experimental\bluetoothds_template                                        |          |    *     |           |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
connectivity\ble_connectivity                                                           |          |    *     |     *     |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
connectivity\experimental_ant                                                           |          |    *     |           |          |
--------------------------------------------------------------------------------------------------------------------------------------
crypto\ifx_optiga_custom_example                                                        |          |    *     |           |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
crypto\nrf_cc310\aes                                                                    |          |          |           |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
crypto\nrf_cc310\chacha_poly                                                            |          |          |           |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
crypto\nrf_cc310\ecc                                                                    |          |          |           |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
crypto\nrf_cc310\hash                                                                   |          |          |           |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
crypto\nrf_cc310\hkdf                                                                   |          |          |           |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
crypto\nrf_cc310\hmac                                                                   |          |          |           |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
crypto\nrf_cc310\rnd                                                                    |          |          |           |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
crypto\nrf_cc310\rsa                                                                    |          |          |           |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
crypto\nrf_cc310_bl                                                                     |          |          |           |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
crypto\nrf_crypto\aes\aes_all_cli                                                       |          |    *     |           |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
crypto\nrf_crypto\aes\aes_cbc_mac                                                       |          |    *     |           |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
crypto\nrf_crypto\aes\aes_cbc_with_padding                                              |          |    *     |           |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
crypto\nrf_crypto\aes\aes_ccm                                                           |          |    *     |           |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
crypto\nrf_crypto\aes\aes_ctr                                                           |          |    *     |           |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
crypto\nrf_crypto\chacha_poly                                                           |          |    *     |           |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
crypto\nrf_crypto\cli                                                                   |          |    *     |           |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
crypto\nrf_crypto\ecdh                                                                  |          |    *     |           |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
crypto\nrf_crypto\ecdsa                                                                 |          |    *     |           |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
crypto\nrf_crypto\eddsa                                                                 |          |    *     |           |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
crypto\nrf_crypto\hash                                                                  |          |    *     |           |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
crypto\nrf_crypto\hkdf                                                                  |          |    *     |           |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
crypto\nrf_crypto\hmac                                                                  |          |    *     |           |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
crypto\nrf_crypto\rng                                                                   |          |    *     |           |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
crypto\nrf_crypto\test_app                                                              |          |    *     |           |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
dfu\open_bootloader                                                                     |          |          |           |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
dfu\secure_bootloader                                                                   |          |    *     |     *     |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
dtm\direct_test_mode                                                                    |          |    *     |     *     |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
dtm\dtm_serialization                                                                   |          |    *     |           |          |
--------------------------------------------------------------------------------------------------------------------------------------
multiprotocol\ble_ant_app_hrm                                                           |          |    *     |     *     |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
multiprotocol\ble_app_gzll                                                              |          |    *     |           |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
nfc\adafruit_tag_reader                                                                 |          |    *     |           |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
nfc\nfc_uart\poller                                                                     |          |    *     |           |          |
--------------------------------------------------------------------------------------------------------------------------------------
nfc\nfc_uart\tag                                                                        |          |    *     |           |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
nfc\record_launch_app                                                                   |          |    *     |           |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
nfc\record_text                                                                         |          |    *     |           |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
nfc\record_url                                                                          |          |    *     |           |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
nfc\wake_on_nfc                                                                         |          |    *     |           |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
nfc\writable_ndef_msg                                                                   |          |    *     |           |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
peripheral\blinky                                                                       |          |    *     |     *     |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
peripheral\blinky_freertos                                                              |          |    *     |           |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
peripheral\blinky_rtc_freertos                                                          |          |    *     |           |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
peripheral\blinky_systick                                                               |          |    *     |           |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
peripheral\bsp                                                                          |          |    *     |           |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
peripheral\cli                                                                          |          |    *     |           |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
peripheral\cli_libuarte                                                                 |          |    *     |           |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
peripheral\csense                                                                       |          |    *     |           |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
peripheral\csense_drv                                                                   |          |    *     |           |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
peripheral\fatfs                                                                        |          |    *     |           |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
peripheral\flash_fds                                                                    |          |    *     |           |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
peripheral\flash_fstorage                                                               |          |    *     |           |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
peripheral\flashwrite                                                                   |          |    *     |           |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
peripheral\fpu_fft                                                                      |          |    *     |           |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
peripheral\gfx                                                                          |          |    *     |           |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
peripheral\gpiote                                                                       |          |    *     |           |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
peripheral\i2s                                                                          |          |    *     |           |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
peripheral\led_softblink                                                                |          |    *     |           |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
peripheral\libuarte                                                                     |          |    *     |           |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
peripheral\low_power_pwm                                                                |          |    *     |           |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
peripheral\lpcomp                                                                       |          |    *     |           |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
peripheral\nrfx_spim                                                                    |          |          |           |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
peripheral\pin_change_int                                                               |          |    *     |           |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
peripheral\ppi                                                                          |          |    *     |           |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
peripheral\preflash                                                                     |          |    *     |           |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
peripheral\pwm_driver                                                                   |          |    *     |           |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
peripheral\pwm_library                                                                  |          |    *     |           |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
peripheral\pwr_mgmt                                                                     |          |    *     |           |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
peripheral\qdec                                                                         |          |    *     |           |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
peripheral\qspi                                                                         |          |          |           |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
peripheral\qspi_bootloader                                                              |          |          |           |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
peripheral\radio\receiver                                                               |          |    *     |           |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
peripheral\radio\transmitter                                                            |          |    *     |           |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
peripheral\radio_test                                                                   |          |    *     |           |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
peripheral\ram_retention                                                                |          |    *     |           |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
peripheral\rng                                                                          |          |    *     |           |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
peripheral\rtc                                                                          |          |    *     |           |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
peripheral\saadc                                                                        |          |    *     |     *     |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
peripheral\simple_timer                                                                 |          |    *     |           |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
peripheral\spi                                                                          |          |    *     |           |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
peripheral\spi_master_using_nrf_spi_mngr                                                |          |    *     |           |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
peripheral\spis                                                                         |          |    *     |           |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
peripheral\temperature                                                                  |          |    *     |           |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
peripheral\template_project                                                             |          |    *     |           |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
peripheral\timer                                                                        |          |    *     |           |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
peripheral\twi_master_using_nrf_twi_mngr                                                |          |    *     |           |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
peripheral\twi_master_with_twis_slave                                                   |          |    *     |           |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
peripheral\twi_scanner                                                                  |          |    *     |           |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
peripheral\twi_sensor                                                                   |          |    *     |           |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
peripheral\uart                                                                         |          |    *     |     *     |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
peripheral\uicr_config                                                                  |          |    *     |           |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
peripheral\usbd                                                                         |          |          |           |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
peripheral\usbd_audio                                                                   |          |          |           |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
peripheral\usbd_ble_uart                                                                |          |          |           |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
peripheral\usbd_ble_uart_freertos                                                       |          |          |           |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
peripheral\usbd_cdc_acm                                                                 |          |          |           |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
peripheral\usbd_hid_composite                                                           |          |          |           |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
peripheral\usbd_hid_generic                                                             |          |          |           |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
peripheral\usbd_msc                                                                     |          |          |           |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
peripheral\wdt                                                                          |          |    *     |           |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
proprietary_rf\esb_low_power_prx                                                        |          |    *     |     *     |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
proprietary_rf\esb_low_power_ptx                                                        |          |    *     |     *     |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
proprietary_rf\esb_prx                                                                  |          |    *     |     *     |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
proprietary_rf\esb_ptx                                                                  |          |    *     |     *     |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
proprietary_rf\gzll\gzll_ack_payload\device                                             |          |    *     |           |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
proprietary_rf\gzll\gzll_ack_payload\host                                               |          |    *     |           |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
proprietary_rf\gzll\gzp_desktop_emulator                                                |          |    *     |           |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
proprietary_rf\gzll\gzp_dynamic_pairing\device                                          |          |    *     |           |    *     |
--------------------------------------------------------------------------------------------------------------------------------------
proprietary_rf\gzll\gzp_dynamic_pairing\host                                            |          |    *     |           |    *     |
--------------------------------------------------------------------------------------------------------------------------------------


Matrix shows which SoftDevice is supported by given example

Example                                                                                 | s112 | s130 | s132 | s140 | s212 | None |
-----------------------------------------------------------------------------------------------------------------------------------
802_15_4\wireless_uart\raw\first                                                        |      |      |      |      |      |  *   |
-----------------------------------------------------------------------------------------------------------------------------------
802_15_4\wireless_uart\raw\second                                                       |      |      |      |      |      |  *   |
-----------------------------------------------------------------------------------------------------------------------------------
802_15_4\wireless_uart\secure\first                                                     |      |      |      |      |      |  *   |
-----------------------------------------------------------------------------------------------------------------------------------
802_15_4\wireless_uart\secure\second                                                    |      |      |      |      |      |  *   |
-----------------------------------------------------------------------------------------------------------------------------------
C:\bambuild\HUSS-RWCRWH1-CR\sdk\nrf5\components\libraries\csense_drv\test               |      |      |      |      |      |  *   |
-----------------------------------------------------------------------------------------------------------------------------------
C:\bambuild\HUSS-RWCRWH1-CR\sdk\nrf5\components\libraries\usbd\test\usb_hosted\example  |      |      |      |      |      |  *   |
-----------------------------------------------------------------------------------------------------------------------------------
C:\bambuild\HUSS-RWCRWH1-CR\sdk\nrf5\config                                             |  *   |      |      |  *   |      |  *   |
-----------------------------------------------------------------------------------------------------------------------------------
ant\ant_advanced_burst                                                                  |      |      |      |      |  *   |      |
-----------------------------------------------------------------------------------------------------------------------------------
ant\ant_async_transmitter                                                               |      |      |      |      |  *   |      |
-----------------------------------------------------------------------------------------------------------------------------------
ant\ant_background_scanning                                                             |      |      |      |      |  *   |      |
-----------------------------------------------------------------------------------------------------------------------------------
ant\ant_broadcast\rx                                                                    |      |      |      |      |  *   |      |
-----------------------------------------------------------------------------------------------------------------------------------
ant\ant_broadcast\tx                                                                    |      |      |      |      |  *   |      |
-----------------------------------------------------------------------------------------------------------------------------------
ant\ant_continuous_scanning_controller                                                  |      |      |      |      |  *   |      |
-----------------------------------------------------------------------------------------------------------------------------------
ant\ant_cw_mode                                                                         |      |      |      |      |  *   |      |
-----------------------------------------------------------------------------------------------------------------------------------
ant\ant_debug                                                                           |      |      |      |      |  *   |      |
-----------------------------------------------------------------------------------------------------------------------------------
ant\ant_fs\client                                                                       |      |      |      |      |  *   |      |
-----------------------------------------------------------------------------------------------------------------------------------
ant\ant_hd_search_and_bs                                                                |      |      |      |      |  *   |      |
-----------------------------------------------------------------------------------------------------------------------------------
ant\ant_io_demo\ant_io_rx                                                               |      |      |      |      |  *   |      |
-----------------------------------------------------------------------------------------------------------------------------------
ant\ant_io_demo\ant_io_tx                                                               |      |      |      |      |  *   |      |
-----------------------------------------------------------------------------------------------------------------------------------
ant\ant_message_types\master                                                            |      |      |      |      |  *   |  *   |
-----------------------------------------------------------------------------------------------------------------------------------
ant\ant_multi_channels\rx                                                               |      |      |      |      |  *   |      |
-----------------------------------------------------------------------------------------------------------------------------------
ant\ant_multi_channels\tx                                                               |      |      |      |      |  *   |      |
-----------------------------------------------------------------------------------------------------------------------------------
ant\ant_multi_channels_encrypted\rx                                                     |      |      |      |      |  *   |      |
-----------------------------------------------------------------------------------------------------------------------------------
ant\ant_multi_channels_encrypted\tx                                                     |      |      |      |      |  *   |      |
-----------------------------------------------------------------------------------------------------------------------------------
ant\ant_plus\ant_bpwr\bpwr_rx                                                           |      |      |      |      |  *   |      |
-----------------------------------------------------------------------------------------------------------------------------------
ant\ant_plus\ant_bpwr\bpwr_tx                                                           |      |      |      |      |  *   |      |
-----------------------------------------------------------------------------------------------------------------------------------
ant\ant_plus\ant_bsc\bsc_rx                                                             |      |      |      |      |  *   |      |
-----------------------------------------------------------------------------------------------------------------------------------
ant\ant_plus\ant_bsc\bsc_tx                                                             |      |      |      |      |  *   |      |
-----------------------------------------------------------------------------------------------------------------------------------
ant\ant_plus\ant_hrm\hrm_rx                                                             |      |      |      |      |  *   |      |
-----------------------------------------------------------------------------------------------------------------------------------
ant\ant_plus\ant_hrm\hrm_tx                                                             |      |      |      |      |  *   |      |
-----------------------------------------------------------------------------------------------------------------------------------
ant\ant_plus\ant_sdm\sdm_rx                                                             |      |      |      |      |  *   |      |
-----------------------------------------------------------------------------------------------------------------------------------
ant\ant_plus\ant_sdm\sdm_tx                                                             |      |      |      |      |  *   |      |
-----------------------------------------------------------------------------------------------------------------------------------
ant\ant_relay_demo                                                                      |      |      |      |      |  *   |      |
-----------------------------------------------------------------------------------------------------------------------------------
ant\ant_scan_and_forward                                                                |      |      |      |      |  *   |      |
-----------------------------------------------------------------------------------------------------------------------------------
ant\ant_search_sharing                                                                  |      |      |      |      |  *   |      |
-----------------------------------------------------------------------------------------------------------------------------------
ant\ant_search_uplink                                                                   |      |      |      |      |  *   |      |
-----------------------------------------------------------------------------------------------------------------------------------
ant\ant_time_synchronization\rx                                                         |      |      |      |      |  *   |      |
-----------------------------------------------------------------------------------------------------------------------------------
ant\ant_time_synchronization\tx                                                         |      |      |      |      |  *   |      |
-----------------------------------------------------------------------------------------------------------------------------------
ant\experimental\ant_frequency_agility\ant_frequency_agility_rx                         |      |      |      |      |  *   |      |
-----------------------------------------------------------------------------------------------------------------------------------
ant\experimental\ant_frequency_agility\ant_frequency_agility_tx                         |      |      |      |      |  *   |      |
-----------------------------------------------------------------------------------------------------------------------------------
ant\experimental\ant_shared_channel\m2m                                                 |      |      |      |      |      |  *   |
-----------------------------------------------------------------------------------------------------------------------------------
ant\experimental\ant_shared_channel\slave                                               |      |      |      |      |  *   |      |
-----------------------------------------------------------------------------------------------------------------------------------
ble_central\ble_app_blinky_c                                                            |      |      |  *   |  *   |      |      |
-----------------------------------------------------------------------------------------------------------------------------------
ble_central\ble_app_gatts                                                               |      |      |  *   |  *   |      |      |
-----------------------------------------------------------------------------------------------------------------------------------
ble_central\ble_app_hrs_c                                                               |      |      |  *   |  *   |      |  *   |
-----------------------------------------------------------------------------------------------------------------------------------
ble_central\ble_app_ias                                                                 |      |      |  *   |  *   |      |      |
-----------------------------------------------------------------------------------------------------------------------------------
ble_central\ble_app_ipsp_initiator                                                      |      |      |  *   |  *   |      |      |
-----------------------------------------------------------------------------------------------------------------------------------
ble_central\ble_app_multilink_central                                                   |      |      |  *   |  *   |      |      |
-----------------------------------------------------------------------------------------------------------------------------------
ble_central\ble_app_rscs_c                                                              |      |      |  *   |  *   |      |      |
-----------------------------------------------------------------------------------------------------------------------------------
ble_central\ble_app_uart_c                                                              |      |      |  *   |  *   |      |  *   |
-----------------------------------------------------------------------------------------------------------------------------------
ble_central\experimental\ble_app_hrs_nfc_c                                              |      |      |  *   |      |      |      |
-----------------------------------------------------------------------------------------------------------------------------------
ble_central\experimental\ble_app_ots_c                                                  |      |      |  *   |  *   |      |      |
-----------------------------------------------------------------------------------------------------------------------------------
ble_central\experimental\ble_nfc_pairing_reference_c                                    |      |      |  *   |      |      |      |
-----------------------------------------------------------------------------------------------------------------------------------
ble_central_and_peripheral\experimental\ble_app_att_mtu_throughput                      |      |      |  *   |  *   |      |      |
-----------------------------------------------------------------------------------------------------------------------------------
ble_central_and_peripheral\experimental\ble_app_hrs_rscs_relay                          |      |      |  *   |  *   |      |      |
-----------------------------------------------------------------------------------------------------------------------------------
ble_central_and_peripheral\experimental\ble_app_interactive                             |      |      |  *   |  *   |      |      |
-----------------------------------------------------------------------------------------------------------------------------------
ble_central_and_peripheral\experimental\ble_app_multirole_lesc                          |      |      |  *   |  *   |      |      |
-----------------------------------------------------------------------------------------------------------------------------------
ble_peripheral\ble_app_alert_notification                                               |  *   |      |  *   |  *   |      |      |
-----------------------------------------------------------------------------------------------------------------------------------
ble_peripheral\ble_app_ancs_c                                                           |  *   |      |  *   |  *   |      |      |
-----------------------------------------------------------------------------------------------------------------------------------
ble_peripheral\ble_app_beacon                                                           |  *   |      |  *   |  *   |      |      |
-----------------------------------------------------------------------------------------------------------------------------------
ble_peripheral\ble_app_blinky                                                           |  *   |      |  *   |  *   |      |      |
-----------------------------------------------------------------------------------------------------------------------------------
ble_peripheral\ble_app_bms                                                              |  *   |      |  *   |  *   |      |      |
-----------------------------------------------------------------------------------------------------------------------------------
ble_peripheral\ble_app_bps                                                              |  *   |      |  *   |  *   |      |      |
-----------------------------------------------------------------------------------------------------------------------------------
ble_peripheral\ble_app_buttonless_dfu                                                   |  *   |      |  *   |  *   |      |      |
-----------------------------------------------------------------------------------------------------------------------------------
ble_peripheral\ble_app_cscs                                                             |  *   |      |  *   |  *   |      |      |
-----------------------------------------------------------------------------------------------------------------------------------
ble_peripheral\ble_app_cts_c                                                            |  *   |      |  *   |  *   |      |      |
-----------------------------------------------------------------------------------------------------------------------------------
ble_peripheral\ble_app_eddystone                                                        |  *   |      |  *   |  *   |      |      |
-----------------------------------------------------------------------------------------------------------------------------------
ble_peripheral\ble_app_gatts_c                                                          |  *   |      |  *   |  *   |      |      |
-----------------------------------------------------------------------------------------------------------------------------------
ble_peripheral\ble_app_gls                                                              |      |      |  *   |  *   |      |      |
-----------------------------------------------------------------------------------------------------------------------------------
ble_peripheral\ble_app_hids_keyboard                                                    |  *   |      |  *   |  *   |      |  *   |
-----------------------------------------------------------------------------------------------------------------------------------
ble_peripheral\ble_app_hids_mouse                                                       |  *   |      |  *   |  *   |      |      |
-----------------------------------------------------------------------------------------------------------------------------------
ble_peripheral\ble_app_hrs                                                              |  *   |      |  *   |  *   |      |  *   |
-----------------------------------------------------------------------------------------------------------------------------------
ble_peripheral\ble_app_hrs_freertos                                                     |      |      |  *   |  *   |      |      |
-----------------------------------------------------------------------------------------------------------------------------------
ble_peripheral\ble_app_hts                                                              |  *   |      |  *   |  *   |      |      |
-----------------------------------------------------------------------------------------------------------------------------------
ble_peripheral\ble_app_ias_c                                                            |  *   |      |  *   |  *   |      |      |
-----------------------------------------------------------------------------------------------------------------------------------
ble_peripheral\ble_app_ipsp_acceptor                                                    |      |      |  *   |  *   |      |      |
-----------------------------------------------------------------------------------------------------------------------------------
ble_peripheral\ble_app_proximity                                                        |  *   |      |  *   |  *   |      |      |
-----------------------------------------------------------------------------------------------------------------------------------
ble_peripheral\ble_app_pwr_profiling                                                    |  *   |      |  *   |  *   |      |      |
-----------------------------------------------------------------------------------------------------------------------------------
ble_peripheral\ble_app_rscs                                                             |  *   |      |  *   |  *   |      |      |
-----------------------------------------------------------------------------------------------------------------------------------
ble_peripheral\ble_app_template                                                         |  *   |      |  *   |  *   |      |      |
-----------------------------------------------------------------------------------------------------------------------------------
ble_peripheral\ble_app_tile                                                             |      |      |  *   |  *   |      |      |
-----------------------------------------------------------------------------------------------------------------------------------
ble_peripheral\ble_app_uart                                                             |  *   |      |  *   |  *   |      |  *   |
-----------------------------------------------------------------------------------------------------------------------------------
ble_peripheral\experimental\ble_app_cgms                                                |      |      |  *   |  *   |      |      |
-----------------------------------------------------------------------------------------------------------------------------------
ble_peripheral\experimental\ble_app_cli                                                 |      |      |  *   |  *   |      |      |
-----------------------------------------------------------------------------------------------------------------------------------
ble_peripheral\experimental\ble_app_hrs_nfc_pairing                                     |  *   |      |  *   |  *   |      |  *   |
-----------------------------------------------------------------------------------------------------------------------------------
ble_peripheral\experimental\ble_app_lls                                                 |  *   |      |  *   |  *   |      |      |
-----------------------------------------------------------------------------------------------------------------------------------
ble_peripheral\experimental\ble_app_lns                                                 |  *   |      |  *   |  *   |      |      |
-----------------------------------------------------------------------------------------------------------------------------------
ble_peripheral\experimental\ble_app_multiperipheral                                     |  *   |      |  *   |  *   |      |      |
-----------------------------------------------------------------------------------------------------------------------------------
ble_peripheral\experimental\ble_app_ots                                                 |      |      |  *   |  *   |      |      |
-----------------------------------------------------------------------------------------------------------------------------------
ble_peripheral\experimental\ble_app_queued_writes                                       |      |      |  *   |  *   |      |      |
-----------------------------------------------------------------------------------------------------------------------------------
ble_peripheral\experimental\ble_hids_keyboard_pair_nfc                                  |      |      |  *   |  *   |      |      |
-----------------------------------------------------------------------------------------------------------------------------------
ble_peripheral\experimental\ble_nfc_pairing_reference                                   |      |      |  *   |  *   |      |      |
-----------------------------------------------------------------------------------------------------------------------------------
ble_peripheral\experimental\bluetoothds_template                                        |      |      |  *   |  *   |      |      |
-----------------------------------------------------------------------------------------------------------------------------------
connectivity\ble_connectivity                                                           |  *   |      |  *   |  *   |      |      |
-----------------------------------------------------------------------------------------------------------------------------------
connectivity\experimental_ant                                                           |      |      |      |      |  *   |      |
-----------------------------------------------------------------------------------------------------------------------------------
crypto\ifx_optiga_custom_example                                                        |      |      |      |      |      |  *   |
-----------------------------------------------------------------------------------------------------------------------------------
crypto\nrf_cc310\aes                                                                    |      |      |      |      |      |  *   |
-----------------------------------------------------------------------------------------------------------------------------------
crypto\nrf_cc310\chacha_poly                                                            |      |      |      |      |      |  *   |
-----------------------------------------------------------------------------------------------------------------------------------
crypto\nrf_cc310\ecc                                                                    |      |      |      |      |      |  *   |
-----------------------------------------------------------------------------------------------------------------------------------
crypto\nrf_cc310\hash                                                                   |      |      |      |      |      |  *   |
-----------------------------------------------------------------------------------------------------------------------------------
crypto\nrf_cc310\hkdf                                                                   |      |      |      |      |      |  *   |
-----------------------------------------------------------------------------------------------------------------------------------
crypto\nrf_cc310\hmac                                                                   |      |      |      |      |      |  *   |
-----------------------------------------------------------------------------------------------------------------------------------
crypto\nrf_cc310\rnd                                                                    |      |      |      |      |      |  *   |
-----------------------------------------------------------------------------------------------------------------------------------
crypto\nrf_cc310\rsa                                                                    |      |      |      |      |      |  *   |
-----------------------------------------------------------------------------------------------------------------------------------
crypto\nrf_cc310_bl                                                                     |      |      |      |      |      |  *   |
-----------------------------------------------------------------------------------------------------------------------------------
crypto\nrf_crypto\aes\aes_all_cli                                                       |      |      |      |      |      |  *   |
-----------------------------------------------------------------------------------------------------------------------------------
crypto\nrf_crypto\aes\aes_cbc_mac                                                       |      |      |      |      |      |  *   |
-----------------------------------------------------------------------------------------------------------------------------------
crypto\nrf_crypto\aes\aes_cbc_with_padding                                              |      |      |      |      |      |  *   |
-----------------------------------------------------------------------------------------------------------------------------------
crypto\nrf_crypto\aes\aes_ccm                                                           |      |      |      |      |      |  *   |
-----------------------------------------------------------------------------------------------------------------------------------
crypto\nrf_crypto\aes\aes_ctr                                                           |      |      |      |      |      |  *   |
-----------------------------------------------------------------------------------------------------------------------------------
crypto\nrf_crypto\chacha_poly                                                           |      |      |      |      |      |  *   |
-----------------------------------------------------------------------------------------------------------------------------------
crypto\nrf_crypto\cli                                                                   |      |      |      |      |      |  *   |
-----------------------------------------------------------------------------------------------------------------------------------
crypto\nrf_crypto\ecdh                                                                  |      |      |      |      |      |  *   |
-----------------------------------------------------------------------------------------------------------------------------------
crypto\nrf_crypto\ecdsa                                                                 |      |      |      |      |      |  *   |
-----------------------------------------------------------------------------------------------------------------------------------
crypto\nrf_crypto\eddsa                                                                 |      |      |      |      |      |  *   |
-----------------------------------------------------------------------------------------------------------------------------------
crypto\nrf_crypto\hash                                                                  |      |      |      |      |      |  *   |
-----------------------------------------------------------------------------------------------------------------------------------
crypto\nrf_crypto\hkdf                                                                  |      |      |      |      |      |  *   |
-----------------------------------------------------------------------------------------------------------------------------------
crypto\nrf_crypto\hmac                                                                  |      |      |      |      |      |  *   |
-----------------------------------------------------------------------------------------------------------------------------------
crypto\nrf_crypto\rng                                                                   |      |      |      |      |      |  *   |
-----------------------------------------------------------------------------------------------------------------------------------
crypto\nrf_crypto\test_app                                                              |      |      |      |      |      |  *   |
-----------------------------------------------------------------------------------------------------------------------------------
dfu\open_bootloader                                                                     |      |      |      |      |      |  *   |
-----------------------------------------------------------------------------------------------------------------------------------
dfu\secure_bootloader                                                                   |  *   |      |  *   |  *   |  *   |  *   |
-----------------------------------------------------------------------------------------------------------------------------------
dtm\direct_test_mode                                                                    |      |      |      |      |      |  *   |
-----------------------------------------------------------------------------------------------------------------------------------
dtm\dtm_serialization                                                                   |      |      |  *   |      |      |      |
-----------------------------------------------------------------------------------------------------------------------------------
multiprotocol\ble_ant_app_hrm                                                           |      |      |      |      |      |  *   |
-----------------------------------------------------------------------------------------------------------------------------------
multiprotocol\ble_app_gzll                                                              |      |      |  *   |  *   |      |      |
-----------------------------------------------------------------------------------------------------------------------------------
nfc\adafruit_tag_reader                                                                 |      |      |      |      |      |  *   |
-----------------------------------------------------------------------------------------------------------------------------------
nfc\nfc_uart\poller                                                                     |      |      |      |      |      |  *   |
-----------------------------------------------------------------------------------------------------------------------------------
nfc\nfc_uart\tag                                                                        |      |      |      |      |      |  *   |
-----------------------------------------------------------------------------------------------------------------------------------
nfc\record_launch_app                                                                   |      |      |      |      |      |  *   |
-----------------------------------------------------------------------------------------------------------------------------------
nfc\record_text                                                                         |      |      |      |      |      |  *   |
-----------------------------------------------------------------------------------------------------------------------------------
nfc\record_url                                                                          |      |      |      |      |      |  *   |
-----------------------------------------------------------------------------------------------------------------------------------
nfc\wake_on_nfc                                                                         |      |      |      |      |      |  *   |
-----------------------------------------------------------------------------------------------------------------------------------
nfc\writable_ndef_msg                                                                   |      |      |      |      |      |  *   |
-----------------------------------------------------------------------------------------------------------------------------------
peripheral\blinky                                                                       |      |      |  *   |      |      |  *   |
-----------------------------------------------------------------------------------------------------------------------------------
peripheral\blinky_freertos                                                              |      |      |      |      |      |  *   |
-----------------------------------------------------------------------------------------------------------------------------------
peripheral\blinky_rtc_freertos                                                          |      |      |      |      |      |  *   |
-----------------------------------------------------------------------------------------------------------------------------------
peripheral\blinky_systick                                                               |      |      |      |      |      |  *   |
-----------------------------------------------------------------------------------------------------------------------------------
peripheral\bsp                                                                          |      |      |      |      |      |  *   |
-----------------------------------------------------------------------------------------------------------------------------------
peripheral\cli                                                                          |      |      |      |      |      |  *   |
-----------------------------------------------------------------------------------------------------------------------------------
peripheral\cli_libuarte                                                                 |      |      |      |      |      |  *   |
-----------------------------------------------------------------------------------------------------------------------------------
peripheral\csense                                                                       |      |      |      |      |      |  *   |
-----------------------------------------------------------------------------------------------------------------------------------
peripheral\csense_drv                                                                   |      |      |      |      |      |  *   |
-----------------------------------------------------------------------------------------------------------------------------------
peripheral\fatfs                                                                        |      |      |      |      |      |  *   |
-----------------------------------------------------------------------------------------------------------------------------------
peripheral\flash_fds                                                                    |      |      |  *   |  *   |      |  *   |
-----------------------------------------------------------------------------------------------------------------------------------
peripheral\flash_fstorage                                                               |      |      |  *   |  *   |      |  *   |
-----------------------------------------------------------------------------------------------------------------------------------
peripheral\flashwrite                                                                   |      |      |      |      |      |  *   |
-----------------------------------------------------------------------------------------------------------------------------------
peripheral\fpu_fft                                                                      |      |      |  *   |  *   |      |  *   |
-----------------------------------------------------------------------------------------------------------------------------------
peripheral\gfx                                                                          |      |      |      |      |      |  *   |
-----------------------------------------------------------------------------------------------------------------------------------
peripheral\gpiote                                                                       |      |      |      |      |      |  *   |
-----------------------------------------------------------------------------------------------------------------------------------
peripheral\i2s                                                                          |      |      |      |      |      |  *   |
-----------------------------------------------------------------------------------------------------------------------------------
peripheral\led_softblink                                                                |      |      |      |      |      |  *   |
-----------------------------------------------------------------------------------------------------------------------------------
peripheral\libuarte                                                                     |      |      |      |      |      |  *   |
-----------------------------------------------------------------------------------------------------------------------------------
peripheral\low_power_pwm                                                                |      |      |      |      |      |  *   |
-----------------------------------------------------------------------------------------------------------------------------------
peripheral\lpcomp                                                                       |      |      |      |      |      |  *   |
-----------------------------------------------------------------------------------------------------------------------------------
peripheral\nrfx_spim                                                                    |      |      |      |      |      |  *   |
-----------------------------------------------------------------------------------------------------------------------------------
peripheral\pin_change_int                                                               |      |      |      |      |      |  *   |
-----------------------------------------------------------------------------------------------------------------------------------
peripheral\ppi                                                                          |      |      |      |      |      |  *   |
-----------------------------------------------------------------------------------------------------------------------------------
peripheral\preflash                                                                     |      |      |      |      |      |  *   |
-----------------------------------------------------------------------------------------------------------------------------------
peripheral\pwm_driver                                                                   |      |      |      |      |      |  *   |
-----------------------------------------------------------------------------------------------------------------------------------
peripheral\pwm_library                                                                  |      |      |      |      |      |  *   |
-----------------------------------------------------------------------------------------------------------------------------------
peripheral\pwr_mgmt                                                                     |      |      |      |      |      |  *   |
-----------------------------------------------------------------------------------------------------------------------------------
peripheral\qdec                                                                         |      |      |      |      |      |  *   |
-----------------------------------------------------------------------------------------------------------------------------------
peripheral\qspi                                                                         |      |      |      |      |      |  *   |
-----------------------------------------------------------------------------------------------------------------------------------
peripheral\qspi_bootloader                                                              |      |      |      |      |      |  *   |
-----------------------------------------------------------------------------------------------------------------------------------
peripheral\radio\receiver                                                               |      |      |      |      |      |  *   |
-----------------------------------------------------------------------------------------------------------------------------------
peripheral\radio\transmitter                                                            |      |      |      |      |      |  *   |
-----------------------------------------------------------------------------------------------------------------------------------
peripheral\radio_test                                                                   |      |      |      |      |      |  *   |
-----------------------------------------------------------------------------------------------------------------------------------
peripheral\ram_retention                                                                |      |      |      |      |      |  *   |
-----------------------------------------------------------------------------------------------------------------------------------
peripheral\rng                                                                          |      |      |      |      |      |  *   |
-----------------------------------------------------------------------------------------------------------------------------------
peripheral\rtc                                                                          |      |      |      |      |      |  *   |
-----------------------------------------------------------------------------------------------------------------------------------
peripheral\saadc                                                                        |      |      |      |      |      |  *   |
-----------------------------------------------------------------------------------------------------------------------------------
peripheral\simple_timer                                                                 |      |      |      |      |      |  *   |
-----------------------------------------------------------------------------------------------------------------------------------
peripheral\spi                                                                          |      |      |      |      |      |  *   |
-----------------------------------------------------------------------------------------------------------------------------------
peripheral\spi_master_using_nrf_spi_mngr                                                |      |      |      |      |      |  *   |
-----------------------------------------------------------------------------------------------------------------------------------
peripheral\spis                                                                         |      |      |      |      |      |  *   |
-----------------------------------------------------------------------------------------------------------------------------------
peripheral\temperature                                                                  |      |      |      |      |      |  *   |
-----------------------------------------------------------------------------------------------------------------------------------
peripheral\template_project                                                             |      |      |      |      |      |  *   |
-----------------------------------------------------------------------------------------------------------------------------------
peripheral\timer                                                                        |      |      |      |      |      |  *   |
-----------------------------------------------------------------------------------------------------------------------------------
peripheral\twi_master_using_nrf_twi_mngr                                                |      |      |      |      |      |  *   |
-----------------------------------------------------------------------------------------------------------------------------------
peripheral\twi_master_with_twis_slave                                                   |      |      |      |      |      |  *   |
-----------------------------------------------------------------------------------------------------------------------------------
peripheral\twi_scanner                                                                  |      |      |      |      |      |  *   |
-----------------------------------------------------------------------------------------------------------------------------------
peripheral\twi_sensor                                                                   |      |      |      |      |      |  *   |
-----------------------------------------------------------------------------------------------------------------------------------
peripheral\uart                                                                         |      |      |      |      |      |  *   |
-----------------------------------------------------------------------------------------------------------------------------------
peripheral\uicr_config                                                                  |      |      |      |      |      |  *   |
-----------------------------------------------------------------------------------------------------------------------------------
peripheral\usbd                                                                         |      |      |      |      |      |  *   |
-----------------------------------------------------------------------------------------------------------------------------------
peripheral\usbd_audio                                                                   |      |      |      |      |      |  *   |
-----------------------------------------------------------------------------------------------------------------------------------
peripheral\usbd_ble_uart                                                                |      |      |      |  *   |      |  *   |
-----------------------------------------------------------------------------------------------------------------------------------
peripheral\usbd_ble_uart_freertos                                                       |      |      |      |  *   |      |      |
-----------------------------------------------------------------------------------------------------------------------------------
peripheral\usbd_cdc_acm                                                                 |      |      |      |      |      |  *   |
-----------------------------------------------------------------------------------------------------------------------------------
peripheral\usbd_hid_composite                                                           |      |      |      |      |      |  *   |
-----------------------------------------------------------------------------------------------------------------------------------
peripheral\usbd_hid_generic                                                             |      |      |      |      |      |  *   |
-----------------------------------------------------------------------------------------------------------------------------------
peripheral\usbd_msc                                                                     |      |      |      |      |      |  *   |
-----------------------------------------------------------------------------------------------------------------------------------
peripheral\wdt                                                                          |      |      |      |      |      |  *   |
-----------------------------------------------------------------------------------------------------------------------------------
proprietary_rf\esb_low_power_prx                                                        |      |      |      |      |      |  *   |
-----------------------------------------------------------------------------------------------------------------------------------
proprietary_rf\esb_low_power_ptx                                                        |      |      |      |      |      |  *   |
-----------------------------------------------------------------------------------------------------------------------------------
proprietary_rf\esb_prx                                                                  |      |      |      |      |      |  *   |
-----------------------------------------------------------------------------------------------------------------------------------
proprietary_rf\esb_ptx                                                                  |      |      |      |      |      |  *   |
-----------------------------------------------------------------------------------------------------------------------------------
proprietary_rf\gzll\gzll_ack_payload\device                                             |      |      |      |      |      |  *   |
-----------------------------------------------------------------------------------------------------------------------------------
proprietary_rf\gzll\gzll_ack_payload\host                                               |      |      |      |      |      |  *   |
-----------------------------------------------------------------------------------------------------------------------------------
proprietary_rf\gzll\gzp_desktop_emulator                                                |      |      |      |      |      |  *   |
-----------------------------------------------------------------------------------------------------------------------------------
proprietary_rf\gzll\gzp_dynamic_pairing\device                                          |      |      |      |      |      |  *   |
-----------------------------------------------------------------------------------------------------------------------------------
proprietary_rf\gzll\gzp_dynamic_pairing\host                                            |      |      |      |      |      |  *   |
-----------------------------------------------------------------------------------------------------------------------------------
