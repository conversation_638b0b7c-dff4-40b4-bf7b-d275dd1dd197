<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=iso-8859-1">
<title>The Protothreads Library 1.4: lc-switch.h File Reference</title>
<link href="doxygen.css" rel="stylesheet" type="text/css">
<link href="tabs.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.4.6 -->
<div class="tabs">
  <ul>
    <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
    <li><a href="modules.html"><span>Modules</span></a></li>
    <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
    <li id="current"><a href="files.html"><span>Files</span></a></li>
  </ul></div>
<div class="tabs">
  <ul>
    <li><a href="files.html"><span>File&nbsp;List</span></a></li>
    <li><a href="globals.html"><span>Globals</span></a></li>
  </ul></div>
<h1>lc-switch.h File Reference</h1><hr><a name="_details"></a><h2>Detailed Description</h2>
Implementation of local continuations based on switch() statment. 
<p>
<dl compact><dt><b>Author:</b></dt><dd>Adam Dunkels &lt;<a href="mailto:<EMAIL>"><EMAIL></a>&gt;</dd></dl>
This implementation of local continuations uses the C switch() statement to resume execution of a function somewhere inside the function's body. The implementation is based on the fact that switch() statements are able to jump directly into the bodies of control structures such as if() or while() statmenets.<p>
This implementation borrows heavily from Simon Tatham's coroutines implementation in C: <a href="http://www.chiark.greenend.org.uk/~sgtatham/coroutines.html">http://www.chiark.greenend.org.uk/~sgtatham/coroutines.html</a> 
<p>
Definition in file <a class="el" href="a00019.html">lc-switch.h</a>.
<p>

<p>
<a href="a00019.html">Go to the source code of this file.</a><table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Defines</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="anchor" name="g2c1bb4fa6d7a6ff951a41c73fc721109"></a><!-- doxytag: member="lc-switch.h::LC_INIT" ref="g2c1bb4fa6d7a6ff951a41c73fc721109" args="(s)" -->
#define&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00017.html#g2c1bb4fa6d7a6ff951a41c73fc721109">LC_INIT</a>(s)&nbsp;&nbsp;&nbsp;s = 0;</td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="anchor" name="g1ec8b8f4710dce1fa7fb87d3a31541ae"></a><!-- doxytag: member="lc-switch.h::LC_RESUME" ref="g1ec8b8f4710dce1fa7fb87d3a31541ae" args="(s)" -->
#define&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00017.html#g1ec8b8f4710dce1fa7fb87d3a31541ae">LC_RESUME</a>(s)&nbsp;&nbsp;&nbsp;switch(s) { case 0:</td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="anchor" name="gd8eec328a4868d767f0c00c8d1c6cfc1"></a><!-- doxytag: member="lc-switch.h::LC_SET" ref="gd8eec328a4868d767f0c00c8d1c6cfc1" args="(s)" -->
#define&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00017.html#gd8eec328a4868d767f0c00c8d1c6cfc1">LC_SET</a>(s)&nbsp;&nbsp;&nbsp;s = __LINE__; case __LINE__:</td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="anchor" name="gca51ceb2f5d855dfde55bcedf8d3b92d"></a><!-- doxytag: member="lc-switch.h::LC_END" ref="gca51ceb2f5d855dfde55bcedf8d3b92d" args="(s)" -->
#define&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00017.html#gca51ceb2f5d855dfde55bcedf8d3b92d">LC_END</a>(s)&nbsp;&nbsp;&nbsp;}</td></tr>

<tr><td colspan="2"><br><h2>Typedefs</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="anchor" name="gfad6704adb116cc16edb80f744e7239d"></a><!-- doxytag: member="lc-switch.h::lc_t" ref="gfad6704adb116cc16edb80f744e7239d" args="" -->
typedef unsigned short&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00017.html#gfad6704adb116cc16edb80f744e7239d">lc_t</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">The local continuation type. <br></td></tr>
</table>
<hr size="1"><address style="align: right;"><small>Generated on Mon Oct 2 10:06:29 2006 for The Protothreads Library 1.4 by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.4.6 </small></address>
</body>
</html>
