{"configurations": [{"name": "nrf52832_xxaa", "includePath": ["d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\examples\\ble_peripheral\\ble_app_uart\\config", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\ble\\ble_advertising", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\ble\\ble_dtm", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\ble\\ble_link_ctx_manager", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\ble\\ble_racp", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\ble\\ble_services\\ble_ancs_c", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\ble\\ble_services\\ble_ans_c", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\ble\\ble_services\\ble_bas", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\ble\\ble_services\\ble_bas_c", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\ble\\ble_services\\ble_cscs", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\ble\\ble_services\\ble_cts_c", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\ble\\ble_services\\ble_dfu", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\ble\\ble_services\\ble_dis", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\ble\\ble_services\\ble_gls", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\ble\\ble_services\\ble_hids", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\ble\\ble_services\\ble_hrs", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\ble\\ble_services\\ble_hrs_c", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\ble\\ble_services\\ble_hts", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\ble\\ble_services\\ble_ias", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\ble\\ble_services\\ble_ias_c", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\ble\\ble_services\\ble_lbs", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\ble\\ble_services\\ble_lbs_c", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\ble\\ble_services\\ble_lls", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\ble\\ble_services\\ble_nus", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\ble\\ble_services\\ble_nus_c", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\ble\\ble_services\\ble_rscs", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\ble\\ble_services\\ble_rscs_c", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\ble\\ble_services\\ble_tps", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\ble\\common", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\ble\\nrf_ble_gatt", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\ble\\nrf_ble_qwr", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\ble\\peer_manager", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\boards", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\libraries\\atomic", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\libraries\\atomic_fifo", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\libraries\\atomic_flags", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\libraries\\balloc", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\libraries\\bootloader\\ble_dfu", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\libraries\\bsp", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\libraries\\button", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\libraries\\cli", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\libraries\\crc16", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\libraries\\crc32", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\libraries\\crypto", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\libraries\\csense", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\libraries\\csense_drv", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\libraries\\delay", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\libraries\\ecc", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\libraries\\experimental_section_vars", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\libraries\\experimental_task_manager", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\libraries\\fds", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\libraries\\fifo", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\libraries\\fstorage", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\libraries\\gfx", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\libraries\\gpiote", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\libraries\\hardfault", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\libraries\\hci", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\libraries\\led_softblink", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\libraries\\log", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\libraries\\log\\src", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\libraries\\low_power_pwm", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\libraries\\mem_manager", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\libraries\\memobj", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\libraries\\mpu", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\libraries\\mutex", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\libraries\\pwm", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\libraries\\pwr_mgmt", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\libraries\\queue", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\libraries\\ringbuf", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\libraries\\scheduler", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\libraries\\sdcard", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\libraries\\slip", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\libraries\\sortlist", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\libraries\\spi_mngr", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\libraries\\stack_guard", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\libraries\\strerror", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\libraries\\svc", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\libraries\\timer", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\libraries\\twi_mngr", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\libraries\\twi_sensor", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\libraries\\uart", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\libraries\\usbd", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\libraries\\usbd\\class\\audio", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\libraries\\usbd\\class\\cdc", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\libraries\\usbd\\class\\cdc\\acm", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\libraries\\usbd\\class\\hid", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\libraries\\usbd\\class\\hid\\generic", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\libraries\\usbd\\class\\hid\\kbd", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\libraries\\usbd\\class\\hid\\mouse", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\libraries\\usbd\\class\\msc", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\libraries\\util", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\nfc\\ndef\\conn_hand_parser", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\nfc\\ndef\\conn_hand_parser\\ac_rec_parser", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\nfc\\ndef\\conn_hand_parser\\ble_oob_advdata_parser", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\nfc\\ndef\\conn_hand_parser\\le_oob_rec_parser", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\nfc\\ndef\\connection_handover\\ac_rec", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\nfc\\ndef\\connection_handover\\ble_oob_advdata", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\nfc\\ndef\\connection_handover\\ble_pair_lib", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\nfc\\ndef\\connection_handover\\ble_pair_msg", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\nfc\\ndef\\connection_handover\\common", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\nfc\\ndef\\connection_handover\\ep_oob_rec", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\nfc\\ndef\\connection_handover\\hs_rec", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\nfc\\ndef\\connection_handover\\le_oob_rec", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\nfc\\ndef\\generic\\message", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\nfc\\ndef\\generic\\record", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\nfc\\ndef\\launchapp", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\nfc\\ndef\\parser\\message", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\nfc\\ndef\\parser\\record", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\nfc\\ndef\\text", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\nfc\\ndef\\uri", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\nfc\\platform", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\nfc\\t2t_lib", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\nfc\\t2t_parser", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\nfc\\t4t_lib", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\nfc\\t4t_parser\\apdu", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\nfc\\t4t_parser\\cc_file", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\nfc\\t4t_parser\\hl_detection_procedure", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\nfc\\t4t_parser\\tlv", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\softdevice\\common", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\softdevice\\s132\\headers", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\softdevice\\s132\\headers\\nrf52", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\external\\fprintf", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\external\\segger_rtt", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\external\\utf_converter", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\integration\\nrfx", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\integration\\nrfx\\legacy", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\modules\\nrfx", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\modules\\nrfx\\drivers\\include", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\modules\\nrfx\\hal", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\examples\\ble_peripheral\\ble_app_uart\\pca10040\\s132\\config", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\drive", "D:\\RJ\\keil\\ARM\\ARMCC\\include", "D:\\RJ\\keil\\ARM\\ARMCC\\include\\rw", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\examples\\ble_peripheral\\ble_app_uart", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\modules\\nrfx\\soc", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\modules\\nrfx\\drivers\\src", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\modules\\nrfx\\drivers\\src\\prs"], "defines": ["APP_TIMER_V2", "APP_TIMER_V2_RTC1_ENABLED", "BOARD_PCA10040", "CONFIG_GPIO_AS_PINRESET", "FLOAT_ABI_HARD", "NRF52", "NRF52832_XXAA", "NRF52_PAN_74", "NRF_SD_BLE_API_VERSION=7", "S132", "SOFTDEVICE_PRESENT", "__HEAP_SIZE=8192", "__STACK_SIZE=8192", "__CC_ARM", "__arm__", "__align(x)=", "__ALIGNOF__(x)=", "__alignof__(x)=", "__asm(x)=", "__forceinline=", "__restrict=", "__global_reg(n)=", "__inline=", "__int64=long long", "__INTADDR__(expr)=0", "__irq=", "__packed=", "__pure=", "__smc(n)=", "__svc(n)=", "__svc_indirect(n)=", "__svc_indirect_r7(n)=", "__value_in_regs=", "__weak=", "__writeonly=", "__declspec(x)=", "__attribute__(x)=", "__nonnull__(x)=", "__register=", "__breakpoint(x)=", "__cdp(x,y,z)=", "__clrex()=", "__clz(x)=0U", "__current_pc()=0U", "__current_sp()=0U", "__disable_fiq()=", "__disable_irq()=", "__dmb(x)=", "__dsb(x)=", "__enable_fiq()=", "__enable_irq()=", "__fabs(x)=0.0", "__fabsf(x)=0.0f", "__force_loads()=", "__force_stores()=", "__isb(x)=", "__ldrex(x)=0U", "__ldrexd(x)=0U", "__ldrt(x)=0U", "__memory_changed()=", "__nop()=", "__pld(...)=", "__pli(...)=", "__qadd(x,y)=0", "__qdbl(x)=0", "__qsub(x,y)=0", "__rbit(x)=0U", "__rev(x)=0U", "__return_address()=0U", "__ror(x,y)=0U", "__schedule_barrier()=", "__semihost(x,y)=0", "__sev()=", "__sqrt(x)=0.0", "__sqrtf(x)=0.0f", "__ssat(x,y)=0", "__strex(x,y)=0U", "__strexd(x,y)=0", "__strt(x,y)=", "__swp(x,y)=0U", "__usat(x,y)=0U", "__wfe()=", "__wfi()=", "__yield()=", "__vfp_status(x,y)=0"], "intelliSenseMode": "${default}"}, {"name": "flash_s132_nrf52_7.0.1_softdevice", "includePath": ["d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\examples\\ble_peripheral\\ble_app_uart\\config", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\examples\\ble_peripheral\\ble_app_uart\\pca10040\\s132\\config", "D:\\RJ\\keil\\ARM\\ARMCC\\include", "D:\\RJ\\keil\\ARM\\ARMCC\\include\\rw", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\examples\\ble_peripheral\\ble_app_uart", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\drive", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\boards", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\libraries\\bsp", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\external\\utf_converter", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\ble\\common", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\ble\\ble_advertising", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\ble\\ble_link_ctx_manager", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\ble\\nrf_ble_gatt", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\ble\\nrf_ble_qwr", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\ble\\ble_services\\ble_nus", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\integration\\nrfx\\legacy", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\modules\\nrfx\\soc", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\modules\\nrfx\\drivers\\src", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\modules\\nrfx\\drivers\\src\\prs", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\libraries\\button", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\libraries\\util", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\libraries\\fifo", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\libraries\\scheduler", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\libraries\\timer", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\libraries\\uart", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\libraries\\hardfault", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\libraries\\atomic_fifo", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\libraries\\atomic_flags", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\libraries\\atomic", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\libraries\\balloc", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\external\\fprintf", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\libraries\\memobj", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\libraries\\pwr_mgmt", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\libraries\\ringbuf", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\libraries\\experimental_section_vars", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\libraries\\sortlist", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\libraries\\strerror", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\libraries\\log\\src", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\external\\segger_rtt", "d:\\PhD\\Code\\C\\work\\7.10\\52832_1294\\components\\softdevice\\common"], "defines": ["__HEAP_SIZE=8192", "__STACK_SIZE=8192", "__CC_ARM", "__arm__", "__align(x)=", "__ALIGNOF__(x)=", "__alignof__(x)=", "__asm(x)=", "__forceinline=", "__restrict=", "__global_reg(n)=", "__inline=", "__int64=long long", "__INTADDR__(expr)=0", "__irq=", "__packed=", "__pure=", "__smc(n)=", "__svc(n)=", "__svc_indirect(n)=", "__svc_indirect_r7(n)=", "__value_in_regs=", "__weak=", "__writeonly=", "__declspec(x)=", "__attribute__(x)=", "__nonnull__(x)=", "__register=", "__breakpoint(x)=", "__cdp(x,y,z)=", "__clrex()=", "__clz(x)=0U", "__current_pc()=0U", "__current_sp()=0U", "__disable_fiq()=", "__disable_irq()=", "__dmb(x)=", "__dsb(x)=", "__enable_fiq()=", "__enable_irq()=", "__fabs(x)=0.0", "__fabsf(x)=0.0f", "__force_loads()=", "__force_stores()=", "__isb(x)=", "__ldrex(x)=0U", "__ldrexd(x)=0U", "__ldrt(x)=0U", "__memory_changed()=", "__nop()=", "__pld(...)=", "__pli(...)=", "__qadd(x,y)=0", "__qdbl(x)=0", "__qsub(x,y)=0", "__rbit(x)=0U", "__rev(x)=0U", "__return_address()=0U", "__ror(x,y)=0U", "__schedule_barrier()=", "__semihost(x,y)=0", "__sev()=", "__sqrt(x)=0.0", "__sqrtf(x)=0.0f", "__ssat(x,y)=0", "__strex(x,y)=0U", "__strexd(x,y)=0", "__strt(x,y)=", "__swp(x,y)=0U", "__usat(x,y)=0U", "__wfe()=", "__wfi()=", "__yield()=", "__vfp_status(x,y)=0"], "intelliSenseMode": "${default}"}], "version": 4}