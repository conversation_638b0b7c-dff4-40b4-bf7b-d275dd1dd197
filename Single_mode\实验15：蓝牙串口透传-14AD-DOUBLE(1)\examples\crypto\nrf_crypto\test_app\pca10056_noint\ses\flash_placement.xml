<!DOCTYPE Linker_Placement_File>
<Root name="Flash Section Placement">
  <MemorySegment name="FLASH" start="$(FLASH_PH_START)" size="$(FLASH_PH_SIZE)">
    <ProgramSection alignment="0x100" load="Yes" name=".vectors" start="$(FLASH_START)" />
    <ProgramSection alignment="4" load="Yes" name=".init" />
    <ProgramSection alignment="4" load="Yes" name=".init_rodata" />
    <ProgramSection alignment="4" load="Yes" name=".text" />
    <ProgramSection alignment="4" keep="Yes" load="Yes" name=".crypto_data" inputsections="*(SORT(.crypto_data*))" address_symbol="__start_crypto_data" end_symbol="__stop_crypto_data" />
    <ProgramSection alignment="4" keep="Yes" load="Yes" name=".test_vector_aes_func_data" inputsections="*(SORT(.test_vector_aes_func_data*))" address_symbol="__start_test_vector_aes_func_data" end_symbol="__stop_test_vector_aes_func_data" />
    <ProgramSection alignment="4" keep="Yes" load="Yes" name=".test_vector_aes_data" inputsections="*(SORT(.test_vector_aes_data*))" address_symbol="__start_test_vector_aes_data" end_symbol="__stop_test_vector_aes_data" />
    <ProgramSection alignment="4" keep="Yes" load="Yes" name=".test_vector_aes_mac_data" inputsections="*(SORT(.test_vector_aes_mac_data*))" address_symbol="__start_test_vector_aes_mac_data" end_symbol="__stop_test_vector_aes_mac_data" />
    <ProgramSection alignment="4" keep="Yes" load="Yes" name=".test_vector_aes_monte_carlo_data" inputsections="*(SORT(.test_vector_aes_monte_carlo_data*))" address_symbol="__start_test_vector_aes_monte_carlo_data" end_symbol="__stop_test_vector_aes_monte_carlo_data" />
    <ProgramSection alignment="4" keep="Yes" load="Yes" name=".test_vector_aead_data" inputsections="*(SORT(.test_vector_aead_data*))" address_symbol="__start_test_vector_aead_data" end_symbol="__stop_test_vector_aead_data" />
    <ProgramSection alignment="4" keep="Yes" load="Yes" name=".test_vector_aead_simple_data" inputsections="*(SORT(.test_vector_aead_simple_data*))" address_symbol="__start_test_vector_aead_simple_data" end_symbol="__stop_test_vector_aead_simple_data" />
    <ProgramSection alignment="4" keep="Yes" load="Yes" name=".test_vector_ecdh_data_deterministic_simple" inputsections="*(SORT(.test_vector_ecdh_data_deterministic_simple*))" address_symbol="__start_test_vector_ecdh_data_deterministic_simple" end_symbol="__stop_test_vector_ecdh_data_deterministic_simple" />
    <ProgramSection alignment="4" keep="Yes" load="Yes" name=".test_vector_ecdh_data_deterministic_full" inputsections="*(SORT(.test_vector_ecdh_data_deterministic_full*))" address_symbol="__start_test_vector_ecdh_data_deterministic_full" end_symbol="__stop_test_vector_ecdh_data_deterministic_full" />
    <ProgramSection alignment="4" keep="Yes" load="Yes" name=".test_vector_ecdh_data_random" inputsections="*(SORT(.test_vector_ecdh_data_random*))" address_symbol="__start_test_vector_ecdh_data_random" end_symbol="__stop_test_vector_ecdh_data_random" />
    <ProgramSection alignment="4" keep="Yes" load="Yes" name=".test_vector_ecdsa_sign_data" inputsections="*(SORT(.test_vector_ecdsa_sign_data*))" address_symbol="__start_test_vector_ecdsa_sign_data" end_symbol="__stop_test_vector_ecdsa_sign_data" />
    <ProgramSection alignment="4" keep="Yes" load="Yes" name=".test_vector_ecdsa_random_data" inputsections="*(SORT(.test_vector_ecdsa_random_data*))" address_symbol="__start_test_vector_ecdsa_random_data" end_symbol="__stop_test_vector_ecdsa_random_data" />
    <ProgramSection alignment="4" keep="Yes" load="Yes" name=".test_vector_ecdsa_verify_data" inputsections="*(SORT(.test_vector_ecdsa_verify_data*))" address_symbol="__start_test_vector_ecdsa_verify_data" end_symbol="__stop_test_vector_ecdsa_verify_data" />
    <ProgramSection alignment="4" keep="Yes" load="Yes" name=".test_vector_eddsa_verify_data" inputsections="*(SORT(.test_vector_eddsa_verify_data*))" address_symbol="__start_test_vector_eddsa_verify_data" end_symbol="__stop_test_vector_eddsa_verify_data" />
    <ProgramSection alignment="4" keep="Yes" load="Yes" name=".test_vector_eddsa_sign_data" inputsections="*(SORT(.test_vector_eddsa_sign_data*))" address_symbol="__start_test_vector_eddsa_sign_data" end_symbol="__stop_test_vector_eddsa_sign_data" />
    <ProgramSection alignment="4" keep="Yes" load="Yes" name=".test_vector_hmac_data" inputsections="*(SORT(.test_vector_hmac_data*))" address_symbol="__start_test_vector_hmac_data" end_symbol="__stop_test_vector_hmac_data" />
    <ProgramSection alignment="4" keep="Yes" load="Yes" name=".test_vector_hkdf_data" inputsections="*(SORT(.test_vector_hkdf_data*))" address_symbol="__start_test_vector_hkdf_data" end_symbol="__stop_test_vector_hkdf_data" />
    <ProgramSection alignment="4" keep="Yes" load="Yes" name=".test_vector_hash_data" inputsections="*(SORT(.test_vector_hash_data*))" address_symbol="__start_test_vector_hash_data" end_symbol="__stop_test_vector_hash_data" />
    <ProgramSection alignment="4" keep="Yes" load="Yes" name=".test_vector_hash_long_data" inputsections="*(SORT(.test_vector_hash_long_data*))" address_symbol="__start_test_vector_hash_long_data" end_symbol="__stop_test_vector_hash_long_data" />
    <ProgramSection alignment="4" keep="Yes" load="Yes" name=".test_case_data" inputsections="*(SORT(.test_case_data*))" address_symbol="__start_test_case_data" end_symbol="__stop_test_case_data" />
    <ProgramSection alignment="4" keep="Yes" load="Yes" name=".nrf_queue" inputsections="*(.nrf_queue*)" address_symbol="__start_nrf_queue" end_symbol="__stop_nrf_queue" />
    <ProgramSection alignment="4" keep="Yes" load="Yes" name=".log_const_data" inputsections="*(SORT(.log_const_data*))" address_symbol="__start_log_const_data" end_symbol="__stop_log_const_data" />
    <ProgramSection alignment="4" keep="Yes" load="Yes" name=".log_backends" inputsections="*(SORT(.log_backends*))" address_symbol="__start_log_backends" end_symbol="__stop_log_backends" />
    <ProgramSection alignment="4" keep="Yes" load="Yes" name=".nrf_balloc" inputsections="*(.nrf_balloc*)" address_symbol="__start_nrf_balloc" end_symbol="__stop_nrf_balloc" />
    <ProgramSection alignment="4" keep="Yes" load="No" name=".nrf_sections" address_symbol="__start_nrf_sections" />
    <ProgramSection alignment="4" keep="Yes" load="Yes" name=".log_dynamic_data"  inputsections="*(SORT(.log_dynamic_data*))" runin=".log_dynamic_data_run"/>
    <ProgramSection alignment="4" keep="Yes" load="Yes" name=".log_filter_data"  inputsections="*(SORT(.log_filter_data*))" runin=".log_filter_data_run"/>
    <ProgramSection alignment="4" load="Yes" name=".dtors" />
    <ProgramSection alignment="4" load="Yes" name=".ctors" />
    <ProgramSection alignment="4" load="Yes" name=".rodata" />
    <ProgramSection alignment="4" load="Yes" name=".ARM.exidx" address_symbol="__exidx_start" end_symbol="__exidx_end" />
    <ProgramSection alignment="4" load="Yes" runin=".fast_run" name=".fast" />
    <ProgramSection alignment="4" load="Yes" runin=".data_run" name=".data" />
    <ProgramSection alignment="4" load="Yes" runin=".tdata_run" name=".tdata" />
  </MemorySegment>
  <MemorySegment name="RAM" start="$(RAM_PH_START)" size="$(RAM_PH_SIZE)">
    <ProgramSection alignment="0x100" load="No" name=".vectors_ram" start="$(RAM_START)" address_symbol="__app_ram_start__"/>
    <ProgramSection alignment="4" keep="Yes" load="No" name=".nrf_sections_run" address_symbol="__start_nrf_sections_run" />
    <ProgramSection alignment="4" keep="Yes" load="No" name=".log_dynamic_data_run" address_symbol="__start_log_dynamic_data" end_symbol="__stop_log_dynamic_data" />
    <ProgramSection alignment="4" keep="Yes" load="No" name=".log_filter_data_run" address_symbol="__start_log_filter_data" end_symbol="__stop_log_filter_data" />
    <ProgramSection alignment="4" keep="Yes" load="No" name=".nrf_sections_run_end" address_symbol="__end_nrf_sections_run" />
    <ProgramSection alignment="4" load="No" name=".fast_run" />
    <ProgramSection alignment="4" load="No" name=".data_run" />
    <ProgramSection alignment="4" load="No" name=".tdata_run" />
    <ProgramSection alignment="4" load="No" name=".bss" />
    <ProgramSection alignment="4" load="No" name=".tbss" />
    <ProgramSection alignment="4" load="No" name=".non_init" />
    <ProgramSection alignment="4" size="__HEAPSIZE__" load="No" name=".heap" />
    <ProgramSection alignment="8" size="__STACKSIZE__" load="No" place_from_segment_end="Yes" name=".stack"  address_symbol="__StackLimit" end_symbol="__StackTop"/>
    <ProgramSection alignment="8" size="__STACKSIZE_PROCESS__" load="No" name=".stack_process" />
  </MemorySegment>
</Root>
