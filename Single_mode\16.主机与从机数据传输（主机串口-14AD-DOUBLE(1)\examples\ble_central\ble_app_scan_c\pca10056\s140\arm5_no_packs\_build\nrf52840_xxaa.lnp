--cpu Cortex-M4.fp
".\_build\main.o"
".\_build\boards.o"
".\_build\bsp.o"
".\_build\bsp_btn_ble.o"
".\_build\utf.o"
".\_build\ble_advdata.o"
".\_build\ble_db_discovery.o"
".\_build\ble_srv_common.o"
".\_build\nrf_ble_gatt.o"
".\_build\nrf_ble_gq.o"
".\_build\nrf_ble_scan.o"
".\_build\nrf_drv_clock.o"
".\_build\nrf_drv_uart.o"
".\_build\nrfx_atomic.o"
".\_build\nrfx_clock.o"
".\_build\nrfx_gpiote.o"
".\_build\nrfx_prs.o"
".\_build\nrfx_uart.o"
".\_build\nrfx_uarte.o"
".\_build\app_button.o"
".\_build\app_error.o"
".\_build\app_error_handler_keil.o"
".\_build\app_error_weak.o"
".\_build\app_fifo.o"
".\_build\app_scheduler.o"
".\_build\app_timer2.o"
".\_build\app_uart_fifo.o"
".\_build\app_util_platform.o"
".\_build\drv_rtc.o"
".\_build\hardfault_implementation.o"
".\_build\nrf_assert.o"
".\_build\nrf_atfifo.o"
".\_build\nrf_atomic.o"
".\_build\nrf_balloc.o"
".\_build\nrf_fprintf.o"
".\_build\nrf_fprintf_format.o"
".\_build\nrf_memobj.o"
".\_build\nrf_pwr_mgmt.o"
".\_build\nrf_queue.o"
".\_build\nrf_ringbuf.o"
".\_build\nrf_section_iter.o"
".\_build\nrf_sortlist.o"
".\_build\nrf_strerror.o"
".\_build\retarget.o"
".\_build\nrf_log_backend_rtt.o"
".\_build\nrf_log_backend_serial.o"
".\_build\nrf_log_default_backends.o"
".\_build\nrf_log_frontend.o"
".\_build\nrf_log_str_formatter.o"
".\_build\segger_rtt.o"
".\_build\segger_rtt_syscalls_keil.o"
".\_build\segger_rtt_printf.o"
".\_build\nrf_sdh.o"
".\_build\nrf_sdh_ble.o"
".\_build\nrf_sdh_soc.o"
".\_build\arm_startup_nrf52840.o"
".\_build\system_nrf52840.o"
--library_type=microlib --strict --scatter ".\_build\nrf52840_xxaa.sct"
--diag_suppress 6330 --summary_stderr --info summarysizes --map --xref --callgraph --symbols
--info sizes --info totals --info unused --info veneers
--list ".\_build\nrf52840_xxaa.map" -o .\_build\nrf52840_xxaa.axf