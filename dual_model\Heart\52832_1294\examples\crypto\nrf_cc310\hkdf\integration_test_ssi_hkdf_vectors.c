/**
 * Copyright (c) 2017 - 2020, Nordic Semiconductor ASA
 *
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without modification,
 * are permitted provided that the following conditions are met:
 *
 * 1. Redistributions of source code must retain the above copyright notice, this
 *    list of conditions and the following disclaimer.
 *
 * 2. Redistributions in binary form, except as embedded into a Nordic
 *    Semiconductor ASA integrated circuit in a product or a software update for
 *    such product, must reproduce the above copyright notice, this list of
 *    conditions and the following disclaimer in the documentation and/or other
 *    materials provided with the distribution.
 *
 * 3. Neither the name of Nordic Semiconductor ASA nor the names of its
 *    contributors may be used to endorse or promote products derived from this
 *    software without specific prior written permission.
 *
 * 4. This software, with or without modification, must only be used with a
 *    Nordic Semiconductor ASA integrated circuit.
 *
 * 5. Any software provided in binary form under this license must not be reverse
 *    engineered, decompiled, modified and/or disassembled.
 *
 * THIS SOFTWARE IS PROVIDED BY NORDIC SEMICONDUCTOR ASA "AS IS" AND ANY EXPRESS
 * OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY, NONINFRINGEMENT, AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL NORDIC SEMICONDUCTOR ASA OR CONTRIBUTORS BE
 * LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
 * CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE
 * GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
 * LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT
 * OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 */
#include <stdint.h>
#ifdef DX_LINUX_PLATFORM
#include <stdio.h>//for printf
#endif
#include <string.h>//for memcpy
#ifdef DX_LINUX_PLATFORM
#include <unistd.h>//for usleep
#endif
#include "ssi_pal_types.h"
#include "integration_test_ssi_data.h"
#include "crys_hkdf.h"


/**
 * @brief This file includes a set of test vectors taken from rfc 5869.
 */ 


const hkdfDataStuct hkdfVectors[] = {
	/* SHA-256  					*/
	{ 
		/* TST_0*/
		{"CRYS_HKDF_HASH_SHA256_mode / IKM=22 SALT=13 INFO=10 L=42 \n"},
		CRYS_HKDF_HASH_SHA256_mode,
		/* TST_IKM */
		{0x0b,0x0b,0x0b,0x0b,0x0b,0x0b,0x0b,0x0b,0x0b,0x0b,0x0b,0x0b,0x0b,0x0b,0x0b,0x0b,
		 0x0b,0x0b,0x0b,0x0b,0x0b,0x0b},
		/*TST_IKMSize*/ 
		22,
		/* TST_SALT */
		{0x00,0x01,0x02,0x03,0x04,0x05,0x06,0x07,0x08,0x09,0x0a,0x0b,0x0c},
		/*TST_SALTSize*/ 
		13,
		/* TST_INFO */
		{0xf0,0xf1,0xf2,0xf3,0xf4,0xf5,0xf6,0xf7,0xf8,0xf9},
		/*TST_INFOSize*/ 
		10,
		/* TST_ExpectedPRK */
		{ 0x07,0x77,0x09,0x36,0x2c,0x2e,0x32,0xdf,0x0d,0xdc,0x3f,0x0d,0xc4,0x7b,0xba,0x63,
		  0x90,0xb6,0xc7,0x3b,0xb5,0x0f,0x9c,0x31,0x22,0xec,0x84,0x4a,0xd7,0xc2,0xb3,0xe5},
		/*TST_ExpectedPRKSize*/ 
		32,
		/* TST_ExpectOKM */
		{ 0x3c,0xb2,0x5f,0x25,0xfa,0xac,0xd5,0x7a,0x90,0x43,0x4f,0x64,0xd0,0x36,0x2f,0x2a,
		  0x2d,0x2d,0x0a,0x90,0xcf,0x1a,0x5a,0x4c,0x5d,0xb0,0x2d,0x56,0xec,0xc4,0xc5,0xbf,
		  0x34,0x00,0x72,0x08,0xd5,0xb8,0x87,0x18,0x58,0x65},
		/*TST_LSize*/ 
		42,
	},

	{ 
		/* TST_1*/
		{"CRYS_HKDF_HASH_SHA256_mode / IKM=80 SALT=80 INFO=80 L=82 \n"},
		CRYS_HKDF_HASH_SHA256_mode,
		/* TST_IKM */
		{ 0x00,0x01,0x02,0x03,0x04,0x05,0x06,0x07,0x08,0x09,0x0a,0x0b,0x0c,0x0d,0x0e,0x0f,
		  0x10,0x11,0x12,0x13,0x14,0x15,0x16,0x17,0x18,0x19,0x1a,0x1b,0x1c,0x1d,0x1e,0x1f,
		  0x20,0x21,0x22,0x23,0x24,0x25,0x26,0x27,0x28,0x29,0x2a,0x2b,0x2c,0x2d,0x2e,0x2f,
		  0x30,0x31,0x32,0x33,0x34,0x35,0x36,0x37,0x38,0x39,0x3a,0x3b,0x3c,0x3d,0x3e,0x3f,
		  0x40,0x41,0x42,0x43,0x44,0x45,0x46,0x47,0x48,0x49,0x4a,0x4b,0x4c,0x4d,0x4e,0x4f},
		/*TST_IKMSize*/ 
		80,
		/* TST_SALT */
		{ 0x60,0x61,0x62,0x63,0x64,0x65,0x66,0x67,0x68,0x69,0x6a,0x6b,0x6c,0x6d,0x6e,0x6f,
		  0x70,0x71,0x72,0x73,0x74,0x75,0x76,0x77,0x78,0x79,0x7a,0x7b,0x7c,0x7d,0x7e,0x7f,
		  0x80,0x81,0x82,0x83,0x84,0x85,0x86,0x87,0x88,0x89,0x8a,0x8b,0x8c,0x8d,0x8e,0x8f,
		  0x90,0x91,0x92,0x93,0x94,0x95,0x96,0x97,0x98,0x99,0x9a,0x9b,0x9c,0x9d,0x9e,0x9f,
		  0xa0,0xa1,0xa2,0xa3,0xa4,0xa5,0xa6,0xa7,0xa8,0xa9,0xaa,0xab,0xac,0xad,0xae,0xaf},
		/*TST_SALTSize*/ 
		80,
		/* TST_INFO */
		{ 0xb0,0xb1,0xb2,0xb3,0xb4,0xb5,0xb6,0xb7,0xb8,0xb9,0xba,0xbb,0xbc,0xbd,0xbe,0xbf,
		  0xc0,0xc1,0xc2,0xc3,0xc4,0xc5,0xc6,0xc7,0xc8,0xc9,0xca,0xcb,0xcc,0xcd,0xce,0xcf,
		  0xd0,0xd1,0xd2,0xd3,0xd4,0xd5,0xd6,0xd7,0xd8,0xd9,0xda,0xdb,0xdc,0xdd,0xde,0xdf,
		  0xe0,0xe1,0xe2,0xe3,0xe4,0xe5,0xe6,0xe7,0xe8,0xe9,0xea,0xeb,0xec,0xed,0xee,0xef,
		  0xf0,0xf1,0xf2,0xf3,0xf4,0xf5,0xf6,0xf7,0xf8,0xf9,0xfa,0xfb,0xfc,0xfd,0xfe,0xff},
		/*TST_INFOSize*/ 
		80,
		/* TST_ExpectedPRK */
		{ 0x06,0xa6,0xb8,0x8c,0x58,0x53,0x36,0x1a,0x06,0x10,0x4c,0x9c,0xeb,0x35,0xb4,0x5c,
		  0xef,0x76,0x00,0x14,0x90,0x46,0x71,0x01,0x4a,0x19,0x3f,0x40,0xc1,0x5f,0xc2,0x44},
		/*TST_ExpectedPRKSize*/ 
		32,
		/* TST_ExpectOKM */
		{ 0xb1,0x1e,0x39,0x8d,0xc8,0x03,0x27,0xa1,0xc8,0xe7,0xf7,0x8c,0x59,0x6a,0x49,0x34,
		  0x4f,0x01,0x2e,0xda,0x2d,0x4e,0xfa,0xd8,0xa0,0x50,0xcc,0x4c,0x19,0xaf,0xa9,0x7c,
		  0x59,0x04,0x5a,0x99,0xca,0xc7,0x82,0x72,0x71,0xcb,0x41,0xc6,0x5e,0x59,0x0e,0x09,
		  0xda,0x32,0x75,0x60,0x0c,0x2f,0x09,0xb8,0x36,0x77,0x93,0xa9,0xac,0xa3,0xdb,0x71,
		  0xcc,0x30,0xc5,0x81,0x79,0xec,0x3e,0x87,0xc1,0x4c,0x01,0xd5,0xc1,0xf3,0x43,0x4f,
		  0x1d,0x87},
		/*TST_LSize*/ 
		82,
	},

	{ 
		/* TST_2*/
		{"CRYS_HKDF_HASH_SHA256_mode / IKM=22 SALT=0 INFO=0 L=42 \n"},
		CRYS_HKDF_HASH_SHA256_mode,
		/* TST_IKM */
		{0x0b,0x0b,0x0b,0x0b,0x0b,0x0b,0x0b,0x0b,0x0b,0x0b,0x0b,0x0b,0x0b,0x0b,0x0b,0x0b,
		 0x0b,0x0b,0x0b,0x0b,0x0b,0x0b},
		/*TST_IKMSize*/ 
		22,
		/* TST_SALT */
		{0x0},
		/*TST_SALTSize*/ 
		0,
		/* TST_INFO */
		{0x0},
		/*TST_INFOSize*/ 
		0,
		/* TST_ExpectedPRK */
		{ 0x19,0xef,0x24,0xa3,0x2c,0x71,0x7b,0x16,0x7f,0x33,0xa9,0x1d,0x6f,0x64,0x8b,0xdf,
		  0x96,0x59,0x67,0x76,0xaf,0xdb,0x63,0x77,0xac,0x43,0x4c,0x1c,0x29,0x3c,0xcb,0x04},
		/*TST_ExpectedPRKSize*/ 
		32,
		/* TST_ExpectOKM */
		{ 0x8d,0xa4,0xe7,0x75,0xa5,0x63,0xc1,0x8f,0x71,0x5f,0x80,0x2a,0x06,0x3c,0x5a,0x31,
		  0xb8,0xa1,0x1f,0x5c,0x5e,0xe1,0x87,0x9e,0xc3,0x45,0x4e,0x5f,0x3c,0x73,0x8d,0x2d,
		  0x9d,0x20,0x13,0x95,0xfa,0xa4,0xb6,0x1a,0x96,0xc8},
		/*TST_LSize*/ 
		42,
	},
	

	/* SHA-1							*/
	{ 
		/* TST_3*/
		{"CRYS_HKDF_HASH_SHA1_mode / IKM=11 SALT=13 INFO=10 L=42 \n"},
		CRYS_HKDF_HASH_SHA1_mode,
		/* TST_IKM */
		{0x0b,0x0b,0x0b,0x0b,0x0b,0x0b,0x0b,0x0b,0x0b,0x0b,0x0b},
		/*TST_IKMSize*/ 
		11,
		/* TST_SALT */
		{0x00,0x01,0x02,0x03,0x04,0x05,0x06,0x07,0x08,0x09,0x0a,0x0b,0x0c},
		/*TST_SALTSize*/ 
		13,
		/* TST_INFO */
		{0xf0,0xf1,0xf2,0xf3,0xf4,0xf5,0xf6,0xf7,0xf8,0xf9},
		/*TST_INFOSize*/ 
		10,
		/* TST_ExpectedPRK */
		{ 0x9b,0x6c,0x18,0xc4,0x32,0xa7,0xbf,0x8f,0x0e,0x71,0xc8,0xeb,0x88,0xf4,0xb3,0x0b,
		  0xaa,0x2b,0xa2,0x43}, 
		/*TST_ExpectedPRKSize*/ 
		20,
		/* TST_ExpectOKM */
		{ 0x08,0x5a,0x01,0xea,0x1b,0x10,0xf3,0x69,0x33,0x06,0x8b,0x56,0xef,0xa5,0xad,0x81,
		  0xa4,0xf1,0x4b,0x82,0x2f,0x5b,0x09,0x15,0x68,0xa9,0xcd,0xd4,0xf1,0x55,0xfd,0xa2,
		  0xc2,0x2e,0x42,0x24,0x78,0xd3,0x05,0xf3,0xf8,0x96},
		/*TST_LSize*/ 
		42,
	},
	{ 
		/* TST_4*/
		{"CRYS_HKDF_HASH_SHA1_mode / IKM=80 SALT=80 INFO=80 L=82 \n"},
		CRYS_HKDF_HASH_SHA1_mode,
		{ 0x00,0x01,0x02,0x03,0x04,0x05,0x06,0x07,0x08,0x09,0x0a,0x0b,0x0c,0x0d,0x0e,0x0f,
		  0x10,0x11,0x12,0x13,0x14,0x15,0x16,0x17,0x18,0x19,0x1a,0x1b,0x1c,0x1d,0x1e,0x1f,
		  0x20,0x21,0x22,0x23,0x24,0x25,0x26,0x27,0x28,0x29,0x2a,0x2b,0x2c,0x2d,0x2e,0x2f,
		  0x30,0x31,0x32,0x33,0x34,0x35,0x36,0x37,0x38,0x39,0x3a,0x3b,0x3c,0x3d,0x3e,0x3f,
		  0x40,0x41,0x42,0x43,0x44,0x45,0x46,0x47,0x48,0x49,0x4a,0x4b,0x4c,0x4d,0x4e,0x4f},
		/*TST_IKMSize*/ 
		80,
		/* TST_SALT */
		{ 0x60,0x61,0x62,0x63,0x64,0x65,0x66,0x67,0x68,0x69,0x6a,0x6b,0x6c,0x6d,0x6e,0x6f,
		  0x70,0x71,0x72,0x73,0x74,0x75,0x76,0x77,0x78,0x79,0x7a,0x7b,0x7c,0x7d,0x7e,0x7f,
		  0x80,0x81,0x82,0x83,0x84,0x85,0x86,0x87,0x88,0x89,0x8a,0x8b,0x8c,0x8d,0x8e,0x8f,
		  0x90,0x91,0x92,0x93,0x94,0x95,0x96,0x97,0x98,0x99,0x9a,0x9b,0x9c,0x9d,0x9e,0x9f,
		  0xa0,0xa1,0xa2,0xa3,0xa4,0xa5,0xa6,0xa7,0xa8,0xa9,0xaa,0xab,0xac,0xad,0xae,0xaf},
		/*TST_SALTSize*/ 
		80,
		/* TST_INFO */
		{ 0xb0,0xb1,0xb2,0xb3,0xb4,0xb5,0xb6,0xb7,0xb8,0xb9,0xba,0xbb,0xbc,0xbd,0xbe,0xbf,
		  0xc0,0xc1,0xc2,0xc3,0xc4,0xc5,0xc6,0xc7,0xc8,0xc9,0xca,0xcb,0xcc,0xcd,0xce,0xcf,
		  0xd0,0xd1,0xd2,0xd3,0xd4,0xd5,0xd6,0xd7,0xd8,0xd9,0xda,0xdb,0xdc,0xdd,0xde,0xdf,
		  0xe0,0xe1,0xe2,0xe3,0xe4,0xe5,0xe6,0xe7,0xe8,0xe9,0xea,0xeb,0xec,0xed,0xee,0xef,
		  0xf0,0xf1,0xf2,0xf3,0xf4,0xf5,0xf6,0xf7,0xf8,0xf9,0xfa,0xfb,0xfc,0xfd,0xfe,0xff},
		/*TST_INFOSize*/ 
		80,
		/* TST_ExpectedPRK */
		{ 0x8a,0xda,0xe0,0x9a,0x2a,0x30,0x70,0x59,0x47,0x8d,0x30,0x9b,0x26,0xc4,0x11,0x5a,
		  0x22,0x4c,0xfa,0xf6},
		/*TST_ExpectedPRKSize*/ 
		20,
		/* TST_ExpectOKM */
		{ 0x0b,0xd7,0x70,0xa7,0x4d,0x11,0x60,0xf7,0xc9,0xf1,0x2c,0xd5,0x91,0x2a,0x06,0xeb,
		  0xff,0x6a,0xdc,0xae,0x89,0x9d,0x92,0x19,0x1f,0xe4,0x30,0x56,0x73,0xba,0x2f,0xfe,
		  0x8f,0xa3,0xf1,0xa4,0xe5,0xad,0x79,0xf3,0xf3,0x34,0xb3,0xb2,0x02,0xb2,0x17,0x3c,
		  0x48,0x6e,0xa3,0x7c,0xe3,0xd3,0x97,0xed,0x03,0x4c,0x7f,0x9d,0xfe,0xb1,0x5c,0x5e,
		  0x92,0x73,0x36,0xd0,0x44,0x1f,0x4c,0x43,0x00,0xe2,0xcf,0xf0,0xd0,0x90,0x0b,0x52,
		  0xd3,0xb4},
		/*TST_LSize*/ 
		82,
	},

	{ 
		/* TST_5*/
		{"CRYS_HKDF_HASH_SHA1_mode / IKM=22 SALT=0 INFO=0 L=42 \n"},
		CRYS_HKDF_HASH_SHA1_mode,
		/* TST_IKM */
		{0x0b,0x0b,0x0b,0x0b,0x0b,0x0b,0x0b,0x0b,0x0b,0x0b,0x0b,0x0b,0x0b,0x0b,0x0b,0x0b,
		 0x0b,0x0b,0x0b,0x0b,0x0b,0x0b},
		/*TST_IKMSize*/ 
		22,
		/* TST_SALT */
		{0x0},
		/*TST_SALTSize*/ 
		0,
		/* TST_INFO */
		{0x0},
		/*TST_INFOSize*/ 
		0,
		/* TST_ExpectedPRK */
		{ 0xda,0x8c,0x8a,0x73,0xc7,0xfa,0x77,0x28,0x8e,0xc6,0xf5,0xe7,0xc2,0x97,0x78,0x6a,
		  0xa0,0xd3,0x2d,0x01},
		/*TST_ExpectedPRKSize*/ 
		20,
		/* TST_ExpectOKM */
		{ 0x0a,0xc1,0xaf,0x70,0x02,0xb3,0xd7,0x61,0xd1,0xe5,0x52,0x98,0xda,0x9d,0x05,0x06,
		  0xb9,0xae,0x52,0x05,0x72,0x20,0xa3,0x06,0xe0,0x7b,0x6b,0x87,0xe8,0xdf,0x21,0xd0,
		  0xea,0x00,0x03,0x3d,0xe0,0x39,0x84,0xd3,0x49,0x18},
		/*TST_LSize*/ 
		42,
	},
	
	{ 
		/* TST_6*/
		{"CRYS_HKDF_HASH_SHA1_mode / IKM=22 SALT=NULL INFO=0 L=42 \n"},
		CRYS_HKDF_HASH_SHA1_mode,
		/* TST_IKM */
		{ 0x0c,0x0c,0x0c,0x0c,0x0c,0x0c,0x0c,0x0c,0x0c,0x0c,0x0c,0x0c,0x0c,0x0c,0x0c,0x0c,
		  0x0c,0x0c,0x0c,0x0c,0x0c,0x0c},
		/*TST_IKMSize*/ 
		22,
		/* TST_SALT */
		{0},
		/*TST_SALTSize*/ 
		0,
		/* TST_INFO */
		{0},
		/*TST_INFOSize*/ 
		0,
		/* TST_ExpectedPRK */
		{ 0x2a,0xdc,0xca,0xda,0x18,0x77,0x9e,0x7c,0x20,0x77,0xad,0x2e,0xb1,0x9d,0x3f,0x3e,
		  0x73,0x13,0x85,0xdd},
		/*TST_ExpectedPRKSize*/ 
		20,
		/* TST_ExpectOKM */
		{ 0x2c,0x91,0x11,0x72,0x04,0xd7,0x45,0xf3,0x50,0x0d,0x63,0x6a,0x62,0xf6,0x4f,0x0a,
		  0xb3,0xba,0xe5,0x48,0xaa,0x53,0xd4,0x23,0xb0,0xd1,0xf2,0x7e,0xbb,0xa6,0xf5,0xe5,
		  0x67,0x3a,0x08,0x1d,0x70,0xcc,0xe7,0xac,0xfc,0x48},
		/*TST_LSize*/ 
		42,
	},
	
	{ 
		/* TST_7*/
		{"CRYS_HKDF_HASH_SHA512_mode / IKM=22 SALT=NULL INFO=0 L=42 \n"},
		CRYS_HKDF_HASH_SHA512_mode,
		/* TST_IKM */
		{0x0b,0x0b,0x0b,0x0b,0x0b,0x0b,0x0b,0x0b,0x0b,0x0b,0x0b,0x0b,0x0b,0x0b,0x0b,0x0b,
		 0x0b,0x0b,0x0b,0x0b,0x0b,0x0b},
		/*TST_IKMSize*/ 
		22,
		/* TST_SALT */
		{0x00,0x01,0x02,0x03,0x04,0x05,0x06,0x07,0x08,0x09,0x0a,0x0b,0x0c},
		/*TST_SALTSize*/ 
		13,
		/* TST_INFO */
		{0xf0,0xf1,0xf2,0xf3,0xf4,0xf5,0xf6,0xf7,0xf8,0xf9},
		/*TST_INFOSize*/ 
		10,
		/* TST_ExpectedPRK */
		{ 0x66,0x57,0x99,0x82,0x37,0x37,0xde,0xd0,0x4a,0x88,0xe4,0x7e,0x54,0xa5,0x89,0x0b,
		  0xb2,0xc3,0xd2,0x47,0xc7,0xa4,0x25,0x4a,0x8e,0x61,0x35,0x07,0x23,0x59,0x0a,0x26,
		  0xc3,0x62,0x38,0x12,0x7d,0x86,0x61,0xb8,0x8c,0xf8,0x0e,0xf8,0x02,0xd5,0x7e,0x2f,
		  0x7c,0xeb,0xcf,0x1e,0x00,0xe0,0x83,0x84,0x8b,0xe1,0x99,0x29,0xc6,0x1b,0x42,0x37},
		/*TST_ExpectedPRKSize*/ 
		64,
		/* TST_ExpectOKM */
		{ 0x83,0x23,0x90,0x08,0x6c,0xda,0x71,0xfb,0x47,0x62,0x5b,0xb5,0xce,0xb1,0x68,0xe4,
		  0xc8,0xe2,0x6a,0x1a,0x16,0xed,0x34,0xd9,0xfc,0x7f,0xe9,0x2c,0x14,0x81,0x57,0x93,
		  0x38,0xda,0x36,0x2c,0xb8,0xd9,0xf9,0x25,0xd7,0xcb},
		/*TST_LSize*/ 
		42,
	},
};




