<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html lang="en">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<meta http-equiv="Content-Style-Type" content="text/css">
<link rel="up" title="FatFs" href="../00index_e.html">
<link rel="alternate" hreflang="ja" title="Japanese" href="../ja/write.html">
<link rel="stylesheet" href="../css_e.css" type="text/css" media="screen" title="ELM Default">
<title>FatFs - f_write</title>
</head>

<body>

<div class="para func">
<h2>f_write</h2>
<p>The f_write writes data to a file.</p>
<pre>
FRESULT f_write (
  FIL* <span class="arg">fp</span>,          <span class="c">/* [IN] Pointer to the file object structure */</span>
  const void* <span class="arg">buff</span>, <span class="c">/* [IN] Pointer to the data to be written */</span>
  UINT <span class="arg">btw</span>,         <span class="c">/* [IN] Number of bytes to write */</span>
  UINT* <span class="arg">bw</span>          <span class="c">/* [OUT] Pointer to the variable to return number of bytes written */</span>
);
</pre>
</div>

<div class="para arg">
<h4>Parameters</h4>
<dl class="par">
<dt>fp</dt>
<dd>Pointer to the open file object structure.</dd>
<dt>buff</dt>
<dd>Pointer to the data to be written.</dd>
<dt>btw</dt>
<dd>Specifies number of bytes to write in range of <tt>UINT</tt> type.</dd>
<dt>bw</dt>
<dd>Pointer to the <tt>UINT</tt> variable to return the number of bytes written. The value is always valid after the function call regardless of the result code.</dd>
</dl>
</div>


<div class="para ret">
<h4>Return Values</h4>
<p>
<a href="rc.html#ok">FR_OK</a>,
<a href="rc.html#de">FR_DISK_ERR</a>,
<a href="rc.html#ie">FR_INT_ERR</a>,
<a href="rc.html#io">FR_INVALID_OBJECT</a>,
<a href="rc.html#tm">FR_TIMEOUT</a>
</p>
</div>


<div class="para desc">
<h4>Description</h4>
<p>The function starts to write data to the file at the position pointed by the read/write pointer. The read/write pointer advances as number of bytes written. After the function succeeded, <tt class="arg">*bw</tt> should be checked to detect the disk full. In case of <tt class="arg">*bw</tt> &lt; <tt class="arg">btw</tt>, it means the volume got full during the write operation. The function can take a time when the volume is full or close to full.</p>
</div>


<div class="para comp">
<h4>QuickInfo</h4>
<p>Available when <tt>_FS_READONLY == 0</tt>.</p>
</div>


<div class="para ref">
<h4>See Also</h4>
<p><tt><a href="open.html">f_open</a>, <a href="read.html">f_read</a>, <a href="putc.html">fputc</a>, <a href="puts.html">fputs</a>, <a href="printf.html">fprintf</a>, <a href="close.html">f_close</a>, <a href="sfile.html">FIL</a></tt></p>
</div>

<p class="foot"><a href="../00index_e.html">Return</a></p>
</body>
</html>
