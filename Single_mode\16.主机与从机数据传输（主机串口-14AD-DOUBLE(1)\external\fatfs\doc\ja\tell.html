<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html lang="ja">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta http-equiv="Content-Style-Type" content="text/css">
<link rel="up" title="FatFs" href="../00index_e.html">
<link rel="alternate" hreflang="en" title="English" href="../en/tell.html">
<link rel="stylesheet" href="../css_j.css" type="text/css" media="screen" title="ELM Default">
<title>FatFs - f_tell</title>
</head>

<body>

<div class="para func">
<h2>f_tell</h2>
<p>現在のリード/ライト ポインタを取得します。</p>
<pre>
FSIZE_t f_tell (
  FIL* <span class="arg">fp</span>   <span class="c">/* [IN] ファイル オブジェクト */</span>
);
</pre>
</div>


<div class="para arg">
<h4>引数</h4>
<dl class="par">
<dt>fp</dt>
<dd>ファイル オブジェクト構造体へのポインタを指定します。</dd>
</dl>
</div>


<div class="para ret">
<h4>戻り値</h4>
<p>現在のリード/ライト ポインタ(ファイル先頭からのバイト単位のオフセット)が返ります。</p>
</div>


<div class="para desc">
<h4>解説</h4>
<p>f_tell関数は、現リビジョンではマクロとして実装されています。ファイル オブジェクトの正当性チェックや排他制御は行いません。</p>
<pre>
<span class="k">#define</span> f_tell(fp) ((fp)->fptr)
</pre>
</div>


<div class="para comp">
<h4>対応情報</h4>
<p>常に使用可能。</p>
</div>


<div class="para ref">
<h4>参照</h4>
<p><tt><a href="open.html">f_open</a>, <a href="lseek.html">f_lseek</a>, <a href="sfile.html">FIL</a></tt></p>
</div>

<p class="foot"><a href="../00index_j.html">戻る</a></p>
</body>
</html>
