<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html lang="en">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<meta http-equiv="Content-Style-Type" content="text/css">
<link rel="up" title="FatFs" href="../00index_e.html">
<link rel="alternate" hreflang="ja" title="Japanese" href="../ja/gets.html">
<link rel="stylesheet" href="../css_e.css" type="text/css" media="screen" title="ELM Default">
<title>FatFs - f_gets</title>
</head>

<body>

<div class="para func">
<h2>f_gets</h2>
<p>The f_gets reads a string from the file.</p>
<pre>
TCHAR* f_gets (
  TCHAR* <span class="arg">buff</span>, <span class="c">/* [OUT] Read buffer */</span>
  int <span class="arg">len</span>,     <span class="c">/* [IN] Size of the read buffer */</span>
  FIL* <span class="arg">fp</span>      <span class="c">/* [IN] File object */</span>
);
</pre>
</div>

<div class="para arg">
<h4>Parameters</h4>
<dl class="par">
<dt>buff</dt>
<dd>Pointer to read buffer to store the read string.</dd>
<dt>len</dt>
<dd>Size of the read buffer in unit of character.</dd>
<dt>fp</dt>
<dd>Pointer to the open file object structure.</dd>
</dl>
</div>


<div class="para ret">
<h4>Return Values</h4>
<p>When the function succeeded, <tt class="arg">buff</tt> will be returuned.</p>
</div>


<div class="para desc">
<h4>Description</h4>
<p>The read operation continues until a <tt>'\n'</tt> is stored, reached end of the file or the buffer is filled with <tt>len - 1</tt> characters. The read string is terminated with a <tt>'\0'</tt>. When no character to read or any error occured during read operation, it returns a null pointer. The status of EOF and error can be examined with <tt>f_eof</tt> and <tt>f_error</tt> function.</p>
<p>When FatFs is configured to Unicode API (<tt>_LFN_UNICODE == 1</tt>), data types on the srting fuctions, <tt>f_putc</tt>, <tt>f_puts</tt>, <tt>f_printf</tt> and <tt>f_gets</tt>, is also switched to Unicode. The character encoding on the file to be read/written via those functions is selected by <tt>_STRF_ENCODE</tt> option.</p>
</div>


<div class="para comp">
<h4>QuickInfo</h4>
<p>This is a wrapper function of <a href="read.html"><tt>f_read</tt></a> function. Available when <tt>_USE_STRFUNC</tt> is 1 or 2. When it is set to 2, <tt>'\r'</tt>s contained in the file are stripped out.</p>
</div>


<div class="para ref">
<h4>See Also</h4>
<p><tt><a href="open.html">f_open</a>, <a href="read.html">f_read</a>, <a href="putc.html">f_putc</a>, <a href="puts.html">f_puts</a>, <a href="printf.html">f_printf</a>, <a href="close.html">f_close</a>, <a href="sfile.html">FIL</a></tt></p>
</div>

<p class="foot"><a href="../00index_e.html">Return</a></p>
</body>
</html>
