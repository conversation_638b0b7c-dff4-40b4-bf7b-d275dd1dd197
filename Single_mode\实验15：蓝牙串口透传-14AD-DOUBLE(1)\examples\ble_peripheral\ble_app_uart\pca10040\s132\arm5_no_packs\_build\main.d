.\_build\main.o: ..\..\..\main.c
.\_build\main.o: E:\RJ\MDK\Keil_v5\ARM\ARMCC\Bin\..\include\stdint.h
.\_build\main.o: E:\RJ\MDK\Keil_v5\ARM\ARMCC\Bin\..\include\string.h
.\_build\main.o: ..\..\..\..\..\..\components\libraries\util\nordic_common.h
.\_build\main.o: E:\RJ\MDK\Keil_v5\ARM\PACK\NordicSemiconductor\nRF_DeviceFamilyPack\8.32.1\Device\Include\nrf.h
.\_build\main.o: E:\RJ\MDK\Keil_v5\ARM\PACK\NordicSemiconductor\nRF_DeviceFamilyPack\8.32.1\Device\Include\nrf52.h
.\_build\main.o: E:\RJ\MDK\Keil_v5\ARM\PACK\ARM\CMSIS\5.6.0\CMSIS\Core\Include\core_cm4.h
.\_build\main.o: E:\RJ\MDK\Keil_v5\ARM\PACK\ARM\CMSIS\5.6.0\CMSIS\Core\Include\cmsis_version.h
.\_build\main.o: E:\RJ\MDK\Keil_v5\ARM\PACK\ARM\CMSIS\5.6.0\CMSIS\Core\Include\cmsis_compiler.h
.\_build\main.o: E:\RJ\MDK\Keil_v5\ARM\PACK\ARM\CMSIS\5.6.0\CMSIS\Core\Include\cmsis_armcc.h
.\_build\main.o: E:\RJ\MDK\Keil_v5\ARM\PACK\ARM\CMSIS\5.6.0\CMSIS\Core\Include\mpu_armv7.h
.\_build\main.o: E:\RJ\MDK\Keil_v5\ARM\PACK\NordicSemiconductor\nRF_DeviceFamilyPack\8.32.1\Device\Include\system_nrf52.h
.\_build\main.o: E:\RJ\MDK\Keil_v5\ARM\PACK\NordicSemiconductor\nRF_DeviceFamilyPack\8.32.1\Device\Include\nrf52_bitfields.h
.\_build\main.o: E:\RJ\MDK\Keil_v5\ARM\PACK\NordicSemiconductor\nRF_DeviceFamilyPack\8.32.1\Device\Include\nrf51_to_nrf52.h
.\_build\main.o: E:\RJ\MDK\Keil_v5\ARM\PACK\NordicSemiconductor\nRF_DeviceFamilyPack\8.32.1\Device\Include\nrf52_name_change.h
.\_build\main.o: E:\RJ\MDK\Keil_v5\ARM\PACK\NordicSemiconductor\nRF_DeviceFamilyPack\8.32.1\Device\Include\compiler_abstraction.h
.\_build\main.o: ..\..\..\..\..\..\components\softdevice\s132\headers\ble_hci.h
.\_build\main.o: ..\..\..\..\..\..\components\ble\common\ble_advdata.h
.\_build\main.o: E:\RJ\MDK\Keil_v5\ARM\ARMCC\Bin\..\include\stdbool.h
.\_build\main.o: ..\..\..\..\..\..\components\softdevice\s132\headers\ble.h
.\_build\main.o: ..\..\..\..\..\..\components\softdevice\s132\headers\nrf_svc.h
.\_build\main.o: ..\..\..\..\..\..\components\softdevice\s132\headers\nrf_error.h
.\_build\main.o: ..\..\..\..\..\..\components\softdevice\s132\headers\ble_err.h
.\_build\main.o: ..\..\..\..\..\..\components\softdevice\s132\headers\ble_gap.h
.\_build\main.o: ..\..\..\..\..\..\components\softdevice\s132\headers\ble_ranges.h
.\_build\main.o: ..\..\..\..\..\..\components\softdevice\s132\headers\ble_types.h
.\_build\main.o: ..\..\..\..\..\..\components\softdevice\s132\headers\ble_l2cap.h
.\_build\main.o: ..\..\..\..\..\..\components\softdevice\s132\headers\ble_gatt.h
.\_build\main.o: ..\..\..\..\..\..\components\softdevice\s132\headers\ble_gattc.h
.\_build\main.o: ..\..\..\..\..\..\components\softdevice\s132\headers\ble_gatts.h
.\_build\main.o: ..\..\..\..\..\..\components\libraries\util\sdk_common.h
.\_build\main.o: ..\config\sdk_config.h
.\_build\main.o: ..\..\..\..\..\..\components\libraries\util\sdk_os.h
.\_build\main.o: ..\..\..\..\..\..\components\libraries\util\sdk_errors.h
.\_build\main.o: ..\..\..\..\..\..\components\libraries\util\app_util.h
.\_build\main.o: E:\RJ\MDK\Keil_v5\ARM\ARMCC\Bin\..\include\stddef.h
.\_build\main.o: ..\..\..\..\..\..\components\softdevice\s132\headers\nrf52\nrf_mbr.h
.\_build\main.o: ..\..\..\..\..\..\components\libraries\util\sdk_macros.h
.\_build\main.o: ..\..\..\..\..\..\components\libraries\util\nrf_assert.h
.\_build\main.o: ..\..\..\..\..\..\components\ble\ble_advertising\ble_advertising.h
.\_build\main.o: ..\..\..\..\..\..\components\ble\common\ble_conn_params.h
.\_build\main.o: ..\..\..\..\..\..\components\ble\common\ble_srv_common.h
.\_build\main.o: ..\..\..\..\..\..\components\softdevice\common\nrf_sdh.h
.\_build\main.o: ..\..\..\..\..\..\components\libraries\experimental_section_vars\nrf_section_iter.h
.\_build\main.o: ..\..\..\..\..\..\components\libraries\experimental_section_vars\nrf_section.h
.\_build\main.o: ..\..\..\..\..\..\components\softdevice\common\nrf_sdh_soc.h
.\_build\main.o: ..\..\..\..\..\..\components\softdevice\s132\headers\nrf_soc.h
.\_build\main.o: ..\..\..\..\..\..\components\softdevice\s132\headers\nrf_error_soc.h
.\_build\main.o: ..\..\..\..\..\..\components\softdevice\common\nrf_sdh_ble.h
.\_build\main.o: ..\..\..\..\..\..\components\ble\nrf_ble_gatt\nrf_ble_gatt.h
.\_build\main.o: ..\..\..\..\..\..\components\ble\nrf_ble_qwr\nrf_ble_qwr.h
.\_build\main.o: ..\..\..\..\..\..\components\libraries\timer\app_timer.h
.\_build\main.o: ..\..\..\..\..\..\components\libraries\util\app_error.h
.\_build\main.o: E:\RJ\MDK\Keil_v5\ARM\ARMCC\Bin\..\include\stdio.h
.\_build\main.o: ..\..\..\..\..\..\components\libraries\util\app_error_weak.h
.\_build\main.o: ..\..\..\..\..\..\components\libraries\log\nrf_log_instance.h
.\_build\main.o: ..\..\..\..\..\..\components\libraries\log\nrf_log_types.h
.\_build\main.o: ..\..\..\..\..\..\components\libraries\sortlist\nrf_sortlist.h
.\_build\main.o: ..\..\..\..\..\..\components\ble\ble_services\ble_nus\ble_nus.h
.\_build\main.o: ..\..\..\..\..\..\components\ble\ble_link_ctx_manager\ble_link_ctx_manager.h
.\_build\main.o: ..\..\..\..\..\..\components\ble\common\ble_conn_state.h
.\_build\main.o: ..\..\..\..\..\..\components\libraries\atomic\nrf_atomic.h
.\_build\main.o: ..\..\..\..\..\..\components\libraries\uart\app_uart.h
.\_build\main.o: ..\..\..\..\..\..\components\libraries\util\app_util_platform.h
.\_build\main.o: ..\..\..\..\..\..\components\softdevice\s132\headers\nrf_nvic.h
.\_build\main.o: ..\..\..\..\..\..\components\libraries\bsp\bsp_btn_ble.h
.\_build\main.o: ..\..\..\..\..\..\components\libraries\bsp\bsp.h
.\_build\main.o: ..\..\..\..\..\..\components\boards\boards.h
.\_build\main.o: ..\..\..\..\..\..\modules\nrfx\hal\nrf_gpio.h
.\_build\main.o: ..\..\..\..\..\..\modules\nrfx\nrfx.h
.\_build\main.o: ..\..\..\..\..\..\integration\nrfx\nrfx_config.h
.\_build\main.o: ..\..\..\..\..\..\modules\nrfx\drivers/nrfx_common.h
.\_build\main.o: E:\RJ\MDK\Keil_v5\ARM\PACK\NordicSemiconductor\nRF_DeviceFamilyPack\8.32.1\Device\Include\nrf_peripherals.h
.\_build\main.o: E:\RJ\MDK\Keil_v5\ARM\PACK\NordicSemiconductor\nRF_DeviceFamilyPack\8.32.1\Device\Include\nrf52832_peripherals.h
.\_build\main.o: ..\..\..\..\..\..\integration\nrfx\nrfx_glue.h
.\_build\main.o: ..\..\..\..\..\..\integration\nrfx\legacy/apply_old_config.h
.\_build\main.o: ..\..\..\..\..\..\modules\nrfx\soc/nrfx_irqs.h
.\_build\main.o: ..\..\..\..\..\..\modules\nrfx\soc/nrfx_irqs_nrf52832.h
.\_build\main.o: ..\..\..\..\..\..\modules\nrfx\soc/nrfx_coredep.h
.\_build\main.o: ..\..\..\..\..\..\modules\nrfx\soc/nrfx_atomic.h
.\_build\main.o: ..\..\..\..\..\..\modules\nrfx\nrfx.h
.\_build\main.o: ..\..\..\..\..\..\components\libraries\util\sdk_resources.h
.\_build\main.o: ..\..\..\..\..\..\components\softdevice\s132\headers\nrf_sd_def.h
.\_build\main.o: ..\..\..\..\..\..\modules\nrfx\drivers/nrfx_errors.h
.\_build\main.o: ..\..\..\..\..\..\components\boards\pca10040.h
.\_build\main.o: ..\..\..\..\..\..\components\libraries\button\app_button.h
.\_build\main.o: ..\..\..\..\..\..\components\libraries\pwr_mgmt\nrf_pwr_mgmt.h
.\_build\main.o: ..\..\..\..\..\..\modules\nrfx\hal\nrf_uart.h
.\_build\main.o: ..\..\..\..\..\..\modules\nrfx\hal\nrf_uarte.h
.\_build\main.o: ..\..\..\..\..\..\components\libraries\log\nrf_log.h
.\_build\main.o: ..\..\..\..\..\..\components\libraries\log\src\nrf_log_internal.h
.\_build\main.o: ..\..\..\..\..\..\components\libraries\log\nrf_log_ctrl.h
.\_build\main.o: ..\..\..\..\..\..\components\libraries\log\src\nrf_log_ctrl_internal.h
.\_build\main.o: ..\..\..\..\..\..\components\libraries\log\nrf_log_backend_interface.h
.\_build\main.o: ..\..\..\..\..\..\components\libraries\log\nrf_log_default_backends.h
.\_build\main.o: ..\..\..\..\..\..\integration\nrfx\legacy\nrf_drv_saadc.h
.\_build\main.o: ..\..\..\..\..\..\modules\nrfx\drivers\include\nrfx_saadc.h
.\_build\main.o: ..\..\..\..\..\..\modules\nrfx\hal/nrf_saadc.h
.\_build\main.o: ..\..\..\..\..\..\integration\nrfx\legacy\nrf_drv_ppi.h
.\_build\main.o: ..\..\..\..\..\..\modules\nrfx\drivers\include\nrfx_ppi.h
.\_build\main.o: ..\..\..\..\..\..\modules\nrfx\hal/nrf_ppi.h
.\_build\main.o: ..\..\..\..\..\..\integration\nrfx\legacy\nrf_drv_timer.h
.\_build\main.o: ..\..\..\..\..\..\modules\nrfx\drivers\include\nrfx_timer.h
.\_build\main.o: ..\..\..\..\..\..\modules\nrfx\hal/nrf_timer.h
