/*
 * Copyright (c) 2010 - 2020, Nordic Semiconductor ASA
 * 
 * All rights reserved.
 * 
 * Redistribution and use in source and binary forms, with or without modification,
 * are permitted provided that the following conditions are met:
 * 
 * 1. Redistributions of source code must retain the above copyright notice, this
 * list of conditions and the following disclaimer.
 * 
 * 2. Redistributions in binary form, except as embedded into a Nordic
 * Semiconductor ASA integrated circuit in a product or a software update for
 * such product, must reproduce the above copyright notice, this list of
 * conditions and the following disclaimer in the documentation and/or other
 * materials provided with the distribution.
 * 
 * 3. Neither the name of Nordic Semiconductor ASA nor the names of its
 * contributors may be used to endorse or promote products derived from this
 * software without specific prior written permission.
 * 
 * 4. This software, with or without modification, must only be used with a
 * Nordic Semiconductor ASA integrated circuit.
 * 
 * 5. Any software provided in binary form under this license must not be reverse
 * engineered, decompiled, modified and/or disassembled.
 * 
 * THIS SOFTWARE IS PROVIDED BY NORDIC SEMICONDUCTOR ASA "AS IS" AND ANY EXPRESS
 * OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY, NONINFRINGEMENT, AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL NORDIC SEMICONDUCTOR ASA OR CONTRIBUTORS BE
 * LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
 * CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE
 * GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
 * LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT
 * OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 * @file     nrf9160.h
 * @brief    CMSIS HeaderFile
 * @version  1
 * @date     04. March 2020
 * @note     Generated by SVDConv V3.3.25 on Wednesday, 04.03.2020 14:59:25
 *           from File 'nrf9160.svd',
 *           last modified on Wednesday, 04.03.2020 13:59:18
 */



/** @addtogroup Nordic Semiconductor
  * @{
  */


/** @addtogroup nrf9160
  * @{
  */


#ifndef NRF9160_H
#define NRF9160_H

#ifdef __cplusplus
extern "C" {
#endif


/** @addtogroup Configuration_of_CMSIS
  * @{
  */



/* =========================================================================================================================== */
/* ================                                Interrupt Number Definition                                ================ */
/* =========================================================================================================================== */

typedef enum {
/* =======================================  ARM Cortex-M33 Specific Interrupt Numbers  ======================================= */
  Reset_IRQn                = -15,              /*!< -15  Reset Vector, invoked on Power up and warm reset                     */
  NonMaskableInt_IRQn       = -14,              /*!< -14  Non maskable Interrupt, cannot be stopped or preempted               */
  HardFault_IRQn            = -13,              /*!< -13  Hard Fault, all classes of Fault                                     */
  MemoryManagement_IRQn     = -12,              /*!< -12  Memory Management, MPU mismatch, including Access Violation
                                                     and No Match                                                              */
  BusFault_IRQn             = -11,              /*!< -11  Bus Fault, Pre-Fetch-, Memory Access Fault, other address/memory
                                                     related Fault                                                             */
  UsageFault_IRQn           = -10,              /*!< -10  Usage Fault, i.e. Undef Instruction, Illegal State Transition        */
  SecureFault_IRQn          =  -9,              /*!< -9 Secure Fault Handler                                                   */
  SVCall_IRQn               =  -5,              /*!< -5 System Service Call via SVC instruction                                */
  DebugMonitor_IRQn         =  -4,              /*!< -4 Debug Monitor                                                          */
  PendSV_IRQn               =  -2,              /*!< -2 Pendable request for system service                                    */
  SysTick_IRQn              =  -1,              /*!< -1 System Tick Timer                                                      */
/* ==========================================  nrf9160 Specific Interrupt Numbers  =========================================== */
  SPU_IRQn                  =   3,              /*!< 3  SPU                                                                    */
  CLOCK_POWER_IRQn          =   5,              /*!< 5  CLOCK_POWER                                                            */
  UARTE0_SPIM0_SPIS0_TWIM0_TWIS0_IRQn=   8,     /*!< 8  UARTE0_SPIM0_SPIS0_TWIM0_TWIS0                                         */
  UARTE1_SPIM1_SPIS1_TWIM1_TWIS1_IRQn=   9,     /*!< 9  UARTE1_SPIM1_SPIS1_TWIM1_TWIS1                                         */
  UARTE2_SPIM2_SPIS2_TWIM2_TWIS2_IRQn=  10,     /*!< 10 UARTE2_SPIM2_SPIS2_TWIM2_TWIS2                                         */
  UARTE3_SPIM3_SPIS3_TWIM3_TWIS3_IRQn=  11,     /*!< 11 UARTE3_SPIM3_SPIS3_TWIM3_TWIS3                                         */
  GPIOTE0_IRQn              =  13,              /*!< 13 GPIOTE0                                                                */
  SAADC_IRQn                =  14,              /*!< 14 SAADC                                                                  */
  TIMER0_IRQn               =  15,              /*!< 15 TIMER0                                                                 */
  TIMER1_IRQn               =  16,              /*!< 16 TIMER1                                                                 */
  TIMER2_IRQn               =  17,              /*!< 17 TIMER2                                                                 */
  RTC0_IRQn                 =  20,              /*!< 20 RTC0                                                                   */
  RTC1_IRQn                 =  21,              /*!< 21 RTC1                                                                   */
  WDT_IRQn                  =  24,              /*!< 24 WDT                                                                    */
  EGU0_IRQn                 =  27,              /*!< 27 EGU0                                                                   */
  EGU1_IRQn                 =  28,              /*!< 28 EGU1                                                                   */
  EGU2_IRQn                 =  29,              /*!< 29 EGU2                                                                   */
  EGU3_IRQn                 =  30,              /*!< 30 EGU3                                                                   */
  EGU4_IRQn                 =  31,              /*!< 31 EGU4                                                                   */
  EGU5_IRQn                 =  32,              /*!< 32 EGU5                                                                   */
  PWM0_IRQn                 =  33,              /*!< 33 PWM0                                                                   */
  PWM1_IRQn                 =  34,              /*!< 34 PWM1                                                                   */
  PWM2_IRQn                 =  35,              /*!< 35 PWM2                                                                   */
  PWM3_IRQn                 =  36,              /*!< 36 PWM3                                                                   */
  PDM_IRQn                  =  38,              /*!< 38 PDM                                                                    */
  I2S_IRQn                  =  40,              /*!< 40 I2S                                                                    */
  IPC_IRQn                  =  42,              /*!< 42 IPC                                                                    */
  FPU_IRQn                  =  44,              /*!< 44 FPU                                                                    */
  GPIOTE1_IRQn              =  49,              /*!< 49 GPIOTE1                                                                */
  KMU_IRQn                  =  57,              /*!< 57 KMU                                                                    */
  CRYPTOCELL_IRQn           =  64               /*!< 64 CRYPTOCELL                                                             */
} IRQn_Type;



/* =========================================================================================================================== */
/* ================                           Processor and Core Peripheral Section                           ================ */
/* =========================================================================================================================== */

/* ==========================  Configuration of the ARM Cortex-M33 Processor and Core Peripherals  =========================== */
#define __CM33_REV                 0x0004U      /*!< CM33 Core Revision                                                        */
#define __DSP_PRESENT                  1        /*!< DSP present or not                                                        */
#define __NVIC_PRIO_BITS               3        /*!< Number of Bits used for Priority Levels                                   */
#define __Vendor_SysTickConfig         0        /*!< Set to 1 if different SysTick Config is used                              */
#define __VTOR_PRESENT                 1        /*!< Set to 1 if CPU supports Vector Table Offset Register                     */
#define __MPU_PRESENT                  1        /*!< MPU present                                                               */
#define __FPU_PRESENT                  1        /*!< FPU present                                                               */
#define __FPU_DP                       0        /*!< Double Precision FPU                                                      */
#define __SAU_REGION_PRESENT           0        /*!< SAU present                                                               */


/** @} */ /* End of group Configuration_of_CMSIS */

#include "core_cm33.h"                          /*!< ARM Cortex-M33 processor and core peripherals                             */
#include "system_nrf9160.h"                     /*!< nrf9160 System                                                            */

#ifndef __IM                                    /*!< Fallback for older CMSIS versions                                         */
  #define __IM   __I
#endif
#ifndef __OM                                    /*!< Fallback for older CMSIS versions                                         */
  #define __OM   __O
#endif
#ifndef __IOM                                   /*!< Fallback for older CMSIS versions                                         */
  #define __IOM  __IO
#endif


/* =========================================================================================================================== */
/* ================                              Device Specific Cluster Section                              ================ */
/* =========================================================================================================================== */


/** @addtogroup Device_Peripheral_clusters
  * @{
  */


/**
  * @brief FICR_INFO [INFO] (Device info)
  */
typedef struct {
  __IM  uint32_t  RESERVED;
  __IM  uint32_t  DEVICEID[2];                  /*!< (@ 0x00000004) Description collection: Device identifier                  */
  __IM  uint32_t  PART;                         /*!< (@ 0x0000000C) Part code                                                  */
  __IM  uint32_t  VARIANT;                      /*!< (@ 0x00000010) Part Variant, Hardware version and Production
                                                                    configuration                                              */
  __IM  uint32_t  PACKAGE;                      /*!< (@ 0x00000014) Package option                                             */
  __IM  uint32_t  RAM;                          /*!< (@ 0x00000018) RAM variant                                                */
  __IM  uint32_t  FLASH;                        /*!< (@ 0x0000001C) Flash variant                                              */
  __IM  uint32_t  CODEPAGESIZE;                 /*!< (@ 0x00000020) Code memory page size                                      */
  __IM  uint32_t  CODESIZE;                     /*!< (@ 0x00000024) Code memory size                                           */
  __IM  uint32_t  DEVICETYPE;                   /*!< (@ 0x00000028) Device type                                                */
} FICR_INFO_Type;                               /*!< Size = 44 (0x2c)                                                          */


/**
  * @brief FICR_TRIMCNF [TRIMCNF] (Unspecified)
  */
typedef struct {
  __IM  uint32_t  ADDR;                         /*!< (@ 0x00000000) Description cluster: Address                               */
  __IM  uint32_t  DATA;                         /*!< (@ 0x00000004) Description cluster: Data                                  */
} FICR_TRIMCNF_Type;                            /*!< Size = 8 (0x8)                                                            */


/**
  * @brief FICR_TRNG90B [TRNG90B] (NIST800-90B RNG calibration data)
  */
typedef struct {
  __IM  uint32_t  BYTES;                        /*!< (@ 0x00000000) Amount of bytes for the required entropy bits              */
  __IM  uint32_t  RCCUTOFF;                     /*!< (@ 0x00000004) Repetition counter cutoff                                  */
  __IM  uint32_t  APCUTOFF;                     /*!< (@ 0x00000008) Adaptive proportion cutoff                                 */
  __IM  uint32_t  STARTUP;                      /*!< (@ 0x0000000C) Amount of bytes for the startup tests                      */
  __IM  uint32_t  ROSC1;                        /*!< (@ 0x00000010) Sample count for ring oscillator 1                         */
  __IM  uint32_t  ROSC2;                        /*!< (@ 0x00000014) Sample count for ring oscillator 2                         */
  __IM  uint32_t  ROSC3;                        /*!< (@ 0x00000018) Sample count for ring oscillator 3                         */
  __IM  uint32_t  ROSC4;                        /*!< (@ 0x0000001C) Sample count for ring oscillator 4                         */
} FICR_TRNG90B_Type;                            /*!< Size = 32 (0x20)                                                          */


/**
  * @brief UICR_KEYSLOT_CONFIG [CONFIG] (Unspecified)
  */
typedef struct {
  __IOM uint32_t  DEST;                         /*!< (@ 0x00000000) Description cluster: Destination address where
                                                                    content of the key value registers (KEYSLOT.KEYn.VALUE[0-3
                                                                    ) will be pushed by KMU. Note that this
                                                                    address MUST match that of a peripherals
                                                                    APB mapped write-only key registers, else
                                                                    the KMU can push this key value into an
                                                                    address range which the CPU can potentially
                                                                    read!                                                      */
  __IOM uint32_t  PERM;                         /*!< (@ 0x00000004) Description cluster: Define permissions for the
                                                                    key slot. Bits 0-15 and 16-31 can only be
                                                                    written when equal to 0xFFFF.                              */
} UICR_KEYSLOT_CONFIG_Type;                     /*!< Size = 8 (0x8)                                                            */


/**
  * @brief UICR_KEYSLOT_KEY [KEY] (Unspecified)
  */
typedef struct {
  __IOM uint32_t  VALUE[4];                     /*!< (@ 0x00000000) Description collection: Define bits [31+o*32:0+o*32]
                                                                    of value assigned to KMU key slot.                         */
} UICR_KEYSLOT_KEY_Type;                        /*!< Size = 16 (0x10)                                                          */


/**
  * @brief UICR_KEYSLOT [KEYSLOT] (Unspecified)
  */
typedef struct {
  __IOM UICR_KEYSLOT_CONFIG_Type CONFIG[128];   /*!< (@ 0x00000000) Unspecified                                                */
  __IOM UICR_KEYSLOT_KEY_Type KEY[128];         /*!< (@ 0x00000400) Unspecified                                                */
} UICR_KEYSLOT_Type;                            /*!< Size = 3072 (0xc00)                                                       */


/**
  * @brief TAD_PSEL [PSEL] (Unspecified)
  */
typedef struct {
  __IOM uint32_t  TRACECLK;                     /*!< (@ 0x00000000) Pin number configuration for TRACECLK                      */
  __IOM uint32_t  TRACEDATA0;                   /*!< (@ 0x00000004) Pin number configuration for TRACEDATA[0]                  */
  __IOM uint32_t  TRACEDATA1;                   /*!< (@ 0x00000008) Pin number configuration for TRACEDATA[1]                  */
  __IOM uint32_t  TRACEDATA2;                   /*!< (@ 0x0000000C) Pin number configuration for TRACEDATA[2]                  */
  __IOM uint32_t  TRACEDATA3;                   /*!< (@ 0x00000010) Pin number configuration for TRACEDATA[3]                  */
} TAD_PSEL_Type;                                /*!< Size = 20 (0x14)                                                          */


/**
  * @brief SPU_EXTDOMAIN [EXTDOMAIN] (Unspecified)
  */
typedef struct {
  __IOM uint32_t  PERM;                         /*!< (@ 0x00000000) Description cluster: Access for bus access generated
                                                                    from the external domain n List capabilities
                                                                    of the external domain n                                   */
} SPU_EXTDOMAIN_Type;                           /*!< Size = 4 (0x4)                                                            */


/**
  * @brief SPU_DPPI [DPPI] (Unspecified)
  */
typedef struct {
  __IOM uint32_t  PERM;                         /*!< (@ 0x00000000) Description cluster: Select between secure and
                                                                    non-secure attribute for the DPPI channels.                */
  __IOM uint32_t  LOCK;                         /*!< (@ 0x00000004) Description cluster: Prevent further modification
                                                                    of the corresponding PERM register                         */
} SPU_DPPI_Type;                                /*!< Size = 8 (0x8)                                                            */


/**
  * @brief SPU_GPIOPORT [GPIOPORT] (Unspecified)
  */
typedef struct {
  __IOM uint32_t  PERM;                         /*!< (@ 0x00000000) Description cluster: Select between secure and
                                                                    non-secure attribute for pins 0 to 31 of
                                                                    port n.                                                    */
  __IOM uint32_t  LOCK;                         /*!< (@ 0x00000004) Description cluster: Prevent further modification
                                                                    of the corresponding PERM register                         */
} SPU_GPIOPORT_Type;                            /*!< Size = 8 (0x8)                                                            */


/**
  * @brief SPU_FLASHNSC [FLASHNSC] (Unspecified)
  */
typedef struct {
  __IOM uint32_t  REGION;                       /*!< (@ 0x00000000) Description cluster: Define which flash region
                                                                    can contain the non-secure callable (NSC)
                                                                    region n                                                   */
  __IOM uint32_t  SIZE;                         /*!< (@ 0x00000004) Description cluster: Define the size of the non-secure
                                                                    callable (NSC) region n                                    */
} SPU_FLASHNSC_Type;                            /*!< Size = 8 (0x8)                                                            */


/**
  * @brief SPU_RAMNSC [RAMNSC] (Unspecified)
  */
typedef struct {
  __IOM uint32_t  REGION;                       /*!< (@ 0x00000000) Description cluster: Define which RAM region
                                                                    can contain the non-secure callable (NSC)
                                                                    region n                                                   */
  __IOM uint32_t  SIZE;                         /*!< (@ 0x00000004) Description cluster: Define the size of the non-secure
                                                                    callable (NSC) region n                                    */
} SPU_RAMNSC_Type;                              /*!< Size = 8 (0x8)                                                            */


/**
  * @brief SPU_FLASHREGION [FLASHREGION] (Unspecified)
  */
typedef struct {
  __IOM uint32_t  PERM;                         /*!< (@ 0x00000000) Description cluster: Access permissions for flash
                                                                    region n                                                   */
} SPU_FLASHREGION_Type;                         /*!< Size = 4 (0x4)                                                            */


/**
  * @brief SPU_RAMREGION [RAMREGION] (Unspecified)
  */
typedef struct {
  __IOM uint32_t  PERM;                         /*!< (@ 0x00000000) Description cluster: Access permissions for RAM
                                                                    region n                                                   */
} SPU_RAMREGION_Type;                           /*!< Size = 4 (0x4)                                                            */


/**
  * @brief SPU_PERIPHID [PERIPHID] (Unspecified)
  */
typedef struct {
  __IOM uint32_t  PERM;                         /*!< (@ 0x00000000) Description cluster: List capabilities and access
                                                                    permissions for the peripheral with ID n                   */
} SPU_PERIPHID_Type;                            /*!< Size = 4 (0x4)                                                            */


/**
  * @brief CTRLAPPERI_MAILBOX [MAILBOX] (Unspecified)
  */
typedef struct {
  __IM  uint32_t  RXDATA;                       /*!< (@ 0x00000000) Data sent from the debugger to the CPU                     */
  __IM  uint32_t  RXSTATUS;                     /*!< (@ 0x00000004) Status to indicate if data sent from the debugger
                                                                    to the CPU has been read                                   */
  __IM  uint32_t  RESERVED[30];
  __IOM uint32_t  TXDATA;                       /*!< (@ 0x00000080) Data sent from the CPU to the debugger                     */
  __IM  uint32_t  TXSTATUS;                     /*!< (@ 0x00000084) Status to indicate if data sent from the CPU
                                                                    to the debugger has been read                              */
} CTRLAPPERI_MAILBOX_Type;                      /*!< Size = 136 (0x88)                                                         */


/**
  * @brief CTRLAPPERI_ERASEPROTECT [ERASEPROTECT] (Unspecified)
  */
typedef struct {
  __IOM uint32_t  LOCK;                         /*!< (@ 0x00000000) Lock register ERASEPROTECT.DISABLE from being
                                                                    written until next reset                                   */
  __IOM uint32_t  DISABLE;                      /*!< (@ 0x00000004) Disable ERASEPROTECT and perform ERASEALL                  */
} CTRLAPPERI_ERASEPROTECT_Type;                 /*!< Size = 8 (0x8)                                                            */


/**
  * @brief SPIM_PSEL [PSEL] (Unspecified)
  */
typedef struct {
  __IOM uint32_t  SCK;                          /*!< (@ 0x00000000) Pin select for SCK                                         */
  __IOM uint32_t  MOSI;                         /*!< (@ 0x00000004) Pin select for MOSI signal                                 */
  __IOM uint32_t  MISO;                         /*!< (@ 0x00000008) Pin select for MISO signal                                 */
} SPIM_PSEL_Type;                               /*!< Size = 12 (0xc)                                                           */


/**
  * @brief SPIM_RXD [RXD] (RXD EasyDMA channel)
  */
typedef struct {
  __IOM uint32_t  PTR;                          /*!< (@ 0x00000000) Data pointer                                               */
  __IOM uint32_t  MAXCNT;                       /*!< (@ 0x00000004) Maximum number of bytes in receive buffer                  */
  __IM  uint32_t  AMOUNT;                       /*!< (@ 0x00000008) Number of bytes transferred in the last transaction        */
  __IOM uint32_t  LIST;                         /*!< (@ 0x0000000C) EasyDMA list type                                          */
} SPIM_RXD_Type;                                /*!< Size = 16 (0x10)                                                          */


/**
  * @brief SPIM_TXD [TXD] (TXD EasyDMA channel)
  */
typedef struct {
  __IOM uint32_t  PTR;                          /*!< (@ 0x00000000) Data pointer                                               */
  __IOM uint32_t  MAXCNT;                       /*!< (@ 0x00000004) Maximum number of bytes in transmit buffer                 */
  __IM  uint32_t  AMOUNT;                       /*!< (@ 0x00000008) Number of bytes transferred in the last transaction        */
  __IOM uint32_t  LIST;                         /*!< (@ 0x0000000C) EasyDMA list type                                          */
} SPIM_TXD_Type;                                /*!< Size = 16 (0x10)                                                          */


/**
  * @brief SPIS_PSEL [PSEL] (Unspecified)
  */
typedef struct {
  __IOM uint32_t  SCK;                          /*!< (@ 0x00000000) Pin select for SCK                                         */
  __IOM uint32_t  MISO;                         /*!< (@ 0x00000004) Pin select for MISO signal                                 */
  __IOM uint32_t  MOSI;                         /*!< (@ 0x00000008) Pin select for MOSI signal                                 */
  __IOM uint32_t  CSN;                          /*!< (@ 0x0000000C) Pin select for CSN signal                                  */
} SPIS_PSEL_Type;                               /*!< Size = 16 (0x10)                                                          */


/**
  * @brief SPIS_RXD [RXD] (Unspecified)
  */
typedef struct {
  __IOM uint32_t  PTR;                          /*!< (@ 0x00000000) RXD data pointer                                           */
  __IOM uint32_t  MAXCNT;                       /*!< (@ 0x00000004) Maximum number of bytes in receive buffer                  */
  __IM  uint32_t  AMOUNT;                       /*!< (@ 0x00000008) Number of bytes received in last granted transaction       */
  __IOM uint32_t  LIST;                         /*!< (@ 0x0000000C) EasyDMA list type                                          */
} SPIS_RXD_Type;                                /*!< Size = 16 (0x10)                                                          */


/**
  * @brief SPIS_TXD [TXD] (Unspecified)
  */
typedef struct {
  __IOM uint32_t  PTR;                          /*!< (@ 0x00000000) TXD data pointer                                           */
  __IOM uint32_t  MAXCNT;                       /*!< (@ 0x00000004) Maximum number of bytes in transmit buffer                 */
  __IM  uint32_t  AMOUNT;                       /*!< (@ 0x00000008) Number of bytes transmitted in last granted transaction    */
  __IOM uint32_t  LIST;                         /*!< (@ 0x0000000C) EasyDMA list type                                          */
} SPIS_TXD_Type;                                /*!< Size = 16 (0x10)                                                          */


/**
  * @brief TWIM_PSEL [PSEL] (Unspecified)
  */
typedef struct {
  __IOM uint32_t  SCL;                          /*!< (@ 0x00000000) Pin select for SCL signal                                  */
  __IOM uint32_t  SDA;                          /*!< (@ 0x00000004) Pin select for SDA signal                                  */
} TWIM_PSEL_Type;                               /*!< Size = 8 (0x8)                                                            */


/**
  * @brief TWIM_RXD [RXD] (RXD EasyDMA channel)
  */
typedef struct {
  __IOM uint32_t  PTR;                          /*!< (@ 0x00000000) Data pointer                                               */
  __IOM uint32_t  MAXCNT;                       /*!< (@ 0x00000004) Maximum number of bytes in receive buffer                  */
  __IM  uint32_t  AMOUNT;                       /*!< (@ 0x00000008) Number of bytes transferred in the last transaction        */
  __IOM uint32_t  LIST;                         /*!< (@ 0x0000000C) EasyDMA list type                                          */
} TWIM_RXD_Type;                                /*!< Size = 16 (0x10)                                                          */


/**
  * @brief TWIM_TXD [TXD] (TXD EasyDMA channel)
  */
typedef struct {
  __IOM uint32_t  PTR;                          /*!< (@ 0x00000000) Data pointer                                               */
  __IOM uint32_t  MAXCNT;                       /*!< (@ 0x00000004) Maximum number of bytes in transmit buffer                 */
  __IM  uint32_t  AMOUNT;                       /*!< (@ 0x00000008) Number of bytes transferred in the last transaction        */
  __IOM uint32_t  LIST;                         /*!< (@ 0x0000000C) EasyDMA list type                                          */
} TWIM_TXD_Type;                                /*!< Size = 16 (0x10)                                                          */


/**
  * @brief TWIS_PSEL [PSEL] (Unspecified)
  */
typedef struct {
  __IOM uint32_t  SCL;                          /*!< (@ 0x00000000) Pin select for SCL signal                                  */
  __IOM uint32_t  SDA;                          /*!< (@ 0x00000004) Pin select for SDA signal                                  */
} TWIS_PSEL_Type;                               /*!< Size = 8 (0x8)                                                            */


/**
  * @brief TWIS_RXD [RXD] (RXD EasyDMA channel)
  */
typedef struct {
  __IOM uint32_t  PTR;                          /*!< (@ 0x00000000) RXD Data pointer                                           */
  __IOM uint32_t  MAXCNT;                       /*!< (@ 0x00000004) Maximum number of bytes in RXD buffer                      */
  __IM  uint32_t  AMOUNT;                       /*!< (@ 0x00000008) Number of bytes transferred in the last RXD transaction    */
  __IOM uint32_t  LIST;                         /*!< (@ 0x0000000C) EasyDMA list type                                          */
} TWIS_RXD_Type;                                /*!< Size = 16 (0x10)                                                          */


/**
  * @brief TWIS_TXD [TXD] (TXD EasyDMA channel)
  */
typedef struct {
  __IOM uint32_t  PTR;                          /*!< (@ 0x00000000) TXD Data pointer                                           */
  __IOM uint32_t  MAXCNT;                       /*!< (@ 0x00000004) Maximum number of bytes in TXD buffer                      */
  __IM  uint32_t  AMOUNT;                       /*!< (@ 0x00000008) Number of bytes transferred in the last TXD transaction    */
  __IOM uint32_t  LIST;                         /*!< (@ 0x0000000C) EasyDMA list type                                          */
} TWIS_TXD_Type;                                /*!< Size = 16 (0x10)                                                          */


/**
  * @brief UARTE_PSEL [PSEL] (Unspecified)
  */
typedef struct {
  __IOM uint32_t  RTS;                          /*!< (@ 0x00000000) Pin select for RTS signal                                  */
  __IOM uint32_t  TXD;                          /*!< (@ 0x00000004) Pin select for TXD signal                                  */
  __IOM uint32_t  CTS;                          /*!< (@ 0x00000008) Pin select for CTS signal                                  */
  __IOM uint32_t  RXD;                          /*!< (@ 0x0000000C) Pin select for RXD signal                                  */
} UARTE_PSEL_Type;                              /*!< Size = 16 (0x10)                                                          */


/**
  * @brief UARTE_RXD [RXD] (RXD EasyDMA channel)
  */
typedef struct {
  __IOM uint32_t  PTR;                          /*!< (@ 0x00000000) Data pointer                                               */
  __IOM uint32_t  MAXCNT;                       /*!< (@ 0x00000004) Maximum number of bytes in receive buffer                  */
  __IM  uint32_t  AMOUNT;                       /*!< (@ 0x00000008) Number of bytes transferred in the last transaction        */
} UARTE_RXD_Type;                               /*!< Size = 12 (0xc)                                                           */


/**
  * @brief UARTE_TXD [TXD] (TXD EasyDMA channel)
  */
typedef struct {
  __IOM uint32_t  PTR;                          /*!< (@ 0x00000000) Data pointer                                               */
  __IOM uint32_t  MAXCNT;                       /*!< (@ 0x00000004) Maximum number of bytes in transmit buffer                 */
  __IM  uint32_t  AMOUNT;                       /*!< (@ 0x00000008) Number of bytes transferred in the last transaction        */
} UARTE_TXD_Type;                               /*!< Size = 12 (0xc)                                                           */


/**
  * @brief SAADC_EVENTS_CH [EVENTS_CH] (Peripheral events.)
  */
typedef struct {
  __IOM uint32_t  LIMITH;                       /*!< (@ 0x00000000) Description cluster: Last results is equal or
                                                                    above CH[n].LIMIT.HIGH                                     */
  __IOM uint32_t  LIMITL;                       /*!< (@ 0x00000004) Description cluster: Last results is equal or
                                                                    below CH[n].LIMIT.LOW                                      */
} SAADC_EVENTS_CH_Type;                         /*!< Size = 8 (0x8)                                                            */


/**
  * @brief SAADC_PUBLISH_CH [PUBLISH_CH] (Publish configuration for events)
  */
typedef struct {
  __IOM uint32_t  LIMITH;                       /*!< (@ 0x00000000) Description cluster: Publish configuration for
                                                                    event CH[n].LIMITH                                         */
  __IOM uint32_t  LIMITL;                       /*!< (@ 0x00000004) Description cluster: Publish configuration for
                                                                    event CH[n].LIMITL                                         */
} SAADC_PUBLISH_CH_Type;                        /*!< Size = 8 (0x8)                                                            */


/**
  * @brief SAADC_CH [CH] (Unspecified)
  */
typedef struct {
  __IOM uint32_t  PSELP;                        /*!< (@ 0x00000000) Description cluster: Input positive pin selection
                                                                    for CH[n]                                                  */
  __IOM uint32_t  PSELN;                        /*!< (@ 0x00000004) Description cluster: Input negative pin selection
                                                                    for CH[n]                                                  */
  __IOM uint32_t  CONFIG;                       /*!< (@ 0x00000008) Description cluster: Input configuration for
                                                                    CH[n]                                                      */
  __IOM uint32_t  LIMIT;                        /*!< (@ 0x0000000C) Description cluster: High/low limits for event
                                                                    monitoring a channel                                       */
} SAADC_CH_Type;                                /*!< Size = 16 (0x10)                                                          */


/**
  * @brief SAADC_RESULT [RESULT] (RESULT EasyDMA channel)
  */
typedef struct {
  __IOM uint32_t  PTR;                          /*!< (@ 0x00000000) Data pointer                                               */
  __IOM uint32_t  MAXCNT;                       /*!< (@ 0x00000004) Maximum number of buffer words to transfer                 */
  __IM  uint32_t  AMOUNT;                       /*!< (@ 0x00000008) Number of buffer words transferred since last
                                                                    START                                                      */
} SAADC_RESULT_Type;                            /*!< Size = 12 (0xc)                                                           */


/**
  * @brief DPPIC_TASKS_CHG [TASKS_CHG] (Channel group tasks)
  */
typedef struct {
  __OM  uint32_t  EN;                           /*!< (@ 0x00000000) Description cluster: Enable channel group n                */
  __OM  uint32_t  DIS;                          /*!< (@ 0x00000004) Description cluster: Disable channel group n               */
} DPPIC_TASKS_CHG_Type;                         /*!< Size = 8 (0x8)                                                            */


/**
  * @brief DPPIC_SUBSCRIBE_CHG [SUBSCRIBE_CHG] (Subscribe configuration for tasks)
  */
typedef struct {
  __IOM uint32_t  EN;                           /*!< (@ 0x00000000) Description cluster: Subscribe configuration
                                                                    for task CHG[n].EN                                         */
  __IOM uint32_t  DIS;                          /*!< (@ 0x00000004) Description cluster: Subscribe configuration
                                                                    for task CHG[n].DIS                                        */
} DPPIC_SUBSCRIBE_CHG_Type;                     /*!< Size = 8 (0x8)                                                            */


/**
  * @brief PWM_SEQ [SEQ] (Unspecified)
  */
typedef struct {
  __IOM uint32_t  PTR;                          /*!< (@ 0x00000000) Description cluster: Beginning address in RAM
                                                                    of this sequence                                           */
  __IOM uint32_t  CNT;                          /*!< (@ 0x00000004) Description cluster: Number of values (duty cycles)
                                                                    in this sequence                                           */
  __IOM uint32_t  REFRESH;                      /*!< (@ 0x00000008) Description cluster: Number of additional PWM
                                                                    periods between samples loaded into compare
                                                                    register                                                   */
  __IOM uint32_t  ENDDELAY;                     /*!< (@ 0x0000000C) Description cluster: Time added after the sequence         */
  __IM  uint32_t  RESERVED[4];
} PWM_SEQ_Type;                                 /*!< Size = 32 (0x20)                                                          */


/**
  * @brief PWM_PSEL [PSEL] (Unspecified)
  */
typedef struct {
  __IOM uint32_t  OUT[4];                       /*!< (@ 0x00000000) Description collection: Output pin select for
                                                                    PWM channel n                                              */
} PWM_PSEL_Type;                                /*!< Size = 16 (0x10)                                                          */


/**
  * @brief PDM_PSEL [PSEL] (Unspecified)
  */
typedef struct {
  __IOM uint32_t  CLK;                          /*!< (@ 0x00000000) Pin number configuration for PDM CLK signal                */
  __IOM uint32_t  DIN;                          /*!< (@ 0x00000004) Pin number configuration for PDM DIN signal                */
} PDM_PSEL_Type;                                /*!< Size = 8 (0x8)                                                            */


/**
  * @brief PDM_SAMPLE [SAMPLE] (Unspecified)
  */
typedef struct {
  __IOM uint32_t  PTR;                          /*!< (@ 0x00000000) RAM address pointer to write samples to with
                                                                    EasyDMA                                                    */
  __IOM uint32_t  MAXCNT;                       /*!< (@ 0x00000004) Number of samples to allocate memory for in EasyDMA
                                                                    mode                                                       */
} PDM_SAMPLE_Type;                              /*!< Size = 8 (0x8)                                                            */


/**
  * @brief I2S_CONFIG [CONFIG] (Unspecified)
  */
typedef struct {
  __IOM uint32_t  MODE;                         /*!< (@ 0x00000000) I2S mode.                                                  */
  __IOM uint32_t  RXEN;                         /*!< (@ 0x00000004) Reception (RX) enable.                                     */
  __IOM uint32_t  TXEN;                         /*!< (@ 0x00000008) Transmission (TX) enable.                                  */
  __IOM uint32_t  MCKEN;                        /*!< (@ 0x0000000C) Master clock generator enable.                             */
  __IOM uint32_t  MCKFREQ;                      /*!< (@ 0x00000010) Master clock generator frequency.                          */
  __IOM uint32_t  RATIO;                        /*!< (@ 0x00000014) MCK / LRCK ratio.                                          */
  __IOM uint32_t  SWIDTH;                       /*!< (@ 0x00000018) Sample width.                                              */
  __IOM uint32_t  ALIGN;                        /*!< (@ 0x0000001C) Alignment of sample within a frame.                        */
  __IOM uint32_t  FORMAT;                       /*!< (@ 0x00000020) Frame format.                                              */
  __IOM uint32_t  CHANNELS;                     /*!< (@ 0x00000024) Enable channels.                                           */
} I2S_CONFIG_Type;                              /*!< Size = 40 (0x28)                                                          */


/**
  * @brief I2S_RXD [RXD] (Unspecified)
  */
typedef struct {
  __IOM uint32_t  PTR;                          /*!< (@ 0x00000000) Receive buffer RAM start address.                          */
} I2S_RXD_Type;                                 /*!< Size = 4 (0x4)                                                            */


/**
  * @brief I2S_TXD [TXD] (Unspecified)
  */
typedef struct {
  __IOM uint32_t  PTR;                          /*!< (@ 0x00000000) Transmit buffer RAM start address.                         */
} I2S_TXD_Type;                                 /*!< Size = 4 (0x4)                                                            */


/**
  * @brief I2S_RXTXD [RXTXD] (Unspecified)
  */
typedef struct {
  __IOM uint32_t  MAXCNT;                       /*!< (@ 0x00000000) Size of RXD and TXD buffers.                               */
} I2S_RXTXD_Type;                               /*!< Size = 4 (0x4)                                                            */


/**
  * @brief I2S_PSEL [PSEL] (Unspecified)
  */
typedef struct {
  __IOM uint32_t  MCK;                          /*!< (@ 0x00000000) Pin select for MCK signal.                                 */
  __IOM uint32_t  SCK;                          /*!< (@ 0x00000004) Pin select for SCK signal.                                 */
  __IOM uint32_t  LRCK;                         /*!< (@ 0x00000008) Pin select for LRCK signal.                                */
  __IOM uint32_t  SDIN;                         /*!< (@ 0x0000000C) Pin select for SDIN signal.                                */
  __IOM uint32_t  SDOUT;                        /*!< (@ 0x00000010) Pin select for SDOUT signal.                               */
} I2S_PSEL_Type;                                /*!< Size = 20 (0x14)                                                          */


/**
  * @brief VMC_RAM [RAM] (Unspecified)
  */
typedef struct {
  __IOM uint32_t  POWER;                        /*!< (@ 0x00000000) Description cluster: RAMn power control register           */
  __OM  uint32_t  POWERSET;                     /*!< (@ 0x00000004) Description cluster: RAMn power control set register       */
  __OM  uint32_t  POWERCLR;                     /*!< (@ 0x00000008) Description cluster: RAMn power control clear
                                                                    register                                                   */
  __IM  uint32_t  RESERVED;
} VMC_RAM_Type;                                 /*!< Size = 16 (0x10)                                                          */


/** @} */ /* End of group Device_Peripheral_clusters */


/* =========================================================================================================================== */
/* ================                            Device Specific Peripheral Section                             ================ */
/* =========================================================================================================================== */


/** @addtogroup Device_Peripheral_peripherals
  * @{
  */



/* =========================================================================================================================== */
/* ================                                          FICR_S                                           ================ */
/* =========================================================================================================================== */


/**
  * @brief Factory Information Configuration Registers (FICR_S)
  */

typedef struct {                                /*!< (@ 0x00FF0000) FICR_S Structure                                           */
  __IM  uint32_t  RESERVED[128];
  __IOM FICR_INFO_Type INFO;                    /*!< (@ 0x00000200) Device info                                                */
  __IM  uint32_t  RESERVED1[53];
  __IOM FICR_TRIMCNF_Type TRIMCNF[256];         /*!< (@ 0x00000300) Unspecified                                                */
  __IM  uint32_t  RESERVED2[64];
  __IOM FICR_TRNG90B_Type TRNG90B;              /*!< (@ 0x00000C00) NIST800-90B RNG calibration data                           */
} NRF_FICR_Type;                                /*!< Size = 3104 (0xc20)                                                       */



/* =========================================================================================================================== */
/* ================                                          UICR_S                                           ================ */
/* =========================================================================================================================== */


/**
  * @brief User information configuration registers User information configuration registers (UICR_S)
  */

typedef struct {                                /*!< (@ 0x00FF8000) UICR_S Structure                                           */
  __IOM uint32_t  APPROTECT;                    /*!< (@ 0x00000000) Access port protection                                     */
  __IM  uint32_t  RESERVED[4];
  __IOM uint32_t  XOSC32M;                      /*!< (@ 0x00000014) Oscillator control                                         */
  __IM  uint32_t  RESERVED1;
  __IOM uint32_t  HFXOSRC;                      /*!< (@ 0x0000001C) HFXO clock source selection                                */
  __IOM uint32_t  HFXOCNT;                      /*!< (@ 0x00000020) HFXO startup counter                                       */
  __IM  uint32_t  RESERVED2[2];
  __IOM uint32_t  SECUREAPPROTECT;              /*!< (@ 0x0000002C) Secure access port protection                              */
  __IOM uint32_t  ERASEPROTECT;                 /*!< (@ 0x00000030) Erase protection                                           */
  __IM  uint32_t  RESERVED3[53];
  __IOM uint32_t  OTP[190];                     /*!< (@ 0x00000108) Description collection: One time programmable
                                                                    memory                                                     */
  __IOM UICR_KEYSLOT_Type KEYSLOT;              /*!< (@ 0x00000400) Unspecified                                                */
} NRF_UICR_Type;                                /*!< Size = 4096 (0x1000)                                                      */



/* =========================================================================================================================== */
/* ================                                           TAD_S                                           ================ */
/* =========================================================================================================================== */


/**
  * @brief Trace and debug control (TAD_S)
  */

typedef struct {                                /*!< (@ 0xE0080000) TAD_S Structure                                            */
  __OM  uint32_t  CLOCKSTART;                   /*!< (@ 0x00000000) Start all trace and debug clocks.                          */
  __OM  uint32_t  CLOCKSTOP;                    /*!< (@ 0x00000004) Stop all trace and debug clocks.                           */
  __IM  uint32_t  RESERVED[318];
  __IOM uint32_t  ENABLE;                       /*!< (@ 0x00000500) Enable debug domain and aquire selected GPIOs              */
  __IOM TAD_PSEL_Type PSEL;                     /*!< (@ 0x00000504) Unspecified                                                */
  __IOM uint32_t  TRACEPORTSPEED;               /*!< (@ 0x00000518) Clocking options for the Trace Port debug interface        */
} NRF_TAD_Type;                                 /*!< Size = 1308 (0x51c)                                                       */



/* =========================================================================================================================== */
/* ================                                           SPU_S                                           ================ */
/* =========================================================================================================================== */


/**
  * @brief System protection unit (SPU_S)
  */

typedef struct {                                /*!< (@ 0x50003000) SPU_S Structure                                            */
  __IM  uint32_t  RESERVED[64];
  __IOM uint32_t  EVENTS_RAMACCERR;             /*!< (@ 0x00000100) A security violation has been detected for the
                                                                    RAM memory space                                           */
  __IOM uint32_t  EVENTS_FLASHACCERR;           /*!< (@ 0x00000104) A security violation has been detected for the
                                                                    flash memory space                                         */
  __IOM uint32_t  EVENTS_PERIPHACCERR;          /*!< (@ 0x00000108) A security violation has been detected on one
                                                                    or several peripherals                                     */
  __IM  uint32_t  RESERVED1[29];
  __IOM uint32_t  PUBLISH_RAMACCERR;            /*!< (@ 0x00000180) Publish configuration for event RAMACCERR                  */
  __IOM uint32_t  PUBLISH_FLASHACCERR;          /*!< (@ 0x00000184) Publish configuration for event FLASHACCERR                */
  __IOM uint32_t  PUBLISH_PERIPHACCERR;         /*!< (@ 0x00000188) Publish configuration for event PERIPHACCERR               */
  __IM  uint32_t  RESERVED2[93];
  __IOM uint32_t  INTEN;                        /*!< (@ 0x00000300) Enable or disable interrupt                                */
  __IOM uint32_t  INTENSET;                     /*!< (@ 0x00000304) Enable interrupt                                           */
  __IOM uint32_t  INTENCLR;                     /*!< (@ 0x00000308) Disable interrupt                                          */
  __IM  uint32_t  RESERVED3[61];
  __IM  uint32_t  CAP;                          /*!< (@ 0x00000400) Show implemented features for the current device           */
  __IM  uint32_t  RESERVED4[15];
  __IOM SPU_EXTDOMAIN_Type EXTDOMAIN[1];        /*!< (@ 0x00000440) Unspecified                                                */
  __IM  uint32_t  RESERVED5[15];
  __IOM SPU_DPPI_Type DPPI[1];                  /*!< (@ 0x00000480) Unspecified                                                */
  __IM  uint32_t  RESERVED6[14];
  __IOM SPU_GPIOPORT_Type GPIOPORT[1];          /*!< (@ 0x000004C0) Unspecified                                                */
  __IM  uint32_t  RESERVED7[14];
  __IOM SPU_FLASHNSC_Type FLASHNSC[2];          /*!< (@ 0x00000500) Unspecified                                                */
  __IM  uint32_t  RESERVED8[12];
  __IOM SPU_RAMNSC_Type RAMNSC[2];              /*!< (@ 0x00000540) Unspecified                                                */
  __IM  uint32_t  RESERVED9[44];
  __IOM SPU_FLASHREGION_Type FLASHREGION[32];   /*!< (@ 0x00000600) Unspecified                                                */
  __IM  uint32_t  RESERVED10[32];
  __IOM SPU_RAMREGION_Type RAMREGION[32];       /*!< (@ 0x00000700) Unspecified                                                */
  __IM  uint32_t  RESERVED11[32];
  __IOM SPU_PERIPHID_Type PERIPHID[67];         /*!< (@ 0x00000800) Unspecified                                                */
} NRF_SPU_Type;                                 /*!< Size = 2316 (0x90c)                                                       */



/* =========================================================================================================================== */
/* ================                                       REGULATORS_NS                                       ================ */
/* =========================================================================================================================== */


/**
  * @brief Voltage regulators control 0 (REGULATORS_NS)
  */

typedef struct {                                /*!< (@ 0x40004000) REGULATORS_NS Structure                                    */
  __IM  uint32_t  RESERVED[320];
  __OM  uint32_t  SYSTEMOFF;                    /*!< (@ 0x00000500) System OFF register                                        */
  __IM  uint32_t  RESERVED1[29];
  __IOM uint32_t  DCDCEN;                       /*!< (@ 0x00000578) Enable DC/DC mode of the main voltage regulator.           */
} NRF_REGULATORS_Type;                          /*!< Size = 1404 (0x57c)                                                       */



/* =========================================================================================================================== */
/* ================                                         CLOCK_NS                                          ================ */
/* =========================================================================================================================== */


/**
  * @brief Clock management 0 (CLOCK_NS)
  */

typedef struct {                                /*!< (@ 0x40005000) CLOCK_NS Structure                                         */
  __OM  uint32_t  TASKS_HFCLKSTART;             /*!< (@ 0x00000000) Start HFCLK source                                         */
  __OM  uint32_t  TASKS_HFCLKSTOP;              /*!< (@ 0x00000004) Stop HFCLK source                                          */
  __OM  uint32_t  TASKS_LFCLKSTART;             /*!< (@ 0x00000008) Start LFCLK source                                         */
  __OM  uint32_t  TASKS_LFCLKSTOP;              /*!< (@ 0x0000000C) Stop LFCLK source                                          */
  __IM  uint32_t  RESERVED[28];
  __IOM uint32_t  SUBSCRIBE_HFCLKSTART;         /*!< (@ 0x00000080) Subscribe configuration for task HFCLKSTART                */
  __IOM uint32_t  SUBSCRIBE_HFCLKSTOP;          /*!< (@ 0x00000084) Subscribe configuration for task HFCLKSTOP                 */
  __IOM uint32_t  SUBSCRIBE_LFCLKSTART;         /*!< (@ 0x00000088) Subscribe configuration for task LFCLKSTART                */
  __IOM uint32_t  SUBSCRIBE_LFCLKSTOP;          /*!< (@ 0x0000008C) Subscribe configuration for task LFCLKSTOP                 */
  __IM  uint32_t  RESERVED1[28];
  __IOM uint32_t  EVENTS_HFCLKSTARTED;          /*!< (@ 0x00000100) HFCLK oscillator started                                   */
  __IOM uint32_t  EVENTS_LFCLKSTARTED;          /*!< (@ 0x00000104) LFCLK started                                              */
  __IM  uint32_t  RESERVED2[30];
  __IOM uint32_t  PUBLISH_HFCLKSTARTED;         /*!< (@ 0x00000180) Publish configuration for event HFCLKSTARTED               */
  __IOM uint32_t  PUBLISH_LFCLKSTARTED;         /*!< (@ 0x00000184) Publish configuration for event LFCLKSTARTED               */
  __IM  uint32_t  RESERVED3[94];
  __IOM uint32_t  INTEN;                        /*!< (@ 0x00000300) Enable or disable interrupt                                */
  __IOM uint32_t  INTENSET;                     /*!< (@ 0x00000304) Enable interrupt                                           */
  __IOM uint32_t  INTENCLR;                     /*!< (@ 0x00000308) Disable interrupt                                          */
  __IM  uint32_t  INTPEND;                      /*!< (@ 0x0000030C) Pending interrupts                                         */
  __IM  uint32_t  RESERVED4[62];
  __IM  uint32_t  HFCLKRUN;                     /*!< (@ 0x00000408) Status indicating that HFCLKSTART task has been
                                                                    triggered                                                  */
  __IM  uint32_t  HFCLKSTAT;                    /*!< (@ 0x0000040C) The register shows if HFXO has been requested
                                                                    by triggering HFCLKSTART task and if it
                                                                    has been started (STATE)                                   */
  __IM  uint32_t  RESERVED5;
  __IM  uint32_t  LFCLKRUN;                     /*!< (@ 0x00000414) Status indicating that LFCLKSTART task has been
                                                                    triggered                                                  */
  __IM  uint32_t  LFCLKSTAT;                    /*!< (@ 0x00000418) The register shows which LFCLK source has been
                                                                    requested (SRC) when triggering LFCLKSTART
                                                                    task and if the source has been started
                                                                    (STATE)                                                    */
  __IM  uint32_t  LFCLKSRCCOPY;                 /*!< (@ 0x0000041C) Copy of LFCLKSRC register, set after LFCLKSTART
                                                                    task has been triggered                                    */
  __IM  uint32_t  RESERVED6[62];
  __IOM uint32_t  LFCLKSRC;                     /*!< (@ 0x00000518) Clock source for the LFCLK. LFCLKSTART task starts
                                                                    starts a clock source selected with this
                                                                    register.                                                  */
} NRF_CLOCK_Type;                               /*!< Size = 1308 (0x51c)                                                       */



/* =========================================================================================================================== */
/* ================                                         POWER_NS                                          ================ */
/* =========================================================================================================================== */


/**
  * @brief Power control 0 (POWER_NS)
  */

typedef struct {                                /*!< (@ 0x40005000) POWER_NS Structure                                         */
  __IM  uint32_t  RESERVED[30];
  __OM  uint32_t  TASKS_CONSTLAT;               /*!< (@ 0x00000078) Enable constant latency mode.                              */
  __OM  uint32_t  TASKS_LOWPWR;                 /*!< (@ 0x0000007C) Enable low power mode (variable latency)                   */
  __IM  uint32_t  RESERVED1[30];
  __IOM uint32_t  SUBSCRIBE_CONSTLAT;           /*!< (@ 0x000000F8) Subscribe configuration for task CONSTLAT                  */
  __IOM uint32_t  SUBSCRIBE_LOWPWR;             /*!< (@ 0x000000FC) Subscribe configuration for task LOWPWR                    */
  __IM  uint32_t  RESERVED2[2];
  __IOM uint32_t  EVENTS_POFWARN;               /*!< (@ 0x00000108) Power failure warning                                      */
  __IM  uint32_t  RESERVED3[2];
  __IOM uint32_t  EVENTS_SLEEPENTER;            /*!< (@ 0x00000114) CPU entered WFI/WFE sleep                                  */
  __IOM uint32_t  EVENTS_SLEEPEXIT;             /*!< (@ 0x00000118) CPU exited WFI/WFE sleep                                   */
  __IM  uint32_t  RESERVED4[27];
  __IOM uint32_t  PUBLISH_POFWARN;              /*!< (@ 0x00000188) Publish configuration for event POFWARN                    */
  __IM  uint32_t  RESERVED5[2];
  __IOM uint32_t  PUBLISH_SLEEPENTER;           /*!< (@ 0x00000194) Publish configuration for event SLEEPENTER                 */
  __IOM uint32_t  PUBLISH_SLEEPEXIT;            /*!< (@ 0x00000198) Publish configuration for event SLEEPEXIT                  */
  __IM  uint32_t  RESERVED6[89];
  __IOM uint32_t  INTEN;                        /*!< (@ 0x00000300) Enable or disable interrupt                                */
  __IOM uint32_t  INTENSET;                     /*!< (@ 0x00000304) Enable interrupt                                           */
  __IOM uint32_t  INTENCLR;                     /*!< (@ 0x00000308) Disable interrupt                                          */
  __IM  uint32_t  RESERVED7[61];
  __IOM uint32_t  RESETREAS;                    /*!< (@ 0x00000400) Reset reason                                               */
  __IM  uint32_t  RESERVED8[15];
  __IM  uint32_t  POWERSTATUS;                  /*!< (@ 0x00000440) Modem domain power status                                  */
  __IM  uint32_t  RESERVED9[54];
  __IOM uint32_t  GPREGRET[2];                  /*!< (@ 0x0000051C) Description collection: General purpose retention
                                                                    register                                                   */
} NRF_POWER_Type;                               /*!< Size = 1316 (0x524)                                                       */



/* =========================================================================================================================== */
/* ================                                      CTRL_AP_PERI_S                                       ================ */
/* =========================================================================================================================== */


/**
  * @brief Control access port (CTRL_AP_PERI_S)
  */

typedef struct {                                /*!< (@ 0x50006000) CTRL_AP_PERI_S Structure                                   */
  __IM  uint32_t  RESERVED[256];
  __IOM CTRLAPPERI_MAILBOX_Type MAILBOX;        /*!< (@ 0x00000400) Unspecified                                                */
  __IM  uint32_t  RESERVED1[30];
  __IOM CTRLAPPERI_ERASEPROTECT_Type ERASEPROTECT;/*!< (@ 0x00000500) Unspecified                                              */
} NRF_CTRLAPPERI_Type;                          /*!< Size = 1288 (0x508)                                                       */



/* =========================================================================================================================== */
/* ================                                         SPIM0_NS                                          ================ */
/* =========================================================================================================================== */


/**
  * @brief Serial Peripheral Interface Master with EasyDMA 0 (SPIM0_NS)
  */

typedef struct {                                /*!< (@ 0x40008000) SPIM0_NS Structure                                         */
  __IM  uint32_t  RESERVED[4];
  __OM  uint32_t  TASKS_START;                  /*!< (@ 0x00000010) Start SPI transaction                                      */
  __OM  uint32_t  TASKS_STOP;                   /*!< (@ 0x00000014) Stop SPI transaction                                       */
  __IM  uint32_t  RESERVED1;
  __OM  uint32_t  TASKS_SUSPEND;                /*!< (@ 0x0000001C) Suspend SPI transaction                                    */
  __OM  uint32_t  TASKS_RESUME;                 /*!< (@ 0x00000020) Resume SPI transaction                                     */
  __IM  uint32_t  RESERVED2[27];
  __IOM uint32_t  SUBSCRIBE_START;              /*!< (@ 0x00000090) Subscribe configuration for task START                     */
  __IOM uint32_t  SUBSCRIBE_STOP;               /*!< (@ 0x00000094) Subscribe configuration for task STOP                      */
  __IM  uint32_t  RESERVED3;
  __IOM uint32_t  SUBSCRIBE_SUSPEND;            /*!< (@ 0x0000009C) Subscribe configuration for task SUSPEND                   */
  __IOM uint32_t  SUBSCRIBE_RESUME;             /*!< (@ 0x000000A0) Subscribe configuration for task RESUME                    */
  __IM  uint32_t  RESERVED4[24];
  __IOM uint32_t  EVENTS_STOPPED;               /*!< (@ 0x00000104) SPI transaction has stopped                                */
  __IM  uint32_t  RESERVED5[2];
  __IOM uint32_t  EVENTS_ENDRX;                 /*!< (@ 0x00000110) End of RXD buffer reached                                  */
  __IM  uint32_t  RESERVED6;
  __IOM uint32_t  EVENTS_END;                   /*!< (@ 0x00000118) End of RXD buffer and TXD buffer reached                   */
  __IM  uint32_t  RESERVED7;
  __IOM uint32_t  EVENTS_ENDTX;                 /*!< (@ 0x00000120) End of TXD buffer reached                                  */
  __IM  uint32_t  RESERVED8[10];
  __IOM uint32_t  EVENTS_STARTED;               /*!< (@ 0x0000014C) Transaction started                                        */
  __IM  uint32_t  RESERVED9[13];
  __IOM uint32_t  PUBLISH_STOPPED;              /*!< (@ 0x00000184) Publish configuration for event STOPPED                    */
  __IM  uint32_t  RESERVED10[2];
  __IOM uint32_t  PUBLISH_ENDRX;                /*!< (@ 0x00000190) Publish configuration for event ENDRX                      */
  __IM  uint32_t  RESERVED11;
  __IOM uint32_t  PUBLISH_END;                  /*!< (@ 0x00000198) Publish configuration for event END                        */
  __IM  uint32_t  RESERVED12;
  __IOM uint32_t  PUBLISH_ENDTX;                /*!< (@ 0x000001A0) Publish configuration for event ENDTX                      */
  __IM  uint32_t  RESERVED13[10];
  __IOM uint32_t  PUBLISH_STARTED;              /*!< (@ 0x000001CC) Publish configuration for event STARTED                    */
  __IM  uint32_t  RESERVED14[12];
  __IOM uint32_t  SHORTS;                       /*!< (@ 0x00000200) Shortcuts between local events and tasks                   */
  __IM  uint32_t  RESERVED15[64];
  __IOM uint32_t  INTENSET;                     /*!< (@ 0x00000304) Enable interrupt                                           */
  __IOM uint32_t  INTENCLR;                     /*!< (@ 0x00000308) Disable interrupt                                          */
  __IM  uint32_t  RESERVED16[125];
  __IOM uint32_t  ENABLE;                       /*!< (@ 0x00000500) Enable SPIM                                                */
  __IM  uint32_t  RESERVED17;
  __IOM SPIM_PSEL_Type PSEL;                    /*!< (@ 0x00000508) Unspecified                                                */
  __IM  uint32_t  RESERVED18[4];
  __IOM uint32_t  FREQUENCY;                    /*!< (@ 0x00000524) SPI frequency. Accuracy depends on the HFCLK
                                                                    source selected.                                           */
  __IM  uint32_t  RESERVED19[3];
  __IOM SPIM_RXD_Type RXD;                      /*!< (@ 0x00000534) RXD EasyDMA channel                                        */
  __IOM SPIM_TXD_Type TXD;                      /*!< (@ 0x00000544) TXD EasyDMA channel                                        */
  __IOM uint32_t  CONFIG;                       /*!< (@ 0x00000554) Configuration register                                     */
  __IM  uint32_t  RESERVED20[26];
  __IOM uint32_t  ORC;                          /*!< (@ 0x000005C0) Over-read character. Character clocked out in
                                                                    case an over-read of the TXD buffer.                       */
} NRF_SPIM_Type;                                /*!< Size = 1476 (0x5c4)                                                       */



/* =========================================================================================================================== */
/* ================                                         SPIS0_NS                                          ================ */
/* =========================================================================================================================== */


/**
  * @brief SPI Slave 0 (SPIS0_NS)
  */

typedef struct {                                /*!< (@ 0x40008000) SPIS0_NS Structure                                         */
  __IM  uint32_t  RESERVED[9];
  __OM  uint32_t  TASKS_ACQUIRE;                /*!< (@ 0x00000024) Acquire SPI semaphore                                      */
  __OM  uint32_t  TASKS_RELEASE;                /*!< (@ 0x00000028) Release SPI semaphore, enabling the SPI slave
                                                                    to acquire it                                              */
  __IM  uint32_t  RESERVED1[30];
  __IOM uint32_t  SUBSCRIBE_ACQUIRE;            /*!< (@ 0x000000A4) Subscribe configuration for task ACQUIRE                   */
  __IOM uint32_t  SUBSCRIBE_RELEASE;            /*!< (@ 0x000000A8) Subscribe configuration for task RELEASE                   */
  __IM  uint32_t  RESERVED2[22];
  __IOM uint32_t  EVENTS_END;                   /*!< (@ 0x00000104) Granted transaction completed                              */
  __IM  uint32_t  RESERVED3[2];
  __IOM uint32_t  EVENTS_ENDRX;                 /*!< (@ 0x00000110) End of RXD buffer reached                                  */
  __IM  uint32_t  RESERVED4[5];
  __IOM uint32_t  EVENTS_ACQUIRED;              /*!< (@ 0x00000128) Semaphore acquired                                         */
  __IM  uint32_t  RESERVED5[22];
  __IOM uint32_t  PUBLISH_END;                  /*!< (@ 0x00000184) Publish configuration for event END                        */
  __IM  uint32_t  RESERVED6[2];
  __IOM uint32_t  PUBLISH_ENDRX;                /*!< (@ 0x00000190) Publish configuration for event ENDRX                      */
  __IM  uint32_t  RESERVED7[5];
  __IOM uint32_t  PUBLISH_ACQUIRED;             /*!< (@ 0x000001A8) Publish configuration for event ACQUIRED                   */
  __IM  uint32_t  RESERVED8[21];
  __IOM uint32_t  SHORTS;                       /*!< (@ 0x00000200) Shortcuts between local events and tasks                   */
  __IM  uint32_t  RESERVED9[64];
  __IOM uint32_t  INTENSET;                     /*!< (@ 0x00000304) Enable interrupt                                           */
  __IOM uint32_t  INTENCLR;                     /*!< (@ 0x00000308) Disable interrupt                                          */
  __IM  uint32_t  RESERVED10[61];
  __IM  uint32_t  SEMSTAT;                      /*!< (@ 0x00000400) Semaphore status register                                  */
  __IM  uint32_t  RESERVED11[15];
  __IOM uint32_t  STATUS;                       /*!< (@ 0x00000440) Status from last transaction                               */
  __IM  uint32_t  RESERVED12[47];
  __IOM uint32_t  ENABLE;                       /*!< (@ 0x00000500) Enable SPI slave                                           */
  __IM  uint32_t  RESERVED13;
  __IOM SPIS_PSEL_Type PSEL;                    /*!< (@ 0x00000508) Unspecified                                                */
  __IM  uint32_t  RESERVED14[7];
  __IOM SPIS_RXD_Type RXD;                      /*!< (@ 0x00000534) Unspecified                                                */
  __IOM SPIS_TXD_Type TXD;                      /*!< (@ 0x00000544) Unspecified                                                */
  __IOM uint32_t  CONFIG;                       /*!< (@ 0x00000554) Configuration register                                     */
  __IM  uint32_t  RESERVED15;
  __IOM uint32_t  DEF;                          /*!< (@ 0x0000055C) Default character. Character clocked out in case
                                                                    of an ignored transaction.                                 */
  __IM  uint32_t  RESERVED16[24];
  __IOM uint32_t  ORC;                          /*!< (@ 0x000005C0) Over-read character                                        */
} NRF_SPIS_Type;                                /*!< Size = 1476 (0x5c4)                                                       */



/* =========================================================================================================================== */
/* ================                                         TWIM0_NS                                          ================ */
/* =========================================================================================================================== */


/**
  * @brief I2C compatible Two-Wire Master Interface with EasyDMA 0 (TWIM0_NS)
  */

typedef struct {                                /*!< (@ 0x40008000) TWIM0_NS Structure                                         */
  __OM  uint32_t  TASKS_STARTRX;                /*!< (@ 0x00000000) Start TWI receive sequence                                 */
  __IM  uint32_t  RESERVED;
  __OM  uint32_t  TASKS_STARTTX;                /*!< (@ 0x00000008) Start TWI transmit sequence                                */
  __IM  uint32_t  RESERVED1[2];
  __OM  uint32_t  TASKS_STOP;                   /*!< (@ 0x00000014) Stop TWI transaction. Must be issued while the
                                                                    TWI master is not suspended.                               */
  __IM  uint32_t  RESERVED2;
  __OM  uint32_t  TASKS_SUSPEND;                /*!< (@ 0x0000001C) Suspend TWI transaction                                    */
  __OM  uint32_t  TASKS_RESUME;                 /*!< (@ 0x00000020) Resume TWI transaction                                     */
  __IM  uint32_t  RESERVED3[23];
  __IOM uint32_t  SUBSCRIBE_STARTRX;            /*!< (@ 0x00000080) Subscribe configuration for task STARTRX                   */
  __IM  uint32_t  RESERVED4;
  __IOM uint32_t  SUBSCRIBE_STARTTX;            /*!< (@ 0x00000088) Subscribe configuration for task STARTTX                   */
  __IM  uint32_t  RESERVED5[2];
  __IOM uint32_t  SUBSCRIBE_STOP;               /*!< (@ 0x00000094) Subscribe configuration for task STOP                      */
  __IM  uint32_t  RESERVED6;
  __IOM uint32_t  SUBSCRIBE_SUSPEND;            /*!< (@ 0x0000009C) Subscribe configuration for task SUSPEND                   */
  __IOM uint32_t  SUBSCRIBE_RESUME;             /*!< (@ 0x000000A0) Subscribe configuration for task RESUME                    */
  __IM  uint32_t  RESERVED7[24];
  __IOM uint32_t  EVENTS_STOPPED;               /*!< (@ 0x00000104) TWI stopped                                                */
  __IM  uint32_t  RESERVED8[7];
  __IOM uint32_t  EVENTS_ERROR;                 /*!< (@ 0x00000124) TWI error                                                  */
  __IM  uint32_t  RESERVED9[8];
  __IOM uint32_t  EVENTS_SUSPENDED;             /*!< (@ 0x00000148) SUSPEND task has been issued, TWI traffic is
                                                                    now suspended.                                             */
  __IOM uint32_t  EVENTS_RXSTARTED;             /*!< (@ 0x0000014C) Receive sequence started                                   */
  __IOM uint32_t  EVENTS_TXSTARTED;             /*!< (@ 0x00000150) Transmit sequence started                                  */
  __IM  uint32_t  RESERVED10[2];
  __IOM uint32_t  EVENTS_LASTRX;                /*!< (@ 0x0000015C) Byte boundary, starting to receive the last byte           */
  __IOM uint32_t  EVENTS_LASTTX;                /*!< (@ 0x00000160) Byte boundary, starting to transmit the last
                                                                    byte                                                       */
  __IM  uint32_t  RESERVED11[8];
  __IOM uint32_t  PUBLISH_STOPPED;              /*!< (@ 0x00000184) Publish configuration for event STOPPED                    */
  __IM  uint32_t  RESERVED12[7];
  __IOM uint32_t  PUBLISH_ERROR;                /*!< (@ 0x000001A4) Publish configuration for event ERROR                      */
  __IM  uint32_t  RESERVED13[8];
  __IOM uint32_t  PUBLISH_SUSPENDED;            /*!< (@ 0x000001C8) Publish configuration for event SUSPENDED                  */
  __IOM uint32_t  PUBLISH_RXSTARTED;            /*!< (@ 0x000001CC) Publish configuration for event RXSTARTED                  */
  __IOM uint32_t  PUBLISH_TXSTARTED;            /*!< (@ 0x000001D0) Publish configuration for event TXSTARTED                  */
  __IM  uint32_t  RESERVED14[2];
  __IOM uint32_t  PUBLISH_LASTRX;               /*!< (@ 0x000001DC) Publish configuration for event LASTRX                     */
  __IOM uint32_t  PUBLISH_LASTTX;               /*!< (@ 0x000001E0) Publish configuration for event LASTTX                     */
  __IM  uint32_t  RESERVED15[7];
  __IOM uint32_t  SHORTS;                       /*!< (@ 0x00000200) Shortcuts between local events and tasks                   */
  __IM  uint32_t  RESERVED16[63];
  __IOM uint32_t  INTEN;                        /*!< (@ 0x00000300) Enable or disable interrupt                                */
  __IOM uint32_t  INTENSET;                     /*!< (@ 0x00000304) Enable interrupt                                           */
  __IOM uint32_t  INTENCLR;                     /*!< (@ 0x00000308) Disable interrupt                                          */
  __IM  uint32_t  RESERVED17[110];
  __IOM uint32_t  ERRORSRC;                     /*!< (@ 0x000004C4) Error source                                               */
  __IM  uint32_t  RESERVED18[14];
  __IOM uint32_t  ENABLE;                       /*!< (@ 0x00000500) Enable TWIM                                                */
  __IM  uint32_t  RESERVED19;
  __IOM TWIM_PSEL_Type PSEL;                    /*!< (@ 0x00000508) Unspecified                                                */
  __IM  uint32_t  RESERVED20[5];
  __IOM uint32_t  FREQUENCY;                    /*!< (@ 0x00000524) TWI frequency. Accuracy depends on the HFCLK
                                                                    source selected.                                           */
  __IM  uint32_t  RESERVED21[3];
  __IOM TWIM_RXD_Type RXD;                      /*!< (@ 0x00000534) RXD EasyDMA channel                                        */
  __IOM TWIM_TXD_Type TXD;                      /*!< (@ 0x00000544) TXD EasyDMA channel                                        */
  __IM  uint32_t  RESERVED22[13];
  __IOM uint32_t  ADDRESS;                      /*!< (@ 0x00000588) Address used in the TWI transfer                           */
} NRF_TWIM_Type;                                /*!< Size = 1420 (0x58c)                                                       */



/* =========================================================================================================================== */
/* ================                                         TWIS0_NS                                          ================ */
/* =========================================================================================================================== */


/**
  * @brief I2C compatible Two-Wire Slave Interface with EasyDMA 0 (TWIS0_NS)
  */

typedef struct {                                /*!< (@ 0x40008000) TWIS0_NS Structure                                         */
  __IM  uint32_t  RESERVED[5];
  __OM  uint32_t  TASKS_STOP;                   /*!< (@ 0x00000014) Stop TWI transaction                                       */
  __IM  uint32_t  RESERVED1;
  __OM  uint32_t  TASKS_SUSPEND;                /*!< (@ 0x0000001C) Suspend TWI transaction                                    */
  __OM  uint32_t  TASKS_RESUME;                 /*!< (@ 0x00000020) Resume TWI transaction                                     */
  __IM  uint32_t  RESERVED2[3];
  __OM  uint32_t  TASKS_PREPARERX;              /*!< (@ 0x00000030) Prepare the TWI slave to respond to a write command        */
  __OM  uint32_t  TASKS_PREPARETX;              /*!< (@ 0x00000034) Prepare the TWI slave to respond to a read command         */
  __IM  uint32_t  RESERVED3[23];
  __IOM uint32_t  SUBSCRIBE_STOP;               /*!< (@ 0x00000094) Subscribe configuration for task STOP                      */
  __IM  uint32_t  RESERVED4;
  __IOM uint32_t  SUBSCRIBE_SUSPEND;            /*!< (@ 0x0000009C) Subscribe configuration for task SUSPEND                   */
  __IOM uint32_t  SUBSCRIBE_RESUME;             /*!< (@ 0x000000A0) Subscribe configuration for task RESUME                    */
  __IM  uint32_t  RESERVED5[3];
  __IOM uint32_t  SUBSCRIBE_PREPARERX;          /*!< (@ 0x000000B0) Subscribe configuration for task PREPARERX                 */
  __IOM uint32_t  SUBSCRIBE_PREPARETX;          /*!< (@ 0x000000B4) Subscribe configuration for task PREPARETX                 */
  __IM  uint32_t  RESERVED6[19];
  __IOM uint32_t  EVENTS_STOPPED;               /*!< (@ 0x00000104) TWI stopped                                                */
  __IM  uint32_t  RESERVED7[7];
  __IOM uint32_t  EVENTS_ERROR;                 /*!< (@ 0x00000124) TWI error                                                  */
  __IM  uint32_t  RESERVED8[9];
  __IOM uint32_t  EVENTS_RXSTARTED;             /*!< (@ 0x0000014C) Receive sequence started                                   */
  __IOM uint32_t  EVENTS_TXSTARTED;             /*!< (@ 0x00000150) Transmit sequence started                                  */
  __IM  uint32_t  RESERVED9[4];
  __IOM uint32_t  EVENTS_WRITE;                 /*!< (@ 0x00000164) Write command received                                     */
  __IOM uint32_t  EVENTS_READ;                  /*!< (@ 0x00000168) Read command received                                      */
  __IM  uint32_t  RESERVED10[6];
  __IOM uint32_t  PUBLISH_STOPPED;              /*!< (@ 0x00000184) Publish configuration for event STOPPED                    */
  __IM  uint32_t  RESERVED11[7];
  __IOM uint32_t  PUBLISH_ERROR;                /*!< (@ 0x000001A4) Publish configuration for event ERROR                      */
  __IM  uint32_t  RESERVED12[9];
  __IOM uint32_t  PUBLISH_RXSTARTED;            /*!< (@ 0x000001CC) Publish configuration for event RXSTARTED                  */
  __IOM uint32_t  PUBLISH_TXSTARTED;            /*!< (@ 0x000001D0) Publish configuration for event TXSTARTED                  */
  __IM  uint32_t  RESERVED13[4];
  __IOM uint32_t  PUBLISH_WRITE;                /*!< (@ 0x000001E4) Publish configuration for event WRITE                      */
  __IOM uint32_t  PUBLISH_READ;                 /*!< (@ 0x000001E8) Publish configuration for event READ                       */
  __IM  uint32_t  RESERVED14[5];
  __IOM uint32_t  SHORTS;                       /*!< (@ 0x00000200) Shortcuts between local events and tasks                   */
  __IM  uint32_t  RESERVED15[63];
  __IOM uint32_t  INTEN;                        /*!< (@ 0x00000300) Enable or disable interrupt                                */
  __IOM uint32_t  INTENSET;                     /*!< (@ 0x00000304) Enable interrupt                                           */
  __IOM uint32_t  INTENCLR;                     /*!< (@ 0x00000308) Disable interrupt                                          */
  __IM  uint32_t  RESERVED16[113];
  __IOM uint32_t  ERRORSRC;                     /*!< (@ 0x000004D0) Error source                                               */
  __IM  uint32_t  MATCH;                        /*!< (@ 0x000004D4) Status register indicating which address had
                                                                    a match                                                    */
  __IM  uint32_t  RESERVED17[10];
  __IOM uint32_t  ENABLE;                       /*!< (@ 0x00000500) Enable TWIS                                                */
  __IM  uint32_t  RESERVED18;
  __IOM TWIS_PSEL_Type PSEL;                    /*!< (@ 0x00000508) Unspecified                                                */
  __IM  uint32_t  RESERVED19[9];
  __IOM TWIS_RXD_Type RXD;                      /*!< (@ 0x00000534) RXD EasyDMA channel                                        */
  __IOM TWIS_TXD_Type TXD;                      /*!< (@ 0x00000544) TXD EasyDMA channel                                        */
  __IM  uint32_t  RESERVED20[13];
  __IOM uint32_t  ADDRESS[2];                   /*!< (@ 0x00000588) Description collection: TWI slave address n                */
  __IM  uint32_t  RESERVED21;
  __IOM uint32_t  CONFIG;                       /*!< (@ 0x00000594) Configuration register for the address match
                                                                    mechanism                                                  */
  __IM  uint32_t  RESERVED22[10];
  __IOM uint32_t  ORC;                          /*!< (@ 0x000005C0) Over-read character. Character sent out in case
                                                                    of an over-read of the transmit buffer.                    */
} NRF_TWIS_Type;                                /*!< Size = 1476 (0x5c4)                                                       */



/* =========================================================================================================================== */
/* ================                                         UARTE0_NS                                         ================ */
/* =========================================================================================================================== */


/**
  * @brief UART with EasyDMA 0 (UARTE0_NS)
  */

typedef struct {                                /*!< (@ 0x40008000) UARTE0_NS Structure                                        */
  __OM  uint32_t  TASKS_STARTRX;                /*!< (@ 0x00000000) Start UART receiver                                        */
  __OM  uint32_t  TASKS_STOPRX;                 /*!< (@ 0x00000004) Stop UART receiver                                         */
  __OM  uint32_t  TASKS_STARTTX;                /*!< (@ 0x00000008) Start UART transmitter                                     */
  __OM  uint32_t  TASKS_STOPTX;                 /*!< (@ 0x0000000C) Stop UART transmitter                                      */
  __IM  uint32_t  RESERVED[7];
  __OM  uint32_t  TASKS_FLUSHRX;                /*!< (@ 0x0000002C) Flush RX FIFO into RX buffer                               */
  __IM  uint32_t  RESERVED1[20];
  __IOM uint32_t  SUBSCRIBE_STARTRX;            /*!< (@ 0x00000080) Subscribe configuration for task STARTRX                   */
  __IOM uint32_t  SUBSCRIBE_STOPRX;             /*!< (@ 0x00000084) Subscribe configuration for task STOPRX                    */
  __IOM uint32_t  SUBSCRIBE_STARTTX;            /*!< (@ 0x00000088) Subscribe configuration for task STARTTX                   */
  __IOM uint32_t  SUBSCRIBE_STOPTX;             /*!< (@ 0x0000008C) Subscribe configuration for task STOPTX                    */
  __IM  uint32_t  RESERVED2[7];
  __IOM uint32_t  SUBSCRIBE_FLUSHRX;            /*!< (@ 0x000000AC) Subscribe configuration for task FLUSHRX                   */
  __IM  uint32_t  RESERVED3[20];
  __IOM uint32_t  EVENTS_CTS;                   /*!< (@ 0x00000100) CTS is activated (set low). Clear To Send.                 */
  __IOM uint32_t  EVENTS_NCTS;                  /*!< (@ 0x00000104) CTS is deactivated (set high). Not Clear To Send.          */
  __IOM uint32_t  EVENTS_RXDRDY;                /*!< (@ 0x00000108) Data received in RXD (but potentially not yet
                                                                    transferred to Data RAM)                                   */
  __IM  uint32_t  RESERVED4;
  __IOM uint32_t  EVENTS_ENDRX;                 /*!< (@ 0x00000110) Receive buffer is filled up                                */
  __IM  uint32_t  RESERVED5[2];
  __IOM uint32_t  EVENTS_TXDRDY;                /*!< (@ 0x0000011C) Data sent from TXD                                         */
  __IOM uint32_t  EVENTS_ENDTX;                 /*!< (@ 0x00000120) Last TX byte transmitted                                   */
  __IOM uint32_t  EVENTS_ERROR;                 /*!< (@ 0x00000124) Error detected                                             */
  __IM  uint32_t  RESERVED6[7];
  __IOM uint32_t  EVENTS_RXTO;                  /*!< (@ 0x00000144) Receiver timeout                                           */
  __IM  uint32_t  RESERVED7;
  __IOM uint32_t  EVENTS_RXSTARTED;             /*!< (@ 0x0000014C) UART receiver has started                                  */
  __IOM uint32_t  EVENTS_TXSTARTED;             /*!< (@ 0x00000150) UART transmitter has started                               */
  __IM  uint32_t  RESERVED8;
  __IOM uint32_t  EVENTS_TXSTOPPED;             /*!< (@ 0x00000158) Transmitter stopped                                        */
  __IM  uint32_t  RESERVED9[9];
  __IOM uint32_t  PUBLISH_CTS;                  /*!< (@ 0x00000180) Publish configuration for event CTS                        */
  __IOM uint32_t  PUBLISH_NCTS;                 /*!< (@ 0x00000184) Publish configuration for event NCTS                       */
  __IOM uint32_t  PUBLISH_RXDRDY;               /*!< (@ 0x00000188) Publish configuration for event RXDRDY                     */
  __IM  uint32_t  RESERVED10;
  __IOM uint32_t  PUBLISH_ENDRX;                /*!< (@ 0x00000190) Publish configuration for event ENDRX                      */
  __IM  uint32_t  RESERVED11[2];
  __IOM uint32_t  PUBLISH_TXDRDY;               /*!< (@ 0x0000019C) Publish configuration for event TXDRDY                     */
  __IOM uint32_t  PUBLISH_ENDTX;                /*!< (@ 0x000001A0) Publish configuration for event ENDTX                      */
  __IOM uint32_t  PUBLISH_ERROR;                /*!< (@ 0x000001A4) Publish configuration for event ERROR                      */
  __IM  uint32_t  RESERVED12[7];
  __IOM uint32_t  PUBLISH_RXTO;                 /*!< (@ 0x000001C4) Publish configuration for event RXTO                       */
  __IM  uint32_t  RESERVED13;
  __IOM uint32_t  PUBLISH_RXSTARTED;            /*!< (@ 0x000001CC) Publish configuration for event RXSTARTED                  */
  __IOM uint32_t  PUBLISH_TXSTARTED;            /*!< (@ 0x000001D0) Publish configuration for event TXSTARTED                  */
  __IM  uint32_t  RESERVED14;
  __IOM uint32_t  PUBLISH_TXSTOPPED;            /*!< (@ 0x000001D8) Publish configuration for event TXSTOPPED                  */
  __IM  uint32_t  RESERVED15[9];
  __IOM uint32_t  SHORTS;                       /*!< (@ 0x00000200) Shortcuts between local events and tasks                   */
  __IM  uint32_t  RESERVED16[63];
  __IOM uint32_t  INTEN;                        /*!< (@ 0x00000300) Enable or disable interrupt                                */
  __IOM uint32_t  INTENSET;                     /*!< (@ 0x00000304) Enable interrupt                                           */
  __IOM uint32_t  INTENCLR;                     /*!< (@ 0x00000308) Disable interrupt                                          */
  __IM  uint32_t  RESERVED17[93];
  __IOM uint32_t  ERRORSRC;                     /*!< (@ 0x00000480) Error source Note : this register is read / write
                                                                    one to clear.                                              */
  __IM  uint32_t  RESERVED18[31];
  __IOM uint32_t  ENABLE;                       /*!< (@ 0x00000500) Enable UART                                                */
  __IM  uint32_t  RESERVED19;
  __IOM UARTE_PSEL_Type PSEL;                   /*!< (@ 0x00000508) Unspecified                                                */
  __IM  uint32_t  RESERVED20[3];
  __IOM uint32_t  BAUDRATE;                     /*!< (@ 0x00000524) Baud rate. Accuracy depends on the HFCLK source
                                                                    selected.                                                  */
  __IM  uint32_t  RESERVED21[3];
  __IOM UARTE_RXD_Type RXD;                     /*!< (@ 0x00000534) RXD EasyDMA channel                                        */
  __IM  uint32_t  RESERVED22;
  __IOM UARTE_TXD_Type TXD;                     /*!< (@ 0x00000544) TXD EasyDMA channel                                        */
  __IM  uint32_t  RESERVED23[7];
  __IOM uint32_t  CONFIG;                       /*!< (@ 0x0000056C) Configuration of parity and hardware flow control          */
} NRF_UARTE_Type;                               /*!< Size = 1392 (0x570)                                                       */



/* =========================================================================================================================== */
/* ================                                         GPIOTE0_S                                         ================ */
/* =========================================================================================================================== */


/**
  * @brief GPIO Tasks and Events 0 (GPIOTE0_S)
  */

typedef struct {                                /*!< (@ 0x5000D000) GPIOTE0_S Structure                                        */
  __OM  uint32_t  TASKS_OUT[8];                 /*!< (@ 0x00000000) Description collection: Task for writing to pin
                                                                    specified in CONFIG[n].PSEL. Action on pin
                                                                    is configured in CONFIG[n].POLARITY.                       */
  __IM  uint32_t  RESERVED[4];
  __OM  uint32_t  TASKS_SET[8];                 /*!< (@ 0x00000030) Description collection: Task for writing to pin
                                                                    specified in CONFIG[n].PSEL. Action on pin
                                                                    is to set it high.                                         */
  __IM  uint32_t  RESERVED1[4];
  __OM  uint32_t  TASKS_CLR[8];                 /*!< (@ 0x00000060) Description collection: Task for writing to pin
                                                                    specified in CONFIG[n].PSEL. Action on pin
                                                                    is to set it low.                                          */
  __IOM uint32_t  SUBSCRIBE_OUT[8];             /*!< (@ 0x00000080) Description collection: Subscribe configuration
                                                                    for task OUT[n]                                            */
  __IM  uint32_t  RESERVED2[4];
  __IOM uint32_t  SUBSCRIBE_SET[8];             /*!< (@ 0x000000B0) Description collection: Subscribe configuration
                                                                    for task SET[n]                                            */
  __IM  uint32_t  RESERVED3[4];
  __IOM uint32_t  SUBSCRIBE_CLR[8];             /*!< (@ 0x000000E0) Description collection: Subscribe configuration
                                                                    for task CLR[n]                                            */
  __IOM uint32_t  EVENTS_IN[8];                 /*!< (@ 0x00000100) Description collection: Event generated from
                                                                    pin specified in CONFIG[n].PSEL                            */
  __IM  uint32_t  RESERVED4[23];
  __IOM uint32_t  EVENTS_PORT;                  /*!< (@ 0x0000017C) Event generated from multiple input GPIO pins
                                                                    with SENSE mechanism enabled                               */
  __IOM uint32_t  PUBLISH_IN[8];                /*!< (@ 0x00000180) Description collection: Publish configuration
                                                                    for event IN[n]                                            */
  __IM  uint32_t  RESERVED5[23];
  __IOM uint32_t  PUBLISH_PORT;                 /*!< (@ 0x000001FC) Publish configuration for event PORT                       */
  __IM  uint32_t  RESERVED6[65];
  __IOM uint32_t  INTENSET;                     /*!< (@ 0x00000304) Enable interrupt                                           */
  __IOM uint32_t  INTENCLR;                     /*!< (@ 0x00000308) Disable interrupt                                          */
  __IM  uint32_t  RESERVED7[129];
  __IOM uint32_t  CONFIG[8];                    /*!< (@ 0x00000510) Description collection: Configuration for OUT[n],
                                                                    SET[n] and CLR[n] tasks and IN[n] event                    */
} NRF_GPIOTE_Type;                              /*!< Size = 1328 (0x530)                                                       */



/* =========================================================================================================================== */
/* ================                                         SAADC_NS                                          ================ */
/* =========================================================================================================================== */


/**
  * @brief Analog to Digital Converter 0 (SAADC_NS)
  */

typedef struct {                                /*!< (@ 0x4000E000) SAADC_NS Structure                                         */
  __OM  uint32_t  TASKS_START;                  /*!< (@ 0x00000000) Start the ADC and prepare the result buffer in
                                                                    RAM                                                        */
  __OM  uint32_t  TASKS_SAMPLE;                 /*!< (@ 0x00000004) Take one ADC sample, if scan is enabled all channels
                                                                    are sampled                                                */
  __OM  uint32_t  TASKS_STOP;                   /*!< (@ 0x00000008) Stop the ADC and terminate any on-going conversion         */
  __OM  uint32_t  TASKS_CALIBRATEOFFSET;        /*!< (@ 0x0000000C) Starts offset auto-calibration                             */
  __IM  uint32_t  RESERVED[28];
  __IOM uint32_t  SUBSCRIBE_START;              /*!< (@ 0x00000080) Subscribe configuration for task START                     */
  __IOM uint32_t  SUBSCRIBE_SAMPLE;             /*!< (@ 0x00000084) Subscribe configuration for task SAMPLE                    */
  __IOM uint32_t  SUBSCRIBE_STOP;               /*!< (@ 0x00000088) Subscribe configuration for task STOP                      */
  __IOM uint32_t  SUBSCRIBE_CALIBRATEOFFSET;    /*!< (@ 0x0000008C) Subscribe configuration for task CALIBRATEOFFSET           */
  __IM  uint32_t  RESERVED1[28];
  __IOM uint32_t  EVENTS_STARTED;               /*!< (@ 0x00000100) The ADC has started                                        */
  __IOM uint32_t  EVENTS_END;                   /*!< (@ 0x00000104) The ADC has filled up the Result buffer                    */
  __IOM uint32_t  EVENTS_DONE;                  /*!< (@ 0x00000108) A conversion task has been completed. Depending
                                                                    on the mode, multiple conversions might
                                                                    be needed for a result to be transferred
                                                                    to RAM.                                                    */
  __IOM uint32_t  EVENTS_RESULTDONE;            /*!< (@ 0x0000010C) A result is ready to get transferred to RAM.               */
  __IOM uint32_t  EVENTS_CALIBRATEDONE;         /*!< (@ 0x00000110) Calibration is complete                                    */
  __IOM uint32_t  EVENTS_STOPPED;               /*!< (@ 0x00000114) The ADC has stopped                                        */
  __IOM SAADC_EVENTS_CH_Type EVENTS_CH[8];      /*!< (@ 0x00000118) Peripheral events.                                         */
  __IM  uint32_t  RESERVED2[10];
  __IOM uint32_t  PUBLISH_STARTED;              /*!< (@ 0x00000180) Publish configuration for event STARTED                    */
  __IOM uint32_t  PUBLISH_END;                  /*!< (@ 0x00000184) Publish configuration for event END                        */
  __IOM uint32_t  PUBLISH_DONE;                 /*!< (@ 0x00000188) Publish configuration for event DONE                       */
  __IOM uint32_t  PUBLISH_RESULTDONE;           /*!< (@ 0x0000018C) Publish configuration for event RESULTDONE                 */
  __IOM uint32_t  PUBLISH_CALIBRATEDONE;        /*!< (@ 0x00000190) Publish configuration for event CALIBRATEDONE              */
  __IOM uint32_t  PUBLISH_STOPPED;              /*!< (@ 0x00000194) Publish configuration for event STOPPED                    */
  __IOM SAADC_PUBLISH_CH_Type PUBLISH_CH[8];    /*!< (@ 0x00000198) Publish configuration for events                           */
  __IM  uint32_t  RESERVED3[74];
  __IOM uint32_t  INTEN;                        /*!< (@ 0x00000300) Enable or disable interrupt                                */
  __IOM uint32_t  INTENSET;                     /*!< (@ 0x00000304) Enable interrupt                                           */
  __IOM uint32_t  INTENCLR;                     /*!< (@ 0x00000308) Disable interrupt                                          */
  __IM  uint32_t  RESERVED4[61];
  __IM  uint32_t  STATUS;                       /*!< (@ 0x00000400) Status                                                     */
  __IM  uint32_t  RESERVED5[63];
  __IOM uint32_t  ENABLE;                       /*!< (@ 0x00000500) Enable or disable ADC                                      */
  __IM  uint32_t  RESERVED6[3];
  __IOM SAADC_CH_Type CH[8];                    /*!< (@ 0x00000510) Unspecified                                                */
  __IM  uint32_t  RESERVED7[24];
  __IOM uint32_t  RESOLUTION;                   /*!< (@ 0x000005F0) Resolution configuration                                   */
  __IOM uint32_t  OVERSAMPLE;                   /*!< (@ 0x000005F4) Oversampling configuration. OVERSAMPLE should
                                                                    not be combined with SCAN. The RESOLUTION
                                                                    is applied before averaging, thus for high
                                                                    OVERSAMPLE a higher RESOLUTION should be
                                                                    used.                                                      */
  __IOM uint32_t  SAMPLERATE;                   /*!< (@ 0x000005F8) Controls normal or continuous sample rate                  */
  __IM  uint32_t  RESERVED8[12];
  __IOM SAADC_RESULT_Type RESULT;               /*!< (@ 0x0000062C) RESULT EasyDMA channel                                     */
} NRF_SAADC_Type;                               /*!< Size = 1592 (0x638)                                                       */



/* =========================================================================================================================== */
/* ================                                         TIMER0_NS                                         ================ */
/* =========================================================================================================================== */


/**
  * @brief Timer/Counter 0 (TIMER0_NS)
  */

typedef struct {                                /*!< (@ 0x4000F000) TIMER0_NS Structure                                        */
  __OM  uint32_t  TASKS_START;                  /*!< (@ 0x00000000) Start Timer                                                */
  __OM  uint32_t  TASKS_STOP;                   /*!< (@ 0x00000004) Stop Timer                                                 */
  __OM  uint32_t  TASKS_COUNT;                  /*!< (@ 0x00000008) Increment Timer (Counter mode only)                        */
  __OM  uint32_t  TASKS_CLEAR;                  /*!< (@ 0x0000000C) Clear time                                                 */
  __OM  uint32_t  TASKS_SHUTDOWN;               /*!< (@ 0x00000010) Deprecated register - Shut down timer                      */
  __IM  uint32_t  RESERVED[11];
  __OM  uint32_t  TASKS_CAPTURE[6];             /*!< (@ 0x00000040) Description collection: Capture Timer value to
                                                                    CC[n] register                                             */
  __IM  uint32_t  RESERVED1[10];
  __IOM uint32_t  SUBSCRIBE_START;              /*!< (@ 0x00000080) Subscribe configuration for task START                     */
  __IOM uint32_t  SUBSCRIBE_STOP;               /*!< (@ 0x00000084) Subscribe configuration for task STOP                      */
  __IOM uint32_t  SUBSCRIBE_COUNT;              /*!< (@ 0x00000088) Subscribe configuration for task COUNT                     */
  __IOM uint32_t  SUBSCRIBE_CLEAR;              /*!< (@ 0x0000008C) Subscribe configuration for task CLEAR                     */
  __IOM uint32_t  SUBSCRIBE_SHUTDOWN;           /*!< (@ 0x00000090) Deprecated register - Subscribe configuration
                                                                    for task SHUTDOWN                                          */
  __IM  uint32_t  RESERVED2[11];
  __IOM uint32_t  SUBSCRIBE_CAPTURE[6];         /*!< (@ 0x000000C0) Description collection: Subscribe configuration
                                                                    for task CAPTURE[n]                                        */
  __IM  uint32_t  RESERVED3[26];
  __IOM uint32_t  EVENTS_COMPARE[6];            /*!< (@ 0x00000140) Description collection: Compare event on CC[n]
                                                                    match                                                      */
  __IM  uint32_t  RESERVED4[26];
  __IOM uint32_t  PUBLISH_COMPARE[6];           /*!< (@ 0x000001C0) Description collection: Publish configuration
                                                                    for event COMPARE[n]                                       */
  __IM  uint32_t  RESERVED5[10];
  __IOM uint32_t  SHORTS;                       /*!< (@ 0x00000200) Shortcuts between local events and tasks                   */
  __IM  uint32_t  RESERVED6[64];
  __IOM uint32_t  INTENSET;                     /*!< (@ 0x00000304) Enable interrupt                                           */
  __IOM uint32_t  INTENCLR;                     /*!< (@ 0x00000308) Disable interrupt                                          */
  __IM  uint32_t  RESERVED7[126];
  __IOM uint32_t  MODE;                         /*!< (@ 0x00000504) Timer mode selection                                       */
  __IOM uint32_t  BITMODE;                      /*!< (@ 0x00000508) Configure the number of bits used by the TIMER             */
  __IM  uint32_t  RESERVED8;
  __IOM uint32_t  PRESCALER;                    /*!< (@ 0x00000510) Timer prescaler register                                   */
  __IOM uint32_t  ONESHOTEN[6];                 /*!< (@ 0x00000514) Description collection: Enable one-shot operation
                                                                    for Capture/Compare channel n                              */
  __IM  uint32_t  RESERVED9[5];
  __IOM uint32_t  CC[6];                        /*!< (@ 0x00000540) Description collection: Capture/Compare register
                                                                    n                                                          */
} NRF_TIMER_Type;                               /*!< Size = 1368 (0x558)                                                       */



/* =========================================================================================================================== */
/* ================                                          RTC0_NS                                          ================ */
/* =========================================================================================================================== */


/**
  * @brief Real-time counter 0 (RTC0_NS)
  */

typedef struct {                                /*!< (@ 0x40014000) RTC0_NS Structure                                          */
  __OM  uint32_t  TASKS_START;                  /*!< (@ 0x00000000) Start RTC counter                                          */
  __OM  uint32_t  TASKS_STOP;                   /*!< (@ 0x00000004) Stop RTC counter                                           */
  __OM  uint32_t  TASKS_CLEAR;                  /*!< (@ 0x00000008) Clear RTC counter                                          */
  __OM  uint32_t  TASKS_TRIGOVRFLW;             /*!< (@ 0x0000000C) Set counter to 0xFFFFF0                                    */
  __IM  uint32_t  RESERVED[28];
  __IOM uint32_t  SUBSCRIBE_START;              /*!< (@ 0x00000080) Subscribe configuration for task START                     */
  __IOM uint32_t  SUBSCRIBE_STOP;               /*!< (@ 0x00000084) Subscribe configuration for task STOP                      */
  __IOM uint32_t  SUBSCRIBE_CLEAR;              /*!< (@ 0x00000088) Subscribe configuration for task CLEAR                     */
  __IOM uint32_t  SUBSCRIBE_TRIGOVRFLW;         /*!< (@ 0x0000008C) Subscribe configuration for task TRIGOVRFLW                */
  __IM  uint32_t  RESERVED1[28];
  __IOM uint32_t  EVENTS_TICK;                  /*!< (@ 0x00000100) Event on counter increment                                 */
  __IOM uint32_t  EVENTS_OVRFLW;                /*!< (@ 0x00000104) Event on counter overflow                                  */
  __IM  uint32_t  RESERVED2[14];
  __IOM uint32_t  EVENTS_COMPARE[4];            /*!< (@ 0x00000140) Description collection: Compare event on CC[n]
                                                                    match                                                      */
  __IM  uint32_t  RESERVED3[12];
  __IOM uint32_t  PUBLISH_TICK;                 /*!< (@ 0x00000180) Publish configuration for event TICK                       */
  __IOM uint32_t  PUBLISH_OVRFLW;               /*!< (@ 0x00000184) Publish configuration for event OVRFLW                     */
  __IM  uint32_t  RESERVED4[14];
  __IOM uint32_t  PUBLISH_COMPARE[4];           /*!< (@ 0x000001C0) Description collection: Publish configuration
                                                                    for event COMPARE[n]                                       */
  __IM  uint32_t  RESERVED5[77];
  __IOM uint32_t  INTENSET;                     /*!< (@ 0x00000304) Enable interrupt                                           */
  __IOM uint32_t  INTENCLR;                     /*!< (@ 0x00000308) Disable interrupt                                          */
  __IM  uint32_t  RESERVED6[13];
  __IOM uint32_t  EVTEN;                        /*!< (@ 0x00000340) Enable or disable event routing                            */
  __IOM uint32_t  EVTENSET;                     /*!< (@ 0x00000344) Enable event routing                                       */
  __IOM uint32_t  EVTENCLR;                     /*!< (@ 0x00000348) Disable event routing                                      */
  __IM  uint32_t  RESERVED7[110];
  __IM  uint32_t  COUNTER;                      /*!< (@ 0x00000504) Current counter value                                      */
  __IOM uint32_t  PRESCALER;                    /*!< (@ 0x00000508) 12-bit prescaler for counter frequency (32768/(PRESCALER+1)).
                                                                    Must be written when RTC is stopped.                       */
  __IM  uint32_t  RESERVED8[13];
  __IOM uint32_t  CC[4];                        /*!< (@ 0x00000540) Description collection: Compare register n                 */
} NRF_RTC_Type;                                 /*!< Size = 1360 (0x550)                                                       */



/* =========================================================================================================================== */
/* ================                                         DPPIC_NS                                          ================ */
/* =========================================================================================================================== */


/**
  * @brief Distributed Programmable Peripheral Interconnect Controller 0 (DPPIC_NS)
  */

typedef struct {                                /*!< (@ 0x40017000) DPPIC_NS Structure                                         */
  __OM  DPPIC_TASKS_CHG_Type TASKS_CHG[6];      /*!< (@ 0x00000000) Channel group tasks                                        */
  __IM  uint32_t  RESERVED[20];
  __IOM DPPIC_SUBSCRIBE_CHG_Type SUBSCRIBE_CHG[6];/*!< (@ 0x00000080) Subscribe configuration for tasks                        */
  __IM  uint32_t  RESERVED1[276];
  __IOM uint32_t  CHEN;                         /*!< (@ 0x00000500) Channel enable register                                    */
  __IOM uint32_t  CHENSET;                      /*!< (@ 0x00000504) Channel enable set register                                */
  __IOM uint32_t  CHENCLR;                      /*!< (@ 0x00000508) Channel enable clear register                              */
  __IM  uint32_t  RESERVED2[189];
  __IOM uint32_t  CHG[6];                       /*!< (@ 0x00000800) Description collection: Channel group n Note:
                                                                    Writes to this register is ignored if either
                                                                    SUBSCRIBE_CHG[n].EN/DIS are enabled.                       */
} NRF_DPPIC_Type;                               /*!< Size = 2072 (0x818)                                                       */



/* =========================================================================================================================== */
/* ================                                          WDT_NS                                           ================ */
/* =========================================================================================================================== */


/**
  * @brief Watchdog Timer 0 (WDT_NS)
  */

typedef struct {                                /*!< (@ 0x40018000) WDT_NS Structure                                           */
  __OM  uint32_t  TASKS_START;                  /*!< (@ 0x00000000) Start the watchdog                                         */
  __IM  uint32_t  RESERVED[31];
  __IOM uint32_t  SUBSCRIBE_START;              /*!< (@ 0x00000080) Subscribe configuration for task START                     */
  __IM  uint32_t  RESERVED1[31];
  __IOM uint32_t  EVENTS_TIMEOUT;               /*!< (@ 0x00000100) Watchdog timeout                                           */
  __IM  uint32_t  RESERVED2[31];
  __IOM uint32_t  PUBLISH_TIMEOUT;              /*!< (@ 0x00000180) Publish configuration for event TIMEOUT                    */
  __IM  uint32_t  RESERVED3[96];
  __IOM uint32_t  INTENSET;                     /*!< (@ 0x00000304) Enable interrupt                                           */
  __IOM uint32_t  INTENCLR;                     /*!< (@ 0x00000308) Disable interrupt                                          */
  __IM  uint32_t  RESERVED4[61];
  __IM  uint32_t  RUNSTATUS;                    /*!< (@ 0x00000400) Run status                                                 */
  __IM  uint32_t  REQSTATUS;                    /*!< (@ 0x00000404) Request status                                             */
  __IM  uint32_t  RESERVED5[63];
  __IOM uint32_t  CRV;                          /*!< (@ 0x00000504) Counter reload value                                       */
  __IOM uint32_t  RREN;                         /*!< (@ 0x00000508) Enable register for reload request registers               */
  __IOM uint32_t  CONFIG;                       /*!< (@ 0x0000050C) Configuration register                                     */
  __IM  uint32_t  RESERVED6[60];
  __OM  uint32_t  RR[8];                        /*!< (@ 0x00000600) Description collection: Reload request n                   */
} NRF_WDT_Type;                                 /*!< Size = 1568 (0x620)                                                       */



/* =========================================================================================================================== */
/* ================                                          EGU0_NS                                          ================ */
/* =========================================================================================================================== */


/**
  * @brief Event generator unit 0 (EGU0_NS)
  */

typedef struct {                                /*!< (@ 0x4001B000) EGU0_NS Structure                                          */
  __OM  uint32_t  TASKS_TRIGGER[16];            /*!< (@ 0x00000000) Description collection: Trigger n for triggering
                                                                    the corresponding TRIGGERED[n] event                       */
  __IM  uint32_t  RESERVED[16];
  __IOM uint32_t  SUBSCRIBE_TRIGGER[16];        /*!< (@ 0x00000080) Description collection: Subscribe configuration
                                                                    for task TRIGGER[n]                                        */
  __IM  uint32_t  RESERVED1[16];
  __IOM uint32_t  EVENTS_TRIGGERED[16];         /*!< (@ 0x00000100) Description collection: Event number n generated
                                                                    by triggering the corresponding TRIGGER[n]
                                                                    task                                                       */
  __IM  uint32_t  RESERVED2[16];
  __IOM uint32_t  PUBLISH_TRIGGERED[16];        /*!< (@ 0x00000180) Description collection: Publish configuration
                                                                    for event TRIGGERED[n]                                     */
  __IM  uint32_t  RESERVED3[80];
  __IOM uint32_t  INTEN;                        /*!< (@ 0x00000300) Enable or disable interrupt                                */
  __IOM uint32_t  INTENSET;                     /*!< (@ 0x00000304) Enable interrupt                                           */
  __IOM uint32_t  INTENCLR;                     /*!< (@ 0x00000308) Disable interrupt                                          */
} NRF_EGU_Type;                                 /*!< Size = 780 (0x30c)                                                        */



/* =========================================================================================================================== */
/* ================                                          PWM0_NS                                          ================ */
/* =========================================================================================================================== */


/**
  * @brief Pulse width modulation unit 0 (PWM0_NS)
  */

typedef struct {                                /*!< (@ 0x40021000) PWM0_NS Structure                                          */
  __IM  uint32_t  RESERVED;
  __OM  uint32_t  TASKS_STOP;                   /*!< (@ 0x00000004) Stops PWM pulse generation on all channels at
                                                                    the end of current PWM period, and stops
                                                                    sequence playback                                          */
  __OM  uint32_t  TASKS_SEQSTART[2];            /*!< (@ 0x00000008) Description collection: Loads the first PWM value
                                                                    on all enabled channels from sequence n,
                                                                    and starts playing that sequence at the
                                                                    rate defined in SEQ[n]REFRESH and/or DECODER.MODE.
                                                                    Causes PWM generation to start if not running.             */
  __OM  uint32_t  TASKS_NEXTSTEP;               /*!< (@ 0x00000010) Steps by one value in the current sequence on
                                                                    all enabled channels if DECODER.MODE=NextStep.
                                                                    Does not cause PWM generation to start if
                                                                    not running.                                               */
  __IM  uint32_t  RESERVED1[28];
  __IOM uint32_t  SUBSCRIBE_STOP;               /*!< (@ 0x00000084) Subscribe configuration for task STOP                      */
  __IOM uint32_t  SUBSCRIBE_SEQSTART[2];        /*!< (@ 0x00000088) Description collection: Subscribe configuration
                                                                    for task SEQSTART[n]                                       */
  __IOM uint32_t  SUBSCRIBE_NEXTSTEP;           /*!< (@ 0x00000090) Subscribe configuration for task NEXTSTEP                  */
  __IM  uint32_t  RESERVED2[28];
  __IOM uint32_t  EVENTS_STOPPED;               /*!< (@ 0x00000104) Response to STOP task, emitted when PWM pulses
                                                                    are no longer generated                                    */
  __IOM uint32_t  EVENTS_SEQSTARTED[2];         /*!< (@ 0x00000108) Description collection: First PWM period started
                                                                    on sequence n                                              */
  __IOM uint32_t  EVENTS_SEQEND[2];             /*!< (@ 0x00000110) Description collection: Emitted at end of every
                                                                    sequence n, when last value from RAM has
                                                                    been applied to wave counter                               */
  __IOM uint32_t  EVENTS_PWMPERIODEND;          /*!< (@ 0x00000118) Emitted at the end of each PWM period                      */
  __IOM uint32_t  EVENTS_LOOPSDONE;             /*!< (@ 0x0000011C) Concatenated sequences have been played the amount
                                                                    of times defined in LOOP.CNT                               */
  __IM  uint32_t  RESERVED3[25];
  __IOM uint32_t  PUBLISH_STOPPED;              /*!< (@ 0x00000184) Publish configuration for event STOPPED                    */
  __IOM uint32_t  PUBLISH_SEQSTARTED[2];        /*!< (@ 0x00000188) Description collection: Publish configuration
                                                                    for event SEQSTARTED[n]                                    */
  __IOM uint32_t  PUBLISH_SEQEND[2];            /*!< (@ 0x00000190) Description collection: Publish configuration
                                                                    for event SEQEND[n]                                        */
  __IOM uint32_t  PUBLISH_PWMPERIODEND;         /*!< (@ 0x00000198) Publish configuration for event PWMPERIODEND               */
  __IOM uint32_t  PUBLISH_LOOPSDONE;            /*!< (@ 0x0000019C) Publish configuration for event LOOPSDONE                  */
  __IM  uint32_t  RESERVED4[24];
  __IOM uint32_t  SHORTS;                       /*!< (@ 0x00000200) Shortcuts between local events and tasks                   */
  __IM  uint32_t  RESERVED5[63];
  __IOM uint32_t  INTEN;                        /*!< (@ 0x00000300) Enable or disable interrupt                                */
  __IOM uint32_t  INTENSET;                     /*!< (@ 0x00000304) Enable interrupt                                           */
  __IOM uint32_t  INTENCLR;                     /*!< (@ 0x00000308) Disable interrupt                                          */
  __IM  uint32_t  RESERVED6[125];
  __IOM uint32_t  ENABLE;                       /*!< (@ 0x00000500) PWM module enable register                                 */
  __IOM uint32_t  MODE;                         /*!< (@ 0x00000504) Selects operating mode of the wave counter                 */
  __IOM uint32_t  COUNTERTOP;                   /*!< (@ 0x00000508) Value up to which the pulse generator counter
                                                                    counts                                                     */
  __IOM uint32_t  PRESCALER;                    /*!< (@ 0x0000050C) Configuration for PWM_CLK                                  */
  __IOM uint32_t  DECODER;                      /*!< (@ 0x00000510) Configuration of the decoder                               */
  __IOM uint32_t  LOOP;                         /*!< (@ 0x00000514) Number of playbacks of a loop                              */
  __IM  uint32_t  RESERVED7[2];
  __IOM PWM_SEQ_Type SEQ[2];                    /*!< (@ 0x00000520) Unspecified                                                */
  __IOM PWM_PSEL_Type PSEL;                     /*!< (@ 0x00000560) Unspecified                                                */
} NRF_PWM_Type;                                 /*!< Size = 1392 (0x570)                                                       */



/* =========================================================================================================================== */
/* ================                                          PDM_NS                                           ================ */
/* =========================================================================================================================== */


/**
  * @brief Pulse Density Modulation (Digital Microphone) Interface 0 (PDM_NS)
  */

typedef struct {                                /*!< (@ 0x40026000) PDM_NS Structure                                           */
  __OM  uint32_t  TASKS_START;                  /*!< (@ 0x00000000) Starts continuous PDM transfer                             */
  __OM  uint32_t  TASKS_STOP;                   /*!< (@ 0x00000004) Stops PDM transfer                                         */
  __IM  uint32_t  RESERVED[30];
  __IOM uint32_t  SUBSCRIBE_START;              /*!< (@ 0x00000080) Subscribe configuration for task START                     */
  __IOM uint32_t  SUBSCRIBE_STOP;               /*!< (@ 0x00000084) Subscribe configuration for task STOP                      */
  __IM  uint32_t  RESERVED1[30];
  __IOM uint32_t  EVENTS_STARTED;               /*!< (@ 0x00000100) PDM transfer has started                                   */
  __IOM uint32_t  EVENTS_STOPPED;               /*!< (@ 0x00000104) PDM transfer has finished                                  */
  __IOM uint32_t  EVENTS_END;                   /*!< (@ 0x00000108) The PDM has written the last sample specified
                                                                    by SAMPLE.MAXCNT (or the last sample after
                                                                    a STOP task has been received) to Data RAM                 */
  __IM  uint32_t  RESERVED2[29];
  __IOM uint32_t  PUBLISH_STARTED;              /*!< (@ 0x00000180) Publish configuration for event STARTED                    */
  __IOM uint32_t  PUBLISH_STOPPED;              /*!< (@ 0x00000184) Publish configuration for event STOPPED                    */
  __IOM uint32_t  PUBLISH_END;                  /*!< (@ 0x00000188) Publish configuration for event END                        */
  __IM  uint32_t  RESERVED3[93];
  __IOM uint32_t  INTEN;                        /*!< (@ 0x00000300) Enable or disable interrupt                                */
  __IOM uint32_t  INTENSET;                     /*!< (@ 0x00000304) Enable interrupt                                           */
  __IOM uint32_t  INTENCLR;                     /*!< (@ 0x00000308) Disable interrupt                                          */
  __IM  uint32_t  RESERVED4[125];
  __IOM uint32_t  ENABLE;                       /*!< (@ 0x00000500) PDM module enable register                                 */
  __IOM uint32_t  PDMCLKCTRL;                   /*!< (@ 0x00000504) PDM clock generator control                                */
  __IOM uint32_t  MODE;                         /*!< (@ 0x00000508) Defines the routing of the connected PDM microphones'
                                                                    signals                                                    */
  __IM  uint32_t  RESERVED5[3];
  __IOM uint32_t  GAINL;                        /*!< (@ 0x00000518) Left output gain adjustment                                */
  __IOM uint32_t  GAINR;                        /*!< (@ 0x0000051C) Right output gain adjustment                               */
  __IOM uint32_t  RATIO;                        /*!< (@ 0x00000520) Selects the ratio between PDM_CLK and output
                                                                    sample rate. Change PDMCLKCTRL accordingly.                */
  __IM  uint32_t  RESERVED6[7];
  __IOM PDM_PSEL_Type PSEL;                     /*!< (@ 0x00000540) Unspecified                                                */
  __IM  uint32_t  RESERVED7[6];
  __IOM PDM_SAMPLE_Type SAMPLE;                 /*!< (@ 0x00000560) Unspecified                                                */
} NRF_PDM_Type;                                 /*!< Size = 1384 (0x568)                                                       */



/* =========================================================================================================================== */
/* ================                                          I2S_NS                                           ================ */
/* =========================================================================================================================== */


/**
  * @brief Inter-IC Sound 0 (I2S_NS)
  */

typedef struct {                                /*!< (@ 0x40028000) I2S_NS Structure                                           */
  __OM  uint32_t  TASKS_START;                  /*!< (@ 0x00000000) Starts continuous I2S transfer. Also starts MCK
                                                                    generator when this is enabled.                            */
  __OM  uint32_t  TASKS_STOP;                   /*!< (@ 0x00000004) Stops I2S transfer. Also stops MCK generator.
                                                                    Triggering this task will cause the STOPPED
                                                                    event to be generated.                                     */
  __IM  uint32_t  RESERVED[30];
  __IOM uint32_t  SUBSCRIBE_START;              /*!< (@ 0x00000080) Subscribe configuration for task START                     */
  __IOM uint32_t  SUBSCRIBE_STOP;               /*!< (@ 0x00000084) Subscribe configuration for task STOP                      */
  __IM  uint32_t  RESERVED1[31];
  __IOM uint32_t  EVENTS_RXPTRUPD;              /*!< (@ 0x00000104) The RXD.PTR register has been copied to internal
                                                                    double-buffers. When the I2S module is started
                                                                    and RX is enabled, this event will be generated
                                                                    for every RXTXD.MAXCNT words that are received
                                                                    on the SDIN pin.                                           */
  __IOM uint32_t  EVENTS_STOPPED;               /*!< (@ 0x00000108) I2S transfer stopped.                                      */
  __IM  uint32_t  RESERVED2[2];
  __IOM uint32_t  EVENTS_TXPTRUPD;              /*!< (@ 0x00000114) The TDX.PTR register has been copied to internal
                                                                    double-buffers. When the I2S module is started
                                                                    and TX is enabled, this event will be generated
                                                                    for every RXTXD.MAXCNT words that are sent
                                                                    on the SDOUT pin.                                          */
  __IM  uint32_t  RESERVED3[27];
  __IOM uint32_t  PUBLISH_RXPTRUPD;             /*!< (@ 0x00000184) Publish configuration for event RXPTRUPD                   */
  __IOM uint32_t  PUBLISH_STOPPED;              /*!< (@ 0x00000188) Publish configuration for event STOPPED                    */
  __IM  uint32_t  RESERVED4[2];
  __IOM uint32_t  PUBLISH_TXPTRUPD;             /*!< (@ 0x00000194) Publish configuration for event TXPTRUPD                   */
  __IM  uint32_t  RESERVED5[90];
  __IOM uint32_t  INTEN;                        /*!< (@ 0x00000300) Enable or disable interrupt                                */
  __IOM uint32_t  INTENSET;                     /*!< (@ 0x00000304) Enable interrupt                                           */
  __IOM uint32_t  INTENCLR;                     /*!< (@ 0x00000308) Disable interrupt                                          */
  __IM  uint32_t  RESERVED6[125];
  __IOM uint32_t  ENABLE;                       /*!< (@ 0x00000500) Enable I2S module.                                         */
  __IOM I2S_CONFIG_Type CONFIG;                 /*!< (@ 0x00000504) Unspecified                                                */
  __IM  uint32_t  RESERVED7[3];
  __IOM I2S_RXD_Type RXD;                       /*!< (@ 0x00000538) Unspecified                                                */
  __IM  uint32_t  RESERVED8;
  __IOM I2S_TXD_Type TXD;                       /*!< (@ 0x00000540) Unspecified                                                */
  __IM  uint32_t  RESERVED9[3];
  __IOM I2S_RXTXD_Type RXTXD;                   /*!< (@ 0x00000550) Unspecified                                                */
  __IM  uint32_t  RESERVED10[3];
  __IOM I2S_PSEL_Type PSEL;                     /*!< (@ 0x00000560) Unspecified                                                */
} NRF_I2S_Type;                                 /*!< Size = 1396 (0x574)                                                       */



/* =========================================================================================================================== */
/* ================                                          IPC_NS                                           ================ */
/* =========================================================================================================================== */


/**
  * @brief Inter Processor Communication 0 (IPC_NS)
  */

typedef struct {                                /*!< (@ 0x4002A000) IPC_NS Structure                                           */
  __OM  uint32_t  TASKS_SEND[8];                /*!< (@ 0x00000000) Description collection: Trigger events on channel
                                                                    enabled in SEND_CNF[n].                                    */
  __IM  uint32_t  RESERVED[24];
  __IOM uint32_t  SUBSCRIBE_SEND[8];            /*!< (@ 0x00000080) Description collection: Subscribe configuration
                                                                    for task SEND[n]                                           */
  __IM  uint32_t  RESERVED1[24];
  __IOM uint32_t  EVENTS_RECEIVE[8];            /*!< (@ 0x00000100) Description collection: Event received on one
                                                                    or more of the enabled channels in RECEIVE_CNF[n].         */
  __IM  uint32_t  RESERVED2[24];
  __IOM uint32_t  PUBLISH_RECEIVE[8];           /*!< (@ 0x00000180) Description collection: Publish configuration
                                                                    for event RECEIVE[n]                                       */
  __IM  uint32_t  RESERVED3[88];
  __IOM uint32_t  INTEN;                        /*!< (@ 0x00000300) Enable or disable interrupt                                */
  __IOM uint32_t  INTENSET;                     /*!< (@ 0x00000304) Enable interrupt                                           */
  __IOM uint32_t  INTENCLR;                     /*!< (@ 0x00000308) Disable interrupt                                          */
  __IM  uint32_t  INTPEND;                      /*!< (@ 0x0000030C) Pending interrupts                                         */
  __IM  uint32_t  RESERVED4[128];
  __IOM uint32_t  SEND_CNF[8];                  /*!< (@ 0x00000510) Description collection: Send event configuration
                                                                    for TASKS_SEND[n].                                         */
  __IM  uint32_t  RESERVED5[24];
  __IOM uint32_t  RECEIVE_CNF[8];               /*!< (@ 0x00000590) Description collection: Receive event configuration
                                                                    for EVENTS_RECEIVE[n].                                     */
  __IM  uint32_t  RESERVED6[24];
  __IOM uint32_t  GPMEM[4];                     /*!< (@ 0x00000610) Description collection: General purpose memory.            */
} NRF_IPC_Type;                                 /*!< Size = 1568 (0x620)                                                       */



/* =========================================================================================================================== */
/* ================                                          FPU_NS                                           ================ */
/* =========================================================================================================================== */


/**
  * @brief FPU 0 (FPU_NS)
  */

typedef struct {                                /*!< (@ 0x4002C000) FPU_NS Structure                                           */
  __IM  uint32_t  UNUSED;                       /*!< (@ 0x00000000) Unused.                                                    */
} NRF_FPU_Type;                                 /*!< Size = 4 (0x4)                                                            */



/* =========================================================================================================================== */
/* ================                                          KMU_NS                                           ================ */
/* =========================================================================================================================== */


/**
  * @brief Key management unit 0 (KMU_NS)
  */

typedef struct {                                /*!< (@ 0x40039000) KMU_NS Structure                                           */
  __OM  uint32_t  TASKS_PUSH_KEYSLOT;           /*!< (@ 0x00000000) Push a key slot over secure APB                            */
  __IM  uint32_t  RESERVED[63];
  __IOM uint32_t  EVENTS_KEYSLOT_PUSHED;        /*!< (@ 0x00000100) Key slot successfully pushed over secure APB               */
  __IOM uint32_t  EVENTS_KEYSLOT_REVOKED;       /*!< (@ 0x00000104) Key slot has been revoked and cannot be tasked
                                                                    for selection                                              */
  __IOM uint32_t  EVENTS_KEYSLOT_ERROR;         /*!< (@ 0x00000108) No key slot selected, no destination address
                                                                    defined, or error during push operation                    */
  __IM  uint32_t  RESERVED1[125];
  __IOM uint32_t  INTEN;                        /*!< (@ 0x00000300) Enable or disable interrupt                                */
  __IOM uint32_t  INTENSET;                     /*!< (@ 0x00000304) Enable interrupt                                           */
  __IOM uint32_t  INTENCLR;                     /*!< (@ 0x00000308) Disable interrupt                                          */
  __IM  uint32_t  INTPEND;                      /*!< (@ 0x0000030C) Pending interrupts                                         */
  __IM  uint32_t  RESERVED2[63];
  __IM  uint32_t  STATUS;                       /*!< (@ 0x0000040C) Status bits for KMU operation                              */
  __IM  uint32_t  RESERVED3[60];
  __IOM uint32_t  SELECTKEYSLOT;                /*!< (@ 0x00000500) Select key slot to be read over AHB or pushed
                                                                    over secure APB when TASKS_PUSH_KEYSLOT
                                                                    is started                                                 */
} NRF_KMU_Type;                                 /*!< Size = 1284 (0x504)                                                       */



/* =========================================================================================================================== */
/* ================                                          NVMC_NS                                          ================ */
/* =========================================================================================================================== */


/**
  * @brief Non-volatile memory controller 0 (NVMC_NS)
  */

typedef struct {                                /*!< (@ 0x40039000) NVMC_NS Structure                                          */
  __IM  uint32_t  RESERVED[256];
  __IM  uint32_t  READY;                        /*!< (@ 0x00000400) Ready flag                                                 */
  __IM  uint32_t  RESERVED1;
  __IM  uint32_t  READYNEXT;                    /*!< (@ 0x00000408) Ready flag                                                 */
  __IM  uint32_t  RESERVED2[62];
  __IOM uint32_t  CONFIG;                       /*!< (@ 0x00000504) Configuration register                                     */
  __IM  uint32_t  RESERVED3;
  __OM  uint32_t  ERASEALL;                     /*!< (@ 0x0000050C) Register for erasing all non-volatile user memory          */
  __IM  uint32_t  RESERVED4[3];
  __IOM uint32_t  ERASEPAGEPARTIALCFG;          /*!< (@ 0x0000051C) Register for partial erase configuration                   */
  __IM  uint32_t  RESERVED5[8];
  __IOM uint32_t  ICACHECNF;                    /*!< (@ 0x00000540) I-code cache configuration register                        */
  __IM  uint32_t  RESERVED6;
  __IOM uint32_t  IHIT;                         /*!< (@ 0x00000548) I-code cache hit counter                                   */
  __IOM uint32_t  IMISS;                        /*!< (@ 0x0000054C) I-code cache miss counter                                  */
  __IM  uint32_t  RESERVED7[13];
  __IOM uint32_t  CONFIGNS;                     /*!< (@ 0x00000584) Unspecified                                                */
  __OM  uint32_t  WRITEUICRNS;                  /*!< (@ 0x00000588) Non-secure APPROTECT enable register                       */
} NRF_NVMC_Type;                                /*!< Size = 1420 (0x58c)                                                       */



/* =========================================================================================================================== */
/* ================                                          VMC_NS                                           ================ */
/* =========================================================================================================================== */


/**
  * @brief Volatile Memory controller 0 (VMC_NS)
  */

typedef struct {                                /*!< (@ 0x4003A000) VMC_NS Structure                                           */
  __IM  uint32_t  RESERVED[384];
  __IOM VMC_RAM_Type RAM[8];                    /*!< (@ 0x00000600) Unspecified                                                */
} NRF_VMC_Type;                                 /*!< Size = 1664 (0x680)                                                       */



/* =========================================================================================================================== */
/* ================                                       CC_HOST_RGF_S                                       ================ */
/* =========================================================================================================================== */


/**
  * @brief CRYPTOCELL HOST_RGF interface (CC_HOST_RGF_S)
  */

typedef struct {                                /*!< (@ 0x50840000) CC_HOST_RGF_S Structure                                    */
  __IM  uint32_t  RESERVED[1678];
  __IOM uint32_t  HOST_CRYPTOKEY_SEL;           /*!< (@ 0x00001A38) AES hardware key select                                    */
  __IM  uint32_t  RESERVED1[4];
  __IOM uint32_t  HOST_IOT_KPRTL_LOCK;          /*!< (@ 0x00001A4C) This write-once register is the K_PRTL lock register.
                                                                    When this register is set, K_PRTL cannot
                                                                    be used and a zeroed key will be used instead.
                                                                    The value of this register is saved in the
                                                                    CRYPTOCELL AO power domain.                                */
  __IOM uint32_t  HOST_IOT_KDR0;                /*!< (@ 0x00001A50) This register holds bits 31:0 of K_DR. The value
                                                                    of this register is saved in the CRYPTOCELL
                                                                    AO power domain. Reading from this address
                                                                    returns the K_DR valid status indicating
                                                                    if K_DR is successfully retained.                          */
  __OM  uint32_t  HOST_IOT_KDR1;                /*!< (@ 0x00001A54) This register holds bits 63:32 of K_DR. The value
                                                                    of this register is saved in the CRYPTOCELL
                                                                    AO power domain.                                           */
  __OM  uint32_t  HOST_IOT_KDR2;                /*!< (@ 0x00001A58) This register holds bits 95:64 of K_DR. The value
                                                                    of this register is saved in the CRYPTOCELL
                                                                    AO power domain.                                           */
  __OM  uint32_t  HOST_IOT_KDR3;                /*!< (@ 0x00001A5C) This register holds bits 127:96 of K_DR. The
                                                                    value of this register is saved in the CRYPTOCELL
                                                                    AO power domain.                                           */
  __IOM uint32_t  HOST_IOT_LCS;                 /*!< (@ 0x00001A60) Controls lifecycle state (LCS) for CRYPTOCELL
                                                                    subsystem                                                  */
} NRF_CC_HOST_RGF_Type;                         /*!< Size = 6756 (0x1a64)                                                      */



/* =========================================================================================================================== */
/* ================                                       CRYPTOCELL_S                                        ================ */
/* =========================================================================================================================== */


/**
  * @brief ARM TrustZone CryptoCell register interface (CRYPTOCELL_S)
  */

typedef struct {                                /*!< (@ 0x50840000) CRYPTOCELL_S Structure                                     */
  __IM  uint32_t  RESERVED[320];
  __IOM uint32_t  ENABLE;                       /*!< (@ 0x00000500) Enable CRYPTOCELL subsystem                                */
} NRF_CRYPTOCELL_Type;                          /*!< Size = 1284 (0x504)                                                       */



/* =========================================================================================================================== */
/* ================                                           P0_NS                                           ================ */
/* =========================================================================================================================== */


/**
  * @brief GPIO Port 0 (P0_NS)
  */

typedef struct {                                /*!< (@ 0x40842500) P0_NS Structure                                            */
  __IM  uint32_t  RESERVED;
  __IOM uint32_t  OUT;                          /*!< (@ 0x00000004) Write GPIO port                                            */
  __IOM uint32_t  OUTSET;                       /*!< (@ 0x00000008) Set individual bits in GPIO port                           */
  __IOM uint32_t  OUTCLR;                       /*!< (@ 0x0000000C) Clear individual bits in GPIO port                         */
  __IM  uint32_t  IN;                           /*!< (@ 0x00000010) Read GPIO port                                             */
  __IOM uint32_t  DIR;                          /*!< (@ 0x00000014) Direction of GPIO pins                                     */
  __IOM uint32_t  DIRSET;                       /*!< (@ 0x00000018) DIR set register                                           */
  __IOM uint32_t  DIRCLR;                       /*!< (@ 0x0000001C) DIR clear register                                         */
  __IOM uint32_t  LATCH;                        /*!< (@ 0x00000020) Latch register indicating what GPIO pins that
                                                                    have met the criteria set in the PIN_CNF[n].SENSE
                                                                    registers                                                  */
  __IOM uint32_t  DETECTMODE;                   /*!< (@ 0x00000024) Select between default DETECT signal behavior
                                                                    and LDETECT mode (For non-secure pin only)                 */
  __IOM uint32_t  DETECTMODE_SEC;               /*!< (@ 0x00000028) Select between default DETECT signal behavior
                                                                    and LDETECT mode (For secure pin only)                     */
  __IM  uint32_t  RESERVED1[117];
  __IOM uint32_t  PIN_CNF[32];                  /*!< (@ 0x00000200) Description collection: Configuration of GPIO
                                                                    pins                                                       */
} NRF_GPIO_Type;                                /*!< Size = 640 (0x280)                                                        */


/** @} */ /* End of group Device_Peripheral_peripherals */


/* =========================================================================================================================== */
/* ================                          Device Specific Peripheral Address Map                           ================ */
/* =========================================================================================================================== */


/** @addtogroup Device_Peripheral_peripheralAddr
  * @{
  */

#define NRF_FICR_S_BASE             0x00FF0000UL
#define NRF_UICR_S_BASE             0x00FF8000UL
#define NRF_TAD_S_BASE              0xE0080000UL
#define NRF_SPU_S_BASE              0x50003000UL
#define NRF_REGULATORS_NS_BASE      0x40004000UL
#define NRF_REGULATORS_S_BASE       0x50004000UL
#define NRF_CLOCK_NS_BASE           0x40005000UL
#define NRF_POWER_NS_BASE           0x40005000UL
#define NRF_CLOCK_S_BASE            0x50005000UL
#define NRF_POWER_S_BASE            0x50005000UL
#define NRF_CTRL_AP_PERI_S_BASE     0x50006000UL
#define NRF_SPIM0_NS_BASE           0x40008000UL
#define NRF_SPIS0_NS_BASE           0x40008000UL
#define NRF_TWIM0_NS_BASE           0x40008000UL
#define NRF_TWIS0_NS_BASE           0x40008000UL
#define NRF_UARTE0_NS_BASE          0x40008000UL
#define NRF_SPIM0_S_BASE            0x50008000UL
#define NRF_SPIS0_S_BASE            0x50008000UL
#define NRF_TWIM0_S_BASE            0x50008000UL
#define NRF_TWIS0_S_BASE            0x50008000UL
#define NRF_UARTE0_S_BASE           0x50008000UL
#define NRF_SPIM1_NS_BASE           0x40009000UL
#define NRF_SPIS1_NS_BASE           0x40009000UL
#define NRF_TWIM1_NS_BASE           0x40009000UL
#define NRF_TWIS1_NS_BASE           0x40009000UL
#define NRF_UARTE1_NS_BASE          0x40009000UL
#define NRF_SPIM1_S_BASE            0x50009000UL
#define NRF_SPIS1_S_BASE            0x50009000UL
#define NRF_TWIM1_S_BASE            0x50009000UL
#define NRF_TWIS1_S_BASE            0x50009000UL
#define NRF_UARTE1_S_BASE           0x50009000UL
#define NRF_SPIM2_NS_BASE           0x4000A000UL
#define NRF_SPIS2_NS_BASE           0x4000A000UL
#define NRF_TWIM2_NS_BASE           0x4000A000UL
#define NRF_TWIS2_NS_BASE           0x4000A000UL
#define NRF_UARTE2_NS_BASE          0x4000A000UL
#define NRF_SPIM2_S_BASE            0x5000A000UL
#define NRF_SPIS2_S_BASE            0x5000A000UL
#define NRF_TWIM2_S_BASE            0x5000A000UL
#define NRF_TWIS2_S_BASE            0x5000A000UL
#define NRF_UARTE2_S_BASE           0x5000A000UL
#define NRF_SPIM3_NS_BASE           0x4000B000UL
#define NRF_SPIS3_NS_BASE           0x4000B000UL
#define NRF_TWIM3_NS_BASE           0x4000B000UL
#define NRF_TWIS3_NS_BASE           0x4000B000UL
#define NRF_UARTE3_NS_BASE          0x4000B000UL
#define NRF_SPIM3_S_BASE            0x5000B000UL
#define NRF_SPIS3_S_BASE            0x5000B000UL
#define NRF_TWIM3_S_BASE            0x5000B000UL
#define NRF_TWIS3_S_BASE            0x5000B000UL
#define NRF_UARTE3_S_BASE           0x5000B000UL
#define NRF_GPIOTE0_S_BASE          0x5000D000UL
#define NRF_SAADC_NS_BASE           0x4000E000UL
#define NRF_SAADC_S_BASE            0x5000E000UL
#define NRF_TIMER0_NS_BASE          0x4000F000UL
#define NRF_TIMER0_S_BASE           0x5000F000UL
#define NRF_TIMER1_NS_BASE          0x40010000UL
#define NRF_TIMER1_S_BASE           0x50010000UL
#define NRF_TIMER2_NS_BASE          0x40011000UL
#define NRF_TIMER2_S_BASE           0x50011000UL
#define NRF_RTC0_NS_BASE            0x40014000UL
#define NRF_RTC0_S_BASE             0x50014000UL
#define NRF_RTC1_NS_BASE            0x40015000UL
#define NRF_RTC1_S_BASE             0x50015000UL
#define NRF_DPPIC_NS_BASE           0x40017000UL
#define NRF_DPPIC_S_BASE            0x50017000UL
#define NRF_WDT_NS_BASE             0x40018000UL
#define NRF_WDT_S_BASE              0x50018000UL
#define NRF_EGU0_NS_BASE            0x4001B000UL
#define NRF_EGU0_S_BASE             0x5001B000UL
#define NRF_EGU1_NS_BASE            0x4001C000UL
#define NRF_EGU1_S_BASE             0x5001C000UL
#define NRF_EGU2_NS_BASE            0x4001D000UL
#define NRF_EGU2_S_BASE             0x5001D000UL
#define NRF_EGU3_NS_BASE            0x4001E000UL
#define NRF_EGU3_S_BASE             0x5001E000UL
#define NRF_EGU4_NS_BASE            0x4001F000UL
#define NRF_EGU4_S_BASE             0x5001F000UL
#define NRF_EGU5_NS_BASE            0x40020000UL
#define NRF_EGU5_S_BASE             0x50020000UL
#define NRF_PWM0_NS_BASE            0x40021000UL
#define NRF_PWM0_S_BASE             0x50021000UL
#define NRF_PWM1_NS_BASE            0x40022000UL
#define NRF_PWM1_S_BASE             0x50022000UL
#define NRF_PWM2_NS_BASE            0x40023000UL
#define NRF_PWM2_S_BASE             0x50023000UL
#define NRF_PWM3_NS_BASE            0x40024000UL
#define NRF_PWM3_S_BASE             0x50024000UL
#define NRF_PDM_NS_BASE             0x40026000UL
#define NRF_PDM_S_BASE              0x50026000UL
#define NRF_I2S_NS_BASE             0x40028000UL
#define NRF_I2S_S_BASE              0x50028000UL
#define NRF_IPC_NS_BASE             0x4002A000UL
#define NRF_IPC_S_BASE              0x5002A000UL
#define NRF_FPU_NS_BASE             0x4002C000UL
#define NRF_FPU_S_BASE              0x5002C000UL
#define NRF_GPIOTE1_NS_BASE         0x40031000UL
#define NRF_KMU_NS_BASE             0x40039000UL
#define NRF_NVMC_NS_BASE            0x40039000UL
#define NRF_KMU_S_BASE              0x50039000UL
#define NRF_NVMC_S_BASE             0x50039000UL
#define NRF_VMC_NS_BASE             0x4003A000UL
#define NRF_VMC_S_BASE              0x5003A000UL
#define NRF_CC_HOST_RGF_S_BASE      0x50840000UL
#define NRF_CRYPTOCELL_S_BASE       0x50840000UL
#define NRF_P0_NS_BASE              0x40842500UL
#define NRF_P0_S_BASE               0x50842500UL

/** @} */ /* End of group Device_Peripheral_peripheralAddr */


/* =========================================================================================================================== */
/* ================                                  Peripheral declaration                                   ================ */
/* =========================================================================================================================== */


/** @addtogroup Device_Peripheral_declaration
  * @{
  */

#define NRF_FICR_S                  ((NRF_FICR_Type*)          NRF_FICR_S_BASE)
#define NRF_UICR_S                  ((NRF_UICR_Type*)          NRF_UICR_S_BASE)
#define NRF_TAD_S                   ((NRF_TAD_Type*)           NRF_TAD_S_BASE)
#define NRF_SPU_S                   ((NRF_SPU_Type*)           NRF_SPU_S_BASE)
#define NRF_REGULATORS_NS           ((NRF_REGULATORS_Type*)    NRF_REGULATORS_NS_BASE)
#define NRF_REGULATORS_S            ((NRF_REGULATORS_Type*)    NRF_REGULATORS_S_BASE)
#define NRF_CLOCK_NS                ((NRF_CLOCK_Type*)         NRF_CLOCK_NS_BASE)
#define NRF_POWER_NS                ((NRF_POWER_Type*)         NRF_POWER_NS_BASE)
#define NRF_CLOCK_S                 ((NRF_CLOCK_Type*)         NRF_CLOCK_S_BASE)
#define NRF_POWER_S                 ((NRF_POWER_Type*)         NRF_POWER_S_BASE)
#define NRF_CTRL_AP_PERI_S          ((NRF_CTRLAPPERI_Type*)    NRF_CTRL_AP_PERI_S_BASE)
#define NRF_SPIM0_NS                ((NRF_SPIM_Type*)          NRF_SPIM0_NS_BASE)
#define NRF_SPIS0_NS                ((NRF_SPIS_Type*)          NRF_SPIS0_NS_BASE)
#define NRF_TWIM0_NS                ((NRF_TWIM_Type*)          NRF_TWIM0_NS_BASE)
#define NRF_TWIS0_NS                ((NRF_TWIS_Type*)          NRF_TWIS0_NS_BASE)
#define NRF_UARTE0_NS               ((NRF_UARTE_Type*)         NRF_UARTE0_NS_BASE)
#define NRF_SPIM0_S                 ((NRF_SPIM_Type*)          NRF_SPIM0_S_BASE)
#define NRF_SPIS0_S                 ((NRF_SPIS_Type*)          NRF_SPIS0_S_BASE)
#define NRF_TWIM0_S                 ((NRF_TWIM_Type*)          NRF_TWIM0_S_BASE)
#define NRF_TWIS0_S                 ((NRF_TWIS_Type*)          NRF_TWIS0_S_BASE)
#define NRF_UARTE0_S                ((NRF_UARTE_Type*)         NRF_UARTE0_S_BASE)
#define NRF_SPIM1_NS                ((NRF_SPIM_Type*)          NRF_SPIM1_NS_BASE)
#define NRF_SPIS1_NS                ((NRF_SPIS_Type*)          NRF_SPIS1_NS_BASE)
#define NRF_TWIM1_NS                ((NRF_TWIM_Type*)          NRF_TWIM1_NS_BASE)
#define NRF_TWIS1_NS                ((NRF_TWIS_Type*)          NRF_TWIS1_NS_BASE)
#define NRF_UARTE1_NS               ((NRF_UARTE_Type*)         NRF_UARTE1_NS_BASE)
#define NRF_SPIM1_S                 ((NRF_SPIM_Type*)          NRF_SPIM1_S_BASE)
#define NRF_SPIS1_S                 ((NRF_SPIS_Type*)          NRF_SPIS1_S_BASE)
#define NRF_TWIM1_S                 ((NRF_TWIM_Type*)          NRF_TWIM1_S_BASE)
#define NRF_TWIS1_S                 ((NRF_TWIS_Type*)          NRF_TWIS1_S_BASE)
#define NRF_UARTE1_S                ((NRF_UARTE_Type*)         NRF_UARTE1_S_BASE)
#define NRF_SPIM2_NS                ((NRF_SPIM_Type*)          NRF_SPIM2_NS_BASE)
#define NRF_SPIS2_NS                ((NRF_SPIS_Type*)          NRF_SPIS2_NS_BASE)
#define NRF_TWIM2_NS                ((NRF_TWIM_Type*)          NRF_TWIM2_NS_BASE)
#define NRF_TWIS2_NS                ((NRF_TWIS_Type*)          NRF_TWIS2_NS_BASE)
#define NRF_UARTE2_NS               ((NRF_UARTE_Type*)         NRF_UARTE2_NS_BASE)
#define NRF_SPIM2_S                 ((NRF_SPIM_Type*)          NRF_SPIM2_S_BASE)
#define NRF_SPIS2_S                 ((NRF_SPIS_Type*)          NRF_SPIS2_S_BASE)
#define NRF_TWIM2_S                 ((NRF_TWIM_Type*)          NRF_TWIM2_S_BASE)
#define NRF_TWIS2_S                 ((NRF_TWIS_Type*)          NRF_TWIS2_S_BASE)
#define NRF_UARTE2_S                ((NRF_UARTE_Type*)         NRF_UARTE2_S_BASE)
#define NRF_SPIM3_NS                ((NRF_SPIM_Type*)          NRF_SPIM3_NS_BASE)
#define NRF_SPIS3_NS                ((NRF_SPIS_Type*)          NRF_SPIS3_NS_BASE)
#define NRF_TWIM3_NS                ((NRF_TWIM_Type*)          NRF_TWIM3_NS_BASE)
#define NRF_TWIS3_NS                ((NRF_TWIS_Type*)          NRF_TWIS3_NS_BASE)
#define NRF_UARTE3_NS               ((NRF_UARTE_Type*)         NRF_UARTE3_NS_BASE)
#define NRF_SPIM3_S                 ((NRF_SPIM_Type*)          NRF_SPIM3_S_BASE)
#define NRF_SPIS3_S                 ((NRF_SPIS_Type*)          NRF_SPIS3_S_BASE)
#define NRF_TWIM3_S                 ((NRF_TWIM_Type*)          NRF_TWIM3_S_BASE)
#define NRF_TWIS3_S                 ((NRF_TWIS_Type*)          NRF_TWIS3_S_BASE)
#define NRF_UARTE3_S                ((NRF_UARTE_Type*)         NRF_UARTE3_S_BASE)
#define NRF_GPIOTE0_S               ((NRF_GPIOTE_Type*)        NRF_GPIOTE0_S_BASE)
#define NRF_SAADC_NS                ((NRF_SAADC_Type*)         NRF_SAADC_NS_BASE)
#define NRF_SAADC_S                 ((NRF_SAADC_Type*)         NRF_SAADC_S_BASE)
#define NRF_TIMER0_NS               ((NRF_TIMER_Type*)         NRF_TIMER0_NS_BASE)
#define NRF_TIMER0_S                ((NRF_TIMER_Type*)         NRF_TIMER0_S_BASE)
#define NRF_TIMER1_NS               ((NRF_TIMER_Type*)         NRF_TIMER1_NS_BASE)
#define NRF_TIMER1_S                ((NRF_TIMER_Type*)         NRF_TIMER1_S_BASE)
#define NRF_TIMER2_NS               ((NRF_TIMER_Type*)         NRF_TIMER2_NS_BASE)
#define NRF_TIMER2_S                ((NRF_TIMER_Type*)         NRF_TIMER2_S_BASE)
#define NRF_RTC0_NS                 ((NRF_RTC_Type*)           NRF_RTC0_NS_BASE)
#define NRF_RTC0_S                  ((NRF_RTC_Type*)           NRF_RTC0_S_BASE)
#define NRF_RTC1_NS                 ((NRF_RTC_Type*)           NRF_RTC1_NS_BASE)
#define NRF_RTC1_S                  ((NRF_RTC_Type*)           NRF_RTC1_S_BASE)
#define NRF_DPPIC_NS                ((NRF_DPPIC_Type*)         NRF_DPPIC_NS_BASE)
#define NRF_DPPIC_S                 ((NRF_DPPIC_Type*)         NRF_DPPIC_S_BASE)
#define NRF_WDT_NS                  ((NRF_WDT_Type*)           NRF_WDT_NS_BASE)
#define NRF_WDT_S                   ((NRF_WDT_Type*)           NRF_WDT_S_BASE)
#define NRF_EGU0_NS                 ((NRF_EGU_Type*)           NRF_EGU0_NS_BASE)
#define NRF_EGU0_S                  ((NRF_EGU_Type*)           NRF_EGU0_S_BASE)
#define NRF_EGU1_NS                 ((NRF_EGU_Type*)           NRF_EGU1_NS_BASE)
#define NRF_EGU1_S                  ((NRF_EGU_Type*)           NRF_EGU1_S_BASE)
#define NRF_EGU2_NS                 ((NRF_EGU_Type*)           NRF_EGU2_NS_BASE)
#define NRF_EGU2_S                  ((NRF_EGU_Type*)           NRF_EGU2_S_BASE)
#define NRF_EGU3_NS                 ((NRF_EGU_Type*)           NRF_EGU3_NS_BASE)
#define NRF_EGU3_S                  ((NRF_EGU_Type*)           NRF_EGU3_S_BASE)
#define NRF_EGU4_NS                 ((NRF_EGU_Type*)           NRF_EGU4_NS_BASE)
#define NRF_EGU4_S                  ((NRF_EGU_Type*)           NRF_EGU4_S_BASE)
#define NRF_EGU5_NS                 ((NRF_EGU_Type*)           NRF_EGU5_NS_BASE)
#define NRF_EGU5_S                  ((NRF_EGU_Type*)           NRF_EGU5_S_BASE)
#define NRF_PWM0_NS                 ((NRF_PWM_Type*)           NRF_PWM0_NS_BASE)
#define NRF_PWM0_S                  ((NRF_PWM_Type*)           NRF_PWM0_S_BASE)
#define NRF_PWM1_NS                 ((NRF_PWM_Type*)           NRF_PWM1_NS_BASE)
#define NRF_PWM1_S                  ((NRF_PWM_Type*)           NRF_PWM1_S_BASE)
#define NRF_PWM2_NS                 ((NRF_PWM_Type*)           NRF_PWM2_NS_BASE)
#define NRF_PWM2_S                  ((NRF_PWM_Type*)           NRF_PWM2_S_BASE)
#define NRF_PWM3_NS                 ((NRF_PWM_Type*)           NRF_PWM3_NS_BASE)
#define NRF_PWM3_S                  ((NRF_PWM_Type*)           NRF_PWM3_S_BASE)
#define NRF_PDM_NS                  ((NRF_PDM_Type*)           NRF_PDM_NS_BASE)
#define NRF_PDM_S                   ((NRF_PDM_Type*)           NRF_PDM_S_BASE)
#define NRF_I2S_NS                  ((NRF_I2S_Type*)           NRF_I2S_NS_BASE)
#define NRF_I2S_S                   ((NRF_I2S_Type*)           NRF_I2S_S_BASE)
#define NRF_IPC_NS                  ((NRF_IPC_Type*)           NRF_IPC_NS_BASE)
#define NRF_IPC_S                   ((NRF_IPC_Type*)           NRF_IPC_S_BASE)
#define NRF_FPU_NS                  ((NRF_FPU_Type*)           NRF_FPU_NS_BASE)
#define NRF_FPU_S                   ((NRF_FPU_Type*)           NRF_FPU_S_BASE)
#define NRF_GPIOTE1_NS              ((NRF_GPIOTE_Type*)        NRF_GPIOTE1_NS_BASE)
#define NRF_KMU_NS                  ((NRF_KMU_Type*)           NRF_KMU_NS_BASE)
#define NRF_NVMC_NS                 ((NRF_NVMC_Type*)          NRF_NVMC_NS_BASE)
#define NRF_KMU_S                   ((NRF_KMU_Type*)           NRF_KMU_S_BASE)
#define NRF_NVMC_S                  ((NRF_NVMC_Type*)          NRF_NVMC_S_BASE)
#define NRF_VMC_NS                  ((NRF_VMC_Type*)           NRF_VMC_NS_BASE)
#define NRF_VMC_S                   ((NRF_VMC_Type*)           NRF_VMC_S_BASE)
#define NRF_CC_HOST_RGF_S           ((NRF_CC_HOST_RGF_Type*)   NRF_CC_HOST_RGF_S_BASE)
#define NRF_CRYPTOCELL_S            ((NRF_CRYPTOCELL_Type*)    NRF_CRYPTOCELL_S_BASE)
#define NRF_P0_NS                   ((NRF_GPIO_Type*)          NRF_P0_NS_BASE)
#define NRF_P0_S                    ((NRF_GPIO_Type*)          NRF_P0_S_BASE)

/** @} */ /* End of group Device_Peripheral_declaration */


#ifdef __cplusplus
}
#endif

#endif /* NRF9160_H */


/** @} */ /* End of group nrf9160 */

/** @} */ /* End of group Nordic Semiconductor */
