<!DOCTYPE CrossStudio_Project_File>
<solution Name="secure_bootloader_ble_s132_pca10040_debug" target="8" version="2">
  <project Name="secure_bootloader_ble_s132_pca10040_debug">
    <configuration
      Name="Common"
      arm_architecture="v7EM"
      arm_core_type="Cortex-M4"
      arm_endian="Little"
      arm_fp_abi="Hard"
      arm_fpu_type="FPv4-SP-D16"
      arm_linker_heap_size="0"
      arm_linker_process_stack_size="0"
      arm_linker_stack_size="2048"
      arm_linker_treat_warnings_as_errors="No"
      arm_simulator_memory_simulation_parameter="RWX 00000000,00100000,FFFFFFFF;RWX 20000000,00010000,CDCDCDCD"
      arm_target_device_name="nRF52832_xxAA"
      arm_target_interface_type="SWD"
      c_user_include_directories="../../config;../../../../../components/ble/common;../../../../../components/boards;../../../../../components/libraries/atomic;../../../../../components/libraries/atomic_fifo;../../../../../components/libraries/balloc;../../../../../components/libraries/bootloader;../../../../../components/libraries/bootloader/ble_dfu;../../../../../components/libraries/bootloader/dfu;../../../../../components/libraries/crc32;../../../../../components/libraries/crypto;../../../../../components/libraries/crypto/backend/cc310;../../../../../components/libraries/crypto/backend/cc310_bl;../../../../../components/libraries/crypto/backend/cifra;../../../../../components/libraries/crypto/backend/mbedtls;../../../../../components/libraries/crypto/backend/micro_ecc;../../../../../components/libraries/crypto/backend/nrf_hw;../../../../../components/libraries/crypto/backend/nrf_sw;../../../../../components/libraries/crypto/backend/oberon;../../../../../components/libraries/crypto/backend/optiga;../../../../../components/libraries/delay;../../../../../components/libraries/experimental_section_vars;../../../../../components/libraries/fstorage;../../../../../components/libraries/log;../../../../../components/libraries/log/src;../../../../../components/libraries/mem_manager;../../../../../components/libraries/memobj;../../../../../components/libraries/queue;../../../../../components/libraries/ringbuf;../../../../../components/libraries/scheduler;../../../../../components/libraries/sha256;../../../../../components/libraries/stack_info;../../../../../components/libraries/strerror;../../../../../components/libraries/svc;../../../../../components/libraries/util;../../../../../components/softdevice/common;../../../../../components/softdevice/s132/headers;../../../../../components/softdevice/s132/headers/nrf52;../../../../../components/toolchain/cmsis/include;../..;../../../../../external/fprintf;../../../../../external/micro-ecc/micro-ecc;../../../../../external/nano-pb;../../../../../external/nrf_oberon;../../../../../external/nrf_oberon/include;../../../../../external/segger_rtt;../../../../../integration/nrfx;../../../../../modules/nrfx;../../../../../modules/nrfx/hal;../../../../../modules/nrfx/mdk;../config;"
      c_preprocessor_definitions="BLE_STACK_SUPPORT_REQD;BOARD_PCA10040;CONFIG_GPIO_AS_PINRESET;DEBUG_NRF;FLOAT_ABI_HARD;INITIALIZE_USER_SECTIONS;NO_VTOR_CONFIG;NRF52;NRF52832_XXAA;NRF52_PAN_74;NRF_DFU_DEBUG_VERSION;NRF_DFU_SETTINGS_VERSION=2;NRF_DFU_SVCI_ENABLED;NRF_SD_BLE_API_VERSION=7;S132;SOFTDEVICE_PRESENT;SVC_INTERFACE_CALL_AS_NORMAL_FUNCTION;uECC_ENABLE_VLI_API=0;uECC_OPTIMIZATION_LEVEL=3;uECC_SQUARE_FUNC=0;uECC_SUPPORT_COMPRESSED_POINT=0;uECC_VLI_NATIVE_LITTLE_ENDIAN=1;"
      debug_target_connection="J-Link"
      gcc_entry_point="Reset_Handler"
      macros="CMSIS_CONFIG_TOOL=../../../../../external_tools/cmsisconfig/CMSIS_Configuration_Wizard.jar"
      debug_register_definition_file="../../../../../modules/nrfx/mdk/nrf52.svd"
      debug_additional_load_file="../../../../../components/softdevice/s132/hex/s132_nrf52_7.0.1_softdevice.hex"
      debug_start_from_entry_point_symbol="No"
      gcc_debugging_level="Level 3"      linker_output_format="hex"
      linker_printf_width_precision_supported="Yes"
      linker_printf_fmt_level="long"
      linker_scanf_fmt_level="long"
      linker_section_placement_file="flash_placement.xml"
      linker_section_placement_macros="FLASH_PH_START=0x0;FLASH_PH_SIZE=0x80000;RAM_PH_START=0x20000000;RAM_PH_SIZE=0x10000;FLASH_START=0x71000;FLASH_SIZE=0xd000;RAM_START=0x20005968;RAM_SIZE=0xa698"
      
      linker_section_placements_segments="FLASH RX 0x0 0x80000;RAM RWX 0x20000000 0x10000;mbr_params_page RX 0x0007E000 0x1000;bootloader_settings_page RX 0x0007F000 0x1000;uicr_bootloader_start_address RX 0x10001014 0x4;uicr_mbr_params_page RX 0x10001018 0x4"
      project_directory=""
      project_type="Executable" />
      <folder Name="Segger Startup Files">
        <file file_name="$(StudioDir)/source/thumb_crt0.s" />
      </folder>
    <folder Name="nRF_Log">
      <file file_name="../../../../../components/libraries/log/src/nrf_log_backend_rtt.c" />
      <file file_name="../../../../../components/libraries/log/src/nrf_log_backend_serial.c" />
      <file file_name="../../../../../components/libraries/log/src/nrf_log_default_backends.c" />
      <file file_name="../../../../../components/libraries/log/src/nrf_log_frontend.c" />
      <file file_name="../../../../../components/libraries/log/src/nrf_log_str_formatter.c" />
    </folder>
    <folder Name="nRF_Libraries">
      <file file_name="../../../../../components/libraries/util/app_error_weak.c" />
      <file file_name="../../../../../components/libraries/scheduler/app_scheduler.c" />
      <file file_name="../../../../../components/libraries/util/app_util_platform.c" />
      <file file_name="../../../../../components/libraries/crc32/crc32.c" />
      <file file_name="../../../../../components/libraries/mem_manager/mem_manager.c" />
      <file file_name="../../../../../components/libraries/util/nrf_assert.c" />
      <file file_name="../../../../../components/libraries/atomic_fifo/nrf_atfifo.c" />
      <file file_name="../../../../../components/libraries/atomic/nrf_atomic.c" />
      <file file_name="../../../../../components/libraries/balloc/nrf_balloc.c" />
      <file file_name="../../../../../external/fprintf/nrf_fprintf.c" />
      <file file_name="../../../../../external/fprintf/nrf_fprintf_format.c" />
      <file file_name="../../../../../components/libraries/fstorage/nrf_fstorage.c" />
      <file file_name="../../../../../components/libraries/fstorage/nrf_fstorage_nvmc.c" />
      <file file_name="../../../../../components/libraries/fstorage/nrf_fstorage_sd.c" />
      <file file_name="../../../../../components/libraries/memobj/nrf_memobj.c" />
      <file file_name="../../../../../components/libraries/queue/nrf_queue.c" />
      <file file_name="../../../../../components/libraries/ringbuf/nrf_ringbuf.c" />
      <file file_name="../../../../../components/libraries/experimental_section_vars/nrf_section_iter.c" />
      <file file_name="../../../../../components/libraries/strerror/nrf_strerror.c" />
      <file file_name="../../../../../components/libraries/sha256/sha256.c" />
    </folder>
    <folder Name="nRF_Drivers">
      <file file_name="../../../../../modules/nrfx/hal/nrf_nvmc.c" />
      <file file_name="../../../../../modules/nrfx/soc/nrfx_atomic.c" />
    </folder>
    <folder Name="nRF_Crypto backend uECC">
      <file file_name="../../../../../components/libraries/crypto/backend/micro_ecc/micro_ecc_backend_ecc.c" />
      <file file_name="../../../../../components/libraries/crypto/backend/micro_ecc/micro_ecc_backend_ecdh.c" />
      <file file_name="../../../../../components/libraries/crypto/backend/micro_ecc/micro_ecc_backend_ecdsa.c" />
    </folder>
    <folder Name="nano-pb">
      <file file_name="../../../../../external/nano-pb/pb_common.c" />
      <file file_name="../../../../../external/nano-pb/pb_decode.c" />
    </folder>
    <folder Name="Board Definition">
      <file file_name="../../../../../components/boards/boards.c" />
    </folder>
    <folder Name="nRF_SVC">
      <file file_name="../../../../../components/libraries/bootloader/dfu/nrf_dfu_svci.c" />
      <file file_name="../../../../../components/libraries/bootloader/dfu/nrf_dfu_svci_handler.c" />
      <file file_name="../../../../../components/libraries/svc/nrf_svc_handler.c" />
    </folder>
    <folder Name="nRF_Oberon_Crypto">
      <file file_name="../../../../../external/nrf_oberon/lib/cortex-m4/hard-float/liboberon_3.0.5.a" />
    </folder>
    <folder Name="nRF_Crypto">
      <file file_name="../../../../../components/libraries/crypto/nrf_crypto_ecc.c" />
      <file file_name="../../../../../components/libraries/crypto/nrf_crypto_ecdsa.c" />
      <file file_name="../../../../../components/libraries/crypto/nrf_crypto_hash.c" />
      <file file_name="../../../../../components/libraries/crypto/nrf_crypto_init.c" />
      <file file_name="../../../../../components/libraries/crypto/nrf_crypto_shared.c" />
    </folder>
    <folder Name="Application">
      <file file_name="../../../dfu_public_key.c" />
      <file file_name="../../main.c" />
      <file file_name="../config/sdk_config.h" />
    </folder>
    <folder Name="nRF_micro-ecc">
      <file file_name="../../../../../external/micro-ecc/nrf52hf_armgcc/armgcc/micro_ecc_lib_nrf52.a" />
    </folder>
    <folder Name="nRF_Segger_RTT">
      <file file_name="../../../../../external/segger_rtt/SEGGER_RTT.c" />
      <file file_name="../../../../../external/segger_rtt/SEGGER_RTT_Syscalls_SES.c" />
      <file file_name="../../../../../external/segger_rtt/SEGGER_RTT_printf.c" />
    </folder>
    <folder Name="nRF_Bootloader">
      <file file_name="../../../../../components/libraries/bootloader/nrf_bootloader.c" />
      <file file_name="../../../../../components/libraries/bootloader/nrf_bootloader_app_start.c" />
      <file file_name="../../../../../components/libraries/bootloader/nrf_bootloader_app_start_final.c" />
      <file file_name="../../../../../components/libraries/bootloader/nrf_bootloader_dfu_timers.c" />
      <file file_name="../../../../../components/libraries/bootloader/nrf_bootloader_fw_activation.c" />
      <file file_name="../../../../../components/libraries/bootloader/nrf_bootloader_info.c" />
      <file file_name="../../../../../components/libraries/bootloader/nrf_bootloader_wdt.c" />
    </folder>
    <folder Name="None">
      <file file_name="../../../../../modules/nrfx/mdk/ses_startup_nrf52.s" />
      <file file_name="../../../../../modules/nrfx/mdk/ses_startup_nrf_common.s" />
      <file file_name="../../../../../modules/nrfx/mdk/system_nrf52.c" />
    </folder>
    <folder Name="nRF_Crypto backend nRF sw">
      <file file_name="../../../../../components/libraries/crypto/backend/nrf_sw/nrf_sw_backend_hash.c" />
    </folder>
    <folder Name="nRF_BLE">
      <file file_name="../../../../../components/ble/common/ble_srv_common.c" />
    </folder>
    <folder Name="nRF_DFU">
      <file file_name="../../../../../components/libraries/bootloader/dfu/dfu-cc.pb.c" />
      <file file_name="../../../../../components/libraries/bootloader/dfu/nrf_dfu.c" />
      <file file_name="../../../../../components/libraries/bootloader/ble_dfu/nrf_dfu_ble.c" />
      <file file_name="../../../../../components/libraries/bootloader/dfu/nrf_dfu_flash.c" />
      <file file_name="../../../../../components/libraries/bootloader/dfu/nrf_dfu_handling_error.c" />
      <file file_name="../../../../../components/libraries/bootloader/dfu/nrf_dfu_mbr.c" />
      <file file_name="../../../../../components/libraries/bootloader/dfu/nrf_dfu_req_handler.c" />
      <file file_name="../../../../../components/libraries/bootloader/dfu/nrf_dfu_settings.c" />
      <file file_name="../../../../../components/libraries/bootloader/dfu/nrf_dfu_settings_svci.c" />
      <file file_name="../../../../../components/libraries/bootloader/dfu/nrf_dfu_transport.c" />
      <file file_name="../../../../../components/libraries/bootloader/dfu/nrf_dfu_utils.c" />
      <file file_name="../../../../../components/libraries/bootloader/dfu/nrf_dfu_validation.c" />
      <file file_name="../../../../../components/libraries/bootloader/dfu/nrf_dfu_ver_validation.c" />
    </folder>
    <folder Name="nRF_SoftDevice">
      <file file_name="../../../../../components/softdevice/common/nrf_sdh.c" />
      <file file_name="../../../../../components/softdevice/common/nrf_sdh_ble.c" />
      <file file_name="../../../../../components/softdevice/common/nrf_sdh_soc.c" />
    </folder>
    <folder Name="nRF_Crypto backend Oberon">
      <file file_name="../../../../../components/libraries/crypto/backend/oberon/oberon_backend_chacha_poly_aead.c" />
      <file file_name="../../../../../components/libraries/crypto/backend/oberon/oberon_backend_ecc.c" />
      <file file_name="../../../../../components/libraries/crypto/backend/oberon/oberon_backend_ecdh.c" />
      <file file_name="../../../../../components/libraries/crypto/backend/oberon/oberon_backend_ecdsa.c" />
      <file file_name="../../../../../components/libraries/crypto/backend/oberon/oberon_backend_eddsa.c" />
      <file file_name="../../../../../components/libraries/crypto/backend/oberon/oberon_backend_hash.c" />
      <file file_name="../../../../../components/libraries/crypto/backend/oberon/oberon_backend_hmac.c" />
    </folder>
  </project>
  <configuration Name="Release"
    c_preprocessor_definitions="NDEBUG"
    link_time_optimization="No"    gcc_optimization_level="Optimize For Size" />

</solution>
